﻿using AutoMapper;
using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Org.BouncyCastle.Ocsp;
using SqlSugar;
using XH.H81.IServices;
using XH.H81.IServices.Pms;
using XH.H81.Models.Dtos.External;
using XH.H81.Models.Dtos.Pms;
using XH.H81.Models.Entities.others;
using XH.H81.Models.Entities.Pms;
using XH.H81.Models.Entities.Tag;
using XH.H81.Models.SugarDbContext;
using XH.LAB.UTILS.Helpers;
using XH.LAB.UTILS.Interface;
using XH.LAB.UTILS.Models;
using XH.LAB.UTILS.Models.Dto;
using Serilog;

namespace XH.H81.Services
{

    public class ExternalService : IExternalService
    {
        private readonly IHttpContextAccessor _httpContext;
        private readonly IConfiguration _configuration;
        private readonly ISqlSugarUow<SugarDbContext_Master> _soa;
        private readonly IMapper _mapper;
        private readonly ISystemService _systemService;
        private readonly string FileHttpUrl = "/H81pdf/api";
        private readonly Microsoft.AspNetCore.Hosting.IHostingEnvironment _hostingEnvironment;
        private readonly IBaseDataServices _IBaseDataServices;
        private readonly IEvaluatePlanService _IEvaluatePlanService;
        private readonly IPmsService _IPmsService;
        private readonly IUploadFileService _IUploadFileService;
        public ExternalService(IConfiguration configuration, IHttpContextAccessor httpContext, ISqlSugarUow<SugarDbContext_Master> dbContext, IMapper mapper, ISystemService systemService, IBaseDataServices baseDataServices, IEvaluatePlanService evaluatePlanService, IPmsService ipmsService, Microsoft.AspNetCore.Hosting.IHostingEnvironment hostingEnvironment, IUploadFileService UploadFileService)
        {
            _configuration = configuration;
            _httpContext = httpContext;
            _soa = dbContext;
            _mapper = mapper;
            _systemService = systemService;
            _IBaseDataServices = baseDataServices;
            _IEvaluatePlanService = evaluatePlanService;
            _IPmsService = ipmsService;
            _hostingEnvironment = hostingEnvironment;
            _IUploadFileService = UploadFileService;
        }

        #region 采血外援相关接口
        private const string BLOOD_CLASS_ID = "采血外援";
        /// <summary>
        /// 
        /// </summary>
        /// <param name="hospitalId"></param>
        /// <param name="userName"></param>
        /// <param name="hisId"></param>
        /// <returns></returns>
        /// <exception cref="BizException"></exception>
        public BloodCollectPersonOutput RegisterBloodCollectSupportPerson(string hospitalId, string userName, string hisId)
        {
            PMS_PERSON_INFO person = null;
            var persons = _soa.Db.Queryable<PMS_PERSON_INFO>().Where(a => a.HOSPITAL_ID == hospitalId && a.HIS_ID == hisId && a.PERSON_STATE == "1").ToList();
            if (persons.Count == 0)
            {
                person = new PMS_PERSON_INFO();
                person.PERSON_ID = "BLD" + _IBaseDataServices.GetTableMaxNumber("PMS_PERSON_INFO", "PERSON_ID", 1, 1, "XH_OA").data.ToString().PadLeft(10, '0');
                person.FIRST_RPERSON = "外部调用创建";
                person.FIRST_RTIME = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                person.PERSON_STATE = "1";
                person.USER_NAME = userName;
                person.HIS_ID = hisId;
                person.HOSPITAL_ID = hospitalId;
                person.PGROUP_ID = "PG000";
                //person.PERSON_DOC_STATE = "1";//在职
                //person.USER_TYPE = "1";//TODO
                //person.PERSON_TYPE = "1";//TODO
                _soa.Db.Insertable(person).ExecuteCommand();
            }
            else if (persons.Count == 1)
            {
                person = persons.First();
            }
            else
            {
                if (persons.Count(a => a.USER_NAME == userName) == 1)
                {
                    person = persons.Find(a => a.USER_NAME == userName);
                }
                else
                {
                    //关联外部系统对照表，获取默认外部系统的
                    var defaultSystemUserNo = _soa.Db.Queryable<SYS6_USER_HISID>()
                        .Where(a => a.HIS_ID == hisId && a.HIS_NAME == userName && a.DEFAULT_HISID == "1" && a.STATE_FLAG == "1")
                        .Select(a => a.USER_NO)
                        .ToList();

                    if (defaultSystemUserNo.Count == 1)
                    {
                        person = persons.Find(a => a.USER_ID == defaultSystemUserNo.First());
                    }
                }
            }
            if (person == null)
            {
                throw new BizException($"找到多条HIS_ID为{hisId}的人员记录，请先到系统数据管理模块修复数据！");
            }

            //增加采血外援人员标签
            if (!_soa.Db.Queryable<PMS_PERSON_TAG>().Any(a => a.PERSON_ID == person.PERSON_ID && a.PERSON_TAG_ID == "TG000005X" && a.TAG_PSTATE == "1"))
            {
                var personTag = new PMS_PERSON_TAG
                {
                    PERSON_ID = person.PERSON_ID,
                    PERSON_TAG_ID = "TG000005X",//采血外援
                    TAG_PSTATE = "1",
                    FIRST_RPERSON = "外部调用创建",
                    FIRST_RTIME = DateTime.Now,
                };
                _soa.Db.Insertable(personTag).ExecuteCommand();
            }
            var result = new BloodCollectPersonOutput
            {
                PERSON_ID = person.PERSON_ID,
                HOSPITAL_ID = person.HOSPITAL_ID,
                USER_NAME = person.USER_NAME,
                HIS_ID = person.HIS_ID,
                //SUPPORTER_STATE = "1"
            };

            return result;
        }
        /// <summary>
        /// 新增或保存采血外援人员
        /// </summary>
        /// <param name="parm"></param>
        /// <returns></returns>
        public BloodCollectPersonOutput SaveBloodCollectPerson(Dictionary<string, object> parm) //(BloodCollectPersonInput parm)
        {
            PMS_PERSON_INFO person = CreateOrUpdatePerson(parm);
            List<BloodCollectUploadFileOutput> files = null;
            try
            {
                files = parm.ContainsKey("FILES") ? JsonConvert.DeserializeObject<List<BloodCollectUploadFileOutput>>(parm["FILES"].ToString()) : null;
                if (files != null && files.Any())
                {
                    foreach (var file in files)
                    {
                        string isChecked = file.IS_CHECKED ? "1" : "0";
                        _soa.Db.Updateable<PMS_PERSON_FILE>().SetColumns(a => a.REMARK == isChecked).Where(a => a.FILE_ID == file.FILE_ID).ExecuteCommand();
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error($"SaveBloodCollectPerson方法解释文件失败:{ex.ToString()}");
            }
            return GetBloodCollectPersonList(null, person.PERSON_ID).FirstOrDefault();
        }
        private PMS_PERSON_INFO CreateOrUpdatePerson(Dictionary<string, object> parm)
        {
            PMS_PERSON_INFO person = null;
            if (parm == null || !parm.ContainsKey("HIS_ID") || !parm.ContainsKey("USER_NAME") || !parm.ContainsKey("HOSPITAL_ID") || !parm.ContainsKey("PHONE"))
            {
                throw new Exception("机构、工号、手机号及姓名不能为空！");
            }
            var persons = _soa.Db.Queryable<PMS_PERSON_INFO>().Where(a => a.HOSPITAL_ID == parm["HOSPITAL_ID"] && a.HIS_ID == parm["HIS_ID"] && a.PERSON_STATE == "1").ToList();
            //新增
            if (persons.Count == 0)
            {
                person = new PMS_PERSON_INFO();
                person.PERSON_ID = "BLD" + _IBaseDataServices.GetTableMaxNumber("PMS_PERSON_INFO", "PERSON_ID", 1, 1, "XH_OA").data.ToString().PadLeft(10, '0');
                person.FIRST_RPERSON = "外部调用创建";
                person.FIRST_RTIME = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                person.PERSON_STATE = "1";
                person.USER_NAME = parm["USER_NAME"].ToString();
                person.HIS_ID = parm["HIS_ID"].ToString();
                person.HOSPITAL_ID = parm["HOSPITAL_ID"].ToString();
                person.PGROUP_ID = "PG000";
                person.LAB_ID = parm.ContainsKey("LAB_ID") ? parm["LAB_ID"].ToString() : null;
                person.AREA_ID = parm.ContainsKey("AREA_ID") ? parm["AREA_ID"].ToString() : null;
                person.NATION = parm.ContainsKey("NATION") ? parm["NATION"].ToString() : null;
                person.POLITICIAN = parm.ContainsKey("POLITICIAN") ? parm["POLITICIAN"].ToString() : null;
                person.HIGHEST_DEGREE = parm.ContainsKey("HIGHEST_DEGREE") ? parm["HIGHEST_DEGREE"].ToString() : null;
                person.CARD_TYPE = parm.ContainsKey("CARD_TYPE") ? parm["CARD_TYPE"].ToString() : null;
                person.ID_CARD = parm.ContainsKey("ID_CARD") ? parm["ID_CARD"].ToString() : null;
                person.PHONE = parm.ContainsKey("PHONE") ? parm["PHONE"].ToString() : null;
                person.OFFICE_PHONE = parm.ContainsKey("OFFICE_PHONE") ? parm["OFFICE_PHONE"].ToString() : null;
                person.CURRENT_ADDRESS = parm.ContainsKey("CURRENT_ADDRESS") ? parm["CURRENT_ADDRESS"].ToString() : null;
                person.WORK_TIME = parm.ContainsKey("WORK_TIME") ? parm["WORK_TIME"].ToString() : null; // parm.WORK_TIME?.ToString("yyyy-MM-dd");
                person.OFFICE_PHONE = parm.ContainsKey("OFFICE_PHONE") ? parm["OFFICE_PHONE"].ToString() : null;


                //person.PERSON_DOC_STATE = "1";//在职
                //person.USER_TYPE = "1";//TODO
                //person.PERSON_TYPE = "1";//TODO
                _soa.Db.Insertable(person).ExecuteCommand();
            }
            else if (persons.Count == 1)
            {
                person = persons.First();
            }
            else
            {
                if (persons.Count(a => a.USER_NAME == parm["USER_NAME"]) == 1)
                {
                    person = persons.Find(a => a.USER_NAME == parm["USER_NAME"]);
                }
                else
                {
                    //关联外部系统对照表，获取默认外部系统的
                    var defaultSystemUserNo = _soa.Db.Queryable<SYS6_USER_HISID>()
                        .Where(a => a.HIS_ID == parm["HIS_ID"] && a.HIS_NAME == parm["USER_NAME"] && a.DEFAULT_HISID == "1" && a.STATE_FLAG == "1")
                        .Select(a => a.USER_NO)
                        .ToList();

                    if (defaultSystemUserNo.Count == 1)
                    {
                        person = persons.Find(a => a.USER_ID == defaultSystemUserNo.First());
                    }
                }
            }
            if (person == null)
            {
                throw new BizException($"找到多条HIS_ID为{parm["HIS_ID"]}的人员记录，请先到系统数据管理模块修复数据！");
            }
            else  //更新
            {
                person.LAB_ID = parm.ContainsKey("LAB_ID") ? parm["LAB_ID"].ToString() : person.LAB_ID;
                person.AREA_ID = parm.ContainsKey("AREA_ID") ? parm["AREA_ID"].ToString() : person.AREA_ID;
                person.NATION = parm.ContainsKey("NATION") ? parm["NATION"].ToString() : person.NATION;
                person.POLITICIAN = parm.ContainsKey("POLITICIAN") ? parm["POLITICIAN"].ToString() : person.POLITICIAN;
                person.HIGHEST_DEGREE = parm.ContainsKey("HIGHEST_DEGREE") ? parm["HIGHEST_DEGREE"].ToString() : person.HIGHEST_DEGREE;
                person.CARD_TYPE = parm.ContainsKey("CARD_TYPE") ? parm["CARD_TYPE"].ToString() : person.CARD_TYPE;
                person.ID_CARD = parm.ContainsKey("ID_CARD") ? parm["ID_CARD"].ToString() : person.ID_CARD;
                person.PHONE = parm.ContainsKey("PHONE") ? parm["PHONE"].ToString() : person.PHONE;
                person.OFFICE_PHONE = parm.ContainsKey("OFFICE_PHONE") ? parm["OFFICE_PHONE"].ToString() : person.OFFICE_PHONE;
                person.CURRENT_ADDRESS = parm.ContainsKey("CURRENT_ADDRESS") ? parm["CURRENT_ADDRESS"].ToString() : person.CURRENT_ADDRESS;
                person.WORK_TIME = parm.ContainsKey("WORK_TIME") ? parm["WORK_TIME"].ToString() : person.WORK_TIME; // parm.WORK_TIME?.ToString("yyyy-MM-dd");
                person.OFFICE_PHONE = parm.ContainsKey("OFFICE_PHONE") ? parm["OFFICE_PHONE"].ToString() : person.OFFICE_PHONE;
                _soa.Db.Updateable(person).ExecuteCommand();
            }

            //增加采血外援人员标签
            if (!_soa.Db.Queryable<PMS_PERSON_TAG>().Any(a => a.PERSON_ID == person.PERSON_ID && a.PERSON_TAG_ID == "TG000005X" && a.TAG_PSTATE == "1"))
            {
                var personTag = new PMS_PERSON_TAG
                {
                    PERSON_ID = person.PERSON_ID,
                    PERSON_TAG_ID = "TG000005X",//采血外援
                    TAG_PSTATE = "1",
                    FIRST_RPERSON = "外部调用创建",
                    FIRST_RTIME = DateTime.Now,
                };
                _soa.Db.Insertable(personTag).ExecuteCommand();
            }

            return person;
        }
        /// <summary>
        /// 获取采血外援人员列表
        /// </summary>
        /// <param name="searchKey"></param>
        /// <param name="personId"></param>
        /// <returns></returns>
        public List<BloodCollectPersonOutput> GetBloodCollectPersonList(string searchKey, string personId)
        {
            var result = new List<BloodCollectPersonOutput>();
            var persons = _soa.Db.Queryable<PMS_PERSON_INFO>()
                  .InnerJoin<PMS_PERSON_TAG>((person, tag) => tag.PERSON_ID == person.PERSON_ID && tag.PERSON_TAG_ID == "TG000005X" && person.PERSON_STATE == "1")//采血外援
                  .WhereIF(searchKey.IsNotNullOrEmpty(), (person, tag) => person.HIS_ID.Contains(searchKey) || person.USER_NAME.Contains(searchKey))
                  .WhereIF(personId.IsNotNullOrEmpty(), (person, tag) => person.PERSON_ID == personId)
                  .ToList()
                  .DistinctBy(person => person.PERSON_ID)
                  .ToList();
            var areaList = _soa.Db.Queryable<SYS6_INSPECTION_AREA>().Where(a => persons.Select(p => p.AREA_ID).Distinct().ToList().Contains(a.AREA_ID)).ToList();
            var labList = _soa.Db.Queryable<SYS6_INSPECTION_LAB>().Where(a => persons.Select(p => p.LAB_ID).Distinct().ToList().Contains(a.LAB_ID)).ToList();
            var files = _soa.Db.Queryable<PMS_PERSON_FILE>().Where(a => a.FILE_CLASS == BLOOD_CLASS_ID && persons.Select(p => p.PERSON_ID).Distinct().ToList().Contains(a.PERSON_ID)).ToList();
            foreach (var person in persons)
            {
                var pFiles = files.FindAll(a => a.PERSON_ID == person.PERSON_ID)
                                .Select(f =>
                                {
                                    _IPmsService.FillPersonFileInfo(f);
                                    var file = new BloodCollectUploadFileOutput
                                    {
                                        CERTIFICATE_KIND = f.FILE_NUM,
                                        CERTIFICATE_KIND_NAME = f.FILE_NUM == "1" ? "执业资格证" : f.FILE_NUM == "2" ? "职称证书" : f.FILE_NUM == "3" ? "培训授权证" : null,
                                        FILE_ID = f.FILE_ID,
                                        FILE_NAME = $"{f.FILE_NAME}{f.FILE_SUFFIX}",
                                        UPLOAD_PATH = f.HTTP_FILE_PATH,
                                        FILE_TYPE = f.FILE_TYPE,
                                        IS_CHECKED = f.REMARK == "1" ? true : false,
                                    };
                                    return file;
                                })
                                .ToList();
                var p = new BloodCollectPersonOutput
                {
                    PERSON_ID = person.PERSON_ID,
                    HOSPITAL_ID = person.HOSPITAL_ID,
                    USER_NAME = person.USER_NAME,
                    HIS_ID = person.HIS_ID,
                    AREA_ID = person.AREA_ID,
                    CARD_TYPE = person.CARD_TYPE,
                    ID_CARD = person.ID_CARD,
                    CURRENT_ADDRESS = person.CURRENT_ADDRESS,
                    HIGHEST_DEGREE = person.HIGHEST_DEGREE,
                    LAB_ID = person.LAB_ID,
                    NATION = person.NATION,
                    PHONE = person.PHONE,
                    OFFICE_PHONE = person.OFFICE_PHONE,
                    POLITICIAN = person.POLITICIAN,
                    WORK_TIME = DateTime.TryParse(person.WORK_TIME, out var wortTime) ? wortTime : null,
                    //SUPPORTER_STATE = "1"
                    AREA_NAME = areaList.Find(a => a.AREA_ID == person.AREA_ID)?.AREA_NAME,
                    LAB_NAME = labList.Find(a => a.LAB_ID == person.LAB_ID)?.LAB_NAME,
                    CARD_TYPE_NAME = _IBaseDataServices.RecordClassBaseName("证件类型", person.CARD_TYPE),
                    HIGHEST_DEGREE_NAME = _IBaseDataServices.RecordClassBaseName("最高学历", person.HIGHEST_DEGREE),
                    NATION_NAME = _IBaseDataServices.RecordClassBaseName("民族", person.NATION),
                    POLITICIAN_NAME = _IBaseDataServices.RecordClassBaseName("政治面貌", person.POLITICIAN),
                    FILES = pFiles,
                };
                result.Add(p);
            }
            return result;
        }
        /// <summary>
        /// 删除采血外援人员证件文件
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        public ResultDto DeleteBloodCollectPersonFile(BloodCollectUploadFileOutput file)
        {
            var result = _IPmsService.DeleteNotHandleFile(file.FILE_ID);
            return result;
        }
        /// <summary>
        /// 上传采血外援人员证件文件
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        public BloodCollectUploadFileOutput UploadBloodCollectPersonFile([FromForm] BloodCollectUploadFileInput file)
        {
            var personParm = new Dictionary<string, object>();
            personParm["HIS_ID"] = file.HIS_ID;
            personParm["USER_NAME"] = file.USER_NAME;
            personParm["HOSPITAL_ID"] = file.HOSPITAL_ID;
            var operatorHisName = $"采用外援{file.HIS_ID}_{file.USER_NAME}";
            PMS_PERSON_INFO person = CreateOrUpdatePerson(personParm);

            string fileName = file.FILE.FileName;
            string personId = person.PERSON_ID;
            string resultNums = string.Empty;
            string fileGuidName = Guid.NewGuid().ToString("N");
            string suffix = fileName.Substring(fileName.LastIndexOf('.'));
            string suffixLower = suffix.ToLower();

            var noRecordFile = CreateUploadFile(person.PERSON_ID, BLOOD_CLASS_ID, null, file.FILE.FileName, suffixLower, fileGuidName, file.FILE);
            UploadRecordFile(personParm["HOSPITAL_ID"].ToString(), operatorHisName, fileGuidName, file.CERTIFICATE_KIND, false, noRecordFile);

            var dbFile = _soa.Db.Queryable<PMS_PERSON_FILE>().Where(a => a.FILE_CNAME == fileGuidName).First();
            _IPmsService.FillPersonFileInfo(dbFile);
            var result = new BloodCollectUploadFileOutput
            {
                CERTIFICATE_KIND = dbFile.FILE_NUM,
                CERTIFICATE_KIND_NAME = dbFile.FILE_NUM == "1" ? "执业资格证" : dbFile.FILE_NUM == "2" ? "职称证书" : dbFile.FILE_NUM == "3" ? "培训授权证" : null,
                FILE_ID = dbFile.FILE_ID,
                FILE_NAME = $"{dbFile.FILE_NAME}{dbFile.FILE_SUFFIX}",
                UPLOAD_PATH = dbFile.HTTP_FILE_PATH,
                FILE_TYPE = dbFile.FILE_TYPE,
                IS_CHECKED = dbFile.REMARK == "1" ? true : false,
            };
            return result;
        }


        private PmsUploadFileDto CreateUploadFile(string personId, string classId, string recordId, string fileName, string suffix, string fileGuidName, IFormFile formFile)
        {
            PmsUploadFileDto file = new PmsUploadFileDto
            {
                PERSON_ID = personId,
                ARCHIVE_TABLE = classId,
                RECORD_ID = recordId,
                FILE_NAME = fileName,
                FILE_SUFFIX = suffix,
                //主要区分PDF与图片
                //FILE_TYPE = (suffix == ".pdf") ? "PDF" : (suffix == ".bmp" || suffix == ".jpg" || suffix == ".jpeg" || suffix == ".png" || suffix == ".gif" || suffix == ".jfif") ? "IMG" : null,//todo
                UPLOAD_FILE_NAME = fileGuidName + suffix,
                UPLOAD_FOLDER_NAME = OfficeHelper.PathCombine("PMS", personId),
                FILE = formFile
            };
            FixUploadFileErrors(file, fileGuidName);
            return file;


            void FixUploadFileErrors(UploadFileDto file, string fileGuidName)
            {
                file.FILE_NAME = file.FILE_NAME.Replace(".", "_");//避免后缀误识别而报错
                file.FILE_SUFFIX = file.FILE_SUFFIX.ToLower(); //后缀转成小写开
                file.UPLOAD_FILE_NAME = fileGuidName + file.FILE_SUFFIX;//文件名称
                if (file.FILE_SUFFIX == ".pdf")
                {
                    file.FILE_TYPE = "PDF";
                }
                else if (file.FILE_SUFFIX == ".bmp" || file.FILE_SUFFIX == ".jpg" || file.FILE_SUFFIX == ".jpeg" || file.FILE_SUFFIX == ".png" || file.FILE_SUFFIX == ".gif" || file.FILE_SUFFIX == ".jfif")
                {
                    file.FILE_TYPE = "IMG";
                }
                else if (file.FILE_SUFFIX == ".doc" || file.FILE_SUFFIX == ".docx")
                {
                    file.FILE_TYPE = "DOC";
                }
            }
        }
        private string UploadRecordFile(string hospitalId, string operatorHisName, string fileGuidName, string certificateKind, bool isChecked, PmsUploadFileDto file)
        {
            string resultNums = string.Empty;

            var claim = _httpContext.HttpContext.User.ToClaimsDto();
            string user_name = operatorHisName;//操作人名称
            string hospital_id = hospitalId;
            string contentRootPath = _hostingEnvironment.ContentRootPath;

            file.FILE_NAME = file.FILE_NAME.Replace(".", "_");//避免后缀误识别而报错
            file.FILE_SUFFIX = file.FILE_SUFFIX.ToLower(); //后缀转成小写开
            file.UPLOAD_FOLDER_NAME = OfficeHelper.PathCombine("PMS", file.PERSON_ID);
            file.UPLOAD_FILE_NAME = fileGuidName + file.FILE_SUFFIX;//文件名称

            PMS_PERSON_FILE pms_person_file = new PMS_PERSON_FILE();
            pms_person_file.FILE_CNAME = fileGuidName;
            pms_person_file.HOSPITAL_ID = hospital_id;
            pms_person_file.FILE_CLASS = file.ARCHIVE_TABLE;
            pms_person_file.FILE_NAME = file.FILE_NAME;
            pms_person_file.FILE_SUFFIX = file.FILE_SUFFIX;
            pms_person_file.FILE_PATH = "/" + file.UPLOAD_FOLDER_NAME + "/";
            pms_person_file.FILE_STATE = "0";
            pms_person_file.FILE_NUM = certificateKind;
            pms_person_file.REMARK = isChecked ? "1" : "0";

            if (file.FILE_SUFFIX == ".pdf")
            {
                pms_person_file.FILE_TYPE = "PDF";
            }
            else if (file.FILE_SUFFIX == ".bmp" || file.FILE_SUFFIX == ".jpg" || file.FILE_SUFFIX == ".jpeg" || file.FILE_SUFFIX == ".png" || file.FILE_SUFFIX == ".gif" || file.FILE_SUFFIX == ".jfif")
            {
                pms_person_file.FILE_TYPE = "IMG";
            }
            else if (file.FILE_SUFFIX == ".doc" || file.FILE_SUFFIX == ".docx")
            {
                pms_person_file.FILE_TYPE = "DOC";
            }
            string file_id = _IPmsService.SaveAnnexPath(pms_person_file, user_name, "I", hospital_id, file.PERSON_ID, file.FILE_CLASS, file.ARCHIVE_TABLE);
            if (file_id.IsNotNullOrEmpty())
            {
                var result = _IUploadFileService.UploadFileOperate(file);
                if (result?.success == false)
                    throw new BizException($"UploadRecordFile执行时发生异常：{result.msg}");
                return OfficeHelper.PathCombine(FileHttpUrl, result.data.ToString());
            }
            return "上传文件失败";
        }


        #endregion 
    }
}
