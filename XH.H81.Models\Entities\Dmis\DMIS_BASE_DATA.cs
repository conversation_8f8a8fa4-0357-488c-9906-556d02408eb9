using System;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using SqlSugar;
using H.Utility.SqlSugarInfra;

namespace XH.H81.Models.Entities.Dmis
{
    [DBOwner("XH_OA")]
    public class DMIS_BASE_DATA
    {
        /// <summary>
        /// 序号
        /// </summary>
        [Key]
        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        [Column("DATA_ID")]
        [Required(ErrorMessage = "序号不允许为空")]

        [StringLength(20, ErrorMessage = "序号长度不能超出20字符")]
        [Unicode(false)]
        public string DATA_ID { get; set; }

        /// <summary>
        /// 英文名
        /// </summary>
        [Column("DATA_ENAME")]
        [StringLength(100, ErrorMessage = "英文名长度不能超出100字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? DATA_ENAME { get; set; }

        /// <summary>
        /// 首次登记人
        /// </summary>
        [Column("FIRST_RPERSON")]
        [StringLength(50, ErrorMessage = "首次登记人长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FIRST_RPERSON { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        [Column("LAST_MTIME")]
        [Unicode(false)]
        public DateTime? LAST_MTIME { get; set; }

        /// <summary>
        /// 父级ID
        /// </summary>
        [Column("FATHER_ID")]
        [StringLength(20, ErrorMessage = "父级ID长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FATHER_ID { get; set; }

        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FATHER_NAME { get; set; }
        /// <summary>
        /// 自定义码
        /// </summary>
        [Column("CUSTOM_CODE")]
        [StringLength(20, ErrorMessage = "自定义码长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CUSTOM_CODE { get; set; }

        /// <summary>
        /// 状态  0禁用1在用
        /// </summary>
        [Column("DATA_STATE")]
        [StringLength(20, ErrorMessage = "状态  0禁用1在用长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? DATA_STATE { get; set; }

        /// <summary>
        /// 排序号
        /// </summary>
        [Column("DATA_SORT")]
        [StringLength(20, ErrorMessage = "排序号长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? DATA_SORT { get; set; }

        /// <summary>
        /// 中文名
        /// </summary>
        [Column("DATA_CNAME")]
        [StringLength(100, ErrorMessage = "中文名长度不能超出100字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? DATA_CNAME { get; set; }

        /// <summary>
        /// 类ID
        /// </summary>
        [Column("CLASS_ID")]
        [Required(ErrorMessage = "类ID不允许为空")]

        [StringLength(50, ErrorMessage = "类ID长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string CLASS_ID { get; set; }

        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CLASS_NAME { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        [Column("REMARK")]
        [StringLength(200, ErrorMessage = "备注长度不能超出200字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? REMARK { get; set; }

        /// <summary>
        /// 首次登记时间
        /// </summary>
        [Column("FIRST_RTIME")]
        [Unicode(false)]
        public DateTime? FIRST_RTIME { get; set; }

        /// <summary>
        /// 最后修改人员
        /// </summary>
        [Column("LAST_MPERSON")]
        [StringLength(50, ErrorMessage = "最后修改人员长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? LAST_MPERSON { get; set; }

        /// <summary>
        /// 拼音码
        /// </summary>
        [Column("SPELL_CODE")]
        [StringLength(20, ErrorMessage = "拼音码长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? SPELL_CODE { get; set; }

        /// <summary>
        /// 医疗机构ID
        /// </summary>
        [Column("HOSPITAL_ID")]
        [Required(ErrorMessage = "医疗机构ID不允许为空")]

        [StringLength(20, ErrorMessage = "医疗机构ID长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string HOSPITAL_ID { get; set; }
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? IF_RELATED_WORK { get; set; }

        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? IF_RELATED_WORK_NAME { get; set; }

        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? UNIT_ID { get; set; }

        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? UNIT_NAME { get; set; }

        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? REFRESH_TYPE { get; set; }


        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? TOP_INFO_CLASS { get; set; }

    }
}
