﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Dtos.Tag
{
    public class Rules
    {
        /// <summary>
        /// 
        /// </summary>
        public bool required { get; set; }
    }

    public class WigetProps2
    {
        /// <summary>
        /// 请输入
        /// </summary>
        public string placeholder { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool allowClear { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int maxLength { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool disabled { get; set; }
    }

    public class StyleJson
    {

        /// <summary>
        /// 
        /// </summary>
        public string dataClass { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string dataType { get; set; }


        public bool labelHide { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public LabelStyle labelStyle { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public object rules { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public object wigetProps { get; set; }


    }

}
