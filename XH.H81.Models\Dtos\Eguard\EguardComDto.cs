﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Dtos.Eguard
{
    /// <summary>
    /// 门禁组合
    /// </summary>
    public class EguardComDto
    {
        /// <summary>
        /// 组合ID
        /// </summary>
        public string? EGUARD_COM_ID { get; set; }
        /// <summary>
        /// 组合名称
        /// </summary>
        public string? EGUARD_COM_NAME { get; set; }
        /// <summary>
        /// 组合状态
        /// </summary>
        public string? EGUARD_COM_STATE { get; set; }
        /// <summary>
        /// 组合描述
        /// </summary>
        public string? EGUARD_COM_DESC { get; set; }
        /// <summary>
        /// 门禁个数
        /// </summary>
        public int EGUARD_AMOUNT { get; set; }
        public string? FIRST_RPERSON { get; set; }
        public DateTime? FIRST_RTIME { get; set; }
        public string? LAST_MPERSON { get; set; }
        public DateTime? LAST_MTIME { get; set; }
        /// <summary>
        /// 关联的门禁id列表
        /// </summary>
        public List<string>? EGUARD_IDS { get; set; }
    }

    /// <summary>
    /// 门禁组合启用/禁用
    /// </summary>
    public class ChangeEguardComStateDto
    {
        [Required(ErrorMessage = "门禁组合ID不能为空")]
        public string EGUARD_COM_ID { get; set; }
        /// <summary>
        /// 0禁用 1启用
        /// </summary>
        [Range(0, 1, ErrorMessage = "状态码入参错误")]
        public string EGUARD_COM_STATE { get; set; }
    }

    /// <summary>
    /// 删除门禁组合信息
    /// </summary>
    public class DeleteEguardComDto
    {
        [Required(ErrorMessage = "门禁组合ID不能为空")]
        public string EGUARD_COM_ID { get; set; }
    }

    /// <summary>
    /// 判断门禁组合或门禁是否可选
    /// </summary>
    public class JudgEguardCom
    {
        /// <summary>
        /// 岗位ID
        /// </summary>
        public List<string>? RoleIds { get; set; }
        /// <summary>
        /// 门禁人员ID 按照人员设置
        /// </summary>
        public List<string>? PersonIds { get; set; }
        /// <summary>
        /// 门禁类型 1门禁组合 2门禁
        /// </summary>
        public string? EGUARD_DATA_TYPE { get; set; }
        /// <summary>
        /// 授权id
        /// </summary>
        public string EGUARD_AUTH_ID { get; set; }
    }
}
