﻿using AutoMapper;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XH.H81.Models.ExternalEntity;
using XH.LAB.UTILS.Models;

namespace XH.H81.Models.Dtos.Pms
{
    [AutoMap(typeof(SYS6_MENU), ReverseMap = true)]
    public class Sys6MenuDto
    {
        public string MENU_ID { get; set; }

        public string? FIRST_RPERSON { get; set; }

        public string? REFRESH_TYPE { get; set; }

        public string? FONT_COLOR { get; set; }

        public string? BACKGROUND_COLOR { get; set; }

        public string? MENU_NAME { get; set; }

        public DateTime? LAST_MTIME { get; set; }

        public string? MENU_MODULE { get; set; }

        public string? RIGHT_MENU { get; set; }

        public string? IF_ASSIST_FUNC { get; set; }

        public string? IF_RIGHT_MENU { get; set; }

        public string? MENU_TYPE { get; set; }

        public string? TOOLBAR_SORT { get; set; }

        public string? IF_TOOLBAR { get; set; }

        public string? FUNC_ID { get; set; }

        public string? PARENT_CODE { get; set; }

        public string? SYS_MENU { get; set; }

        public string? MENU_ICON { get; set; }

        public string? SHORTCUT_KEY { get; set; }

        public string? MENU_SORT { get; set; }

        public string? MENU_ENAME { get; set; }

        public string? MENU_STATE { get; set; }

        public string? MENU_CODE { get; set; }

        public string? MENU_URL { get; set; }

        public string? RIGHT_MENU_SORT { get; set; }

        public string? REMARK { get; set; }

        public string? MENU_SNAME { get; set; }

        public string? IF_EXEC_AFTER { get; set; }

        public DateTime? FIRST_RTIME { get; set; }

        public string? LAST_MPERSON { get; set; }

        public string? EXEC_AFTER_FUNC { get; set; }

        public string? MENU_CLASS { get; set; }

        public string? TOOLBAR_SNAME { get; set; }

        public string? MENU_LEVEL { get; set; }

        public string? MENU_BNAME { get; set; }

        public string? ASSIST_FUNC { get; set; }

        public List<Sys6MenuDto> ChildMenu {  get; set; }

    }
}
