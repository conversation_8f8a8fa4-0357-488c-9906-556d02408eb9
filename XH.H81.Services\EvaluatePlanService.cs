﻿using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using RestSharp;
using Spectre.Console;
using SqlSugar;
using System.Diagnostics;
using XH.H81.IServices;
using XH.H81.Models.Dtos;
using XH.H81.Models.Dtos.EvaluatePlan;
using XH.H81.Models.Dtos.Pms;
using XH.H81.Models.Entities.EvaluatePlan;
using XH.H81.Models.Entities.others;
using XH.H81.Models.Entities.Pms;
using XH.H81.Models.Enums;
using XH.H81.Models.ExternalEntity;
using XH.H81.Models.SugarDbContext;
using XH.LAB.UTILS.Helpers;
using XH.LAB.UTILS.Models;
using XH.LAB.UTILS.Models.Dto;
using XH.LAB.UTILS.Models.Entites;
using XH.LAB.UTILS.Models.Enums;
using Serilog;
using AutoMapper;
using XH.LAB.UTILS.Interface;
using XH.H81.Models;
using Npoi.Mapper;
using SqlSugar.Extensions;
using OA_CERTIFICATE_DICT = XH.H81.Models.Entities.EvaluatePlan.OA_CERTIFICATE_DICT;
using XH.H81.Models.Entities.Tag;

namespace XH.H81.Services
{
    public class EvaluatePlanService : IEvaluatePlanService
    {
        private readonly IHttpContextAccessor _httpContext;
        private readonly IConfiguration _configuration;
        private readonly IBaseDataServices _baseDataServices;
        private readonly IAuthorityService _authorityService;
        private readonly IOrganizationTreeService _organizationTreeService;
        private readonly RestClient _clientH07;
        private readonly IMapper _mapper;

        private readonly ISqlSugarUow<SugarDbContext_Master> _soa;
        public EvaluatePlanService(IConfiguration configuration, IHttpContextAccessor httpContext, ISqlSugarUow<SugarDbContext_Master> dbContext, IBaseDataServices baseDataServices, IMapper mapper, IOrganizationTreeService organizationTreeService, IAuthorityService authorityService)
        {
            _configuration = configuration;
            _httpContext = httpContext;
            _soa = dbContext;
            _mapper = mapper;
            _baseDataServices = baseDataServices;
            _organizationTreeService = organizationTreeService;
            _authorityService = authorityService;
            var addressH07 = configuration["H07"];//系统数据
            ExecutingChangeSqlHelper.ExecutingChangeSql(_soa, configuration, true);
            if (addressH07.IsNotNullOrEmpty())
            {
                _clientH07 = new RestClient(new RestClientOptions()
                {
                    RemoteCertificateValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true,
                    BaseUrl = new Uri(addressH07),
                    ThrowOnAnyError = true
                });
            }
        }



        #region 规评方案
        /// <summary>
        ///获取基础数据名称
        /// </summary>
        /// <param name="class_id"></param>
        /// <param name="data_id"></param>
        /// <returns></returns>
        public string RecordClassBaseName(string class_id, string data_id)
        {
            string className = string.Empty;
            if (data_id != null)
            {
                List<SYS6_BASE_DATA> lis5_base_data = _baseDataServices.GetSys6BaseData();
                if (lis5_base_data.Where(x => x.CLASS_ID == class_id && x.DATA_ID == data_id).FirstOrDefault() != null)
                {
                    className = lis5_base_data.Where(x => x.CLASS_ID == class_id && x.DATA_ID == data_id).FirstOrDefault().DATA_CNAME;
                }
            }
            return className;
        }


        public List<OaEvaluatePlanDict> GetEvaluatePlanDict(string HOSPITAL_ID, string EPLAN_TYPE, string EPLAN_STATE, string LAB_PGROUP_ID, string SEARCH_KEY,
            List<string> EPLAN_ID_LIST = null)
        {
            List<OaEvaluatePlanDict> result = _soa.Db.Queryable<OA_EVALUATE_PLAN_DICT>()
                .WhereIF(EPLAN_STATE.IsNullOrEmpty(), a => a.EPLAN_STATE != "2")
                .WhereIF(EPLAN_STATE.IsNotNullOrEmpty(), a => a.EPLAN_STATE == EPLAN_STATE)
                .WhereIF(HOSPITAL_ID.IsNotNullOrEmpty(), a => a.HOSPITAL_ID == HOSPITAL_ID)
                .WhereIF(EPLAN_TYPE.IsNotNullOrEmpty(), a => a.EPLAN_TYPE == EPLAN_TYPE)
                .WhereIF(LAB_PGROUP_ID.IsNotNullOrEmpty() && LAB_PGROUP_ID.StartsWith("L"), a => a.LAB_ID == LAB_PGROUP_ID && a.PGROUP_ID == null)
                .WhereIF(LAB_PGROUP_ID.IsNotNullOrEmpty() && LAB_PGROUP_ID.StartsWith("P"), a => a.PGROUP_ID == LAB_PGROUP_ID)
                .WhereIF(SEARCH_KEY.IsNotNullOrEmpty(), a => a.EPLAN_NAME.Contains(SEARCH_KEY))
                .WhereIF(EPLAN_ID_LIST != null && EPLAN_ID_LIST.Any(), a => EPLAN_ID_LIST.Contains(a.EPLAN_ID))
                .OrderBy(a => a.EPLAN_SORT)
                .Select(a => new OaEvaluatePlanDict
                {
                    EPLAN_ID = a.EPLAN_ID.SelectAll(),
                })
                .ToList();

            List<string> labIds = result.Select(a => a.LAB_ID).Distinct().ToList();
            var labNames = _soa.Db.Queryable<SYS6_INSPECTION_LAB>()
                .Where(a => labIds.Contains(a.LAB_ID))
                .Select(a => new { a.LAB_ID, a.LAB_NAME })
                .ToList();
            List<string> pGroupIds = result.Select(a => a.PGROUP_ID).Distinct().ToList();
            var pgroupNames = _soa.Db.Queryable<SYS6_INSPECTION_PGROUP>()
                .Where(a => pGroupIds.Contains(a.PGROUP_ID))
                .Select(a => new { a.PGROUP_ID, a.PGROUP_NAME })
                .ToList();

            foreach (var item in result)
            {
                item.LAB_PGROUP_ID = item.PGROUP_ID ?? item.LAB_ID;
                item.LAB_PGROUP_NAME = pgroupNames.Find(a => a.PGROUP_ID == item.LAB_PGROUP_ID)?.PGROUP_NAME
                    ?? labNames.Find(a => a.LAB_ID == item.LAB_PGROUP_ID)?.LAB_NAME;
                var split = item.LAST_MPERSON.Split('_');
                item.LAST_MPERSON_NAME = split.Count() >= 2 ? split[1] : "";
            }
            return result;
        }

        public string SaveEvaluatePlanDict(OaEvaluatePlanDict dto, string hisName, string newEplanId = null)
        {
            string eplanId = dto.EPLAN_ID;
            OA_EVALUATE_PLAN_DICT eplanEty = null;
            bool isInsert = false;

            if (eplanId.IsNullOrEmpty())
            {
                eplanId = newEplanId ?? ("EP" + _baseDataServices.GetTableMaxNumber("OA_EVALUATE_PLAN_DICT", "EPLAN_ID", 1, 1, "XH_OA").data.ToString().PadLeft(8, '0'));
                eplanEty = new OA_EVALUATE_PLAN_DICT
                {
                    EPLAN_ID = eplanId,
                    HOSPITAL_ID = dto.HOSPITAL_ID,
                    EPLAN_NAME = dto.EPLAN_NAME,
                    EPLAN_TYPE = dto.EPLAN_TYPE,
                    EPLAN_SORT = EntityHelper.GetSort(),
                    EPLAN_STATE = "1", // 默认在用状态
                    FIRST_RPERSON = hisName,
                    FIRST_RTIME = DateTime.Now,
                    REMARK = dto.REMARK
                };
                isInsert = true;
            }
            else
            {
                if (_soa.Db.Queryable<OA_EVALUATE_PLAN_SETUP>().Any(a => a.EPLAN_ID == eplanId && a.EPLAN_SSTATE == EvaluatePlanSetupStateEnum.ENABLE.ToIntStr()))
                {
                    throw new BizException("存在已启用的规评设置，无法修改规评方案！");
                }
                eplanEty = _soa.Db.Queryable<OA_EVALUATE_PLAN_DICT>().Where(a => a.EPLAN_ID == eplanId).First();
            }

            if (eplanEty != null)
            {
                var pgroup = _soa.Db.Queryable<SYS6_INSPECTION_PGROUP>().Where(a => a.PGROUP_ID == dto.LAB_PGROUP_ID).Select(a => new { a.LAB_ID, a.PGROUP_ID }).First();
                if (pgroup != null)
                {
                    eplanEty.PGROUP_ID = pgroup.PGROUP_ID;
                    eplanEty.LAB_ID = pgroup.LAB_ID;
                }
                else
                {
                    eplanEty.LAB_ID = dto.LAB_PGROUP_ID;
                }
                eplanEty.EPLAN_NAME = dto.EPLAN_NAME;
                eplanEty.REMARK = dto.REMARK;
                eplanEty.LAST_MPERSON = hisName;
                eplanEty.LAST_MTIME = DateTime.Now;

                if (isInsert)
                    _soa.Db.Insertable(eplanEty).ExecuteCommand();
                else
                    _soa.Db.Updateable(eplanEty).UpdateColumns(a => new { a.EPLAN_NAME, a.LAB_ID, a.PGROUP_ID, a.REMARK, a.LAST_MPERSON, a.LAST_MTIME }).ExecuteCommand();
            }
            else
            {
                throw new Exception("找不到该规评方案，保存失败！");
            }

            return eplanId;
        }

        public string EnableEvaluatePlanDict(string eplanId, bool isDisable, string hisName)
        {
            if (isDisable && _soa.Db.Queryable<OA_EVALUATE_PLAN_SETUP>().Any(a => a.EPLAN_ID == eplanId && a.EPLAN_SSTATE == EvaluatePlanSetupStateEnum.ENABLE.ToIntStr()))
            {
                throw new BizException("存在已启用的规评设置，无法修改规评方案！");
            }

            var eplanEty = _soa.Db.Queryable<OA_EVALUATE_PLAN_DICT>().Where(a => a.EPLAN_ID == eplanId).First();

            if (eplanEty != null)
            {
                eplanEty.EPLAN_STATE = isDisable ? "0" : "1"; // 将状态设置为删除
                eplanEty.LAST_MPERSON = hisName;
                eplanEty.LAST_MTIME = DateTime.Now;
                _soa.Db.Updateable(eplanEty).UpdateColumns(a => new
                {
                    a.EPLAN_STATE,
                    a.LAST_MPERSON,
                    a.LAST_MTIME
                }).ExecuteCommand();
            }
            else
            {
                throw new Exception($"找不到该规评方案，{(isDisable ? "禁用" : "启用")}失败！");
            }

            return $"{(isDisable ? "禁用" : "启用")}成功！";
        }

        public string DeleteEvaluatePlanDict(string eplanId, string hisName)
        {
            var setups = _soa.Db.Queryable<OA_EVALUATE_PLAN_SETUP>().Where(a => a.EPLAN_ID == eplanId && a.EPLAN_SSTATE != EvaluatePlanSetupStateEnum.DELETE.ToIntStr()).ToList();
            if (setups.Any(a => a.EPLAN_SSTATE == EvaluatePlanSetupStateEnum.ENABLE.ToIntStr()))
            {
                throw new BizException("存在已启用的规评设置，无法删除规评方案！");
            }
            var eplanEty = _soa.Db.Queryable<OA_EVALUATE_PLAN_DICT>().Where(a => a.EPLAN_ID == eplanId).First();

            if (eplanEty != null)
            {
                eplanEty.EPLAN_STATE = "2"; // 将状态设置为删除
                eplanEty.LAST_MPERSON = hisName;
                eplanEty.LAST_MTIME = DateTime.Now;
                _soa.Db.Updateable(eplanEty).UpdateColumns(a => new
                {
                    a.EPLAN_STATE,
                    a.LAST_MPERSON,
                    a.LAST_MTIME
                }).ExecuteCommand();

                //删除规评设置
                if (setups.Any())
                {
                    foreach (var set in setups)
                    {
                        set.EPLAN_SSTATE = EvaluatePlanSetupStateEnum.DELETE.ToIntStr();
                        set.LAST_MPERSON = hisName;
                        set.LAST_MTIME = DateTime.Now;
                    }
                    _soa.Db.Updateable(setups).UpdateColumns(a => new { a.EPLAN_SSTATE, a.LAST_MPERSON, a.LAST_MTIME }).ExecuteCommand();
                }
            }
            else
            {
                throw new Exception("找不到该规评方案，删除失败！");
            }

            return "删除成功！";
        }
        public string UnDeleteEvaluatePlanDict(string eplanId, string hisName)
        {
            var eplanEty = _soa.Db.Queryable<OA_EVALUATE_PLAN_DICT>().Where(a => a.EPLAN_ID == eplanId).First();

            if (eplanEty != null)
            {
                eplanEty.EPLAN_STATE = "1"; // 将状态设置为在用
                eplanEty.LAST_MPERSON = hisName;
                eplanEty.LAST_MTIME = DateTime.Now;
                _soa.Db.Updateable(eplanEty).UpdateColumns(a => new
                {
                    a.EPLAN_STATE,
                    a.LAST_MPERSON,
                    a.LAST_MTIME
                }).ExecuteCommand();
            }
            else
            {
                throw new Exception("找不到该规评方案，恢复失败！");
            }

            return "恢复成功！";
        }

        public bool SortEvaluatePlanDict(List<OaEvaluatePlanDict> eplans)
        {
            var eplanIds = eplans.Where(a => a.EPLAN_ID.IsNotNullOrEmpty()).Select(a => a.EPLAN_ID).ToList();
            if (!eplanIds.Any())
                return true;
            var data = _soa.Db.Queryable<OA_EVALUATE_PLAN_DICT>().Where(w => eplanIds.Contains(w.EPLAN_ID)).ToList();
            foreach (var d in data)
            {
                int index = eplanIds.IndexOf(d.EPLAN_ID);
                if (index > -1)
                    d.EPLAN_SORT = index.ToString().PadLeft(10, '0');
            }
            int rows = _soa.Db.Updateable(data).ExecuteCommand();
            return rows > 0;
        }

        #endregion

        #region 限权组合

        public List<SYS6_LIMIT_ROLE_DICT> GetLimitRoleDict(string HOSPITAL_ID, string LROLE_STATE, string SEARCH_KEY)
        {
            List<SYS6_LIMIT_ROLE_DICT> result = _soa.Db.Queryable<SYS6_LIMIT_ROLE_DICT>()
                .Where(a => a.LROLE_STATE != "2")
                .WhereIF(HOSPITAL_ID.IsNotNullOrEmpty(), a => a.HOSPITAL_ID == HOSPITAL_ID)
                .WhereIF(LROLE_STATE.IsNotNullOrEmpty(), a => a.LROLE_STATE == LROLE_STATE)
                .WhereIF(SEARCH_KEY.IsNotNullOrEmpty(), a => a.LROLE_NAME.Contains(SEARCH_KEY))
                .OrderBy(a => a.LROLE_SORT)
                .ToList();
            return result;
        }



        public string SaveLimitRoleDict(SYS6_LIMIT_ROLE_DICT dto, string hisName)
        {
            string lroleNo = dto.LROLE_NO;
            SYS6_LIMIT_ROLE_DICT lroleEty = null;
            bool isInsert = false;

            if (lroleNo.IsNullOrEmpty())
            {
                lroleNo = "LR" + _baseDataServices.GetTableMaxNumber("SYS6_LIMIT_ROLE_DICT", "LROLE_NO", 1, 1, "XH_SYS").data.ToString().PadLeft(8, '0');
                lroleEty = new SYS6_LIMIT_ROLE_DICT
                {
                    LROLE_NO = lroleNo,
                    HOSPITAL_ID = dto.HOSPITAL_ID,
                    LROLE_NAME = dto.LROLE_NAME,
                    LROLE_DESCRIPTION = dto.LROLE_DESCRIPTION,
                    LROLE_SORT = EntityHelper.GetSort(),
                    LROLE_STATE = "1", // 默认在用状态
                    FIRST_RPERSON = hisName,
                    FIRST_RTIME = DateTime.Now,
                    REMARK = dto.REMARK
                };
                isInsert = true;
            }
            else
            {
                lroleEty = _soa.Db.Queryable<SYS6_LIMIT_ROLE_DICT>().Where(a => a.LROLE_NO == lroleNo && a.LROLE_STATE != "2").First();
            }

            if (lroleEty != null)
            {
                lroleEty.LROLE_NAME = dto.LROLE_NAME;
                lroleEty.LROLE_DESCRIPTION = dto.LROLE_DESCRIPTION;
                lroleEty.REMARK = dto.REMARK;
                lroleEty.LAST_MPERSON = hisName;
                lroleEty.LAST_MTIME = DateTime.Now;

                if (isInsert)
                    _soa.Db.Insertable(lroleEty).ExecuteCommand();
                else
                    _soa.Db.Updateable(lroleEty).UpdateColumns(a => new { a.LROLE_NAME, a.LROLE_DESCRIPTION, a.REMARK, a.LAST_MPERSON, a.LAST_MTIME }).ExecuteCommand();
            }
            else
            {
                throw new Exception("找不到该限权组合，保存失败！");
            }

            return lroleNo;
        }


        public string EnableLimitRoleDict(string lroleNo, bool isDisable, string hisName)
        {
            var lroleEty = _soa.Db.Queryable<SYS6_LIMIT_ROLE_DICT>().Where(a => a.LROLE_NO == lroleNo).First();

            if (lroleEty != null)
            {
                if (isDisable && _soa.Db.Queryable<OA_EVALUATE_PLAN_SETUP>().Any(a => a.EPLAN_SSTATE != EvaluatePlanSetupStateEnum.DELETE.ToIntStr() && ("+" + a.PROLE_COM_SID + "+").Contains("+" + lroleEty.LROLE_NO + "+")))
                    throw new BizException("该限权组合已被规评设置应用，无法禁用！");

                lroleEty.LROLE_STATE = isDisable ? "0" : "1"; // 将状态设置为禁用或启用
                lroleEty.LAST_MPERSON = hisName;
                lroleEty.LAST_MTIME = DateTime.Now;
                _soa.Db.Updateable(lroleEty).UpdateColumns(a => new
                {
                    a.LROLE_STATE,
                    a.LAST_MPERSON,
                    a.LAST_MTIME
                }).ExecuteCommand();
            }
            else
            {
                throw new Exception($"找不到该限权组合，{(isDisable ? "禁用" : "启用")}失败！");
            }

            return $"{(isDisable ? "禁用" : "启用")}成功！";
        }


        public string DeleteLimitRoleDict(string lroleNo, string hisName)
        {
            var lroleEty = _soa.Db.Queryable<SYS6_LIMIT_ROLE_DICT>().Where(a => a.LROLE_NO == lroleNo).First();

            if (lroleEty != null)
            {
                if (_soa.Db.Queryable<OA_EVALUATE_PLAN_SETUP>().Any(a => a.EPLAN_SSTATE != EvaluatePlanSetupStateEnum.DELETE.ToIntStr() && ("+" + a.PROLE_COM_SID + "+").Contains("+" + lroleEty.LROLE_NO + "+")))
                    throw new BizException("该限权组合已被规评设置应用，无法删除！");

                lroleEty.LROLE_STATE = "2"; // 将状态设置为删除
                lroleEty.LAST_MPERSON = hisName;
                lroleEty.LAST_MTIME = DateTime.Now;
                _soa.Db.Updateable(lroleEty).UpdateColumns(a => new
                {
                    a.LROLE_STATE,
                    a.LAST_MPERSON,
                    a.LAST_MTIME
                }).ExecuteCommand();
            }
            else
            {
                throw new Exception("找不到该限权组合，删除失败！");
            }

            return "删除成功！";
        }

        public bool SortLimitRoleDict(List<string> lroleNos)
        {
            lroleNos = lroleNos.FindAll(a => a.IsNotNullOrEmpty());
            if (!lroleNos.Any())
                return true;
            var data = _soa.Db.Queryable<SYS6_LIMIT_ROLE_DICT>().Where(w => lroleNos.Contains(w.LROLE_NO)).ToList();
            foreach (var d in data)
            {
                int index = lroleNos.IndexOf(d.LROLE_NO);

                if (index > -1)
                    d.LROLE_SORT = index.ToString().PadLeft(10, '0');
            }
            int rows = _soa.Db.Updateable(data).ExecuteCommand();
            return rows > 0;
        }

        /// <summary>
        /// 获取模块列表
        /// </summary>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        public List<Sys6SoftModuleInfoDto> GetSoftModuleInfo(string hospitalId, string? searchKey)
        {
            DateTime warnDate = DateTime.Now.AddDays(-30);
            var moduleFunc = _soa.Db.Queryable<SYS6_SOFT_MODULE_INFO>()
                .Where(a => a.MODULE_STATE == "1")
                .WhereIF(searchKey.IsNotNullOrEmpty(), a => a.MODULE_ID.Contains(searchKey) || a.MODULE_NAME.Contains(searchKey))
                .Select(a => new
                {
                    MODULE_ID = a.MODULE_ID,
                    MODULE_NO = a.MODULE_NO,
                    MODULE_CODE = a.MODULE_CODE,
                    MODULE_NAME = a.MODULE_NAME,
                    SOFT_KEY = a.SOFT_KEY,
                    MODULE_STATE = a.MODULE_STATE,
                    HAS_MENU = SqlFunc.Subqueryable<SYS6_MENU>().Where(me => me.MENU_STATE == "1" && me.MENU_ID.Contains(a.MODULE_ID)).Any(),
                    NEW_MENU_COUNT = SqlFunc.Subqueryable<SYS6_MENU>().Where(me => me.MENU_STATE == "1" && me.MENU_ID.Contains(a.MODULE_ID)
                      && me.FIRST_RTIME != null && SqlFunc.DateDiff(DateType.Day, me.FIRST_RTIME.Value, DateTime.Now) <= 30).Count(),
                })
                .OrderBy(a => a.MODULE_ID)
                .ToList()
                .Where(a => a.HAS_MENU)
                .ToList();

            List<Sys6SoftModuleInfoDto> result = moduleFunc.Select(a => new Sys6SoftModuleInfoDto
            {
                MODULE_ID = a.MODULE_ID,
                MODULE_NO = a.MODULE_NO,
                MODULE_CODE = a.MODULE_CODE,
                MODULE_NAME = a.MODULE_NAME,
                SOFT_KEY = a.SOFT_KEY,
                MODULE_STATE = a.MODULE_STATE,
                NEW_MENU_COUNT = a.NEW_MENU_COUNT,
            }).ToList();

            return result;
        }

        private List<MenuDto> BuildMenuTree(List<SYS6_MENU> moduleMenus, List<SYS6_LIMIT_ROLE_MENU> limitRoleMenus, ref List<string> checkedIdList)
        {
            var menuTree = new List<MenuDto>();

            if (checkedIdList == null)
                checkedIdList = new List<string>();

            List<SYS6_MENU> rootMenus = moduleMenus.Where(a => a.MENU_LEVEL == "0").DistinctBy(a => a.MENU_ID).ToList();

            List<SYS6_SOFT_MODULE_INFO> modules = _soa.Db.Queryable<SYS6_SOFT_MODULE_INFO>()
                .InnerJoin<SYS6_MENU>((mo, me) => me.MENU_ID.Contains(mo.MODULE_ID))
                .Where((mo, me) => rootMenus.Select(r => r.MENU_ID).ToList().Contains(me.MENU_ID))
                .ToList();

            modules = modules.DistinctBy(a => a.MODULE_ID).ToList();

            //支持多个模块
            foreach (SYS6_MENU menu in rootMenus)
            {
                //0级菜单
                var moduleMenu = new MenuDto
                {
                    MENU_ID = menu.MENU_ID,
                    MENU_NAME = menu.MENU_NAME,
                    MODULE_ID = modules.Find(a => menu.MENU_ID.Contains(a.MODULE_ID))?.MODULE_ID,
                    IS_CHECKED = limitRoleMenus.Any(a => a.MENU_NO == menu.MENU_ID) ? "1" : "0",
                };
                menuTree.Add(moduleMenu);
                if (moduleMenu.IS_CHECKED == "1")
                    checkedIdList.Add(moduleMenu.MENU_ID);
                FillChildrenMenu(moduleMenu, moduleMenus, limitRoleMenus, ref checkedIdList);
            }

            return menuTree;

            void FillChildrenMenu(MenuDto menu, List<SYS6_MENU> moduleMenus, List<SYS6_LIMIT_ROLE_MENU> limitRoleMenus, ref List<string> checkedIdList)
            {
                foreach (var mm in moduleMenus.Where(a => a.PARENT_CODE == menu.MENU_ID))
                {
                    var childMenu = new MenuDto();
                    childMenu.MENU_ID = mm.MENU_ID;
                    childMenu.MENU_NAME = mm.MENU_NAME;
                    childMenu.MODULE_ID = menu.MODULE_ID;
                    childMenu.IS_CHECKED = limitRoleMenus.Any(a => a.MENU_NO == mm.MENU_ID) ? "1" : "0";
                    childMenu.IS_NEW = childMenu.IS_CHECKED != "1" && DateTime.Now.AddDays(-30) < mm.FIRST_RTIME;//最新变化的，先简单处理，1个月内没选的算最新

                    menu.CHILDREN.Add(childMenu);
                    if (childMenu.IS_CHECKED == "1")
                        checkedIdList.Add(childMenu.MENU_ID);
                    FillChildrenMenu(childMenu, moduleMenus, limitRoleMenus, ref checkedIdList);
                }
            }
        }


        public ModuleLimitRoleMenuListResult GetModuleLimitRoleMenuList(string hospitalId, string limitRoleId, string moduleId)
        {
            var tree = new List<MenuDto>();
            var checkedIdList = new List<string>();

            List<SYS6_MENU> moduleMenus = _soa.Db.Queryable<SYS6_MENU>()
                .Where(a => a.MENU_STATE == "1" && a.MENU_ID.Contains(moduleId))
                .ToList();

            List<SYS6_LIMIT_ROLE_MENU> limitRoleMenus = _soa.Db.Queryable<SYS6_LIMIT_ROLE_MENU>()
                .Where(a => a.MODULE_ID == moduleId && a.LROLE_NO == limitRoleId)
                .ToList();

            tree = BuildMenuTree(moduleMenus, limitRoleMenus, ref checkedIdList);
            return new ModuleLimitRoleMenuListResult { tree = tree, checkedIdList = checkedIdList };
        }

        public List<MenuDto> GetLimitRoleMenuList(List<string> limitRoleIds)
        {
            var result = new List<MenuDto>();
            if (limitRoleIds == null || !limitRoleIds.Any())
                return result;

            List<SYS6_LIMIT_ROLE_MENU> limitRoleMenus = _soa.Db.Queryable<SYS6_LIMIT_ROLE_MENU>()
                .Where(a => limitRoleIds.Contains(a.LROLE_NO))
                .ToList();

            List<string> findMenuIds = limitRoleMenus.Select(a => a.MENU_NO).Distinct().ToList();
            List<SYS6_MENU> moduleMenus = new List<SYS6_MENU>();
            //往上溯源父节点，以构建树
            do
            {
                var findMenus = _soa.Db.Queryable<SYS6_MENU>()
                    .Where(a => a.MENU_STATE == "1" && findMenuIds.Contains(a.MENU_ID))
                    .ToList();

                moduleMenus.AddRange(findMenus);

                findMenuIds = moduleMenus.Select(a => a.PARENT_CODE)
                    .Distinct()
                    .Where(a => a != null && a != "root" && !moduleMenus.Any(m => m.MENU_ID == a))
                    .ToList();

            }
            while (findMenuIds.Any());
            var checkedIdList = new List<string>();
            result = BuildMenuTree(moduleMenus, limitRoleMenus, ref checkedIdList);
            return result;
        }

        public string DeleteLimitRoleMenu(string limitRoleId, string menuId)
        {
            ////以下是删除个菜单的逻辑
            //List<SYS6_LIMIT_ROLE_MENU> limitRoleMenus = _soa.Db.Queryable<SYS6_LIMIT_ROLE_MENU>()
            //      .Where(a => a.LROLE_NO == limitRoleId && a.MENU_NO == menuId)
            //      .ToList();

            //_soa.Db.Deleteable(limitRoleMenus).ExecuteCommand();

            //return "删除成功！";


            //以下是删除菜单节点下所有菜单的逻辑
            List<string> lowerMenuIds = new List<string> { menuId };
            List<SYS6_MENU> moduleMenus = new List<SYS6_MENU>();
            var findMenus = _soa.Db.Queryable<SYS6_MENU>()
                    .Where(a => a.MENU_STATE == "1" && a.MENU_ID == menuId)
                    .ToList();

            moduleMenus.AddRange(findMenus);

            //往下查找子菜单
            do
            {
                findMenus = _soa.Db.Queryable<SYS6_MENU>()
                    .Where(a => a.MENU_STATE == "1" && lowerMenuIds.Contains(a.PARENT_CODE))
                    .ToList();

                moduleMenus.AddRange(findMenus);

                lowerMenuIds = findMenus.Select(a => a.MENU_ID)
                    .Distinct()
                    .ToList();

            }
            while (lowerMenuIds.Any());

            var lowerMenuIdList = moduleMenus.Select(a => a.MENU_ID).ToList();

            List<SYS6_LIMIT_ROLE_MENU> limitRoleMenus = _soa.Db.Queryable<SYS6_LIMIT_ROLE_MENU>()
                  .Where(a => a.LROLE_NO == limitRoleId && lowerMenuIdList.Contains(a.MENU_NO))
                  .ToList();

            _soa.Db.Deleteable(limitRoleMenus).ExecuteCommand();

            return "删除成功！";
        }

        public string SaveModuleLimitRoleMenuList(string limitRoleId, string moduleId, List<string> checkedMenuIds, string hisName)
        {
            DateTime now = DateTime.Now;
            List<SYS6_LIMIT_ROLE_MENU> limitRoleMenus = _soa.Db.Queryable<SYS6_LIMIT_ROLE_MENU>()
                  .Where(a => a.LROLE_NO == limitRoleId && a.MODULE_ID == moduleId)
                  .ToList();

            //List<H07MenuDto> allSavingMenus = GetLineMenuFromTree(moduleMenus);//原参数为  List<H07MenuDto> moduleMenus ，改为List<string> checkedMenuIds
            List<SYS6_MENU> moduleMenus = _soa.Db.Queryable<SYS6_MENU>()
                  .Where(a => a.MENU_STATE == "1" && a.MENU_ID.Contains(moduleId))
                  .ToList();

            List<SYS6_LIMIT_ROLE_MENU> inserts = new List<SYS6_LIMIT_ROLE_MENU>();
            List<SYS6_LIMIT_ROLE_MENU> deletes = new List<SYS6_LIMIT_ROLE_MENU>();

            foreach (var menu in moduleMenus)
            {
                var limitRoles = limitRoleMenus.FindAll(a => a.MENU_NO == menu.MENU_ID && a.LROLE_NO == limitRoleId);
                bool idCheckedMenu = checkedMenuIds.Contains(menu.MENU_ID);
                if (idCheckedMenu && !limitRoles.Any())
                {
                    inserts.Add(new SYS6_LIMIT_ROLE_MENU
                    {
                        MODULE_ID = moduleId,
                        LROLE_NO = limitRoleId,
                        MENU_NO = menu.MENU_ID,
                        FIRST_RPERSON = hisName,
                        FIRST_RTIME = now,
                        LAST_MPERSON = hisName,
                        LAST_MTIME = now,
                    });
                }
                else if (!idCheckedMenu && limitRoles.Any())
                {
                    deletes.AddRange(limitRoles);
                }
            }

            //处理主键
            int IdMin = _baseDataServices.GetTableMaxNumber("SYS6_LIMIT_ROLE_MENU", "SERIAL_NO", inserts.Count, 1, "XH_SYS").data.ObjToInt() - inserts.Count;
            inserts.ForEach(a => a.SERIAL_NO = $"RM{(++IdMin).ToString().PadLeft(8, '0')}");

            if (inserts.Any())
                _soa.Db.Insertable(inserts).ExecuteCommand();
            if (deletes.Any())
                _soa.Db.Deleteable(deletes).ExecuteCommand();
            //生成规评变动事件
            CreateEPlanChangeEvent(limitRoleId, inserts, deletes, hisName);

            return "保存成功！";

            //原参数为  List<H07MenuDto> moduleMenus ，改为List<string> checkedMenuIds
            //List<H07MenuDto> GetLineMenuFromTree(List<H07MenuDto> treeMenus)
            //{
            //    var lineMenus = new List<H07MenuDto>();
            //    foreach (var menu in treeMenus)
            //    {
            //        lineMenus.Add(menu);
            //        lineMenus.AddRange(GetLineMenuFromTree(menu.CHILDREN));
            //    }
            //    return lineMenus;
            //}
        }
        //生成规评变动事件
        private void CreateEPlanChangeEvent(string limitRoleId, List<SYS6_LIMIT_ROLE_MENU> inserts, List<SYS6_LIMIT_ROLE_MENU> deletes, string hisName)
        {
            DateTime now = DateTime.Now;
            var newEvents = new List<OaEvaluatePlanEventParm>();
            string insertInfo = inserts.Any() ? ("添加MENU_ID:" + string.Join(',', inserts.Select(a => a.MENU_NO)) + ";") : "";
            string deleteInfo = deletes.Any() ? ("删除MENU_ID:" + string.Join(',', deletes.Select(a => a.MENU_NO)) + ";") : "";
            var DateChangeEvent = new OaEvaluatePlanEventParm
            {
                HOSPITAL_ID = _httpContext.GetHospitalId(),
                EVENT_TYPE = "6",
                CHANGE_TIME = now,
                CHANGE_PERSON = hisName,
                EVENT_INFO = $"限权组合{limitRoleId}{insertInfo}{deleteInfo}",
            };
            newEvents.Add(DateChangeEvent);
            CreateOaEvaluatePlanEvent(newEvents);
        }
        #endregion

        #region 资质证书

        public List<OaCertificateDict> GetCertificateDict(string hospitalId, string cerType, string state, string level, string ePlanFlag, string searchKey, List<string> CERTIFICATE_DID_LIST = null)
        {
            // 查询证书字典
            List<OaCertificateDict> result = _soa.Db.Queryable<OA_CERTIFICATE_DICT>()
                .LeftJoin<OA_BASE_DATA>((c, b) => c.CERTIFICATE_DTYPE == b.DATA_ID && b.CLASS_ID == "人事技能证书类型")
                .Where((c, b) => c.CERTIFICATE_DSTATE != "2") // 排除逻辑删除的证书
                .WhereIF(!string.IsNullOrEmpty(hospitalId), (c, b) => c.HOSPITAL_ID == hospitalId) // 根据医疗机构ID筛选
                .WhereIF(!string.IsNullOrEmpty(cerType), (c, b) => c.CERTIFICATE_DTYPE == cerType) // 根据
                .WhereIF(!string.IsNullOrEmpty(state), (c, b) => c.CERTIFICATE_DSTATE == state) // 根据证书状态筛选
                .WhereIF(!string.IsNullOrEmpty(level), (c, b) => c.CERTIFICATE_DLEVEL.Contains(level))
                .WhereIF(!string.IsNullOrEmpty(ePlanFlag), (c, b) => c.EPLAN_FLAG == ePlanFlag)
                .WhereIF(!string.IsNullOrEmpty(searchKey), (c, b) => b.DATA_NAME.Contains(searchKey) || c.CERTIFICATE_DNAME.Contains(searchKey)) // 根据搜索关键字模糊匹配
                .WhereIF(CERTIFICATE_DID_LIST != null, (c, b) => CERTIFICATE_DID_LIST.Contains(c.CERTIFICATE_DID)) // 根据搜索关键字模糊匹配
                .Select((c, b) => new OaCertificateDict
                {
                    CERTIFICATE_DID = c.CERTIFICATE_DID,
                    CERTIFICATE_DLEVEL = c.CERTIFICATE_DLEVEL,
                    CERTIFICATE_DSORT = c.CERTIFICATE_DSORT,
                    CERTIFICATE_DSTATE = c.CERTIFICATE_DSTATE,
                    CERTIFICATE_DNAME = c.CERTIFICATE_DNAME,
                    CERTIFICATE_DTYPE = c.CERTIFICATE_DTYPE,
                    EPLAN_FLAG = c.EPLAN_FLAG,
                    FIRST_RPERSON = "字段未加，暂时不返回",//c.FIRST_RPERSON,
                    FIRST_RTIME = c.FIRST_RTIME,
                    HOSPITAL_ID = c.HOSPITAL_ID,
                    LAST_MPERSON = c.LAST_MPERSON,
                    LAST_MTIME = c.LAST_MTIME,
                    REMARK = c.REMARK,
                    CERTIFICATE_DTYPE_NAME = b.DATA_NAME,
                    CERTIFICATE_DTYPE_SORT = b.DATA_SORT,
                    CER_LIFE_UNIT = c.CER_LIFE_UNIT,
                    CER_LIFE_VALUE = c.CER_LIFE_VALUE,
                })
                .ToList()
                .OrderBy(d => d.CERTIFICATE_DTYPE_SORT).ThenBy(d => d.CERTIFICATE_DTYPE).ThenBy(d => d.CERTIFICATE_DSORT)
                .ToList();

            return result;
        }

        /// <summary>
        /// 获取维护数据项证书类型
        /// </summary>
        /// <param name="hospitalId"></param>
        /// <param name="cerType"></param>
        /// <param name="state"></param>
        /// <param name="ePlanFlag"></param>
        /// <param name="searchKey"></param>
        /// <param name="tagId"></param>
        /// <returns></returns>
        public List<OaCertificateDict> GetCertificateDictAndTag(string hospitalId, string cerType, string state, string ePlanFlag, string searchKey, string tagId)
        {
            var result = new List<OaCertificateDict>();
            hospitalId = hospitalId ?? _httpContext.GetHospitalId();
            var cerList = GetCertificateDict(hospitalId, cerType, state, null, ePlanFlag, searchKey);
            var cerIds = cerList.Select(a => a.CERTIFICATE_DID).ToList();
            var relates = _soa.Db.Queryable<PMS_PERSON_TAG_RELATE>()
                            .InnerJoin<PMS_PERSON_TAG_DICT>((rel, tag) => rel.PERSON_TAG_ID == tag.PERSON_TAG_ID && tag.TAG_STATE == "1")
                            .Where((rel, tag) => rel.DATA_TYPE == "CERTIFICATE" && cerIds.Contains(rel.DATA_ID))
                            .Select((rel, tag) => new { rel.DATA_ID, tag.PERSON_TAG_ID, tag.TAG_NAME })
                            .ToList();

            foreach (var cer in cerList)
            {
                var thisRelates = relates.FindAll(a => a.DATA_ID == cer.CERTIFICATE_DID);
                var tagIds = thisRelates.Select(a => a.PERSON_TAG_ID).ToList();
                if (tagId.IsNullOrEmpty() || tagIds.Contains(tagId))
                {
                    cer.PERSON_TAG_IDS = tagIds;
                    cer.PERSON_TAG_NAMES = string.Join('、', thisRelates.Select(a => a.TAG_NAME));
                    result.Add(cer);
                }
            }
            return result;
        }


        public string SaveCertificateDict(OaCertificateDict dto, string hisName)
        {
            string certificateId = dto.CERTIFICATE_DID;
            OA_CERTIFICATE_DICT certEntity = null;
            bool isInsert = false;

            // 判断是否为新增
            if (string.IsNullOrEmpty(certificateId))
            {
                certEntity = new OA_CERTIFICATE_DICT
                {
                    CERTIFICATE_DID = "CE" + _baseDataServices.GetTableMaxNumber("OA_CERTIFICATE_DICT", "CERTIFICATE_DID", 1, 1, "XH_OA").data.ToString().PadLeft(8, '0'),
                    HOSPITAL_ID = dto.HOSPITAL_ID,
                    CERTIFICATE_DSORT = EntityHelper.GetSort(), //排序生成方法
                    CERTIFICATE_DTYPE = dto.CERTIFICATE_DTYPE,
                    CERTIFICATE_DLEVEL = dto.CERTIFICATE_DLEVEL,
                    CERTIFICATE_DSTATE = "1", // 默认在用状态
                    EPLAN_FLAG = dto.EPLAN_FLAG,
                    //FIRST_RPERSON = hisName,"字段未加，暂时不返回",
                    FIRST_RTIME = DateTime.Now,
                };
                isInsert = true;
            }
            else
            {
                if (_soa.Db.Queryable<OA_EVALUATE_PLAN_SETUP>().Any(a => a.EPLAN_ID == certificateId && a.EPLAN_SSTATE == EvaluatePlanSetupStateEnum.ENABLE.ToIntStr()))
                {
                    throw new BizException("存在已启用的规评设置，无法修改证书！");
                }
                // 查询现有记录
                certEntity = _soa.Db.Queryable<OA_CERTIFICATE_DICT>()
                                    .Where(a => a.CERTIFICATE_DID == certificateId)
                                    .First();
            }

            // 如果找到实体或是新增
            if (certEntity != null)
            {
                // 修改或覆盖属性
                certEntity.CERTIFICATE_DTYPE = dto.CERTIFICATE_DTYPE;
                certEntity.CERTIFICATE_DLEVEL = dto.CERTIFICATE_DLEVEL;
                certEntity.EPLAN_FLAG = dto.EPLAN_FLAG;
                certEntity.REMARK = dto.REMARK;
                certEntity.CERTIFICATE_DNAME = dto.CERTIFICATE_DNAME;
                certEntity.CER_LIFE_VALUE = dto.CER_LIFE_VALUE;
                certEntity.CER_LIFE_UNIT = dto.CER_LIFE_UNIT;
                certEntity.LAST_MPERSON = hisName;
                certEntity.LAST_MTIME = DateTime.Now;

                if (isInsert)
                {
                    // 执行新增操作
                    _soa.Db.Insertable(certEntity).ExecuteCommand();
                }
                else
                {
                    // 执行更新操作
                    _soa.Db.Updateable(certEntity)
                           .UpdateColumns(a => new
                           {
                               a.CERTIFICATE_DTYPE,
                               a.CERTIFICATE_DLEVEL,
                               a.EPLAN_FLAG,
                               a.REMARK,
                               a.LAST_MPERSON,
                               a.LAST_MTIME,
                               a.CERTIFICATE_DNAME,
                               a.CER_LIFE_VALUE,
                               a.CER_LIFE_UNIT,
                           })
                           .ExecuteCommand();
                }
            }
            else
            {
                throw new Exception("找不到该证书记录，保存失败！");
            }

            return certEntity.CERTIFICATE_DID;
        }

        /// <summary>
        /// 保存维护数据项证书类型
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="hisName"></param>
        /// <returns></returns>
        public string SaveCertificateDictAndTag(OaCertificateDict dto, string hisName)
        {
            var Id = SaveCertificateDict(dto, hisName);
            var relates = _soa.Db.Queryable<PMS_PERSON_TAG_RELATE>()
                  .Where(rel => rel.DATA_TYPE == "CERTIFICATE" && rel.DATA_ID == dto.CERTIFICATE_DID)
                  .ToList();

            Func<IEnumerable<string>, IEnumerable<string>, bool> isEqual = (list1, list2) => list1.All(a => list2.Any(b => a == b))
               && list1.Count() == list2.Count();

            //标签已经变化
            if (dto.PERSON_TAG_IDS != null && !isEqual(relates.Select(tag => tag.PERSON_TAG_ID), dto.PERSON_TAG_IDS))
            {
                var newRelates = new List<PMS_PERSON_TAG_RELATE>();
                foreach (var tagId in dto.PERSON_TAG_IDS)
                {
                    newRelates.Add(CreatePersonTagEntity(dto.HOSPITAL_ID, tagId, "CERTIFICATE", Id));
                }
                _soa.Db.Insertable(newRelates).ExecuteCommand();
                _soa.Db.Deleteable(relates).ExecuteCommand();
            }
            return Id;
        }
        private PMS_PERSON_TAG_RELATE CreatePersonTagEntity(string hospitalId, string tagId, string dataType, string dataId)
        {
            var now = DateTime.Now;
            return new PMS_PERSON_TAG_RELATE
            {
                TAG_DATA_RID = IDGenHelper.CreateGuid(),
                HOSPITAL_ID = hospitalId ?? _httpContext.GetHospitalId(),
                PERSON_TAG_ID = tagId,
                DATA_TYPE = dataType,
                DATA_ID = dataId,
                FIRST_RPERSON = _httpContext.GetUserHisName(),
                FIRST_RTIME = now,
                LAST_MPERSON = _httpContext.GetUserHisName(),
                LAST_MTIME = now,
            };
        }
        public string DeleteCertificateDict(string certificateDid, string hisName)
        {
            var setups = _soa.Db.Queryable<OA_EVALUATE_PLAN_SETUP>().Where(a => a.EPLAN_ID == certificateDid && a.EPLAN_SSTATE != EvaluatePlanSetupStateEnum.DELETE.ToIntStr()).ToList();
            if (setups.Any(a => a.EPLAN_SSTATE == EvaluatePlanSetupStateEnum.ENABLE.ToIntStr()))
            {
                throw new BizException("存在已启用的规评设置，无法删除证书！");
            }

            // 查询证书实体
            var certEntity = _soa.Db.Queryable<OA_CERTIFICATE_DICT>()
                                    .Where(cert => cert.CERTIFICATE_DID == certificateDid)
                                    .First();

            // 检查是否找到证书
            if (certEntity != null)
            {
                // 设置状态为删除
                certEntity.CERTIFICATE_DSTATE = "2"; // 状态 "2" 表示删除
                certEntity.LAST_MPERSON = hisName; // 最后修改人
                certEntity.LAST_MTIME = DateTime.Now; // 最后修改时间

                // 更新数据库
                _soa.Db.Updateable(certEntity)
                       .UpdateColumns(cert => new
                       {
                           cert.CERTIFICATE_DSTATE,
                           cert.LAST_MPERSON,
                           cert.LAST_MTIME
                       })
                       .ExecuteCommand();

                //删除规评设置
                if (setups.Any())
                {
                    foreach (var set in setups)
                    {
                        set.EPLAN_SSTATE = EvaluatePlanSetupStateEnum.DELETE.ToIntStr();
                        set.LAST_MPERSON = hisName;
                        set.LAST_MTIME = DateTime.Now;
                    }
                    _soa.Db.Updateable(setups).UpdateColumns(a => new { a.EPLAN_SSTATE, a.LAST_MPERSON, a.LAST_MTIME }).ExecuteCommand();
                }
            }
            else
            {
                throw new Exception("找不到该证书记录，删除失败！");
            }

            return "删除成功！";
        }


        public string EnableCertificateDict(string certificateDid, bool isDisable, string hisName)
        {
            if (isDisable && _soa.Db.Queryable<OA_EVALUATE_PLAN_SETUP>().Any(a => a.EPLAN_ID == certificateDid && a.EPLAN_SSTATE == EvaluatePlanSetupStateEnum.ENABLE.ToIntStr()))
            {
                throw new BizException("存在已启用的规评设置，无法禁用证书！");
            }

            // 查询证书实体
            var certEntity = _soa.Db.Queryable<OA_CERTIFICATE_DICT>()
                                    .Where(cert => cert.CERTIFICATE_DID == certificateDid)
                                    .First();

            // 检查是否找到证书
            if (certEntity != null)
            {
                // 设置状态为启用或禁用
                certEntity.CERTIFICATE_DSTATE = isDisable ? "0" : "1"; // "0" 表示禁用, "1" 表示启用
                certEntity.LAST_MPERSON = hisName; // 最后修改人
                certEntity.LAST_MTIME = DateTime.Now; // 最后修改时间

                // 更新数据库
                _soa.Db.Updateable(certEntity)
                       .UpdateColumns(cert => new
                       {
                           cert.CERTIFICATE_DSTATE,
                           cert.LAST_MPERSON,
                           cert.LAST_MTIME
                       })
                       .ExecuteCommand();
            }
            else
            {
                throw new Exception($"找不到该证书记录，{(isDisable ? "禁用" : "启用")}失败！");
            }

            return $"{(isDisable ? "禁用" : "启用")}成功！";
        }



        public bool SortCertificateDict(List<string> certificateIds)
        {
            // 过滤掉空或无效的 ID
            certificateIds = certificateIds.FindAll(id => !string.IsNullOrEmpty(id));
            if (!certificateIds.Any())
                return true;

            // 查询需要调整排序的证书记录
            var certificates = _soa.Db.Queryable<OA_CERTIFICATE_DICT>()
                                      .Where(cert => certificateIds.Contains(cert.CERTIFICATE_DID))
                                      .ToList();

            // 调整排序号
            foreach (var cert in certificates)
            {
                int index = certificateIds.IndexOf(cert.CERTIFICATE_DID);
                if (index > -1)
                {
                    cert.CERTIFICATE_DSORT = index.ToString().PadLeft(10, '0'); // 填充排序号
                }
            }

            // 更新数据库
            int rows = _soa.Db.Updateable(certificates).ExecuteCommand();

            // 返回是否成功
            return rows > 0;
        }


        #endregion

        #region 规评设置
        class UnitName
        {
            public string unit_type { get; set; }
            public string epland_sid { get; set; }
            public string lab_name { get; set; }
            public string mgroup_name { get; set; }
            public string pgroup_name { get; set; }
            public string post_type { get; set; }
            public string post_name { get; set; }
            public string post_role_name { get; set; }
        }

        public List<OaEvaluatePlanSetup> GetEvaluatePlanSetupList(
            string HOSPITAL_ID,
            string EPLAN_TYPE,// 规评方案类型 (1-学习, 2-培训, 3-评估, 4-考试, 5-资质证书)
            string PROLE_COM_ID, //限权组合ID
            string EPLAN_SSTATE,
            string LAB_PGROUP_ID,
            string SEARCH_KEY,
            string? LIMIT_PROLE_TYPE,//限权类型 (1-限权, 2-停岗)
            string? EPLAN_APPLY_TYPE,//规评方案适用类型（注意值传英文编码）LAB-检验科室 ；PGROUP-检验专业组；MGROUP-管理专业组；POST-岗位；PROLE-岗位角色
            string? UNIT_ID,//单位ID
            bool IS_INCLUDE_UNIT = true,
            bool IS_INCLUDE_EMPTY = false,//是否包含空白规评设置)
            List<string> EPLAN_ID_LIST = null,
            List<string> EPLAN_SID_LIST = null)
        {
            var result = new List<OaEvaluatePlanSetup>();
            List<string> eplanIds = new List<string>();
            List<OaEvaluatePlanDict> ePlans = null;
            List<OaCertificateDict> cets = null;

            bool IsCertificate = EPLAN_TYPE == "5";

            if (EPLAN_TYPE.IsNullOrEmpty() || !IsCertificate)//非资质证书
            {
                ePlans = GetEvaluatePlanDict(HOSPITAL_ID, EPLAN_TYPE, EPLAN_STATE: "1", LAB_PGROUP_ID, SEARCH_KEY, EPLAN_ID_LIST);
                eplanIds.AddRange(ePlans.Select(a => a.EPLAN_ID).ToList());
            }
            if (EPLAN_TYPE.IsNullOrEmpty() || IsCertificate)//资质证书
            {
                cets = GetCertificateDict(HOSPITAL_ID, null, state: "1", null, ePlanFlag: "1", SEARCH_KEY, CERTIFICATE_DID_LIST: EPLAN_ID_LIST);
                eplanIds.AddRange(cets.Select(a => a.CERTIFICATE_DID).ToList());
            }
            var setups = _soa.Db.Queryable<OA_EVALUATE_PLAN_SETUP>()
                .Where(a => eplanIds.Contains(a.EPLAN_ID))
                .WhereIF(EPLAN_SSTATE.IsNullOrEmpty(), a => a.EPLAN_SSTATE == null || a.EPLAN_SSTATE != EvaluatePlanSetupStateEnum.DELETE.ToIntStr())//默认显示除删除外的所有
                .WhereIF(EPLAN_SSTATE.IsNotNullOrEmpty(), a => a.EPLAN_SSTATE == EPLAN_SSTATE)
                .WhereIF(LIMIT_PROLE_TYPE.IsNotNullOrEmpty(), a => a.LIMIT_PROLE_TYPE.Contains(LIMIT_PROLE_TYPE))
                .WhereIF(EPLAN_APPLY_TYPE.IsNotNullOrEmpty(), a => a.EPLAN_APPLY_TYPE == EPLAN_APPLY_TYPE)
                .WhereIF(EPLAN_SID_LIST != null && EPLAN_SID_LIST.Any(), a => EPLAN_SID_LIST.Contains(a.EPLAN_SID))
                .Select(a => new OaEvaluatePlanSetup
                {
                    EPLAN_ID = a.EPLAN_ID.SelectAll(),
                })
                .ToList();

            //岗位适用设置的单位ID（科室ID、管理专业组ID、检验专业组ID、岗位ID、岗位角色ID）
            List<UnitName> unitNames = null;
            if (IS_INCLUDE_UNIT || UNIT_ID.IsNotNullOrEmpty())
            {
                var setupIds = setups.Select(a => a.EPLAN_SID).ToList();

                unitNames = _soa.Db.Queryable<OA_EVALUATE_PLAN_UNIT>()
                     .LeftJoin<SYS6_INSPECTION_PGROUP>((u, pg) => u.UNIT_ID == pg.PGROUP_ID)
                     .LeftJoin<SYS6_INSPECTION_MGROUP>((u, pg, mg) => u.UNIT_ID == mg.MGROUP_ID)
                     .LeftJoin<SYS6_INSPECTION_LAB>((u, pg, mg, l) => u.UNIT_ID == l.LAB_ID)
                     .Where((u, pg, mg, l) => setupIds.Contains(u.EPLAN_SID) && u.EPLAN_USTATE == "1" && (u.UNIT_TYPE == "LAB" || u.UNIT_TYPE == "MGROUP" || u.UNIT_TYPE == "PGROUP"))
                     .WhereIF(UNIT_ID.IsNotNullOrEmpty(), (u, pg, mg, l) => u.UNIT_ID == UNIT_ID)
                     .Select((u, pg, mg, l) => new UnitName
                     {
                         epland_sid = u.EPLAN_SID,
                         unit_type = u.UNIT_TYPE,
                         lab_name = l.LAB_NAME,
                         mgroup_name = mg.MGROUP_NAME,
                         pgroup_name = pg.PGROUP_NAME
                     })
                     .ToList();

                unitNames.AddRange
                    (_soa.Db.Queryable<OA_EVALUATE_PLAN_UNIT>()
                     .LeftJoin<SYS6_POST_ROLE>((u, pr) => u.UNIT_ID == pr.POSTROLE_ID)
                     .LeftJoin<SYS6_POST_ROLE_DICT>((u, pr, d) => pr.PROLE_DICT_ID == d.PROLE_DICT_ID)
                     .InnerJoin<SYS6_POST>((u, pr, d, po) => u.UNIT_ID == po.POST_ID || pr.POST_ID == po.POST_ID)
                     .LeftJoin<SYS6_INSPECTION_PGROUP>((u, pr, d, po, pg) => po.PGROUP_ID == pg.PGROUP_ID)
                     .LeftJoin<SYS6_INSPECTION_MGROUP>((u, pr, d, po, pg, mg) => po.PGROUP_ID == mg.MGROUP_ID)
                     .LeftJoin<SYS6_INSPECTION_LAB>((u, pr, d, po, pg, mg, l) => po.LAB_ID == l.LAB_ID && po.POST_ULEVEL == "L")
                     .LeftJoin<SYS6_BASE_DATA>((u, pr, d, po, pg, mg, l, bd) => bd.CLASS_ID == "岗位角色" && pr.PROLE_DICT_ID == bd.DATA_ID)
                     .Where((u, pr, d, po, pg, mg, l, bd) => setupIds.Contains(u.EPLAN_SID) && u.EPLAN_USTATE == "1" && (u.UNIT_TYPE == "POST" || u.UNIT_TYPE == "PROLE"))
                     .WhereIF(UNIT_ID.IsNotNullOrEmpty(), (u, pr, d, po, pg, mg, l, bd) => u.UNIT_ID == UNIT_ID)
                     .Select((u, pr, d, po, pg, mg, l, bd) => new UnitName
                     {
                         epland_sid = u.EPLAN_SID,
                         unit_type = u.UNIT_TYPE,
                         lab_name = l.LAB_NAME,
                         mgroup_name = mg.MGROUP_NAME,
                         pgroup_name = pg.PGROUP_NAME,
                         post_type = po.POST_ULEVEL,
                         post_name = po.POST_NAME,
                         post_role_name = d.POSTROLE_NAME ?? bd.DATA_CNAME
                     })
                     .ToList());

                var units = _soa.Db.Queryable<OA_EVALUATE_PLAN_UNIT>()
                      .Where(u => setupIds.Contains(u.EPLAN_SID) && u.EPLAN_USTATE == "1")
                      .WhereIF(UNIT_ID.IsNotNullOrEmpty(), u => u.UNIT_ID == UNIT_ID)
                      .ToList();


                //当适用范围入参不为空时，进行过滤
                if (UNIT_ID.IsNotNullOrEmpty())
                {
                    setups = setups.FindAll(a => units.Select(u => u.EPLAN_SID).Contains(a.EPLAN_SID)).ToList();
                }

                foreach (var set in setups)
                {
                    List<string> names = new List<string>();
                    set.UNITS = units.FindAll(a => a.EPLAN_SID == set.EPLAN_SID);
                    foreach (var unit in unitNames.Where(a => a.epland_sid == set.EPLAN_SID))
                    {
                        switch (unit.unit_type)
                        {
                            case "LAB":
                                names.Add(unit.lab_name);
                                break;
                            case "MGROUP":
                                names.Add(unit.mgroup_name);
                                break;
                            case "PGROUP":
                                names.Add(unit.pgroup_name);
                                break;
                            case "POST":
                                {
                                    if (unit.post_type == "L")
                                        names.Add($"{unit.lab_name}/{unit.post_name}");
                                    else if (unit.post_type == "M")
                                        names.Add($"{unit.mgroup_name}/{unit.post_name}");
                                    else if (unit.post_type == "P")
                                        names.Add($"{unit.pgroup_name}/{unit.post_name}");
                                    else
                                        names.Add($"{unit.pgroup_name}/{unit.post_name}");
                                }
                                break;
                            case "PROLE":
                                {
                                    if (unit.post_type == "L")
                                        names.Add($"{unit.lab_name}/{unit.post_name}/{unit.post_role_name}");
                                    else if (unit.post_type == "M")
                                        names.Add($"{unit.mgroup_name}/{unit.post_name}/{unit.post_role_name}");
                                    else if (unit.post_type == "P")
                                        names.Add($"{unit.pgroup_name}/{unit.post_name}/{unit.post_role_name}");
                                    else
                                        names.Add($"{unit.pgroup_name}/{unit.post_name}/{unit.post_role_name}");
                                }
                                break;
                        }
                    }
                    set.UNITS_NAME = string.Join("; ", names);
                }
            }

            var limitRoleComIds = setups.SelectMany(a => a.PROLE_COM_SID.IsNullOrEmpty() ? Array.Empty<string>() : a.PROLE_COM_SID.Split('+')).Distinct().ToList();
            var limitRoleComs = _soa.Db.Queryable<SYS6_LIMIT_ROLE_DICT>()
                .Where(a => limitRoleComIds.Contains(a.LROLE_NO))
                .OrderBy(a => a.LROLE_SORT)
                .ToList();

            //处理限权信息
            foreach (var set in setups)
            {
                List<SYS6_LIMIT_ROLE_DICT> limitRoles = limitRoleComs.FindAll(a => (set.PROLE_COM_SID?.Split('+') ?? Array.Empty<string>()).Contains(a.LROLE_NO));
                set.PROLE_COM_NAME = string.Join(",", limitRoles.Select(a => a.LROLE_NAME));
            }
            if (EPLAN_TYPE.IsNullOrEmpty() || !IsCertificate)//非资质证书
            {
                ePlans.ForEach(ePlan =>
                {
                    var setup = setups.Where(s => s.EPLAN_ID == ePlan.EPLAN_ID && s.EPLAN_SSTATE != EvaluatePlanSetupStateEnum.DELETE.ToIntStr()).OrderBy(s => s.FIRST_RTIME).FirstOrDefault();
                    if (setup == null && IS_INCLUDE_EMPTY && EPLAN_SSTATE.IsNullOrEmpty())
                    {
                        setup = new OaEvaluatePlanSetup
                        {
                            HOSPITAL_ID = ePlan.HOSPITAL_ID,
                            LAB_ID = ePlan.LAB_ID,
                            EPLAN_ID = ePlan.EPLAN_ID,
                        };
                    }
                    if (setup != null)
                    {
                        setup.PGROUP_ID = ePlan.PGROUP_ID;
                        setup.LAB_PGROUP_ID = ePlan.PGROUP_ID;
                        setup.LAB_PGROUP_NAME = ePlan.LAB_PGROUP_NAME;
                        setup.EPLAN_NAME = ePlan.EPLAN_NAME;
                        setup.EPLAN_TYPE = ePlan.EPLAN_TYPE;
                        setup.EPLAN_TYPE_NAME = ePlan.EPLAN_TYPE_NAME;

                        result.Add(setup);
                    }
                });
            }
            if (EPLAN_TYPE.IsNullOrEmpty() || IsCertificate)//资质证书
            {
                foreach (var cet in cets)
                {
                    var setup = setups.Where(s => s.EPLAN_ID == cet.CERTIFICATE_DID && s.EPLAN_SSTATE != EvaluatePlanSetupStateEnum.DELETE.ToIntStr()).OrderBy(s => s.FIRST_RTIME).FirstOrDefault();
                    if (setup == null && IS_INCLUDE_EMPTY && EPLAN_SSTATE.IsNullOrEmpty())
                    {
                        setup = new OaEvaluatePlanSetup
                        {
                            HOSPITAL_ID = cet.HOSPITAL_ID,
                            EPLAN_ID = cet.CERTIFICATE_DID,
                        };
                    }
                    if (setup != null)
                    {
                        setup.EPLAN_NAME = cet.CERTIFICATE_DNAME ?? cet.CERTIFICATE_DTYPE_NAME;
                        setup.EPLAN_TYPE = EPlanTypeEnum.CERTIFIDATE.ToIntStr();
                        setup.EPLAN_TYPE_NAME = EPlanTypeEnum.CERTIFIDATE.ToDesc();
                        setup.CERTIFICATE = cet;

                        result.Add(setup);
                    }
                }
            }
            result.ForEach(setup =>
            {

                if (setup != null)
                {
                    //效期单位默认月
                    if (setup.SHELF_LIFE_UTYPE.IsNullOrEmpty())
                        setup.SHELF_LIFE_UTYPE = ShelfLifeUnitTypeEnum.MONTH.ToIntStr();

                    //预警单位默认日
                    if (setup.WARN_UTYPE.IsNullOrEmpty())
                        setup.WARN_UTYPE = ShelfLifeUnitTypeEnum.DAY.ToIntStr();

                    var applyType = EnumUtils.FromKey<EPlanApplyTypeEnum>(setup.EPLAN_APPLY_TYPE);
                    setup.EPLAN_APPLY_TYPE_NAME = applyType.HasValue ? applyType.Value.ToDesc() : "";

                    var eplanType = EnumUtils.FromID<EPlanTypeEnum>(setup.EPLAN_TYPE);
                    setup.EPLAN_TYPE_NAME = eplanType.HasValue ? eplanType.Value.ToDesc() : "";

                    var state = EnumUtils.FromID<EvaluatePlanSetupStateEnum>(setup.EPLAN_SSTATE);
                    setup.EPLAN_SSTATE_NAME = state.HasValue ? state.Value.ToDesc() : "";

                    var limitRoleType = EnumUtils.FromID<LimitRoleTypeEnum>(setup.LIMIT_PROLE_TYPE);
                    setup.LIMIT_PROLE_TYPE_NAME = limitRoleType.HasValue ? limitRoleType.Value.ToDesc() : "";

                    var lifeUnitType = EnumUtils.FromID<ShelfLifeUnitTypeEnum>(setup.SHELF_LIFE_UTYPE);
                    setup.SHELF_LIFE_UTYPE_NAME = lifeUnitType.HasValue ? lifeUnitType.Value.ToDesc() : "";

                    var warnUnitType = EnumUtils.FromID<ShelfLifeUnitTypeEnum>(setup.WARN_UTYPE);
                    setup.WARN_UTYPE_NAME = warnUnitType.HasValue ? warnUnitType.Value.ToDesc() : "";
                }
            });
            result = result.Where(a => a != null)
            .WhereIF(PROLE_COM_ID.IsNotNullOrEmpty(), a => a.PROLE_COM_SID.IsNotNullOrEmpty() && a.PROLE_COM_SID.Split(',').Contains(PROLE_COM_ID))
            .WhereIF(LIMIT_PROLE_TYPE.IsNotNullOrEmpty(), a => a.LIMIT_PROLE_TYPE == LIMIT_PROLE_TYPE)
            .WhereIF(EPLAN_APPLY_TYPE.IsNotNullOrEmpty(), a => a.EPLAN_APPLY_TYPE == EPLAN_APPLY_TYPE)
            //.WhereIF(UNIT_ID.IsNotNullOrEmpty(), g => g.UNITS != null && g.UNITS.Any(b => b.UNIT_ID == UNIT_ID))
            //.WhereIF(!IS_INCLUDE_EMPTY, cet => cet.PROLE_COM_SID.IsNotNullOrEmpty() 
            //                                    && cet.LIMIT_PROLE_TYPE.IsNotNullOrEmpty() 
            //                                    && (cet.EPLAN_SHELF_LIFE ?? 0) != 0 
            //                                    && cet.SHELF_LIFE_UTYPE.IsNotNullOrEmpty())
            //.OrderBy(cet => cet.EPLAN_SSORT) //暂时按规评方案的顺序
            .ToList();
            return result;
        }


        public string SaveEvaluatePlanSetup(List<OaEvaluatePlanSetup> dtos, string operateType, string hisName)
        {
            var returnIds = new List<string>();
            var planSids = new List<string>();
            var deletePlanSids = new List<string>();
            foreach (OaEvaluatePlanSetup dto in dtos)
            {
                if (dto.EPLAN_ID.IsNullOrEmpty())
                    throw new BizException("规评方案不能为空！");

                string eplanSid = dto.EPLAN_SID;
                OA_EVALUATE_PLAN_SETUP eplanEty = null;
                bool isInsert = false;

                if (!string.IsNullOrEmpty(eplanSid))
                {
                    // 查询现有记录
                    eplanEty = _soa.Db.Queryable<OA_EVALUATE_PLAN_SETUP>()
                        .Where(a => a.EPLAN_SID == eplanSid && (a.EPLAN_SSTATE == null || a.EPLAN_SSTATE != EvaluatePlanSetupStateEnum.DELETE.ToIntStr()))
                        .First();
                }
                else
                {
                    eplanEty = _soa.Db.Queryable<OA_EVALUATE_PLAN_SETUP>()
                        .Where(a => a.EPLAN_ID == dto.EPLAN_ID && (a.EPLAN_SSTATE == null || a.EPLAN_SSTATE != EvaluatePlanSetupStateEnum.DELETE.ToIntStr()))
                        .First();
                }

                if (eplanEty == null)
                {
                    // 生成新的主键
                    eplanSid = "ES" + _baseDataServices.GetTableMaxNumber("OA_EVALUATE_PLAN_SETUP", "EPLAN_SID", 1, 1, "XH_OA").data.ToString().PadLeft(8, '0');

                    // 查询规评方案
                    var eplan = _soa.Db.Queryable<OA_EVALUATE_PLAN_DICT>()
                         .Where(a => a.EPLAN_ID == dto.EPLAN_ID && a.EPLAN_STATE != "2")
                         .First();
                    if (eplan != null)
                    {
                        eplanEty = new OA_EVALUATE_PLAN_SETUP
                        {
                            EPLAN_SID = eplanSid,
                            EPLAN_ID = dto.EPLAN_ID,
                            HOSPITAL_ID = eplan.HOSPITAL_ID,
                            LAB_ID = eplan.LAB_ID,
                            EPLAN_SSORT = EntityHelper.GetSort(),
                            EPLAN_SSTATE = null, //状态状态为空
                            //EPLAN_SSTATE = EvaluatePlanSetupStateEnum.UNABLE.ToIntStr(), //状态默认为未启用
                            FIRST_RPERSON = hisName,
                            FIRST_RTIME = DateTime.Now,
                            REMARK = dto.REMARK
                        };
                    }
                    else
                    {
                        // 查询资质证书
                        var cert = _soa.Db.Queryable<OA_CERTIFICATE_DICT>()
                            .Where(a => a.CERTIFICATE_DID == dto.EPLAN_ID && a.CERTIFICATE_DSTATE != "2")
                            .First();

                        if (cert != null)
                        {
                            eplanEty = new OA_EVALUATE_PLAN_SETUP
                            {
                                EPLAN_SID = eplanSid,
                                EPLAN_ID = dto.EPLAN_ID,
                                HOSPITAL_ID = cert.HOSPITAL_ID,
                                //LAB_ID = cert.LAB_ID,
                                EPLAN_SSORT = EntityHelper.GetSort(),
                                EPLAN_SSTATE = EvaluatePlanSetupStateEnum.UNABLE.ToIntStr(), //状态默认为未启用
                                FIRST_RPERSON = hisName,
                                FIRST_RTIME = DateTime.Now,
                                REMARK = dto.REMARK
                            };
                        }
                    }
                    isInsert = true;
                }

                if (eplanEty != null)
                {
                    eplanSid = eplanEty.EPLAN_SID;
                    // 更新属性
                    switch (operateType)
                    {
                        case "Edit"://修改改效期
                            //启用的设置不能修改
                            if (eplanEty.EPLAN_SSTATE == EvaluatePlanSetupStateEnum.ENABLE.ToIntStr())
                                break;
                            if (dto.EPLAN_SHELF_LIFE == null || dto.SHELF_LIFE_UTYPE.IsNullOrEmpty() || dto.LIMIT_PROLE_TYPE.IsNullOrEmpty()
                               || (dto.LIMIT_PROLE_TYPE == LimitRoleTypeEnum.LIMIT.ToIntStr() && dto.PROLE_COM_SID.IsNullOrEmpty())
                               || dto.UNITS == null || !dto.UNITS.Any())
                                throw new BizException("必填内容未填写完整，无法保存！");
                            eplanEty.EPLAN_SSTATE = EvaluatePlanSetupStateEnum.UNABLE.ToIntStr();//修改后默认状态为未启用
                            eplanEty.EPLAN_SHELF_LIFE = dto.EPLAN_SHELF_LIFE;
                            eplanEty.SHELF_LIFE_UTYPE = dto.SHELF_LIFE_UTYPE;
                            eplanEty.WARN_DURATION = dto.WARN_DURATION;
                            eplanEty.WARN_UTYPE = dto.WARN_UTYPE;
                            eplanEty.REMARK = dto.REMARK;
                            break;

                        case "Enable"://启用
                            //启用的设置不能修改
                            if (eplanEty.EPLAN_SSTATE != EvaluatePlanSetupStateEnum.UNABLE.ToIntStr())
                                throw new BizException("未启用状态的规评设置才能启用！");
                            if (dto.EPLAN_SHELF_LIFE == null || dto.SHELF_LIFE_UTYPE.IsNullOrEmpty() || dto.LIMIT_PROLE_TYPE.IsNullOrEmpty()
                               || (dto.LIMIT_PROLE_TYPE == LimitRoleTypeEnum.LIMIT.ToIntStr() && dto.PROLE_COM_SID.IsNullOrEmpty())
                               || dto.UNITS == null || !dto.UNITS.Any())
                                throw new BizException("必填内容未填写完整，无法启用！");
                            eplanEty.EPLAN_SSTATE = EvaluatePlanSetupStateEnum.ENABLE.ToIntStr();
                            break;

                        case "Disable"://禁用
                            eplanEty.EPLAN_SSTATE = EvaluatePlanSetupStateEnum.DISABLE.ToIntStr();
                            break;

                        case "Limit"://限权
                            //启用的设置不能修改
                            if (eplanEty.EPLAN_SSTATE == EvaluatePlanSetupStateEnum.ENABLE.ToIntStr())
                                throw new BizException("无法修改启用状态的规评设置！");
                            eplanEty.PROLE_COM_SID = dto.PROLE_COM_SID;
                            eplanEty.LIMIT_PROLE_TYPE = LimitRoleTypeEnum.LIMIT.ToIntStr();
                            eplanEty.EPLAN_SHELF_LIFE = dto.EPLAN_SHELF_LIFE;
                            eplanEty.SHELF_LIFE_UTYPE = dto.SHELF_LIFE_UTYPE;
                            eplanEty.WARN_DURATION = dto.WARN_DURATION;
                            eplanEty.WARN_UTYPE = dto.WARN_UTYPE;
                            eplanEty.REMARK = dto.REMARK;
                            break;

                        case "Stop"://停岗
                            //启用的设置不能修改
                            if (eplanEty.EPLAN_SSTATE == EvaluatePlanSetupStateEnum.ENABLE.ToIntStr())
                                throw new BizException("无法修改启用状态的规评设置！");
                            eplanEty.PROLE_COM_SID = null;
                            eplanEty.LIMIT_PROLE_TYPE = LimitRoleTypeEnum.STOP.ToIntStr();
                            eplanEty.EPLAN_SHELF_LIFE = dto.EPLAN_SHELF_LIFE;
                            eplanEty.SHELF_LIFE_UTYPE = dto.SHELF_LIFE_UTYPE;
                            eplanEty.WARN_DURATION = dto.WARN_DURATION;
                            eplanEty.WARN_UTYPE = dto.WARN_UTYPE;
                            eplanEty.REMARK = dto.REMARK;
                            break;

                        case "SaveUnits"://保存单元
                            //启用的设置不能修改
                            if (eplanEty.EPLAN_SSTATE == EvaluatePlanSetupStateEnum.ENABLE.ToIntStr())
                                throw new BizException("无法修改启用状态的规评设置！");
                            break;

                        case "Revoke"://撤销
                            //启用的设置不能修改
                            if (eplanEty.EPLAN_SSTATE == EvaluatePlanSetupStateEnum.ENABLE.ToIntStr())
                                throw new BizException("无法移除启用状态的规评设置！");
                            //eplanEty.PROLE_COM_SID = null;
                            //eplanEty.LIMIT_PROLE_TYPE = null;
                            eplanEty.EPLAN_SSTATE = EvaluatePlanSetupStateEnum.DELETE.ToIntStr();
                            deletePlanSids.Add(eplanEty.EPLAN_SID);
                            break;

                    }
                    eplanEty.LAST_MPERSON = hisName;
                    eplanEty.LAST_MTIME = DateTime.Now;
                    planSids.Add(eplanEty.EPLAN_SID);

                    if (isInsert)
                    {
                        // 插入新记录
                        _soa.Db.Insertable(eplanEty).ExecuteCommand();
                    }
                    else
                    {
                        // 更新现有记录
                        _soa.Db.Updateable(eplanEty)
                            .UpdateColumns(a => new
                            {
                                a.EPLAN_ID,
                                a.PROLE_COM_SID,
                                a.EPLAN_APPLY_TYPE,
                                a.EPLAN_SHELF_LIFE,
                                a.SHELF_LIFE_UTYPE,
                                a.WARN_DURATION,
                                a.WARN_UTYPE,
                                a.LIMIT_PROLE_TYPE,
                                a.EPLAN_SSTATE,
                                a.REMARK,
                                a.LAST_MPERSON,
                                a.LAST_MTIME
                            })
                            .ExecuteCommand();
                    }

                }
                else
                {
                    throw new Exception("找不到该规评方案设置，保存失败！");
                }
                returnIds.Add(eplanSid);
                //为了生成变动事件的数据有值
                {
                    dto.HOSPITAL_ID = eplanEty.HOSPITAL_ID;
                    dto.EPLAN_SID = eplanEty.EPLAN_SID;
                }
            }

            DeleteEvaluatePlanUnit(deletePlanSids);
            //HandleEvaluatePlanCurrentChanged(null, planSids);

            //插入规评方案变动事件
            if (operateType == "Enable" || operateType == "Disable" || operateType == "Revoke")
                CreateEPlanChangeEvent(dtos, hisName);
            return string.Join(',', returnIds);
        }

        private void DeleteEvaluatePlanUnit(List<string> eplanSids)
        {
            if (eplanSids == null || !eplanSids.Any())
                return;
            var deleteUnits = _soa.Db.Queryable<OA_EVALUATE_PLAN_UNIT>().Where(a => eplanSids.Contains(a.EPLAN_SID)).ToList();
            _soa.Db.Deleteable(deleteUnits).ExecuteCommand();
        }

        public string DeleteEvaluatePlanSetup(string eplanSid, string hisName)
        {
            // 查询规评方案设置
            var eplanEty = _soa.Db.Queryable<OA_EVALUATE_PLAN_SETUP>()
                .Where(a => a.EPLAN_SID == eplanSid)
                .First();

            if (eplanEty != null)
            {
                // 将状态设置为 "2" 表示删除
                eplanEty.EPLAN_SSTATE = "2";
                eplanEty.LAST_MPERSON = hisName;
                eplanEty.LAST_MTIME = DateTime.Now;

                // 更新数据库中的状态及修改信息
                _soa.Db.Updateable(eplanEty).UpdateColumns(a => new
                {
                    a.EPLAN_SSTATE,
                    a.LAST_MPERSON,
                    a.LAST_MTIME
                }).ExecuteCommand();
            }
            else
            {
                // 如果记录未找到，抛出异常
                throw new Exception("找不到该规评方案设置，删除失败！");
            }

            return "删除成功！";
        }

        private void CreateEPlanChangeEvent(List<OaEvaluatePlanSetup> dtos, string hisName)
        {
            DateTime now = DateTime.Now;
            var newEvents = new List<OaEvaluatePlanEventParm>();

            foreach (var eplanSet in dtos)
            {
                var DateChangeEvent = new OaEvaluatePlanEventParm
                {
                    HOSPITAL_ID = eplanSet.HOSPITAL_ID,
                    EVENT_TYPE = "1",
                    CHANGE_TIME = now,
                    CHANGE_PERSON = hisName,
                    EPLAN_ID = eplanSet.EPLAN_ID,
                    SOURCE_ID = eplanSet.EPLAN_SID,
                    EVENT_INFO = $"规评方案[{eplanSet.EPLAN_NAME}](EPLAN_SID:{eplanSet.EPLAN_SID})已被修改",
                };
                newEvents.Add(DateChangeEvent);
            }
            CreateOaEvaluatePlanEvent(newEvents);
        }

        public OrgTree GetEvaluatePlanSetupPostTreeByPostRole(string hospitalId, string? areaId, string? labId, string? pgroupId, string? keyWord)
        {
            return GetEvaluatePlanSetupPostRoleReverseTree(hospitalId, labId, areaId, null, pgroupId, keyWord);
        }

        public OrgTree GetEvaluatePlanSetupPostTreeByPgroup(string eplanApplyType, string hospitalId, string? areaId, string? labId, string? pgroupId, string? keyWord)
        {
            labId = labId ?? _httpContext.GetLabId();
            switch (eplanApplyType)
            {
                case "LAB":
                    return GetEvaluatePlanSetupLabTree(hospitalId, labId, areaId, null, pgroupId, keyWord);
                case "MGROUP":
                    return GetEvaluatePlanSetupMgroupTree(hospitalId, labId, areaId, null, pgroupId, keyWord);
                case "PGROUP":
                    return GetEvaluatePlanSetupPgroupTree(hospitalId, labId, areaId, null, pgroupId, keyWord);
                case "POST":
                    return GetEvaluatePlanSetupPostTree(hospitalId, labId, areaId, null, pgroupId, keyWord);
                case "PROLE":
                    return GetEvaluatePlanSetupPostRoleTree(hospitalId, labId, areaId, null, pgroupId, keyWord);
                default:
                    return new OrgTree();
            }
        }

        private OrgTree GetEvaluatePlanSetupLabTree(string hospitalId, string? labId, string? areaId, string? mgroupId, string? pgroupId, string? keyWord)
        {
            var tree = new OrgTree();
            List<string> labIds = null;
            if (areaId.IsNotNullOrEmpty() || mgroupId.IsNotNullOrEmpty() || pgroupId.IsNotNullOrEmpty())
            {
                labIds = _soa.Db.Queryable<SYS6_INSPECTION_PGROUP>()
                    .WhereIF(areaId.IsNotNullOrEmpty(), a => a.AREA_ID == areaId)
                    .WhereIF(mgroupId.IsNotNullOrEmpty(), a => a.MGROUP_ID == mgroupId)
                    .WhereIF(pgroupId.IsNotNullOrEmpty(), a => a.PGROUP_ID == pgroupId)
                    .Select(a => a.LAB_ID)
                    .Distinct()
                    .ToList();
            }

            var labs = _soa.Db.Queryable<SYS6_INSPECTION_LAB>()
                .Where(a => a.HOSPITAL_ID == hospitalId && a.STATE_FLAG == "1" && a.LAB_CLASS == "1")
                .WhereIF(labId.IsNotNullOrEmpty(), a => a.LAB_ID == labId)
                .WhereIF(keyWord.IsNotNullOrEmpty(), a => a.LAB_NAME.Contains(keyWord))
                .WhereIF(labIds != null && labIds.Any(), a => labIds.Contains(a.LAB_ID))
                .ToList();

            foreach (var lab in labs)
            {
                var labNode = new OrgTreeNode();
                labNode.SOURCE_ID = lab.LAB_ID;
                labNode.NAME = lab.LAB_NAME;
                labNode.SORT = lab.LAB_SORT;
                labNode.LAB_ID = lab.LAB_ID;
                labNode.NODE_TYPE = GroupTreeNodeTypeEnum.LAB.ToIntStr();
                labNode.NODE_TYPE_NAME = GroupTreeNodeTypeEnum.LAB.ToDesc();
                tree.ChildAdd(labNode);
            }
            tree.RefreshTree(a => true);
            return tree;
        }

        private OrgTree GetEvaluatePlanSetupMgroupTree(string hospitalId, string? labId, string? areaId, string? mgroupId, string? pgroupId, string? keyWord)
        {
            var tree = new OrgTree();
            List<string> mgroupIds = null;
            if (areaId.IsNotNullOrEmpty() || pgroupId.IsNotNullOrEmpty())
            {
                mgroupIds = _soa.Db.Queryable<SYS6_INSPECTION_PGROUP>()
                    .WhereIF(areaId.IsNotNullOrEmpty(), a => a.AREA_ID == areaId)
                    .WhereIF(pgroupId.IsNotNullOrEmpty(), a => a.PGROUP_ID == pgroupId)
                    .Select(a => a.MGROUP_ID)
                    .Distinct()
                    .ToList();
            }

            var mgroups = _soa.Db.Queryable<SYS6_INSPECTION_MGROUP>()
                .Where(a => a.HOSPITAL_ID == hospitalId && a.MGROUP_STATE == "1")
                .WhereIF(labId.IsNotNullOrEmpty(), a => a.LAB_ID == labId)
                .WhereIF(mgroupId.IsNotNullOrEmpty(), a => a.MGROUP_ID == mgroupId)
                .WhereIF(mgroupIds != null && mgroupIds.Any(), a => mgroupIds.Contains(a.MGROUP_ID))
                .WhereIF(keyWord.IsNotNullOrEmpty(), a => a.MGROUP_NAME.Contains(keyWord))
                .ToList();

            foreach (var mg in mgroups)
            {
                var mgroupNode = new OrgTreeNode();
                mgroupNode.SOURCE_ID = mg.MGROUP_ID;
                mgroupNode.NAME = mg.MGROUP_NAME;
                mgroupNode.SORT = mg.MGROUP_SORT;
                mgroupNode.LAB_ID = mg.LAB_ID;
                mgroupNode.NODE_TYPE = GroupTreeNodeTypeEnum.MGROUP.ToIntStr();
                mgroupNode.NODE_TYPE_NAME = GroupTreeNodeTypeEnum.MGROUP.ToDesc();
                tree.ChildAdd(mgroupNode);
            }
            tree.RefreshTree(a => true);
            return tree;
        }

        private OrgTree GetEvaluatePlanSetupPgroupTree(string hospitalId, string? labId, string? areaId, string? mgroupId, string? pgroupId, string? keyWord)
        {
            var tree = new OrgTree();

            var pgroups = _soa.Db.Queryable<SYS6_INSPECTION_PGROUP>()
                .Where(a => a.HOSPITAL_ID == hospitalId && a.PGROUP_STATE == "1")
                .WhereIF(labId.IsNotNullOrEmpty(), a => a.LAB_ID == labId)
                .WhereIF(areaId.IsNotNullOrEmpty(), a => a.AREA_ID == areaId)
                .WhereIF(mgroupId.IsNotNullOrEmpty(), a => a.MGROUP_ID == mgroupId)
                .WhereIF(pgroupId.IsNotNullOrEmpty(), a => a.PGROUP_ID == pgroupId)
                .WhereIF(keyWord.IsNotNullOrEmpty(), a => a.PGROUP_NAME.Contains(keyWord))
                .ToList();

            foreach (var pg in pgroups)
            {
                var pgroupNode = new OrgTreeNode();
                pgroupNode.SOURCE_ID = pg.PGROUP_ID;
                pgroupNode.NAME = pg.PGROUP_NAME;
                pgroupNode.SORT = pg.PGROUP_SORT;
                pgroupNode.LAB_ID = pg.LAB_ID;
                pgroupNode.AREA_ID = pg.AREA_ID;
                pgroupNode.NODE_TYPE = GroupTreeNodeTypeEnum.PGROUP.ToIntStr();
                pgroupNode.NODE_TYPE_NAME = GroupTreeNodeTypeEnum.PGROUP.ToDesc();
                tree.ChildAdd(pgroupNode);
            }
            tree.RefreshTree(a => true);
            return tree;
        }

        public string SaveEvaluatePlanSetupApplyUnit(string? eplanSid, string eplanId, string eplanApplyType, List<string> unitIds, string hisName)
        {
            var inserts = new List<OA_EVALUATE_PLAN_UNIT>();
            var updates = new List<OA_EVALUATE_PLAN_UNIT>();

            if (_soa.Db.Queryable<OA_EVALUATE_PLAN_SETUP>().Any(a => a.EPLAN_ID == eplanId && a.EPLAN_SSTATE == EvaluatePlanSetupStateEnum.ENABLE.ToIntStr()))
            {
                throw new BizException("存在已启用的规评设置，无法修改！");
            }
            //保存规评设置
            if (eplanSid.IsNullOrEmpty())
            {
                var newSet = new OaEvaluatePlanSetup();
                newSet.EPLAN_ID = eplanId;
                eplanSid = SaveEvaluatePlanSetup(new List<OaEvaluatePlanSetup> { newSet }, "SaveUnits", hisName);
            }

            var oldUnits = _soa.Db.Queryable<OA_EVALUATE_PLAN_UNIT>()
                .Where(a => a.EPLAN_SID == eplanSid)
                .ToList();

            var oldHandled = new List<OA_EVALUATE_PLAN_UNIT>();
            foreach (var id in unitIds)
            {
                OA_EVALUATE_PLAN_UNIT item = oldUnits.Find(old => old.UNIT_ID == id && old.UNIT_TYPE == eplanApplyType);
                if (item != null)
                {
                    if (item.EPLAN_USTATE != "1")
                    {
                        item.EPLAN_USTATE = "1";
                        updates.Add(item);
                    }
                    oldHandled.Add(item);
                }
                else
                {
                    item = new OA_EVALUATE_PLAN_UNIT
                    {
                        EPLAN_SID = eplanSid,
                        UNIT_TYPE = eplanApplyType,
                        EPLAN_USTATE = "1",
                        FIRST_RPERSON = hisName,
                        FIRST_RTIME = DateTime.Now,
                        LAST_MPERSON = hisName,
                        LAST_MTIME = DateTime.Now,
                        UNIT_ID = id,
                    };
                    inserts.Add(item);
                }
            }
            foreach (var old in oldUnits.Where(a => !oldHandled.Contains(a)))
            {
                old.EPLAN_USTATE = "2"; //删除
                old.LAST_MPERSON = hisName;
                old.LAST_MTIME = DateTime.Now;
                updates.Add(old);
            }
            if (inserts.Any())
            {
                _soa.Db.Insertable(inserts).ExecuteCommand();
            }
            if (updates.Any())
            {
                //_soa.Db.Updateable(updates).UpdateColumns(g => new { g.EPLAN_USTATE, g.LAST_MTIME, g.LAST_MPERSON }).ExecuteCommand();//联合主键会有问题
                _soa.Db.Updateable(updates).UpdateColumns(a => new { a.EPLAN_USTATE, a.LAST_MTIME, a.LAST_MPERSON }).ExecuteCommand();
            }

            // 查询现有记录
            var eplanEty = _soa.Db.Queryable<OA_EVALUATE_PLAN_SETUP>()
                .Where(a => a.EPLAN_SID == eplanSid)
                .First();
            // 更新适用类型
            if (eplanEty.EPLAN_APPLY_TYPE != eplanApplyType)
            {
                eplanEty.EPLAN_APPLY_TYPE = eplanApplyType;
                eplanEty.LAST_MPERSON = hisName;
                eplanEty.LAST_MTIME = DateTime.Now;
                _soa.Db.Updateable(eplanEty).UpdateColumns(a => new { a.EPLAN_APPLY_TYPE, a.LAST_MTIME, a.LAST_MPERSON }).ExecuteCommand();
            }

            //插入规评方案变动事件
            var eplanSets = new List<OaEvaluatePlanSetup> { new OaEvaluatePlanSetup { HOSPITAL_ID = eplanEty.HOSPITAL_ID, EPLAN_ID = eplanEty.EPLAN_ID, EPLAN_SID = eplanEty.EPLAN_SID } };
            CreateEPlanChangeEvent(eplanSets, hisName);
            return "保存成功！";
        }

        public string AddEvaluatePlanSetupApplyUnit(List<string> eplanIds, string eplanApplyType, List<string> unitIds, string hisName)
        {
            foreach (var eplanId in eplanIds)
            {
                SaveEvaluatePlanSetupApplyUnit(eplanSid: null, eplanId, eplanApplyType, unitIds, hisName);
            }
            return "保存成功！";
        }

        #endregion


        #region 目录树相关  
        public OrgTree GetEvaluatePlanSetupPostOrginalTree(string hospitalId, string? labId, string? areaId, string? mgroupId, string? pgroupId, string? keyWord, bool ifReturnSourceObject = false)
        {
            var tree = new OrgTree();
            if (hospitalId.IsNullOrEmpty())
                throw new BizException("参数机构ID必填！");

            var hospital = _soa.Db.Queryable<SYS6_HOSPITAL_INFO>()
                .Where(a => a.HOSPITAL_ID == hospitalId && a.HOSPITAL_STATE == "1")
                .First();

            var pgroups = _soa.Db.Queryable<SYS6_INSPECTION_PGROUP>()
                .Where(a => a.HOSPITAL_ID == hospitalId && a.PGROUP_STATE == "1")
                .Where(a => a.PGROUP_CLASS != "3" && a.PGROUP_CLASS != "4" && a.MGROUP_ID != null)
                .WhereIF(labId.IsNotNullOrEmpty(), a => a.LAB_ID == labId)
                .WhereIF(areaId.IsNotNullOrEmpty(), a => a.AREA_ID == areaId)
                .WhereIF(mgroupId.IsNotNullOrEmpty(), a => a.MGROUP_ID == mgroupId)
                .OrderBy(a => a.PGROUP_SORT)
                .ToList();

            List<string> pgroupIds = pgroups.Select(a => a.PGROUP_ID)
                .Distinct()
                .ToList();

            List<string?> mgroupIds = pgroups.Select(a => a.MGROUP_ID)
                .Distinct()
                .ToList();

            var labIds = pgroups.Select(a => a.LAB_ID)
                .Distinct()
                .ToList();

            var labs = _soa.Db.Queryable<SYS6_INSPECTION_LAB>()
                .Where(a => a.HOSPITAL_ID == hospitalId && a.STATE_FLAG == "1")
                .WhereIF(labId.IsNotNullOrEmpty(), a => a.LAB_ID == labId)
                .OrderBy(a => a.LAB_SORT)
                .ToList();

            var mgroups = _soa.Db.Queryable<SYS6_INSPECTION_MGROUP>()
                .Where(a => a.HOSPITAL_ID == hospitalId && a.MGROUP_STATE == "1")
                .WhereIF(labId.IsNotNullOrEmpty(), a => a.LAB_ID == labId)
                .WhereIF(mgroupId.IsNotNullOrEmpty(), a => a.MGROUP_ID == mgroupId)
                .WhereIF(mgroupIds != null && mgroupIds.Any(), a => mgroupIds.Contains(a.MGROUP_ID))
                .OrderBy(a => a.MGROUP_SORT)
                .ToList();

            var posts = _soa.Db.Queryable<SYS6_POST>()
                .Where(a => a.HOSPITAL_ID == hospitalId && a.POST_STATE == "1" && a.IF_ASSESS == "1")
                .Where(a =>
                    ((a.POST_ULEVEL == null || a.POST_ULEVEL == "P") && pgroupIds.Contains(a.PGROUP_ID))
                    ||
                    (a.POST_ULEVEL == "M" && mgroupIds.Contains(a.PGROUP_ID))
                    ||
                    (a.POST_ULEVEL == "L" && labIds.Contains(a.LAB_ID)))
                .WhereIF(keyWord.IsNotNullOrEmpty(), a => a.POST_NAME.Contains(keyWord))
                .OrderBy(a => a.POST_SORT)
                .ToList();

            var hospitalNode = new OrgTreeNode
            {
                NAME = hospital.HOSPITAL_CNAME,
                NODE_TYPE = GroupTreeNodeTypeEnum.HOSPITAL.ToIntStr(),
                NODE_TYPE_NAME = GroupTreeNodeTypeEnum.HOSPITAL.ToDesc(),
                SOURCE_ID = hospital.HOSPITAL_ID,
                SOURCE = ifReturnSourceObject ? hospital : null,
            };

            foreach (var lab in labs)
            {
                var labNode = new OrgTreeNode
                {
                    NAME = lab.LAB_NAME,
                    NODE_TYPE = GroupTreeNodeTypeEnum.LAB.ToIntStr(),
                    NODE_TYPE_NAME = GroupTreeNodeTypeEnum.LAB.ToDesc(),
                    SOURCE_ID = lab.LAB_ID,
                    SOURCE = ifReturnSourceObject ? lab : null,
                    LAB_ID = lab.LAB_ID,
                };
                tree.ChildAdd(labNode);

                foreach (var post in posts.Where(a => a.POST_ULEVEL == "L" && a.LAB_ID == lab.LAB_ID))
                {
                    var postNode = new OrgTreeNode
                    {
                        NAME = post.POST_NAME,
                        NODE_TYPE = GroupTreeNodeTypeEnum.POST.ToIntStr(),
                        NODE_TYPE_NAME = GroupTreeNodeTypeEnum.POST.ToDesc(),
                        SOURCE_ID = post.POST_ID,
                        SOURCE = ifReturnSourceObject ? post : null,
                        LAB_ID = lab.LAB_ID,
                    };
                    labNode.ChildAdd(postNode);
                }

                foreach (var pg in pgroups.Where(a => a.LAB_ID == lab.LAB_ID && a.MGROUP_ID.IsNullOrEmpty()))
                {
                    var pgNode = new OrgTreeNode
                    {
                        NAME = pg.PGROUP_NAME,
                        NODE_TYPE = GroupTreeNodeTypeEnum.PGROUP.ToIntStr(),
                        NODE_TYPE_NAME = GroupTreeNodeTypeEnum.PGROUP.ToDesc(),
                        SOURCE_ID = pg.PGROUP_ID,
                        SOURCE = ifReturnSourceObject ? pg : null,
                        LAB_ID = lab.LAB_ID,
                    };
                    tree.ChildAdd(pgNode);

                    foreach (var post in posts.Where(a => (a.POST_ULEVEL == null || a.POST_ULEVEL == "P") && a.PGROUP_ID == pg.PGROUP_ID))
                    {
                        var postNode = new OrgTreeNode
                        {
                            NAME = post.POST_NAME,
                            NODE_TYPE = GroupTreeNodeTypeEnum.POST.ToIntStr(),
                            NODE_TYPE_NAME = GroupTreeNodeTypeEnum.POST.ToDesc(),
                            SOURCE_ID = post.POST_ID,
                            SOURCE = ifReturnSourceObject ? post : null,
                            LAB_ID = lab.LAB_ID,
                        };
                        pgNode.ChildAdd(postNode);
                    }
                }

                foreach (var mg in mgroups.Where(a => a.LAB_ID == lab.LAB_ID))
                {
                    var mgNode = new OrgTreeNode
                    {
                        NAME = mg.MGROUP_NAME,
                        NODE_TYPE = GroupTreeNodeTypeEnum.MGROUP.ToIntStr(),
                        NODE_TYPE_NAME = GroupTreeNodeTypeEnum.MGROUP.ToDesc(),
                        SOURCE_ID = mg.MGROUP_ID,
                        SOURCE = ifReturnSourceObject ? mg : null,
                        LAB_ID = lab.LAB_ID,
                    };
                    tree.ChildAdd(mgNode);

                    foreach (var post in posts.Where(a => a.POST_ULEVEL == "M" && a.PGROUP_ID == mg.MGROUP_ID))
                    {
                        var postNode = new OrgTreeNode
                        {
                            NAME = post.POST_NAME,
                            NODE_TYPE = GroupTreeNodeTypeEnum.POST.ToIntStr(),
                            NODE_TYPE_NAME = GroupTreeNodeTypeEnum.POST.ToDesc(),
                            SOURCE_ID = post.POST_ID,
                            SOURCE = ifReturnSourceObject ? post : null,
                            LAB_ID = lab.LAB_ID,
                        };
                        mgNode.ChildAdd(postNode);
                    }

                    foreach (var pg in pgroups.Where(a => a.MGROUP_ID == mg.MGROUP_ID))
                    {
                        var pgNode = new OrgTreeNode
                        {
                            NAME = pg.PGROUP_NAME,
                            NODE_TYPE = GroupTreeNodeTypeEnum.PGROUP.ToIntStr(),
                            NODE_TYPE_NAME = GroupTreeNodeTypeEnum.PGROUP.ToDesc(),
                            SOURCE_ID = pg.PGROUP_ID,
                            SOURCE = ifReturnSourceObject ? pg : null,
                            LAB_ID = lab.LAB_ID,
                        };
                        tree.ChildAdd(pgNode);

                        foreach (var post in posts.Where(a => (a.POST_ULEVEL == null || a.POST_ULEVEL == "P") && a.PGROUP_ID == pg.PGROUP_ID))
                        {
                            var postNode = new OrgTreeNode
                            {
                                NAME = post.POST_NAME,
                                NODE_TYPE = GroupTreeNodeTypeEnum.POST.ToIntStr(),
                                NODE_TYPE_NAME = GroupTreeNodeTypeEnum.POST.ToDesc(),
                                SOURCE_ID = post.POST_ID,
                                SOURCE = ifReturnSourceObject ? post : null,
                                LAB_ID = lab.LAB_ID,
                            };
                            pgNode.ChildAdd(postNode);
                        }
                    }
                }

            }
            return tree;
        }
        public OrgTree GetEvaluatePlanSetupPostTree(string hospitalId, string? labId, string? areaId, string? mgroupId, string? pgroupId, string? keyWord, bool ifReturnSourceObject = false)
        {
            var tree = GetEvaluatePlanSetupPostOrginalTree(hospitalId, labId, areaId, mgroupId, pgroupId, keyWord, ifReturnSourceObject);
            tree.RefreshTree(a => a.NODE_TYPE == GroupTreeNodeTypeEnum.POST.ToIntStr());
            tree.AllNodesRemove(a => a.NUM == 0);//清除无岗位节点
            return tree;
        }

        //获取岗位角色树
        //public OrgTree GetEvaluatePlanSetupPostRoleTree_old(string hospitalId, string? labId, string? areaId, string? mgroupId, string? pgroupId, string? keyWord, bool ifReturnSourceObject = false)
        //{
        //    var parm = new OrgUserParams { hospital_id = hospitalId, lab_id = labId, area_id = areaId, mgroup_id = mgroupId, pgroup_id = pgroupId, user_id = _httpContext.GetUserId() };
        //    var tree = _organizationTreeService2.GetOrgTreeType_Lab_A(_soa, parm, "H81", false);
        //    var treeAllNodes = tree.GetAllNodes();
        //    List<string> labIds = treeAllNodes.Where(a => a.NODE_TYPE == GroupTreeNodeTypeEnum.LAB.ToIntStr()).Select(a => a.SOURCE_ID).ToList();
        //    var posts = _soa.Db.Queryable<SYS6_POST>().Where(a => labIds.Contains(a.LAB_ID) && a.POST_STATE == "1").ToList();
        //    List<string> postIds = posts.Select(a => a.POST_ID).ToList();

        //    var roles = _soa.Db.Queryable<SYS6_POST_ROLE>()
        //         .InnerJoin<SYS6_POST_ROLE_DICT>((pr, rd) => pr.PROLE_DICT_ID == rd.PROLE_DICT_ID && (rd.POSTROLE_STATE == "-1" || rd.POSTROLE_STATE == "1")) //-1是虚拟的岗位角色（当岗位创建没有岗位角色时候，会插一条虚拟的岗位角色和对照关系）
        //         .Where((pr, rd) => postIds.Contains(pr.POST_ID))
        //         .Select((pr, rd) => new { pr.POSTROLE_ID, pr.POST_ID, rd.PROLE_DICT_ID, rd.POSTROLE_NAME })
        //         .ToList();

        //    Func<SYS6_POST, OrgTreeNode> GetPostNode = p =>
        //       new OrgTreeNode
        //       {
        //           NAME = p.POST_NAME,
        //           NODE_TYPE = GroupTreeNodeTypeEnum.POST.ToIntStr(),
        //           NODE_TYPE_NAME = GroupTreeNodeTypeEnum.POST.ToDesc(),
        //           SOURCE_ID = p.POST_ID,
        //           SOURCE = ifReturnSourceObject ? p : null,
        //           LAB_ID = p.LAB_ID,
        //       };

        //    foreach (var post in posts)
        //    {
        //        OrgTreeNode parentNode = null;
        //        switch (post.POST_ULEVEL)
        //        {
        //            case "L":
        //                parentNode = treeAllNodes.Where(a => a.NODE_TYPE == GroupTreeNodeTypeEnum.LAB.ToIntStr() && a.SOURCE_ID == post.LAB_ID).FirstOrDefault();
        //                break;
        //            case "M":
        //                parentNode = treeAllNodes.Where(a => a.NODE_TYPE == GroupTreeNodeTypeEnum.MGROUP.ToIntStr() && a.SOURCE_ID == post.PGROUP_ID).FirstOrDefault();
        //                break;
        //            case "P":
        //                parentNode = treeAllNodes.Where(a => a.NODE_TYPE == GroupTreeNodeTypeEnum.PGROUP.ToIntStr() && a.SOURCE_ID == post.PGROUP_ID).FirstOrDefault();
        //                break;

        //            default:
        //                break;
        //        }
        //        if (parentNode != null)
        //        {
        //            OrgTreeNode postNode = GetPostNode(post);
        //            parentNode.ChildAdd(postNode);

        //            foreach (var role in roles)
        //            {
        //                if (role.POST_ID == post.POST_ID)
        //                {
        //                    var PRoleNode = new OrgTreeNode
        //                    {
        //                        NAME = role.POSTROLE_NAME,
        //                        NODE_TYPE = GroupTreeNodeTypeEnum.PROLE.ToIntStr(),
        //                        NODE_TYPE_NAME = GroupTreeNodeTypeEnum.PROLE.ToDesc(),
        //                        SOURCE_ID = role.POSTROLE_ID,
        //                        SOURCE = ifReturnSourceObject ? role : null,
        //                        LAB_ID = post.LAB_ID,
        //                    };
        //                    postNode.ChildAdd(PRoleNode);
        //                }
        //            }
        //        }
        //    }
        //    tree.RefreshTree(GroupTreeNodeTypeEnum.PROLE.ToIntStr());
        //    tree.AllNodesRemove(a => a.NUM == 0);//清除无角色节点
        //    return tree;
        //}

        //获取岗位角色树
        public OrgTree GetEvaluatePlanSetupPostRoleTree(string hospitalId, string? labId, string? areaId, string? mgroupId, string? pgroupId, string? keyWord, bool ifReturnSourceObject = false)
        {
            var tree = GetEvaluatePlanSetupPostOrginalTree(hospitalId, labId, areaId, mgroupId, pgroupId, keyWord, ifReturnSourceObject);
            var postNodes = tree.GetAllNodes(a => a.NODE_TYPE == GroupTreeNodeTypeEnum.POST.ToIntStr());
            var postIds = postNodes.Select(a => a.SOURCE_ID).ToList();
            var roles = _soa.Db.Queryable<SYS6_POST_ROLE>()
                 .LeftJoin<SYS6_POST_ROLE_DICT>((pr, rd) => pr.PROLE_DICT_ID == rd.PROLE_DICT_ID && (rd.POSTROLE_STATE == "-1" || rd.POSTROLE_STATE == "1")) //-1是虚拟的岗位角色（当岗位创建没有岗位角色时候，会插一条虚拟的岗位角色和对照关系）
                 .LeftJoin<SYS6_BASE_DATA>((pr, rd, bd) => bd.CLASS_ID == "岗位角色" && bd.DATA_ID == pr.PROLE_DICT_ID)
                 .Where((pr, rd, bd) => postIds.Contains(pr.POST_ID))
                 .WhereIF(keyWord.IsNotNullOrEmpty(), (pr, rd, bd) => rd.POSTROLE_NAME.Contains(keyWord))
                 .Select((pr, rd, bd) => new { pr.POSTROLE_ID, pr.POST_ID, pr.PROLE_DICT_ID, POSTROLE_NAME = rd.POSTROLE_NAME ?? bd.DATA_CNAME, POSTROLE_SORT = rd.POSTROLE_SORT ?? bd.DATA_ID })
                 .ToList()
                 .OrderBy(a => a.POSTROLE_SORT)
                 .ToList();

            foreach (var role in roles)
            {
                var postNode = postNodes.Where(a => a.SOURCE_ID == role.POST_ID).First();
                if (postNode != null)
                {
                    var PRoleNode = new OrgTreeNode
                    {
                        NAME = role.POSTROLE_NAME,
                        NODE_TYPE = GroupTreeNodeTypeEnum.PROLE.ToIntStr(),
                        NODE_TYPE_NAME = GroupTreeNodeTypeEnum.PROLE.ToDesc(),
                        SOURCE_ID = role.POSTROLE_ID,
                        SOURCE = ifReturnSourceObject ? role : null,
                        LAB_ID = postNode.LAB_ID,
                    };
                    postNode.ChildAdd(PRoleNode);
                }
            }
            tree.RefreshTree(GroupTreeNodeTypeEnum.PROLE.ToIntStr());
            tree.AllNodesRemove(a => a.NUM == 0);//清除无角色节点
            return tree;
        }

        public OrgTree GetEvaluatePlanSetupPostRoleReverseTree(string hospitalId, string? labId, string? areaId, string? mgroupId, string? pgroupId, string? keyWord)
        {
            var tree = new OrgTree();
            labId = labId ?? _httpContext.GetLabId();
            List<string> postIds = _soa.Db.Queryable<SYS6_POST>()
                .Where(a => a.HOSPITAL_ID == hospitalId && a.POST_STATE == "1")
                .WhereIF(labId.IsNotNullOrEmpty(), a => a.LAB_ID == labId)
                .Select(a => a.POST_ID)
                .ToList();

            var postRoleData = _soa.Db.Queryable<SYS6_POST_ROLE>()
                 .LeftJoin<SYS6_POST_ROLE_DICT>((pr, rd) => pr.PROLE_DICT_ID == rd.PROLE_DICT_ID) //-1是虚拟的岗位角色（当岗位创建没有岗位角色时候，会插一条虚拟的岗位角色和对照关系）
                 .LeftJoin<SYS6_BASE_DATA>((pr, rd, bd) => bd.CLASS_ID == "岗位角色" && bd.DATA_ID == pr.PROLE_DICT_ID)
                 .InnerJoin<SYS6_POST>((pr, rd, bd, po) => pr.POST_ID == po.POST_ID && postIds.Contains(po.POST_ID) && po.POST_STATE != "0" && po.POST_STATE != "2" && po.IF_ASSESS == "1")
                 .LeftJoin<SYS6_INSPECTION_LAB>((pr, rd, bd, po, l) => /*po.POST_ULEVEL == "L" &&*/ l.LAB_ID == po.LAB_ID)
                 .LeftJoin<SYS6_INSPECTION_MGROUP>((pr, rd, bd, po, l, m) => /*po.POST_ULEVEL == "M" && */ m.MGROUP_ID == po.PGROUP_ID)
                 .LeftJoin<SYS6_INSPECTION_PGROUP>((pr, rd, bd, po, l, m, p) => /*po.POST_ULEVEL == "P" &&*/ p.PGROUP_ID == po.PGROUP_ID)
                 .Where((pr, rd, bd, po, l, m, p) => postIds.Contains(pr.POST_ID))
                 .WhereIF(labId.IsNotNullOrEmpty(), (pr, rd, bd, po, l, m, p) => po.LAB_ID == labId)
                 .WhereIF(areaId.IsNotNullOrEmpty(), (pr, rd, bd, po, l, m, p) => p.AREA_ID == areaId)
                 .WhereIF(mgroupId.IsNotNullOrEmpty(), (pr, rd, bd, po, l, m, p) => p.MGROUP_ID == mgroupId)
                 .WhereIF(pgroupId.IsNotNullOrEmpty(), (pr, rd, bd, po, l, m, p) => po.PGROUP_ID == pgroupId)
                 .WhereIF(keyWord.IsNotNullOrEmpty(), (pr, rd, bd, po, l, m, p) => po.POST_NAME.Contains(keyWord) || rd.POSTROLE_NAME.Contains(keyWord))
                 .Select((pr, rd, bd, po, l, m, p) => new { PROLE_DICT_ID = rd.PROLE_DICT_ID ?? bd.DATA_ID, POSTROLE_NAME = rd.POSTROLE_NAME ?? bd.DATA_CNAME, po.POST_ULEVEL, po.LAB_ID, l.LAB_NAME, m.MGROUP_ID, m.MGROUP_NAME, p.PGROUP_ID, p.PGROUP_NAME, MGROUP = p.MGROUP_ID, pr.POSTROLE_ID, pr.POST_ID, po.POST_NAME })
                 .ToList();

            foreach (var roleDict in postRoleData.Where(d => d.PROLE_DICT_ID.IsNotNullOrEmpty()).GroupBy(d => d.PROLE_DICT_ID).OrderBy(pr => pr.Key))
            {
                var firstLevel = new OrgTreeNode
                {
                    NAME = roleDict.First().POSTROLE_NAME,
                    NODE_TYPE = GroupTreeNodeTypeEnum.POST.ToIntStr(),
                    NODE_TYPE_NAME = GroupTreeNodeTypeEnum.POST.ToDesc(),
                    SOURCE_ID = roleDict.First().PROLE_DICT_ID,
                };
                tree.ChildAdd(firstLevel);

                foreach (var org in roleDict.GroupBy(p => $"{p.LAB_ID}_{p.MGROUP_ID ?? p.MGROUP}_{p.PGROUP_ID}").OrderBy(r => r.Key))
                {
                    OrgTreeNode secondLevel = null;
                    switch (org.First().POST_ULEVEL)
                    {
                        case "L":
                            {
                                var labNode = new OrgTreeNode
                                {
                                    NAME = org.First().LAB_NAME,
                                    NODE_TYPE = GroupTreeNodeTypeEnum.LAB.ToIntStr(),
                                    NODE_TYPE_NAME = GroupTreeNodeTypeEnum.LAB.ToDesc(),
                                    SOURCE_ID = org.First().LAB_ID,
                                };
                                secondLevel = labNode;
                            }
                            break;
                        case "M":
                            {
                                var mgroupNode = new OrgTreeNode
                                {
                                    NAME = org.First().MGROUP_NAME,
                                    NODE_TYPE = GroupTreeNodeTypeEnum.MGROUP.ToIntStr(),
                                    NODE_TYPE_NAME = GroupTreeNodeTypeEnum.MGROUP.ToDesc(),
                                    SOURCE_ID = org.First().MGROUP_ID,
                                };
                                secondLevel = mgroupNode;
                            }
                            break;
                        case "P":
                            {
                                var pgroupNode = new OrgTreeNode
                                {
                                    NAME = org.First().PGROUP_NAME,
                                    NODE_TYPE = GroupTreeNodeTypeEnum.PGROUP.ToIntStr(),
                                    NODE_TYPE_NAME = GroupTreeNodeTypeEnum.PGROUP.ToDesc(),
                                    SOURCE_ID = org.First().PGROUP_ID,
                                };
                                secondLevel = pgroupNode;
                            }
                            break;
                    }
                    if (secondLevel != null)
                    {
                        firstLevel.ChildAdd(secondLevel);
                        foreach (var post in org.GroupBy(o => o.POSTROLE_ID))
                        {
                            var thirdLevel = new OrgTreeNode
                            {
                                NAME = post.First().POST_NAME,
                                NODE_TYPE = GroupTreeNodeTypeEnum.PROLE.ToIntStr(),
                                NODE_TYPE_NAME = GroupTreeNodeTypeEnum.PROLE.ToDesc(),
                                SOURCE_ID = post.First().POSTROLE_ID,
                            };
                            secondLevel.ChildAdd(thirdLevel);
                        }
                    }
                }
            }
            tree.RefreshTree(GroupTreeNodeTypeEnum.PROLE.ToIntStr());
            tree.AllNodesRemove(a => a.NUM == 0);
            return tree;
        }

        #endregion

        #region 岗位信息
        public List<PostInfoDto> GetUserPostInfoList(string personId, string? postClass, string? startDate, string? endDate, string? keyWord)
        {
            var userId = _soa.Db.Queryable<PMS_PERSON_INFO>().Where(a => a.PERSON_ID == personId).Select(a => a.USER_ID).First();
            if (userId == null)
                return new List<PostInfoDto>();

            return GetUserPostInfoListByUserIds(new List<string> { userId }, postClass, startDate, endDate, keyWord);

        }
        private List<PostInfoDto> GetUserPostInfoListByUserIds(List<string> userIds, string? postClass, string? startDate, string? endDate, string? keyWord)
        {
            var result = _soa.Db.Queryable<SYS6_USER_POST>()
                .LeftJoin<SYS6_POST_ROLE>((u, pr) => u.POSTROLE_ID == pr.POSTROLE_ID)
                .InnerJoin<SYS6_POST>((u, pr, p) => (u.POST_ID == p.POST_ID || pr.POST_ID == p.POST_ID) && p.POST_ULEVEL != null && p.POST_STATE != "0" && p.POST_STATE != "2")
                .LeftJoin<SYS6_POST_ROLE_DICT>((u, pr, p, r) => r.PROLE_DICT_ID == pr.PROLE_DICT_ID && r.POSTROLE_STATE != "0" && r.POSTROLE_STATE != "2") //r.POSTROLE_STATE == -1 表示虚拟岗位角色
                .LeftJoin<SYS6_BASE_DATA>((u, pr, p, r, bd) => bd.CLASS_ID == "岗位角色" && bd.DATA_ID == pr.PROLE_DICT_ID)
                .Where((u, pr, p, r, bd) => userIds.Contains(u.USER_NO) && u.UPOST_STATE != "0" && u.UPOST_STATE != "2")
                .Where((u, pr, p, r, bd) => pr.POSTROLE_ID != null) //只查新权限
                .WhereIF(postClass.IsNotNullOrEmpty(), (u, pr, p, r, bd) => p.POST_CLASS == postClass)
                .WhereIF(keyWord.IsNotNullOrEmpty(), (u, pr, p, r, bd) => p.POST_NAME.Contains(keyWord) || r.POSTROLE_NAME.Contains(keyWord) || bd.DATA_CNAME.Contains(keyWord))
                .Select((u, pr, p, r, bd) => new PostInfoDto
                {
                    POST_ID = p.POST_ID.SelectAll(),
                    UPOST_ID = u.UPOST_ID,
                    POSTROLE_ID = pr.POSTROLE_ID,
                    PROLE_DICT_ID = pr.PROLE_DICT_ID,
                    POSTROLE_NAME = r.POSTROLE_NAME ?? bd.DATA_CNAME,
                    POSTROLE_SORT = pr.POSTROLE_ID,
                    USER_POST_STATE = u.UPOST_STATE,
                    UPOST_USE_SDATE = u.UPOST_USE_SDATE,
                    UPOST_USE_EDATE = u.UPOST_USE_EDATE,
                    UPOST_END_DATE = u.UPOST_END_DATE,
                }
                ).ToList();

            result.ForEach(a =>
            {
                a.POST_CLASS_NAME = RecordClassBaseName("岗位分类", a.POST_CLASS);
                a.POST_TYPE_NAME = RecordClassBaseName("岗位类型", a.POST_TYPE);
                a.USER_POST_STATE_NAME = a.USER_POST_STATE switch
                {
                    "0" => "停岗",
                    "1" => "在用",
                    "2" => "结束",
                    _ => ""
                };
            });

            if (startDate.IsNotNullOrEmpty())
            {
                DateTime start = Convert.ToDateTime(startDate);
                result = result.FindAll(a => a.UPOST_USE_SDATE >= start || a.UPOST_USE_EDATE >= start);
            }
            if (endDate.IsNotNullOrEmpty())
            {
                DateTime end = Convert.ToDateTime(endDate).AddMonths(1);
                result = result.FindAll(a => a.UPOST_USE_SDATE < end || a.UPOST_USE_EDATE < end);
            }
            result = result.OrderBy(a => a.POST_SORT).ThenBy(a => a.POST_ID).ThenBy(a => a.POSTROLE_SORT).ToList();
            return result;
        }

        public List<PostInfoDto> GetUserPostInfoListByPersonIds(List<string> personIds, string? postClass, string? startDate, string? endDate, string keyWord)
        {
            List<string> userIds = _soa.Db.Queryable<PMS_PERSON_INFO>().Where(a => personIds.Contains(a.PERSON_ID)).Select(a => a.USER_ID).ToList();
            if (userIds == null && !userIds.Any())
                return new List<PostInfoDto>();

            var result = GetUserPostInfoListByUserIds(userIds, postClass, startDate, endDate, keyWord);

            List<string> upostIds = result.Select(a => a.UPOST_ID).Distinct().ToList();

            var groupPersonList = _soa.Db.Queryable<SYS6_USER_POST>()
                .InnerJoin<SYS6_USER>((up, u) => up.USER_NO == u.USER_NO && u.STATE_FLAG == "1")
                .InnerJoin<SYS6_INSPECTION_PGROUP>((up, u, p) => u.DEPT_CODE == p.PGROUP_ID && p.PGROUP_STATE == "1")
                .Where((up, u, p) => upostIds.Contains(up.UPOST_ID))
                .Select((up, u, p) => new
                {
                    up.UPOST_ID,
                    p.PGROUP_ID,
                    p.PGROUP_NAME,
                    u.USER_NO,
                    u.USERNAME,
                    u.HIS_ID
                })
                .ToList()
                .GroupBy(a => a.UPOST_ID)
                .Select(post => new
                {
                    post.First().UPOST_ID,
                    PgroupPersonList = post.GroupBy(b => b.PGROUP_ID).Select(group => new
                    {
                        group.First().PGROUP_ID,
                        group.First().PGROUP_NAME,
                        PGROUP_COUNT = group.Count(),
                        PersonList = group.Select(person => new
                        {
                            person.HIS_ID,
                            person.USERNAME,
                            HIS_NAME = $"{person.HIS_ID}_{person.USERNAME}",
                            person.USER_NO
                        })
                    })
                    .ToList()
                });

            result.ForEach(a =>
            {
                a.PgroupPersonList = groupPersonList.Where(list => list.UPOST_ID == a.UPOST_ID).FirstOrDefault()?.PgroupPersonList;
            });

            return result;
        }

        public List<PersonPostInfoDto> BatchGetUserPostInfoList(List<string> personIds, string? userPostState, string? startDate, string? endDate, string? keyWord)
        {
            List<string> userIds = _soa.Db.Queryable<PMS_PERSON_INFO>().Where(a => personIds.Contains(a.PERSON_ID)).Select(a => a.USER_ID).ToList();
            if (userIds == null && !userIds.Any())
                return new List<PersonPostInfoDto>();

            var result = _soa.Db.Queryable<SYS6_USER_POST>()
                .LeftJoin<SYS6_POST_ROLE>((up, pr) => up.POSTROLE_ID == pr.POSTROLE_ID)
                .InnerJoin<SYS6_POST>((up, pr, p) => (up.POST_ID == p.POST_ID || pr.POST_ID == p.POST_ID) && p.POST_ULEVEL != null && p.POST_STATE != "0" && p.POST_STATE != "2")
                .LeftJoin<SYS6_POST_ROLE_DICT>((up, pr, p, r) => r.PROLE_DICT_ID == pr.PROLE_DICT_ID && (r.POSTROLE_STATE == "-1" || r.POSTROLE_STATE == "1")) //r.POSTROLE_STATE == -1 表示虚拟岗位角色
                .LeftJoin<SYS6_BASE_DATA>((up, pr, p, r, bd) => bd.CLASS_ID == "岗位角色" && pr.PROLE_DICT_ID == bd.DATA_ID)
                .InnerJoin<PMS_PERSON_INFO>((up, pr, p, r, bd, u) => up.USER_NO == u.USER_ID)
                .LeftJoin<SYS6_INSPECTION_PGROUP>((up, pr, p, r, bd, u, pg) => pg.PGROUP_ID == u.PGROUP_ID && pg.PGROUP_STATE == "1")
                .Where((up, pr, p, r, bd, u, pg) => userIds.Contains(up.USER_NO))
                .Where((up, pr, p, r, bd, u, pg) => pr.POSTROLE_ID != null)
                .WhereIF(userPostState.IsNotNullOrEmpty(), (up, pr, p, r, bd, u, pg) => up.UPOST_STATE == userPostState)
                .WhereIF(keyWord.IsNotNullOrEmpty(), (up, pr, p, r, bd, u, pg) => p.POST_NAME.Contains(keyWord) || r.POSTROLE_NAME.Contains(keyWord) || u.USER_NAME.Contains(keyWord))
                .Select((up, pr, p, r, bd, u, pg) => new PersonPostInfoDto
                {
                    POST_ID = p.POST_ID.SelectAll(),
                    UPOST_ID = up.UPOST_ID,
                    POSTROLE_ID = pr.POSTROLE_ID,
                    PROLE_DICT_ID = r.PROLE_DICT_ID ?? bd.DATA_ID,
                    POSTROLE_NAME = r.POSTROLE_NAME ?? bd.DATA_CNAME,
                    POSTROLE_SORT = r.POSTROLE_SORT,
                    USER_POST_STATE = up.UPOST_STATE,
                    UPOST_USE_SDATE = up.UPOST_USE_SDATE,
                    UPOST_USE_EDATE = up.UPOST_USE_EDATE,
                    UPOST_END_DATE = up.UPOST_END_DATE,
                    USER_ID = u.USER_ID,
                    PERSON_ID = u.PERSON_ID,
                    USER_NAME = u.USER_NAME,
                    HIS_ID = u.HIS_ID,
                    SEX_NAME = u.SEX == "1" ? "男" : u.SEX == "2" ? "女" : "",
                    PGROUP_ID = u.PGROUP_ID,
                    PGROUP_NAME = pg.PGROUP_NAME,
                    FIRST_RTIME = up.FIRST_RTIME,
                    FIRST_RPERSON = up.FIRST_RPERSON,
                    LAST_MPERSON = up.LAST_MPERSON,
                    LAST_MTIME = up.LAST_MTIME,
                }
                ).ToList();

            result.ForEach(a =>
            {
                //g.POST_CLASS_NAME = RecordClassBaseName("岗位分类", g.POST_CLASS);
                //g.POST_TYPE_NAME = RecordClassBaseName("岗位类型", g.POST_TYPE);
                a.USER_POST_STATE_NAME = a.USER_POST_STATE switch
                {
                    "0" => "停岗",
                    "1" => "在用",
                    "2" => "结束",
                    _ => ""
                };
            });

            if (startDate.IsNotNullOrEmpty())
            {
                DateTime start = Convert.ToDateTime(startDate);
                result = result.FindAll(a => a.UPOST_USE_SDATE >= start || a.UPOST_USE_EDATE >= start);
            }
            if (endDate.IsNotNullOrEmpty())
            {
                DateTime end = Convert.ToDateTime(endDate).AddMonths(1);
                result = result.FindAll(a => a.UPOST_USE_SDATE < end || a.UPOST_USE_EDATE < end);
            }
            result = result.OrderBy(a => a.POST_SORT).ThenBy(a => a.POST_ID).ThenBy(a => a.POSTROLE_SORT).ToList();
            return result;
        }

        /// <summary>
        /// 获取岗位角色规评结果
        /// </summary>
        /// <param name="personId"></param>
        /// <param name="postRoleId"></param>
        /// 
        /// <returns></returns>
        public List<PersonPostRoleEPlanResult> GetPersonPostRoleEPlanResultList(string? personId, string postRoleId)
        {
            var eplanInfo = _soa.Db.Queryable<SYS6_POST_ROLE>()
                 .InnerJoin<SYS6_POST>((pr, p) => pr.POST_ID == p.POST_ID && p.POST_STATE == "1" && p.IF_ASSESS == "1") // && post.POST_STATE=="1"  岗位状态
                 .LeftJoin<SYS6_INSPECTION_PGROUP>((pr, p, pg) => p.POST_ULEVEL == "P" && p.PGROUP_ID == pg.PGROUP_ID)
                 .InnerJoin<OA_EVALUATE_PLAN_UNIT>((pr, p, pg, eu) => (eu.UNIT_TYPE == "LAB" && eu.UNIT_ID == p.LAB_ID)
                                                                    || (eu.UNIT_TYPE == "MGROUP" && (eu.UNIT_ID == p.PGROUP_ID || eu.UNIT_ID == pg.MGROUP_ID))
                                                                    || (eu.UNIT_TYPE == "PGROUP" && eu.UNIT_ID == p.PGROUP_ID)
                                                                    || (eu.UNIT_TYPE == "POST" && eu.UNIT_ID == p.POST_ID)
                                                                    || (eu.UNIT_TYPE == "PROLE" && eu.UNIT_ID == pr.POSTROLE_ID)
                                                                    )
                 .InnerJoin<OA_EVALUATE_PLAN_SETUP>((pr, p, pg, eu, set) => set.EPLAN_SID == eu.EPLAN_SID && set.EPLAN_SSTATE == EvaluatePlanSetupStateEnum.ENABLE.ToIntStr())
                 .Where((pr, p, pg, eu, set) => pr.POSTROLE_ID == postRoleId && eu.EPLAN_USTATE == "1")
                 .Select((pr, p, pg, eu, set) => new { set.EPLAN_ID, p.HOSPITAL_ID })
                 .ToList();

            if (!eplanInfo.Any())
                return new List<PersonPostRoleEPlanResult>();

            var eplanIds = eplanInfo.Select(a => a.EPLAN_ID).Distinct().ToList();
            var hospitalId = eplanInfo.Select(a => a.HOSPITAL_ID).FirstOrDefault();

            List<OA_EVALUATE_PLAN_USER> eplanUsers = new List<OA_EVALUATE_PLAN_USER>();

            if (personId.IsNotNullOrEmpty())
            {
                eplanUsers = _soa.Db.Queryable<OA_EVALUATE_PLAN_USER>()
                 .InnerJoin<PMS_PERSON_INFO>((eu, p) => p.USER_ID == eu.USER_ID)
                 .Where((eu, p) => p.PERSON_ID == personId && eplanIds.Contains(eu.EPLAN_ID))
                 .ToList();
            }

            var setups = GetEvaluatePlanSetupList(hospitalId, null, null, EPLAN_SSTATE: "1", null, null, null, null, null, IS_INCLUDE_EMPTY: false, EPLAN_ID_LIST: eplanIds);

            var data = setups.Select(set =>
                {
                    PersonPostRoleEPlanResult result = _mapper.Map<PersonPostRoleEPlanResult>(set);
                    var passUser = eplanUsers.Where(a => a.EPLAN_ID == set.EPLAN_ID && a.EPLAN_USER_STATE == "1" && a.EXPIRED_DATE != null)
                        .OrderByDescending(a => a.EXPIRED_DATE)
                        .FirstOrDefault();

                    if (passUser != null)
                    {
                        if (passUser.EXPIRED_DATE.Value > DateTime.Now)
                        {
                            if (passUser.WARN_START_DATE != null && passUser.WARN_START_DATE.Value < DateTime.Now)
                            {
                                result.RESULT_TYPE = "3";
                                result.RESULT_DESC = $"剩余{(passUser.EXPIRED_DATE.Value - DateTime.Now.Date).Days}天过期";
                            }
                            else
                            {
                                result.RESULT_TYPE = "1";
                                result.RESULT_DESC = "通过";
                            }
                        }
                        else
                        {
                            result.RESULT_TYPE = "4";
                            result.RESULT_DESC = "过期";
                        }
                    }
                    else if (personId.IsNotNullOrEmpty())
                    {
                        result.RESULT_TYPE = "2";
                        result.RESULT_DESC = "未通过";
                    }
                    return result;
                }).ToList();
            return data;
        }

        /// <summary>
        /// 获取岗位授权信息
        /// </summary>
        /// <param name="hospitalId"></param>
        /// <param name="postId"></param>
        /// <param name="postRoleId"></param>
        /// <returns></returns>
        /// <exception cref="BizException"></exception>
        public ResultDto GetPostRoleAndUnitInfo(string? hospitalId, string postId, string postRoleId)
        {
            Stopwatch sw = new Stopwatch();
            sw.Start();
            hospitalId = hospitalId ?? _httpContext.GetHospitalId();
            var accessToken = _httpContext.HttpContext.Request.Headers.Authorization;
            string url = $"/api/UserPostNew/GetNewPostRoleAndUnitInfoNew?postId={postId}&postRoleId={postRoleId}&hospitalId={hospitalId}";

            RestRequest request = new RestRequest(url);
            request.AddHeader("Authorization", accessToken);
            var response = _clientH07.ExecuteGet<ResultDto>(request);
            sw.Stop();
            if (response.ErrorException != null)
            {
                Log.Error($"调用H07模块[GetNewPostRoleAndUnitInfoNew]发生错误:{response.ErrorException}");
                throw new BizException($"调用H07模块[GetNewPostRoleAndUnitInfoNew]发生错误:{response.ErrorException}");
            }
            else
            {
                if (response.Data.success)
                {
                    return response.Data;
                }
                else
                {
                    Log.Error($"调用H07模块[GetNewPostRoleAndUnitInfoNew]发生错误:" + response.Data.msg);
                    return null;
                }
            }
        }
        #endregion

        #region 规评结果处理

        /// <summary>
        /// 写入人员规评结果（一般在生效环节调用本方法）
        /// </summary>
        /// <param name="soa"></param>
        /// <param name="userId">用户ID</param>
        /// <param name="ePlanSid">规评方案主键</param>
        /// <param name="dataClass">业务数据类型，由业务系统自定义，比如“EXAM”</param>
        /// <param name="dataId">业务数据主键</param>
        /// <param name="result">业务数据评价结果：1-通过 2-未通过</param>
        /// <param name="affectDate">评价生效日期</param>
        /// <returns>是否成功</returns>
        /// <exception cref="Exception"></exception>
        public bool WriteEvaluatePlanUserResult(List<EvaluatePlanUserResultParm> users)
        {
            var result = _authorityService.WriteEvaluatePlanUserResult(_soa, users);
            //var userIds = users.Select(g => g.USER_ID).Distinct().ToList();
            //HandleEvaluatePlanCurrentChanged(userIds);
            return result;
        }

        /// <summary>
        /// 限权停岗计算
        /// </summary>
        /// <param name="eplanSids"></param>
        /// <param name="userIds"></param>
        /// <param name="operatorHisName"></param>
        /// <returns></returns>
        public bool HandleEvaluatePlanCurrentChanged(List<string>? userIds = null, List<string>? eplanSids = null, string? operatorHisName = null)
        {
            List<SYS6_USER_POWER_LOG> powerLogs_inserts = new List<SYS6_USER_POWER_LOG>();
            List<SYS6_USER_POSTROLE_LOG> postLogs_inserts = new List<SYS6_USER_POSTROLE_LOG>();
            List<SYS6_USER_POSTROLE_LOG> postLogs_updates = new List<SYS6_USER_POSTROLE_LOG>();

            var eplanSetUserRoles = _soa.Db.Queryable<SYS6_POST_ROLE>()
                .InnerJoin<SYS6_POST>((pr, p) => pr.POST_ID == p.POST_ID && p.POST_STATE != "0" && p.POST_STATE != "2") //&& p.POST_STATE != "2"  岗位状态
                .LeftJoin<SYS6_INSPECTION_PGROUP>((pr, p, pg) => p.POST_ULEVEL == "P" && p.PGROUP_ID == pg.PGROUP_ID)
                .InnerJoin<OA_EVALUATE_PLAN_UNIT>((pr, p, pg, eu) => (eu.UNIT_TYPE == "LAB" && eu.UNIT_ID == p.LAB_ID)
                                                                   || (eu.UNIT_TYPE == "MGROUP" && (eu.UNIT_ID == p.PGROUP_ID || eu.UNIT_ID == pg.MGROUP_ID))
                                                                   || (eu.UNIT_TYPE == "PGROUP" && eu.UNIT_ID == p.PGROUP_ID)
                                                                   || (eu.UNIT_TYPE == "POST" && eu.UNIT_ID == p.POST_ID)
                                                                   || (eu.UNIT_TYPE == "PROLE" && eu.UNIT_ID == pr.POSTROLE_ID)
                                                                   )
                .InnerJoin<OA_EVALUATE_PLAN_SETUP>((pr, p, pg, eu, set) => eu.EPLAN_SID == set.EPLAN_SID && set.EPLAN_SHELF_LIFE != null && set.SHELF_LIFE_UTYPE != null && (set.LIMIT_PROLE_TYPE == "1" && set.PROLE_COM_SID != null || set.LIMIT_PROLE_TYPE == "2"))
                .InnerJoin<SYS6_USER_POST>((pr, p, pg, eu, set, up) => up.POST_ID == pr.POSTROLE_ID)  // && up.UPOST_STATE == "1" 岗位状态 0停岗 1在用 2结束
                .Where((pr, p, pg, eu, set, up) => eu.EPLAN_USTATE == "1")
                .WhereIF(eplanSids != null && eplanSids.Any(), (pr, p, pg, eu, set, up) => set.EPLAN_SSTATE == "1" || eplanSids.Contains(set.EPLAN_SID)) //【注意】指定方案也要判断所有有效方案的影响；对于方案撤销的场景，需去掉状态过滤
                .WhereIF(eplanSids == null || !eplanSids.Any(), (pr, p, pg, eu, set, up) => set.EPLAN_SSTATE == "1")
                .WhereIF(userIds != null && userIds.Any(), (pr, p, pg, eu, set, up) => userIds.Contains(up.USER_NO))
                .Select((pr, p, pg, eu, set, up) => new
                {
                    set.HOSPITAL_ID,
                    pr.POSTROLE_ID,
                    p.POST_ID,
                    set.EPLAN_SID,
                    set.EPLAN_ID,
                    set.EPLAN_SHELF_LIFE,
                    set.SHELF_LIFE_UTYPE,
                    set.LIMIT_PROLE_TYPE,
                    set.PROLE_COM_SID,
                    set.EPLAN_SSTATE,
                    up.USER_NO
                })
                .ToList();

            //var eplanIdList = eplanSetUserRoles.Select(g => g.EPLAN_ID).Distinct().ToList();
            //var eplanUsers = _soa.Db.Queryable<OA_EVALUATE_PLAN_USER>().Where(g => eplanIdList.Contains(g.EPLAN_ID)).ToList();
            var eplanUsers = _soa.Db.Queryable<OA_EVALUATE_PLAN_USER>()
                 .WhereIF(userIds != null && userIds.Any(), a => userIds.Contains(a.USER_ID))//待根据时间进行优化
                 .ToList();

            var userSetResults = eplanSetUserRoles.Select(userRole =>
            new
            {
                //决定同一方案之下的结果
                IsPassInTime = eplanUsers.Any(a => a.EPLAN_ID == userRole.EPLAN_ID && a.USER_ID == userRole.USER_NO
                    && a.EPLAN_USER_STATE == "1"
                    && a.AFFECT_DATE != null
                    && userRole.EPLAN_SHELF_LIFE != null
                    && userRole.SHELF_LIFE_UTYPE switch
                    {
                        "1" => a.AFFECT_DATE.Value.Date.AddYears(Convert.ToInt32(userRole.EPLAN_SHELF_LIFE)),
                        "2" => a.AFFECT_DATE.Value.Date.AddMonths(Convert.ToInt32(userRole.EPLAN_SHELF_LIFE)),
                        "3" => a.AFFECT_DATE.Value.Date.AddDays(Convert.ToInt32(userRole.EPLAN_SHELF_LIFE))
                    } > DateTime.Now.Date),
                //方案已经取消启用
                IsCancelEplan = userRole.EPLAN_SSTATE != EvaluatePlanSetupStateEnum.ENABLE.ToIntStr(),
                userRole.HOSPITAL_ID,
                userRole.POSTROLE_ID,
                userRole.POST_ID,
                userRole.EPLAN_SID,
                userRole.EPLAN_ID,
                userRole.EPLAN_SHELF_LIFE,
                userRole.SHELF_LIFE_UTYPE,
                userRole.LIMIT_PROLE_TYPE,
                userRole.PROLE_COM_SID,
                userRole.USER_NO
            }).ToList();

            //处理限权
            {
                var LimitRoleIds = userSetResults.SelectMany(a => a.PROLE_COM_SID.IsNullOrEmpty() ? Array.Empty<string>() : a.PROLE_COM_SID.Split('+')).Distinct().ToList();
                var LimitRoleMenus = _soa.Db.Queryable<SYS6_LIMIT_ROLE_DICT>()
                    .InnerJoin<SYS6_LIMIT_ROLE_MENU>((d, m) => d.LROLE_NO == m.LROLE_NO)
                    .Where((d, m) => LimitRoleIds.Contains(d.LROLE_NO) && d.LROLE_STATE == "1")
                    .Select((d, m) => m)
                    .ToList();

                var postIds = userSetResults.Select(a => a.POSTROLE_ID).Distinct().ToList();//应该取岗位还是岗位角色？

                var postMenus = _soa.Db.Queryable<SYS6_POST_ROLE_COM>()
                    .InnerJoin<SYS6_ROLE_COM_LIST>((roleCom, comList) => comList.ROLECOM_ID == roleCom.ROLECOM_ID && comList.ROLE_STATE == "1" && postIds.Contains(roleCom.POST_ID))
                    .InnerJoin<SYS6_ROLE>((roleCom, comList, role) => comList.ROLE_ID == role.ROLE_NO && role.ROLE_STATE == "1")
                    .InnerJoin<SYS6_ROLE_MENU>((roleCom, comList, role, roMenu) => comList.ROLE_ID == roMenu.ROLE_NO)
                    //.InnerJoin<SYS6_MENU>((roleCom, comList, role, roMenu, menu) => roMenu.MENU_NO == menu.MENU_ID && menu.MENU_STATE != "0" && menu.MENU_STATE != "2")
                    .Select((roleCom, comList, role, roMenu/*, menu*/) => new { roleCom.POST_ID, role.ROLE_NO, role.MODULE_ID /*, menu.MENU_ID */})
                    .Distinct()
                    .ToList();

                //用户 - 岗位 - 模块 - 角色 - 是否限权 - 限权组合列表
                var roleMenus = userSetResults.Where(a => a.LIMIT_PROLE_TYPE == LimitRoleTypeEnum.LIMIT.ToIntStr())
                    .Join(postMenus, a => a.POSTROLE_ID, b => b.POST_ID, (a, b) => new  // g.POST_ID 结果为空
                    {
                        a.HOSPITAL_ID,
                        a.USER_NO,
                        a.POSTROLE_ID,
                        a.EPLAN_SID,
                        b.MODULE_ID,
                        b.ROLE_NO,
                        IsRoleEnable = a.IsPassInTime || a.IsCancelEplan,
                        limitRoles = a.PROLE_COM_SID?.Split('+') ?? Array.Empty<string>(),
                        //b.MENU_ID,
                    })
                    .Where(a => a.limitRoles.Any())
                    .ToList();

                //用户 - 岗位 - 模块 - 角色
                roleMenus.GroupBy(a => new { a.HOSPITAL_ID, a.USER_NO, a.POSTROLE_ID, a.MODULE_ID, a.ROLE_NO, })
                    .ForEach(g =>
                    {
                        var disableMenus = g.Where(a => !a.IsRoleEnable)
                            .SelectMany(a => LimitRoleMenus.FindAll(m => a.limitRoles.Contains(m.LROLE_NO)))
                            .Select(a => a.MENU_NO)
                            .Distinct()
                            .ToList();

                        var enableMenus = g.Where(a => a.IsRoleEnable)
                            .SelectMany(a => LimitRoleMenus.FindAll(m => a.limitRoles.Contains(m.LROLE_NO)))
                            .Select(a => a.MENU_NO)
                            .Distinct()
                            .Where(a => !disableMenus.Contains(a)) //排除限权菜单
                            .ToList();

                        var now = DateTime.Now;
                        if (disableMenus.Any())
                        {
                            powerLogs_inserts.Add(
                            new SYS6_USER_POWER_LOG
                            {
                                UPOWE_ID = IDGenHelper.CreateGuid(),
                                HOSPITAL_ID = g.First().HOSPITAL_ID,
                                USER_SNO = g.First().USER_NO,
                                POST_ID = g.First().POSTROLE_ID,  //岗位ID还是岗位角色ID?
                                MODULE_ID = g.First().MODULE_ID,
                                ROLE_NO = g.First().ROLE_NO,
                                CHANGE_POWER = string.Join("+", disableMenus),
                                CHANGE_TIME = now,
                                OPERATE_TYPE = "规评限权",
                                CHANGE_PERSON = _httpContext.GetUserHisName(),
                                FIRST_RPERSON = _httpContext.GetUserHisName(),
                                FIRST_RTIME = now,
                                LAST_MPERSON = _httpContext.GetUserHisName(),
                                LAST_MTIME = now,
                                REMARK = string.Join('+', g.Select(a => a.EPLAN_SID)),
                            });
                        }
                        if (enableMenus.Any())
                        {
                            powerLogs_inserts.Add(
                            new SYS6_USER_POWER_LOG
                            {
                                UPOWE_ID = IDGenHelper.CreateGuid(),
                                HOSPITAL_ID = g.First().HOSPITAL_ID,
                                USER_SNO = g.First().USER_NO,
                                POST_ID = g.First().POSTROLE_ID,  //岗位ID还是岗位角色ID?
                                MODULE_ID = g.First().MODULE_ID,
                                ROLE_NO = g.First().ROLE_NO,
                                CHANGE_POWER = string.Join("+", enableMenus),
                                CHANGE_TIME = now,
                                OPERATE_TYPE = "规评启用权限",
                                CHANGE_PERSON = _httpContext.GetUserHisName(),
                                FIRST_RPERSON = _httpContext.GetUserHisName(),
                                FIRST_RTIME = now,
                                LAST_MPERSON = _httpContext.GetUserHisName(),
                                LAST_MTIME = now,
                                REMARK = string.Join('+', g.Select(a => a.EPLAN_SID)),
                            });
                        }
                    });

            }
            //处理停岗
            {
                var userIdList = userSetResults.Where(a => a.LIMIT_PROLE_TYPE == LimitRoleTypeEnum.STOP.ToIntStr()).Select(a => a.USER_NO).Distinct().ToList();
                var proleList = userSetResults.Where(a => a.LIMIT_PROLE_TYPE == LimitRoleTypeEnum.STOP.ToIntStr()).Select(a => a.POSTROLE_ID).Distinct().ToList();

                var historyLogs = _soa.Db.Queryable<SYS6_USER_POSTROLE_LOG>()
                    .Where(a => userIdList.Contains(a.USER_NO) && proleList.Contains(a.POSTROLE_ID))
                    .ToList();

                userSetResults.Where(a => a.LIMIT_PROLE_TYPE == LimitRoleTypeEnum.STOP.ToIntStr())
                .GroupBy(a => new { a.HOSPITAL_ID, a.USER_NO, a.POSTROLE_ID })
                .ForEach(prole =>
                {
                    bool isStop = prole.All(a => !a.IsPassInTime && !a.IsCancelEplan);
                    var lastLog = historyLogs.Where(a => a.USER_NO == prole.First().USER_NO && a.POSTROLE_ID == prole.First().POSTROLE_ID && a.HOSPITAL_ID == prole.First().HOSPITAL_ID)
                        .OrderByDescending(i => i.UPOST_START_DATE)//按用户、岗位角色分组，并取最新一条
                        .FirstOrDefault();

                    //结论与日志不一致，则需修改
                    if (lastLog == null || (lastLog.OPERATE_TYPE == "规评停岗" && !isStop) || (lastLog.OPERATE_TYPE == "规评启用岗位" && isStop))
                    {
                        if (lastLog != null)
                        {
                            //更新旧日志
                            lastLog.UPOST_END_DATE = DateTime.Now.Date;
                            lastLog.LAST_MPERSON = _httpContext.GetUserHisName();
                            lastLog.LAST_MTIME = DateTime.Now;
                            postLogs_updates.Add(lastLog);
                        }

                        //更新新日志
                        postLogs_inserts.Add(
                            new SYS6_USER_POSTROLE_LOG
                            {
                                UPOST_LOG_ID = IDGenHelper.CreateGuid(),
                                HOSPITAL_ID = prole.First().HOSPITAL_ID,
                                USER_NO = prole.First().USER_NO,
                                POSTROLE_ID = prole.First().POSTROLE_ID,
                                UPOST_START_DATE = DateTime.Now.Date,
                                CHANGE_PERSON = _httpContext.GetUserHisName(),
                                CHANGE_TIME = DateTime.Now,
                                OPERATE_TYPE = isStop ? "规评停岗" : "规评启用岗位",
                                FIRST_RPERSON = _httpContext.GetUserHisName(),
                                FIRST_RTIME = DateTime.Now,
                                REMARK = $"EPLAN_ID:{string.Join('+', prole.Select(a => a.EPLAN_ID).Distinct())}",
                            });
                    }
                });
            }

            if (postLogs_updates.Any())
                _soa.Db.Updateable(postLogs_updates).ExecuteCommand();
            if (postLogs_inserts.Any())
                _soa.Db.Insertable(postLogs_inserts).ExecuteCommand();
            if (powerLogs_inserts.Any())
                _soa.Db.Insertable(powerLogs_inserts).ExecuteCommand();

            return true;
        }


        public bool CreateOaEvaluatePlanEvent(List<OaEvaluatePlanEventParm> parms)
        {
            try
            {
                var events = new List<OA_EVALUATE_PLAN_EVENT>();
                DateTime now = DateTime.Now;
                foreach (var parm in parms)
                {
                    var evt = new OA_EVALUATE_PLAN_EVENT
                    {
                        EVENT_ID = $"EE_{IDGenHelper.CreateGuid()}",
                        HOSPITAL_ID = parm.HOSPITAL_ID,
                        EVENT_TYPE = parm.EVENT_TYPE,
                        EPLAN_ID = parm.EPLAN_ID,
                        USER_SID = parm.USER_SID,
                        SOURCE_ID = parm.SOURCE_ID,
                        EVENT_INFO = parm.EVENT_INFO,
                        CHANGE_PERSON = parm.CHANGE_PERSON ?? _httpContext.GetUserHisName(),
                        CHANGE_TIME = parm.CHANGE_TIME,
                    };
                    evt.FIRST_RPERSON = evt.CHANGE_PERSON;
                    evt.LAST_MPERSON = evt.CHANGE_PERSON;
                    evt.FIRST_RTIME = now;
                    evt.LAST_MTIME = now;
                    events.Add(evt);
                }
                if (events.Any())
                    _soa.Db.Insertable(events).ExecuteCommand();
                return true;
            }
            catch (Exception ex)
            {
                Log.Error($"创建规评方案变动事件失败！执行CreateOaEvaluatePlanEvent方法报错：{ex.ToString()}");
                throw new BizException("创建规评方案变动事件失败！");
            }
        }

        public bool HandleOaEvaluatePlanEvent()
        {
            string guid = Guid.NewGuid().ToString();
            try
            {
                var unHandles = _soa.Db.Queryable<OA_EVALUATE_PLAN_EVENT>()
                    .Where(a => a.EVENT_HANDLE_UID == null)
                    .ToList();
                if (unHandles.Any() && SetEvaluatePlanEventDBLock(guid))
                {
                    List<string> userIds = null;
                    if (unHandles.All(a => a.USER_SID.IsNotNullOrEmpty()))
                    {
                        userIds = unHandles.Where(a => a.USER_SID.IsNotNullOrEmpty())
                        .SelectMany(a => a.USER_SID.Split('+'))
                        .Distinct()
                        .ToList();
                    }
                    DateTime now = DateTime.Now;
                    //处理规评变动事件的具体逻辑
                    if (DoHandleOaEvaluatePlanEvent(guid, now, userIds))
                    {
                        foreach (var hand in unHandles)
                        {
                            hand.EVENT_HANDLE_UID = guid;
                            hand.EVENT_HANDLE_TIME = now;
                            hand.LAST_MPERSON = "H81规评事件处理程序";
                            hand.LAST_MTIME = now;
                        }
                        _soa.Db.Updateable(unHandles).ExecuteCommand();
                    }
                    DeleteEvaluatePlanEventDBLock(guid);
                }
                return true;
            }
            catch (Exception ex)
            {
                Log.Error($"处理规评变动事件失败！执行HandleOaEvaluatePlanEvent方法：{ex.ToString()}");
                DeleteEvaluatePlanEventDBLock(guid);
                throw new BizException("处理规评变动事件失败！");
            }
        }

        string LOCK_ID = "EPLAN_EVENT_LOCKING";//固定ID
        /// 设置数据库全局锁
        private bool SetEvaluatePlanEventDBLock(string guid)
        {
            DateTime deletePoint = DateTime.Now.AddMinutes(-30);
            //固定清除半小时前的锁，避免阻塞
            _soa.Db.Deleteable<OA_EVALUATE_PLAN_EVENT>().Where(a => a.EVENT_ID == LOCK_ID && a.FIRST_RTIME < deletePoint).ExecuteCommand();
            var newLockEntity = new OA_EVALUATE_PLAN_EVENT
            {
                EVENT_ID = LOCK_ID,
                HOSPITAL_ID = "H0000",
                EVENT_TYPE = "0",
                EVENT_HANDLE_UID = guid,
                FIRST_RPERSON = "限权作业处理中",
                FIRST_RTIME = DateTime.Now
            };
            try
            {
                _soa.Db.Insertable(newLockEntity).ExecuteCommand();
            }
            catch { }
            //成功插入锁数据，表示独享执行权
            bool isSucceed = _soa.Db.Queryable<OA_EVALUATE_PLAN_EVENT>().Any(a => a.EVENT_ID == LOCK_ID && a.EVENT_HANDLE_UID == guid);
            return isSucceed;
        }
        /// 任务结束后，清除数据库全局锁
        private void DeleteEvaluatePlanEventDBLock(string guid)
        {
            _soa.Db.Deleteable<OA_EVALUATE_PLAN_EVENT>().Where(a => a.EVENT_ID == LOCK_ID && a.EVENT_HANDLE_UID == guid).ExecuteCommand();
        }

        private bool DoHandleOaEvaluatePlanEvent(string guid, DateTime now, List<string>? userIds)
        {
            var newLogList = new List<OA_EVALUATE_EVENT_DISPOSE>();
            var oldLogList = new List<OA_EVALUATE_EVENT_DISPOSE_no_clob>();
            var newLimitItems = new List<SYS6_USER_PROLE_LIMIT>();

            var eplanSetUserRoles = _soa.Db.Queryable<SYS6_POST>()
                .LeftJoin<SYS6_POST_ROLE>((p, pr) => pr.POST_ID == p.POST_ID) //&& p.POST_STATE != "2"  岗位状态
                .LeftJoin<SYS6_INSPECTION_PGROUP>((p, pr, pg) => /*p.POST_ULEVEL == "P" &&*/ p.PGROUP_ID == pg.PGROUP_ID)
                .InnerJoin<OA_EVALUATE_PLAN_UNIT>((p, pr, pg, eu) => (eu.UNIT_TYPE == "LAB" && eu.UNIT_ID == p.LAB_ID)
                                                                   || (eu.UNIT_TYPE == "MGROUP" && (eu.UNIT_ID == p.PGROUP_ID || eu.UNIT_ID == pg.MGROUP_ID))
                                                                   || (eu.UNIT_TYPE == "PGROUP" && eu.UNIT_ID == p.PGROUP_ID)
                                                                   || (eu.UNIT_TYPE == "POST" && eu.UNIT_ID == p.POST_ID)
                                                                   || (eu.UNIT_TYPE == "PROLE" && eu.UNIT_ID == pr.POSTROLE_ID)
                                                                   )
                .InnerJoin<OA_EVALUATE_PLAN_SETUP>((p, pr, pg, eu, set) => eu.EPLAN_SID == set.EPLAN_SID && set.EPLAN_SHELF_LIFE != null && set.SHELF_LIFE_UTYPE != null && (set.LIMIT_PROLE_TYPE == "1" && set.PROLE_COM_SID != null || set.LIMIT_PROLE_TYPE == "2"))
                .InnerJoin<SYS6_USER_POST>((p, pr, pg, eu, set, up) => (up.POST_ID == "0" && up.POSTROLE_ID == pr.POSTROLE_ID) || (up.POST_ID == p.POST_ID && pr.POSTROLE_ID == null))  // && up.UPOST_STATE == "1" 岗位状态 0停岗 1在用 2结束。  up.POST_ID == "0"表示新人岗权逻辑
                .LeftJoin<OA_EVALUATE_PLAN_DICT>((p, pr, pg, eu, set, up, di) => di.EPLAN_ID == set.EPLAN_ID && di.EPLAN_STATE == "1") //【注意】只查有效规评方案！ 
                .LeftJoin<OA_CERTIFICATE_DICT>((p, pr, pg, eu, set, up, di, ce) => ce.CERTIFICATE_DID == set.EPLAN_ID && ce.CERTIFICATE_DSTATE == "1" && ce.EPLAN_FLAG == "1") //【注意】只查有效的证书！ 
                .Where((p, pr, pg, eu, set, up, di, ce) => p.IF_ASSESS == "1" && p.POST_STATE != "0" && p.POST_STATE != "2")
                .Where((p, pr, pg, eu, set, up, di, ce) => eu.EPLAN_USTATE == "1" && set.LIMIT_PROLE_TYPE != null)
                .Where((p, pr, pg, eu, set, up, di, ce) => set.EPLAN_SSTATE == "1") //【注意】只查有效规评设置！
                .Where((p, pr, pg, eu, set, up, di, ce) => di.EPLAN_ID != null || ce.CERTIFICATE_DID != null)
                //.WhereIF(eplanSids != null && eplanSids.Any(), (p, pr, pg, eu, set, up) => set.EPLAN_SSTATE == "1" || eplanSids.Contains(set.EPLAN_SID)) //【注意】指定方案也要判断所有有效方案的影响；对于方案撤销的场景，需去掉状态过滤
                //.WhereIF(eplanSids == null || !eplanSids.Any(), (p, pr, pg, eu, set, up) => set.EPLAN_SSTATE == "1")
                .WhereIF(userIds != null && userIds.Any(), (p, pr, pg, eu, set, up, di, ce) => userIds.Contains(up.USER_NO))
                .Select((p, pr, pg, eu, set, up, di, ce) => new
                {
                    set.HOSPITAL_ID,
                    up.POST_ID,
                    pr.POSTROLE_ID,
                    set.EPLAN_SID,
                    set.EPLAN_ID,
                    set.EPLAN_SHELF_LIFE,
                    set.SHELF_LIFE_UTYPE,
                    set.LIMIT_PROLE_TYPE,
                    set.PROLE_COM_SID,
                    //set.EPLAN_SSTATE, //只查有效记录，所以本属性不必要
                    up.USER_NO
                })
                .ToList();

            var eplanIdList = eplanSetUserRoles.Select(a => a.EPLAN_ID).Distinct().ToList();

            var eplanUsers = _soa.Db.Queryable<OA_EVALUATE_PLAN_USER>()
                .Where(a => eplanIdList.Contains(a.EPLAN_ID) && a.EPLAN_USER_STATE == "1")
                .WhereIF(userIds != null && userIds.Any(), a => userIds.Contains(a.USER_ID))
                .ToList();

            var userSetResults = eplanSetUserRoles.Select(userRole =>
            new
            {
                //同一规评方案，有一个结果通过，视为通过
                IsPassInTime = eplanUsers.Any(a => a.EPLAN_ID == userRole.EPLAN_ID && a.USER_ID == userRole.USER_NO
                    && a.EPLAN_USER_STATE == "1"
                    && a.AFFECT_DATE != null
                    && userRole.EPLAN_SHELF_LIFE != null
                    && userRole.SHELF_LIFE_UTYPE switch
                    {
                        "1" => a.AFFECT_DATE.Value.Date.AddYears(Convert.ToInt32(userRole.EPLAN_SHELF_LIFE)),
                        "2" => a.AFFECT_DATE.Value.Date.AddMonths(Convert.ToInt32(userRole.EPLAN_SHELF_LIFE)),
                        "3" => a.AFFECT_DATE.Value.Date.AddDays(Convert.ToInt32(userRole.EPLAN_SHELF_LIFE))
                    } > DateTime.Now.Date),
                ////方案已经取消
                //IsCancelEplan = userRole.EPLAN_SSTATE == "0" || userRole.EPLAN_SSTATE == "2",
                userRole.HOSPITAL_ID,
                userRole.POST_ID,  //POST_ID == "0"表示新人岗权逻辑
                userRole.POSTROLE_ID,
                userRole.EPLAN_SID,
                userRole.EPLAN_ID,
                userRole.EPLAN_SHELF_LIFE,
                userRole.SHELF_LIFE_UTYPE,
                userRole.LIMIT_PROLE_TYPE,
                userRole.PROLE_COM_SID,
                userRole.USER_NO
            }).ToList();

            var LimitRoleIds = userSetResults.SelectMany(a => a.PROLE_COM_SID.IsNullOrEmpty() ? Array.Empty<string>() : a.PROLE_COM_SID.Split('+')).Distinct().ToList();
            var LimitRoleMenus = _soa.Db.Queryable<SYS6_LIMIT_ROLE_DICT>()
                .InnerJoin<SYS6_LIMIT_ROLE_MENU>((d, m) => d.LROLE_NO == m.LROLE_NO)
                .Where((d, m) => LimitRoleIds.Contains(d.LROLE_NO) && d.LROLE_STATE == "1")
                .Select((d, m) => m)
                .ToList();

            //规评变动事件处理表
            userSetResults.GroupBy(a => new { a.HOSPITAL_ID, a.POST_ID, a.POSTROLE_ID, a.EPLAN_ID, a.USER_NO }).ForEach(g =>
            {
                //同一规评方案，有一个结果通过，视为通过
                if (!g.Any(i => i.IsPassInTime))
                {
                    var log = new OA_EVALUATE_EVENT_DISPOSE
                    {
                        DISPOSE_ID = $"ED_{IDGenHelper.CreateGuid()}",
                        HOSPITAL_ID = g.Key.HOSPITAL_ID,
                        EPLAN_ID = g.Key.EPLAN_ID,
                        USER_NO = g.Key.USER_NO,
                        POSTROLE_ID = g.Key.POSTROLE_ID ?? g.Key.POST_ID,
                        EVENT_HANDLE_UID = guid,
                        LIMIT_TYPE = g.First().LIMIT_PROLE_TYPE,
                        IF_CHANGE_LIMIT = "1",//表示最近一次处理
                        FIRST_RPERSON = "H81规评事件处理程序",
                        LAST_MPERSON = "H81规评事件处理程序",
                        FIRST_RTIME = now,
                        LAST_MTIME = now,
                    };
                    if (log.LIMIT_TYPE == LimitRoleTypeEnum.LIMIT.ToIntStr())
                    {
                        var limitRoleIds = g.First().PROLE_COM_SID?.Split('+') ?? Array.Empty<string>();
                        var menuIds = LimitRoleMenus.FindAll(m => limitRoleIds.Contains(m.LROLE_NO)).Select(i => i.MENU_NO);
                        log.LIMIT_MENU_SID = string.Join("+", menuIds);
                        log.REMARK = "限权";
                    }
                    else
                    {
                        log.REMARK = "停岗";
                    }

                    //临时代码：排除了fr_李影和gz?_广测账号的限权记录
                    if (log.USER_NO == "7" && log.USER_NO == "510360014" && log.USER_NO == "510360015" && log.USER_NO == "510360016" && log.USER_NO == "510360017")
                        return;

                    newLogList.Add(log);
                }
            });

            //处理旧日志
            var dbLogs = _soa.Db.Queryable<OA_EVALUATE_EVENT_DISPOSE_no_clob>()
                .Where(a => a.IF_CHANGE_LIMIT == "1")
                .WhereIF(userIds != null && userIds.Any(), a => userIds.Contains(a.USER_NO))
                .ToList();

            foreach (var log in dbLogs)
            {
                //上次日志标记为0
                log.IF_CHANGE_LIMIT = "0";
                log.LAST_MPERSON = "H81规评事件处理程序";
                log.LAST_MTIME = now;
                oldLogList.Add(log);

                //记录在本次失效的规评
                if (!newLogList.Any(a => a.USER_NO == log.USER_NO && a.EPLAN_ID == log.EPLAN_ID && a.POSTROLE_ID == log.POSTROLE_ID && a.HOSPITAL_ID == log.HOSPITAL_ID))
                {
                    var disable = new OA_EVALUATE_EVENT_DISPOSE
                    {
                        DISPOSE_ID = $"ED_{IDGenHelper.CreateGuid()}",
                        HOSPITAL_ID = log.HOSPITAL_ID,
                        EPLAN_ID = log.EPLAN_ID,
                        USER_NO = log.USER_NO,
                        POSTROLE_ID = log.POSTROLE_ID,
                        EVENT_HANDLE_UID = guid,
                        //LIMIT_TYPE = log.LIMIT_TYPE == LimitRoleTypeEnum.LIMIT.ToIntStr() ? LimitRoleTypeEnum.CANCEL_LIMIT.ToIntStr(),
                        IF_CHANGE_LIMIT = "1",//表示最近一次处理
                        FIRST_RPERSON = "H81规评事件处理程序",
                        LAST_MPERSON = "H81规评事件处理程序",
                        FIRST_RTIME = now,
                        LAST_MTIME = now,
                        //LIMIT_MENU_SID = limit.LIMIT_MENU_SID,
                        //REMARK = "取消限权",
                    };
                    if (log.LIMIT_TYPE == LimitRoleTypeEnum.LIMIT.ToIntStr())
                    {
                        disable.LIMIT_TYPE = LimitRoleTypeEnum.CANCEL_LIMIT.ToIntStr();
                        disable.REMARK = LimitRoleTypeEnum.CANCEL_LIMIT.ToDesc();
                        newLogList.Add(disable);
                    }
                    else if (log.LIMIT_TYPE == LimitRoleTypeEnum.STOP.ToIntStr())
                    {
                        disable.LIMIT_TYPE = LimitRoleTypeEnum.CANCEL_STOP.ToIntStr();
                        disable.REMARK = LimitRoleTypeEnum.CANCEL_STOP.ToDesc();
                        newLogList.Add(disable);
                    }
                }
            }

            //用户规评岗位限权表
            var oldUserProleLimits = _soa.Db.Queryable<SYS6_USER_PROLE_LIMIT>()
                .WhereIF(userIds != null && userIds.Any(), a => userIds.Contains(a.USER_NO))
                .ToList();

            newLogList.Where(a => a.LIMIT_TYPE == LimitRoleTypeEnum.LIMIT.ToIntStr() || a.LIMIT_TYPE == LimitRoleTypeEnum.STOP.ToIntStr())
                .GroupBy(a => new { a.HOSPITAL_ID, a.USER_NO, a.POSTROLE_ID })
                .ForEach(g =>
                {
                    var limitItem = new SYS6_USER_PROLE_LIMIT
                    {
                        USER_LIMIT_ID = $"EL_{IDGenHelper.CreateGuid()}",
                        HOSPITAL_ID = g.Key.HOSPITAL_ID,
                        USER_NO = g.Key.USER_NO,
                        POSTROLE_ID = g.Key.POSTROLE_ID,
                        LIMIT_TYPE = g.Any(i => i.LIMIT_TYPE == LimitRoleTypeEnum.STOP.ToIntStr()) ? LimitRoleTypeEnum.STOP.ToIntStr()
                                        : LimitRoleTypeEnum.LIMIT.ToIntStr(), //如有停岗，直接停岗；没有停岗，就是限权
                        EVENT_HANDLE_UID = guid,
                        FIRST_RPERSON = "H81规评事件处理程序",
                        LAST_MPERSON = "H81规评事件处理程序",
                        FIRST_RTIME = now,
                        LAST_MTIME = now,
                    };
                    //汇总限权菜单
                    if (limitItem.LIMIT_TYPE == LimitRoleTypeEnum.LIMIT.ToIntStr())
                    {
                        List<string> limitMenus = g.Where(i => i.LIMIT_TYPE == LimitRoleTypeEnum.LIMIT.ToIntStr())
                                                   .SelectMany(i => i.LIMIT_MENU_SID.IsNullOrEmpty() ? Array.Empty<string>() : i.LIMIT_MENU_SID.Split('+'))
                                                   .Distinct()
                                                   .ToList();
                        limitItem.LIMIT_MENU_SID = string.Join("+", limitMenus);
                        limitItem.REMARK = "规评限权";
                    }
                    else
                    {
                        limitItem.REMARK = "规评停岗";
                    }
                    newLimitItems.Add(limitItem);
                });

            if (newLogList.Any())
                _soa.Db.Insertable(newLogList).PageSize(500).ExecuteCommand();
            if (oldLogList.Any())
                _soa.Db.Updateable(oldLogList).UpdateColumns(a => new { a.IF_CHANGE_LIMIT, a.LAST_MPERSON, a.LAST_MTIME }).PageSize(500).ExecuteCommand();
            if (newLimitItems.Any())
                _soa.Db.Insertable(newLimitItems).PageSize(500).ExecuteCommand();
            if (oldUserProleLimits.Any())
                _soa.Db.Deleteable(oldUserProleLimits).PageSize(500).ExecuteCommand();

            //系统数据要求填入的相关日志表
            ResolveSys6UserPostRoleLogs(guid, now, userIds);
            ResolveSys6UserPowerLogs(guid, now, oldUserProleLimits, newLimitItems);
            //定时删除中间数据
            DeleteHistoryEvaluateEventDispose();
            DeleteHistoryEvaluateEvent();

            return true;
        }

        private void ResolveSys6UserPostRoleLogs(string uid, DateTime now, List<string>? userIds)
        {
            List<SYS6_USER_POSTROLE_LOG> postLogs_inserts = new List<SYS6_USER_POSTROLE_LOG>();
            List<SYS6_USER_POSTROLE_LOG> postLogs_updates = new List<SYS6_USER_POSTROLE_LOG>();


            //处理用户岗位调整记录表
            //停岗 - 如有进行中的启用记录：结束启用记录;
            var runningLogs = _soa.Db.Queryable<SYS6_USER_PROLE_LIMIT>()
                .InnerJoin<SYS6_USER_POSTROLE_LOG>((limit, log) => log.USER_NO == limit.USER_NO && log.POSTROLE_ID == limit.POSTROLE_ID && log.UPOST_END_DATE == null && log.OPERATE_TYPE == "规评启用岗位")
                .Where((limit, log) => limit.LIMIT_TYPE == "2")
                .WhereIF(userIds != null && userIds.Any(), (limit, log) => userIds.Contains(limit.USER_NO))
                .Select((limit, log) => log)
                .ToList();

            runningLogs.Distinct().ForEach(log =>
            {
                log.UPOST_END_DATE = now;
                log.LAST_MPERSON = "H81规评事件处理程序";
                log.LAST_MTIME = now;
                postLogs_updates.Add(log);
            });

            //停岗 - 如没有进行中的停岗记录，增加停岗记录
            var newStopLogs = _soa.Db.Queryable<SYS6_USER_PROLE_LIMIT>()
             .LeftJoin<SYS6_USER_POSTROLE_LOG>((limit, log) => log.USER_NO == limit.USER_NO && log.POSTROLE_ID == limit.POSTROLE_ID && log.UPOST_END_DATE == null && log.OPERATE_TYPE == "规评停岗")
             .Where((limit, log) => limit.LIMIT_TYPE == "2" && log.UPOST_LOG_ID == null)
             .WhereIF(userIds != null && userIds.Any(), (limit, log) => userIds.Contains(limit.USER_NO))
             .Select((limit, log) => limit)
             .ToList();

            newStopLogs.DistinctBy(a => new { a.USER_NO, a.POSTROLE_ID }).ForEach(limit =>
            {
                postLogs_inserts.Add(
                        new SYS6_USER_POSTROLE_LOG
                        {
                            UPOST_LOG_ID = IDGenHelper.CreateGuid(),
                            HOSPITAL_ID = limit.HOSPITAL_ID,
                            USER_NO = limit.USER_NO,
                            POSTROLE_ID = limit.POSTROLE_ID,
                            UPOST_START_DATE = now,
                            UPOST_END_DATE = null,
                            CHANGE_PERSON = limit.FIRST_RPERSON,
                            CHANGE_TIME = now,
                            OPERATE_TYPE = "规评停岗",
                            FIRST_RPERSON = limit.FIRST_RPERSON,
                            FIRST_RTIME = DateTime.Now,
                            REMARK = $"EVENT_HANDLE_UID:{uid}"
                        });
            });

            //处理用户岗位调整记录表
            //不停岗 - 如果有进行中的停岗记录：结束停岗记录，同时，如果没有进行中的启用记录：增加启用记录
            var oldStops = _soa.Db.Queryable<SYS6_USER_POSTROLE_LOG>()
                 .LeftJoin<SYS6_USER_PROLE_LIMIT>((stopLog, limit) => stopLog.USER_NO == limit.USER_NO && stopLog.POSTROLE_ID == limit.POSTROLE_ID && limit.LIMIT_TYPE == "2")
                 .Where((stopLog, limit) => stopLog.UPOST_END_DATE == null && stopLog.OPERATE_TYPE == "规评停岗" && limit.USER_LIMIT_ID == null)
                 .WhereIF(userIds != null && userIds.Any(), (stopLog, limit) => userIds.Contains(stopLog.USER_NO))
                 .Select((stopLog, limit) => stopLog)
                 .ToList();

            //结束停岗记录
            oldStops.GroupBy(a => new { a.USER_NO, a.POSTROLE_ID }).ForEach(postRole =>
            {
                postRole.Distinct().ForEach(log =>
                {
                    log.UPOST_END_DATE = now;
                    log.LAST_MPERSON = "H81规评事件处理程序";
                    log.LAST_MTIME = now;
                    postLogs_updates.Add(log);
                });

                postLogs_inserts.Add(
                       new SYS6_USER_POSTROLE_LOG
                       {
                           UPOST_LOG_ID = IDGenHelper.CreateGuid(),
                           HOSPITAL_ID = postRole.First().HOSPITAL_ID,
                           USER_NO = postRole.First().USER_NO,
                           POSTROLE_ID = postRole.First().POSTROLE_ID,
                           UPOST_START_DATE = now,
                           UPOST_END_DATE = null,
                           CHANGE_PERSON = "H81规评事件处理程序",
                           CHANGE_TIME = now,
                           OPERATE_TYPE = "规评启用岗位",
                           FIRST_RPERSON = "H81规评事件处理程序",
                           FIRST_RTIME = now,
                           REMARK = $"EVENT_HANDLE_UID:{uid}"
                       });
            });

            if (postLogs_updates.Any())
                _soa.Db.Updateable(postLogs_updates).ExecuteCommand();
            if (postLogs_inserts.Any())
                _soa.Db.Insertable(postLogs_inserts).ExecuteCommand();
        }

        private void ResolveSys6UserPowerLogs(string uid, DateTime now, List<SYS6_USER_PROLE_LIMIT> oldUserProleLimits, List<SYS6_USER_PROLE_LIMIT> newUserProleLimits)
        {
            //限权分三种：
            //1、新增限权
            //2、限权变化
            //3、限权不变
            //4、取消限权
            List<SYS6_USER_POWER_LOG> powerLogs_inserts = new List<SYS6_USER_POWER_LOG>();
            var addLimits = new List<SYS6_USER_PROLE_LIMIT>();
            var delLimits = new List<SYS6_USER_PROLE_LIMIT>();

            var userRoles = oldUserProleLimits.Union(newUserProleLimits)
                .Where(a => a.LIMIT_TYPE == LimitRoleTypeEnum.LIMIT.ToIntStr())
                .Select(a => new { a.USER_NO, a.POSTROLE_ID })
                .Distinct()
                .ToList();

            foreach (var userRole in userRoles)
            {
                var newLimits = newUserProleLimits.Find(a => a.USER_NO == userRole.USER_NO && a.POSTROLE_ID == userRole.POSTROLE_ID && a.LIMIT_TYPE == LimitRoleTypeEnum.LIMIT.ToIntStr());
                var oldLimits = oldUserProleLimits.Find(a => a.USER_NO == userRole.USER_NO && a.POSTROLE_ID == userRole.POSTROLE_ID && a.LIMIT_TYPE == LimitRoleTypeEnum.LIMIT.ToIntStr());
                //新增限权/限权变化
                if (newLimits != null && (oldLimits == null || newLimits.LIMIT_MENU_SID != oldLimits.LIMIT_MENU_SID))
                    addLimits.Add(newLimits);
                //取消限权
                if (newLimits == null && oldLimits != null)
                    delLimits.Add(oldLimits);
            }

            var postRoleIds = addLimits.Union(delLimits).Select(a => a.POSTROLE_ID).Distinct().ToList();

            var postModules = _soa.Db.Queryable<SYS6_POST_ROLE_COM>()
              .InnerJoin<SYS6_ROLE_COM_LIST>((roleCom, comList) => comList.ROLECOM_ID == roleCom.ROLECOM_ID && comList.ROLE_STATE == "1")
              .Where((roleCom, comList) => postRoleIds.Contains(roleCom.POST_ID))
              .Select((roleCom, comList) => new { roleCom.POST_ID, comList.MODULE_ID })
              .Distinct()
              .ToList();

            foreach (var add in addLimits)
            {
                var modules = postModules.FindAll(a => a.POST_ID == add.POSTROLE_ID);
                foreach (var module in modules)
                {
                    powerLogs_inserts.Add(
                        new SYS6_USER_POWER_LOG
                        {
                            UPOWE_ID = IDGenHelper.CreateGuid(),
                            HOSPITAL_ID = add.HOSPITAL_ID,
                            USER_SNO = add.USER_NO,
                            POST_ID = add.POSTROLE_ID,  //岗位ID还是岗位角色ID?
                            MODULE_ID = module.MODULE_ID,
                            //ROLE_NO = add.rol, //没法弄
                            CHANGE_POWER = add.LIMIT_MENU_SID,
                            CHANGE_TIME = now,
                            OPERATE_TYPE = "规评限权",
                            CHANGE_PERSON = add.FIRST_RPERSON,
                            FIRST_RPERSON = add.FIRST_RPERSON,
                            FIRST_RTIME = now,
                            LAST_MPERSON = add.FIRST_RPERSON,
                            LAST_MTIME = now,
                            REMARK = $"EVENT_HANDLE_UID:{uid}"
                        });
                }
            }

            foreach (var del in delLimits)
            {
                var modules = postModules.FindAll(a => a.POST_ID == del.POSTROLE_ID);
                foreach (var module in modules)
                {
                    powerLogs_inserts.Add(
                        new SYS6_USER_POWER_LOG
                        {
                            UPOWE_ID = IDGenHelper.CreateGuid(),
                            HOSPITAL_ID = del.HOSPITAL_ID,
                            USER_SNO = del.USER_NO,
                            POST_ID = del.POSTROLE_ID,  //岗位ID还是岗位角色ID?
                            MODULE_ID = module.MODULE_ID,
                            //ROLE_NO = del.rol, //没法弄
                            CHANGE_POWER = del.LIMIT_MENU_SID,
                            CHANGE_TIME = now,
                            OPERATE_TYPE = "规评启用权限",
                            CHANGE_PERSON = del.FIRST_RPERSON,
                            FIRST_RPERSON = del.FIRST_RPERSON,
                            FIRST_RTIME = now,
                            LAST_MPERSON = del.FIRST_RPERSON,
                            LAST_MTIME = now,
                            REMARK = $"EVENT_HANDLE_UID:{uid}"
                        });
                }
            }

            if (powerLogs_inserts.Any())
                _soa.Db.Insertable(powerLogs_inserts).ExecuteCommand();
        }

        private void DeleteHistoryEvaluateEventDispose()
        {
            var historyTime = DateTime.Now.AddDays(-14);
            _soa.Db.Deleteable<OA_EVALUATE_EVENT_DISPOSE>().Where(a => a.LAST_MTIME < historyTime).ExecuteCommand();
        }
        private void DeleteHistoryEvaluateEvent()
        {
            var historyTime = DateTime.Now.AddDays(-30);
            _soa.Db.Deleteable<OA_EVALUATE_PLAN_EVENT>().Where(a => a.LAST_MTIME < historyTime).ExecuteCommand();
        }
        #endregion
    }
}
