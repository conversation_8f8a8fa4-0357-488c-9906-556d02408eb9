using AutoMapper;
using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using NPOI.Util;
using Serilog;
using Spire.Doc;
using Spire.Doc.Documents;
using Spire.Doc.Fields;
using SqlSugar;
using System.Net;
using XH.H81.Base.Helper;
using XH.H81.IServices;
using XH.H81.IServices.Pms;
using XH.H81.Models;
using XH.H81.Models.Dtos;
using XH.H81.Models.Dtos.Pms;
using XH.H81.Models.Dtos.Template;
using XH.H81.Models.Entities.Pms;
using XH.H81.Models.ExternalEntity;
using XH.H81.Models.SugarDbContext;
using XH.LAB.UTILS.Helpers;
using XH.LAB.UTILS.Models;

namespace XH.H81.Services
{
    /// <summary>
    /// 设置分类 操作工具箱数据库
    /// </summary>
    public class PageSettingService : IPageSettingService
    {
        private readonly ISqlSugarUow<SugarDbContext_Master> _soa;
        private readonly IMapper _mapper;
        private readonly IBaseDataServices _IBaseDataServices;
        private readonly IHostingEnvironment _hostingEnvironment;
        private readonly IConfiguration _configuration;
        private readonly IModuleLabGroupService _IModuleLabGroupService;
        private readonly ISystemService _systemService;
        private readonly ILogger<PageSettingService> _logger;
        private readonly IHttpContextAccessor _httpContext;
        private readonly string DATE_EXPIRED_KEY = "DATE_EXPIRED_FIELDS";
        private readonly string LIST_STATICS_KEY = "LIST_STATICS_KEY";
        private readonly string BASIC_DATA_KEY = "BASIC_DATA_KEY";
        public PageSettingService(ISqlSugarUow<SugarDbContext_Master> suow, IMapper mapper, IBaseDataServices iBaseDataServices,
            IHostingEnvironment hostingEnvironment, IModuleLabGroupService iModuleLabGroupService, ISystemService systemService, IConfiguration configuration, ILogger<PageSettingService> logger, IHttpContextAccessor httpContext)
        {
            _soa = suow;
            _mapper = mapper;
            _IBaseDataServices = iBaseDataServices;
            _hostingEnvironment = hostingEnvironment;
            _IModuleLabGroupService = iModuleLabGroupService;
            _systemService = systemService;
            _configuration = configuration;
            _logger = logger;
            _httpContext = httpContext;
            ExecutingChangeSqlHelper.ExecutingChangeSql(_soa, configuration);
        }


        /// <summary>
        /// 获取分类数据
        /// </summary>
        /// <param name="hospital_id"></param>
        /// <returns></returns>
        public ResultDto GetClassInfo(string hospital_id)
        {
            ResultDto resultDto = new ResultDto();

            var varMoudle = _soa.Db.Queryable<SYS6_MODULE_FUNC_DICT>()
                .Where(p => p.HOSPITAL_ID == hospital_id && p.MODULE_ID == "H81" && p.SETUP_CLASS == "B05-52|B05-53")
                .Select(s => new
                {
                    SETUP_ID = s.SETUP_ID,
                    SETUP_CNAME = s.SETUP_CNAME,
                    SETUP_NAME = s.SETUP_NAME,
                    SETUP_STATE = s.SETUP_STATE,
                    SETUP_SORT = s.SETUP_SORT,
                    REMARK = s.REMARK,
                    FORM_JSON = s.FORM_JSON
                }).OrderBy(w => w.SETUP_SORT).ToList();

            List<SysSetUpInfoDto> list = new List<SysSetUpInfoDto>();
            List<SysSetUpInfoDto> listChild = new List<SysSetUpInfoDto>();
            List<PMS_ADDN_CLASS_INFO> listAddnClass = _IModuleLabGroupService.GetPmsAddnClassInfo(true);
            SysSetUpInfoDto healthRecord = null;  //健康档案
            #region 以OA分类表为准版

            //foreach (var aClass in listAddnClass)
            //{
            //    SysSetUpInfoDto sysSetUpInfoDto = new SysSetUpInfoDto();

            //    if (aClass.CLASS_ID == "PMS_HEALTH_DOC_LIST")
            //        healthRecord = sysSetUpInfoDto;
            //    sysSetUpInfoDto.IF_NEW = aClass.CLASS_TYPE;
            //    sysSetUpInfoDto.CLASS_KIND = aClass.CLASS_KIND;
            //    sysSetUpInfoDto.SMBL_FLAG = aClass.SMBL_FLAG;
            //    sysSetUpInfoDto.SMBL_REC_FLAG = aClass.SMBL_REC_FLAG;

            //    //原分类默认上传附件
            //    if (sysSetUpInfoDto.IF_NEW == "0")
            //    {
            //        sysSetUpInfoDto.IF_UPLOAD_FILE = "1";
            //        sysSetUpInfoDto.IS_HIDE = 0;
            //        sysSetUpInfoDto.IS_AUDITABLE = 1;
            //        sysSetUpInfoDto.IS_PROP_EDITABLE = 1;
            //    }
            //    else
            //    {
            //        sysSetUpInfoDto.IS_HIDE = aClass.IS_HIDE;
            //        sysSetUpInfoDto.IS_AUDITABLE = aClass.IS_AUDITABLE;
            //        sysSetUpInfoDto.IS_PROP_EDITABLE = aClass.IS_PROP_EDITABLE;
            //    }
            //    if (aClass.CLASS_ADDN_CONFIG.IsNotNullOrEmpty())
            //    {
            //        Dictionary<string, object> dict = JsonConvert.DeserializeObject<Dictionary<string, object>>(aClass.CLASS_ADDN_CONFIG);
            //        foreach (var item in dict)
            //        {
            //            if (item.Key == "IF_UPLOAD_FILE")
            //                sysSetUpInfoDto.IF_UPLOAD_FILE = item.Value?.ToString();
            //        }
            //    }
            //    if (sysSetUpInfoDto.CLASS_KIND == "1")
            //        listChild.Add(sysSetUpInfoDto);
            //    else if (aClass.CLASS_ID != "PMS_HEALTH_DOC_LIST")
            //        list.Add(sysSetUpInfoDto);


            //    int index = varMoudle.FindIndex(w => w.SETUP_ID == aClass.FORM_SETUP_ID);
            //    if (index > -1)
            //    {
            //        sysSetUpInfoDto.SETUP_CNAME = varMoudle[index].SETUP_CNAME;
            //        sysSetUpInfoDto.SETUP_STATE = varMoudle[index].SETUP_STATE;
            //        sysSetUpInfoDto.SETUP_ID = varMoudle[index].SETUP_ID;
            //        sysSetUpInfoDto.REMARK = varMoudle[index].REMARK;
            //        sysSetUpInfoDto.SETUP_SORT = varMoudle[index].SETUP_SORT;
            //    }
            //    else
            //    {
            //        sysSetUpInfoDto.SETUP_CNAME = aClass.CLASS_NAME;
            //        sysSetUpInfoDto.SETUP_STATE = aClass.CLASS_STATE;
            //        sysSetUpInfoDto.REMARK = aClass.REMARK;
            //    }
            //}
            #endregion
            foreach (var moudle in varMoudle)
            {
                SysSetUpInfoDto sysSetUpInfoDto = new SysSetUpInfoDto();
                sysSetUpInfoDto.SETUP_CNAME = moudle.SETUP_CNAME;
                sysSetUpInfoDto.SETUP_STATE = moudle.SETUP_STATE;
                sysSetUpInfoDto.SETUP_ID = moudle.SETUP_ID;
                sysSetUpInfoDto.REMARK = moudle.REMARK;
                sysSetUpInfoDto.SETUP_SORT = moudle.SETUP_SORT;
                int index = listAddnClass.FindIndex(w => w.FORM_SETUP_ID == moudle.SETUP_ID);
                if (index > -1)
                {
                    if (listAddnClass[index].CLASS_ID == "PMS_HEALTH_DOC_LIST")
                        healthRecord = sysSetUpInfoDto;
                    sysSetUpInfoDto.IF_NEW = listAddnClass[index].CLASS_TYPE;
                    sysSetUpInfoDto.CLASS_KIND = listAddnClass[index].CLASS_KIND;
                    sysSetUpInfoDto.SMBL_FLAG = listAddnClass[index].SMBL_FLAG;
                    sysSetUpInfoDto.SMBL_REC_FLAG = listAddnClass[index].SMBL_REC_FLAG;
                    //原分类默认上传附件
                    if (sysSetUpInfoDto.IF_NEW == "0")
                    {
                        sysSetUpInfoDto.IF_UPLOAD_FILE = "1";
                        sysSetUpInfoDto.IS_HIDE = 0;
                        sysSetUpInfoDto.IS_AUDITABLE = 1;
                        sysSetUpInfoDto.IS_PROP_EDITABLE = 1;
                    }
                    else
                    {
                        sysSetUpInfoDto.IS_HIDE = listAddnClass[index].IS_HIDE;
                        sysSetUpInfoDto.IS_AUDITABLE = listAddnClass[index].IS_AUDITABLE;
                        sysSetUpInfoDto.IS_PROP_EDITABLE = listAddnClass[index].IS_PROP_EDITABLE;
                    }
                    if (listAddnClass[index].CLASS_ADDN_CONFIG.IsNotNullOrEmpty())
                    {
                        Dictionary<string, object> dict = JsonConvert.DeserializeObject<Dictionary<string, object>>(listAddnClass[index].CLASS_ADDN_CONFIG);
                        foreach (var item in dict)
                        {
                            if (item.Key == "IF_UPLOAD_FILE")
                                sysSetUpInfoDto.IF_UPLOAD_FILE = item.Value != null && item.Value.ToString() == "1" ? "1" : "0";
                            if (item.Key == "IF_MUST_UPLOAD_FILE")
                                sysSetUpInfoDto.IF_MUST_UPLOAD_FILE = item.Value != null && item.Value.ToString() == "1" ? "1" : "0";
                        }
                    }
                    if (sysSetUpInfoDto.CLASS_KIND == "1")
                        listChild.Add(sysSetUpInfoDto);
                    else if (listAddnClass[index].CLASS_ID != "PMS_HEALTH_DOC_LIST")
                        list.Add(sysSetUpInfoDto);
                }
            }
            list = list.OrderBy(w => w.SETUP_SORT).ToList();
            if (healthRecord != null)
            {
                healthRecord.CHILD = listChild;
                list.Insert(0, healthRecord);
            }
            resultDto.data = list;
            return resultDto;
        }

        public ResultDto GetClassTableInfo(string hospital_id)
        {

            ResultDto resultDto = new ResultDto();
            var varMoudle = _soa.Db.Queryable<SYS6_MODULE_FUNC_DICT>().Where(p => p.HOSPITAL_ID == hospital_id && p.MODULE_ID == "H81" && p.SETUP_CLASS == "B05-52|B05-53")
               .Select(s => new
               {
                   SETUP_ID = s.SETUP_ID,
                   SETUP_CNAME = s.SETUP_CNAME,
                   SETUP_NAME = s.SETUP_NAME,
                   SETUP_STATE = s.SETUP_STATE,
                   SETUP_SORT = s.SETUP_SORT,
                   REMARK = s.REMARK,
                   FORM_JSON = s.FORM_JSON
               }).OrderBy(w => w.SETUP_SORT).ToList();
            List<SysSetUpInfoDto> list = new List<SysSetUpInfoDto>();
            List<SysSetUpInfoDto> listChild = new List<SysSetUpInfoDto>();
            List<PMS_ADDN_CLASS_INFO> listAddnClass = _IModuleLabGroupService.GetPmsAddnClassInfo(true);
            foreach (var moudle in varMoudle)
            {
                SysSetUpInfoDto sysSetUpInfoDto = new SysSetUpInfoDto();
                sysSetUpInfoDto.SETUP_CNAME = moudle.SETUP_CNAME;
                sysSetUpInfoDto.SETUP_STATE = moudle.SETUP_STATE;
                sysSetUpInfoDto.SETUP_ID = moudle.SETUP_ID;
                sysSetUpInfoDto.REMARK = moudle.REMARK;
                sysSetUpInfoDto.SETUP_SORT = moudle.SETUP_SORT;
                int index = listAddnClass.FindIndex(w => w.FORM_SETUP_ID == moudle.SETUP_ID);
                if (index > -1)
                {
                    sysSetUpInfoDto.IF_NEW = listAddnClass[index].CLASS_TYPE;
                    sysSetUpInfoDto.CLASS_KIND = listAddnClass[index].CLASS_KIND;
                    sysSetUpInfoDto.SMBL_FLAG = listAddnClass[index].SMBL_FLAG;
                    sysSetUpInfoDto.SMBL_REC_FLAG = listAddnClass[index].SMBL_REC_FLAG;
                    //原分类默认上传附件
                    if (sysSetUpInfoDto.IF_NEW == "0")
                    {
                        sysSetUpInfoDto.IF_UPLOAD_FILE = "1";
                        sysSetUpInfoDto.IS_HIDE = 0;
                        sysSetUpInfoDto.IS_AUDITABLE = 1;
                        sysSetUpInfoDto.IS_PROP_EDITABLE = 1;
                    }
                    else
                    {
                        sysSetUpInfoDto.IS_HIDE = listAddnClass[index].IS_HIDE;
                        sysSetUpInfoDto.IS_AUDITABLE = listAddnClass[index].IS_AUDITABLE;
                        sysSetUpInfoDto.IS_PROP_EDITABLE = listAddnClass[index].IS_PROP_EDITABLE;
                    }
                    if (listAddnClass[index].CLASS_ADDN_CONFIG.IsNotNullOrEmpty())
                    {
                        Dictionary<string, object> dict = JsonConvert.DeserializeObject<Dictionary<string, object>>(listAddnClass[index].CLASS_ADDN_CONFIG);
                        foreach (var item in dict)
                        {
                            if (item.Key == "IF_UPLOAD_FILE")
                                sysSetUpInfoDto.IF_UPLOAD_FILE = item.Value != null && item.Value.ToString() == "1" ? "1" : "0";
                            if (item.Key == "IF_MUST_UPLOAD_FILE")
                                sysSetUpInfoDto.IF_MUST_UPLOAD_FILE = item.Value != null && item.Value.ToString() == "1" ? "1" : "0";
                        }
                    }
                    if (sysSetUpInfoDto.CLASS_KIND == "1")
                        sysSetUpInfoDto.CLASS_KIND_NAME = "健康档案";
                    list.Add(sysSetUpInfoDto);
                }
            }
            list = list.OrderBy(w => w.CLASS_KIND).ThenBy(w => w.SETUP_SORT).ToList();
            resultDto.data = list;
            return resultDto;
        }


        /// <summary>
        /// 获取分类属性数据
        /// </summary>
        /// <param name="setUpId"></param>
        /// <returns></returns>
        public ResultDto GetClassAttributeInfo(string setUpId, string hospitalId)
        {
            ResultDto resultDto = new ResultDto();
            var varMoudle = _soa.Db.Queryable<SYS6_MODULE_FUNC_DICT>().Where(p => p.SETUP_ID == setUpId && p.HOSPITAL_ID == hospitalId)
    .Select(s => new
    {
        SETUP_ID = s.SETUP_ID,
        SETUP_CNAME = s.SETUP_CNAME,
        SETUP_NAME = s.SETUP_NAME,
        SETUP_STATE = s.SETUP_STATE,
        REMARK = s.REMARK,
        FORM_JSON = s.FORM_JSON
    }).First();
            List<SysClassAttributeDto> list = new List<SysClassAttributeDto>();
            if (varMoudle != null && varMoudle.FORM_JSON != null)
            {
                PageSettingForm PageSettingForm = null;
                try
                {
                    PageSettingForm = JsonConvert.DeserializeObject<PageSettingForm>(varMoudle.FORM_JSON);
                }
                catch (Exception ex)
                {
                    Log.Error($"调用GetClassAttributeInfo方法报错,PageSettingForm解释失败：{ex}\r\nFORM_JSON的值为：{varMoudle.FORM_JSON}");
                }
                PMS_ADDN_CLASS_INFO addnClass = _IModuleLabGroupService.GetPmsAddnClassInfo().Where(p => p.FORM_SETUP_ID == setUpId/* && p.HOSPITAL_ID == hospitalId*/)?.FirstOrDefault();
                if (PageSettingForm != null && PageSettingForm.form != null)
                {
                    Dictionary<string, object> config = new Dictionary<string, object>();
                    if (addnClass != null && addnClass.CLASS_ADDN_CONFIG.IsNotNullOrEmpty())
                    {
                        try
                        {
                            config = JsonConvert.DeserializeObject<Dictionary<string, object>>(addnClass.CLASS_ADDN_CONFIG);
                        }
                        catch (Exception ex)
                        {
                            Log.Error($"调用GetClassAttributeInfo方法报错,config解释失败：{ex}\r\nCLASS_ADDN_CONFIG的值为：{addnClass.CLASS_ADDN_CONFIG}");
                        }
                    }
                    foreach (var item in PageSettingForm.form)
                    {
                        SysClassAttributeDto sysClassAttributeDto = new SysClassAttributeDto();
                        sysClassAttributeDto.formName = item.formName;
                        sysClassAttributeDto.formCode = item.formCode;
                        sysClassAttributeDto.ifShow = item.ifShow;
                        sysClassAttributeDto.ifNew = item.ifNew;
                        sysClassAttributeDto.sort = item.sort;
                        sysClassAttributeDto.dataClass = item.dataClass;
                        sysClassAttributeDto.dataType = item.dataType;
                        sysClassAttributeDto.ifRequired = item.ifRequired;
                        sysClassAttributeDto.SETUP_ID = varMoudle.SETUP_ID;
                        if (sysClassAttributeDto.formCode.IsNotNullOrEmpty() && config.ContainsKey(sysClassAttributeDto.formCode))
                            sysClassAttributeDto.remark = config[sysClassAttributeDto.formCode]?.ToString();
                        if (sysClassAttributeDto.formCode.IsNotNullOrEmpty() && config.ContainsKey(BASIC_DATA_KEY))
                            sysClassAttributeDto.ifBasicData = (bool)(config[BASIC_DATA_KEY]?.ToString().Contains(sysClassAttributeDto.formCode)) ? true : false;
                        if (sysClassAttributeDto.formCode.IsNotNullOrEmpty() && config.ContainsKey(LIST_STATICS_KEY))
                            sysClassAttributeDto.ifListStatistics = (bool)(config[LIST_STATICS_KEY]?.ToString().Contains(sysClassAttributeDto.formCode)) ? true : false;
                        if (sysClassAttributeDto.ifNew != null && !sysClassAttributeDto.ifNew.Value)
                            sysClassAttributeDto.ifListStatistics = true;
                        if (sysClassAttributeDto.formCode.IsNotNullOrEmpty() && config.ContainsKey(DATE_EXPIRED_KEY))
                            sysClassAttributeDto.ifCheckExpired = (bool)(config[DATE_EXPIRED_KEY]?.ToString().Contains(sysClassAttributeDto.formCode)) ? true : false;
                        list.Add(sysClassAttributeDto);
                    }
                }
            }
            list = list.OrderBy(x => x.sort).ToList();
            resultDto.data = list;
            return resultDto;
        }

        /// <summary>
        /// 保存分类信息 需要同时插入工具箱表格和表单属性信息
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="claims"></param>
        /// <returns></returns>
        public ResultDto SaveClassInfo(SysSetUpInfoDto dto, H.Utility.ClaimsDto claims)
        {
            string smblFlag = _httpContext.GetSmblFlag();
            var varMoudle = _soa.Db.Queryable<SYS6_MODULE_FUNC_DICT>().Where(p => p.HOSPITAL_ID == claims.HOSPITAL_ID && p.MODULE_ID == "H81")
            .Select(s => new SYS6_MODULE_FUNC_DICT()
            {
                SETUP_ID = s.SETUP_ID,
                SETUP_CNAME = s.SETUP_CNAME,
                SETUP_NAME = s.SETUP_NAME,
                SETUP_CLASS = s.SETUP_CLASS
            }).ToList();
            var resultDto = new ResultDto();
            if (varMoudle.FindIndex(w => w.SETUP_CNAME == dto.SETUP_CNAME && w.SETUP_CLASS == "B05-52|B05-53") > -1 && dto.SETUP_ID.IsNullOrEmpty())
            {
                resultDto.msg = $"分类名称:{dto.SETUP_CNAME}已存在，请勿重复添加";
                resultDto.success = false;
                return resultDto;
            }
            if (dto.SETUP_ID.IsNotNullOrEmpty())
            {
                int index = varMoudle.FindIndex(w => w.SETUP_ID == dto.SETUP_ID);
                if (index > -1)
                {
                    varMoudle.RemoveAt(index);
                    if (varMoudle.FindIndex(w => w.SETUP_CNAME == dto.SETUP_CNAME && w.SETUP_CLASS == "B05-52|B05-53") > -1)
                    {
                        resultDto.msg = $"分类名称:{dto.SETUP_CNAME}已存在，请勿修改";
                        resultDto.success = false;
                        return resultDto;
                    }
                }
            }
            //生成主键处理
            List<string> listSetUpId = varMoudle.Select(w => w.SETUP_ID).ToList().FindAll(w => w.Contains("H81"));
            List<int> listIntSetUpId = new List<int>();
            foreach (var item in listSetUpId)
            {
                string splitRight = item.Split("H81")[1];
                int sort = 0;
                if (splitRight.Contains("CT"))
                {
                    string splitLeft = splitRight.Split("CT")[0];
                    int.TryParse(splitLeft, out sort);
                    listIntSetUpId.Add(sort);
                }
                else
                {
                    int.TryParse(splitRight, out sort);
                    listIntSetUpId.Add(sort);
                }
            }
            using (_soa.Begin())
            {
                //    //新增需同时插入表单和表格信息到工具箱
                if (dto.SETUP_ID.IsNullOrEmpty())
                {
                    List<SYS6_MODULE_FUNC_DICT> listAdd = new List<SYS6_MODULE_FUNC_DICT>();
                    dto.SETUP_ID = "H810" + (listIntSetUpId.Max() + 1).ToString();
                    //表单
                    SYS6_MODULE_FUNC_DICT formDict = new SYS6_MODULE_FUNC_DICT();
                    formDict.SETUP_ID = dto.SETUP_ID;
                    formDict.FUNC_ID = "H81";
                    formDict.MODULE_ID = "H81";
                    formDict.SETUP_NAME = dto.SETUP_CNAME;
                    formDict.SETUP_CNAME = dto.SETUP_CNAME;
                    formDict.SETUP_CLASS = "B05-52|B05-53";
                    formDict.SETUP_SORT = dto.SETUP_ID;
                    formDict.SETUP_STATE = dto.SETUP_STATE;
                    formDict.FIRST_RPERSON = claims.USER_NAME;
                    formDict.FIRST_RTIME = DateTime.Now;
                    formDict.HOSPITAL_ID = claims.HOSPITAL_ID;
                    formDict.REMARK = dto.REMARK;
                    //排序号需要默认新增
                    PageSettingForm pageSetting = new PageSettingForm();
                    pageSetting.form = new List<Form>();
                    Form form = new Form();
                    form.formCname = "排序号";
                    form.formName = "排序号";
                    form.formCode = "SORT_NUM";
                    form.ifShow = true;
                    form.ifNew = false;
                    form.titleShow = true;
                    form.titleColor = "#000000";
                    form.titleSize = 13;
                    form.titleStyle = "0";
                    form.titleAlign = "3";
                    form.contentLine = 1;
                    form.contentMaxLine = 1;
                    form.contentHeightClass = "1";
                    form.contentHeightRatio = "";
                    form.contentAlign = "1";
                    form.contentFontSize = 13;
                    form.contentStyle = "0";
                    form.titleAndContentType = "1";
                    form.contentEnlarge = false;
                    form.ifRequired = true;
                    form.replaceField = true;
                    form.onlyRead = "1";
                    form.dataType = "1";
                    form.allowMaintainDropDownData = false;
                    form.editeState = false;
                    form.unitFlag = false;
                    pageSetting.form.Add(form);
                    formDict.FORM_JSON = JsonConvert.SerializeObject(pageSetting);
                    listAdd.Add(formDict);
                    //表格
                    SYS6_MODULE_FUNC_DICT tableDict = _mapper.Map<SYS6_MODULE_FUNC_DICT>(formDict).Copy();
                    tableDict.SETUP_CNAME = tableDict.SETUP_CNAME + "信息";
                    tableDict.SETUP_NAME = tableDict.SETUP_NAME + "信息";
                    tableDict.SETUP_ID = formDict.SETUP_ID + "CT";
                    tableDict.SETUP_CLASS = "B05-41|B05-42";
                    tableDict.SETUP_SORT = "";
                    tableDict.FORM_JSON = null;
                    tableDict.REMARK = dto.REMARK;
                    List<PageSettingTableCol> settingTableCols = new List<PageSettingTableCol>();
                    PageSettingTableCol pageSettingTableCol = new PageSettingTableCol();
                    pageSettingTableCol.headerName = "排序号";
                    pageSettingTableCol.field = "SORT_NUM";
                    pageSettingTableCol.hide = true;
                    pageSettingTableCol.sort = "0";
                    pageSettingTableCol.width = 100;
                    pageSettingTableCol.sortable = false;
                    settingTableCols.Add(pageSettingTableCol);
                    tableDict.FORM_COL_JSON = JsonConvert.SerializeObject(settingTableCols);
                    listAdd.Add(tableDict);
                    int sort = 0;
                    PMS_ADDN_CLASS_INFO pmsAdd = _IModuleLabGroupService.GetPmsAddnClassInfo()?.First();
                    int.TryParse(pmsAdd?.CLASS_SORT, out sort);
                    //分类映射信息
                    PMS_ADDN_CLASS_INFO addnClassInfo = new PMS_ADDN_CLASS_INFO();
                    addnClassInfo.CLASS_ID = _IBaseDataServices.GetTableMax("PMS_ADDN_CLASS_INFO", "CLASS_ID", 1, 1).data.ToString();
                    addnClassInfo.HOSPITAL_ID = claims.HOSPITAL_ID;
                    addnClassInfo.CLASS_TYPE = dto.IF_NEW;
                    addnClassInfo.CLASS_NAME = dto.SETUP_CNAME;
                    addnClassInfo.CLASS_SORT = dto.SETUP_SORT;
                    addnClassInfo.FORM_SETUP_ID = formDict.SETUP_ID;
                    addnClassInfo.TABLE_SETUP_ID = tableDict.SETUP_ID;
                    addnClassInfo.CLASS_TABLE_NAME = "PMS_ADDN_RECORD";
                    addnClassInfo.FIRST_RPERSON = claims.HIS_NAME;
                    addnClassInfo.FIRST_RTIME = DateTime.Now;
                    addnClassInfo.IS_HIDE = dto.IS_HIDE != null ? dto.IS_HIDE.Value : 0;
                    addnClassInfo.IS_PROP_EDITABLE = 1;
                    addnClassInfo.IS_AUDITABLE = 1;
                    addnClassInfo.CLASS_SORT = (sort + 1).ToString().PadLeft(5, '0');
                    addnClassInfo.CLASS_KIND = dto.CLASS_KIND;
                    addnClassInfo.SMBL_FLAG = dto.SMBL_FLAG;
                    addnClassInfo.SMBL_REC_FLAG = dto.SMBL_REC_FLAG;
                    addnClassInfo.SMBL_FLAG = smblFlag == "1" ? "1" : dto.SMBL_FLAG;
                    Dictionary<string, string> keyValuePairs = new Dictionary<string, string>();
                    keyValuePairs.Add("IF_UPLOAD_FILE", dto.IF_UPLOAD_FILE);
                    keyValuePairs.Add("IF_MUST_UPLOAD_FILE", dto.IF_MUST_UPLOAD_FILE);
                    addnClassInfo.CLASS_ADDN_CONFIG = JsonConvert.SerializeObject(keyValuePairs);
                    _soa.Db.Insertable(addnClassInfo).ExecuteCommand();
                    _soa.Db.Insertable(listAdd).ExecuteCommand();
                }
                else
                {
                    //更新也需要同步更新表单表格信息
                    List<SYS6_MODULE_FUNC_DICT> listUpdate = new List<SYS6_MODULE_FUNC_DICT>();
                    SYS6_MODULE_FUNC_DICT formDict = _soa.Db.Queryable<SYS6_MODULE_FUNC_DICT>().Where(p => p.SETUP_ID == dto.SETUP_ID && p.HOSPITAL_ID == claims.HOSPITAL_ID)?.First();
                    if (formDict != null)
                    {
                        formDict.SETUP_NAME = dto.SETUP_CNAME;
                        formDict.SETUP_CNAME = dto.SETUP_CNAME;
                        formDict.REMARK = dto.REMARK;
                        formDict.SETUP_SORT = dto.SETUP_SORT;
                        formDict.SETUP_STATE = dto.SETUP_STATE;
                        listUpdate.Add(formDict);
                    }

                    PMS_ADDN_CLASS_INFO addnClass = _IModuleLabGroupService.GetPmsAddnClassInfo(true).Where(p => p.FORM_SETUP_ID == dto.SETUP_ID /*&& p.HOSPITAL_ID == claims.HOSPITAL_ID*/)?.First();

                    if (addnClass != null)
                    {
                        addnClass.CLASS_NAME = dto.SETUP_CNAME;
                        addnClass.LAST_MPERSON = claims.HIS_NAME;
                        addnClass.LAST_TIME = DateTime.Now;
                        addnClass.CLASS_STATE = dto.SETUP_STATE;
                        addnClass.IS_HIDE = dto.IS_HIDE != null ? dto.IS_HIDE.Value : 0;
                        addnClass.CLASS_KIND = dto.CLASS_KIND;
                        addnClass.SMBL_FLAG = smblFlag == "1" ? "1" : dto.SMBL_FLAG;
                        addnClass.SMBL_REC_FLAG = dto.SMBL_REC_FLAG;
                        //addnClass.IS_PROP_EDITABLE = dto.IS_PROP_EDITABLE != null ? dto.IS_PROP_EDITABLE.Value : 1;
                        //addnClass.IS_AUDITABLE = dto.IS_AUDITABLE != null ? dto.IS_AUDITABLE.Value : 1;
                        Dictionary<string, string> keyValuePairs = new Dictionary<string, string>();
                        if (addnClass.CLASS_ADDN_CONFIG.IsNotNullOrEmpty())
                        {
                            try
                            {
                                keyValuePairs = JsonConvert.DeserializeObject<Dictionary<string, string>>(addnClass.CLASS_ADDN_CONFIG);
                            }
                            catch { }
                        }
                        keyValuePairs["IF_UPLOAD_FILE"] = dto.IF_UPLOAD_FILE;
                        keyValuePairs["IF_MUST_UPLOAD_FILE"] = dto.IF_MUST_UPLOAD_FILE;
                        addnClass.CLASS_ADDN_CONFIG = JsonConvert.SerializeObject(keyValuePairs);
                        _soa.Db.Updateable(addnClass).ExecuteCommand();
                        SYS6_MODULE_FUNC_DICT tableDict = _soa.Db.Queryable<SYS6_MODULE_FUNC_DICT>().Where(p => p.SETUP_ID == addnClass.TABLE_SETUP_ID && p.HOSPITAL_ID == claims.HOSPITAL_ID)?.First();
                        if (tableDict != null)
                        {
                            tableDict.SETUP_NAME = dto.SETUP_CNAME + "信息";
                            tableDict.SETUP_CNAME = dto.SETUP_CNAME + "信息";
                            tableDict.REMARK = dto.REMARK;
                            listUpdate.Add(tableDict);
                        }
                    }

                    _soa.Db.Updateable(listUpdate).ExecuteCommand();
                }

                _soa.SaveChanges();

            }
            return resultDto;
        }
        /// <summary>
        /// 保存分类属性信息 需要同步工具箱表格和表单属性信息
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="claims"></param>
        /// <returns></returns>
        public ResultDto SaveClassAttributeInfo(SysClassAttributeDto dto, H.Utility.ClaimsDto claims)
        {
            ResultDto resultDto = new ResultDto();
            try
            {
                List<SYS6_MODULE_FUNC_DICT> listUpdate = new List<SYS6_MODULE_FUNC_DICT>();
                //表单
                SYS6_MODULE_FUNC_DICT formDict = _soa.Db.Queryable<SYS6_MODULE_FUNC_DICT>().Where(p => p.SETUP_ID == dto.SETUP_ID && p.HOSPITAL_ID == claims.HOSPITAL_ID)?.First();
                PMS_ADDN_CLASS_INFO addnClass = _IModuleLabGroupService.GetPmsAddnClassInfo().Where(p => p.FORM_SETUP_ID == dto.SETUP_ID /*&& p.HOSPITAL_ID == claims.HOSPITAL_ID*/)?.First();
                Form form = new Form();
                OA_FIELD_DICT filedDictAdd = null;
                OA_FIELD_DICT filedDictDelete = null;
                OA_FIELD_DICT filedDictUpdate = null;
                if (formDict != null)
                {
                    PageSettingForm PageSettingForm = new PageSettingForm();
                    if (formDict.FORM_JSON != null)
                    {
                        PageSettingForm = JsonConvert.DeserializeObject<PageSettingForm>(formDict.FORM_JSON);
                        if (PageSettingForm?.form?.FindIndex(w => w.formName == dto.formName) > -1 && dto.formCode.IsNullOrEmpty())
                        {
                            resultDto.msg = $"分类属性名称:{dto.formName}已存在，请勿重复添加";
                            resultDto.success = false;
                            return resultDto;
                        }
                        if (dto.formCode.IsNotNullOrEmpty())
                        {
                            PageSettingForm PageSettingClone = PageSettingForm.Copy();
                            int index = PageSettingClone.form.FindIndex(w => w.formCode == dto.formCode);
                            if (index > -1)
                            {
                                PageSettingClone.form.RemoveAt(index);
                                if (PageSettingClone.form.FindIndex(w => w.formName == dto.formName) > -1)
                                {
                                    resultDto.msg = $"分类属性名称:{dto.formName}已存在，请勿修改";
                                    resultDto.success = false;
                                    return resultDto;
                                }
                            }
                        }
                    }
                    Random random = new Random();

                    form.formCname = dto.formName;
                    form.formName = dto.formName;
                    form.formCode = new SpellAndWbCodeTookit().GetSpellCode(form.formName) + random.Next(10000, 99999);
                    form.ifShow = dto.ifShow;
                    form.ifNew = dto.ifNew;
                    form.titleShow = true;
                    form.titleColor = "#000000";
                    form.titleSize = 13;
                    form.titleStyle = "0";
                    form.titleAlign = "3";
                    form.contentLine = 1;
                    form.contentMaxLine = 1;
                    form.contentHeightClass = "1";
                    form.contentHeightRatio = "";
                    form.contentAlign = "1";
                    form.contentFontSize = 13;
                    form.contentStyle = "0";
                    form.titleAndContentType = "1";
                    form.contentEnlarge = false;
                    form.ifRequired = dto.ifRequired;
                    form.replaceField = true;
                    form.onlyRead = "1";
                    form.dataType = dto.dataType;
                    form.dataClass = dto.dataClass;
                    form.allowMaintainDropDownData = false;
                    form.editeState = false;
                    form.unitFlag = false;
                    //查询序号 
                    var varFiledDictSort = _soa.Db.Queryable<OA_FIELD_DICT>().Where(w => w.MODULE_ID == "H81").Select(w => w.FIELD_SORT).ToList();
                    List<int> listDictSort = new List<int>();
                    foreach (var item in varFiledDictSort)
                    {
                        string sort = item?.Split("H81")[1];
                        int intSort = 0;
                        int.TryParse(sort, out intSort);
                        listDictSort.Add(intSort);
                    }
                    //首次新增表单属性
                    if (PageSettingForm.form == null || PageSettingForm.form.Count <= 0)
                    {
                        form.sort = 1;
                        PageSettingForm.form = new List<Form>();
                        PageSettingForm.form.Add(form);
                        //同步OA_FIELD_DICT
                        filedDictAdd = new OA_FIELD_DICT();
                        filedDictAdd.FIELD_ID = IDGenHelper.CreateGuid();
                        filedDictAdd.MODULE_ID = "H81";
                        filedDictAdd.CLASSE_CODE = addnClass.CLASS_TABLE_NAME;
                        filedDictAdd.CLASSE_NAME = addnClass.CLASS_NAME;
                        filedDictAdd.FIELD_CODE = form.formCode;
                        filedDictAdd.FIELD_NAME = form.formName;
                        filedDictAdd.FIELD_DESC = form.formName;
                        filedDictAdd.FIELD_SORT = "H81" + (listDictSort.Max() + 1).ToString();
                        filedDictAdd.FIELD_STATE = "1";
                        filedDictAdd.FIELD_TYPE = "1";
                        filedDictAdd.FIRST_RPERSON = claims.HIS_NAME;
                        filedDictAdd.FIRST_RTIME = DateTime.Now;
                    }
                    else
                    {
                        //更新表单属性
                        int index = PageSettingForm.form.FindIndex(w => w.formCode == dto.formCode);
                        if (index > -1)
                        {
                            form = PageSettingForm.form[index];
                            //已录入数据，不允许修改数据类型
                            if (!dto.ifDelete && form.dataType != dto.dataType && CheckFeildExistsData(addnClass, form, "无法修改输入方式的数据类型", out var resultDto2))
                            {
                                return resultDto2;
                            }
                            form.formCname = dto.formName;
                            form.formName = dto.formName;
                            form.ifShow = dto.ifShow;
                            form.dataType = dto.dataType;
                            form.dataClass = dto.dataClass;
                            form.ifRequired = dto.ifRequired;
                            //  form.formCode = new SpellAndWbCodeTookit().GetSpellCode(form.formName);
                            if (dto.ifDelete)
                            {
                                //已录入数据，不允许删除分类属性
                                if (CheckFeildExistsData(addnClass, form, "无法删除该分类属性", out resultDto))
                                {
                                    return resultDto;
                                }
                                PageSettingForm.form.Remove(form);
                                filedDictDelete = _soa.Db.Queryable<OA_FIELD_DICT>().Where(p => p.MODULE_ID == "H81" && p.CLASSE_CODE == addnClass.CLASS_TABLE_NAME && p.FIELD_CODE == form.formCode && p.FIELD_STATE == "1")?.First();
                            }
                            else
                            {
                                filedDictUpdate = _soa.Db.Queryable<OA_FIELD_DICT>().Where(p => p.MODULE_ID == "H81" && p.CLASSE_CODE == addnClass.CLASS_TABLE_NAME && p.FIELD_CODE == form.formCode && p.FIELD_STATE == "1")?.First();
                                if (filedDictUpdate != null)
                                {
                                    filedDictUpdate.FIELD_CODE = form.formCode;
                                    filedDictUpdate.FIELD_NAME = form.formName;
                                    filedDictUpdate.FIELD_DESC = form.formName;
                                    filedDictUpdate.LAST_MPERSON = claims.HIS_NAME;
                                    filedDictUpdate.LAST_MTIME = DateTime.Now;
                                }
                                if (!dto.ifListStatistics)
                                    filedDictDelete = _soa.Db.Queryable<OA_FIELD_DICT>().Where(p => p.MODULE_ID == "H81" && p.CLASSE_CODE == addnClass.CLASS_TABLE_NAME && p.FIELD_CODE == form.formCode && p.FIELD_STATE == "1")?.First();
                            }
                        }
                        else
                        {
                            //插入表单属性
                            form.sort = PageSettingForm.form.Count + 1;
                            PageSettingForm.form.Add(form);
                            //同步OA_FIELD_DICT
                            filedDictAdd = new OA_FIELD_DICT();
                            filedDictAdd.FIELD_ID = IDGenHelper.CreateGuid();
                            filedDictAdd.MODULE_ID = "H81";
                            filedDictAdd.CLASSE_CODE = addnClass.CLASS_TABLE_NAME;
                            filedDictAdd.CLASSE_NAME = addnClass.CLASS_NAME;
                            filedDictAdd.FIELD_CODE = form.formCode;
                            filedDictAdd.FIELD_NAME = form.formName;
                            filedDictAdd.FIELD_DESC = form.formName;
                            filedDictAdd.FIELD_SORT = "H81" + (listDictSort.Max() + 1).ToString();
                            filedDictAdd.FIELD_STATE = "1";
                            filedDictAdd.FIELD_TYPE = "1";
                            filedDictAdd.FIRST_RPERSON = claims.HIS_NAME;
                            filedDictAdd.FIRST_RTIME = DateTime.Now;
                        }
                    }
                    formDict.FORM_JSON = JsonConvert.SerializeObject(PageSettingForm);
                    listUpdate.Add(formDict);
                }
                Dictionary<string, object> config = new Dictionary<string, object>();
                if (addnClass.CLASS_ADDN_CONFIG.IsNotNullOrEmpty())
                    config = JsonConvert.DeserializeObject<Dictionary<string, object>>(addnClass.CLASS_ADDN_CONFIG);
                if (config.ContainsKey(form.formCode))
                    config[form.formCode] = dto.remark;
                else
                    config.Add(form.formCode, dto.remark);
                //写入：是否校验日期及时间过期
                if (dto.ifCheckExpired || config.ContainsKey(DATE_EXPIRED_KEY))
                {
                    List<string> dateExpiredFieldList = config.TryGetValue(DATE_EXPIRED_KEY, out object fieldStrings) ? fieldStrings.ToString().Split(',').ToList() : new List<string>(); //DATE_EXPIRED_KEY"DATE_EXPIRED_FIELDS"
                    if (dto.ifCheckExpired)
                        dateExpiredFieldList.Add(form.formCode);
                    else
                        dateExpiredFieldList.RemoveAll(a => a == form.formCode);
                    config[DATE_EXPIRED_KEY] = string.Join(',', dateExpiredFieldList.Where(a => a.IsNotNullOrEmpty()).Distinct());
                }
                //写入：是否计入清单统计
                if (dto.ifListStatistics || config.ContainsKey(LIST_STATICS_KEY))
                {
                    List<string> listStatics = config.TryGetValue(LIST_STATICS_KEY, out object fieldStrings) ? fieldStrings.ToString().Split(',').ToList() : new List<string>();
                    if (dto.ifListStatistics)
                        listStatics.Add(form.formCode);
                    else
                        listStatics.RemoveAll(a => a == form.formCode);
                    config[LIST_STATICS_KEY] = string.Join(',', listStatics.Distinct());
                }
                //写入：是否自定义基础数据
                if (dto.ifBasicData || config.ContainsKey(BASIC_DATA_KEY))
                {
                    List<string> listBasic = config.TryGetValue(BASIC_DATA_KEY, out object fieldStrings) ? fieldStrings.ToString().Split(',').ToList() : new List<string>();
                    if (dto.ifBasicData)
                        listBasic.Add(form.formCode);
                    else
                        listBasic.RemoveAll(a => a == form.formCode);
                    config[BASIC_DATA_KEY] = string.Join(',', listBasic.Distinct());
                }
                addnClass.CLASS_ADDN_CONFIG = JsonConvert.SerializeObject(config);
                if (addnClass != null && addnClass.TABLE_SETUP_ID.IsNotNullOrEmpty())
                {   //表格
                    SYS6_MODULE_FUNC_DICT tableDict = _soa.Db.Queryable<SYS6_MODULE_FUNC_DICT>().Where(p => p.SETUP_ID == addnClass.TABLE_SETUP_ID && p.HOSPITAL_ID == claims.HOSPITAL_ID)?.First();
                    if (tableDict != null)
                    {
                        List<PageSettingTableCol> listTableCol = new List<PageSettingTableCol>();
                        if (tableDict.FORM_COL_JSON != null)
                            listTableCol = JsonConvert.DeserializeObject<List<PageSettingTableCol>>(tableDict.FORM_COL_JSON);
                        if (listTableCol?.FindIndex(w => w.headerName == dto.formName) > -1 && dto.formCode.IsNullOrEmpty())
                        {
                            resultDto.msg = $"分类属性名称:{dto.formName}已存在，请勿重复添加";
                            resultDto.success = false;
                            return resultDto;
                        }
                        PageSettingTableCol col = new PageSettingTableCol();
                        col.headerName = dto.formName;

                        col.hide = !dto.ifShow;
                        //新增表格属性
                        if (listTableCol == null || listTableCol.Count <= 0)
                        {
                            listTableCol = new List<PageSettingTableCol>();
                            listTableCol.Add(col);
                        }
                        else //更新表格属性
                        {
                            //更新表格属性
                            int index = listTableCol.FindIndex(w => w.field == dto.formCode);
                            if (index > -1)
                            {
                                col = listTableCol[index];
                                col.field = form.formCode;
                                col.headerName = dto.formName;
                                col.hide = !dto.ifShow;
                                if (dto.ifDelete)
                                    listTableCol.Remove(col);
                            }
                            else
                            {
                                col.field = form.formCode;
                                //插入表格属性
                                col.sort = (listTableCol.Count + 1).ToString();
                                listTableCol.Add(col);
                            }
                        }
                        tableDict.FORM_COL_JSON = JsonConvert.SerializeObject(listTableCol);
                        listUpdate.Add(tableDict);
                    }
                }
                using (_soa.Begin())
                {
                    _soa.Db.Updateable(listUpdate).ExecuteCommand();
                    _soa.Db.Updateable(addnClass).UpdateColumns(i => new { i.CLASS_ADDN_CONFIG }).ExecuteCommand();
                    if (filedDictAdd != null && dto.ifListStatistics)
                        _soa.Db.Insertable(filedDictAdd).ExecuteCommand();
                    if (filedDictUpdate != null)
                        _soa.Db.Updateable(filedDictUpdate).ExecuteCommand();
                    if (filedDictDelete != null)
                        _soa.Db.Deleteable(filedDictDelete).ExecuteCommand();
                    _soa.SaveChanges();
                }
                //调用工具箱清除缓存接口 同步修改
                var res = _systemService.ClearCache(claims.HOSPITAL_ID, "H81");
            }
            catch (Exception ex)
            {
                resultDto.success = false;
                resultDto.msg = "保存失败";
                _logger.LogError($"SaveClassAttributeInfo:{ex.ToString()}");
            }


            return resultDto;
        }

        private bool CheckFeildExistsData(PMS_ADDN_CLASS_INFO classInfo, Form form, string warningWord, out ResultDto resultDto)
        {
            bool isExistsData = false;
            resultDto = new ResultDto();

            if (form == null || (form.ifNew != null && !form.ifNew.Value) || classInfo == null)
            {
                return isExistsData;
            }
            string feildName = form.formCode;
            List<string> personIds = new List<string>();

            //动态分类 
            if (classInfo.CLASS_TYPE != "0")
                personIds = _soa.Db.Queryable<PMS_ADDN_RECORD>().Where(a => a.CLASS_ID == classInfo.CLASS_ID && a.RECORD_DATA.Contains(feildName)).Select(a => a.PERSON_ID).Take(50).ToList();

            //固定分类
            else if (classInfo.CLASS_ID == "PMS_REWARD_LIST")
                personIds = _soa.Db.Queryable<PMS_REWARD_LIST>().Where(a => a.RECORD_DATA.Contains(feildName)).Select(a => a.PERSON_ID).Take(50).ToList();
            else if (classInfo.CLASS_ID == "PMS_TEACH_LIST")
                personIds = _soa.Db.Queryable<PMS_TEACH_LIST>().Where(a => a.RECORD_DATA.Contains(feildName)).Select(a => a.PERSON_ID).Take(50).ToList();
            else if (classInfo.CLASS_ID == "PMS_RESUME_LIST")
                personIds = _soa.Db.Queryable<PMS_RESUME_LIST>().Where(a => a.RECORD_DATA.Contains(feildName)).Select(a => a.PERSON_ID).Take(50).ToList();
            else if (classInfo.CLASS_ID == "PMS_STUDY_LIST")
                personIds = _soa.Db.Queryable<PMS_STUDY_LIST>().Where(a => a.RECORD_DATA.Contains(feildName)).Select(a => a.PERSON_ID).Take(50).ToList();
            else if (classInfo.CLASS_ID == "PMS_RESEARCH_LIST")
                personIds = _soa.Db.Queryable<PMS_RESEARCH_LIST>().Where(a => a.RECORD_DATA.Contains(feildName)).Select(a => a.PERSON_ID).Take(50).ToList();
            else if (classInfo.CLASS_ID == "PMS_THESIS_LIST")
                personIds = _soa.Db.Queryable<PMS_THESIS_LIST>().Where(a => a.RECORD_DATA.Contains(feildName)).Select(a => a.PERSON_ID).Take(50).ToList();
            else if (classInfo.CLASS_ID == "PMS_EDUCATION_LIST")
                personIds = _soa.Db.Queryable<PMS_EDUCATION_LIST>().Where(a => a.RECORD_DATA.Contains(feildName)).Select(a => a.PERSON_ID).Take(50).ToList();
            else if (classInfo.CLASS_ID == "PMS_SKILL_CERTIFICATE_LIST")
                personIds = _soa.Db.Queryable<PMS_SKILL_CERTIFICATE_LIST>().Where(a => a.RECORD_DATA.Contains(feildName)).Select(a => a.PERSON_ID).Take(50).ToList();
            else if (classInfo.CLASS_ID == "PMS_EXPATRIATE_LIST")
                personIds = _soa.Db.Queryable<PMS_EXPATRIATE_LIST>().Where(a => a.RECORD_DATA.Contains(feildName)).Select(a => a.PERSON_ID).Take(50).ToList();
            else if (classInfo.CLASS_ID == "PMS_EXCHANGE_LIST")
                personIds = _soa.Db.Queryable<PMS_EXCHANGE_LIST>().Where(a => a.RECORD_DATA.Contains(feildName)).Select(a => a.PERSON_ID).Take(50).ToList();
            else if (classInfo.CLASS_ID == "PMS_INTELLECTUAL_LIST")
                personIds = _soa.Db.Queryable<PMS_INTELLECTUAL_LIST>().Where(a => a.RECORD_DATA.Contains(feildName)).Select(a => a.PERSON_ID).Take(50).ToList();
            else if (classInfo.CLASS_ID == "PMS_SOCIAL_OFFICE_LIST")
                personIds = _soa.Db.Queryable<PMS_SOCIAL_OFFICE_LIST>().Where(a => a.RECORD_DATA.Contains(feildName)).Select(a => a.PERSON_ID).Take(50).ToList();
            else if (classInfo.CLASS_ID == "PMS_TRAIN_LIST")
                personIds = _soa.Db.Queryable<PMS_TRAIN_LIST>().Where(a => a.RECORD_DATA.Contains(feildName)).Select(a => a.PERSON_ID).Take(50).ToList();
            else if (classInfo.CLASS_ID == "PMS_PROFESSIONAL_LIST")
                personIds = _soa.Db.Queryable<PMS_PROFESSIONAL_LIST>().Where(a => a.RECORD_DATA.Contains(feildName)).Select(a => a.PERSON_ID).Take(50).ToList();
            if (personIds.Any())
            {
                personIds = personIds.Distinct().ToList();
                List<string> hisIds = _soa.Db.Queryable<PMS_PERSON_INFO>().Where(a => personIds.Contains(a.PERSON_ID)).Select(a => a.HIS_ID).Distinct().ToList();

                if (hisIds.Any())
                {
                    isExistsData = true;
                    if (warningWord.Any())
                        warningWord = "，" + warningWord;
                    resultDto.msg = $"分类属性名称[{form.formName}]在以下工号的员工档案中已存在数据{warningWord}：{string.Join("、", hisIds)}";
                    resultDto.success = false;
                }
            }
            return isExistsData;
        }

        /// <summary>
        /// 删除分类信息
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="claims"></param>
        /// <returns></returns>
        public ResultDto DeleteClassInfo(string setUpId, H.Utility.ClaimsDto claims)
        {
            ResultDto resultDto = new ResultDto();
            List<SYS6_MODULE_FUNC_DICT> listDelete = new List<SYS6_MODULE_FUNC_DICT>();
            List<OA_FIELD_DICT> listDeleteField = new List<OA_FIELD_DICT>();
            int resultRow = 0;
            //表单
            SYS6_MODULE_FUNC_DICT formDict = _soa.Db.Queryable<SYS6_MODULE_FUNC_DICT>().Where(p => p.SETUP_ID == setUpId && p.HOSPITAL_ID == claims.HOSPITAL_ID).First();
            if (formDict != null)
            {
                listDelete.Add(formDict);
            }
            PMS_ADDN_CLASS_INFO addnClass = _IModuleLabGroupService.GetPmsAddnClassInfo().Where(p => p.FORM_SETUP_ID == setUpId /*&& p.HOSPITAL_ID == claims.HOSPITAL_ID*/).First();
            if (addnClass != null)
            {
                //已录入该类型记录的工号
                List<string> hisIds = _soa.Db.Queryable<PMS_ADDN_RECORD>().InnerJoin<PMS_PERSON_INFO>((r, p) => r.PERSON_ID == p.PERSON_ID && r.CLASS_ID == addnClass.CLASS_ID)
                    .Select((r, p) => p.HIS_ID).ToList()
                    .Distinct().ToList();
                //存在已录入数据，不允许删除
                if (hisIds.Any())
                {
                    throw new Exception($"该分类[{addnClass.CLASS_NAME}]在以下工号的员工档案中已存在记录，无法删除：{string.Join('、', hisIds)}");
                }
                // addnClass.CLASS_STATE = "2";
                //表格
                SYS6_MODULE_FUNC_DICT tableDict = _soa.Db.Queryable<SYS6_MODULE_FUNC_DICT>().Where(p => p.SETUP_ID == addnClass.TABLE_SETUP_ID && p.HOSPITAL_ID == claims.HOSPITAL_ID)?.First();
                if (tableDict != null)
                {
                    listDelete.Add(tableDict);
                }
                listDeleteField = _soa.Db.Queryable<OA_FIELD_DICT>().Where(p => p.MODULE_ID == "H81" && p.CLASSE_CODE == addnClass.CLASS_TABLE_NAME).ToList();
            }
            using (_soa.Begin())
            {
                resultRow = _soa.Db.Deleteable(listDelete).ExecuteCommand();
                _soa.Db.Deleteable(listDeleteField).ExecuteCommand();
                if (addnClass != null)
                    _soa.Db.Deleteable(addnClass).ExecuteCommand();
                _soa.SaveChanges();
            }
            resultDto.data = resultRow;
            if (resultRow == 0)
            {
                resultDto.success = false;
                resultDto.msg = "删除失败！";
            }
            else
            {
                resultDto.success = true;
                resultDto.msg = "删除成功！";
            }
            return resultDto;
        }
        /// <summary>
        /// 保存分类排序
        /// </summary>
        /// <param name="listDto"></param>
        /// <param name="claims"></param>
        /// <returns></returns>
        public ResultDto SaveClassInfoSort(List<SysSetUpInfoDto> listDto, H.Utility.ClaimsDto claims)
        {
            ResultDto resultDto = new ResultDto();
            var varMoudle = _soa.Db.Queryable<SYS6_MODULE_FUNC_DICT>().Where(p => p.SETUP_STATE == "1" && p.HOSPITAL_ID == claims.HOSPITAL_ID && p.MODULE_ID == "H81")
.Select(s => new
{
    SETUP_ID = s.SETUP_ID,
    SETUP_SORT = s.SETUP_SORT,
    LAST_MPERSON = s.LAST_MPERSON,
    LAST_MTIME = s.LAST_MTIME
}).ToList();
            List<PMS_ADDN_CLASS_INFO> listClassInfo = _IModuleLabGroupService.GetPmsAddnClassInfo();
            List<SYS6_MODULE_FUNC_DICT> listUpadte = new List<SYS6_MODULE_FUNC_DICT>();
            foreach (var item in listDto)
            {
                SYS6_MODULE_FUNC_DICT dict = new SYS6_MODULE_FUNC_DICT();
                int index = varMoudle.FindIndex(w => w.SETUP_ID == item.SETUP_ID);
                if (index > -1)
                {
                    dict.SETUP_ID = item.SETUP_ID;
                    dict.SETUP_SORT = item.SETUP_SORT.ToString().PadLeft(5, '0');
                    dict.LAST_MPERSON = claims.HIS_NAME;
                    dict.LAST_MTIME = DateTime.Now;
                    listUpadte.Add(dict);
                }
                int indexClass = listClassInfo.FindIndex(w => w.FORM_SETUP_ID == item.SETUP_ID);
                if (indexClass > -1)
                {
                    listClassInfo[indexClass].CLASS_SORT = item.SETUP_SORT.ToString().PadLeft(5, '0');
                    listClassInfo[indexClass].LAST_MPERSON = claims.HIS_NAME;
                    listClassInfo[indexClass].LAST_TIME = DateTime.Now;
                }
            }
            using (_soa.Begin())
            {
                _soa.Db.Updateable(listUpadte).WhereColumns(w => w.SETUP_ID).UpdateColumns(w => new { w.SETUP_SORT, w.LAST_MTIME, w.LAST_MPERSON }).ExecuteCommand();
                _soa.Db.Updateable(listClassInfo).WhereColumns(w => w.CLASS_ID).UpdateColumns(w => new { w.CLASS_SORT, w.LAST_TIME, w.LAST_MPERSON }).ExecuteCommand();
                _soa.SaveChanges();
                resultDto.success = true;
            }
            return resultDto;
        }

        /// <summary>
        /// 保存分类属性排序
        /// </summary>
        /// <param name="listDto"></param>
        /// <returns></returns>
        public ResultDto SaveClassAttributeInfoSort(List<SysClassAttributeDto> listDto, H.Utility.ClaimsDto claims)
        {
            ResultDto resultDto = new ResultDto();
            SYS6_MODULE_FUNC_DICT formDict = _soa.Db.Queryable<SYS6_MODULE_FUNC_DICT>().Where(p => p.SETUP_ID == listDto[0].SETUP_ID && p.HOSPITAL_ID == claims.HOSPITAL_ID)?.First();
            if (formDict != null)
            {
                PageSettingForm PageSettingForm = new PageSettingForm();
                if (formDict.FORM_JSON != null)
                {
                    PageSettingForm = JsonConvert.DeserializeObject<PageSettingForm>(formDict.FORM_JSON);
                    foreach (var item in PageSettingForm.form)
                    {
                        SysClassAttributeDto dto = listDto.Find(w => w.formCode == item.formCode);
                        item.sort = dto?.sort != null ? dto.sort.Value : 0;
                    }
                    formDict.FORM_JSON = formDict.FORM_JSON = JsonConvert.SerializeObject(PageSettingForm);
                    _soa.Db.Updateable(formDict).ExecuteCommand();
                }
            }
            //调用工具箱清除缓存接口 同步修改
            var res = _systemService.ClearCache(claims.HOSPITAL_ID, "H81");
            return resultDto;
        }

        /// <summary>
        /// 获取分类日期类型
        /// </summary>
        /// <param name="classId"></param>
        /// <returns></returns>

        public ResultDto GetClassDateType(string classId, string hospitalId)
        {
            ResultDto resultDto = new ResultDto();
            List<CommboxInfo> commboxInfos = new List<CommboxInfo>();
            if (classId == "PMS_HEALTH_DOC_LIST")
            {
                commboxInfos.Add(new CommboxInfo { ID = "FIRST_RTIME", NAME = "创建日期" });
                commboxInfos.Add(new CommboxInfo { ID = "LAST_MTIME", NAME = "修改日期" });
                resultDto.data = commboxInfos;
                return resultDto;
            }
            else if (classId == "PMS_EXAM_LIST")
            {
                commboxInfos.Add(new CommboxInfo { ID = "EXAM_START_DATE", NAME = "考试日期" });
                resultDto.data = commboxInfos;
                return resultDto;
            }
            else if (classId == "PMS_ASSESS_LIST")
            {
                commboxInfos.Add(new CommboxInfo { ID = "EVALUATION_START_TIME", NAME = "评估日期" });
                resultDto.data = commboxInfos;
                return resultDto;
            }
            else if (classId == "PMS_PERSON_INFO")
            {
                commboxInfos.Add(new CommboxInfo { ID = "IN_LAB_TIME", NAME = "来科日期" });
                commboxInfos.Add(new CommboxInfo { ID = "GRADUATE_DATE", NAME = "毕业时间" });
                commboxInfos.Add(new CommboxInfo { ID = "BIRTHDAY", NAME = "出生年月" });
                commboxInfos.Add(new CommboxInfo { ID = "WORK_TIME", NAME = "参加工作时间" });
                commboxInfos.Add(new CommboxInfo { ID = "TECH_CERTIFICE_TIME", NAME = "职称取得时间" });
                resultDto.data = commboxInfos;
                return resultDto;
            }
            var pmsAddnClassInfo = _soa.Db.Queryable<PMS_ADDN_CLASS_INFO>()
                    .Where(a => (a.CLASS_TYPE == "0" || a.CLASS_TYPE == "1" || a.CLASS_TYPE == "99") && a.CLASS_ID == classId)//排除掉11-P3实验室的分类
                    .OrderBy(a => a.CLASS_SORT).ToList().FirstOrDefault();
            if (pmsAddnClassInfo != null)
            {
                //表单
                SYS6_MODULE_FUNC_DICT formDict = _soa.Db.Queryable<SYS6_MODULE_FUNC_DICT>().Where(p => p.SETUP_ID == pmsAddnClassInfo.FORM_SETUP_ID && p.HOSPITAL_ID == hospitalId)?.First();
                if (formDict?.FORM_JSON != null)
                {
                    PageSettingForm PageSettingForm = JsonConvert.DeserializeObject<PageSettingForm>(formDict.FORM_JSON);
                    List<Form> forms = PageSettingForm?.form.FindAll(w => w.dataType == "3");
                    foreach (Form item in forms)
                    {
                        CommboxInfo commboxInfo = new CommboxInfo();
                        commboxInfo.ID = item.formCode;
                        commboxInfo.NAME = item.formName;
                        commboxInfos.Add(commboxInfo);
                    }
                }
            }
            resultDto.data = commboxInfos;
            return resultDto;
        }

        public ResultDto InsertPersonInfoSetting()
        { 
            ResultDto resultDto = new ResultDto();
            List<PropertyInfoModel> propertyInfos = EntityMetadataGenerator.GenerateMetadataJson<PMS_PERSON_INFO>();
            SYS6_MODULE_FUNC_DICT formDict = _soa.Db.Queryable<SYS6_MODULE_FUNC_DICT>()
                .Where(p => p.SETUP_ID == "H8101424")?
                .First();
            if (formDict != null)
            {
                PageSettingForm PageSettingForm = new PageSettingForm();
                PageSettingForm.form = new List<Form>();
                foreach (var item in propertyInfos)
                {
                    Form form = new Form();
                    form.formCname = item.Description;
                    form.formName = item.Description;
                    form.formCode = item.PropertyName;
                    form.ifShow = false;
                    form.titleShow = true;
                    form.titleColor = "#000000";
                    form.titleSize = 13;
                    form.titleStyle = "0";
                    form.titleAlign = "3";
                    form.contentLine = 1;
                    form.contentMaxLine = 1;
                    form.contentHeightClass = "1";
                    form.contentHeightRatio = "";
                    form.contentAlign = "1";
                    form.contentFontSize = 13;
                    form.contentStyle = "0";
                    form.titleAndContentType = "1";
                    form.contentEnlarge = false;
                    //form.ifRequired = dto.ifRequired;
                    form.replaceField = true;
                    form.onlyRead = "1";
                    form.dataType = item.DbType;
                   // form.dataClass = dto.dataClass;
                    form.allowMaintainDropDownData = false;
                    form.editeState = false;
                    form.unitFlag = false;
                    PageSettingForm.form.Add(form);
                }
                formDict.FORM_COL_JSON= JsonConvert.SerializeObject(PageSettingForm);
                _soa.Db.Updateable(formDict).ExecuteCommand();
            }
            return resultDto;
        }
    }
}
