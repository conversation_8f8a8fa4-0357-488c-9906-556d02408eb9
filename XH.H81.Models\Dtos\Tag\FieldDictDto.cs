﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Dtos.Tag
{
    public class FieldDictDto
    {
        /// <summary>
        /// 字段ID，主键
        /// </summary>
        public string? FIELD_ID { get; set; }

        /// <summary>
        /// 医疗机构ID
        /// </summary>
        public string? HOSPITAL_ID { get; set; }

        /// <summary>
        /// 模块ID
        /// </summary>
        public string? MODULE_ID { get; set; }

        /// <summary>
        /// 字段分类
        /// </summary>
        public string FIELD_CLASS { get; set; }

        /// <summary>
        /// 字段代码
        /// </summary>
        public string? FIELD_CODE { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string? FIELD_DESC { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public string? FIELD_SORT { get; set; }

        /// <summary>
        /// 字段名称
        /// </summary>
        public string FIELD_NAME { get; set; }


        /// <summary>
        /// 状态
        /// </summary>
        public string FIELD_STATE { get; set; }

        /// <summary>
        /// 只读配置JSON
        /// </summary>
        public string READONLY_JSON { get; set; }

        /// <summary>
        /// 样式配置JSON
        /// </summary>
        public string? STYLE_JSON { get; set; }


        /// <summary>
        /// 扩展json
        /// </summary>
        public string? ADDN_JSON { get; set; }

        /// <summary>
        /// 首次操作人
        /// </summary>
        public string? FIRST_RPERSON { get; set; }

        /// <summary>-
        /// 首次操作时间
        /// </summary>
        public DateTime? FIRST_RTIME { get; set; }

        /// <summary>
        /// 最后修改人
        /// </summary>
        public string? LAST_MPERSON { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime? LAST_MTIME { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? REMARK { get; set; }
    }
}
