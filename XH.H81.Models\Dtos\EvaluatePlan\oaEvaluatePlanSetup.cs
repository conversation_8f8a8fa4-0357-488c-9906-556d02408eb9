﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XH.LAB.UTILS.Models.Entites;

namespace XH.H81.Models.Dtos.EvaluatePlan
{
    public class OaEvaluatePlanSetup
    {
        /// <summary>
        /// 规评方案设置ID (主键)
        /// </summary>
        public string? EPLAN_SID { get; set; }

        /// <summary>
        /// 医疗机构ID
        /// </summary>
        public string HOSPITAL_ID { get; set; }

        /// <summary>
        /// 科室ID
        /// </summary>
        public string? LAB_ID { get; set; }

        /// <summary>
        /// 专业组ID
        /// </summary>
        public string? PGROUP_ID { get; set; }

        /// <summary>
        /// 科室/专业组ID
        /// </summary>
        public string? LAB_PGROUP_ID { get; set; }

        /// <summary>
        /// 科室/专业组名称
        /// </summary>
        public string? LAB_PGROUP_NAME { get; set; }

        /// <summary>
        /// 规评方案名称
        /// </summary>
        public string? EPLAN_NAME { get; set; }

        /// <summary>
        /// 规评方案ID (主键)
        /// </summary>
        public string EPLAN_ID { get; set; }

        /// <summary>
        /// 规评方案类型 (1-学习, 2-培训, 3-评估, 4-考试, 5-资质证书)
        /// </summary>
        public string? EPLAN_TYPE { get; set; }

        /// <summary>
        /// 规评方案类型 (1-学习, 2-培训, 3-评估, 4-考试, 5-资质证书)
        /// </summary>
        public string? EPLAN_TYPE_NAME { get; set; }

        /// <summary>
        /// 权限组合ID
        /// </summary>
        public string? PROLE_COM_SID { get; set; }

        /// <summary>
        /// 权限组合ID
        /// </summary>
        public string? PROLE_COM_NAME { get; set; }

        /// <summary>
        /// 规评方案适用类型（注意值传英文编码）LAB-检验科室 ；PGROUP-检验专业组；MGROUP-管理专业组；POST-岗位；PROLE-岗位角色
        /// </summary>
        public string? EPLAN_APPLY_TYPE { get; set; }

        /// <summary>
        /// 规评方案适用类型
        /// </summary>
        public string? EPLAN_APPLY_TYPE_NAME { get; set; }

        /// <summary>
        /// 规评效期
        /// </summary>
        public decimal? EPLAN_SHELF_LIFE { get; set; }

        /// <summary>
        /// 规评效期单位类型 (1-年, 2-月, 3-日)
        /// </summary>
        public string? SHELF_LIFE_UTYPE { get; set; }

        /// <summary>
        /// 规评效期单位类型 (1-年, 2-月, 3-日)
        /// </summary>
        public string? SHELF_LIFE_UTYPE_NAME { get; set; }

        /// <summary>
        /// 规评预警时长
        /// </summary>
        public decimal? WARN_DURATION { get; set; }

        /// <summary>
        /// 预警时长单位类型 (1-年, 2-月, 3-日)
        /// </summary>
        public string? WARN_UTYPE { get; set; }

        /// <summary>
        /// 预警时长单位类型 (1-年, 2-月, 3-日)
        /// </summary>
        public string? WARN_UTYPE_NAME { get; set; }

        /// <summary>
        /// 限权类型 (0-禁用, 1-限权, 2-停岗)
        /// </summary>
        public string? LIMIT_PROLE_TYPE { get; set; }

        /// <summary>
        /// 限权类型 (0-禁用, 1-限权, 2-停岗)
        /// </summary>
        public string? LIMIT_PROLE_TYPE_NAME { get; set; }

        /// <summary>
        /// 排序号
        /// </summary>
        public string? EPLAN_SSORT { get; set; }

        /// <summary>
        /// 状态 (0-禁用, 1-在用, 2-删除, 3-未启用)设置好限权/停岗后,保存，状态由空变成未启用，点击启用按钮，状态变成启用，按钮变成禁用。禁用情况下可以修改，修改保存后，状态变成未启用，需要重新启用。如果关闭窗口，或者点击跳转其他菜单，需要弹出提醒保存。
        /// </summary>
        public string? EPLAN_SSTATE { get; set; }
        /// <summary>
        /// 状态 (0-禁用, 1-在用, 2-删除, 3-未启用)设置好限权/停岗后,保存，状态由空变成未启用，点击启用按钮，状态变成启用，按钮变成禁用。禁用情况下可以修改，修改保存后，状态变成未启用，需要重新启用。如果关闭窗口，或者点击跳转其他菜单，需要弹出提醒保存。
        /// </summary>
        public string? EPLAN_SSTATE_NAME { get; set; }

        /// <summary>
        /// 首次登记人
        /// </summary>
        public string? FIRST_RPERSON { get; set; }

        /// <summary>
        /// 首次登记时间
        /// </summary>
        public DateTime? FIRST_RTIME { get; set; }

        /// <summary>
        /// 最后修改人员
        /// </summary>
        public string? LAST_MPERSON { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime? LAST_MTIME { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? REMARK { get; set; }

        /// <summary>
        /// 适用范围单位对象列表（科室ID、管理专业组ID、检验专业组ID、岗位ID、岗位角色ID）
        /// </summary>
        public List<OA_EVALUATE_PLAN_UNIT>? UNITS { get; set; }
        /// <summary>
        /// 适用范围单位名称（科室、管理专业组、检验专业组、岗位、岗位角色）
        /// </summary>
        public string? UNITS_NAME { get; set; }
        /// <summary>
        /// 证书对象
        /// </summary>
        public OaCertificateDict? CERTIFICATE { get; set; }
    }
}
