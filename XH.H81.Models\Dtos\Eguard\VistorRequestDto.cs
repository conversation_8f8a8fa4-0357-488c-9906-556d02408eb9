﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Dtos.Eguard
{
    public class VistorRequestDto
    {
        /// <summary>
        /// 访问申请id
        /// </summary>
        public string VISIT_REQ_ID { get; set; }
        //状态  0:提交 1：审核 2:驳回
        public string? VISIT_REQ_STATE { get; set; }
        public string? PHOTO { get; set; }
        public string? PERSON_NAME { get; set; }
        public string? PHONE_NO { get; set; }
        public string? WORK_UNIT { get; set; }
        public string? VISIT_REQ_TYPE { get; set; }
        public string? VISIT_PERSON_ID { get; set; }
        public string? VISIT_PERSON_NAME { get; set; }
        public string? VISIT_ROOM_ADDR { get; set; }
        public string? FELLOW_PERSON { get; set; }
        public int? FELLOW_PERSON_NUM { get; set; }
        public string? VISIT_REQ_REASON { get; set; }
        public string? VISIT_TIME_FRAME { get; set; }
        public DateTime? REQ_TIME { get; set; }
        public string? CHECK_PERSON { get; set; }
        public DateTime? CHECK_TIME { get; set; }
        public string? CHECK_REJECT_CAUSE { get; set; }
        public string? APPROVE_PERSON { get; set; }
        public DateTime? APPROVE_TIME { get; set; }
        public string? APPROVE_REJECT_CAUSE { get; set; }
        public string? VISIT_ROOM_ID { get; set; }
        public string? PERSON_ID { get; set; }
        //申请来源 1 i检验  2PC端
        public string? VISIT_ORIGIN { get; set; }
        public DateTime? FIRST_RTIME { get; set; }
    }
}
