﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Entities.others
{
    /// <summary>
    /// 用户HISID关联表
    /// </summary>
    public class SYS6_USER_HISID
    {
        /// <summary>
        /// HISID标识（主键）
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string HISID_ID { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public string USER_NO { get; set; }

        /// <summary>
        /// HISID值
        /// </summary>
        public string HIS_ID { get; set; }

        /// <summary>
        /// HIS名称
        /// </summary>
        public string HIS_NAME { get; set; }

        /// <summary>
        /// 接口标识
        /// </summary>
        public string INTERFACE_ID { get; set; }

        /// <summary>
        /// 状态标志
        /// </summary>
        public string STATE_FLAG { get; set; }

        /// <summary>
        /// 首次操作人
        /// </summary>
        public string FIRST_RPERSON { get; set; }

        /// <summary>
        /// 首次操作时间
        /// </summary>
        public DateTime? FIRST_RTIME { get; set; }

        /// <summary>
        /// 末次操作人
        /// </summary>
        public string LAST_MPERSON { get; set; }

        /// <summary>
        /// 末次操作时间
        /// </summary>
        public DateTime? LAST_MTIME { get; set; }

        /// <summary>
        /// 备注信息
        /// </summary>
        public string REMARK { get; set; }

        /// <summary>
        /// 是否默认HISID
        /// </summary>
        public string DEFAULT_HISID { get; set; }
    }
}
