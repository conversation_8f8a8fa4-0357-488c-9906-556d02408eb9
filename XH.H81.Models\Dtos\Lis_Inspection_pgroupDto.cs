﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Dtos
{
    public class Lis_Inspection_pgroupDto
    {
        public string PGROUP_ID { get; set; }
        public string LAB_ID { get; set; }
        public string H<PERSON><PERSON>TAL_ID { get; set; }
        public string PGROUP_CODE { get; set; }
        public string PGROUP_SORT { get; set; }
        public string PGROUP_NAME { get; set; }
        public string PGROUP_CLASS { get; set; }
        public string PGROUP_PROPERTY { get; set; }
        public string PGROUP_PERSON { get; set; }
        public string PGROUP_ADDRESS { get; set; }
        public string PGROUP_TEL { get; set; }
        public string SPELL_CODE { get; set; }
        public string PGROUP_STATE { get; set; }
        public string FIRST_RPERSON { get; set; }
        public string FIRST_RTIME { get; set; }
        public string LAST_MPERSON { get; set; }
        public string LAST_MTIME { get; set; }
        public string REMARK { get; set; }
        public string AREA_ID { get; set; }
        public string HIS_ID { get; set; }
    }
}
