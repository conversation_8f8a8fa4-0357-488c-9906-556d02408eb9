﻿using AutoMapper;
using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using XH.H81.IServices;
using XH.H81.IServices.Pms;
using XH.H81.Models.Dtos.Exam;
using XH.H81.Models.Entities.EvaluatePlan;
using XH.H81.Models.Entities.Exam;
using XH.H81.Models.Entities.others;
using XH.H81.Models.Entities.Pms;
using XH.H81.Models.Entities.Tag;
using XH.H81.Models.Exam;
using XH.H81.Models.SugarDbContext;
using XH.LAB.UTILS.Helpers;
using XH.LAB.UTILS.Interface;
using XH.LAB.UTILS.Models;
using XH.LAB.UTILS.Models.Dto;
using XH.LAB.UTILS.Models.Entites;
using XH.LAB.UTILS.Models.Enums;

namespace XH.H81.Services
{
    public class ExamService : IExamService
    {
        private readonly IHttpContextAccessor _httpContext;
        private readonly IConfiguration _configuration;
        private readonly ISqlSugarUow<SugarDbContext_Master> _soa;
        private readonly IMapper _mapper;
        private readonly ISystemService _systemService;
        private readonly ILogger<ExamService> _logger;
        private readonly string FileHttpUrl = "/H81pdf/api";
        private readonly IBaseDataServices _IBaseDataServices;
        private readonly IEvaluatePlanService _IEvaluatePlanService;
        private readonly IPmsService _IPmsService;
        public ExamService(IConfiguration configuration, IHttpContextAccessor httpContext, ISqlSugarUow<SugarDbContext_Master> dbContext, IMapper mapper, ISystemService systemService, ILogger<ExamService> logger, IBaseDataServices baseDataServices, IEvaluatePlanService evaluatePlanService, IPmsService ipmsService)
        {
            _configuration = configuration;
            _httpContext = httpContext;
            _soa = dbContext;
            _mapper = mapper;
            _systemService = systemService;
            _logger = logger;
            _IBaseDataServices = baseDataServices;
            _IEvaluatePlanService = evaluatePlanService;
            _IPmsService = ipmsService;
    }
        /// <summary>
        /// 获取用户考试记录
        /// </summary>
        /// <param name="userNo">人员id</param>
        /// <param name="smblFlag">生安标志</param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public ResultDto GetUserExamInfo(string userNo, string? labId, string mgroupId, string pgroupId, string? examState, string? examType, string examName, string? smblFlag, string dateType, string startDate, string endDate,string cerId, string hospitalId)
        {
            ResultDto resultDto = new ResultDto();
            if (_httpContext.GetSmblFlag() == "1")
                smblFlag = "1";
            List<string> listPgroupId = new List<string>();
            if (mgroupId.IsNotNullOrEmpty())
            {
                List<SYS6_INSPECTION_PGROUP> listPgroup = _soa.Db.Queryable<SYS6_INSPECTION_PGROUP>()
                .Where(pg => pg.PGROUP_STATE == "1" && pg.PGROUP_CLASS != "3" && pg.MGROUP_ID == mgroupId)
                .ToList();
                listPgroupId = listPgroup.Select(w => w.PGROUP_ID).ToList();
            }
            else if (labId.IsNotNullOrEmpty())
            {
                List<SYS6_INSPECTION_PGROUP> listPgroup = _soa.Db.Queryable<SYS6_INSPECTION_PGROUP>()
              .Where(pg => pg.PGROUP_STATE == "1" && pg.PGROUP_CLASS != "3" && pg.LAB_ID == labId)
              .ToList();
                listPgroupId = listPgroup.Select(w => w.PGROUP_ID).ToList();
            }

            var varExamInfo = _soa.Db.Queryable<EMP_EXAM_USER_ANSWER>()
                .LeftJoin<OA_EVALUATE_PLAN_SETUP>((a, b) => a.STD_SCHEME_ID == b.EPLAN_ID && b.EPLAN_SSTATE == "1")
                .LeftJoin<OA_EVALUATE_PLAN_DICT>((a, b, c) => a.STD_SCHEME_ID == c.EPLAN_ID)
                .LeftJoin<EMP_EXAM_INFO>((a, b, c, d) => a.EXAM_ID == d.EXAM_ID)
                .LeftJoin<EMP_PAPER_INFO>((a, b, c, d, f) => a.PAPER_ID == f.PAPER_ID)
                .Where((a, b, c, d, f) => a.EXAM_USER_STATE != "10" && a.USER_ID != "STD_EXAM")
                .WhereIF(userNo.IsNotNullOrEmpty(), (a, b, c, d, f) => a.USER_ID == userNo)
                .WhereIF(pgroupId.IsNotNullOrEmpty(), (a, b, c, d, f) => a.PGROUP_ID == pgroupId)
                .WhereIF(listPgroupId.Count > 0, (a, b, c, d, f) => listPgroupId.Contains(a.PGROUP_ID))
                .WhereIF(smblFlag == "1", (a, b, c, d, f) => a.SMBL_FLAG == smblFlag)
                .WhereIF(examState.IsNotNullOrEmpty(), (a, b, c, d, f) => a.EXAM_USER_STATE == examState)
                .WhereIF(examType.IsNotNullOrEmpty(), (a, b, c, d, f) => d.EXAM_CLASS == examType)
                .WhereIF(cerId.IsNotNullOrEmpty(), (a, b, c, d, f) => a.CER_ID == cerId)
                .WhereIF(examName.IsNotNullOrEmpty(), (a, b, c, d, f) => (a.EXAM_NAME != null && a.EXAM_NAME.Contains(examName)) || (d.EXAM_NAME != null && d.EXAM_NAME.Contains(examName) || (c.EPLAN_NAME != null && c.EPLAN_NAME.Contains(examName))))
                .Select((a, b, c, d, f) => new EmpExamUserAnswerDto()
                {
                    EXAM_USERID = a.EXAM_USERID,
                    USER_ID = a.USER_ID,
                    EXAM_USER_STATE = a.EXAM_USER_STATE,
                    EXAM_SCORE = a.EXAM_SCORE,
                    COMPREHENSIVE_ASSESS = a.COMPREHENSIVE_ASSESS,
                    EXAM_START_DATE = a.EXAM_START_DATE,
                    EPLAN_APPLY_TYPE = b.EPLAN_APPLY_TYPE,
                    EXAM_NAME = a.EXAM_NAME ?? d.EXAM_NAME,
                    EXAM_START_TIME = a.EXAM_START_TIME,
                    SHELF_LIFE_UTYPE = b.SHELF_LIFE_UTYPE,
                    EPLAN_SHELF_LIFE = b.EPLAN_SHELF_LIFE,
                    WARN_DURATION = b.WARN_DURATION,
                    WARN_UTYPE = b.WARN_UTYPE,
                    EVALUATE_AFFIX = a.EVALUATE_AFFIX,
                    PAPER_NAME = f.PAPER_NAME,
                    EPLAN_NAME = c.EPLAN_NAME,
                    SOURCE_TYPE = a.SOURCE_TYPE,
                    EXAM_CLASS = d.EXAM_CLASS,
                    SMBL_FLAG = a.SMBL_FLAG,
                    STD_SCHEME_ID = a.STD_SCHEME_ID,
                    CER_ID=a.CER_ID,
                    REMARK = a.REMARK
                })
                .ToList();
            List<OA_DATA_CLASS> listClass = _IBaseDataServices.GetExamClassInfo(hospitalId);
            List<EmpExamUserAnswerDto> listDto = _mapper.Map<List<EmpExamUserAnswerDto>>(varExamInfo);
            listDto = listDto.OrderBy(w => w.EXAM_START_DATE).ToList();
            listDto = listDto.FindAll(w => (w.SOURCE_TYPE == "1") || (w.SOURCE_TYPE != "1" && w.EXAM_USER_STATE == "7"));
            if (startDate.IsNotNullOrEmpty() && endDate.IsNotNullOrEmpty())
                listDto = listDto.FindAll(w => Convert.ToDateTime(startDate) <= w.EXAM_START_DATE && Convert.ToDateTime(endDate).AddMonths(1) > w.EXAM_START_DATE);
            List<string>listStdId= varExamInfo.Select(w=>w.STD_SCHEME_ID).Distinct().ToList();
            var varEvalDict = _soa.Db.Queryable<OA_EVAL_STAGE_DICT>().Where(w => listStdId.Contains(w.EVAL_STAGE_ID)).ToList();

            var dataClass = _IBaseDataServices.GetOaBaseData().FindAll(x => x.CLASS_ID == "考试评价结果");
            List<string> listCerId = varExamInfo.Select(w => w.CER_ID).Distinct().ToList();
            var varCer = _soa.Db.Queryable<PMS_SKILL_CERTIFICATE_LIST>().Where(w => listCerId.Contains(w.CERTIFICATE_ID)).ToList();
            foreach (var item in listDto)
            {
                int index = dataClass.FindIndex(w => w.DATA_ID == item.COMPREHENSIVE_ASSESS);
                if (index > -1)
                    item.COMPREHENSIVE_ASSESS = dataClass[index].DATA_NAME;
            }
            List<PMS_PERSON_FILE> pmsPersonFile = _soa.Db.Queryable<PMS_PERSON_FILE>().Where(p => p.FILE_STATE == "1").Select<PMS_PERSON_FILE>().ToList();
            foreach (var item in listDto)
            {
                item.EXAM_CLASS_NAME = listClass.Find(w => w.DATA_CLASS_ID == item.EXAM_CLASS)?.DATA_CLASS_CNAME;
                OA_EVAL_STAGE_DICT evalDict = varEvalDict.Find(w => w.EVAL_STAGE_ID == item.STD_SCHEME_ID);
                item.EVAL_STAGE_CLASS = evalDict?.EVAL_STAGE_CLASS;
                if (item.EPLAN_NAME.IsNullOrEmpty())
                    item.EPLAN_NAME = evalDict?.EVAL_STAGE_NAME;
                string resume_affix_name = string.Empty;
                List< PMS_SKILL_CERTIFICATE_LIST >listCer = varCer.FindAll(w => w.CERTIFICATE_ID == item.CER_ID);
                foreach (var cer in listCer)
                {
                    cer.CERTIFICATE_TYPE_NAME =_IBaseDataServices.RecordClassOaBaseName("人事技能证书类型", cer.CERTIFICATE_TYPE);
                    cer.CERTIFICATE_DID_NAME = _IBaseDataServices. GetSkillCertificateName(cer);
                    if (cer.CERTIFICATE_AFFIX != null && cer.CERTIFICATE_AFFIX != "")
                    {
                        string[] AFFIXARRY = cer.CERTIFICATE_AFFIX.Split(",");
                        List<PMS_PERSON_FILE> pmsPersonFileCertificate = pmsPersonFile.Where(p => AFFIXARRY.Contains(p.FILE_ID)).ToList();
                        for (int i = 0; i < AFFIXARRY.Length; i++)
                        {
                            var PersonFile = pmsPersonFileCertificate.FirstOrDefault(p => p.FILE_ID == AFFIXARRY[i]);
                            if (PersonFile != null)
                            {
                                _IPmsService.FillPersonFileInfo(PersonFile);
                                cer.PMS_PERSON_FILE = pmsPersonFileCertificate;
                                resume_affix_name += PersonFile.FILE_NAME + ",";
                            }
                        }
                        if (resume_affix_name != "")
                        {
                            cer.AFFIX_NAME = resume_affix_name.Substring(0, resume_affix_name.Length - 1);
                        }
                    }
                }
                if (listCer.Count > 0)
                    item.CERTIFICATE = listCer[0];
                if (item.EVALUATE_AFFIX.IsNotNullOrEmpty())
                {
                    string[] AFFIXARRY = item.EVALUATE_AFFIX.Split(",");
                    List<PMS_PERSON_FILE> pmsPersonFileReward = pmsPersonFile.Where(p => AFFIXARRY.Contains(p.FILE_ID)).ToList();
                    for (int i = 0; i < AFFIXARRY.Length; i++)
                    {
                        var PersonFile = pmsPersonFileReward.FirstOrDefault(p => p.FILE_ID == AFFIXARRY[i]);
                        if (PersonFile != null)
                        {
                            _IPmsService.FillPersonFileInfo(PersonFile);
                            item.PMS_PERSON_FILE = pmsPersonFileReward;
                            resume_affix_name += PersonFile.FILE_NAME + ",";
                        }
                    }
                    if (resume_affix_name != "")
                    {
                        item.AFFIX_NAME = resume_affix_name.Substring(0, resume_affix_name.Length - 1);
                    }
                }
            }
            resultDto.data = listDto;
            return resultDto;
        }


       
        public ResultDto SaveExamInfo(EmpExamUserAnswerDto dto, string userName, string hospitalId)
        {
            ResultDto resultDto = new ResultDto();
            string smblFlag = _httpContext.GetSmblFlag();
            EMP_EXAM_USER_ANSWER examUserAnswer = _mapper.Map<EMP_EXAM_USER_ANSWER>(dto);
            int count;
            if (examUserAnswer.EXAM_USERID.IsNullOrEmpty())
            {
                examUserAnswer.EXAM_USERID = IDGenHelper.CreateGuid();
                examUserAnswer.FIRST_RPERSON = userName;
                examUserAnswer.FIRST_RTIME = DateTime.Now;
                examUserAnswer.EXAM_USER_STATE = "4";
                examUserAnswer.EXAM_NUM = "1";
                examUserAnswer.EXAM_ID = IDGenHelper.CreateGuid();
                examUserAnswer.PAPER_ID = IDGenHelper.CreateGuid();
                examUserAnswer.SOURCE_TYPE = "1";//来源于人事录入
                if (smblFlag == "1")
                    examUserAnswer.SMBL_FLAG = smblFlag;
                count = _soa.Db.Insertable(examUserAnswer).ExecuteCommand();

            }
            else
            {
                examUserAnswer.LAST_MPERSON = userName;
                examUserAnswer.LAST_MTIME = DateTime.Now;
                count = _soa.Db.Updateable(examUserAnswer).IgnoreColumns(w => new { w.EVALUATE_AFFIX, w.EXAM_ID, w.PAPER_ID, w.FIRST_RPERSON, w.FIRST_RTIME, w.EXAM_NUM, w.EXAM_USER_STATE, w.SOURCE_TYPE }).ExecuteCommand();
            }
            //增加证书保存
            if (dto.CERTIFICATE != null && dto.CERTIFICATE.CERTIFICATE_NAME != null)
            {
                string cer_list = JsonConvert.SerializeObject(dto.CERTIFICATE);
                string action =  (dto.CERTIFICATE.CERTIFICATE_ID != null && dto.CERTIFICATE.CERTIFICATE_ID != "") ? "U" : "I";
                examUserAnswer.CER_ID = _IPmsService.SaveAttachCertificate("PMS_SKILL_CERTIFICATE_LIST", cer_list, userName, action, hospitalId, dto.PERSON_ID,"", "EXAM", examUserAnswer.EXAM_USERID);
                _soa.Db.Updateable(examUserAnswer).UpdateColumns(new List<string> { "CER_ID" }.ToArray()).ExecuteCommand();
            }
            Dictionary<string, object> dicReturn = new Dictionary<string, object>();
            dicReturn.Add("COUNT", count);
            dicReturn.Add("MAXID", examUserAnswer.EXAM_USERID);
            resultDto.success = count > 0;
            resultDto.data = dicReturn;
            return resultDto;
        }



        /// <summary>
        /// 考试审核/删除流程
        /// </summary>
        /// <param name="exam_userids">人员考试ID串，逗号隔开</param>
        /// <param name="exam_user_state">操作类型【5提交6驳回7通过8撤销】</param>
        /// <param name="check_person">指定审核人user_no</param>
        /// <param name="oper_cause">驳回原因</param>
        /// <param name="pwd_bs">验证密码，加密串</param>
        /// <param name="user_name">操作人</param>
        /// <param name="hospital_id">医疗机构id</param>
        /// <param name="log_id">用户登陆id</param>
        /// <param name="oper_computer">操作电脑</param>
        /// <returns></returns>
        public ResultDto UpdateExamUserAnswerInfo(string? exam_userids, string? exam_user_state, string? user_name, string? hospital_id, string? check_person, string? oper_cause, string? oper_computer, string? pwd_bs, string? log_id)
        {
            ResultDto rDto = new ResultDto();
            int rowCount = 0;
            var success = false;
            try
            {
                if (exam_user_state != "10")
                {
                    var obj = new
                    {
                        logId = log_id,
                        password = pwd_bs
                    };
                    string jsonStr = JsonConvert.SerializeObject(obj);
                    success = _systemService.UserVerify(jsonStr).success;
                }
                else
                    success = true;
                if (success)
                {
                    //添加审核流程信息
                    List<EMP_EXAM_ANSWER_LOG> listOperAdd = new List<EMP_EXAM_ANSWER_LOG>();
                    //修改审核流程信息
                    List<EMP_EXAM_ANSWER_LOG> listOperUpdate = new List<EMP_EXAM_ANSWER_LOG>();
                    //修改人员考试信息
                    List<EMP_EXAM_USER_ANSWER> listExamUserAnswerUpdate = new List<EMP_EXAM_USER_ANSWER>();
                    List<string> ids = new List<string>();
                    for (int i = 0; i < exam_userids.Split(',').Length; i++)
                    {
                        ids.Add(exam_userids.Split(',')[i]);
                    }
                    var res = _soa.Db.Queryable<EMP_EXAM_USER_ANSWER>().Where(p => ids.Contains(p.EXAM_USERID) && p.EXAM_USER_STATE != "10").ToList();

                    var resOper = _soa.Db.Queryable<EMP_EXAM_ANSWER_LOG>().Where(p => ids.Contains(p.EXAM_USERID) && p.OPER_STATE == "1").ToList();

                    if (res.Count() > 0)
                    {
                        var dt = DateTime.Now;
                        //  var resExam = _soa.Db.Queryable<EMP_EXAM_INFO>().Where(p => p.EXAM_ID == res[0].EXAM_ID).First();
                        foreach (var item in ids)
                        {
                            var dataExamUserAnswer = res.Where(p => p.EXAM_USERID == item).FirstOrDefault();
                            EMP_EXAM_ANSWER_LOG dataOper = new EMP_EXAM_ANSWER_LOG();
                            dataOper.EXAM_ANSWER_ID = IDGenHelper.CreateGuid();// _basedataService.GetTableMax("EMP_EXAM_ANSWER_LOG", 1, 1).data.ToString();
                                                                               //dataOper.EXAM_ANSWER_ID = _basedataService.GetTableMax("EMP_EXAM_ANSWER_LOG", 1, 1);
                            dataOper.EXAM_USERID = item;
                            dataOper.HOSPITAL_ID = hospital_id;
                            switch (exam_user_state)
                            {

                                case "8": //撤销
                                    //获取最后一条流程信息，修改成撤销状态
                                    var dataOperInfo = resOper.Where(p => p.EXAM_USERID == item).OrderByDescending(p => p.OPER_TIME).FirstOrDefault();
                                    bool markUp = _soa.Db.Queryable<EMP_EXAM_USER_ANSWER>().Where(w => w.USER_ID == dataExamUserAnswer.USER_ID && w.EXAM_ID == dataExamUserAnswer.EXAM_ID && w.EXAM_FLAG == 1).ToList().Count > 0;
                                    //只能谁操作谁撤销
                                    if (dataOperInfo != null && dataOperInfo.OPER_PERSON.Contains("_"))
                                    {
                                        string lastPerson = dataOperInfo.OPER_PERSON?.Split('_')?.FirstOrDefault();
                                        if (lastPerson != log_id)
                                        {
                                            rDto.success = false;
                                            rDto.msg = $"当前操作人{user_name}无法撤销{dataOperInfo.OPER_PERSON}{dataOperInfo.OPER_TYPE}的试卷";
                                            return rDto;
                                        }
                                    }
                                    if (dataOperInfo != null)
                                    {
                                        dataOperInfo.OPER_STATE = "0";
                                        listOperUpdate.Add(dataOperInfo);
                                    }
                                    if (dataExamUserAnswer.EXAM_USER_STATE == "5")
                                    {
                                        dataExamUserAnswer.EXAM_USER_STATE = "4";
                                    }
                                    if (dataExamUserAnswer.EXAM_USER_STATE == "7")
                                    {
                                        dataExamUserAnswer.EXAM_USER_STATE = "5";
                                    }
                                    if (dataExamUserAnswer.EXAM_USER_STATE == "6")
                                    {
                                        dataExamUserAnswer.EXAM_USER_STATE = "4";
                                    }
                                    //撤销关联证书
                                    if (dataExamUserAnswer.CER_ID == $"EXAM_{dataExamUserAnswer.EXAM_USERID}")
                                    {
                                        _IPmsService.BatchSubmitRecordInfo($"EXAM_{dataExamUserAnswer.EXAM_USERID}", "PMS_SKILL_CERTIFICATE_LIST", user_name, hospital_id, "R", check_person, log_id, oper_cause, pwd_bs);
                                    }
                                    break;
                                case "6": //驳回
                                    if (dataExamUserAnswer.EXAM_USER_STATE != "5")//审核驳回
                                    {
                                        rDto.success = false;
                                        rDto.msg = "只有状态为“提交”的评卷才可驳回";
                                    }
                                    else if (oper_cause.IsNullOrEmpty())
                                    {
                                        rDto.success = false;
                                        rDto.msg = "驳回原因不能为空";
                                    }
                                    else
                                    {
                                        dataOper.EXAM_ANSWER_ID = IDGenHelper.CreateGuid();// _basedataService.GetTableMax("EMP_EXAM_ANSWER_LOG", 1, 1).data.ToString();
                                                                                           //dataOper.EXAM_ANSWER_ID = _basedataService.GetTableMax("EMP_EXAM_ANSWER_LOG", 1, 1);
                                        dataOper.EXAM_USERID = item;
                                        dataOper.HOSPITAL_ID = hospital_id;
                                        dataOper.OPER_TYPE = "审核驳回";
                                        dataOper.CAUSE_TYPE = "审核驳回";
                                        dataOper.OPER_CAUSE = oper_cause;
                                        dataOper.OPER_TIME = dt;
                                        dataOper.OPER_PERSON = log_id + "_" + user_name;
                                        dataOper.OPER_COMPUTER = oper_computer;
                                        dataOper.OPER_STATE = "1";
                                        listOperAdd.Add(dataOper);
                                        dataExamUserAnswer.EXAM_USER_STATE = "6";
                                    }
                                    break;
                                case "5": //提交
                                    if (dataExamUserAnswer.EXAM_USER_STATE == exam_user_state)
                                    {
                                        rDto.success = false;
                                        rDto.msg = "该试卷已提交评卷审核信息,无法重复提交!";
                                    }
                                    if (dataExamUserAnswer.EXAM_USER_STATE == "7")
                                    {
                                        rDto.success = false;
                                        rDto.msg = "已审核通过的评卷无法提交";
                                    }
                                    else if (resOper.Where(p => p.OPER_TYPE != null && p.OPER_TYPE.Contains("提交")).Count() > 0)
                                    {
                                        dataOper.OPER_TYPE = "重新提交";
                                        dataOper.CAUSE_TYPE = "重新提交";
                                        dataExamUserAnswer.CHECK_PERSON = check_person;
                                    }
                                    else
                                    {
                                        dataOper.OPER_TYPE = "提交";
                                        dataOper.CAUSE_TYPE = "提交";
                                        dataExamUserAnswer.MARK_EP_PERSON = check_person;
                                        dataExamUserAnswer.MARK_EP_TIME = dt;
                                        dataExamUserAnswer.CHECK_PERSON = check_person;
                                    }
                                    dataExamUserAnswer.EXAM_USER_STATE = exam_user_state;

                                    //提交关联证书
                                    if (dataExamUserAnswer.CER_ID == $"EXAM_{dataExamUserAnswer.EXAM_USERID}")
                                    {
                                      _IPmsService.BatchSubmitRecordInfo($"EXAM_{dataExamUserAnswer.EXAM_USERID}", "PMS_SKILL_CERTIFICATE_LIST", user_name, hospital_id, "B", check_person, log_id, oper_cause, pwd_bs);
                                    }

                                    break;
                                case "7": //审核
                                    if (dataExamUserAnswer.EXAM_USER_STATE == exam_user_state)
                                    {
                                        rDto.success = false;
                                        rDto.msg = "该试卷评卷信息已审核,无法重复审核!";
                                    }
                                    if (dataExamUserAnswer.EXAM_USER_STATE != "5")
                                    {
                                        rDto.success = false;
                                        rDto.msg = "只有状态为“提交”的评卷才可审核";
                                    }
                                    else
                                    {
                                        dataOper.OPER_TYPE = "通过";
                                        dataOper.CAUSE_TYPE = "通过";
                                        dataOper.OPER_CAUSE = "同意";
                                        dataExamUserAnswer.CHECK_PERSON = check_person;
                                        dataExamUserAnswer.CHECK_TIME = dt;
                                        dataExamUserAnswer.CHECK_OPINION = oper_cause;
                                    }
                                    dataExamUserAnswer.EXAM_USER_STATE = exam_user_state;
                                    break;
                                case "10": //删除
                                    if (dataExamUserAnswer.EXAM_USER_STATE != "4")
                                    {
                                        rDto.success = false;
                                        rDto.msg = "只有状态为未提交的考试才可删除";
                                    }
                                    else
                                    {
                                        dataOper.OPER_TYPE = "通过";
                                        dataOper.CAUSE_TYPE = "通过";
                                        dataOper.OPER_CAUSE = "同意";
                                    }
                                    dataExamUserAnswer.EXAM_USER_STATE = exam_user_state;
                                    break;
                                default:
                                    break;
                            }

                            dataOper.OPER_TIME = dt;
                            dataOper.OPER_PERSON = log_id + "_" + user_name;
                            dataOper.OPER_COMPUTER = oper_computer;
                            dataOper.OPER_STATE = "1";
                            listOperAdd.Add(dataOper);
                            dataExamUserAnswer.LAST_MPERSON = user_name;
                            dataExamUserAnswer.LAST_MTIME = dt;
                            listExamUserAnswerUpdate.Add(dataExamUserAnswer);


                        }
                        if (listExamUserAnswerUpdate.Count() > 0)
                        {
                            rowCount = _soa.Db.Updateable(listExamUserAnswerUpdate).ExecuteCommand();
                        }
                        if (listOperAdd.Count() > 0)
                        {
                            rowCount = _soa.Db.Insertable(listOperAdd).ExecuteCommand();
                        }
                        if (listOperUpdate.Count() > 0)
                        {
                            rowCount = _soa.Db.Updateable(listOperUpdate).ExecuteCommand();
                        }
                        if (rowCount == 0)
                        {
                            rDto.msg = "操作失败" + (rDto.msg.IsNullOrEmpty() ? "" : $"-{rDto.msg}");
                            rDto.success = false;
                        }
                    }
                    else
                    {
                        rDto.success = false;
                        rDto.msg = "没有找到要操作的数据";
                    }
                }
                else
                {
                    rDto.success = false;
                    rDto.msg = "密码输入错误";
                }
            }
            catch (Exception ex)
            {
                rDto.msg = ex.Message;
                rDto.success = false;
                _logger.LogError($"UpdateExamUserAnswerInfo:{ex.ToString()}");
            }
            return rDto;
        }


        /// <summary>
        /// 保存考试规评明细登记
        /// </summary>
        /// <param name="stdSchemeDto"></param>
        /// <param name="userName"></param>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        public ResultDto SaveExamStdSchemeDetail(List<ExamStdSchemeDto> listsStdSchemeDto, string userName, string hospitalId)
        {
            ResultDto rDto = new ResultDto();
            List<EMP_EXAM_USER_ANSWER> listExamUserAnswer = _mapper.Map<List<EMP_EXAM_USER_ANSWER>>(listsStdSchemeDto);
            if (listExamUserAnswer.Count <= 0)
            {
                rDto.success = false;
                rDto.msg = "参数为空";
                return rDto;
            }
            List<EMP_EXAM_USER_ANSWER> varUserAnswer = _soa.Db.Queryable<EMP_EXAM_USER_ANSWER>().Where(w => w.STD_GROUP_ID == listsStdSchemeDto[0].STD_GROUP_ID).ToList();
            EMP_EXAM_USER_ANSWER stdGroup = varUserAnswer.Find(w => w.USER_ID == "STD_EXAM");
            if (stdGroup == null)
            {
                rDto.success = false;
                rDto.msg = "查询分组失败！";
                return rDto;
            }
            List<EMP_EXAM_USER_ANSWER> listAdd = new List<EMP_EXAM_USER_ANSWER>();
            List<EMP_EXAM_USER_ANSWER> listUpdate = new List<EMP_EXAM_USER_ANSWER>();
            List<SYS6_USER> listGroup = _soa.Db.Queryable<SYS6_USER>().Where(w => w.STATE_FLAG == "1").ToList();
            var dataClass = _IBaseDataServices.GetOaBaseData().FindAll(x => x.CLASS_ID == "考试评价结果" && (x.HOSPITAL_ID == _httpContext.GetHospitalId() || x.HOSPITAL_ID == "H0000")).OrderBy(x => x.DATA_SORT).ToList();
            foreach (var item in listExamUserAnswer)
            {
                item.EXAM_USERID = varUserAnswer?.Find(w => w.USER_ID == item.USER_ID)?.EXAM_USERID;
                if (item.EXAM_USERID.IsNullOrEmpty())
                {
                    item.STD_GROUP_ID = stdGroup.STD_GROUP_ID;
                    item.STD_SCHEME_ID = stdGroup.STD_SCHEME_ID;
                    item.SMBL_FLAG = stdGroup.SMBL_FLAG;
                    item.EXAM_NAME = stdGroup.EXAM_NAME;
                    item.EXAM_START_DATE = stdGroup.EXAM_START_DATE;
                    item.TRAIN_ID = stdGroup.TRAIN_ID;
                    item.CER_ID = stdGroup.CER_ID;
                    item.EXAM_USERID = IDGenHelper.CreateGuid();
                    item.EXAM_ID = stdGroup.EXAM_ID;
                    item.SOURCE_TYPE = "1";
                    item.EXAM_NUM = "1";
                    item.PAPER_ID = stdGroup.PAPER_ID;
                    //每个人所选的规评所属分组id
                    item.FIRST_RPERSON = userName;
                    item.FIRST_RTIME = DateTime.Now;
                    item.EXAM_USER_STATE = "7";
                    if (item.PGROUP_ID.IsNullOrEmpty())
                        item.PGROUP_ID = _soa.Db.Queryable<SYS6_USER>().Where(w => w.USER_NO == item.USER_ID).ToList().FirstOrDefault()?.DEPT_CODE;
                    listAdd.Add(item);
                }
                else
                {
                    item.STD_GROUP_ID = stdGroup.STD_GROUP_ID;
                    item.STD_SCHEME_ID = stdGroup.STD_SCHEME_ID;
                    item.SMBL_FLAG = stdGroup.SMBL_FLAG;
                    item.EXAM_NAME = stdGroup.EXAM_NAME;
                    item.EXAM_START_DATE = stdGroup.EXAM_START_DATE;
                    item.TRAIN_ID = stdGroup.TRAIN_ID;
                    item.CER_ID = stdGroup.CER_ID;
                    item.SOURCE_TYPE = "1";
                    //item.EXAM_USERID = IDGenHelper.CreateGuid();
                    item.EXAM_ID = stdGroup.EXAM_ID;
                    item.EXAM_NUM = "1";
                    item.PAPER_ID = stdGroup.PAPER_ID;
                    item.EXAM_USER_STATE = "7";
                    //每个人所选的规评所属分组id
                    item.LAST_MPERSON = userName;
                    item.LAST_MTIME = DateTime.Now;
                    listUpdate.Add(item);
                }
            }
            int count = 0;
            if (listAdd.Count > 0)
                count = _soa.Db.Insertable(listAdd).ExecuteCommand();
            if (listUpdate.Count > 0)
                count = _soa.Db.Updateable(listUpdate).IgnoreColumns(w => new { w.FIRST_RPERSON, w.FIRST_RTIME, w.EVALUATE_AFFIX }).ExecuteCommand();
            //写入规评结果  
            List<EMP_EXAM_USER_ANSWER> listTemp = new List<EMP_EXAM_USER_ANSWER>();
            listTemp.AddRange(listAdd.FindAll(w => w.COMPREHENSIVE_ASSESS.IsNotNullOrEmpty()));
            listTemp.AddRange(listUpdate.FindAll(w => w.COMPREHENSIVE_ASSESS.IsNotNullOrEmpty()));
            List<EvaluatePlanUserResultParm> listParm = new List<EvaluatePlanUserResultParm>();
            foreach (var item in listTemp)
            {
                EvaluatePlanUserResultParm evaluatePlanUserResultParm = new EvaluatePlanUserResultParm();
                evaluatePlanUserResultParm.EPLAN_ID = item.STD_SCHEME_ID;
                evaluatePlanUserResultParm.USER_ID = item.USER_ID;
                evaluatePlanUserResultParm.DATA_CLASS = "考试规评登记";
                evaluatePlanUserResultParm.DATA_ID = item.EXAM_USERID;
                evaluatePlanUserResultParm.RESULT = dataClass.Find(w => w.DATA_ID == item.COMPREHENSIVE_ASSESS)?.DATA_NAME == "不合格" ? EPlanResultEnum.FAIL : EPlanResultEnum.PASS;
                evaluatePlanUserResultParm.AFFECT_DATE = DateTime.Now;
                listParm.Add(evaluatePlanUserResultParm);
            }
            _IEvaluatePlanService.WriteEvaluatePlanUserResult(listParm);
            rDto.success = count > 0;
            return rDto;
        }




        /// <summary>
        /// 保存考试规评登记
        /// </summary>
        /// <param name="stdSchemeDto"></param>
        /// <param name="userName"></param>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        public ResultDto SaveExamStdScheme(ExamStdSchemeDto stdSchemeDto, string userName, string hospitalId)
        {
            ResultDto rDto = new ResultDto();
            EMP_EXAM_USER_ANSWER examUserAnswer = _mapper.Map<EMP_EXAM_USER_ANSWER>(stdSchemeDto);
            string smblFlag = _httpContext.GetSmblFlag();
            int count;
            if (examUserAnswer.EXAM_USERID.IsNullOrEmpty())
            {
                examUserAnswer.EXAM_USERID = IDGenHelper.CreateGuid();
                examUserAnswer.STD_GROUP_ID = IDGenHelper.CreateGuid();
                examUserAnswer.USER_ID = "STD_EXAM";//固定写死 查询使用
                examUserAnswer.FIRST_RPERSON = userName;
                examUserAnswer.FIRST_RTIME = DateTime.Now;
                examUserAnswer.EXAM_USER_STATE = "4";
                examUserAnswer.EXAM_NUM = "1";
                examUserAnswer.EXAM_ID = IDGenHelper.CreateGuid();
                examUserAnswer.PAPER_ID = IDGenHelper.CreateGuid();
                if (smblFlag == "1")
                    examUserAnswer.SMBL_FLAG = smblFlag;
                //证书新增一条证书记录 证书id绑定到CER_ID
                if (stdSchemeDto.CERTIFICATE_TYPE.IsNotNullOrEmpty())
                {
                    PMS_SKILL_CERTIFICATE_LIST skillCer = new PMS_SKILL_CERTIFICATE_LIST();
                    skillCer.PERSON_ID = examUserAnswer.EXAM_USERID;
                    skillCer.CERTIFICATE_ID = IDGenHelper.CreateGuid();
                    skillCer.CERTIFICATE_TYPE = stdSchemeDto.CERTIFICATE_TYPE;
                    skillCer.CERTIFICATE_DATE = stdSchemeDto.CERTIFICATE_DATE;
                    skillCer.CERTIFICATE_UNIT = stdSchemeDto.CERTIFICATE_UNIT;
                    skillCer.CERTIFICATE_SORT = EntityHelper.GetSort();
                    bool success = _soa.Db.Insertable(skillCer).ExecuteCommand() > 0;
                    if (success)
                        examUserAnswer.CER_ID = skillCer.CERTIFICATE_ID;
                    else
                    {
                        rDto.success = false;
                        rDto.msg = "保存资质证书失败";
                        return rDto;
                    }
                }
                count = _soa.Db.Insertable(examUserAnswer).ExecuteCommand();
                rDto.success = count > 0;
            }
            else
            {
                if (examUserAnswer.CER_ID.IsNotNullOrEmpty())
                {
                    PMS_SKILL_CERTIFICATE_LIST skillCer = _soa.Db.Queryable<PMS_SKILL_CERTIFICATE_LIST>().Where(w => w.CERTIFICATE_ID == examUserAnswer.CER_ID).ToList().FirstOrDefault();
                    if (skillCer != null && (skillCer.CERTIFICATE_TYPE != stdSchemeDto.CERTIFICATE_TYPE ||
                        skillCer.CERTIFICATE_UNIT != stdSchemeDto.CERTIFICATE_UNIT ||
                        skillCer.CERTIFICATE_DATE != stdSchemeDto.CERTIFICATE_DATE))
                    {
                        skillCer.CERTIFICATE_TYPE = stdSchemeDto.CERTIFICATE_TYPE;
                        skillCer.CERTIFICATE_DATE = stdSchemeDto.CERTIFICATE_DATE;
                        skillCer.CERTIFICATE_UNIT = stdSchemeDto.CERTIFICATE_UNIT;
                        _soa.Db.Updateable(skillCer).ExecuteCommand();
                    }
                }
                //证书新增一条证书记录 证书id绑定到CER_ID
                else if (stdSchemeDto.CERTIFICATE_TYPE.IsNotNullOrEmpty())
                {
                    PMS_SKILL_CERTIFICATE_LIST skillCer = new PMS_SKILL_CERTIFICATE_LIST();
                    skillCer.PERSON_ID = examUserAnswer.EXAM_USERID;
                    skillCer.CERTIFICATE_ID = IDGenHelper.CreateGuid();
                    skillCer.CERTIFICATE_TYPE = stdSchemeDto.CERTIFICATE_TYPE;
                    skillCer.CERTIFICATE_DATE = stdSchemeDto.CERTIFICATE_DATE;
                    skillCer.CERTIFICATE_UNIT = stdSchemeDto.CERTIFICATE_UNIT;
                    skillCer.CERTIFICATE_SORT = EntityHelper.GetSort();
                    bool success = _soa.Db.Insertable(skillCer).ExecuteCommand() > 0;
                    if (success)
                        examUserAnswer.CER_ID = skillCer.CERTIFICATE_ID;
                }
                examUserAnswer.LAST_MPERSON = userName;
                examUserAnswer.LAST_MTIME = DateTime.Now;
                count = _soa.Db.Updateable(examUserAnswer).IgnoreColumns(w => new { w.EXAM_ID, w.STD_GROUP_ID, w.PAPER_ID, w.FIRST_RPERSON, w.USER_ID, w.FIRST_RTIME, w.EXAM_NUM, w.EXAM_USER_STATE }).ExecuteCommand();
            }
            Dictionary<string, object> dicReturn = new Dictionary<string, object>();
            dicReturn.Add("COUNT", count);
            dicReturn.Add("MAXID", examUserAnswer.EXAM_USERID);
            rDto.success = count > 0;
            rDto.data = dicReturn;
            return rDto;
        }

        /// <summary>
        /// 获取规评信息
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="labGroupId"></param>
        /// <param name="eplanName"></param>
        /// <param name="examName"></param>
        /// <returns></returns>

        public ResultDto GetExamStd(string startDate, string endDate, string labGroupId, string eplanName, string examName, string smblFlag)
        {
            string hospitalId = _httpContext.GetHospitalId();
            string labId = _httpContext.GetLabId();
            ResultDto resultDto = new ResultDto();
            if (_httpContext.GetSmblFlag() == "1")
                smblFlag = "1";
            var varExamInfo = _soa.Db.Queryable<EMP_EXAM_USER_ANSWER>()
                 .InnerJoin<OA_EVALUATE_PLAN_DICT>((a, b) => a.STD_SCHEME_ID == b.EPLAN_ID && b.EPLAN_STATE == "1" && b.HOSPITAL_ID == hospitalId)
                .LeftJoin<OA_EVALUATE_PLAN_SETUP>((a, b, c) => a.STD_SCHEME_ID == c.EPLAN_ID && c.EPLAN_SSTATE == "1")
                .LeftJoin<PMS_SKILL_CERTIFICATE_LIST>((a, b, c, d) => a.CER_ID == d.CERTIFICATE_ID)
                .Where((a, b, c) => a.USER_ID == "STD_EXAM" && a.EXAM_USER_STATE != "10")
                .WhereIF(smblFlag == "1", (a, b, c) => a.SMBL_FLAG == smblFlag)
                .WhereIF(labGroupId.IsNotNullOrEmpty(), (a, b, c) => a.LAB_ID == labGroupId || a.PGROUP_ID == labGroupId)
                .WhereIF(eplanName.IsNotNullOrEmpty(), (a, b, c) => b.EPLAN_NAME.Contains(eplanName))
                .WhereIF(examName.IsNotNullOrEmpty(), (a, b, c) => (a.EXAM_NAME != null && a.EXAM_NAME.Contains(examName)) || b.EPLAN_NAME.Contains(examName))
                .Select((a, b, c, d) => new ExamStdSchemeDto()
                {
                    EXAM_USERID = a.EXAM_USERID,
                    USER_ID = a.USER_ID,
                    EXAM_USER_STATE = a.EXAM_USER_STATE,
                    EXAM_SCORE = a.EXAM_SCORE,
                    LAB_ID = a.LAB_ID,
                    PGROUP_ID = a.PGROUP_ID,
                    EPLAN_NAME = b.EPLAN_NAME,
                    COMPREHENSIVE_ASSESS = a.COMPREHENSIVE_ASSESS,
                    EPLAN_APPLY_TYPE = c.EPLAN_APPLY_TYPE,
                    EXAM_NAME = a.EXAM_NAME,
                    EXAM_START_DATE = a.EXAM_START_DATE,
                    STD_GROUP_ID = a.STD_GROUP_ID,
                    SMBL_FLAG = a.SMBL_FLAG,
                    STD_SCHEME_ID = a.STD_SCHEME_ID,
                    SHELF_LIFE_UTYPE = c.SHELF_LIFE_UTYPE,
                    EPLAN_SHELF_LIFE = c.EPLAN_SHELF_LIFE,
                    WARN_DURATION = c.WARN_DURATION,
                    WARN_UTYPE = c.WARN_UTYPE,
                    EVALUATE_AFFIX = a.EVALUATE_AFFIX,
                    CER_ID = d.CERTIFICATE_ID,
                    CERTIFICATE_TYPE = d.CERTIFICATE_TYPE,
                    CERTIFICATE_UNIT = d.CERTIFICATE_UNIT,
                    CERTIFICATE_DATE = d.CERTIFICATE_DATE,
                    CERTIFICATE_VALIDITY = d.CERTIFICATE_VALIDITY
                })
                .ToList();
            if (startDate.IsNotNullOrEmpty() && endDate.IsNotNullOrEmpty())
                varExamInfo = varExamInfo.FindAll(w => Convert.ToDateTime(startDate) <= w.EXAM_START_DATE && w.EXAM_START_DATE < Convert.ToDateTime(endDate));
            List<SYS6_INSPECTION_LAB> listLab = _soa.Db.Queryable<SYS6_INSPECTION_LAB>().Where(w => w.STATE_FLAG == "1").ToList();
            List<SYS6_INSPECTION_PGROUP> listGroup = _soa.Db.Queryable<SYS6_INSPECTION_PGROUP>().Where(w => w.PGROUP_STATE == "1").ToList();
            List<ExamStdSchemeDto> listDto = _mapper.Map<List<ExamStdSchemeDto>>(varExamInfo);
            List<string> listGroupId = listDto.Select(w => w.STD_GROUP_ID).Distinct().ToList();
            var varExamInfoDetail = _soa.Db.Queryable<EMP_EXAM_USER_ANSWER>().Where(w => listGroupId.Contains(w.STD_GROUP_ID) && w.USER_ID != "STD_EXAM" && w.EXAM_USER_STATE != "10").ToList();
            List<PMS_PERSON_FILE> pmsPersonFile = _soa.Db.Queryable<PMS_PERSON_FILE>().Where(p => p.FILE_STATE == "1").Select<PMS_PERSON_FILE>().ToList();
            foreach (var item in listDto)
            {
                //规评方案的专业组id==规评计划的专业组id则为专业组 取专业组名称
                if (item.DICT_PGROUP_ID.IsNotNullOrEmpty() &&
                    item.PGROUP_ID.IsNotNullOrEmpty() &&
                    item.DICT_PGROUP_ID == item.PGROUP_ID)
                    item.LAB_ID = "";
                else
                    item.LAB_NAME = listLab.Find(w => w.LAB_ID == item.LAB_ID)?.LAB_NAME;
                item.PGROUP_NAME = listGroup.Find(w => w.PGROUP_ID == item.PGROUP_ID)?.PGROUP_NAME;
                var detail = varExamInfoDetail.FindAll(w => w.STD_GROUP_ID == item.STD_GROUP_ID);
                //是否全部完成 填入评价
                bool state = detail.Count > 0 && detail.All(p => !string.IsNullOrEmpty(p.COMPREHENSIVE_ASSESS));
                if (state)
                    item.STATE_NAME = "已完成";
                else
                    item.STATE_NAME = "未完成";

                string resume_affix_name = string.Empty;
                if (item.EVALUATE_AFFIX.IsNotNullOrEmpty())
                {
                    string[] AFFIXARRY = item.EVALUATE_AFFIX.Split(",");
                    List<PMS_PERSON_FILE> pmsPersonFileReward = pmsPersonFile.Where(p => AFFIXARRY.Contains(p.FILE_ID)).ToList();
                    for (int i = 0; i < AFFIXARRY.Length; i++)
                    {
                        var PersonFile = pmsPersonFileReward.FirstOrDefault(p => p.FILE_ID == AFFIXARRY[i]);
                        if (PersonFile != null)
                        {
                            _IPmsService.FillPersonFileInfo(PersonFile);
                            item.PMS_PERSON_FILE = pmsPersonFileReward;
                            resume_affix_name += PersonFile.FILE_NAME + ",";
                        }
                    }
                    if (resume_affix_name != "")
                    {
                        item.AFFIX_NAME = resume_affix_name.Substring(0, resume_affix_name.Length - 1);
                    }
                }
            }
            listDto = listDto.FindAll(w => (w.LAB_ID.IsNullOrEmpty() || w.LAB_ID == labId));
            resultDto.data = listDto;
            return resultDto;
        }


        /// <summary>
        /// 获取规评分组明细
        /// </summary>
        /// <param name="stdGroupId"></param>
        /// <returns></returns>
        public ResultDto GetExamStdDetail(string stdGroupId, string labPgroupId, string comName, string userName)
        {
            string hospitalId = _httpContext.GetHospitalId();
            ResultDto resultDto = new ResultDto();
            var varExamInfo = _soa.Db.Queryable<EMP_EXAM_USER_ANSWER>()
                 .Where(w => w.STD_GROUP_ID == stdGroupId && w.USER_ID != "STD_EXAM" && w.EXAM_USER_STATE != "10")
                 .WhereIF(labPgroupId.IsNotNullOrEmpty(), w => w.LAB_ID == labPgroupId || w.PGROUP_ID == labPgroupId)
                .Select(a => new ExamStdSchemeDto()
                {
                    EXAM_USERID = a.EXAM_USERID,
                    USER_ID = a.USER_ID,
                    EXAM_USER_STATE = a.EXAM_USER_STATE,
                    EXAM_SCORE = a.EXAM_SCORE,
                    LAB_ID = a.LAB_ID,
                    PGROUP_ID = a.PGROUP_ID,
                    COMPREHENSIVE_ASSESS = a.COMPREHENSIVE_ASSESS,
                    EXAM_NAME = a.EXAM_NAME,
                    EXAM_START_TIME = a.EXAM_START_TIME,
                    EVALUATE_AFFIX = a.EVALUATE_AFFIX
                })
                .ToList();
            List<SYS6_INSPECTION_LAB> listLab = _soa.Db.Queryable<SYS6_INSPECTION_LAB>().Where(w => w.STATE_FLAG == "1").ToList();
            List<SYS6_INSPECTION_PGROUP> listGroup = _soa.Db.Queryable<SYS6_INSPECTION_PGROUP>().Where(w => w.PGROUP_STATE == "1").ToList();
            List<PMS_PERSON_FILE> pmsPersonFile = _soa.Db.Queryable<PMS_PERSON_FILE>().Where(p => p.FILE_STATE == "1").Select<PMS_PERSON_FILE>().ToList();
            //获取考生组合信息
            var userCom = _soa.Db.Queryable<SYS6_USER_COM>().Where(p => p.HOSPITAL_ID == hospitalId && p.USER_COM_STATE == "1").ToList();
            userCom = userCom.Where(p => p.USER_SNO.IsNotNullOrEmpty()).ToList();
            List<ExamStdSchemeDto> listDto = _mapper.Map<List<ExamStdSchemeDto>>(varExamInfo);
            List<string> listUserId = listDto.Select(w => w.USER_ID).Distinct().ToList();
            List<SYS6_USER> listUser = _soa.Db.Queryable<SYS6_USER>().Where(w => listUserId.Contains(w.USER_NO)).ToList();
            foreach (var item in listDto)
            {
                item.LAB_NAME = listLab.Find(w => w.LAB_ID == item.LAB_ID)?.LAB_NAME;
                item.PGROUP_NAME = listGroup.Find(w => w.PGROUP_ID == item.PGROUP_ID)?.PGROUP_NAME;
                item.HIS_ID = listUser.Find(w => w.USER_NO == item.USER_ID)?.HIS_ID;
                item.USER_NAME = listUser.Find(w => w.USER_NO == item.USER_ID)?.USERNAME;
                if (userCom.Count() > 0)
                {
                    var dataCom = userCom.Where(p => p.USER_SNO.Split('+').Contains(item.USER_ID)).FirstOrDefault();
                    if (dataCom != null)
                    {
                        item.USER_COM_NAME = dataCom.USER_COM_NAME;
                    }
                }
                string resume_affix_name = string.Empty;
                if (item.EVALUATE_AFFIX.IsNotNullOrEmpty())
                {
                    string[] AFFIXARRY = item.EVALUATE_AFFIX.Split(",");
                    List<PMS_PERSON_FILE> pmsPersonFileReward = pmsPersonFile.Where(p => AFFIXARRY.Contains(p.FILE_ID)).ToList();
                    for (int i = 0; i < AFFIXARRY.Length; i++)
                    {
                        var PersonFile = pmsPersonFileReward.FirstOrDefault(p => p.FILE_ID == AFFIXARRY[i]);
                        if (PersonFile != null)
                        {
                            _IPmsService.FillPersonFileInfo(PersonFile);
                            item.PMS_PERSON_FILE = pmsPersonFileReward;
                            resume_affix_name += PersonFile.FILE_NAME + ",";
                        }
                    }
                    if (resume_affix_name != "")
                    {
                        item.AFFIX_NAME = resume_affix_name.Substring(0, resume_affix_name.Length - 1);
                    }
                }
            }
            if (userName.IsNotNullOrEmpty())
                listDto = listDto.FindAll(w => w.USER_NAME != null && w.USER_NAME.Contains(userName));
            if (comName.IsNotNullOrEmpty())
                listDto = listDto.FindAll(w => w.USER_COM_NAME != null && w.USER_COM_NAME.Contains(comName));
            resultDto.data = listDto;
            return resultDto;
        }

        /// <summary>
        /// 删除规评明细
        /// </summary>
        /// <param name="stdGroupId"></param>
        /// <param name="listUserId"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        public ResultDto DeleteExamStdDetail(string stdGroupId, List<string> listUserId, string userName)
        {
            if (listUserId == null || listUserId.Count == 0)
                return new ResultDto();
            ResultDto resultDto = new ResultDto();
            var userAnswer = _soa.Db.Queryable<EMP_EXAM_USER_ANSWER>()
                  .Where(w => w.STD_GROUP_ID == stdGroupId && listUserId.Contains(w.USER_ID))
                 .ToList();
            userAnswer.ForEach(w => w.EXAM_USER_STATE = "10");
            resultDto.success = _soa.Db.Updateable(userAnswer).ExecuteCommand() > 0;
            //写入规评结果  
            List<EvaluatePlanUserResultParm> listParm = new List<EvaluatePlanUserResultParm>();
            userAnswer = userAnswer.FindAll(w => w.COMPREHENSIVE_ASSESS.IsNotNullOrEmpty());
            if (userAnswer.Count > 0)
            {
                foreach (var item in userAnswer)
                {
                    EvaluatePlanUserResultParm evaluatePlanUserResultParm = new EvaluatePlanUserResultParm();
                    evaluatePlanUserResultParm.EPLAN_ID = item.STD_SCHEME_ID;
                    evaluatePlanUserResultParm.USER_ID = item.USER_ID;
                    evaluatePlanUserResultParm.DATA_CLASS = "考试规评登记";
                    evaluatePlanUserResultParm.DATA_ID = item.EXAM_USERID;
                    evaluatePlanUserResultParm.RESULT = EPlanResultEnum.DISABLE;
                    evaluatePlanUserResultParm.AFFECT_DATE = DateTime.Now;
                    listParm.Add(evaluatePlanUserResultParm);
                }
                _IEvaluatePlanService.WriteEvaluatePlanUserResult(listParm);
            }
            return resultDto;
        }
    }
}
