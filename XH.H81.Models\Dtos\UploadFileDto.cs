﻿//using Microsoft.AspNetCore.Http;
//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Text;
//using System.Threading.Tasks;

//namespace XH.H81.Models.Dtos
//{
//    public class UploadFileDto
//    {

//        public UploadFileDto()
//        {
//            IFCOVER = true;
//            SAVE_TO_S28 = true;
//        }
//        /// <summary>
//        /// 文档类型
//        /// </summary>
//        public string? FILE_TYPE { get; set; }
//        /// <summary>
//        /// 文件名称（不含后缀）
//        /// </summary>
//        public string? FILE_NAME { get; set; }

//        /// <summary>
//        /// 文件后缀
//        /// </summary>
//        public string? FILE_SUFFIX { get; set; }

//        /// <summary>
//        /// 是否覆盖
//        /// </summary>
//        public bool IFCOVER { get; set; }

//        /// <summary>
//        /// 是否保存到S28
//        /// </summary>
//        public bool SAVE_TO_S28 { get; set; }

//        /// <summary>
//        /// 文件路径
//        /// </summary>
//        public string? FILE_URL { get; set; }

//        /// <summary>
//        /// 原始文件的Base64字符
//        /// </summary>
//        public string? FILEBASE64 { get; set; }

//        /// <summary>
//        /// 采用multipart/form-data方式上传文件（如果form-data不为空，则优化使用该方式上传）
//        /// </summary>
//        public IFormFile? FILE { get; set; }

//        /// <summary>
//        /// 真正上传【S28接口】的文件名称(一版是GUID加上后缀)
//        /// </summary>
//        public string? UPLOAD_FILE_NAME { get; set; }

//        /// <summary>
//        /// 文件夹（相对路径，前后不含斜杆，不含文件名，真正上传【S28接口】时对应的参数folderName）
//        /// </summary>
//        public string? UPLOAD_FOLDER_NAME { get; set; }

//        /// <summary>
//        /// 真正上传【S28接口】的二进制数据。如果为空，则使用FILE字段直接上传
//        /// </summary>
//        public byte[]? UPLOAD_BYTES { get; set; }

//    }
//}
