﻿using H.Utility;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XH.H81.Models.Dtos.Exam;
using XH.H81.Models.Entities.Pms;

namespace XH.H81.IServices
{
    public interface IPmsAssessService
    {
        /// <summary>
        /// 获取评估信息
        /// </summary>
        /// <param name="userNo">用户id</param>
        /// <param name="assessPlanState">状态</param>
        /// <param name="assessPlanType">评估类型</param>
        /// <param name="assessPlanName">评估名称</param>
        /// <param name="smblFlag">生安标志</param>
        /// <param name="hospitalId">机构id</param>
        /// <returns></returns>
        ResultDto GetUserAssessInfo(string userNo, string? labId, string? mgroupId, string? pgroupId, string? assessPlanState, string? assessPlanType, string assessPlanName, string? smblFlag, string? dateType, string? startDate, string? endDate,string cerId, string hospitalId);


        /// <summary>
        /// 获取规评信息
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="labGroupId"></param>
        /// <param name="eplanName"></param>
        /// <param name="examName"></param>
        /// <returns></returns>

        ResultDto GetPmsAssessStd(string startDate, string endDate, string labGroupId, string eplanName, string assessName, string smblFlag);

        /// <summary>
        /// 获取评估规评明细
        /// </summary>
        /// <param name="stdGroupId"></param>
        /// <returns></returns>
        ResultDto GetPmsAssessStdDetail(string stdGroupId, string labPgroupId, string comName, string userName);

        /// <summary>
        /// 保存评估记录
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="userName"></param> 
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        ResultDto SaveAssessInfo(PmsAssessPlanPersonDto dto,string userName,string hospitalId);

        /// <summary>
        /// 更新评估记录
        /// </summary>
        /// <param name="planpPersonIds">计划人员评估id合集</param>
        /// <param name="assessPlanType">操作状态</param>
        /// <param name="userName"></param>
        /// <param name="hospitalId"></param>
        /// <param name="checkPerson"></param>
        /// <param name="operCause"></param>
        /// <param name="operComputer"></param>
        /// <param name="pwd"></param>
        /// <param name="logId"></param>
        /// <returns></returns>
        ResultDto UpdateAssessPlanInfo(string? planpPersonIds, string? assessPlanType, string? userName, string? hospitalId, string? checkPerson, string? operCause, string? operComputer, string? pwd, string? logId);

        /// <summary>
        /// 保存评估规评明细
        /// </summary>
        /// <param name="pmsAssessPlanPersonDtos"></param>
        /// <param name="userName"></param>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        ResultDto SavePmsAssessStdSchemeDetail(List<PmsAssessPlanPersonDto> pmsAssessPlanPersonDtos, string userName, string hospitalId);

        ResultDto SavePmsAssessStdScheme(PmsAssessPlanPersonDto pmsAssessPlanPersonDto, string userName, string hospitalId);

        /// <summary>
        /// 刪除规评明细
        /// </summary>
        /// <param name="stdGroupId"></param>
        /// <param name="listUserId"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        ResultDto DeletePmsAssessStdDetail(string stdGroupId, List<string> listUserId, string userName);
    }
}
