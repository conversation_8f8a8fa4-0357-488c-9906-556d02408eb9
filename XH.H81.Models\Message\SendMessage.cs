﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Message
{
    /// <summary>
    /// 已经发送的消息
    /// </summary>
    public class SendMessage
    {
        /// <summary>
        /// 
        /// </summary>
        public string MSG_ID { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string MSG_CORRID { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string HOSPITAL_ID { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string AREA_ID { get; set; }
        /// <summary>
        /// 延1438分  参加考试提醒通知
        /// </summary>
        public string MSG_NAME { get; set; }
        /// <summary>
        /// fr_李影，距离2024-07-23 09:47:00开始的0723客观题自动评卷的考试还有1天
        /// </summary>
        public string MSG_CONTENT { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public DateTime MSG_TIME { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string MSG_TYPE { get; set; }
        /// <summary>
        /// 杏通看板消息
        /// </summary>
        public string MSG_TYPE_NAME { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string MSG_UNIT_ID { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string MODULE_ID { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string CALL_MODULE_ID { get; set; }
        /// <summary>
        /// 个人消息
        /// </summary>
        public string MSG_UNIT_TYPE { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string MSG_UNIT_NAME { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string MSG_CLASS { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string MSG_STATE { get; set; }
        /// <summary>
        /// 未处理
        /// </summary>
        public string MSG_STATE_NAME { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string MSG_DISPOSE_URL { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string CLIENT_MAC { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string LEVEL { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public DateTime SEND_TIME { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string IS_SEND { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string MSG_DISPOSE_TYPE { get; set; }
        /// <summary>
        /// 参加考试提醒通知
        /// </summary>
        public string MSG_TITLE { get; set; }
        /// <summary>
        /// 文档-学习任务
        /// </summary>
        public string MSG_LSNAME { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string MSG_DJSON { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string SERVICE_TYPE { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int DELAY_TIME { get; set; }
    }
}
