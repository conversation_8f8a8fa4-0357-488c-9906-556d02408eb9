﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.NetworkInformation;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Base.Helper
{
    public static class GetMacLoacltion
    {
        public static string GetMacAddress()
        {
            // 获取本地计算机上的所有网络接口
            NetworkInterface[] nics = NetworkInterface.GetAllNetworkInterfaces();

            string macAddress = "";

            foreach (NetworkInterface adapter in nics)
            {
                // 过滤掉虚拟接口和回环接口
                if (adapter.NetworkInterfaceType == NetworkInterfaceType.Ethernet &&
                    adapter.OperationalStatus == OperationalStatus.Up)
                {
                    macAddress = adapter.GetPhysicalAddress().ToString();
                    break;
                }
            }

            return macAddress;
        }
    }
}
