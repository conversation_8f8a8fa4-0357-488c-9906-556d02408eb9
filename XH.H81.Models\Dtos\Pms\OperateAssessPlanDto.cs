﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Dtos.Pms
{
    public class OperateAssessPlanDto
    {
        /// <summary>
        /// 评估人员id合集多个,隔开
        /// </summary>
        public string PlanpPersonIds { get; set; }

        /// <summary>
        /// 1提交2驳回3审核5撤销10删除
        /// </summary>
        public string AssessPlanType { get; set; }

        /// <summary>
        /// 指定审核人
        /// </summary>
        public string? CheckPerson { get; set; }

        /// <summary>
        /// 审核/驳回原因
        /// </summary>
        public string? OperCause { get; set; }

        /// <summary>
        /// 操作电脑
        /// </summary>
        public string? OperComputer { get; set; }

        /// <summary>
        /// 密码
        /// </summary>
        public string? Pwd { get; set; }

    }
}
