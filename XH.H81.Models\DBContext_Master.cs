﻿using Microsoft.EntityFrameworkCore;
using XH.H81.Models.Entities;
using XH.H81.Models.Entities.EvaluatePlan;
using XH.H81.Models.Entities.Exam;
using XH.H81.Models.Entities.others;
using XH.H81.Models.Entities.Pms;
using XH.H81.Models.Entities.Tag;
using XH.H81.Models.Exam;
using XH.H81.Models.ExternalEntity;
using XH.LAB.UTILS.Models;
using XH.LAB.UTILS.Models.Entites;
using SYS6_POST = XH.H81.Models.Entities.SYS6_POST;
using SYS6_USER_POST = XH.H81.Models.Entities.SYS6_USER_POST;

namespace XH.H81.Models
{


    public partial class DBContext_Master : DbContext
    {

        public DBContext_Master()
        {

        }

        public DBContext_Master(DbContextOptions<DBContext_Master> options)
            : base(options)
        {

        }
        #region 人事
        public virtual DbSet<PMS_PERSON_INFO> PMS_PERSON_INFO { get; set; }
        /// <summary>
        /// 模块分类
        /// </summary>
        //public virtual DbSet<SYS_CLASS_INFO> SYS_CLASS_INFO { get; set; }
        /// <summary>
        /// 模块信息
        /// </summary>
        /// <summary>
        /// 架构信息
        /// </summary>
        public virtual DbSet<PMS_ARCHITECTURE_INFO> PMS_ARCHITECTURE_INFO { get; set; }

        /// <summary>
        /// 履历记录
        /// </summary>
        public virtual DbSet<PMS_RESUME_LIST> PMS_RESUME_LIST { get; set; }

        /// <summary>
        /// 奖惩记录
        /// </summary>
        public virtual DbSet<PMS_REWARD_LIST> PMS_REWARD_LIST { get; set; }
        /// <summary>
        /// 教学记录
        /// </summary>
        public virtual DbSet<PMS_TEACH_LIST> PMS_TEACH_LIST { get; set; }
        /// <summary>
        /// 进修记录
        /// </summary>
        public virtual DbSet<PMS_STUDY_LIST> PMS_STUDY_LIST { get; set; }
        /// <summary>
        /// 课题记录
        /// </summary>
        public virtual DbSet<PMS_RESEARCH_LIST> PMS_RESEARCH_LIST { get; set; }
        /// <summary>
        /// 论文记录
        /// </summary>
        public virtual DbSet<PMS_THESIS_LIST> PMS_THESIS_LIST { get; set; }
        /// <summary>
        /// 教育记录
        /// </summary>
        public virtual DbSet<PMS_EDUCATION_LIST> PMS_EDUCATION_LIST { get; set; }
        /// <summary>
        /// 技能证书记录
        /// </summary>
        public virtual DbSet<PMS_SKILL_CERTIFICATE_LIST> PMS_SKILL_CERTIFICATE_LIST { get; set; }
        /// <summary>
        /// 培训记录
        /// </summary>
        public virtual DbSet<PMS_TRAIN_LIST> PMS_TRAIN_LIST { get; set; }
        /// <summary>
        /// 社会任职记录
        /// </summary>
        public virtual DbSet<PMS_SOCIAL_OFFICE_LIST> PMS_SOCIAL_OFFICE_LIST { get; set; }
        /// <summary>
        /// 知识产权记录
        /// </summary>
        public virtual DbSet<PMS_INTELLECTUAL_LIST> PMS_INTELLECTUAL_LIST { get; set; }
        /// <summary>
        /// 请假信息
        /// </summary>
        //public virtual DbSet<PMS_LEAVE_INFO> PMS_LEAVE_INFO { get; set; }
        /// <summary>
        /// 访问交流记录
        /// </summary>
        public virtual DbSet<PMS_EXCHANGE_LIST> PMS_EXCHANGE_LIST { get; set; }

        /// <summary>
        /// 职称记录
        /// </summary>
        public virtual DbSet<PMS_PROFESSIONAL_LIST> PMS_PROFESSIONAL_LIST { get; set; }

        /// <summary>
        /// 外派记录
        /// </summary>
        public virtual DbSet<PMS_EXPATRIATE_LIST> PMS_EXPATRIATE_LIST { get; set; }

        /// <summary>
        /// 人员文件信息
        /// </summary>
        public virtual DbSet<PMS_PERSON_FILE> PMS_PERSON_FILE { get; set; }

        /// <summary>
        /// 人事操作日志
        /// </summary>
        public virtual DbSet<PMS_CHANGE_LOG> PMS_CHANGE_LOG { get; set; }

        /// <summary>
        /// 分类附加表映射表
        /// </summary>
        public virtual DbSet<PMS_ADDN_CLASS_INFO> PMS_ADDN_CLASS_INFO { get; set; }

        /// <summary>
        /// 分类附加表
        /// </summary>
        public virtual DbSet<PMS_ADDN_RECORD> PMS_ADDN_RECORD { get; set; }
        public virtual DbSet<PMS_PERSON_TAG_RELATE> PMS_PERSON_TAG_RELATE { get; set; }
        public virtual DbSet<OA_TRAIN_TYPE_DICT> OA_TRAIN_TYPE_DICT { get; set; }
        public virtual DbSet<PMS_PERSON_TAG_DICT> PMS_PERSON_TAG_DICT { get; set; }
        public virtual DbSet<OA_EVAL_STAGE_DICT> OA_EVAL_STAGE_DICT { get; set; }
        public virtual DbSet<PMS_PERSON_TAG> PMS_PERSON_TAG { get; set; }
        public virtual DbSet<SYS6_FUNC_FIELD_DICT> SYS6_FUNC_FIELD_DICT { get; set; }

        #endregion

        #region 外部表
        /// <summary>
        /// 系统菜单信息表
        /// </summary>
        public virtual DbSet<SYS6_MENU> SYS6_MENU { get; set; }
        /// <summary>
        /// 模块功能设置字典表
        /// </summary>
        public virtual DbSet<SYS6_MODULE_FUNC_DICT> SYS6_MODULE_FUNC_DICT { get; set; }
        /// <summary>
        /// 第三方对照表
        /// </summary>
        public virtual DbSet<SYS6_HGROUP_HISID> SYS6_HGROUP_HISID { get; set; }
        /// <summary>
        /// 用户岗位表
        /// </summary>
        public virtual DbSet<SYS6_USER_POST> SYS6_USER_POST { get; set; }
        /// <summary>
        /// 岗位单元表
        /// </summary>
        public virtual DbSet<SYS6_POST_UNIT> SYS6_POST_UNIT { get; set; }
        /// <summary>
        /// 检验专业组信息表
        /// </summary>
        public virtual DbSet<SYS6_INSPECTION_PGROUP> SYS6_INSPECTION_PGROUP { get; set; }

        /// <summary>
        /// 管理专业组信息表
        /// </summary>
        public virtual DbSet<SYS6_INSPECTION_MGROUP> SYS6_INSPECTION_MGROUP { get; set; }

        /// <summary>
        /// 科室信息表
        /// </summary>
        public virtual DbSet<SYS6_INSPECTION_LAB> SYS6_INSPECTION_LAB { get; set; }

        /// <summary>
        /// 表字段字典表
        /// </summary>
        public virtual DbSet<SYS6_FEILD_DICT> SYS6_FEILD_DICT { get; set; }

        /// <summary>
        /// 固定基础数据分类表
        /// </summary>
        public virtual DbSet<SYS6_BASE_DATA> SYS6_BASE_DATA { get; set; }
        /// <summary>
        /// 岗位信息表
        /// </summary>
        public virtual DbSet<SYS6_POST> SYS6_POST { get; set; }
        /// <summary>
        /// 检测仪器信息表
        /// </summary>
        public virtual DbSet<LIS6_INSTRUMENT_INFO> LIS6_INSTRUMENT_INFO { get; set; }

        /// <summary>
        /// 位置信息表
        /// </summary>
        public virtual DbSet<SYS6_POSITION_DICT> SYS6_POSITION_DICT { get; set; }

        /// <summary>
        /// 分析项目信息表
        /// </summary>
        public virtual DbSet<LIS6_TEST_ITEM> LIS6_TEST_ITEM { get; set; }

        /// <summary>
        /// 诊疗项目信息表
        /// </summary>
        public virtual DbSet<LIS6_CHARGE_ITEM> LIS6_CHARGE_ITEM { get; set; }

        public virtual DbSet<SYS6_USER> SYS6_USER { get; set; }

        /// <summary>
        /// 系统设置
        /// </summary>
        public virtual DbSet<SYS6_SETUP_DICT> SYS6_SETUP_DICT { get; set; }
        /// <summary>
        /// 检验分组
        /// </summary>
        public virtual DbSet<LIS6_INSPECTION_GROUP> LIS6_INSPECTION_GROUP { get; set; }
        /// <summary>
        /// 检测仪器项目
        /// </summary>

        //public virtual DbSet<LIS6_INSTRUMENT_ITEM> LIS6_INSTRUMENT_ITEM { get; set; }

        //public virtual DbSet<QC6_GROUP_INFO> QC6_GROUP_INFO { get; set; }

        //public virtual DbSet<QC6_TEST_UNIT> QC6_TEST_UNIT { get; set; }



        /// <summary>
        /// (标本接收单元表)
        /// </summary>
        //public virtual DbSet<LIS6_INCEPT_UNIT_INFO> LIS6_INCEPT_UNIT_INFO { get; set; }
        /// <summary>
        ///   (采集单元字典表)
        /// </summary>
        //public virtual DbSet<LIS6_SAMPLEGATHER_UNIT> LIS6_SAMPLEGATHER_UNIT { get; set; }

        /// <summary>
        /// (模块角色信息表)
        /// </summary>
        public virtual DbSet<SYS6_ROLE> SYS6_ROLE { get; set; }
        /// <summary>
        /// (岗位权组表)
        /// </summary>
        public virtual DbSet<SYS6_POST_ROLE_COM> SYS6_POST_ROLE_COM { get; set; }


        /// <summary>
        /// （系统角色菜单表）
        /// </summary>

        public virtual DbSet<SYS6_ROLE_MENU> SYS6_ROLE_MENU { get; set; }
        /// <summary>
        /// (单元组合字典表)
        /// </summary>

        public virtual DbSet<SYS6_UNIT_COM_INFO> SYS6_UNIT_COM_INFO { get; set; }

        /// <summary>
        /// 权限组合字典表
        /// </summary>
        //public virtual DbSet<SYS6_ROLE_COM_INFO> SYS6_ROLE_COM_INFO { get; set; }

        public virtual DbSet<SYS6_HOSPITAL_INFO> SYS6_HOSPITAL_INFO { get; set; }
        public virtual DbSet<SMBL_LAB> SMBL_LAB { get; set; }

        public virtual DbSet<EMP_EXAM_USER_ANSWER> EMP_EXAM_USER_ANSWER { get; set; }

        public virtual DbSet<EMP_PAPER_INFO> EMP_PAPER_INFO { get; set; }

        public virtual DbSet<OA_DATA_CLASS> OA_DATA_CLASS { get; set; }

        public virtual DbSet<PMS_ASSESS_PLAN_PERSON> PMS_ASSESS_PLAN_PERSON { get; set; }

        //public virtual DbSet<OA_EVALUATE_PLAN_DICT> OA_EVALUATE_PLAN_DICT { get; set; }

        //public virtual DbSet<OA_EVALUATE_PLAN_SETUP> OA_EVALUATE_PLAN_SETUP { get; set; }

        //public virtual DbSet<OA_EVALUATE_PLAN_UNIT> OA_EVALUATE_PLAN_UNIT { get; set; }

        //public virtual DbSet<OA_EVALUATE_PLAN_USER> OA_EVALUATE_PLAN_USER { get; set; }

        public virtual DbSet<SYS6_LIMIT_ROLE_DICT> SYS6_LIMIT_ROLE_DICT { get; set; }
        public virtual DbSet<SYS6_LIMIT_ROLE_MENU> SYS6_LIMIT_ROLE_MENU { get; set; }

        public virtual DbSet<SYS6_SOFT_MODULE_INFO> SYS6_SOFT_MODULE_INFO { get; set; }

        public virtual DbSet<PMS_ASSESS_PERSON_OPER_LOG> PMS_ASSESS_PERSON_OPER_LOG { get; set; }

        /// <summary>
        /// 用户组合表
        /// </summary>
        public virtual DbSet<SYS6_USER_COM> SYS6_USER_COM { get; set; }
        public virtual DbSet<OA_EVALUATE_PLAN_EVENT> OA_EVALUATE_PLAN_EVENT { get; set; }
        public virtual DbSet<SYS6_USER_PROLE_LIMIT> SYS6_USER_PROLE_LIMIT { get; set; }
        public virtual DbSet<OA_EVALUATE_EVENT_DISPOSE> OA_EVALUATE_EVENT_DISPOSE { get; set; }
        public virtual DbSet<OA_EVALUATE_EVENT_DISPOSE_no_clob> OA_EVALUATE_EVENT_DISPOSE_no_clob { get; set; }

        #endregion

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            foreach (var property in modelBuilder.Model.GetEntityTypes().SelectMany(e => e.GetProperties().Where(p => p.ClrType == typeof(string))))
            {
                property.SetIsUnicode(false);
            }

            //外部表
            modelBuilder.Entity<SYS6_MODULE_FUNC_DICT>(entity =>
            {
                entity.HasKey(c => new { c.SETUP_ID, c.FUNC_ID, c.HOSPITAL_ID });
            });

            modelBuilder.Entity<SYS6_BASE_DATA>(entity =>
            {
                entity.HasKey(c => new { c.DATA_ID, c.CLASS_ID });
            });
            modelBuilder.Entity<LIS6_CHARGE_ITEM>(entity =>
            {
                entity.HasKey(c => new { c.CHARGE_ITEM_ID, c.HOSPITAL_ID });
            });
            modelBuilder.Entity<SYS6_SETUP_DICT>(entity =>
            {
                entity.HasKey(c => new { c.SETUP_NO, c.HOSPITAL_ID });
            });
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            base.OnConfiguring(optionsBuilder);
            optionsBuilder.EnableSensitiveDataLogging();
            optionsBuilder.EnableDetailedErrors();
        }

    }
}
