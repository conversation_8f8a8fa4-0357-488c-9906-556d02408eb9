﻿using System;
using System.Collections.Generic;
using H.Utility.SqlSugarInfra;
using SqlSugar;

/*
 * <AUTHOR> lwh
 * @date : 2025-4-2
 * @desc : 人员标签与数据属性关联表
 */
namespace XH.H81.Models.Entities.Tag
{
    /// <summary>
    /// 人员标签与数据属性关联表
    [DBOwner("XH_OA")]
    public class PMS_PERSON_TAG_RELATE
    {
        /// <summary>
        /// 标签属性关系ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string TAG_DATA_RID { get; set; }

        /// <summary>
        /// 机构id
        /// </summary>
        public string HOSPITAL_ID { get; set; }

        /// <summary>
        /// 人员标签ID
        /// </summary>
        public string PERSON_TAG_ID { get; set; }

        /// <summary>
        /// 数据类型: OA_BASE_DATA 基础数据、 REC_CLASS 档案类型、 CERTIFICATE 证书类型、 TRAIN_TYPE 培训类型、 SUBJECT_TYPE 课题类型
        /// </summary>
        public string DATA_TYPE { get; set; }

        /// <summary>
        /// 数据ID
        /// </summary>
        public string DATA_ID { get; set; }

        /// <summary>
        /// 扩展属性1
        /// </summary>
        public string PROPERTY1 { get; set; }

        /// <summary>
        /// 扩展属性2
        /// </summary>
        public string PROPERTY2 { get; set; }

        /// <summary>
        /// 扩展JSON
        /// </summary>
        public string DATA_JSON { get; set; }

        /// <summary>
        /// 首次登记人
        /// </summary>
        public string FIRST_RPERSON { get; set; }

        /// <summary>
        /// 首次登记时间
        /// </summary>
        public DateTime FIRST_RTIME { get; set; }

        /// <summary>
        /// 最后修改人员
        /// </summary>
        public string LAST_MPERSON { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime LAST_MTIME { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string REMARK { get; set; }

    }
}