﻿#region (c) 2022 杏和软件. All rights reserved.

// @Author: ch<PERSON><PERSON><PERSON><PERSON>
// @Create: 2022-11-03 18:42
// @LastModified: 2022-11-03 15:44
// @Des:Swagger注册类

#endregion

using Microsoft.Extensions.DependencyInjection;
using Microsoft.OpenApi.Models;
using Serilog;
using Swashbuckle.AspNetCore.Filters;
using H.BASE;
using Unchase.Swashbuckle.AspNetCore.Extensions.Extensions;

namespace H.BASE.Infrastructure;

public static class SwaggerSetup
{
    public static void AddSwaggerSetup(this IServiceCollection services)
    {
        var moduleId = AppSettingsProvider.CurrModuleId;
  
        services.AddSwaggerGen(c =>
        {
            //根路径
            var basePath = AppContext.BaseDirectory;
            //c.SwaggerDoc($"XH.H81.API", new OpenApiInfo
            //{
            //    Title = $"XH.H81.API 接口文档 "
            //});
            c.SwaggerDoc("DEMO", new OpenApiInfo
            {
                Title = "DEMO演示接口(注意:_demos下控制器仅开发模式下可用)"
            });
            //c.SwaggerDoc("Pms", new OpenApiInfo
            //{
            //    Title = "Pms人事管理"
            //});
            //c.SwaggerDoc("User", new OpenApiInfo
            //{
            //    Title = "User"
            //});

            //c.SwaggerDoc("ModuleLabGroup", new OpenApiInfo
            //{
            //    Title = "ModuleLabGroup"
            //});

            //c.SwaggerDoc("EvaluatePlan", new OpenApiInfo
            //{
            //    Title = "EvaluatePlan"
            //});

            c.SwaggerDoc("EguardControl", new OpenApiInfo
            {
                Title = "EguardControl"
            });
            // c.OrderActionsBy(o => o.RelativePath);
            c.SwaggerDoc("人事档案相关", new OpenApiInfo
            {
                Title = "人事档案相关"
            });
            c.SwaggerDoc("人员账号相关", new OpenApiInfo
            {
                Title = "人员账号相关"
            });

            c.SwaggerDoc("组织架构相关", new OpenApiInfo
            {
                Title = "组织架构相关"
            });

            c.SwaggerDoc("人员规评相关", new OpenApiInfo
            {
                Title = "人员规评相关"
            });
            c.SwaggerDoc("对外接口", new OpenApiInfo
            {
                Title = "对外接口"
            });

            try
            {
                var xmlPath = Path.Combine(basePath, $"XH.H81.API.xml");
                c.IncludeXmlCommentsWithRemarks(xmlPath, true);

                var xmlModelPath = Path.Combine(basePath, $"XH.H81.Models.xml"); //这个就是Model层的xml文件名
                c.IncludeXmlCommentsWithRemarks(xmlModelPath, true);
            }
            catch (Exception ex)
            {
                Log.Error($"XH.H81.API.xml或XH.H81.Models.xml 丢失，请检查并拷贝。\n" + ex.Message);
            }

            // 开启加权小锁
            c.OperationFilter<AddResponseHeadersFilter>();
            c.OperationFilter<AppendAuthorizeToSummaryOperationFilter>();

            // 在header中添加token，传递到后台
            c.OperationFilter<SecurityRequirementsOperationFilter>();

            // 必须是 oauth2
            c.AddSecurityDefinition("oauth2", new OpenApiSecurityScheme
            {
                Description = "JWT授权(数据将在请求头中进行传输) 直接在下框中输入: Bearer {token}（注意两者之间是一个空格）\"",
                Name = "Authorization", //jwt默认的参数名称
                In = ParameterLocation.Header, //jwt默认存放Authorization信息的位置(请求头中)
                Type = SecuritySchemeType.ApiKey
            });
        });       
    }
}