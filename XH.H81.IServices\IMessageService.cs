﻿using H.Utility;
using XH.H81.Models.Message;

namespace XH.H81.IServices.Message
{
    public interface IMessageService
    {
        /// <summary>
        /// 发送新消息（提醒消息）
        /// </summary>
        /// <param name="sender">发送人</param>
        /// <param name="receivers">接收方</param>

        void SendReminderMessage(string dataId, ClaimsDto sender, List<ReceiverInfo> receivers);

        /// <summary>
        /// 发送人事记录审核提醒消息
        /// </summary>
        /// <param name="examId">考试信息id</param>
        void SendSubmitRecordReminderMessage(ClaimsDto sender, string recordId, string checkUser, string className, string recordCount);

        /// <summary>
        /// 发送人事记录撤销消息
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="examId"></param>
        void SendCancelRecordReminderMessage(ClaimsDto sender, string recordId, string checkUser, string className, string recordCount);

        /// <summary>
        /// 发送待办事项
        /// </summary>
        /// <param name="sender">发送人</param>
        /// <param name="receivers">接收人</param>
        /// <param name="validityPeriodDays">待办有效期</param>
        void SendTodoListMessage(ClaimsDto sender, List<ReceiverInfo> receivers, double validityPeriodDays);

    }
}
