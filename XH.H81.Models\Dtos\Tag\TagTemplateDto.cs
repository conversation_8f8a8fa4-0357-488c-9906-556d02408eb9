﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Dtos.Tag
{
    public class TagTemplateDto
    {
        /// <summary>
        /// 
        /// </summary>
        public string FIELD_CLASS { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string FUNC_ID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string SETUP_ID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string HOSPITAL_ID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string MODULE_ID { get; set; }

        /// <summary>
        /// ISO15189人员表单
        /// </summary>
        public string SETUP_NAME { get; set; }

        /// <summary>
        /// ISO15189人员表单
        /// </summary>
        public string SETUP_CNAME { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string SETUP_SORT { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string SETUP_STATE { get; set; }

        /// <summary>
        /// 模板id
        /// </summary>
        public string LAYOUT_ID { get; set; }

    }
}
