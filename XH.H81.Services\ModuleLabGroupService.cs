﻿using AutoMapper;
using H.BASE;
using H.BASE.SqlSugarInfra.Uow;
using H.IRepository;
using H.Repository;
using H.Utility;
using H.Utility.Helper;
using H.Utility.SerilogStopwatch;
using JWT.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NPOI.Util;
using Serilog;
using Spectre.Console;
using SqlSugar;
using XH.H81.Base.Helper;
using XH.H81.IServices;
using XH.H81.Models;
using XH.H81.Models.Dtos;
using XH.H81.Models.Dtos.Pms;
using XH.H81.Models.Entities;
using XH.H81.Models.Entities.others;
using XH.H81.Models.Entities.Pms;
using XH.H81.Models.Enums;
using XH.H81.Models.ExternalEntity;
using XH.H81.Models.SugarDbContext;
using XH.LAB.UTILS.Helpers;
using XH.LAB.UTILS.Implement;
using XH.LAB.UTILS.Interface;
using XH.LAB.UTILS.Models;
using XH.LAB.UTILS.Models.Dto;
using XH.LAB.UTILS.Models.Entites;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;
using SYS6_POST = XH.H81.Models.Entities.SYS6_POST;
using SYS6_USER_POST = XH.H81.Models.Entities.SYS6_USER_POST;

namespace XH.H81.Services
{
    public class ModuleLabGroupService : IModuleLabGroupService
    {
        private readonly ISqlSugarUow<SugarDbContext_Master> _soa;
        private readonly IMapper _mapper;
        private readonly IBaseDataServices _IBaseDataServices;
        private readonly IAuthorityService _IAuthorityService;
        private readonly IAuthorityService2 _IAuthorityService2;
        private readonly IOrganizationTreeService _organizationTreeService;
        private readonly IOrganizationTreeService2 _organizationTreeService2;
        private readonly IConfiguration _configuration;
        private readonly IHttpContextAccessor _httpContext;
        public ModuleLabGroupService(ISqlSugarUow<SugarDbContext_Master> suow, IMapper mapper, IBaseDataServices iBaseDataServices, IAuthorityService authorityService, IAuthorityService2 authorityService2, IHttpContextAccessor httpContext, IOrganizationTreeService organizationTreeService, IOrganizationTreeService2 organizationTreeService2, IConfiguration configuration)
        {
            _soa = suow;
            _mapper = mapper;
            _IBaseDataServices = iBaseDataServices;
            _IAuthorityService = authorityService;
            _IAuthorityService2 = authorityService2;
            _httpContext = httpContext;
            _organizationTreeService = organizationTreeService;
            _organizationTreeService2 = organizationTreeService2;
            _configuration = _configuration;
            ExecutingChangeSqlHelper.ExecutingChangeSql(_soa, configuration, true);
        }
        /// <summary>
        /// 获取菜单信息
        /// </summary>
        /// <param name="strModuleId"></param>
        /// <returns></returns>
        public List<Sys6MenuDto> GetMenuInfo(string strModuleId, string strHospitalid, string lab_id, string user_no)
        {
            //var pgroups = _IAuthorityService2.GetUserPermissionPgroup(_soa, new OrgParams { hospital_id = "33A001", lab_id = "L001" }, "H86");
            //旧管理单元逻辑
            //List<SYS6_MENU> sys6_menu = _IAuthorityService.GetUserMenuList(_soa, user_no, MenuClassEnum.ALL, parentCode: null, strHospitalid, lab_id).FindAll(a => a.MENU_STATE == "1");
            //多管理单元新逻辑
            List<SYS6_MENU> sys6_menu = _IAuthorityService2.GetUserMenuList(_soa, "H81");
            return HandleMenuLevels(sys6_menu);
        }

        //菜单增加层级
        private List<Sys6MenuDto> HandleMenuLevels(List<SYS6_MENU> menus)
        {
            menus = menus.FindAll(w => w.MENU_CLASS != "button");
            using (var op = LogStopwatch.Begin("获取菜单信息B"))
            {
                var resultMenus = new List<Sys6MenuDto>();
                string rootMenuId = menus.FirstOrDefault(a => a.MENU_LEVEL == "0")?.MENU_ID;
                //兼容多单元新逻辑
                if (rootMenuId == null)
                {
                    menus.Add(new SYS6_MENU { MENU_ID = "H81", MENU_SORT = "H81", MENU_LEVEL = "0", MENU_BNAME = "人员管理" });
                    rootMenuId = "H81";
                }
                foreach (var menu in menus.Where(a => a.MENU_STATE == null || (a.MENU_STATE != "0" && a.MENU_STATE != "2") )
                 .OrderBy(a => a.PARENT_CODE).ThenBy(a => a.MENU_SORT))
                {
                    Sys6MenuDto dto = _mapper.Map<Sys6MenuDto>(menu);
                    if (menu.PARENT_CODE == rootMenuId)
                    {
                        resultMenus.Add(dto);
                    }
                    else
                    {
                        Sys6MenuDto parent = resultMenus.FirstOrDefault(a => a.MENU_ID == dto.PARENT_CODE);
                        if (parent != null)
                        {
                            if (parent.ChildMenu == null)
                                parent.ChildMenu = new List<Sys6MenuDto> { dto };
                            else
                                parent.ChildMenu.Add(dto);
                        }
                    }
                }

                op.Lap($"menus:记录数({menus.Count})");
                //日志结束
                op.Complete();

                return resultMenus;
            }
        }

        /// <summary>
        /// 获取人员对应科室信息
        /// </summary>
        /// <param name="strUserNo"></param>
        /// <returns></returns>
        public List<InspectionLabDto> GetLisInspectionLabInfo(string strUserNo, string strHospitalId)
        {
            //旧管理单元逻辑
            //List<SYS6_INSPECTION_LAB> premissionLabs = _IAuthorityService.GetUserPermissionLab(_soa, strUserNo, "H81", strHospitalId);
            //var inspectionLabDto = premissionLabs.Select(a => new InspectionLabDto
            //{
            //    LAB_ID = a.LAB_ID,
            //    LAB_NAME = a.LAB_NAME,
            //}).ToList();

            //多管理单元新逻辑
            List<InspectionLabDto> inspectionLabDto = _IAuthorityService2.GetLogonLabList(_soa, "H81");
            return inspectionLabDto;
        }

        /// <summary>
        /// 返回生安登录选择机构、科室、备案实验室列表
        /// </summary>
        /// <param name="strUserNo"></param>
        /// <returns></returns>
        public LogonHospitalLabList GetLogonHospitalLabList(string strUserNo, string strHospitalId)
        {
            //旧管理单元逻辑
            //var result = _IAuthorityService.GetLogonHospitalLabList(_soa, strUserNo, "H81", strHospitalId);
            //多管理单元新逻辑
            var result = _IAuthorityService2.GetLogonHospitalLabList(_soa, "H81");
            return result;
        }


        ///// <summary>
        ///// 根据科室和人员获取对应分组信息
        ///// </summary>
        ///// <param name="lab_id"></param>
        ///// <returns></returns>
        //public List<Lis_Inspection_pgroupDto> GetPgroupInfoByLabIdUser(string hospital_id, string lab_id, string pgroup_id, string person_name, string user_no, string page_type)
        //{
        //    //人事系统权限菜单ID
        //    const string MODULE_MENU_ID = "H81";
        //    //人事档案页面权限（编辑权限）
        //    const string PERSON_INFO_EDIT_MENU_ID = "H8107";
        //    //记录审核页面权限（人事档案审核权限）
        //    const string PERSON_INFO_AUDIT_MENU_ID = "H8109";
        //    string permissMenuId = "";
        //    switch (page_type)
        //    {
        //        case "1": permissMenuId = PERSON_INFO_EDIT_MENU_ID; break;
        //        case "2": permissMenuId = PERSON_INFO_AUDIT_MENU_ID; break;
        //        default: permissMenuId = MODULE_MENU_ID; break;
        //    }
        //    List<SYS6_INSPECTION_PGROUP> query_Pgroups = GetUserPermissionPgroup(user_no, permissMenuId, hospital_id, lab_id)
        //        .Where(a => a.PGROUP_STATE == "1" && a.PGROUP_CLASS != "3")//排除临床专业组
        //        .WhereIF(pgroup_id.IsNotNullOrEmpty(), a => a.PGROUP_ID == pgroup_id)
        //        .ToList();

        //    List<Lis_Inspection_pgroupDto> list = new List<Lis_Inspection_pgroupDto>();
        //    if (query_Pgroups.Count() > 0)
        //    {
        //        foreach (var key in query_Pgroups)
        //        {
        //            Lis_Inspection_pgroupDto listinspectionlab = new Lis_Inspection_pgroupDto();
        //            listinspectionlab.PGROUP_ID = key.PGROUP_ID;
        //            listinspectionlab.PGROUP_NAME = key.PGROUP_NAME;
        //            listinspectionlab.PGROUP_SORT = key.PGROUP_SORT;
        //            listinspectionlab.REMARK = key.REMARK;
        //            list.Add(listinspectionlab);
        //        }
        //    }
        //    return list;
        //}


        /// <summary>
        /// 获取人员信息树
        /// </summary>
        /// <param name="hospital_id"></param>
        /// <returns></returns>

        public IEnumerable<InspectionPgroupDto> GetPersonInfoTree(string hospital_id, string lab_id, string pgroup_id, string person_name, string user_no)
        {
            var query = _soa.Db.Queryable<SYS6_USER_POST>()
                .LeftJoin<SYS6_POST>((a, b) => a.POST_ID == b.POST_ID)
                .LeftJoin<SYS6_INSPECTION_PGROUP>((a, b, c) => b.PGROUP_ID == c.PGROUP_ID)
                .Where((a, b, c) => a.HOSPITAL_ID == hospital_id && a.USER_NO == user_no && b.LAB_ID == lab_id && c.PGROUP_STATE == "1")
                .Select((a, b, c) => new
                {
                    LAB_ID = c.LAB_ID,
                    PGROUP_ID = c.PGROUP_ID,
                    PGROUP_NAME = c.PGROUP_NAME,
                    PGROUP_SORT = c.PGROUP_SORT,
                    REMARK = c.REMARK
                }).Distinct().ToList();
            if (lab_id != pgroup_id)
            {
                if (pgroup_id != "")
                {
                    query = query.Where(p => p.PGROUP_ID == pgroup_id).ToList();
                }
            }
            List<InspectionPgroupDto> InspectionPgroupList = new List<InspectionPgroupDto>();
            int pgroup_num = 0;
            List<SYS6_BASE_DATA> sys6_base_data = _IBaseDataServices.GetSys6BaseData();
            var sssList = _soa.Db.Queryable<PMS_PERSON_INFO>().Where(p => p.HOSPITAL_ID == hospital_id).Select<PMS_PERSON_INFO>().ToList();
            try
            {
                foreach (var dic in query)
                {
                    var ssList = _soa.Db.Queryable<SYS6_USER>().Where(p => p.STATE_FLAG == "1" && p.DEPT_CODE == dic.PGROUP_ID)
                    .WhereIF(person_name.IsNotNullOrEmpty(), p => p.USERNAME.Contains(person_name))
                    .Select<SYS6_USER>().ToList();
                    //if (person_name != "")
                    //{
                    //    ssList = ssList.Where(p => p.DEPT_CODE == dic.PGROUP_ID && p.STATE_FLAG == "1" && p.USERNAME.Contains(person_name)).ToList();
                    //}
                    pgroup_num = ssList.Count;
                    List<PgroupPersonInfoOption> optionList = new List<PgroupPersonInfoOption>();
                    foreach (var item in ssList)
                    {
                        var personItemOjb = sssList.Where(p => p.USER_ID == item.USER_NO).First();
                        PgroupPersonInfoOption odto = new PgroupPersonInfoOption();
                        string datetime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                        if (personItemOjb != null)
                        {
                            odto.key = item.USER_NO;
                            odto.PERSON_ID = personItemOjb.PERSON_ID;
                            odto.USER_ID = personItemOjb.USER_ID;
                            odto.title = personItemOjb.USER_NAME;
                            odto.PERSON_PHOTO_PATH = personItemOjb.PERSON_PHOTO_PATH;
                            odto.DUTIES_NAME = RecordClassBaseName(sys6_base_data, "职务", personItemOjb.DUTIES);
                            //odto.IF_COMPLETE = GitRecordWriteRowNum(personItemOjb.PERSON_ID, hospital_id);
                            optionList.Add(odto);
                        }
                    }
                    if (pgroup_num != 0)
                    {
                        InspectionPgroupList.Add(new InspectionPgroupDto
                        {
                            key = dic.PGROUP_ID,
                            title = dic.PGROUP_NAME,
                            LAB_ID = dic.LAB_ID,
                            PGROUP_NUM = pgroup_num,
                            children = optionList
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                _IBaseDataServices.GetErrorTableMax("PMS_PERSON_INFO", ex, "PERSON_ID");
            }

            return InspectionPgroupList;
        }

        /// <summary>
        /// 获取检验分组下拉信息
        /// </summary>
        /// <param name="hospital_id"></param>
        /// <param name="pgroup_id"></param>
        /// <returns></returns>
        public List<DropDowDto> GetGroupDropDownInfo(string hospital_id, string pgroup_id, string lab_id)
        {
            List<DropDowDto> dropdown = new List<DropDowDto>();
            var lis_inspection_group = _soa.Db.Queryable<LIS6_INSPECTION_GROUP>().Where(p => p.HOSPITAL_ID == hospital_id && p.PGROUP_ID == pgroup_id
             && p.LAB_ID == lab_id && p.GROUP_CLASS == "常规检验类")
                 .Select(s => new
                 {
                     GROUP_ID = s.GROUP_ID,
                     GROUP_NAME = s.GROUP_NAME
                 }).ToList();
            if (lis_inspection_group.Count > 0)
            {
                for (int i = 0; i < lis_inspection_group.Count; i++)
                {
                    dropdown.Add(new DropDowDto
                    {
                        key = lis_inspection_group[i].GROUP_ID,
                        value = lis_inspection_group[i].GROUP_NAME
                    });
                }
            }
            return dropdown;
        }


        /// <summary>
        /// 获取固定基础数据下拉
        /// </summary>
        /// <returns></returns>
        public Dictionary<string, List<DropDown>> GetBaseDataInfo(string hospital_id)
        {
            Dictionary<string, List<DropDown>> keyValuePairs = new Dictionary<string, List<DropDown>>();
            //查询工具箱，把所有下拉类型的属性对应的字典加载到前端
            keyValuePairs = AddPageSettingBaseDataInfo(keyValuePairs, hospital_id);

            //获取数据源指向OA_BASE_DATA的类型
            List<string> oaClassIds = _soa.Db.Queryable<SYS6_BASE_DATA_CLASS>()
                .Where(a => a.CLASS_STATE == "1" && a.DATA_TABLE.ToUpper() == "OA_BASE_DATA")
                .Select(a => a.CLASS_ID).ToList();

            var lis5_inspection_pgroup = _IBaseDataServices.GetInspectionPgroup();
            List<DropDown> PGROUP_IDS = new List<DropDown>();
            for (int i = 0; i < lis5_inspection_pgroup.Count; i++)
            {
                PGROUP_IDS.Add(new DropDown
                {
                    key = lis5_inspection_pgroup[i].PGROUP_ID,
                    value = lis5_inspection_pgroup[i].PGROUP_NAME
                });
            }
            keyValuePairs.Add("PGROUP_ID", PGROUP_IDS);

            List<DropDown> SEXS = GetOaOrSys6BaseDataDropDown("性别", oaClassIds.Contains("性别") ? BaseDataSourceType.OA : BaseDataSourceType.SYS);
            keyValuePairs.Add("SEX", SEXS);
            keyValuePairs.TryAdd("性别", SEXS);

            List<DropDown> HIGHEST_DEGREES = GetOaOrSys6BaseDataDropDown("最高学历", oaClassIds.Contains("最高学历") ? BaseDataSourceType.OA : BaseDataSourceType.SYS);
            keyValuePairs.Add("HIGHEST_DEGREE", HIGHEST_DEGREES);
            keyValuePairs.TryAdd("最高学历", HIGHEST_DEGREES);

            //学历
            List<DropDown> EDUCATION_BACKGROUNDS = GetOaOrSys6BaseDataDropDown("最高学历", oaClassIds.Contains("最高学历") ? BaseDataSourceType.OA : BaseDataSourceType.SYS);
            keyValuePairs.Add("EDUCATION_BACKGROUND", EDUCATION_BACKGROUNDS);

            // 学位
            List<DropDown> EDUCATION_DEGREES = GetOaOrSys6BaseDataDropDown("最高学位", oaClassIds.Contains("最高学位") ? BaseDataSourceType.OA : BaseDataSourceType.SYS);
            keyValuePairs.Add("EDUCATION_DEGREE", EDUCATION_DEGREES);
            keyValuePairs.TryAdd("最高学位", EDUCATION_DEGREES);

            List<DropDown> NATION = GetOaOrSys6BaseDataDropDown("民族", oaClassIds.Contains("民族") ? BaseDataSourceType.OA : BaseDataSourceType.SYS);
            keyValuePairs.Add("NATION", NATION);
            keyValuePairs.TryAdd("民族", NATION);

            List<DropDown> POLITICIAN = GetOaOrSys6BaseDataDropDown("政治面貌", oaClassIds.Contains("政治面貌") ? BaseDataSourceType.OA : BaseDataSourceType.SYS);
            keyValuePairs.Add("POLITICIAN", POLITICIAN);
            keyValuePairs.TryAdd("政治面貌", POLITICIAN);

            List<DropDown> HIGHEST_DIPLOMA = GetOaOrSys6BaseDataDropDown("最高学位", oaClassIds.Contains("最高学位") ? BaseDataSourceType.OA : BaseDataSourceType.SYS);
            keyValuePairs.Add("HIGHEST_DIPLOMA", HIGHEST_DIPLOMA);
            keyValuePairs.TryAdd("最高学位", HIGHEST_DIPLOMA);

            List<DropDown> DUTIES = GetOaOrSys6BaseDataDropDown("职务", oaClassIds.Contains("职务") ? BaseDataSourceType.OA : BaseDataSourceType.SYS);
            keyValuePairs.Add("DUTIES", DUTIES);
            keyValuePairs.TryAdd("职务", DUTIES);

            List<DropDown> USER_TYPE = GetOaOrSys6BaseDataDropDown("用工类型", oaClassIds.Contains("用工类型") ? BaseDataSourceType.OA : BaseDataSourceType.SYS);
            keyValuePairs.Add("USER_TYPE", USER_TYPE);
            keyValuePairs.TryAdd("用工类型", USER_TYPE);

            List<DropDown> CARD_TYPE = GetOaOrSys6BaseDataDropDown("证件类型", oaClassIds.Contains("证件类型") ? BaseDataSourceType.OA : BaseDataSourceType.SYS);
            keyValuePairs.Add("CARD_TYPE", CARD_TYPE);
            keyValuePairs.TryAdd("证件类型", CARD_TYPE);

            List<DropDown> EMPLOYMENT_SOURE = GetOaOrSys6BaseDataDropDown("入职方式", oaClassIds.Contains("入职方式") ? BaseDataSourceType.OA : BaseDataSourceType.SYS);
            keyValuePairs.Add("EMPLOYMENT_SOURE", EMPLOYMENT_SOURE);
            keyValuePairs.TryAdd("入职方式", EMPLOYMENT_SOURE);

            List<DropDown> ACADEMIC_POSTS = GetOaOrSys6BaseDataDropDown("职称", oaClassIds.Contains("职称") ? BaseDataSourceType.OA : BaseDataSourceType.SYS);
            keyValuePairs.Add("ACADEMIC_POST", ACADEMIC_POSTS);
            keyValuePairs.TryAdd("职称", ACADEMIC_POSTS);

            List<DropDown> MARITAL_STATUSS = GetOaOrSys6BaseDataDropDown("婚姻状况", oaClassIds.Contains("婚姻状况") ? BaseDataSourceType.OA : BaseDataSourceType.SYS);
            keyValuePairs.Add("MARITAL_STATUS", MARITAL_STATUSS);
            keyValuePairs.TryAdd("婚姻状况", MARITAL_STATUSS);

            List<DropDown> CHILDREN_CONDITIONS = GetOaOrSys6BaseDataDropDown("有无子女", oaClassIds.Contains("有无子女") ? BaseDataSourceType.OA : BaseDataSourceType.SYS);
            keyValuePairs.Add("CHILDREN_CONDITION", CHILDREN_CONDITIONS);
            keyValuePairs.TryAdd("有无子女", CHILDREN_CONDITIONS);

            List<DropDown> HEALTHS = GetOaOrSys6BaseDataDropDown("健康状况", oaClassIds.Contains("健康状况") ? BaseDataSourceType.OA : BaseDataSourceType.SYS);
            keyValuePairs.Add("HEALTH", HEALTHS);
            keyValuePairs.TryAdd("健康状况", HEALTHS);

            List<DropDown> PERSON_DOC_STATES = GetOaOrSys6BaseDataDropDown("人员状态", oaClassIds.Contains("人员状态") ? BaseDataSourceType.OA : BaseDataSourceType.SYS);
            keyValuePairs.Add("PERSON_DOC_STATE", PERSON_DOC_STATES);
            keyValuePairs.TryAdd("人员状态", PERSON_DOC_STATES);

            List<DropDown> TECH_POSTS = GetOaOrSys6BaseDataDropDown("职称级别", oaClassIds.Contains("职称级别") ? BaseDataSourceType.OA : BaseDataSourceType.SYS);
            keyValuePairs.Add("TECH_POST", TECH_POSTS);
            keyValuePairs.TryAdd("职称级别", TECH_POSTS);

            List<DropDown> TECHNOLOGY_TYPES = GetOaOrSys6BaseDataDropDown("职称类型", oaClassIds.Contains("职称类型") ? BaseDataSourceType.OA : BaseDataSourceType.SYS);
            keyValuePairs.Add("TECHNOLOGY_TYPE", TECHNOLOGY_TYPES);
            keyValuePairs.TryAdd("职称类型", TECHNOLOGY_TYPES);

            //List<DropDown> TECH_POSTS_TECH =  GetOaOrSys6BaseDataDropDown("技师职称", oaClassIds.Contains("技师职称") ? BaseDataSourceType.OA : BaseDataSourceType.SYS);
            //keyValuePairs.Add("TECH_POST_TECH", TECH_POSTS_TECH);

            //List<DropDown> TECH_POSTS_DOCTOR =  GetOaOrSys6BaseDataDropDown("医师职称", oaClassIds.Contains("医师职称") ? BaseDataSourceType.OA : BaseDataSourceType.SYS);
            //keyValuePairs.Add("TECH_POST_DOCTOR", TECH_POSTS_DOCTOR);

            //List<DropDown> TECH_POSTS_NURSE =  GetOaOrSys6BaseDataDropDown("护士职称", oaClassIds.Contains("护士职称") ? BaseDataSourceType.OA : BaseDataSourceType.SYS);
            //keyValuePairs.Add("TECH_POST_NURSE", TECH_POSTS_NURSE);

            //List<DropDown> TECH_POSTS_RESEARCHER =  GetOaOrSys6BaseDataDropDown("研究员职称", oaClassIds.Contains("研究员职称") ? BaseDataSourceType.OA : BaseDataSourceType.SYS);
            //keyValuePairs.Add("TECH_POSTS_RESEARCHER", TECH_POSTS_RESEARCHER);

            List<DropDown> ENGLISH_RANKS = GetOaOrSys6BaseDataDropDown("英语级别", oaClassIds.Contains("英语级别") ? BaseDataSourceType.OA : BaseDataSourceType.SYS);
            keyValuePairs.Add("ENGLISH_RANK", ENGLISH_RANKS);
            keyValuePairs.TryAdd("英语级别", ENGLISH_RANKS);

            // 职称级别
            List<DropDown> PROFESSIONAL_LEVELS = GetOaOrSys6BaseDataDropDown("职称级别", oaClassIds.Contains("职称级别") ? BaseDataSourceType.OA : BaseDataSourceType.SYS);
            keyValuePairs.Add("PROFESSIONAL_LEVEL", PROFESSIONAL_LEVELS);
            keyValuePairs.TryAdd("职称级别", PROFESSIONAL_LEVELS);

            //奖惩级别
            List<DropDown> REWARD_LEVELS = GetOaOrSys6BaseDataDropDown("奖惩级别", oaClassIds.Contains("奖惩级别") ? BaseDataSourceType.OA : BaseDataSourceType.SYS);
            keyValuePairs.Add("REWARD_LEVEL", REWARD_LEVELS);
            keyValuePairs.TryAdd("奖惩级别", REWARD_LEVELS);

            // 教学分类
            List<DropDown> TEACH_CLASSS = GetOaOrSys6BaseDataDropDown("教学分类", oaClassIds.Contains("教学分类") ? BaseDataSourceType.OA : BaseDataSourceType.SYS);
            keyValuePairs.Add("TEACH_CLASS", TEACH_CLASSS);
            keyValuePairs.TryAdd("教学分类", TEACH_CLASSS);

            // 教学级别
            List<DropDown> TEACH_LEVELS = GetOaOrSys6BaseDataDropDown("教学级别", oaClassIds.Contains("教学级别") ? BaseDataSourceType.OA : BaseDataSourceType.SYS);
            keyValuePairs.Add("TEACH_LEVEL", TEACH_LEVELS);
            keyValuePairs.TryAdd("教学级别", TEACH_LEVELS);

            // 课题分类
            List<DropDown> RESEARCH_CLASSS = GetOaOrSys6BaseDataDropDown("课题分类", oaClassIds.Contains("课题分类") ? BaseDataSourceType.OA : BaseDataSourceType.SYS);
            keyValuePairs.Add("RESEARCH_CLASS", RESEARCH_CLASSS);
            keyValuePairs.TryAdd("课题分类", RESEARCH_CLASSS);

            // 课题级别
            List<DropDown> RESEARCH_LEVELS = GetOaOrSys6BaseDataDropDown("课题级别", oaClassIds.Contains("课题级别") ? BaseDataSourceType.OA : BaseDataSourceType.SYS);
            keyValuePairs.Add("RESEARCH_LEVEL", RESEARCH_LEVELS);
            keyValuePairs.TryAdd("课题级别", RESEARCH_LEVELS);

            // 论著类型
            List<DropDown> THESIS_TYPES = GetOaOrSys6BaseDataDropDown("论著类型", oaClassIds.Contains("论著类型") ? BaseDataSourceType.OA : BaseDataSourceType.SYS);
            keyValuePairs.Add("THESIS_TYPE", THESIS_TYPES);
            keyValuePairs.TryAdd("论著类型", THESIS_TYPES);

            //论著级别
            List<DropDown> THESIS_LEVELS = GetOaOrSys6BaseDataDropDown("论著级别", oaClassIds.Contains("论著级别") ? BaseDataSourceType.OA : BaseDataSourceType.SYS);
            keyValuePairs.Add("THESIS_LEVEL", THESIS_LEVELS);
            keyValuePairs.TryAdd("论著级别", THESIS_LEVELS);

            // 论著作者排序
            List<DropDown> THESIS_PERSON_SORTS = GetOaOrSys6BaseDataDropDown("论著作者排序", oaClassIds.Contains("论著作者排序") ? BaseDataSourceType.OA : BaseDataSourceType.SYS);
            keyValuePairs.Add("THESIS_PERSON_SORT", THESIS_PERSON_SORTS);
            keyValuePairs.TryAdd("论著作者排序", THESIS_PERSON_SORTS);

            // JCR分区
            List<DropDown> JCR_RANGES = GetOaOrSys6BaseDataDropDown("JCR分区", oaClassIds.Contains("JCR分区") ? BaseDataSourceType.OA : BaseDataSourceType.SYS);
            keyValuePairs.Add("JCR_RANGE", JCR_RANGES);
            keyValuePairs.TryAdd("JCR分区", JCR_RANGES);

            // 是否SCI记录
            List<DropDown> IF_SCIS = GetOaOrSys6BaseDataDropDown("是否SCI记录", oaClassIds.Contains("是否SCI记录") ? BaseDataSourceType.OA : BaseDataSourceType.SYS);
            keyValuePairs.Add("IF_SCI", IF_SCIS);
            keyValuePairs.TryAdd("是否SCI记录", IF_SCIS);

            // 学位类型
            List<DropDown> DEGREE_TYPES = GetOaOrSys6BaseDataDropDown("学位类型", oaClassIds.Contains("学位类型") ? BaseDataSourceType.OA : BaseDataSourceType.SYS);
            keyValuePairs.Add("DEGREE_TYPE", DEGREE_TYPES);
            keyValuePairs.TryAdd("学位类型", DEGREE_TYPES);

            // 学历性质
            List<DropDown> ACADEMIC_PROPERTYS = GetOaOrSys6BaseDataDropDown("学历性质", oaClassIds.Contains("学历性质") ? BaseDataSourceType.OA : BaseDataSourceType.SYS);
            keyValuePairs.Add("ACADEMIC_PROPERTY", ACADEMIC_PROPERTYS);
            keyValuePairs.TryAdd("学历性质", ACADEMIC_PROPERTYS);

            // 学习形式
            List<DropDown> STUDY_FORMS = GetOaOrSys6BaseDataDropDown("学习形式", oaClassIds.Contains("学习形式") ? BaseDataSourceType.OA : BaseDataSourceType.SYS);
            keyValuePairs.Add("STUDY_FORM", STUDY_FORMS);
            keyValuePairs.TryAdd("学习形式", STUDY_FORMS);

            //学分类型
            List<DropDown> SCORE_TYPES = GetOaOrSys6BaseDataDropDown("学分类型", oaClassIds.Contains("学分类型") ? BaseDataSourceType.OA : BaseDataSourceType.SYS);
            keyValuePairs.Add("SCORE_TYPE", SCORE_TYPES);
            keyValuePairs.TryAdd("学分类型", SCORE_TYPES);

            //证书级别
            List<DropDown> CERTIFICATE_LEVELS = GetOaOrSys6BaseDataDropDown("证书级别", oaClassIds.Contains("证书级别") ? BaseDataSourceType.OA : BaseDataSourceType.SYS);
            keyValuePairs.Add("CERTIFICATE_LEVEL", CERTIFICATE_LEVELS);
            keyValuePairs.TryAdd("证书级别", CERTIFICATE_LEVELS);

            // 任职机构等级
            List<DropDown> SOFFICE_LEVELS = GetOaOrSys6BaseDataDropDown("任职机构等级", oaClassIds.Contains("任职机构等级") ? BaseDataSourceType.OA : BaseDataSourceType.SYS);
            keyValuePairs.Add("SOFFICE_LEVEL", SOFFICE_LEVELS);
            keyValuePairs.TryAdd("任职机构等级", SOFFICE_LEVELS);

            // 任职职务
            List<DropDown> SOFFICE_POSTS = GetOaOrSys6BaseDataDropDown("任职职务", oaClassIds.Contains("任职职务") ? BaseDataSourceType.OA : BaseDataSourceType.SYS);
            keyValuePairs.Add("SOFFICE_POST", SOFFICE_POSTS);
            keyValuePairs.TryAdd("任职职务", SOFFICE_POSTS);

            // 当前是否在职
            List<DropDown> IF_SOFFICES = GetOaOrSys6BaseDataDropDown("当前是否在职", oaClassIds.Contains("当前是否在职") ? BaseDataSourceType.OA : BaseDataSourceType.SYS);
            keyValuePairs.Add("IF_SOFFICE", IF_SOFFICES);
            keyValuePairs.TryAdd("当前是否在职", IF_SOFFICES);

            // 知识产权分类
            List<DropDown> INTELLECTUAL_CLASSS = GetOaOrSys6BaseDataDropDown("知识产权分类", oaClassIds.Contains("知识产权分类") ? BaseDataSourceType.OA : BaseDataSourceType.SYS);
            keyValuePairs.Add("INTELLECTUAL_CLASS", INTELLECTUAL_CLASSS);
            keyValuePairs.TryAdd("知识产权分类", INTELLECTUAL_CLASSS);

            // 交流级别
            List<DropDown> EXCHANGE_LEVELS = GetOaOrSys6BaseDataDropDown("交流级别", oaClassIds.Contains("交流级别") ? BaseDataSourceType.OA : BaseDataSourceType.SYS);
            keyValuePairs.Add("EXCHANGE_LEVEL", EXCHANGE_LEVELS);
            keyValuePairs.TryAdd("交流级别", EXCHANGE_LEVELS);

            //外派性质
            List<DropDown> EXPATRIATES = GetOaOrSys6BaseDataDropDown("外派性质", oaClassIds.Contains("外派性质") ? BaseDataSourceType.OA : BaseDataSourceType.SYS);
            keyValuePairs.Add("EXPATRIATE", EXPATRIATES);
            keyValuePairs.TryAdd("外派性质", EXPATRIATES);

            //进修分类
            List<DropDown> STUDY_CLASSS = GetOaOrSys6BaseDataDropDown("进修分类", oaClassIds.Contains("进修分类") ? BaseDataSourceType.OA : BaseDataSourceType.SYS);
            keyValuePairs.Add("STUDY_CLASS", STUDY_CLASSS);
            keyValuePairs.TryAdd("进修分类", STUDY_CLASSS);

            //人事技能证书类型
            List<DropDown> CERTIFICATE_TYPE = GetOaOrSys6BaseDataDropDown("人事技能证书类型", BaseDataSourceType.OA);
            keyValuePairs.Add("CERTIFICATE_TYPE", CERTIFICATE_TYPE);
            keyValuePairs.TryAdd("人事技能证书类型", CERTIFICATE_TYPE);

            //考试评价结果
            List<DropDown> COMPREHENSIVE_ASSESS = GetOaOrSys6BaseDataDropDown("考试评价结果", BaseDataSourceType.OA);
            keyValuePairs.Add("COMPREHENSIVE_ASSESS", COMPREHENSIVE_ASSESS);
            keyValuePairs.TryAdd("考试评价结果", COMPREHENSIVE_ASSESS);

            //人员评估评价结果
            List<DropDown> TOTAL_EVALUATE = GetOaOrSys6BaseDataDropDown("人员评估评价结果", BaseDataSourceType.OA);
            keyValuePairs.Add("TOTAL_EVALUATE", TOTAL_EVALUATE);
            keyValuePairs.TryAdd("人员评估评价结果", TOTAL_EVALUATE);

            //岗位类型
            List<DropDown> POST_TYPE = GetOaOrSys6BaseDataDropDown("岗位类型", oaClassIds.Contains("岗位类型") ? BaseDataSourceType.OA : BaseDataSourceType.SYS);
            keyValuePairs.Add("POST_TYPE", POST_TYPE);
            keyValuePairs.TryAdd("岗位类型", POST_TYPE);

            //岗位分类
            List<DropDown> POST_CLASS = GetOaOrSys6BaseDataDropDown("岗位分类", oaClassIds.Contains("岗位分类") ? BaseDataSourceType.OA : BaseDataSourceType.SYS);
            keyValuePairs.Add("POST_CLASS", POST_CLASS);
            keyValuePairs.TryAdd("岗位分类", POST_CLASS);

            //岗位类别
            List<DropDown> SMBL_POST_TYPE = GetOaOrSys6BaseDataDropDown("岗位类别", BaseDataSourceType.OA);
            keyValuePairs.Add("SMBL_POST_TYPE", SMBL_POST_TYPE);
            keyValuePairs.TryAdd("岗位类别", SMBL_POST_TYPE);

            //现从事岗位
            List<DropDown> SMBL_POST_NOW = GetOaOrSys6BaseDataDropDown("现从事岗位", BaseDataSourceType.OA);
            keyValuePairs.Add("SMBL_POST_NOW", SMBL_POST_NOW);
            keyValuePairs.TryAdd("现从事岗位", SMBL_POST_NOW);

            //人员培训分类
            List<DropDown> TRAIN_TYPE = GetOaOrSys6BaseDataDropDown("人员培训分类", BaseDataSourceType.OA);
            keyValuePairs.Add("TRAIN_TYPE", TRAIN_TYPE);
            keyValuePairs.TryAdd("人员培训分类", TRAIN_TYPE);

            return keyValuePairs;
        }

        /// <summary>
        /// 获取职称下拉
        /// </summary>
        /// <returns></returns>
        public Dictionary<string, List<LevelDropDown>> GetLevelTitle()
        {
            Dictionary<string, List<LevelDropDown>> keyValuePairs = new Dictionary<string, List<LevelDropDown>>();
            List<LevelDropDown> TECH_POSTS_TECH = GetLevelTitleDropDown("技师职称");
            keyValuePairs.Add("TECH_POST_TECH", TECH_POSTS_TECH);

            List<LevelDropDown> TECH_POSTS_DOCTOR = GetLevelTitleDropDown("医师职称");
            keyValuePairs.Add("TECH_POST_DOCTOR", TECH_POSTS_DOCTOR);

            List<LevelDropDown> TECH_POSTS_NURSE = GetLevelTitleDropDown("护士职称");
            keyValuePairs.Add("TECH_POST_NURSE", TECH_POSTS_NURSE);

            List<LevelDropDown> TECH_POSTS_RESEARCHER = GetLevelTitleDropDown("研究员职称");
            keyValuePairs.Add("TECH_POSTS_RESEARCHER", TECH_POSTS_RESEARCHER);

            return keyValuePairs;
        }
        /// <summary>
        /// 查询工具箱，把所有下拉类型的属性对应的字典加载到前端
        /// </summary>
        /// <param name="keyValuePairs"></param>
        /// <param name="hospital_id"></param>
        private Dictionary<string, List<DropDown>> AddPageSettingBaseDataInfo(Dictionary<string, List<DropDown>> keyValuePairs, string hospital_id)
        {
            List<SYS6_MODULE_FUNC_DICT> dicts = _soa.Db.Queryable<SYS6_MODULE_FUNC_DICT>().Where(a => a.SETUP_STATE == "1" && a.MODULE_ID == "H81" && a.SETUP_CLASS == "B05-52|B05-53" && a.HOSPITAL_ID == hospital_id).ToList();
            foreach (var dic in dicts)
            {
                if (dic.FORM_JSON.IsNotNullOrEmpty())
                {
                    PageSettingForm json = null;
                    try
                    {
                        json = JsonConvert.DeserializeObject<PageSettingForm>(dic.FORM_JSON);
                    }
                    catch (Exception ex)
                    {
                        Log.Error(ex.ToString());
                    }
                    if (json == null || json.form == null)
                        break;
                    foreach (Form form in json.form)
                    {
                        if (form.dataClass.IsNotNullOrEmpty() && !keyValuePairs.ContainsKey(form.dataClass))
                        {
                            List<DropDown> dropDowns = GetOaOrSys6BaseDataDropDown(form.dataClass);
                            if (dropDowns.Any())
                                keyValuePairs[form.dataClass] = dropDowns;
                        }
                    }
                }
            }
            return keyValuePairs;
        }

        private List<DropDown> GetOaOrSys6BaseDataDropDown(string classId, BaseDataSourceType soureType = BaseDataSourceType.Auto)
        {
            List<DropDown> dropDowns = new List<DropDown>();
            Action<string> FillDropDownsFromOA = classId =>
            {
                var dataClass = _IBaseDataServices.GetOaBaseData().FindAll(x => x.CLASS_ID == classId && (x.HOSPITAL_ID == _httpContext.GetHospitalId() || x.HOSPITAL_ID == "H0000")).OrderBy(x => x.DATA_SORT);
                foreach (var item in dataClass)
                {
                    dropDowns.Add(new DropDown
                    {
                        key = item.DATA_ID,
                        value = item.DATA_NAME
                    });
                }
            };

            Action<string> FillDropDownsFromSYS = classId =>
            {
                var sysDataClass = _IBaseDataServices.GetSys6BaseData().FindAll(x => x.CLASS_ID == classId && (x.HOSPITAL_ID == _httpContext.GetHospitalId() || x.HOSPITAL_ID == "H0000")).OrderBy(x => x.DATA_SORT);
                foreach (var item in sysDataClass)
                {
                    dropDowns.Add(new DropDown
                    {
                        key = item.DATA_ID,
                        value = item.DATA_CNAME
                    });
                }
            };

            switch (soureType)
            {
                case BaseDataSourceType.Auto:

                    //字典优先获取OA_BASE_DATA表
                    FillDropDownsFromOA(classId);
                    if (!dropDowns.Any())
                    {
                        //然后获取SYS6_BASE_DATA表
                        FillDropDownsFromSYS(classId);
                    }
                    break;
                case BaseDataSourceType.OA:
                    FillDropDownsFromOA(classId);
                    break;
                case BaseDataSourceType.SYS:
                    FillDropDownsFromSYS(classId);
                    break;
            }

            return dropDowns;
        }
        private List<LevelDropDown> GetLevelTitleDropDown(string classId)
        {
            List<LevelDropDown> dropDowns = new List<LevelDropDown>();

            var sysDataClass = _IBaseDataServices.GetSys6BaseData().FindAll(x => x.CLASS_ID == classId);
            foreach (var item in sysDataClass)
            {
                dropDowns.Add(new LevelDropDown
                {
                    key = item.DATA_ID,
                    value = item.DATA_CNAME,
                    level = item.HIS_ID.IsNotNullOrEmpty() && item.HIS_ID.Contains("LEVEL:") ? item.HIS_ID.Split("LEVEL:")[1] : ""
                });
            }
            return dropDowns;
        }

        /// <summary>
        /// 获取人员下拉信息
        /// </summary>
        /// <param name="hospital_id"></param>
        /// <param name="lab_id"></param>
        /// <returns></returns>
        public List<DropDowDto> GetUserDropDownInfo(string hospital_id, string lab_id)
        {
            List<DropDowDto> dropdown = new List<DropDowDto>();
            string menuId = "H8109";
            var orgParm = new OrgParams { hospital_id = hospital_id, lab_id = lab_id };
            List<SampleUserDto> permissionUsers = _IAuthorityService2.GetPermissionUser(_soa, orgParm, new List<string> { menuId });
            foreach (var permissionUser in permissionUsers)
            {
                string strValue = string.Empty;
                if (permissionUser.HIS_ID == null || permissionUser.HIS_ID == "")
                {
                    strValue = permissionUser.LOGID + "_" + permissionUser.USERNAME;
                }
                else
                {
                    strValue = permissionUser.HIS_ID + "_" + permissionUser.USERNAME;
                }
                dropdown.Add(new DropDowDto
                {
                    key = permissionUser.USER_NO,
                    value = strValue
                });
            }

            return dropdown;
        }


        ///根据用户ID获取人员ID
        public string GetPersonId(string hospital_id, string user_no)
        {
            string personId = string.Empty;
            PMS_PERSON_INFO pms_person_info = _soa.Db.Queryable<PMS_PERSON_INFO>().First(P => P.HOSPITAL_ID == hospital_id && P.USER_ID == user_no);
            if (pms_person_info != null)
            {
                personId = pms_person_info.PERSON_ID;
            }
            return personId;
        }
        /// <summary>
        /// 获取院区名称
        /// </summary>
        /// <param name="areaId"></param>
        /// <returns></returns>
        public string GetAreaName(string areaId)
        {
            string areaName = string.Empty;
            SYS6_INSPECTION_AREA sys6InspectionArea = _soa.Db.Queryable<SYS6_INSPECTION_AREA>().First(P => P.AREA_ID == areaId);
            if (sys6InspectionArea != null)
            {
                areaName = sys6InspectionArea.AREA_NAME;
            }
            return areaName;
        }
        /// <summary>
        ///获取基础数据名称
        /// </summary>
        /// <param name="class_id"></param>
        /// <param name="data_id"></param>
        /// <returns></returns>
        public string RecordClassBaseName(List<SYS6_BASE_DATA> sys6_base_data, string class_id, string data_id)
        {
            string className = string.Empty;
            if (data_id != null)
            {
                if (sys6_base_data.Where(x => x.CLASS_ID == class_id && x.DATA_ID == data_id).FirstOrDefault() != null)
                {
                    className = sys6_base_data.Where(x => x.CLASS_ID == class_id && x.DATA_ID == data_id).FirstOrDefault().DATA_CNAME;
                }
            }
            return className;
        }

        /// <summary>
        /// 获取记录单填写的总行数
        /// </summary>
        /// <param name="person_id"></param>
        /// <param name="hospital_id"></param>
        /// <returns></returns>
        public bool GitRecordWriteRowNum(string person_id, string hospital_id)
        {
            int RowNum = 0;
            bool if_complete = false;
            //履历记录
            int resumeCount = _soa.Db.Queryable<PMS_RESUME_LIST>().Where(P => P.HOSPITAL_ID == hospital_id && P.PERSON_ID == person_id && P.CHECK_STATE == "2").Count();
            if (resumeCount > 0)
            {
                RowNum += resumeCount;
            }
            //教育记录
            int educationCount = _soa.Db.Queryable<PMS_EDUCATION_LIST>().Where(P => P.HOSPITAL_ID == hospital_id && P.PERSON_ID == person_id && P.CHECK_STATE == "2").Count();
            if (educationCount > 0)
            {
                RowNum += educationCount;
            }
            if (RowNum > 0)
            {
                if_complete = true;
            }
            return if_complete;
        }

        /// <summary>
        /// 通过PERSON_ID判断是否存在人事档案录入记录（基本信息的必填项+置灰项+履历记录，三者都有填才视为存在）
        /// </summary>
        /// <param name="person_id"></param>
        /// <param name="hospital_id"></param>
        /// <returns></returns>
        public Dictionary<string, bool> GetPersonHasRecord(List<string> pgroup_ids, string hospital_id)
        {

            var query = _soa.Db.Queryable<SYS6_USER>()
                .InnerJoin<PMS_PERSON_INFO>((user, person) => user.USER_NO == person.USER_ID && user.HOSPITAL_ID == hospital_id && user.STATE_FLAG == "1" && pgroup_ids.Contains(user.DEPT_CODE))
                .Where((user, person) => person.SEX != null && person.AGE != null && person.HIGHEST_DEGREE != null && person.TECHNOLOGY_TYPE != null  //必填项
                                    && person.USER_NAME != null && person.PGROUP_ID != null && person.DUTIES != null /*&& person.PERSON_DOC_STATE != null*/ && person.USER_TYPE != null /*&& person.ACADEMIC_POST != null*/)//自动同步项
                .Where((user, person) => SqlFunc.Subqueryable<PMS_RESUME_LIST>().Where(resume => resume.PERSON_ID == person.PERSON_ID && resume.RESUME_STATE != "0" && resume.CHECK_STATE == "2").Any()) //履历记录(审核后)
                .Select((user, person) => new
                {
                    person.USER_ID,
                    person.PERSON_ID,
                    //HAS_RESUME = SqlFunc.Subqueryable<PMS_RESUME_LIST>().Where(resume => resume.PERSON_ID == person.PERSON_ID && resume.RESUME_STATE != "0").Any(),
                    //HAS_EDU = SqlFunc.Subqueryable<PMS_EDUCATION_LIST>().Where(edu => edu.PERSON_ID == person.PERSON_ID && edu.EDUCATION_STATE != "0").Any()
                }
                ).ToList();

            var result = new Dictionary<string, bool>();
            foreach (var person in query)
            {
                result[person.PERSON_ID] = true;
            }
            return result;
        }


        /// <summary>
        /// 获取用户的岗位权限许可的导航列表（或按钮列表，权限类列表）
        /// </summary>
        /// <param name="userNo"></param>
        /// <param name="menuClass"></param>
        /// <param name="parentCode"></param>
        /// <param name="hospitalId"></param>
        /// <param name="labId"></param>
        /// <param name="pGroupId"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public List<SYS6_MENU> GetUserMenuList(string userNo, MenuClassEnum menuClass = MenuClassEnum.ALL, string parentCode = null, string hospitalId = null, string labId = null, string pGroupId = null)
        {
            using (var op = LogStopwatch.Begin("获取菜单信息A"))
            {
                if (pGroupId != null)
                {
                    throw new Exception("参数pGroupId不为空时，参数labId必填！");
                }
                string menuClassStr;
                switch (menuClass)
                {
                    case MenuClassEnum.NAV_MENU:
                        menuClassStr = "nav_menu";
                        break;
                    case MenuClassEnum.BUTTON:
                        menuClassStr = "button";
                        break;
                    case MenuClassEnum.CLASS:
                        menuClassStr = "class";
                        break;
                    default:
                        menuClassStr = null;
                        break;
                }
                var result = new List<SYS6_MENU>();
                var moduleId = AppSettingsProvider.CurrModuleId;

                List<string> postIds = _soa.Db.Queryable<SYS6_POST>()
                    .InnerJoin<SYS6_USER_POST>((post, userPost) => post.POST_ID == userPost.POST_ID && post.POST_STATE == "1" && userPost.USER_NO == userNo
                            && (hospitalId == null || post.HOSPITAL_ID == hospitalId) && (labId == null || post.LAB_ID == labId) && (pGroupId == null || post.PGROUP_ID == "PG000" || post.PGROUP_ID == pGroupId))
                    .Select((post, userPost) => post.POST_ID)
                    .Distinct()
                    .ToList();

                op.Lap($"postIds查询：记录数{postIds.Count}");

                List<SYS6_MENU> menuList = _soa.Db.Queryable<SYS6_POST_ROLE_COM>()
                    .InnerJoin<SYS6_ROLE_COM_LIST>((roleCom, comList) => comList.ROLECOM_ID == roleCom.ROLECOM_ID && postIds.Contains(roleCom.POST_ID) && comList.ROLE_STATE == "1" && comList.MODULE_ID == moduleId)
                    .InnerJoin<SYS6_ROLE>((roleCom, comList, role) => comList.ROLE_ID == role.ROLE_NO && role.ROLE_STATE == "1" && role.MODULE_ID == moduleId)
                    .InnerJoin<SYS6_ROLE_MENU>((roleCom, comList, role, roMenu) => comList.ROLE_ID == roMenu.ROLE_NO)
                    .InnerJoin<SYS6_MENU>((roleCom, comList, role, roMenu, menu) => roMenu.MENU_NO == menu.MENU_ID)
                    .Where((roleCom, comList, role, roMenu, menu) => menu.SYS_MENU == moduleId && menu.MENU_STATE == "1")
                    .WhereIF(menuClassStr != null, (roleCom, comList, role, roMenu, menu) => menu.MENU_CLASS == menuClassStr)
                    .WhereIF(parentCode != null, (roleCom, comList, role, roMenu, menu) => menu.PARENT_CODE == parentCode)
                    .Select((roleCom, comList, role, roMenu, menu) => menu)
                    .Distinct()
                    .ToList();

                op.Lap($"menuList查询：记录数{menuList.Count}");

                //日志结束
                op.Complete();
                return menuList;
            }
        }

        /// <summary>
        /// 判断用户在具体条件下（医院、科室、专业）是否存在某一权限（页面/按钮/权限类）
        /// </summary>
        /// <param name="userNo"></param>
        /// <param name="menuId">需要判断权限的菜单ID（可以是页面/按钮/类），来源于：select * from XH_SYS.SYS6_MENU where sys_menu ='H98'</param>
        /// <param name="hospitalId"></param>
        /// <param name="labId"></param>
        /// <param name="pGroupId"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public bool CheckUserMenuPermission(string userNo, string menuId, string hospitalId = null, string labId = null, string pGroupId = null, bool isThrowException = false)
        {
            if (pGroupId != null)
            {
                throw new Exception("参数pGroupId不为空时，参数labId必填！");
            }
            var moduleId = AppSettingsProvider.CurrModuleId;

            bool hasPermit = _soa.Db.Queryable<SYS6_POST>()
                  .InnerJoin<SYS6_USER_POST>((post, userPost) => post.POST_ID == userPost.POST_ID && post.POST_STATE == "1" && userPost.USER_NO == userNo
                        && (hospitalId == null || post.HOSPITAL_ID == hospitalId) && (labId == null || post.LAB_ID == labId) && (pGroupId == null || post.PGROUP_ID == "PG000" || post.PGROUP_ID == pGroupId))
                    .InnerJoin<SYS6_POST_ROLE_COM>((post, userPost, roleCom) => userPost.POST_ID == roleCom.POST_ID)
                    .InnerJoin<SYS6_ROLE_COM_LIST>((post, userPost, roleCom, comList) => comList.ROLECOM_ID == roleCom.ROLECOM_ID && comList.ROLE_STATE == "1" && comList.MODULE_ID == moduleId)
                    .InnerJoin<SYS6_ROLE>((post, userPost, roleCom, comList, role) => comList.ROLE_ID == role.ROLE_NO && role.ROLE_STATE == "1" && role.MODULE_ID == moduleId)
                    .InnerJoin<SYS6_ROLE_MENU>((post, userPost, roleCom, comList, role, roMenu) => comList.ROLE_ID == roMenu.ROLE_NO)
                    .Where((post, userPost, roleCom, comList, role, roMenu) => roMenu.MENU_NO == menuId)
                    .Any();
            if (hasPermit)
                //判断（页面/按钮/权限类）有效性
                hasPermit = _soa.Db.Queryable<SYS6_MENU>().Where(a => a.MENU_ID == menuId && a.SYS_MENU == moduleId && a.MENU_STATE == "1").Any();

            if (!hasPermit && isThrowException)
            {
                throw new Exception("当前用户没有该操作的权限！");
            }
            return hasPermit;
        }

        /// <summary>
        /// 获取用户某一权限（页面/按钮/权限类）下的专业组列表，返回结果包含所有状态的科室
        /// </summary>
        /// <param name="userNo"></param>
        /// <param name="menuId">需要判断权限的菜单ID（可以是页面/按钮/类），来源于：select * from XH_SYS.SYS6_MENU where sys_menu ='H98'</param>
        /// <param name="hospitalId"></param>
        /// <param name="labId"></param>
        /// <returns></returns>
        public List<SYS6_INSPECTION_PGROUP> GetUserPermissionPgroup(string userNo, string menuId, string hospitalId = null, string labId = null)
        {
            var moduleId = AppSettingsProvider.CurrModuleId;

            List<string> postIds = _soa.Db.Queryable<SYS6_POST_ROLE_COM>()
                .InnerJoin<SYS6_ROLE_COM_LIST>((roleCom, comList) => comList.ROLECOM_ID == roleCom.ROLECOM_ID && comList.ROLE_STATE == "1"
                && comList.MODULE_ID == moduleId)
                        .InnerJoin<SYS6_ROLE>((roleCom, comList, role) => comList.ROLE_ID == role.ROLE_NO && role.ROLE_STATE == "1"
                        && role.MODULE_ID == moduleId)
                        .InnerJoin<SYS6_ROLE_MENU>((roleCom, comList, role, roMenu) => comList.ROLE_ID == roMenu.ROLE_NO)
                        .InnerJoin<SYS6_MENU>((roleCom, comList, role, roMenu, menu) => roMenu.MENU_NO == menu.MENU_ID && roMenu.MENU_NO == menuId)
                        .Select((roleCom, comList, role, roMenu, menu) => roleCom.POST_ID)
                        .Distinct()
                        .ToList();

            List<SYS6_INSPECTION_PGROUP> groupList = _soa.Db.Queryable<SYS6_POST>()
                 .InnerJoin<SYS6_USER_POST>((post, userPost) => post.POST_ID == userPost.POST_ID && userPost.USER_NO == userNo
                 && post.POST_STATE == "1" && (hospitalId == null || post.HOSPITAL_ID == hospitalId) && (labId == null || post.LAB_ID == labId))
                 .InnerJoin<SYS6_INSPECTION_PGROUP>((post, userPost, group) => post.PGROUP_ID == group.PGROUP_ID ||
                 (post.PGROUP_ID == "PG000" && post.LAB_ID == group.LAB_ID))
                 .Where((post, userPost, group) => postIds.Contains(post.POST_ID))
                 .Select((post, userPost, group) => group)
                 .Distinct()
                 .ToList();

            return groupList;
        }

        /// <summary>
        /// 获取专业组列表
        /// </summary>
        /// <param name="hospitalId"></param>
        /// <param name="labId"></param>
        /// <returns></returns>
        public List<SYS6_INSPECTION_PGROUP> GetPgroupList(string hospitalId, string labId = null)
        {
            var pgroupList = _soa.Db.Queryable<SYS6_INSPECTION_PGROUP>()
                .Where(a => a.PGROUP_STATE == "1" && a.HOSPITAL_ID == hospitalId)
                .WhereIF(labId.IsNotNullOrEmpty(), a => a.LAB_ID == labId)
                .ToList();

            return pgroupList;
        }

        /// <summary>
        /// 获取备案实验室列表
        /// </summary>
        /// <param name="hospitalId"></param>
        /// <param name="labId"></param>
        /// <returns></returns>
        public List<SMBL_LAB> GetSmblLabList(string hospitalId, string labId = null)
        {
            var smblLabList = _soa.Db.Queryable<SMBL_LAB>()
                .Where(a => a.SMBL_LAB_STATE == "1" && a.HOSPITAL_ID == hospitalId)
                .WhereIF(labId.IsNotNullOrEmpty(), a => a.LAB_ID == labId)
                .ToList();

            return smblLabList;
        }



        /// <summary>
        /// 获取用户某一权限（页面/按钮/权限类）下的科室列表，返回结果包含所有状态的科室
        /// </summary>
        /// <param name="userNo"></param>
        /// <param name="menuId">需要判断权限的菜单ID（可以是页面/按钮/类），来源于：select * from XH_SYS.SYS6_MENU where sys_menu ='H98'</param>
        /// <param name="hospitalId"></param>
        /// <param name="labId"></param>
        /// <returns></returns>
        public List<SYS6_INSPECTION_LAB> GetUserPermissionLab(string userNo, string menuId, string hospitalId = null, string labId = null)
        {
            var moduleId = AppSettingsProvider.CurrModuleId;

            List<string> postIds = _soa.Db.Queryable<SYS6_POST_ROLE_COM>()
                .InnerJoin<SYS6_ROLE_COM_LIST>((roleCom, comList) => comList.ROLECOM_ID == roleCom.ROLECOM_ID && comList.ROLE_STATE == "1" && comList.MODULE_ID == moduleId)
                        .InnerJoin<SYS6_ROLE>((roleCom, comList, role) => comList.ROLE_ID == role.ROLE_NO && role.ROLE_STATE == "1" && role.MODULE_ID == moduleId)
                        .InnerJoin<SYS6_ROLE_MENU>((roleCom, comList, role, roMenu) => comList.ROLE_ID == roMenu.ROLE_NO)
                        .InnerJoin<SYS6_MENU>((roleCom, comList, role, roMenu, menu) => roMenu.MENU_NO == menu.MENU_ID && roMenu.MENU_NO == menuId)
                        .Select((roleCom, comList, role, roMenu, menu) => roleCom.POST_ID)
                        .Distinct()
                        .ToList();

            List<SYS6_INSPECTION_LAB> labList = _soa.Db.Queryable<SYS6_POST>()
                 .InnerJoin<SYS6_USER_POST>((post, userPost) => post.POST_ID == userPost.POST_ID && userPost.USER_NO == userNo && post.POST_STATE == "1" && (hospitalId == null || post.HOSPITAL_ID == hospitalId) && (labId == null || post.LAB_ID == labId))
                 .InnerJoin<SYS6_INSPECTION_LAB>((post, userPost, lab) => post.LAB_ID == lab.LAB_ID)
                 .Where((post, userPost, lab) => postIds.Contains(post.POST_ID))
                 .Select((post, userPost, lab) => lab)
                 .Distinct()
                 .ToList();

            return labList;
        }

        List<PMS_ADDN_CLASS_INFO> pmsAddnClassInfoCache;
        public List<PMS_ADDN_CLASS_INFO> GetPmsAddnClassInfo(bool isIncludeDisable = false)
        {
            string smblFlag = _httpContext.GetSmblFlag();
            if (pmsAddnClassInfoCache == null)
                pmsAddnClassInfoCache = _soa.Db.Queryable<PMS_ADDN_CLASS_INFO>()
                    .Where(a => (a.CLASS_TYPE == "0" || a.CLASS_TYPE == "1" || a.CLASS_TYPE == "99"))//排除掉11-P3实验室的分类
                    .WhereIF(!isIncludeDisable, a => a.CLASS_STATE == "1")
                    .WhereIF(smblFlag == "1", a => a.SMBL_FLAG == "1")
                    .OrderBy(a => a.CLASS_SORT).ToList();
            return pmsAddnClassInfoCache;
        }

        /// <summary>
        ///  同时获取人员列表和组织树（两者都需要查专业组范围，合并在一次查询）
        /// </summary>
        /// <param name="orgParm"></param>
        /// <param name="person_name"></param>
        /// <param name="permissMenuId"></param>
        /// <returns></returns>
        public PersonRange GetPgroupRangePersonList(OrgUserParams orgParm, string person_name, string permissMenuId, bool ifCheckPremission = true)
        {
            var result = new PersonRange();

            var tree = _organizationTreeService2.GetOrgTreeType_Lab_A(_soa, orgParm, "H81", true, ifCheckPremission);

            var treeAllNodes = tree.GetAllNodes();

            var pgroupdIds = treeAllNodes.Where(a => a.NODE_TYPE == GroupTreeNodeTypeEnum.PGROUP.ToIntStr() && (a.SOURCE as SYS6_INSPECTION_PGROUP)?.PGROUP_CLASS == "1") //只显示检验类PGROUP_CLASS == "1"
                .Select(a => a.SOURCE_ID).ToList();

            //人员分类
            var userClassList = _soa.Db.Queryable<SYS6_USER_CLASS_DICT>().Where(a => a.USERCLASS_STATE == "1")
                .WhereIF(orgParm.hospital_id.IsNotNullOrEmpty(), a => a.HOSPITAL_ID == orgParm.hospital_id)
                .WhereIF(orgParm.lab_id.IsNotNullOrEmpty(), a => a.LAB_ID == orgParm.lab_id)
                .ToList()
                .WhereIF(orgParm.area_id.IsNotNullOrEmpty(), a => a.AREA_SID != null && a.AREA_SID.Split('+').Contains(orgParm.area_id))                
                .ToList();

            foreach (var userClass in userClassList)
            {
                var labNode = treeAllNodes.Where(a => a.NODE_TYPE == GroupTreeNodeTypeEnum.LAB.ToIntStr() && a.SOURCE_ID == userClass.LAB_ID).FirstOrDefault();
                if (labNode != null)
                {
                    var classNode = new OrgTreeNode
                    {
                        NAME = userClass.USERCLASS_NAME,
                        LAB_ID = userClass.LAB_ID,
                        SOURCE_ID = userClass.USERCLASS_ID,
                        NODE_TYPE = GroupTreeNodeTypeEnum.CLASS.ToIntStr(),
                        NODE_TYPE_NAME = GroupTreeNodeTypeEnum.CLASS.ToDesc(),
                    };
                    labNode.ChildAdd(classNode);
                }
            }

            pgroupdIds.AddRange(userClassList.Select(a => a.USERCLASS_ID).ToList());

            var personList = _soa.Db.Queryable<PMS_PERSON_INFO>()
                .Where(p => p.PERSON_STATE == "1"
                && (((p.PERSON_TYPE == "1" || p.PERSON_TYPE == null) && pgroupdIds.Contains(p.PGROUP_ID)) //兼容旧系统数据，PERSON_TYPE为空
                || (p.PERSON_TYPE != "1" && p.PERSON_TYPE != null)) //其它类型时，没有专业组
                                                                    //&& p.LAB_ID != null //科室不为空
                                                                    //&& p.HIS_ID != null  //工号不为空
                                                                    //&& u.MANAGE_CLASS != null && u.MANAGE_CLASS == "4") //非机构级、分组级管理账号)
                && (p.HIS_ID != null && p.HIS_ID != "0000" && p.HIS_ID != "00000")   //排除0000工号
                && (p.PERSON_TYPE == null || (p.PERSON_TYPE != "10" && p.PERSON_TYPE != "13"))) //25-6-27省人悦琳/朱刚提出需求，排除【信息维护工程师】、【生物安全】
                .WhereIF(person_name.IsNotNullOrEmpty(), p => p.USER_NAME.Contains(person_name))
                .ToList()
                /*.Where(p => (p.PERSON_DOC_STATE == "1" || p.PERSON_DOC_STATE == "4")) *///新逻辑不用
                .Where(p => new List<string> { "1", "2", "3", "4", "5", "6", "7" }.Contains(p.USER_TYPE) || p.USER_TYPE == null)  //USER_TYPE可空
                .OrderBy(p => p.PGROUP_ID).ThenBy(p => p.USER_NAME)
                .ToList();

            result.Tree = tree;
            result.PersonList = personList;
            result.UserClassList = userClassList;
            result.PgroupIds = pgroupdIds;

            return result;
        }

        ///// <summary>
        ///// 同时获取人员列表和组织树（两者都需要查专业组范围，合并在一次查询）
        ///// </summary>
        ///// <param name="orgParm"></param>
        ///// <param name="person_name"></param>
        ///// <param name="permissMenuId"></param>
        ///// <returns></returns>
        //private List<PMS_PERSON_INFO> GetRangePersonList(OrgUserParams orgParm, string person_name, string permissMenuId)
        //{
        //    var smblLabList = _IAuthorityService.GetUserPermissionPgroup(_soa, orgParm, permissMenuId);
        //    var pgroupdIds = smblLabList.Select(a => a.PGROUP_ID).ToList();
        //    var personList = _soa.Db.Queryable<PMS_PERSON_INFO>()
        //        .Where(p => p.PERSON_STATE == "1" && pgroupdIds.Contains(p.PGROUP_ID)
        //         && p.LAB_ID != null && p.HIS_ID != null  //科室、工号都不为空
        //         && p.HIS_ID != "0000" && p.HIS_ID != "00000")   //排除0000工号
        //         //&& u.MANAGE_CLASS != null && u.MANAGE_CLASS == "4") //非机构级、分组级管理账号)
        //         .Where(p => (p.PERSON_DOC_STATE == "1" || p.PERSON_DOC_STATE == "4") && (new List<string> { "1", "2", "3", "4", "5", "6", "7" }.Contains(p.USER_TYPE)))
        //         .WhereIF(person_name.IsNotNullOrEmpty(), p => p.USER_NAME.Contains(person_name))
        //        .ToList();
        //    return personList;
        //}

        public List<PMS_PERSON_INFO> GetRangePersonList(OrgUserParams orgParm, string person_name, string permissMenuId, out List<SYS6_USER> userList, out OrgTree areaGroupTree)
        {
            //【注意】业务代码Models.SugarDbContext类的SugarDbContext_Base 必须继承 XH.LAB.UTILS.Models的SugarDbContext_Base_Utils
            // 按条件查询专业组树（tree是以树结构返回，line是相同对象以平铺列表返回）
           // (areaGroupTree, groupLine) = _IAuthorityService.GetAreaGroupTree(_soa, orgParm, permissMenuId, true);
            OrgTree orgTrees = _organizationTreeService2.GetOrgTreeType_Lab_A(_soa, orgParm,"H81",true);
            areaGroupTree = orgTrees;
            List<string> allPgroupIds = orgTrees.GetAllNodes(a => a.NODE_TYPE == PMSTreeNodeTypeEnum.PGROUP.ToIntStr()
                    && (a.SOURCE as SYS6_INSPECTION_PGROUP).PGROUP_CLASS != "3" && (a.SOURCE as SYS6_INSPECTION_PGROUP).PGROUP_CLASS != "4") //  && (a.SOURCE as SYS6_INSPECTION_PGROUP).MGROUP_ID != null
                  .Select(a => a.SOURCE_ID).ToList();

            //2025-6-10注释代码，因为不再参考用户表
            {
                //连接用户表进行查询，避免人员信息表同步问题导致的数据不正确
                var personUserList = _soa.Db.Queryable<SYS6_USER>().InnerJoin<PMS_PERSON_INFO>((u, p) => u.USER_NO == p.USER_ID)
                   .Where((u, p) => u.STATE_FLAG == "1" && allPgroupIds.Contains(u.DEPT_CODE)
                     && u.LAB_ID != null && u.HIS_ID != null  //科室、工号都不为空
                     && u.HIS_ID != "0000" && u.HIS_ID != "00000")   //排除0000工号
                                                                     //&& u.MANAGE_CLASS != null && u.MANAGE_CLASS == "4") //非机构级、分组级管理账号)
                   .Select((u, p) => new { u, p }).ToList();

                //为保证统计数据一致性，使分量数据之和等于总数据，统计前作以下限定
                List<PMS_PERSON_INFO> personList = personUserList.Select(a => a.p)
                    //.Where(p => p.PERSON_DOC_STATE == "1" || p.PERSON_DOC_STATE == "4")  //新人岗权不用
                    .Where(p =>new List<string> { "1", "2", "3", "4", "5", "6", "7" }.Contains(p.USER_TYPE))
                    .Distinct().ToList();

                //以HisID去重
                personList = personList.GroupBy(g => g.HIS_ID).Select(g => g.OrderBy(a => a.FIRST_RTIME).FirstOrDefault()).ToList();

                //姓名过滤须放在去重之后，否则结果不稳定
                if (person_name.IsNotNullOrEmpty())
                    personList = personList.Where(p => p.USER_NAME.Contains(person_name) || (p.HIS_ID != null && p.HIS_ID.Contains(person_name))).ToList();

                var userIds = personList.Select(a => a.USER_ID).Distinct().ToList();
                userList = userIds.Select(a => personUserList.Find(p => p.u.USER_NO == a).u).ToList();
                return personList;
            }
        }

        public List<AreaGroupTreeNode> GetTestOrganizationTree(string? hospital_id)
        {
            var result = new List<AreaGroupTreeNode>();
            var hospitals = _soa.Db.Queryable<SYS6_HOSPITAL_INFO>()
                .Where(a => a.HOSPITAL_STATE == "1")
                .WhereIF(hospital_id.IsNotNullOrEmpty(), a => a.HOSPITAL_ID == hospital_id)
                .ToList();

            var labs = _soa.Db.Queryable<SYS6_INSPECTION_LAB>()
                .Where(a => a.STATE_FLAG == "1")
                .WhereIF(hospital_id.IsNotNullOrEmpty(), a => a.HOSPITAL_ID == hospital_id)
                .ToList();

            var smbl_labs = _soa.Db.Queryable<SMBL_LAB>()
                //.Where(a => a.SMBL_LAB_STATE == "1")
                .WhereIF(hospital_id.IsNotNullOrEmpty(), a => a.HOSPITAL_ID == hospital_id)
                .ToList();

            //var mgroups = _soa.Db.Queryable<SYS6_INSPECTION_MGROUP>()
            //    .Where(a => a.MGROUP_STATE == "1")
            //    .WhereIF(hospital_id.IsNotNullOrEmpty(), a => a.HOSPITAL_ID == hospital_id)
            //    .ToList();

            var pgroups = _soa.Db.Queryable<SYS6_INSPECTION_PGROUP>()
                .Where(a => a.PGROUP_STATE == "1")
                .WhereIF(hospital_id.IsNotNullOrEmpty(), a => a.HOSPITAL_ID == hospital_id)
                .ToList();

            var users = _soa.Db.Queryable<SYS6_USER>()
                .Where(a => a.STATE_FLAG == "1")
                .WhereIF(hospital_id.IsNotNullOrEmpty(), a => a.HOSPITAL_ID == hospital_id)
                .ToList();

            int index = 1;
            foreach (var h in hospitals)
            {
                var hNode = new AreaGroupTreeNode();
                hNode.NAME = h.HOSPITAL_CNAME;
                hNode.NODE_NO = index++;
                hNode.NODE_TYPE = GroupTreeNodeTypeEnum.HOSPITAL.ToIntStr();
                hNode.NODE_TYPE_NAME = GroupTreeNodeTypeEnum.HOSPITAL.ToDesc();
                hNode.SOURCE_ID = h.HOSPITAL_ID;
                hNode.SOURCE = h;
                hNode.SOURCE_PATH = h.HOSPITAL_ID;

                result.Add(hNode);

                var hLevelNode = new AreaGroupTreeNode();
                hLevelNode.NAME = "机构";
                hLevelNode.NODE_NO = index++;
                hLevelNode.NODE_TYPE = GroupTreeNodeTypeEnum.HOSPITAL_LEVEL.ToIntStr();
                hLevelNode.NODE_TYPE_NAME = GroupTreeNodeTypeEnum.HOSPITAL_LEVEL.ToDesc();
                hLevelNode.SOURCE_ID = h.HOSPITAL_ID;
                hLevelNode.SOURCE = h;
                hLevelNode.SOURCE_PATH = $"{h.HOSPITAL_ID}/112";

                hNode.CHILDREN.Add(hLevelNode);

                foreach (var lab in labs.Where(a => a.HOSPITAL_ID == h.HOSPITAL_ID))
                {
                    var lNode = new AreaGroupTreeNode();
                    lNode.NAME = lab.LAB_NAME;
                    lNode.NODE_NO = index++;
                    lNode.NODE_TYPE = GroupTreeNodeTypeEnum.LAB.ToIntStr();
                    lNode.NODE_TYPE_NAME = GroupTreeNodeTypeEnum.LAB.ToDesc();
                    lNode.SOURCE_ID = lab.LAB_ID;
                    lNode.SOURCE = lab;
                    lNode.SOURCE_PATH = $"{h.HOSPITAL_ID}/{lab.LAB_ID}";

                    hNode.CHILDREN.Add(lNode);

                    var lLevelNode = new AreaGroupTreeNode();
                    lLevelNode.NAME = "科室";
                    lLevelNode.NODE_NO = index++;
                    lLevelNode.NODE_TYPE = GroupTreeNodeTypeEnum.LAB_LEVEL.ToIntStr();
                    lLevelNode.NODE_TYPE_NAME = GroupTreeNodeTypeEnum.LAB_LEVEL.ToDesc();
                    lLevelNode.SOURCE_ID = lab.LAB_ID;
                    lLevelNode.SOURCE = null;
                    lLevelNode.SOURCE_PATH = $"{h.HOSPITAL_ID}/113";

                    lNode.CHILDREN.Add(lLevelNode);


                    foreach (var slab in smbl_labs.Where(a => a.LAB_ID == lab.LAB_ID))
                    {
                        var sNode = new AreaGroupTreeNode();
                        sNode.NAME = slab.SMBL_LAB_NAME;
                        sNode.NODE_NO = index++;
                        sNode.NODE_TYPE = GroupTreeNodeTypeEnum.SMBL_LAB.ToIntStr();
                        sNode.NODE_TYPE_NAME = GroupTreeNodeTypeEnum.SMBL_LAB.ToDesc();
                        sNode.SOURCE_ID = slab.LAB_ID;
                        sNode.SOURCE = slab;
                        sNode.SOURCE_PATH = OfficeHelper.CombinePath(hNode.SOURCE_PATH, slab.LAB_ID);

                        lNode.CHILDREN.Add(sNode);
                        var pgroupIds = slab.PGROUP_SID.Split(',');

                        foreach (var pgroup in pgroups.Where(a => pgroupIds.Contains(a.PGROUP_ID)))
                        {
                            var pgNode = new AreaGroupTreeNode();
                            pgNode.NAME = pgroup.PGROUP_NAME;
                            pgNode.NODE_NO = index++;
                            pgNode.NODE_TYPE = GroupTreeNodeTypeEnum.PGROUP.ToIntStr();
                            pgNode.NODE_TYPE_NAME = GroupTreeNodeTypeEnum.PGROUP.ToDesc();
                            pgNode.SOURCE_ID = pgroup.PGROUP_ID;
                            pgNode.SOURCE = pgroup;
                            pgNode.SOURCE_PATH = OfficeHelper.CombinePath(sNode.SOURCE_PATH, slab.PGROUP_SID);

                            sNode.CHILDREN.Add(pgNode);

                            foreach (var u in users.Where(a => a.DEPT_CODE == pgroup.PGROUP_ID))
                            {
                                var uNode = new AreaGroupTreeNode();
                                uNode.NAME = u.USERNAME;
                                uNode.NODE_NO = index++;
                                uNode.NODE_TYPE = GroupTreeNodeTypeEnum.USER.ToIntStr();
                                uNode.NODE_TYPE_NAME = GroupTreeNodeTypeEnum.USER.ToDesc();
                                uNode.SOURCE_ID = u.USER_NO;
                                uNode.SOURCE = u;
                                uNode.SOURCE_PATH = OfficeHelper.CombinePath(pgNode.SOURCE_PATH, u.USER_NO);

                                pgNode.CHILDREN.Add(uNode);
                            }
                        }
                    }
                }
            }
            return result;
        }
        /// <summary>
        /// 获取生安组织ID
        /// </summary>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        public SMBL_ORG GetSmblOrg(string hospitalId)
        {
            SMBL_ORG org = _soa.Db.Queryable<SMBL_ORG>().Where(a => a.HOSPITAL_ID == hospitalId && a.ORG_STATE == "2").First();
            return org;
        }

        public OrgTree GetLabMgroupPgroupTree(OrgUserParams orgParm, string permissMenuId, bool ifReturnSourceObject)
        {
            var result = _organizationTreeService2.GetOrgTreeType_Lab_A(_soa, orgParm, "H81", ifReturnSourceObject);
            result.RefreshTree(GroupTreeNodeTypeEnum.PGROUP.ToIntStr());//自动填充路径、专业组计数
            return result;
        }

        public List<UserComDto> GetUserComInfo(string comName, string userName)
        {
            List<UserComDto> listCom = new List<UserComDto>();
            string hospitalId = _httpContext.GetHospitalId();
            //获取考生组合信息
            var userCom = _soa.Db.Queryable<SYS6_USER_COM>().Where(p => p.HOSPITAL_ID == hospitalId && p.USER_COM_STATE == "1")
                .WhereIF(comName.IsNotNullOrEmpty(), p => p.USER_COM_NAME.Contains(comName)).ToList();
            userCom = userCom.Where(p => p.USER_SNO.IsNotNullOrEmpty()).ToList();
            foreach (var item in userCom)
            {
                UserComDto userComDto = new UserComDto();
                userComDto.Child = new List<UserInfo>();
                userComDto.Id = item.USER_COM_ID;
                userComDto.Name = item.USER_COM_NAME;
                List<string> listUserNo = item.USER_SNO.Split('+').ToList();
                List<SYS6_USER> listUser = _soa.Db.Queryable<SYS6_USER>().Where(w => listUserNo.Contains(w.USER_NO)).WhereIF(userName.IsNotNullOrEmpty(), w => w.USERNAME.Contains(userName)).ToList();
                foreach (var user in listUser)
                {
                    UserInfo userInfo = new UserInfo();
                    userInfo.Id = user.USER_NO;
                    userInfo.Path = $"{userComDto.Id}/{userInfo.Id}";
                    userInfo.Name = user.USERNAME;
                    userInfo.HisId = user.HIS_ID;
                    userComDto.Child.Add(userInfo);
                }
                listCom.Add(userComDto);
            }
            return listCom;
        }

    }

}
