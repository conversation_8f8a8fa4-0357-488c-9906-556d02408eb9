﻿//using iTextSharp.text.pdf;
//using Serilog;
//using Spire.Doc;
//using Spire.Doc.Documents;
//using Spire.Pdf.Bookmarks;
//using Spire.Xls;
//using System.Collections;
//using System.Diagnostics;
//using System.Drawing;
//using System.Net;
//using System.Runtime.CompilerServices;
//using System.Text;
//using System.Text.RegularExpressions;
//using MSExcel = Microsoft.Office.Interop.Excel;

//namespace XH.H81.Base.Helper
//{
//    public class OfficeHelper
//    {
//        public OfficeHelper()
//        {
//            Spire.Pdf.License.LicenseProvider.SetLicenseKey("Exgti80+VQEAWvzoJ2DBP9zbFeaWRiqJ/S7/AglNpZZMbHab9dehnYHo44VeuHrtD6stIccrs26WJqPdD782lV4VYGOWZaF1H+i1WGT1X9BdQ9h5p7QNXQeCHbCLH5oBErmfA7Y4eCsh5M3J1lmDVDFfNKQ9VS6jJjKFA7EgQOa2P4G26eoExTZewyaF3AIgftu4BuX/S3aXH5rVG6RtmwG0pLuTDVyn2mcVw1nYlzc1jPIvyzpAZNSehpG33QXRXMRo7Wp9POYi/egG8NhPpl2f85235tt0d3LXjnIDNZblQcXGzTdIIjX3QR+dsirkqVg7A4mGX3JA9W+M1iRdAfRz3P42Nh/AOFQ5sz1ZUAhfHpWwebFZ/u9AtqJe1CPTwgW1pyKZVyKBXTNITYLkK7priwixTOxnL2s5i6uT/gUFfYrTPJfP/Ynyqlb6DMxxiMGAjB0GzLe51Q57vGaCCGtivTacHWKML65k90FW3AAOhXdhwKqLyWy+amkcJg7lpW82ZsxHYERTZGkDwRf4EODIWbqhVc3x+rZUKabBZULB3LJK8TjRfp5A1LDLnqqom5L9ZFtR+cdG78wFuVCV9dXFY66AZhspaJp8ALj/jQyoRNfih2zBqkbGzvsVS1XkYVFG0AM4CAKUuY2sM0cHoPdqyI/N1s4Y16Ec66hJzYzIEJUmkQ25WGMQWYoZUQzjFyWIO6/tr6uiwu+q3dx/eXFOouQ5pqDDjqxEa4S7cL+FgWZmPIh5lu12lYGyk9ahgOi/1SBrRun3U9LJxPx2lg92XXHG+/yOoRBa2twX6Tx6fRMflERyRi+8SECJu8IoMjcTwW/zqEclKhe3Au9PpDuRZ9PBQt1LBCBbSaIuE2poeoQ3DWHUlHar9wjFX7RQVWrVEi5roaIrZMr1f63acNFil2U9f0VJSeYgIM1kM9gM5f4fxISh6A0pkdSO1SykBHqIQFkpxzbzPIU8KGEuTE/MnYOBkxXIdQZ1aMepYX+p5sv7Brf2vkU+y2JajxXusOdoVaGeiMIWrbMOF6D0CGpuFvKRiktIuxLX1FaKi4SvpNZAcvZgUReZO+HHEDS45jIPkCXiVjcA7R5OiwfAzvMFxI2IZW9oNY+se9OTf/7/UZ1rSETlCY0NbYmSVX9X4yeHSGL/IpqsdJN+JvbXjBb88UZgZN/M1Yu1f+dsMqf8n0Iz3IH9PFEw6KK1NpNj5tRq1FDV+Av8NJy07ovpYBuGitvZZCSixgcUfo92v1IFJ6sI4FYaodPo8OC58NGuB8jR4FZH3CDbW0CbAwRf0gmMdUw0UMJV1fKetp48b/K1/UtK4KyutIlYqMQQBZHlMe1iu0InccHEMOo/XMulrpM/phc8nvdKYWSnJcYQRtdyNIvmLWKnTUGmWDEUG/PEzJaDl3d6v4Q3dwlUb/aFqDPsrOsofNwG9CTs0B0z7Y71bW/nyeCE4Lk/iHUZmgK9Le1t1+vclc4yrSo2oOziyJ/fyqg00FSr13qazezxyVs=");
//            Spire.Doc.License.LicenseProvider.SetLicenseKey("Exgti80+VQEAWvzoJ2DBP9zbFeaWRiqJ/S7/AglNpZZMbHab9dehnYHo44VeuHrtD6stIccrs26WJqPdD782lV4VYGOWZaF1H+i1WGT1X9BdQ9h5p7QNXQeCHbCLH5oBErmfA7Y4eCsh5M3J1lmDVDFfNKQ9VS6jJjKFA7EgQOa2P4G26eoExTZewyaF3AIgftu4BuX/S3aXH5rVG6RtmwG0pLuTDVyn2mcVw1nYlzc1jPIvyzpAZNSehpG33QXRXMRo7Wp9POYi/egG8NhPpl2f85235tt0d3LXjnIDNZblQcXGzTdIIjX3QR+dsirkqVg7A4mGX3JA9W+M1iRdAfRz3P42Nh/AOFQ5sz1ZUAhfHpWwebFZ/u9AtqJe1CPTwgW1pyKZVyKBXTNITYLkK7priwixTOxnL2s5i6uT/gUFfYrTPJfP/Ynyqlb6DMxxiMGAjB0GzLe51Q57vGaCCGtivTacHWKML65k90FW3AAOhXdhwKqLyWy+amkcJg7lpW82ZsxHYERTZGkDwRf4EODIWbqhVc3x+rZUKabBZULB3LJK8TjRfp5A1LDLnqqom5L9ZFtR+cdG78wFuVCV9dXFY66AZhspaJp8ALj/jQyoRNfih2zBqkbGzvsVS1XkYVFG0AM4CAKUuY2sM0cHoPdqyI/N1s4Y16Ec66hJzYzIEJUmkQ25WGMQWYoZUQzjFyWIO6/tr6uiwu+q3dx/eXFOouQ5pqDDjqxEa4S7cL+FgWZmPIh5lu12lYGyk9ahgOi/1SBrRun3U9LJxPx2lg92XXHG+/yOoRBa2twX6Tx6fRMflERyRi+8SECJu8IoMjcTwW/zqEclKhe3Au9PpDuRZ9PBQt1LBCBbSaIuE2poeoQ3DWHUlHar9wjFX7RQVWrVEi5roaIrZMr1f63acNFil2U9f0VJSeYgIM1kM9gM5f4fxISh6A0pkdSO1SykBHqIQFkpxzbzPIU8KGEuTE/MnYOBkxXIdQZ1aMepYX+p5sv7Brf2vkU+y2JajxXusOdoVaGeiMIWrbMOF6D0CGpuFvKRiktIuxLX1FaKi4SvpNZAcvZgUReZO+HHEDS45jIPkCXiVjcA7R5OiwfAzvMFxI2IZW9oNY+se9OTf/7/UZ1rSETlCY0NbYmSVX9X4yeHSGL/IpqsdJN+JvbXjBb88UZgZN/M1Yu1f+dsMqf8n0Iz3IH9PFEw6KK1NpNj5tRq1FDV+Av8NJy07ovpYBuGitvZZCSixgcUfo92v1IFJ6sI4FYaodPo8OC58NGuB8jR4FZH3CDbW0CbAwRf0gmMdUw0UMJV1fKetp48b/K1/UtK4KyutIlYqMQQBZHlMe1iu0InccHEMOo/XMulrpM/phc8nvdKYWSnJcYQRtdyNIvmLWKnTUGmWDEUG/PEzJaDl3d6v4Q3dwlUb/aFqDPsrOsofNwG9CTs0B0z7Y71bW/nyeCE4Lk/iHUZmgK9Le1t1+vclc4yrSo2oOziyJ/fyqg00FSr13qazezxyVs=");
//            Spire.Xls.License.LicenseProvider.SetLicenseKey("Exgti80+VQEAWvzoJ2DBP9zbFeaWRiqJ/S7/AglNpZZMbHab9dehnYHo44VeuHrtD6stIccrs26WJqPdD782lV4VYGOWZaF1H+i1WGT1X9BdQ9h5p7QNXQeCHbCLH5oBErmfA7Y4eCsh5M3J1lmDVDFfNKQ9VS6jJjKFA7EgQOa2P4G26eoExTZewyaF3AIgftu4BuX/S3aXH5rVG6RtmwG0pLuTDVyn2mcVw1nYlzc1jPIvyzpAZNSehpG33QXRXMRo7Wp9POYi/egG8NhPpl2f85235tt0d3LXjnIDNZblQcXGzTdIIjX3QR+dsirkqVg7A4mGX3JA9W+M1iRdAfRz3P42Nh/AOFQ5sz1ZUAhfHpWwebFZ/u9AtqJe1CPTwgW1pyKZVyKBXTNITYLkK7priwixTOxnL2s5i6uT/gUFfYrTPJfP/Ynyqlb6DMxxiMGAjB0GzLe51Q57vGaCCGtivTacHWKML65k90FW3AAOhXdhwKqLyWy+amkcJg7lpW82ZsxHYERTZGkDwRf4EODIWbqhVc3x+rZUKabBZULB3LJK8TjRfp5A1LDLnqqom5L9ZFtR+cdG78wFuVCV9dXFY66AZhspaJp8ALj/jQyoRNfih2zBqkbGzvsVS1XkYVFG0AM4CAKUuY2sM0cHoPdqyI/N1s4Y16Ec66hJzYzIEJUmkQ25WGMQWYoZUQzjFyWIO6/tr6uiwu+q3dx/eXFOouQ5pqDDjqxEa4S7cL+FgWZmPIh5lu12lYGyk9ahgOi/1SBrRun3U9LJxPx2lg92XXHG+/yOoRBa2twX6Tx6fRMflERyRi+8SECJu8IoMjcTwW/zqEclKhe3Au9PpDuRZ9PBQt1LBCBbSaIuE2poeoQ3DWHUlHar9wjFX7RQVWrVEi5roaIrZMr1f63acNFil2U9f0VJSeYgIM1kM9gM5f4fxISh6A0pkdSO1SykBHqIQFkpxzbzPIU8KGEuTE/MnYOBkxXIdQZ1aMepYX+p5sv7Brf2vkU+y2JajxXusOdoVaGeiMIWrbMOF6D0CGpuFvKRiktIuxLX1FaKi4SvpNZAcvZgUReZO+HHEDS45jIPkCXiVjcA7R5OiwfAzvMFxI2IZW9oNY+se9OTf/7/UZ1rSETlCY0NbYmSVX9X4yeHSGL/IpqsdJN+JvbXjBb88UZgZN/M1Yu1f+dsMqf8n0Iz3IH9PFEw6KK1NpNj5tRq1FDV+Av8NJy07ovpYBuGitvZZCSixgcUfo92v1IFJ6sI4FYaodPo8OC58NGuB8jR4FZH3CDbW0CbAwRf0gmMdUw0UMJV1fKetp48b/K1/UtK4KyutIlYqMQQBZHlMe1iu0InccHEMOo/XMulrpM/phc8nvdKYWSnJcYQRtdyNIvmLWKnTUGmWDEUG/PEzJaDl3d6v4Q3dwlUb/aFqDPsrOsofNwG9CTs0B0z7Y71bW/nyeCE4Lk/iHUZmgK9Le1t1+vclc4yrSo2oOziyJ/fyqg00FSr13qazezxyVs=");
//        }

//        public static string PathCombine(string pathA, string pathB )
//        {
//            return Path.Combine(pathA, pathB).Replace("\\","/");  
//        }

//        /// <summary>
//        /// 上传时会先转成PDF的文件格式
//        /// </summary>
//        public static List<string> CastPDFToPreviewSuffixList = new List<string>
//        {
//            ".docx",
//            ".doc",
//            ".xls",
//            ".xlsx"
//        };

//        /// <summary>
//        /// 将使用PDF格式预览的文件后缀修改成后缀.pdf(带点小写)
//        /// </summary>
//        /// <param name="fileSuffix"></param>
//        /// <returns></returns>
//        public static string CastToPreviewSuffix(string fileSuffix)
//        {
//            fileSuffix = fileSuffix.ToLower().Trim();
//            string previewSuffix = fileSuffix;
//            //如果转PDF预览格式，后缀改为.pdf
//            if (CastPDFToPreviewSuffixList.Contains(fileSuffix))
//                previewSuffix = ".pdf";
//            return previewSuffix;
//        }

//        /// <summary>
//        /// word生成pdf
//        /// </summary>
//        /// <param name="sourcePath"></param>
//        /// <param name="targetPath"></param>
//        /// <returns></returns>
//        public string WordToPdfBySpire(string Strbase64, string sourcePath, string targetPath)
//        {
//            bool result = false;
//            string strBase = string.Empty;
//            string base64img = Regex.Replace(Strbase64, "data:application/.*;base64,", "");
//            byte[] photoBytes = Convert.FromBase64String(base64img);
//            Document doc = new Document();
//            try
//            {
//                File.WriteAllBytes(sourcePath, photoBytes);
//                //  doc.LoadFromFile(sourcePath);
//                doc.LoadFromFile(sourcePath);
//                ToPdfParameterList topdf = new ToPdfParameterList();
//                topdf.CreateWordBookmarks = true;
//                topdf.CreateWordBookmarksUsingHeadings = true;
//                doc.SaveToFile(targetPath, topdf);
//                result = true;
//                if (File.Exists(targetPath))
//                {
//                    strBase = FileToBase64String(targetPath);
//                    File.Delete(targetPath);
//                    File.Delete(sourcePath);
//                }
//            }
//            catch (Exception ex)
//            {
//                result = false;
//            }
//            finally
//            {
//                doc.Close();
//            }
//            return strBase;
//        }


//        /// <summary>
//        /// word生成pdf
//        /// </summary>
//        /// <param name="sourcePath"></param>
//        /// <param name="targetPath"></param>
//        /// <returns></returns>
//        public byte[] WordToPdfBySpire(byte[] photoBytes, string sourcePath, string targetPath)
//        {
//            bool result = false;
//            byte[] byteBase = null;
//            Document doc = new Document();
//            try
//            {
//                File.WriteAllBytes(sourcePath, photoBytes);
//                //  doc.LoadFromFile(sourcePath);
//                doc.LoadFromFile(sourcePath);
//                ToPdfParameterList topdf = new ToPdfParameterList();
//                topdf.CreateWordBookmarks = true;
//                topdf.CreateWordBookmarksUsingHeadings = true;
//                doc.SaveToFile(targetPath, topdf);
//                result = true;
//                if (File.Exists(targetPath))
//                {
//                    byteBase = FileToByte(targetPath);
//                    File.Delete(targetPath);
//                    File.Delete(sourcePath);
//                }
//            }
//            catch (Exception ex)
//            {
//                result = false;
//            }
//            finally
//            {
//                doc.Close();
//            }
//            return byteBase;
//        }

//        //public string GetDirectory1(string contentRootPath, string folderPath)
//        //{

//        //    //加载含有多级书签的PDF文件
//        //    PdfDocument doc = new PdfDocument();
//        //    string savePath = contentRootPath + folderPath;
//        //    //  doc.LoadFromFile("示例.pdf");
//        //    doc.LoadFromFile(savePath + "质量手册.pdf");
//        //    //获取文档的书签
//        //    PdfBookmarkCollection bookmarks = doc.Bookmarks;
//        //    //声明一个可变字符串
//        //    StringBuilder stringbuilder = new StringBuilder();
//        //    //获取父书签和子书签           
//        //    GetBookmarkTitle(bookmarks, stringbuilder);
//        //    //声明txt文件，并将获得的多级书签写入到文件.txt
//        //    String fileName = "D://文件.txt";
//        //    File.WriteAllText(fileName, stringbuilder.ToString());
//        //    Console.ReadLine();
//        //    return "";
//        //}
//        public static void GetBookmarkTitle(PdfBookmarkCollection bookmarks, StringBuilder stringbuilder)
//        {
//            if (bookmarks.Count > 0)
//            {
//                foreach (PdfBookmark parentBookmark in bookmarks)
//                {
//                    stringbuilder.AppendLine(parentBookmark.Title);
//                    //递归文档多级书签
//                    GetBookmarkTitle(parentBookmark, stringbuilder);
//                }
//            }
//        }


//        public string GetDirectory(string contentRootPath, string folderPath)
//        {

//            //创建Document对象，并加载测试文档

//            using (Document doc = new Document())
//            {
//                string savePath = contentRootPath + folderPath;
//                doc.LoadFromFile(savePath + "质量手册.docx");

//                //实例化Stringbuilder对象
//                StringBuilder sb = new StringBuilder();
//                //遍历文档中的section
//                foreach (Section section in doc.Sections)
//                {

//                    foreach (Paragraph paragraph in section.Paragraphs)
//                    {
//                        //判断段落标题名称
//                        if (paragraph.StyleName == "Heading1" || paragraph.StyleName == "一级样式" || paragraph.StyleName == "标题1")
//                        {
//                            sb.AppendLine("一级：" + paragraph.Text);
//                        }
//                        if (paragraph.StyleName == "Heading2" || paragraph.StyleName == "二级样式" || paragraph.StyleName == "标题2")
//                        {
//                            sb.AppendLine("二级：" + paragraph.Text);
//                        }
//                        if (paragraph.StyleName == "Heading3" || paragraph.StyleName == "三级样式" || paragraph.StyleName == "标题3")
//                        {
//                            sb.AppendLine("三级：" + paragraph.Text);
//                        }

//                    }
//                }

//                //将符合标题名称的段落写入.txt文档
//                File.WriteAllText("Extract.txt", sb.ToString());
//                // System.Diagnostics.Process.Start(contentRootPath + "Extract.txt");
//                return sb.ToString();
//            }
//        }



//        /// <summary>
//        /// Excel生成pdf
//        /// </summary>
//        /// <param name="sourcePath"></param>
//        /// <param name="targetPath"></param>
//        /// <returns></returns>
//        public string ExcelToPdfBySpire(string Strbase64, string sourcePath, string targetPath)
//        {
//            string strBase = string.Empty;
//            string base64img = Regex.Replace(Strbase64, "data:application/.*;base64,", "");
//            byte[] photoBytes = Convert.FromBase64String(base64img);
//            try
//            {
//                File.WriteAllBytes(sourcePath, photoBytes);
//                //// 加载Excel文档
//                //using (Workbook workbook = new Workbook())
//                //{
//                //    if (sourcePath.ToLower().EndsWith(".xlsx"))
//                //        workbook.LoadFromFile(sourcePath, ExcelVersion.Version2007);
//                //    else
//                //        workbook.LoadFromFile(sourcePath);
//                //    // 创建PDF文档
//                //    workbook.SaveToFile(targetPath, Spire.Xls.FileFormat.PDF);

//                //    //循环不同版本创建pdf的思路不通，会出现不报错，但pdf无法查看的问题
//                //    //foreach (ExcelVersion ver in Enum.GetValues(typeof(ExcelVersion)))
//                //    //{
//                //    //    try
//                //    //    {
//                //    //        workbook.LoadFromFile(sourcePath);
//                //    //        //// 创建PDF文档
//                //    //        workbook.SaveToFile(targetPath, ver);
//                //    //        break;
//                //    //    }
//                //    //    catch { }
//                //    //}
//                //}

//                //实现Excel转Pdf
//                ConvertExeclToPdf(sourcePath, targetPath);
//                if (File.Exists(targetPath))
//                {
//                    strBase = FileToBase64String(targetPath);
//                    File.Delete(targetPath);
//                    File.Delete(sourcePath);
//                }
//            }
//            catch (Exception ex)
//            {
//                Log.Error($"ExcelToPdfBySpire执行时发生异常：{ex.Message}");
//            }
//            return strBase;
//        }


//        /// <summary>
//        /// Excel生成pdf
//        /// </summary>
//        /// <param name="sourcePath"></param>
//        /// <param name="targetPath"></param>
//        /// <returns></returns>
//        public byte[] ExcelToPdfBySpire(byte[] photoBytes, string sourcePath, string targetPath)
//        {
//            byte[] byteBase = null;
//            try
//            {
//                File.WriteAllBytes(sourcePath, photoBytes);
//                //实现Excel转Pdf
//                ConvertExeclToPdf(sourcePath, targetPath);
//                if (File.Exists(targetPath))
//                {
//                    byteBase = FileToByte(targetPath);
//                    File.Delete(targetPath);
//                    File.Delete(sourcePath);
//                }
//            }
//            catch (Exception ex)
//            {
//                Log.Error($"ExcelToPdfBySpire执行时发生异常：{ex.Message}");
//            }
//            return byteBase;
//        }


//        /// <summary>
//        /// excel转成pdf
//        /// </summary>
//        /// <param name="excelFilePath">excel路径（含文件名后缀）</param>
//        /// <param name="pdfFilePath">生成的pdf路径（含文件名后缀）</param>
//        /// <returns>生成的pdf路径（含文件名后缀）</returns>
//        public string ConvertExeclToPdf(string excelFilePath, string pdfFilePath)
//        {
//            //下面这个方法可用
//            string pdfPath = "";
//            try
//            {
//                //创建一个Workbook实例并加载Excel文件
//                Workbook workbook = new Workbook();
//                workbook.LoadFromFile(excelFilePath);
//                workbook.ConverterSetting.SheetFitToPage = true;
//                //设置转换后的PDF页面高宽固定
//                //workbook.GetConverterSetting().SetSheetFitToPage(false);

//                Worksheet sheet = workbook.Worksheets[0];

//                //设置为A4纸张
//                sheet.PageSetup.PaperSize = PaperSizeType.PaperA4;
//                //设置纸张打印方向
//                sheet.PageSetup.Orientation = PageOrientationType.Landscape;

//                // 设置打印优先级模式
//                //        sheet.getPageSetup().setOrder(OrderType.OverThenDown);

//                //        // 设置页面自适应，纵向多少页打印完
//                //        sheet.getPageSetup().setFitToPagesTall(2);
//                //
//                //        // 设置页面自适应，横向多少页打印完
//                //        sheet.getPageSetup().setFitToPagesWide(2);

//                sheet.FreezePanes(1, 2);

//                // 打印时，冻结行
//                // sheet.PageSetup.PrintTitleColumns = "$2:$3";
//                // 打印时，冻结列
//                //  sheet.PageSetup.PrintTitleColumns = "$B:$B";

//                //        // 冻结第一行第一列
//                //        sheet.freezePanes(1,1);

//                //设置文本和字体大小
//                //System.Drawing.Font font = new System.Drawing.Font("仿宋", 40);
//                // string watermark = "内部专用";


//                //将图片设置为页眉
//                // sheet.PageSetup.LeftHeaderImage = imgWtrmrk;
//                //sheet.PageSetup.LeftHeader = "&G";

//                //将显示模式设置为Layout
//                sheet.ViewMode = ViewMode.Layout;

//                ////为第一页设置页眉页脚
//                //sheet.PageSetup.FirstHeaderString = "&\"Arial\"&B&14&KFF0000第一页页眉";
//                //sheet.PageSetup.FirstFooterString = "&\"Arial\"&B&14&KFF0000第一页页脚";

//                ////为其它页设置页眉页脚
//                //sheet.PageSetup.CenterHeader = "&\"Arial\"&B&14&K191970其它页页眉";
//                //sheet.PageSetup.CenterFooter = "&\"Arial\"&B&14&K191970其它页页脚";

//                //设置页边距
//                // sheet.PageSetup.BottomMargin = 5;
//                // sheet.PageSetup.LeftMargin = 5;

//                //将生成的文档保存到指定路径
//                pdfPath = pdfFilePath;
//                workbook.SaveToFile(pdfPath, Spire.Xls.FileFormat.PDF);
//                Log.Information("PDF转换完成.." + pdfPath);
//            }
//            catch (Exception ex)
//            {

//                Log.Information("PDF转换失败:" + ex.ToString());
//            }

//            return pdfPath;
//        }


//        /// <summary>
//        /// Word文件添加水印
//        /// </summary>
//        /// <param name="Strbase64"></param>
//        /// <param name="sourcePath"></param>
//        /// <param name="sourceName"></param>
//        /// <returns></returns>
//        public static string WordAddWatermark(string Strbase64, string sourcePath, string sourceName)
//        {
//            string strBase = string.Empty;
//            byte[] photoBytes = Convert.FromBase64String(Strbase64);
//            string filepath = Path.GetDirectoryName(sourcePath + sourceName);
//            File.WriteAllBytes(sourcePath + sourceName, photoBytes);
//            //创建 Document 类的对象
//            using (Document document = new Document())
//            {
//                //从磁盘加载 Word 文档
//                document.LoadFromFile(sourcePath + sourceName);
//                //添加图片水印
//                InsertImageWatermark(document, sourcePath);
//                //保存文档
//                document.SaveToFile(sourcePath + sourceName, Spire.Doc.FileFormat.Docx);
//                if (File.Exists(sourcePath + sourceName))
//                {
//                    strBase = FileToBase64String(sourcePath + sourceName);
//                    File.Delete(sourcePath + sourceName);
//                }
//                return strBase;
//            }
//        }


//        /// <summary>
//        /// PDF文件添加水印
//        /// </summary>
//        /// <param name="Strbase64"></param>
//        /// <param name="sourcePath"></param>
//        /// <param name="sourceName"></param>
//        /// <returns></returns>
//        public static string PdfAddWatermark(string Strbase64, string sourcePath, string sourceName)
//        {
//            string strBase = string.Empty;
//            byte[] photoBytes = Convert.FromBase64String(Strbase64);
//            string filePath = OfficeHelper.PathCombine(sourcePath, sourceName);
//            string tempPath = OfficeHelper.PathCombine(sourcePath, "temp_"+ sourceName);
//            File.WriteAllBytes(sourcePath + sourceName, photoBytes);

//            PdfReader pdfReader = null;
//            PdfStamper pdfStamper = null;
//            FileStream fStream = null;

//            try
//            {
//                pdfReader = new PdfReader(filePath);

//                int numberOfPages = pdfReader.NumberOfPages;

//                iTextSharp.text.Rectangle psize = pdfReader.GetPageSize(1);

//                float width = psize.Width;

//                float height = psize.Height;

//                fStream =  new FileStream(tempPath, FileMode.Create);
//                pdfStamper = new PdfStamper(pdfReader, fStream);

//                PdfContentByte waterMarkContent;

//                iTextSharp.text.Image image = iTextSharp.text.Image.GetInstance(OfficeHelper.PathCombine(sourcePath, "watermark.png"));

//                float w = image.Width;
//                float h = image.Height;
//                image.GrayFill = 20;//透明度，灰色填充
//                                    //image.Rotation//旋转
//                                    //image.RotationDegrees//旋转角度
//                                    //水印的位置
//                image.SetAbsolutePosition((width - w) / 2, (height - h) / 2);
//                //每一页加水印,也可以设置某一页加水印
//                for (int i = 1; i <= numberOfPages; i++)
//                {
//                    //waterMarkContent = pdfStamper.GetUnderContent(i);//内容下层加水印
//                    waterMarkContent = pdfStamper.GetOverContent(i);//内容上层加水印
//                    waterMarkContent.AddImage(image);
//                }
//            }
//            catch (Exception ex)
//            {
//                ex.ToString();

//            }
//            finally
//            {
//                if (pdfStamper != null)
//                    pdfStamper.Close();

//                if (fStream != null)
//                    fStream.Close();

//                if (pdfReader != null)
//                    pdfReader.Close();
//                if (File.Exists(tempPath))
//                {
//                    File.Delete(filePath);

//                    try
//                    {
//                        strBase = FileToBase64String(tempPath);
//                    }
//                    catch { }
//                    File.Delete(tempPath);
//                    //上述文件读写可能失败
//                    if (strBase == null || !strBase.Any())
//                        strBase = Strbase64;
//                }
//            }
//            return strBase;
//        }



//        /// <summary>
//        /// PDF文件添加水印
//        /// </summary>
//        /// <param name="Strbase64"></param>
//        /// <param name="sourcePath"></param>
//        /// <param name="sourceName"></param>
//        /// <returns></returns>
//        public static byte[] PdfAddWatermark(byte[] photoBytes, string sourcePath, string sourceName)
//        {
//            byte[] strByte = null;
//            string filePath = OfficeHelper.PathCombine(sourcePath, sourceName);
//            string tempPath = OfficeHelper.PathCombine(sourcePath, "temp_"+ sourceName);

//            //   byte[] photoBytes = Convert.FromBase64String(Strbase64);
//            if (!Directory.Exists(sourcePath))
//                Directory.CreateDirectory(sourcePath);
//            //string filepath = Path.GetDirectoryName(OfficeHelper.PathCombine(sourcePath, sourceName));
//            File.WriteAllBytes(filePath, photoBytes);

//            PdfReader pdfReader = null;
//            PdfStamper pdfStamper = null;
//            FileStream fStream = null;

//            try
//            {
//                pdfReader = new PdfReader(filePath);

//                int numberOfPages = pdfReader.NumberOfPages;

//                iTextSharp.text.Rectangle psize = pdfReader.GetPageSize(1);

//                float width = psize.Width;

//                float height = psize.Height;

//                fStream = new FileStream(tempPath, FileMode.Create);
//                pdfStamper = new PdfStamper(pdfReader, fStream);

//                PdfContentByte waterMarkContent;

//                iTextSharp.text.Image image = iTextSharp.text.Image.GetInstance(OfficeHelper.PathCombine(sourcePath, "watermark.png"));

//                float w = image.Width;
//                float h = image.Height;
//                image.GrayFill = 20;//透明度，灰色填充
//                                    //image.Rotation//旋转
//                                    //image.RotationDegrees//旋转角度
//                                    //水印的位置
//                image.SetAbsolutePosition((width - w) / 2, (height - h) / 2);
//                //每一页加水印,也可以设置某一页加水印
//                for (int i = 1; i <= numberOfPages; i++)
//                {
//                    //waterMarkContent = pdfStamper.GetUnderContent(i);//内容下层加水印
//                    waterMarkContent = pdfStamper.GetOverContent(i);//内容上层加水印
//                    waterMarkContent.AddImage(image);
//                }
//            }
//            catch (Exception ex)
//            {
//                ex.ToString();

//            }
//            finally
//            {
//                if (pdfStamper != null)
//                    pdfStamper.Close();

//                if (fStream!=null)
//                    fStream.Close();

//                if (pdfReader != null)
//                    pdfReader.Close();
//                if (File.Exists(tempPath))
//                {
//                    File.Delete(filePath);
//                    try
//                    {
//                        strByte = FileToByte(tempPath);
//                    }
//                    catch { }
//                    File.Delete(tempPath);
//                    //上述文件读写可能失败
//                    if (strByte == null || !strByte.Any())
//                        strByte = photoBytes;
//                }
//            }
//            return strByte;
//        }


//        /// <summary>
//        /// 文件转byte[]
//        /// </summary>
//        /// <returns>byte[]</returns>
//        public static byte[] FileToByte(string FilePath)
//        {
//            FileStream fsForRead = new FileStream(FilePath, FileMode.Open);
//            byte[] fileByte = null;
//            try
//            {
//                //读写指针移到距开头10个字节处
//                fsForRead.Seek(0, SeekOrigin.Begin);
//                fileByte = new byte[fsForRead.Length];
//                int log = Convert.ToInt32(fsForRead.Length);
//                //从文件中读取10个字节放到数组bs中
//                fsForRead.Read(fileByte, 0, log);
//                return fileByte;
//            }
//            catch (Exception ex)
//            {
//                Console.Write(ex.Message);
//                Console.ReadLine();
//                return fileByte;
//            }
//            finally
//            {
//                fsForRead.Close();
//            }
//        }


//        /// <summary>
//        /// 添加图片水印
//        /// </summary>
//        /// <param name="document"></param>
//        /// <param name="sourcePath"></param>
//        private static void InsertImageWatermark(Document document, string sourcePath)
//        {
//            PictureWatermark picture = new PictureWatermark();
//            picture.SetPicture(sourcePath + "watermark.png");
//            picture.Scaling = 100;
//            picture.IsWashout = false;
//            document.Watermark = picture;
//        }
//        /// <summary>
//        /// 文件转base64
//        /// </summary>
//        /// <returns>base64字符串</returns>
//        public static string FileToBase64String(string FilePath)
//        {
//            FileStream fsForRead = new FileStream(FilePath, FileMode.Open);
//            string base64Str = "";
//            try
//            {
//                //读写指针移到距开头10个字节处
//                fsForRead.Seek(0, SeekOrigin.Begin);
//                byte[] bs = new byte[fsForRead.Length];
//                int log = Convert.ToInt32(fsForRead.Length);
//                //从文件中读取10个字节放到数组bs中
//                fsForRead.Read(bs, 0, log);
//                base64Str = Convert.ToBase64String(bs);
//                return base64Str;
//            }
//            catch (Exception ex)
//            {
//                Console.Write(ex.Message);
//                Console.ReadLine();
//                return base64Str;
//            }
//            finally
//            {
//                fsForRead.Close();
//                File.Delete(FilePath);
//            }
//        }

//        /// <summary>
//        /// 数组去重
//        /// </summary>
//        /// <param name="TempArray"></param>
//        /// <returns></returns>
//        public static string[] DelArraySame(string[] TempArray)
//        {
//            ArrayList nStr = new ArrayList();
//            for (int i = 0; i < TempArray.Length; i++)
//            {
//                if (!nStr.Contains(TempArray[i]))
//                {
//                    nStr.Add(TempArray[i]);
//                }
//            }
//            string[] newStr = (string[])nStr.ToArray(typeof(string));
//            return newStr;
//        }


//        /************************
//  *用第二个方法，获取远程文件的大小
//  *************************/
//        //1.判断远程文件是否存在 

//        ///fileUrl:远程文件路径，包括IP地址以及详细的路径

//        public static bool RemoteFileExists(string fileUrl)
//        {
//            Stopwatch stopwatch = new Stopwatch();
//            ServicePointManager.ServerCertificateValidationCallback = delegate { return true; };
//            ServicePointManager.SecurityProtocol = (SecurityProtocolType)192 | (SecurityProtocolType)768 | (SecurityProtocolType)3072;
//            bool result = false;//下载结果
//            stopwatch.Start();
//            WebResponse? response = null;
//            try
//            {
//                WebRequest req = WebRequest.Create(fileUrl);
//                response = req.GetResponse();
//                result = response != null;
//                stopwatch.Stop();
//                Log.Information($"HttpFile操作-[RemoteFileExists完成]-[{stopwatch.ElapsedMilliseconds}]ms,[{fileUrl}]");
//            }
//            catch (Exception ex)
//            {
//                stopwatch.Stop();
//                Log.Error($"HttpFile操作-[RemoteFileExists出错]-[{stopwatch.ElapsedMilliseconds}]ms,[{fileUrl}] \n {ex}");
//                result = false;
//            }
//            finally
//            {
//                response?.Close();
//            }

//            return result;
//        }



//        public string GetFileStream(string serviceUrl)
//        {
//            ServicePointManager.ServerCertificateValidationCallback = delegate { return true; };
//            WebClient client = new WebClient();
//            ServicePointManager.SecurityProtocol = (SecurityProtocolType)192 | (SecurityProtocolType)768 | (SecurityProtocolType)3072;
//            byte[] buffer = client.DownloadData(serviceUrl);
//            string pic = Convert.ToBase64String(buffer);
//            //using (FileStream fileStream = new FileStream(localUrl, FileMode.CreateNew))
//            //{
//            //    fileStream.Write(buffer, 0, buffer.Length);
//            //    fileStream.Close();
//            //}
//            return pic;
//        }

//    }
//}
