﻿using AutoMapper;
using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using XH.H81.IServices;
using XH.H81.IServices.Pms;
using XH.H81.Models.Dtos;
using XH.H81.Models.Dtos.Exam;
using XH.H81.Models.Entities.EvaluatePlan;
using XH.H81.Models.Entities.Exam;
using XH.H81.Models.Entities.others;
using XH.H81.Models.Entities.Pms;
using XH.H81.Models.Entities.Tag;
using XH.H81.Models.Exam;
using XH.H81.Models.SugarDbContext;
using XH.H98.Models.Entities.Pms;
using XH.LAB.UTILS.Helpers;
using XH.LAB.UTILS.Interface;
using XH.LAB.UTILS.Models;
using XH.LAB.UTILS.Models.Dto;
using XH.LAB.UTILS.Models.Entites;
using XH.LAB.UTILS.Models.Enums;

namespace XH.H81.Services
{
    public class PmsAssessService : IPmsAssessService
    {
        private readonly IHttpContextAccessor _httpContext;
        private readonly IConfiguration _configuration;
        private readonly ISqlSugarUow<SugarDbContext_Master> _soa;
        private readonly IMapper _mapper;
        private readonly ISystemService _systemService;
        private readonly ILogger<PmsAssessService> _logger;
        private readonly string FileHttpUrl = "/H81pdf/api";
        private readonly IBaseDataServices _IBaseDataServices;
        private readonly IEvaluatePlanService _IEvaluatePlanService;
        private readonly IPmsService _IPmsService;
        public PmsAssessService(IConfiguration configuration, IHttpContextAccessor httpContext, ISqlSugarUow<SugarDbContext_Master> dbContext, IMapper mapper, ISystemService systemService, ILogger<PmsAssessService> logger, IBaseDataServices baseDataServices, IEvaluatePlanService evaluatePlanService, IPmsService ipmsService)
        {
            _configuration = configuration;
            _httpContext = httpContext;
            _soa = dbContext;
            _mapper = mapper;
            _systemService = systemService;
            _logger = logger;
            _IBaseDataServices = baseDataServices;
            _IEvaluatePlanService = evaluatePlanService;
            _IPmsService= ipmsService;
        }



        /// <summary>
        /// 获取评估记录
        /// </summary>
        /// <param name="userNo">用户id</param>
        /// <param name="assessPlanState">评估状态</param>
        /// <param name="assessPlanType">评估类型</param>
        /// <param name="assessPlanName"></param>
        /// <param name="smblFlag"></param>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        public ResultDto GetUserAssessInfo(string? userNo, string? labId, string? mgroupId, string? pgroupId, string? assessPlanState, string? assessPlanType, string assessPlanName, string? smblFlag, string dateType, string startDate, string endDate,string cerId, string hospitalId)
        {
            ResultDto resultDto = new ResultDto();
            if (_httpContext.GetSmblFlag() == "1")
                smblFlag = "1";
            List<string> listPgroupId = new List<string>();
            if (mgroupId.IsNotNullOrEmpty())
            {
                List<SYS6_INSPECTION_PGROUP> listPgroup = _soa.Db.Queryable<SYS6_INSPECTION_PGROUP>()
                .Where(pg => pg.PGROUP_STATE == "1" && pg.PGROUP_CLASS != "3" && pg.MGROUP_ID == mgroupId)
                .ToList();
                listPgroupId = listPgroup.Select(w => w.PGROUP_ID).ToList();
            }
            else if (labId.IsNotNullOrEmpty())
            {
                List<SYS6_INSPECTION_PGROUP> listPgroup = _soa.Db.Queryable<SYS6_INSPECTION_PGROUP>()
              .Where(pg => pg.PGROUP_STATE == "1" && pg.PGROUP_CLASS != "3" && pg.LAB_ID == labId)
              .ToList();
                listPgroupId = listPgroup.Select(w => w.PGROUP_ID).ToList();
            }
            var varPlanAssess = _soa.Db.Queryable<PMS_ASSESS_PLAN_PERSON>()
                .LeftJoin<OA_EVALUATE_PLAN_SETUP>((a, b) => a.STD_SCHEME_ID == b.EPLAN_ID && b.EPLAN_SSTATE == "1")
                .LeftJoin<OA_EVALUATE_PLAN_DICT>((a, b, c) => a.STD_SCHEME_ID == c.EPLAN_ID)
                .LeftJoin<PMS_ASSESS_PLAN>((a, b, c, d) => a.PLAN_ID == d.PLAN_ID)
                .Where((a, b, c, d) => a.EVALUATE_STATE != "4" &&a.USER_ID!= "STD_PMSASSESS")
                .WhereIF(userNo.IsNotNullOrEmpty(), (a, b, c, d) => a.USER_ID == userNo)
                .WhereIF(pgroupId.IsNotNullOrEmpty(), (a, b, c, d) => a.PGROUP_ID == pgroupId)
                .WhereIF(listPgroupId.Count > 0, (a, b, c, d) => listPgroupId.Contains(a.PGROUP_ID))
                .WhereIF(smblFlag=="1", (a, b, c, d) => a.SMBL_FLAG == smblFlag)
                .WhereIF(assessPlanState.IsNotNullOrEmpty(), (a, b, c, d) => a.EVALUATE_STATE == assessPlanState)
                .WhereIF(assessPlanType.IsNotNullOrEmpty(), (a, b, c, d) => b.EPLAN_APPLY_TYPE == assessPlanType)
                .WhereIF(cerId.IsNotNullOrEmpty(), (a, b, c, d) => a.CER_ID == cerId)
                .WhereIF(assessPlanName.IsNotNullOrEmpty(), (a, b, c, d) => (a.EVALUATE_NAME != null && a.EVALUATE_NAME.Contains(assessPlanName)) || (d.PLAN_NAME != null && d.PLAN_NAME.Contains(assessPlanName)))
                .Select((a, b, c, d) => new PmsAssessPlanPersonDto()
                {
                    PLAN_PERSON_ID = a.PLAN_PERSON_ID,
                    USER_ID = a.USER_ID,
                    EVALUATE_STATE = a.EVALUATE_STATE,
                    TOTAL_EVALUATE = a.TOTAL_EVALUATE,
                    EPLAN_APPLY_TYPE = b.EPLAN_APPLY_TYPE,
                    SEVALUATION_SCORE = a.SEVALUATION_SCORE,
                    EVALUATE_SCORE = a.EVALUATE_SCORE,
                    EXAM_SCORE = a.EXAM_SCORE,
                    TOTAL_SCORE = a.TOTAL_SCORE,
                    EVALUATE_NAME = a.EVALUATE_NAME ?? d.PLAN_NAME,
                    EVALUATION_START_TIME = a.EVALUATION_START_TIME??d.EVALUATION_START_TIME,
                    SHELF_LIFE_UTYPE = b.SHELF_LIFE_UTYPE,
                    EPLAN_SHELF_LIFE = b.EPLAN_SHELF_LIFE,
                    SOURCE_TYPE = a.SOURCE_TYPE,
                    WARN_DURATION = b.WARN_DURATION,
                    WARN_UTYPE = b.WARN_UTYPE,
                    EVALUATE_AFFIX = a.EVALUATE_AFFIX,
                    EPLAN_NAME = c.EPLAN_NAME,
                    STD_SCHEME_ID=a.STD_SCHEME_ID,
                    SMBL_FLAG=a.SMBL_FLAG,
                    CER_ID=a.CER_ID,
                    REMARK = a.REMARK
                })
                .ToList();
            List<PmsAssessPlanPersonDto> listDto = _mapper.Map<List<PmsAssessPlanPersonDto>>(varPlanAssess);
            listDto= listDto.OrderBy(w => w.EVALUATION_START_TIME).ToList();
            //过滤评估记录EVALUATE_STATE不是3的状态
            listDto = listDto.FindAll(p => p.SOURCE_TYPE == "1" || (p.SOURCE_TYPE != "1" && p.EVALUATE_STATE == "3")).ToList();
            if (startDate.IsNotNullOrEmpty() && endDate.IsNotNullOrEmpty())
                listDto = listDto.FindAll(w => Convert.ToDateTime(startDate) <= w.EVALUATION_START_TIME && Convert.ToDateTime(endDate).AddMonths(1) > w.EVALUATION_START_TIME);
            var dataClass = _IBaseDataServices.GetOaBaseData().FindAll(x => x.CLASS_ID == "人员评估评价结果");
            foreach (var item in listDto)
            {
                int index = dataClass.FindIndex(w => w.DATA_ID == item.TOTAL_EVALUATE);
                if (index > -1)
                    item.TOTAL_EVALUATE = dataClass[index].DATA_NAME;
            }
            List<string> listCerId = varPlanAssess.Select(w => w.CER_ID).Distinct().ToList();
            var varCer = _soa.Db.Queryable<PMS_SKILL_CERTIFICATE_LIST>().Where(w => listCerId.Contains(w.CERTIFICATE_ID)).ToList();
            List<PMS_PERSON_FILE> pmsPersonFile = _soa.Db.Queryable<PMS_PERSON_FILE>().Where(p => p.FILE_STATE == "1").Select<PMS_PERSON_FILE>().ToList();

            //对于可预览文件，要输出预览格式
            foreach (var file in pmsPersonFile)
            {
                if (OfficeHelper.CastPDFToPreviewSuffixList.Contains(file.FILE_SUFFIX.ToLower()))
                    file.FILE_TYPE = "PDF";
            }
            List<string> listStdId = varPlanAssess.Select(w => w.STD_SCHEME_ID).Distinct().ToList();
            var varEvalDict = _soa.Db.Queryable<OA_EVAL_STAGE_DICT>().Where(w => listStdId.Contains(w.EVAL_STAGE_ID)).ToList();
            foreach (var item in listDto)
            {
                OA_EVAL_STAGE_DICT evalDict = varEvalDict.Find(w => w.EVAL_STAGE_ID == item.STD_SCHEME_ID);
                item.EVAL_STAGE_CLASS = evalDict?.EVAL_STAGE_CLASS;
                if (item.EPLAN_NAME.IsNullOrEmpty())
                    item.EPLAN_NAME = evalDict?.EVAL_STAGE_NAME;
                string resume_affix_name = string.Empty;
                List<PMS_SKILL_CERTIFICATE_LIST> listCer = varCer.FindAll(w => w.CERTIFICATE_ID == item.CER_ID);
                foreach (var cer in listCer)
                {
                    cer.CERTIFICATE_TYPE_NAME = _IBaseDataServices.RecordClassOaBaseName("人事技能证书类型", cer.CERTIFICATE_TYPE);
                    cer.CERTIFICATE_DID_NAME = _IBaseDataServices.GetSkillCertificateName(cer);
                    if (cer.CERTIFICATE_AFFIX != null && cer.CERTIFICATE_AFFIX != "")
                    {
                        string[] AFFIXARRY = cer.CERTIFICATE_AFFIX.Split(",");
                        List<PMS_PERSON_FILE> pmsPersonFileCertificate = pmsPersonFile.Where(p => AFFIXARRY.Contains(p.FILE_ID)).ToList();
                        for (int i = 0; i < AFFIXARRY.Length; i++)
                        {
                            var PersonFile = pmsPersonFileCertificate.FirstOrDefault(p => p.FILE_ID == AFFIXARRY[i]);
                            if (PersonFile != null)
                            {
                                _IPmsService.FillPersonFileInfo(PersonFile);
                                cer.PMS_PERSON_FILE = pmsPersonFileCertificate;
                                resume_affix_name += PersonFile.FILE_NAME + ",";
                            }
                        }
                        if (resume_affix_name != "")
                        {
                            cer.AFFIX_NAME = resume_affix_name.Substring(0, resume_affix_name.Length - 1);
                        }
                    }
                }
                if (listCer.Count > 0)
                    item.CERTIFICATE = listCer[0];
                if (item.EVALUATE_AFFIX.IsNotNullOrEmpty())
                {
                    string[] AFFIXARRY = item.EVALUATE_AFFIX.Split(",");
                    List<PMS_PERSON_FILE> pmsPersonFileReward = pmsPersonFile.Where(p => AFFIXARRY.Contains(p.FILE_ID)).ToList();
                    for (int i = 0; i < AFFIXARRY.Length; i++)
                    {
                        var PersonFile = pmsPersonFileReward.FirstOrDefault(p => p.FILE_ID == AFFIXARRY[i]);
                        if (PersonFile != null)
                        {
                            _IPmsService.FillPersonFileInfo(PersonFile);
                            item.PMS_PERSON_FILE = pmsPersonFileReward;
                            resume_affix_name += PersonFile.FILE_NAME + ",";
                        }
                    }
                    if (resume_affix_name != "")
                    {
                        item.AFFIX_NAME = resume_affix_name.Substring(0, resume_affix_name.Length - 1);
                    }
                }
            }

            resultDto.data = listDto;
            return resultDto;
        }
        /// <summary>
        /// 保存评估记录
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="userName"></param>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        public ResultDto SaveAssessInfo(PmsAssessPlanPersonDto dto, string userName, string hospitalId)
        {
            ResultDto resultDto = new ResultDto();
            string smblFlag = _httpContext.GetSmblFlag();
            PMS_ASSESS_PLAN_PERSON assessPlan = _mapper.Map<PMS_ASSESS_PLAN_PERSON>(dto);
            int count = 0;
            if (assessPlan.PLAN_PERSON_ID.IsNullOrEmpty())
            {
                assessPlan.PLAN_ID = IDGenHelper.CreateGuid();
                assessPlan.PLAN_PERSON_ID = IDGenHelper.CreateGuid();
                assessPlan.SOURCE_TYPE = "1";
                assessPlan.FIRST_RPERSON = userName;
                assessPlan.FIRST_RTIME = DateTime.Now;
                assessPlan.EVALUATE_STATE = "0";
                if (smblFlag == "1")
                    assessPlan.SMBL_FLAG = smblFlag;
                count = _soa.Db.Insertable(assessPlan).ExecuteCommand();

            }
            else
            {
                assessPlan.LAST_MPERSON = userName;
                assessPlan.LAST_MTIME = DateTime.Now;
                count = _soa.Db.Updateable(assessPlan).IgnoreColumns(w => new { w.EVALUATE_AFFIX,w.EVALUATE_STATE, w.PLAN_ID, w.SOURCE_TYPE, w.FIRST_RTIME, w.FIRST_RPERSON, w.EXAM_EVALUATE}).ExecuteCommand();
            }
            //增加证书保存
            if (dto.CERTIFICATE != null && dto.CERTIFICATE.CERTIFICATE_NAME != null)
            {
                string cer_list = JsonConvert.SerializeObject(dto.CERTIFICATE);
                string action = (dto.CERTIFICATE.CERTIFICATE_ID != null && dto.CERTIFICATE.CERTIFICATE_ID != "") ? "U" : "I";
                assessPlan.CER_ID = _IPmsService.SaveAttachCertificate( "PMS_SKILL_CERTIFICATE_LIST", cer_list, userName, action, hospitalId, dto.PERSON_ID, "","ASSESS", assessPlan.PLAN_PERSON_ID);
                _soa.Db.Updateable(assessPlan).UpdateColumns(new List<string> { "CER_ID" }.ToArray()).ExecuteCommand();
            }
            Dictionary<string, object> dicReturn = new Dictionary<string, object>();
            dicReturn.Add("COUNT", count);
            dicReturn.Add("MAXID", assessPlan.PLAN_PERSON_ID);
            resultDto.success = count > 0;
            resultDto.data = dicReturn;
            return resultDto;
        }
        /// <summary>
        /// 更新评估记录
        /// </summary>
        /// <param name="planpPersonIds"></param>
        /// <param name="assessPlanType"></param>
        /// <param name="userName"></param>
        /// <param name="hospitalId"></param>
        /// <param name="checkPerson"></param>
        /// <param name="operCause"></param>
        /// <param name="operComputer"></param>
        /// <param name="pwd"></param>
        /// <param name="logId"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public ResultDto UpdateAssessPlanInfo(string? planpPersonIds, string? assessPlanType, string? userName, string? hospitalId, string? checkPerson, string? operCause, string? operComputer, string? pwd, string? logId)
        {
            ResultDto rDto = new ResultDto();
            int rowCount = 0;
            var success = false;
            try
            {
                if (assessPlanType != "10")
                {
                    var obj = new
                    {
                        logId = logId,
                        password = pwd
                    };
                    string jsonStr = JsonConvert.SerializeObject(obj);
                    success = _systemService.UserVerify(jsonStr).success;
                }
                else
                    success = true;
                if (success)
                {
                    //添加审核流程信息
                    List<PMS_ASSESS_PERSON_OPER_LOG> listOperAdd = new List<PMS_ASSESS_PERSON_OPER_LOG>();
                    //修改审核流程信息
                    List<PMS_ASSESS_PERSON_OPER_LOG> listOperUpdate = new List<PMS_ASSESS_PERSON_OPER_LOG>();
                    //修改人员评估信息
                    List<PMS_ASSESS_PLAN_PERSON> listAssessPlanUpdate = new List<PMS_ASSESS_PLAN_PERSON>();
                    List<string> ids = new List<string>();
                    if (planpPersonIds.Contains(","))
                    {
                        for (int i = 0; i < planpPersonIds.Split(',').Length; i++)
                        {
                            ids.Add(planpPersonIds.Split(',')[i]);
                        }
                    }
                    else
                        ids.Add(planpPersonIds);
                    var res = _soa.Db.Queryable<PMS_ASSESS_PLAN_PERSON>().Where(p => ids.Contains(p.PLAN_PERSON_ID) && p.EVALUATE_STATE != "4").ToList();

                    var resOper = _soa.Db.Queryable<PMS_ASSESS_PERSON_OPER_LOG>().Where(p => ids.Contains(p.PLAN_PERSON_ID) && p.OPER_STATE == "1").ToList();

                    if (res.Count() > 0)
                    {
                        var dt = DateTime.Now;
                        foreach (var item in ids)
                        {
                            var dataAssessUserPlan = res.Where(p => p.PLAN_PERSON_ID == item).FirstOrDefault();
                            PMS_ASSESS_PERSON_OPER_LOG dataOper = new PMS_ASSESS_PERSON_OPER_LOG();
                            dataOper.LOG_ID = IDGenHelper.CreateGuid();
                            dataOper.PLAN_PERSON_ID = item;
                            dataOper.OPER_ID = "2";
                            dataOper.HOSPITAL_ID = hospitalId;
                            switch (assessPlanType)
                            {

                                case "5": //撤销
                                    //获取最后一条流程信息，修改成撤销状态
                                    var dataOperInfo = resOper.Where(p => p.PLAN_PERSON_ID == item).OrderByDescending(p => p.OPER_TIME).FirstOrDefault();
                                    //只能谁操作谁撤销
                                    if (dataOperInfo != null && dataOperInfo.OPER_PERSON.Contains("_"))
                                    {
                                        string lastPerson = dataOperInfo.OPER_PERSON?.Split('_')?.FirstOrDefault();
                                        if (lastPerson != logId)
                                        {
                                            rDto.success = false;
                                            rDto.msg = $"当前操作人{userName}无法撤销{dataOperInfo.OPER_PERSON}{dataOperInfo.OPER_STATE}的评估";
                                            return rDto;
                                        }
                                    }
                                    if (dataOperInfo != null)
                                    {
                                        dataOperInfo.OPER_STATE = "0";
                                        listOperUpdate.Add(dataOperInfo);
                                    }
                                    switch (dataAssessUserPlan.EVALUATE_STATE)
                                    {
                                        case "1":
                                            dataAssessUserPlan.EVALUATE_STATE = "0";
                                            break;
                                        case "2":
                                            dataAssessUserPlan.EVALUATE_STATE = "0";
                                            break;
                                        case "3":
                                            dataAssessUserPlan.EVALUATE_STATE = "1";
                                            break;
                                        default:
                                            break;
                                    }
                                    //撤销关联证书
                                    if (dataAssessUserPlan.CER_ID == $"ASSESS_{dataAssessUserPlan.PLAN_PERSON_ID}")
                                    {
                                        _IPmsService.BatchSubmitRecordInfo($"ASSESS_{dataAssessUserPlan.PLAN_PERSON_ID}", "PMS_SKILL_CERTIFICATE_LIST", userName, hospitalId, "R", checkPerson, logId, operCause, pwd);
                                    }
                                    break;
                                case "2": //驳回
                                    if (dataAssessUserPlan.EVALUATE_STATE != "2")//审核驳回
                                    {
                                        rDto.success = false;
                                        rDto.msg = "只有状态为“提交”的评估才可驳回";
                                    }
                                    else if (operCause.IsNullOrEmpty())
                                    {
                                        rDto.success = false;
                                        rDto.msg = "驳回原因不能为空";
                                    }
                                    else
                                    {
                                        dataOper.HOSPITAL_ID = hospitalId;
                                        dataOper.CAUSE_TYPE = "审核驳回";
                                        dataOper.OPER_CAUSE = operCause;
                                        dataOper.OPER_TIME = dt;
                                        dataOper.OPER_PERSON = logId + "_" + userName;
                                        dataOper.OPER_COMPUTER = operComputer;
                                        dataOper.OPER_STATE = assessPlanType;
                                        listOperAdd.Add(dataOper);
                                        dataAssessUserPlan.EVALUATE_STATE = "2";
                                    }
                                    break;
                                case "1": //提交
                                    if (dataAssessUserPlan.EVALUATE_STATE == assessPlanType)
                                    {
                                        rDto.success = false;
                                        rDto.msg = "该已提交评估审核信息,无法重复提交!";
                                    }
                                    if (dataAssessUserPlan.EVALUATE_STATE == "3")
                                    {
                                        rDto.success = false;
                                        rDto.msg = "已审核通过的评估无法提交";
                                    }
                                    else
                                    {
                                        dataOper.OPER_STATE = assessPlanType;
                                        dataOper.CAUSE_TYPE = "提交";
                                        dataAssessUserPlan.CHECK_PERSON = checkPerson;
                                    }
                                    dataAssessUserPlan.EVALUATE_STATE = assessPlanType;
                                    //提交关联证书
                                    if (dataAssessUserPlan.CER_ID == $"ASSESS_{dataAssessUserPlan.PLAN_PERSON_ID}")
                                    {
                                        _IPmsService.BatchSubmitRecordInfo($"ASSESS_{dataAssessUserPlan.PLAN_PERSON_ID}", "PMS_SKILL_CERTIFICATE_LIST", userName, hospitalId, "B", checkPerson, logId, operCause, pwd);
                                    }
                                    break;
                                case "3": //审核
                                    if (dataAssessUserPlan.EVALUATE_STATE == assessPlanType)
                                    {
                                        rDto.success = false;
                                        rDto.msg = "该评估信息已审核,无法重复审核!";
                                    }
                                    if (dataAssessUserPlan.EVALUATE_STATE != "2")
                                    {
                                        rDto.success = false;
                                        rDto.msg = "只有状态为“提交”的评估才可审核";
                                    }
                                    else
                                    {
                                        dataOper.OPER_STATE = assessPlanType;
                                        dataOper.CAUSE_TYPE = "通过";
                                        dataOper.OPER_CAUSE = "同意";
                                        //dataAssessUserPlan.CHECK_PERSON = check_person;
                                        dataAssessUserPlan.CHECK_TIME = dt;
                                    }
                                    dataAssessUserPlan.EVALUATE_STATE = assessPlanType;
                                    break;
                                case "10": //删除
                                    if (dataAssessUserPlan.EVALUATE_STATE != "0" && dataAssessUserPlan.USER_ID != "STD_PMSASSESS")
                                    {
                                        rDto.success = false;
                                        rDto.msg = "只有状态为未提交的评估才可删除";
                                    }
                                    else
                                    {
                                        dataOper.OPER_STATE = assessPlanType;
                                        dataOper.CAUSE_TYPE = "通过";
                                        dataOper.OPER_CAUSE = "同意";
                                    }
                                    dataAssessUserPlan.EVALUATE_STATE = "4";
                                    break;
                                default:
                                    break;
                            }

                            dataOper.OPER_TIME = dt;
                            dataOper.OPER_PERSON = userName;
                            dataOper.OPER_COMPUTER = operComputer;
                            dataOper.OPER_STATE = "1";
                            listOperAdd.Add(dataOper);
                            dataAssessUserPlan.LAST_MPERSON = userName;
                            dataAssessUserPlan.LAST_MTIME = dt;
                            listAssessPlanUpdate.Add(dataAssessUserPlan);
                        }
                        if (listAssessPlanUpdate.Count() > 0)
                        {
                            rowCount = _soa.Db.Updateable(listAssessPlanUpdate).ExecuteCommand();
                        }
                        if (listOperAdd.Count() > 0)
                        {
                            rowCount = _soa.Db.Insertable(listOperAdd).ExecuteCommand();
                        }
                        if (listOperUpdate.Count() > 0)
                        {
                            rowCount = _soa.Db.Updateable(listOperUpdate).ExecuteCommand();
                        }
                        if (rowCount == 0)
                        {
                            rDto.msg = "操作失败" + (rDto.msg.IsNullOrEmpty() ? "" : $"-{rDto.msg}");
                            rDto.success = false;
                        }
                    }
                    else
                    {
                        rDto.success = false;
                        rDto.msg = "没有找到要操作的数据";
                    }
                }
                else
                {
                    rDto.success = false;
                    rDto.msg = "密码输入错误";
                }
            }
            catch (Exception ex)
            {
                rDto.msg = ex.Message;
                rDto.success = false;
                _logger.LogError($"UpdateAssessPlanInfo:{ex.ToString()}");
            }
            return rDto;
        }




        /// <summary>
        /// 保存考试规评明细登记
        /// </summary>
        /// <param name="stdSchemeDto"></param>
        /// <param name="userName"></param>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        public ResultDto SavePmsAssessStdSchemeDetail(List<PmsAssessPlanPersonDto> pmsAssessPlanPersonDtos, string userName, string hospitalId)
        {
            ResultDto rDto = new ResultDto();
            List<PMS_ASSESS_PLAN_PERSON> listPmsAssessPlanPerson = _mapper.Map<List<PMS_ASSESS_PLAN_PERSON>>(pmsAssessPlanPersonDtos);
            List<PMS_ASSESS_PLAN_PERSON> varPlanPerson = _soa.Db.Queryable<PMS_ASSESS_PLAN_PERSON>().Where(w => w.STD_GROUP_ID == listPmsAssessPlanPerson[0].STD_GROUP_ID).ToList();
            PMS_ASSESS_PLAN_PERSON stdAssess = varPlanPerson.Find(w => w.USER_ID == "STD_PMSASSESS");
            if (stdAssess == null)
            {
                rDto.msg = "查询数据失败!";
                rDto.success = false;
                return rDto;
            }
            List<PMS_ASSESS_PLAN_PERSON> listAdd = new List<PMS_ASSESS_PLAN_PERSON>();
            List<PMS_ASSESS_PLAN_PERSON> listUpdate = new List<PMS_ASSESS_PLAN_PERSON>();
            var dataClass = _IBaseDataServices.GetOaBaseData().FindAll(x => x.CLASS_ID == "人员评估评价结果" && (x.HOSPITAL_ID == _httpContext.GetHospitalId() || x.HOSPITAL_ID == "H0000")).OrderBy(x => x.DATA_SORT).ToList();
            foreach (var item in listPmsAssessPlanPerson)
            {
                item.PLAN_PERSON_ID = varPlanPerson?.Find(w => w.USER_ID == item.USER_ID)?.PLAN_PERSON_ID;
                if (item.PLAN_PERSON_ID.IsNullOrEmpty())
                {
                    item.PLAN_PERSON_ID = IDGenHelper.CreateGuid();
                    item.STD_SCHEME_ID = stdAssess.STD_SCHEME_ID;
                    item.EVALUATE_NAME = stdAssess.EVALUATE_NAME;
                    item.SMBL_FLAG = stdAssess.SMBL_FLAG;
                    item.SOURCE_TYPE = "1";
                    item.EVALUATE_STATE = "3";
                    item.EVALUATION_START_TIME = stdAssess.EVALUATION_START_TIME;
                    item.TRAIN_ID = stdAssess.TRAIN_ID;
                    item.CER_ID = stdAssess.CER_ID;
                    item.PLAN_ID = stdAssess.PLAN_ID;
                    //每个人所选的规评所属分组id
                    item.FIRST_RPERSON = userName;
                    item.FIRST_RTIME = DateTime.Now;
                    if (item.PGROUP_ID.IsNullOrEmpty())
                        item.PGROUP_ID = _soa.Db.Queryable<SYS6_USER>().Where(w => w.USER_NO == item.USER_ID).ToList().FirstOrDefault()?.DEPT_CODE;
                    listAdd.Add(item);
                }
                else
                {
                    item.STD_GROUP_ID = stdAssess.STD_GROUP_ID;
                    item.STD_SCHEME_ID = stdAssess.STD_SCHEME_ID;
                    item.EVALUATE_STATE = "3";
                    item.SOURCE_TYPE = "1";
                    item.SMBL_FLAG = stdAssess.SMBL_FLAG;
                    item.EVALUATE_NAME = stdAssess.EVALUATE_NAME;
                    item.EVALUATE_STATE = stdAssess.EVALUATE_STATE;
                    item.EVALUATION_START_TIME = stdAssess.EVALUATION_START_TIME;
                    item.TRAIN_ID = stdAssess.TRAIN_ID;
                    item.CER_ID = stdAssess.CER_ID;
                    item.PLAN_ID = stdAssess.PLAN_ID;
                    //每个人所选的规评所属分组id
                    item.LAST_MPERSON = userName;
                    item.LAST_MTIME = DateTime.Now;
                    listUpdate.Add(item);
                }
            }
            int count = 0;
            if (listAdd.Count > 0)
                count = _soa.Db.Insertable(listAdd).ExecuteCommand();
            if (listUpdate.Count > 0)
                count = _soa.Db.Updateable(listUpdate).IgnoreColumns(w => new { w.FIRST_RPERSON, w.FIRST_RTIME, w.EVALUATE_AFFIX }).ExecuteCommand();
            //写入规评结果  
            List<PMS_ASSESS_PLAN_PERSON> listTemp = new List<PMS_ASSESS_PLAN_PERSON>();
            listTemp.AddRange(listAdd.FindAll(w => w.TOTAL_EVALUATE.IsNotNullOrEmpty()));
            listTemp.AddRange(listUpdate.FindAll(w => w.TOTAL_EVALUATE.IsNotNullOrEmpty()));
            List<EvaluatePlanUserResultParm> listParm = new List<EvaluatePlanUserResultParm>();
            foreach (var item in listTemp)
            {
                EvaluatePlanUserResultParm evaluatePlanUserResultParm = new EvaluatePlanUserResultParm();
                evaluatePlanUserResultParm.EPLAN_ID = item.STD_SCHEME_ID;
                evaluatePlanUserResultParm.USER_ID = item.USER_ID;
                evaluatePlanUserResultParm.DATA_CLASS = "评估规评登记";
                evaluatePlanUserResultParm.DATA_ID = item.PLAN_PERSON_ID;
                evaluatePlanUserResultParm.RESULT = dataClass.Find(w=>w.DATA_ID== item.TOTAL_EVALUATE)?.DATA_NAME=="不合格" ? EPlanResultEnum.FAIL : EPlanResultEnum.PASS;
                evaluatePlanUserResultParm.AFFECT_DATE = DateTime.Now;
                listParm.Add(evaluatePlanUserResultParm);
            }
            _IEvaluatePlanService.WriteEvaluatePlanUserResult(listParm);
            rDto.success = count > 0;
            return rDto;
        }




        /// <summary>
        /// 保存评估规评登记
        /// </summary>
        /// <param name="stdSchemeDto"></param>
        /// <param name="userName"></param>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        public ResultDto SavePmsAssessStdScheme(PmsAssessPlanPersonDto pmsAssessPlanPersonDto, string userName, string hospitalId)
        {
            ResultDto rDto = new ResultDto();
            int count = 0;
            string smblFlag = _httpContext.GetSmblFlag();
            PMS_ASSESS_PLAN_PERSON assessPlanPerson = _mapper.Map<PMS_ASSESS_PLAN_PERSON>(pmsAssessPlanPersonDto);
            if (assessPlanPerson.PLAN_PERSON_ID.IsNullOrEmpty())
            {
                assessPlanPerson.PLAN_PERSON_ID = IDGenHelper.CreateGuid();
                 assessPlanPerson.PLAN_ID = IDGenHelper.CreateGuid();
                 assessPlanPerson.STD_GROUP_ID = IDGenHelper.CreateGuid();
                assessPlanPerson.USER_ID = "STD_PMSASSESS";//固定写死 查询使用
                assessPlanPerson.FIRST_RPERSON = userName;
                assessPlanPerson.FIRST_RTIME = DateTime.Now;
                  assessPlanPerson.EVALUATE_STATE = "3";
                 if (smblFlag == "1")
                    assessPlanPerson.SMBL_FLAG = smblFlag;
                //证书新增一条证书记录 证书id绑定到CER_ID
                if (pmsAssessPlanPersonDto.CERTIFICATE_TYPE.IsNotNullOrEmpty())
                {
                    PMS_SKILL_CERTIFICATE_LIST skillCer = new PMS_SKILL_CERTIFICATE_LIST();
                    skillCer.CERTIFICATE_ID = IDGenHelper.CreateGuid();
                    skillCer.CERTIFICATE_TYPE = pmsAssessPlanPersonDto.CERTIFICATE_TYPE;
                    skillCer.CERTIFICATE_DATE = pmsAssessPlanPersonDto.CERTIFICATE_DATE;
                    skillCer.CERTIFICATE_UNIT = pmsAssessPlanPersonDto.CERTIFICATE_UNIT;
                    skillCer.PERSON_ID = assessPlanPerson.PLAN_PERSON_ID;
                    skillCer.CERTIFICATE_SORT = EntityHelper.GetSort();
                    bool success = _soa.Db.Insertable(skillCer).ExecuteCommand() > 0;
                    if (success)
                        assessPlanPerson.CER_ID = skillCer.CERTIFICATE_ID;
                    else
                    {
                        rDto.success = false;
                        rDto.msg = "保存资质证书失败";
                        return rDto;
                    }
                }
                //生成一条规评分组记录
                count = _soa.Db.Insertable(assessPlanPerson).ExecuteCommand();
            }
            else
            {
                if (pmsAssessPlanPersonDto.CER_ID.IsNotNullOrEmpty())
                {
                    PMS_SKILL_CERTIFICATE_LIST skillCer = _soa.Db.Queryable<PMS_SKILL_CERTIFICATE_LIST>().Where(w => w.CERTIFICATE_ID == pmsAssessPlanPersonDto.CER_ID).ToList().FirstOrDefault();
                    if (skillCer != null && (skillCer.CERTIFICATE_TYPE != pmsAssessPlanPersonDto.CERTIFICATE_TYPE ||
                        skillCer.CERTIFICATE_UNIT != pmsAssessPlanPersonDto.CERTIFICATE_UNIT ||
                        skillCer.CERTIFICATE_DATE != pmsAssessPlanPersonDto.CERTIFICATE_DATE))
                    {
                        skillCer.CERTIFICATE_TYPE = pmsAssessPlanPersonDto.CERTIFICATE_TYPE;
                        skillCer.CERTIFICATE_DATE = pmsAssessPlanPersonDto.CERTIFICATE_DATE;
                        skillCer.CERTIFICATE_UNIT = pmsAssessPlanPersonDto.CERTIFICATE_UNIT;
                        _soa.Db.Updateable(skillCer).ExecuteCommand();
                    }
                }               //证书新增一条证书记录 证书id绑定到CER_ID
                if (pmsAssessPlanPersonDto.CERTIFICATE_TYPE.IsNotNullOrEmpty())
                {
                    PMS_SKILL_CERTIFICATE_LIST skillCer = new PMS_SKILL_CERTIFICATE_LIST();
                    skillCer.CERTIFICATE_ID = IDGenHelper.CreateGuid();
                    skillCer.CERTIFICATE_TYPE = pmsAssessPlanPersonDto.CERTIFICATE_TYPE;
                    skillCer.CERTIFICATE_DATE = pmsAssessPlanPersonDto.CERTIFICATE_DATE;
                    skillCer.CERTIFICATE_UNIT = pmsAssessPlanPersonDto.CERTIFICATE_UNIT;
                    skillCer.PERSON_ID = assessPlanPerson.PLAN_PERSON_ID;
                    skillCer.CERTIFICATE_SORT = EntityHelper.GetSort();
                    bool success = _soa.Db.Insertable(skillCer).ExecuteCommand() > 0;
                    if (success)
                        assessPlanPerson.CER_ID = skillCer.CERTIFICATE_ID;
                }
                assessPlanPerson.LAST_MPERSON = userName;
                assessPlanPerson.LAST_MTIME = DateTime.Now;
                count = _soa.Db.Updateable(assessPlanPerson).IgnoreColumns(w => new { w.USER_ID, w.PLAN_ID, w.STD_GROUP_ID, w.FIRST_RPERSON, w.FIRST_RTIME, w.EVALUATE_STATE }).ExecuteCommand();
            }
            rDto.success = count > 0;
            return rDto;
        }

        /// <summary>
        /// 获取规评信息
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="labGroupId"></param>
        /// <param name="eplanName"></param>
        /// <param name="examName"></param>
        /// <returns></returns>

        public ResultDto GetPmsAssessStd(string startDate, string endDate, string labGroupId, string eplanName, string assessName,string smblFlag)
        {
            string hospitalId = _httpContext.GetHospitalId();
            string labId = _httpContext.GetLabId();
            ResultDto resultDto = new ResultDto();
            if (_httpContext.GetSmblFlag() == "1")
                smblFlag = "1";
            var varAssessInfo = _soa.Db.Queryable<PMS_ASSESS_PLAN_PERSON>()
                 .InnerJoin<OA_EVALUATE_PLAN_DICT>((a, b) => a.STD_SCHEME_ID == b.EPLAN_ID && b.EPLAN_STATE == "1" && b.HOSPITAL_ID == hospitalId)
                .LeftJoin<OA_EVALUATE_PLAN_SETUP>((a, b, c) => a.STD_SCHEME_ID == c.EPLAN_ID && c.EPLAN_SSTATE == "1")
                .LeftJoin<PMS_SKILL_CERTIFICATE_LIST>((a, b, c, d) => a.CER_ID == d.CERTIFICATE_ID)
                .Where((a, b, c) => a.USER_ID == "STD_PMSASSESS" && a.EVALUATE_STATE != "4")
                .WhereIF(smblFlag == "1", (a, b, c) => a.SMBL_FLAG == smblFlag)
                .WhereIF(labGroupId.IsNotNullOrEmpty(), (a, b, c) => c.LAB_ID == labGroupId || b.PGROUP_ID == labGroupId)
                .WhereIF(eplanName.IsNotNullOrEmpty(), (a, b, c) => b.EPLAN_NAME.Contains(eplanName))
                .WhereIF(assessName.IsNotNullOrEmpty(), (a, b, c) => (a.EVALUATE_NAME != null && a.EVALUATE_NAME.Contains(assessName) || b.EPLAN_NAME.Contains(assessName)))
                .Select((a, b, c, d) => new PmsAssessPlanPersonDto()
                {
                    PLAN_PERSON_ID = a.PLAN_PERSON_ID,
                    USER_ID = a.USER_ID,
                    EVALUATE_STATE = a.EVALUATE_STATE,
                    EXAM_SCORE = a.EXAM_SCORE,
                    LAB_ID = b.LAB_ID,
                    DICT_PGROUP_ID = b.PGROUP_ID,
                    PGROUP_ID = a.PGROUP_ID,
                    EPLAN_NAME = b.EPLAN_NAME,
                    COMP_EVALUATE = a.COMP_EVALUATE,
                    EPLAN_APPLY_TYPE = c.EPLAN_APPLY_TYPE,
                    EVALUATION_START_TIME = a.EVALUATION_START_TIME,
                    EVALUATE_NAME = a.EVALUATE_NAME,
                    STD_GROUP_ID = a.STD_GROUP_ID,
                    SMBL_FLAG = a.SMBL_FLAG,
                    STD_SCHEME_ID = a.STD_SCHEME_ID,
                    EXAM_START_TIME = a.EXAM_START_TIME,
                    SHELF_LIFE_UTYPE = c.SHELF_LIFE_UTYPE,
                    EPLAN_SHELF_LIFE = c.EPLAN_SHELF_LIFE,
                    WARN_DURATION = c.WARN_DURATION,
                    WARN_UTYPE = c.WARN_UTYPE,
                    EVALUATE_AFFIX = a.EVALUATE_AFFIX,
                    CER_ID = a.CER_ID,
                    CERTIFICATE_TYPE = d.CERTIFICATE_TYPE,
                    CERTIFICATE_UNIT = d.CERTIFICATE_UNIT,
                    CERTIFICATE_DATE = d.CERTIFICATE_DATE,
                    CERTIFICATE_VALIDITY = d.CERTIFICATE_VALIDITY

                })
                .ToList();
            if (startDate.IsNotNullOrEmpty() && endDate.IsNotNullOrEmpty())
                varAssessInfo = varAssessInfo.FindAll(w => Convert.ToDateTime(startDate) <= w.EVALUATION_START_TIME && w.EVALUATION_START_TIME < Convert.ToDateTime(endDate));
            List<SYS6_INSPECTION_LAB> listLab = _soa.Db.Queryable<SYS6_INSPECTION_LAB>().Where(w => w.STATE_FLAG == "1").ToList();
            List<SYS6_INSPECTION_PGROUP> listGroup = _soa.Db.Queryable<SYS6_INSPECTION_PGROUP>().Where(w => w.PGROUP_STATE == "1").ToList();
            List<PmsAssessPlanPersonDto> listDto = _mapper.Map<List<PmsAssessPlanPersonDto>>(varAssessInfo);
            List<string> listGroupId = listDto.Select(w => w.STD_GROUP_ID).Distinct().ToList();
            var varAssessDetail = _soa.Db.Queryable<PMS_ASSESS_PLAN_PERSON>().Where(w => listGroupId.Contains(w.STD_GROUP_ID) && w.USER_ID != "STD_PMSASSESS" && w.EVALUATE_STATE != "4").ToList();
            List<PMS_PERSON_FILE> pmsPersonFile = _soa.Db.Queryable<PMS_PERSON_FILE>().Where(p => p.FILE_STATE == "1").Select<PMS_PERSON_FILE>().ToList();
            foreach (var item in listDto)
            {
                //规评方案的专业组id==规评计划的专业组id则为专业组 取专业组名称
                if (item.DICT_PGROUP_ID.IsNotNullOrEmpty() &&
                    item.PGROUP_ID.IsNotNullOrEmpty() &&
                    item.DICT_PGROUP_ID == item.PGROUP_ID)
                    item.LAB_ID = "";
                else
                    item.LAB_NAME = listLab.Find(w => w.LAB_ID == item.LAB_ID)?.LAB_NAME;
                item.PGROUP_NAME = listGroup.Find(w => w.PGROUP_ID == item.PGROUP_ID)?.PGROUP_NAME;
                string resume_affix_name = string.Empty;
                var detail = varAssessDetail.FindAll(w => w.STD_GROUP_ID == item.STD_GROUP_ID);
                //是否全部完成 填入评价
                bool state = detail.Count > 0 && detail.All(p => !string.IsNullOrEmpty(p.TOTAL_SCORE));
                if (state)
                    item.STATE_NAME = "已完成";
                else
                    item.STATE_NAME = "未完成";
                if (item.EVALUATE_AFFIX.IsNotNullOrEmpty())
                {
                    string[] AFFIXARRY = item.EVALUATE_AFFIX.Split(",");
                    List<PMS_PERSON_FILE> pmsPersonFileReward = pmsPersonFile.Where(p => AFFIXARRY.Contains(p.FILE_ID)).ToList();
                    for (int i = 0; i < AFFIXARRY.Length; i++)
                    {
                        var PersonFile = pmsPersonFileReward.FirstOrDefault(p => p.FILE_ID == AFFIXARRY[i]);
                        if (PersonFile != null)
                        {
                            _IPmsService.FillPersonFileInfo(PersonFile);
                            item.PMS_PERSON_FILE = pmsPersonFileReward;
                            resume_affix_name += PersonFile.FILE_NAME + ",";
                        }
                    }
                    if (resume_affix_name != "")
                    {
                        item.AFFIX_NAME = resume_affix_name.Substring(0, resume_affix_name.Length - 1);
                    }
                }
            }
            listDto = listDto.FindAll(w => (w.LAB_ID.IsNullOrEmpty() || w.LAB_ID == labId));
            resultDto.data = listDto;
            return resultDto;
        }


        /// <summary>
        /// 获取规评分组明细
        /// </summary>
        /// <param name="stdGroupId"></param>
        /// <returns></returns>
        public ResultDto GetPmsAssessStdDetail(string stdGroupId, string labPgroupId, string comName, string userName)
        {
            string hospitalId = _httpContext.GetHospitalId();
            ResultDto resultDto = new ResultDto();
            var varAssessInfo = _soa.Db.Queryable<PMS_ASSESS_PLAN_PERSON>()
                  .LeftJoin<OA_EVALUATE_PLAN_DICT>((a, b) => a.STD_SCHEME_ID == b.EPLAN_ID && b.EPLAN_STATE == "1")
                  .Where((a, b) => a.STD_GROUP_ID == stdGroupId && a.USER_ID != "STD_PMSASSESS" && a.EVALUATE_STATE != "4" )
                 .WhereIF(labPgroupId.IsNotNullOrEmpty(), (a, b) => a.PGROUP_ID == labPgroupId)
                 .Select((a, b) => new PmsAssessPlanPersonDto()
                 {
                     PLAN_PERSON_ID = a.PLAN_PERSON_ID,
                     USER_ID = a.USER_ID,
                     EVALUATE_STATE = a.EVALUATE_STATE,
                     EXAM_SCORE = a.EXAM_SCORE,
                     LAB_ID = b.LAB_ID,
                     PGROUP_ID = a.PGROUP_ID,
                     COMP_EVALUATE = a.COMP_EVALUATE,
                     TOTAL_EVALUATE = a.TOTAL_EVALUATE,
                     TOTAL_SCORE = a.TOTAL_SCORE,
                     EVALUATE_SCORE = a.EVALUATE_SCORE,
                     SEVALUATION_SCORE = a.SEVALUATION_SCORE,
                     EVALUATE_NAME = a.EVALUATE_NAME,
                     EVALUATION_START_TIME = a.EVALUATION_START_TIME,
                     EVALUATE_AFFIX = a.EVALUATE_AFFIX
                 })
                .ToList();
            List<SYS6_INSPECTION_LAB> listLab = _soa.Db.Queryable<SYS6_INSPECTION_LAB>().Where(w => w.STATE_FLAG == "1").ToList();
            List<SYS6_INSPECTION_PGROUP> listGroup = _soa.Db.Queryable<SYS6_INSPECTION_PGROUP>().Where(w => w.PGROUP_STATE == "1").ToList();
            List<PMS_PERSON_FILE> pmsPersonFile = _soa.Db.Queryable<PMS_PERSON_FILE>().Where(p => p.FILE_STATE == "1").Select<PMS_PERSON_FILE>().ToList();
            //获取考生组合信息
            var userCom = _soa.Db.Queryable<SYS6_USER_COM>().Where(p => p.HOSPITAL_ID == hospitalId && p.USER_COM_STATE == "1").ToList();
            userCom = userCom.Where(p => p.USER_SNO.IsNotNullOrEmpty()).ToList();
            List<PmsAssessPlanPersonDto> listDto = _mapper.Map<List<PmsAssessPlanPersonDto>>(varAssessInfo);
            List<string> listUserId = listDto.Select(w => w.USER_ID).Distinct().ToList();
            List<SYS6_USER> listUser = _soa.Db.Queryable<SYS6_USER>().Where(w => listUserId.Contains(w.USER_NO)).ToList();
            foreach (var item in listDto)
            {
                item.LAB_NAME = listLab.Find(w => w.LAB_ID == item.LAB_ID)?.LAB_NAME;
                item.PGROUP_NAME = listGroup.Find(w => w.PGROUP_ID == item.PGROUP_ID)?.PGROUP_NAME;
                item.HIS_ID = listUser.Find(w => w.USER_NO == item.USER_ID)?.HIS_ID;
                item.USER_NAME = listUser.Find(w => w.USER_NO == item.USER_ID)?.USERNAME;
                if (userCom.Count() > 0)
                {
                    var dataCom = userCom.Where(p => p.USER_SNO.Split('+').Contains(item.USER_ID)).FirstOrDefault();
                    if (dataCom != null)
                    {
                        item.USER_COM_NAME = dataCom.USER_COM_NAME;
                    }
                }
                string resume_affix_name = string.Empty;
                if (item.EVALUATE_AFFIX.IsNotNullOrEmpty())
                {
                    string[] AFFIXARRY = item.EVALUATE_AFFIX.Split(",");
                    List<PMS_PERSON_FILE> pmsPersonFileReward = pmsPersonFile.Where(p => AFFIXARRY.Contains(p.FILE_ID)).ToList();
                    for (int i = 0; i < AFFIXARRY.Length; i++)
                    {
                        var PersonFile = pmsPersonFileReward.FirstOrDefault(p => p.FILE_ID == AFFIXARRY[i]);
                        if (PersonFile != null)
                        {
                            _IPmsService.FillPersonFileInfo(PersonFile);
                            item.PMS_PERSON_FILE = pmsPersonFileReward;
                            resume_affix_name += PersonFile.FILE_NAME + ",";
                        }
                    }
                    if (resume_affix_name != "")
                    {
                        item.AFFIX_NAME = resume_affix_name.Substring(0, resume_affix_name.Length - 1);
                    }
                }
            }
            if (userName.IsNotNullOrEmpty())
                listDto = listDto.FindAll(w => w.USER_NAME != null && w.USER_NAME.Contains(userName));
            if (comName.IsNotNullOrEmpty())
                listDto = listDto.FindAll(w => w.USER_COM_NAME != null && w.USER_COM_NAME.Contains(comName));
            resultDto.data = listDto;
            return resultDto;
        }

        /// <summary>
        /// 删除规评明细
        /// </summary>
        /// <param name="stdGroupId"></param>
        /// <param name="listUserId"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        public ResultDto DeletePmsAssessStdDetail(string stdGroupId, List<string> listUserId, string userName)
        {
            if (listUserId == null || listUserId.Count == 0)
                return new ResultDto();
            ResultDto resultDto = new ResultDto();
            var planPerson = _soa.Db.Queryable<PMS_ASSESS_PLAN_PERSON>()
                  .Where(w => w.STD_GROUP_ID == stdGroupId && listUserId.Contains(w.USER_ID))
                 .ToList();
            DateTime dateTime = DateTime.Now;
            foreach (var item in planPerson)
            {
                item.EVALUATE_STATE = "4";
                item.LAST_MPERSON = userName;
                item.LAST_MTIME = dateTime;
            }
            resultDto.success = _soa.Db.Updateable(planPerson).ExecuteCommand() > 0;
            List<EvaluatePlanUserResultParm> listParm = new List<EvaluatePlanUserResultParm>();
            planPerson = planPerson.FindAll(w => w.TOTAL_EVALUATE.IsNotNullOrEmpty());
            if (planPerson.Count > 0)
            {
                foreach (var item in planPerson)
                {
                    EvaluatePlanUserResultParm evaluatePlanUserResultParm = new EvaluatePlanUserResultParm();
                    evaluatePlanUserResultParm.EPLAN_ID = item.STD_SCHEME_ID;
                    evaluatePlanUserResultParm.USER_ID = item.USER_ID;
                    evaluatePlanUserResultParm.DATA_CLASS = "评估规评登记";
                    evaluatePlanUserResultParm.DATA_ID = item.PLAN_PERSON_ID;
                    evaluatePlanUserResultParm.RESULT = EPlanResultEnum.DISABLE;
                    evaluatePlanUserResultParm.AFFECT_DATE = DateTime.Now;
                    listParm.Add(evaluatePlanUserResultParm);
                }
                _IEvaluatePlanService.WriteEvaluatePlanUserResult(listParm);
            }
            return resultDto;
        }
    }
}
