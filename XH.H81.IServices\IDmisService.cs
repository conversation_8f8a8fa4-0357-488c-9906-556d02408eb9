﻿using H.Utility;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XH.H81.Models.Dtos.Dmis;

namespace XH.H81.IServices
{
    public interface IDmisService
    {
        /// <summary>
        /// 获取学习记录
        /// </summary>
        /// <param name="hospital_id"></param>
        /// <param name="lab_id"></param>
        /// <param name="firstmenukey"></param>
        /// <param name="class_id"></param>
        /// <param name="doc_type"></param>
        /// <param name="user_id"></param>
        /// <param name="start_time"></param>
        /// <param name="end_time"></param>
        /// <param name="doc_name"></param>
        /// <param name="learning_state"></param>
        /// <param name="default_lab"></param>
        /// <param name="smbl_flag"></param>
        /// <returns></returns>
        List<JobLogDocDto> GetJobLogDocInfo(string hospital_id, string lab_id, string firstmenukey, string class_id, string doc_type, string user_id, string start_time, string end_time,
         string doc_name, string learning_state, string default_lab, string smbl_flag);

        /// <summary>
        /// 获取文档分类下拉
        /// </summary>
        /// <param name="labId"></param>
        /// <param name="meanId"></param>
        /// <returns></returns>
        ResultDto GetClassTypeDropDownInfo(string labId);



        /// <summary>
        /// 获取文档学习记录(按文档)
        /// </summary>
        /// <param name="lab_id"></param>
        /// <param name="hospital_id"></param>
        /// <param name="firstmenukey"></param>
        /// <param name="class_id"></param>
        /// <param name="doc_type"></param>
        /// <param name="doc_name"></param>
        /// <param name="doc_state">0废止1发布</param>
        /// <param name="if_quest">0未勾选1勾选</param>
        /// <returns></returns>
        ResultDto GetDocJobLogInfo(string lab_id, string firstmenukey, string? class_id, string? doc_type, string? doc_name, string? doc_state, string if_quest);



        /// <summary>
        /// 获取已分学习任务人员信息(按人员)
        /// </summary>
        /// <param name="lab_id"></param>
        /// <param name="pgroup_id"></param>
        /// <param name="user_name"></param>
        /// <param name="if_quest">0未勾选1勾选</param>
        /// <returns></returns>
        ResultDto GetJobLogUserInfo(string lab_id, string? pgroup_id, string? user_name,string if_request);


        /// <summary>
        /// /获取文档对应人员学习记录
        /// </summary>
        /// <param name="lab_id"></param>
        /// <param name="firstmenukey"></param>
        /// <param name="doc_id"></param>
        /// <param name="start_time"></param>
        /// <param name="end_time"></param>
        /// <param name="pgroup_id"></param>
        /// <param name="user_name"></param>
        /// <param name="learning_state">0未完成1已完成</param>
        /// <param name="if_quest">0未勾选1勾选</param>
        /// <returns></returns>
        ResultDto GetUserJobLogInfo(string lab_id, string firstmenukey, string? doc_id, string start_time, string end_time, string? pgroup_id, string? user_name, string? learning_state, string? if_quest);
    }
}
