﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Entities.others
{
    [DBOwner("XH_SYS")]
    public class SYS6_SETUP
    {
        /// <summary>
        /// 设置ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Column(TypeName = "VARCHAR2(20)")]
        public string? SETUP_ID { get; set; }
        /// <summary>
        /// 设置编号
        /// </summary>
        [Column(TypeName = "VARCHAR2(20)")]
        public string? SETUP_NO { get; set; }
        /// <summary>
        /// 医疗机构ID
        /// </summary>
        [Column(TypeName = "VARCHAR2(20)")]
        public string? HOSPITAL_ID { get; set; }
        /// <summary>
        /// 单元ID
        /// </summary>
        [Column(TypeName = "VARCHAR2(20)")]
        public string? UNIT_ID { get; set; }
        /// <summary>
        /// 单元类型
        /// </summary>
        [Column(TypeName = "VARCHAR2(50)")]
        public string? UNIT_TYPE { get; set; }
        /// <summary>
        /// 设置值
        /// </summary>
        [Column(TypeName = "VARCHAR2(500)")]
        public string? SETUP_VALUE { get; set; }
        /// <summary>
        /// 首次登记人员
        /// </summary>
        [Column(TypeName = "VARCHAR2(50)")]
        public string? FIRST_RPERSON { get; set; }
        /// <summary>
        /// 首次登记时间
        /// </summary>
        public DateTime? FIRST_RTIME { get; set; }
        /// <summary>
        /// 最后修改人员
        /// </summary>
        [Column(TypeName = "VARCHAR2(50)")]
        public string? LAST_MPERSON { get; set; }
        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime? LAST_MTIME { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        [Column(TypeName = "VARCHAR2(200)")]
        public string? REMARK { get; set; }

        /// <summary>
        /// 包类型名称
        /// </summary>
        /// <returns></returns>
        public string GetUnitTypeSort()
        {
            return UNIT_TYPE switch
            {
                "5" => "0",  //单元级
                "10" => "1", //院区级
                "1" => "2",  //医疗机构级
                _ => "2"
            };
        }
    }
}
