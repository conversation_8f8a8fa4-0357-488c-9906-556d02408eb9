using H.Utility;
using XH.H81.Models.Dtos.Pms;
using XH.H81.Models.Entities.Pms;
using XH.H81.Models.ExternalEntity;

namespace XH.H81.IServices.Pms
{
    public interface IPageSettingService
    {
        /// <summary>
        /// 获取分类信息
        /// </summary>
        /// <param name="hospital_id"></param>
        /// <returns></returns>
        ResultDto GetClassInfo(string hospital_id);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="hospital_id"></param>
        /// <returns></returns>
        ResultDto GetClassTableInfo(string hospital_id);
        /// <summary>
        /// 保存分类信息
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="claims"></param>
        /// <returns></returns>
        ResultDto SaveClassInfo(SysSetUpInfoDto dto, H.Utility.ClaimsDto claims);

        /// <summary>
        /// 保存分类排序
        /// </summary>
        /// <param name="listDto"></param>
        /// <param name="claims"></param>
        /// <returns></returns>
        ResultDto SaveClassInfoSort(List<SysSetUpInfoDto> listDto, H.Utility.ClaimsDto claims);

        /// <summary>
        /// 保存分类属性排序
        /// </summary>
        /// <param name="listDto"></param>
        /// <returns></returns>
        public ResultDto SaveClassAttributeInfoSort(List<SysClassAttributeDto> listDto, H.Utility.ClaimsDto claims);

        /// <summary>
        /// 删除分类信息
        /// </summary>
        /// <param name="setUpId">主键id</param>
        /// <param name="claims"></param>
        /// <returns></returns>
        ResultDto DeleteClassInfo(string setUpId, H.Utility.ClaimsDto claims);

        /// <summary>
        /// 获取分类属性信息
        /// </summary>
        /// <param name="setUpId">分类主键</param>
        /// <returns></returns>
        ResultDto GetClassAttributeInfo(string setUpId, string hospitalId);

        /// <summary>
        /// 删除分类属性信息
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="claims"></param>
        /// <returns></returns>
        ResultDto SaveClassAttributeInfo(SysClassAttributeDto dto, H.Utility.ClaimsDto claims);

        /// <summary>
        /// 获取分类日期下拉
        /// </summary>
        /// <param name="classId"></param>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        ResultDto GetClassDateType(string classId, string hospitalId);
     
        ResultDto InsertPersonInfoSetting();

    }
}
