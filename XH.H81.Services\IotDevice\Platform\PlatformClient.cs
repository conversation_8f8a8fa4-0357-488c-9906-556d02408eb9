﻿using System.Diagnostics;
using System.Net.Security;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using H.Utility;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using RestSharp;
using Serilog;

namespace XH.H82.Models.BusinessModuleClient.Platform;

/// <summary>
/// 统一平台客户端
/// </summary>
public class PlatformClient
{
    private string address { get; set; }
    private IHttpContextAccessor _httpContext;

    public PlatformClient(string ip,  IHttpContextAccessor httpContext)
    {
        _httpContext = httpContext;
        address = ip;
    }

    public T1 ClientPost<T,T1>(string url, T requestBody = default(T), bool isNeedToken = true)
    {

        if (address.IsNullOrEmpty())
        {
            throw new BizException("address 为空");
        }

        using RestClient client = new RestClient(new RestClientOptions
        {
            RemoteCertificateValidationCallback = (object sender, X509Certificate? certificate, X509Chain? chain, SslPolicyErrors sslPolicyErrors) => true,
            BaseUrl = new Uri(address),
            ThrowOnAnyError = true
        });
        Stopwatch stopwatch = new Stopwatch();
        stopwatch.Start();
        RestRequest request = new RestRequest(url);
        if (requestBody != null)
        {
            request.AddBody(requestBody);
        }

        if (isNeedToken)
        {
            string token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();
            request.AddHeader("Authorization", token);
        }
        try
        {
            var restResponse = client.ExecutePost<string>(request);
            stopwatch.Stop();
            Log.ForContext("elapsed", stopwatch.ElapsedMilliseconds).Information($"调用S10模块[{url}],耗时:{stopwatch.ElapsedMilliseconds}ms");
            if (restResponse.IsSuccessful)
            {
                Log.Information($"调用S10模块[{url}]成功:{restResponse.Data}");
                return JsonConvert.DeserializeObject<T1>(restResponse.Data);
            }

            Log.Error($"调用S10模块[{url}]发生错误:{restResponse.ErrorException}");
            throw new BizException($"调用S10模块[{url}]发生错误:{restResponse.ErrorException}", restResponse.ErrorException);
        }
        catch (Exception ex)
        {
            Log.Error($"调用S10模块[{url}]发生错误:{ex}");
            throw new BizException(ex.Message);
        }
    }
    
    
    public async  Task<T1> ClientPostAsync<T,T1>(string url, T requestBody = default(T), bool isNeedToken = true)
    {

        if (address.IsNullOrEmpty())
        {
            throw new BizException("address 为空");
        }

        using RestClient client = new RestClient(new RestClientOptions
        {
            RemoteCertificateValidationCallback = (object sender, X509Certificate? certificate, X509Chain? chain, SslPolicyErrors sslPolicyErrors) => true,
            BaseUrl = new Uri(address),
            ThrowOnAnyError = true
        });
        Stopwatch stopwatch = new Stopwatch();
        stopwatch.Start();
        RestRequest request = new RestRequest(url);
        if (requestBody != null)
        {
            request.AddBody(requestBody);
        }

        if (isNeedToken)
        {
            string token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();
            request.AddHeader("Authorization", token);
        }
        try
        {
            var restResponse = await client.ExecutePostAsync<string>(request);
            stopwatch.Stop();
            Log.ForContext("elapsed", stopwatch.ElapsedMilliseconds).Information($"调用S10模块[{url}],耗时:{stopwatch.ElapsedMilliseconds}ms");
            if (restResponse.IsSuccessful)
            {
                Log.Information($"调用S10模块[{url}]成功:{restResponse.Data}");
                return JsonConvert.DeserializeObject<T1>(restResponse.Data);
            }

            Log.Error($"调用S10模块[{url}]发生错误:{restResponse.ErrorException}");
            throw new BizException($"调用S10模块[{url}]发生错误:{restResponse.ErrorException}", restResponse.ErrorException);
        }
        catch (Exception ex)
        {
            Log.Error($"调用S10模块[{url}]发生错误:{ex}");
            throw new BizException(ex.Message);
        }
    }

    
}