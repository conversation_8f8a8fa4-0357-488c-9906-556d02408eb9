﻿using System.Timers;
using H.BASE;
using H.Utility.Helper;
using Microsoft.Extensions.Caching.Memory;
using Serilog;
using XH.H81.IServices;
using Timer = System.Timers.Timer;
namespace XH.H81.API.ScheduleJobs;

/// <summary>
/// 门禁任务下发定时任务
/// </summary>
public class AccessControlSync : BackgroundService
{
    private IEguardControlService _eguardControlService;
    //private readonly IEasyCachingProviderFactory _cachingProvider;
    private Timer _timer;
    private readonly IConfiguration _configuration;
    const string lockName = "XH:CACHE:PMS.TASK.SYNC.H81";

    public AccessControlSync(IEguardControlService eguardControlService, IMemoryCache cache, IConfiguration configuration)
    {
        _eguardControlService = eguardControlService;
        _configuration = configuration;
        //_cachingProvider = cachingProvider;
        _timer = new Timer(1000 * 60 * 1)
        {
            AutoReset = true,
            Enabled = true
        };
    }



    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        if (AppSettingsProvider.SynergySign == "1") //仅连接的数据库为主协同库时才执行)
        {
            OnTimedEvent(null, null);
            _timer.Elapsed += OnTimedEvent;
            _timer.Start();
        }
    }
    private void OnTimedEvent(object? source, ElapsedEventArgs e)
    {
        Log.Information("门禁任务下发开始执行");
        Task.Factory.StartNew( async () =>
        {
            try
            {
                var countDate = DateTime.Now.Date.Add(TimeSpan.FromDays(-1));
                if (await GetTheLock())
                {
                    Log.Information($"{countDate.Date}开始执行下发门禁任务");
                    await _eguardControlService.CancelAccessControTasks();
                    await _eguardControlService.PerformAccessControTasks(countDate.Date);
                    Log.Information($"{countDate.Date}门禁任务下发结束");
                }
                else
                {
                    await GetTheLockFalse();
                }
            }
            catch (Exception ex)
            {
                Log.Error($"门禁任务下发开始失败：{ex}");
            }
        });
    }
    
    
    
    private async Task<bool> GetTheLock()
    {
        var minutes = "1";
            
        var time = TimeSpan.FromMinutes(int.Parse(minutes));

        var redis = XhRedisHelper.UseS03();

        var result = await redis.LockTakeAsync(lockName, $"AccessControlSync", time);

        return result;
    }

    private async Task GetTheLockFalse()
    {
        var redis = XhRedisHelper.UseS03();
        var serviceName = await redis.StringGetAsync(lockName);
        var ttl = redis.KeyExpireTime(lockName);
        Console.WriteLine($"已有服务正在执行，服务名：{serviceName},过期时间为{ttl?.ToString("yyyy-MM-dd HH:mm:ss")}。");
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        var redis = XhRedisHelper.UseS03();
        var isDeleted = await redis.KeyDeleteAsync(lockName);

        if (isDeleted)
        {
            Log.Information($"已有停止正在执行的定时任务，服务名：AccessControlSync");
        }
        else
        {
            var serviceName = await redis.StringGetAsync(lockName);
            var ttl = redis.KeyExpireTime(lockName);
            Console.WriteLine($"已有服务正在执行，服务名：{serviceName},过期时间为{ttl?.ToString("yyyy-MM-dd HH:mm:ss")}。");
        }

        _timer.Stop();
        await base.StopAsync(cancellationToken);
    }

    public void Dispose()
    {
        _timer.Dispose();
    }
}