﻿//using System;
//using Microsoft.EntityFrameworkCore;
//using System.ComponentModel.DataAnnotations;
//using System.ComponentModel.DataAnnotations.Schema;
//using SqlSugar;
//using H.Utility.SqlSugarInfra;

//namespace XH.H81.Models.Entitie
//{
//    /// <summary>
//    /// 实验室管理基础数据表
//    /// </summary>
//    [SugarTable("OA_BASE_DATA")]
//    [DBOwner("XH_OA")]
//    public class OA_BASE_DATA
//    {
//        /// <summary>
//        /// 基础数据ID
//        /// </summary>
//        [SugarColumn(IsPrimaryKey = true)]
//        [Required(ErrorMessage = "基础数据ID不允许为空")]
//        [StringLength(20, ErrorMessage = "基础数据ID长度不能超出20字符")]
//        public string DATA_ID { get; set; }

//        /// <summary>
//        /// 医疗机构ID
//        /// </summary>
//        [Required(ErrorMessage = "医疗机构ID不允许为空")]
//        [StringLength(20, ErrorMessage = "医疗机构ID长度不能超出20字符")]
//        public string HOSPITAL_ID { get; set; }

//        /// <summary>
//        /// 模块ID
//        /// </summary>
//        [Required(ErrorMessage = "模块ID不允许为空")]
//        [StringLength(20, ErrorMessage = "模块ID长度不能超出20字符")]
//        public string MODULE_ID { get; set; }

//        /// <summary>
//        /// 父级ID
//        /// </summary>
//        [StringLength(20, ErrorMessage = "父级ID长度不能超出20字符")]
//        public string? FATHER_ID { get; set; }

//        /// <summary>
//        /// 分类ID
//        /// </summary>
//        [StringLength(50, ErrorMessage = "分类ID长度不能超出50字符")]
//        public string? CLASS_ID { get; set; }

//        /// <summary>
//        /// 排序号
//        /// </summary>
//        public string? DATA_SORT { get; set; }

//        /// <summary>
//        /// 中文名
//        /// </summary>
//        [StringLength(100, ErrorMessage = "中文名长度不能超出100字符")]
//        public string? DATA_NAME { get; set; }

//        /// <summary>
//        /// 简称
//        /// </summary>
//        [StringLength(50, ErrorMessage = "简称长度不能超出50字符")]
//        public string? DATA_SNAME { get; set; }

//        /// <summary>
//        /// 英文名
//        /// </summary>
//        [StringLength(50, ErrorMessage = "英文名长度不能超出50字符")]
//        public string? DATA_ENAME { get; set; }

//        /// <summary>
//        /// 标准代码
//        /// </summary>
//        [StringLength(50, ErrorMessage = "标准代码长度不能超出50字符")]
//        public string? STANDART_ID { get; set; }

//        /// <summary>
//        /// 自定义码
//        /// </summary>
//        [StringLength(20, ErrorMessage = "自定义码长度不能超出20字符")]
//        public string? CUSTOM_CODE { get; set; }

//        /// <summary>
//        /// 拼音码
//        /// </summary>
//        [StringLength(20, ErrorMessage = "拼音码长度不能超出20字符")]
//        public string? SPELL_CODE { get; set; }

//        /// <summary>
//        /// 状态
//        /// </summary>
//        [StringLength(20, ErrorMessage = "状态长度不能超出20字符")]
//        public string? STATE_FLAG { get; set; }

//        /// <summary>
//        /// 首次登记人
//        /// </summary>
//        [StringLength(50, ErrorMessage = "首次登记人长度不能超出50字符")]
//        public string? FIRST_RPERSON { get; set; }

//        /// <summary>
//        /// 首次登记时间
//        /// </summary>
//        public DateTime? FIRST_RTIME { get; set; }

//        /// <summary>
//        /// 最后修改人员
//        /// </summary>
//        [StringLength(50, ErrorMessage = "最后修改人员长度不能超出50字符")]
//        public string? LAST_MPERSON { get; set; }

//        /// <summary>
//        /// 最后修改时间
//        /// </summary>
//        public DateTime? LAST_MTIME { get; set; }

//        /// <summary>
//        /// 备注
//        /// </summary>
//        [StringLength(200, ErrorMessage = "备注长度不能超出200字符")]
//        public string? REMARK { get; set; }

//    }
//}