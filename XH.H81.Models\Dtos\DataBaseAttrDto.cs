﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Dtos
{
    /// <summary>
    /// 数据库
    /// </summary>
    public class DataBaseAttrTableDto
    {
        /// <summary>
        /// 表名
        /// </summary>
        public string? TABLE_NAME { get; set; }
    }

    /// <summary>
    /// 数据库字段
    /// </summary>
    public class TabColumnsInfoDto
    {
        /// <summary>
        /// 表名
        /// </summary>
        public string? TABLE_NAME { get; set; }
        /// <summary>
        /// 列明
        /// </summary>
        public string? COLUMN_NAME { get; set; }
        /// <summary>
        /// 类型
        /// </summary>
        public string? DATA_TYPE { get; set; }
        /// <summary>
        /// 长度
        /// </summary>
        public string? DATA_LENGTH { get; set; }
        /// <summary>
        /// 是否可以为空（N,Y）
        /// </summary>
        public string? NULLABLE { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? COMMENTS { get; set; }
        /// <summary>
        /// 主键标识
        /// </summary>
        public string? CONSTRAINT_TYPE { get; set; }
    }
}
