﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static iTextSharp.text.pdf.AcroFields;

namespace XH.H81.Base.Helper
{
    public class CommonHelper
    {/// <summary>
     /// 根据出生日期，计算精确的年龄
     /// </summary>
     /// <param name="birthDate">生日</param>
     /// <returns></returns>
        public static int? CalculateAge(string birthDay)
        {
            if (DateTime.TryParse(birthDay, out DateTime birthDate))
            {
                DateTime nowDateTime = DateTime.Now;
                int age = nowDateTime.Year - birthDate.Year;
                //再考虑月、天的因素
                if (nowDateTime.Month < birthDate.Month || (nowDateTime.Month == birthDate.Month && nowDateTime.Day < birthDate.Day))
                {
                    age--;
                }
                return age;
            }
            else return null;
        }
        /// <summary>
        /// 时间转换 没有时分秒只显示天
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        public static string? FormatDateTime(object value)
        {
            if (value != null && DateTime.TryParse(value.ToString(), out DateTime dateTimeValue))
            {
                return dateTimeValue.TimeOfDay == TimeSpan.Zero ? dateTimeValue.ToShortDateString() : dateTimeValue.ToString("yyyy-MM-dd HH:mm:ss");
            }
            return null;
        }

    }
}
