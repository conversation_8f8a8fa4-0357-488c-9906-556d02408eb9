﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Dtos.Pms
{
    /// <summary>
    /// 分类属性DTO
    /// </summary>
    public class SysClassAttributeDto
    {
        /// <summary>
        /// 分类主键
        /// </summary>
        public string? SETUP_ID { get; set; }
        /// <summary>
        /// 分类属性名称
        /// </summary>
        public string? formName { get; set; }

        /// <summary>
        ///分类属性代码
        /// </summary>
        public string? formCode { get; set; }
        /// <summary>
        /// 是否显示
        /// </summary>
        public bool? ifShow { get; set; }

        /// <summary>
        /// 是否新增
        /// </summary>
        public bool? ifNew { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public int? sort { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string? remark { get; set; }

        /// <summary>
        /// 输入方式
        /// </summary>
        public string? dataType { get; set; }

        /// <summary>
        /// 数据分类
        /// </summary>
        public string? dataClass { get; set; }


        /// <summary>
        /// 是否删除
        /// </summary>
        public bool ifDelete { get; set; }

        /// <summary>
        /// 是否校验日期及时间过期
        /// </summary>
        public bool ifCheckExpired { get; set; }

        /// <summary>
        /// 是否清单统计
        /// </summary>
        public bool ifListStatistics { get; set; }

        /// <summary>
        /// 是否维护基础数据
        /// </summary>
        public bool ifBasicData { get; set; }

        /// <summary>
        /// 是否必填
        /// </summary>
        public bool? ifRequired { get; set; }


    }
}
