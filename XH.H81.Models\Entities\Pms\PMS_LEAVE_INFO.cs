﻿//using H.Utility.SqlSugarInfra;
//using SqlSugar;
//using System;
//using System.Collections.Generic;
//using System.ComponentModel.DataAnnotations;
//using System.Text;

//namespace XH.H81.Models.Entities.Pms
//{
//    [DBOwner("XH_OA")]
//    public class PMS_LEAVE_INFO
//    {
//        [Key]
//        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string LEAVE_ID { get; set; }
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? PGROUP_ID { get; set; }
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? HOSPITAL_ID { get; set; }
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? PERSON_ID { get; set; }
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? USER_ID { get; set; }
//        public DateTime LEAVE_DATE { get; set; }
//        public int LEAVE_WEEK { get; set; } = 0;
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? LEAVE_TYPE { get; set; }
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? LEAVE_CLASS { get; set; }
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? LEAVE_CAUSE { get; set; }
//        public DateTime LEAVE_START_TIME { get; set; }
//        public DateTime LEAVE_END_TIME { get; set; }
//        public int LEAVE_DURATION { get; set; } = 0;
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? LEAVE_DESC { get; set; }
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? CHECK_PERSON { get; set; }
//        public DateTime CHECK_TIME { get; set; }
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? CHECK_REFUSE_CAUSE { get; set; }
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? APPROVAL_PERSON { get; set; }
//        public DateTime APPROVAL_TIME { get; set; }
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? APPROVAL_REFUSE_CAUSE { get; set; }
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? LEAVE_STATE { get; set; }
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? FIRST_RPERSON { get; set; }
//        public DateTime FIRST_RTIME { get; set; }
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? LAST_MPERSON { get; set; }
//        public DateTime LAST_MTIME { get; set; }
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? REMARK { get; set; }

//    }
//}
