﻿using Newtonsoft.Json;

namespace XH.H81.Models.Dtos.IoTDevices;

public class IoTDevicesResult
{
    [JsonProperty("code")]
    public long Code { get; set; }

    [JsonProperty("data")]
    public IoTDevicesDto Data { get; set; }

    [JsonProperty("msg")]
    public string Msg { get; set; }
}



public class IoTDevicesResult<T>
{
    
    [JsonProperty("code")]
    public long Code { get; set; }

    [JsonProperty("data")]
    public T Data { get; set; }

    [JsonProperty("msg")]
    public string Msg { get; set; }
}