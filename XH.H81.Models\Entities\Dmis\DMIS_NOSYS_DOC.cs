using System;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using SqlSugar;
using H.Utility.SqlSugarInfra;

namespace XH.H81.Models.Entities.Dmis
{
    [DBOwner("XH_OA")]
    public class DMIS_NOSYS_DOC
    {
        /// <summary>
        /// 文档ID
        /// </summary>
        [Key]
        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        [Column("DOC_ID")]
        [Required(ErrorMessage = "文档ID不允许为空")]

        [StringLength(50, ErrorMessage = "文档ID长度不能超出50字符")]
        [Unicode(false)]
        public string DOC_ID { get; set; }

        /// <summary>
        /// 首次登记人
        /// </summary>
        [Column("FIRST_RPERSON")]
        [StringLength(50, ErrorMessage = "首次登记人长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FIRST_RPERSON { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("LAB_ID")]
        [Required(ErrorMessage = "不允许为空")]

        [StringLength(20, ErrorMessage = "LAB_ID长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string LAB_ID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("ISSUED_COMPUTER")]
        [StringLength(50, ErrorMessage = "ISSUED_COMPUTER长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? ISSUED_COMPUTER { get; set; }

        /// <summary>
        /// 执行日期
        /// </summary>
        [Column("EXECUTION_DATE")]
        [Unicode(false)]
        public DateTime? EXECUTION_DATE { get; set; }

        /// <summary>
        /// 核准人员
        /// </summary>
        [Column("CHECK_PERSON")]
        [StringLength(50, ErrorMessage = "核准人员长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CHECK_PERSON { get; set; }

        /// <summary>
        /// 放置场所
        /// </summary>
        [Column("DOC_PLACE")]
        [StringLength(50, ErrorMessage = "放置场所长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? DOC_PLACE { get; set; }

        /// <summary>
        /// 提交人
        /// </summary>
        [Column("SUBMIT_PERSON")]
        [StringLength(50, ErrorMessage = "提交人长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? SUBMIT_PERSON { get; set; }

        /// <summary>
        /// 学习时长 
        /// </summary>
        [Column("STUDY_DURATION")]
        [Unicode(false)]
        public decimal? STUDY_DURATION { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        [Column("LAST_MTIME")]
        [Unicode(false)]
        public DateTime? LAST_MTIME { get; set; }

        /// <summary>
        /// 文档类型
        /// </summary>
        [Column("DOC_TYPE")]
        [StringLength(50, ErrorMessage = "文档类型长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? DOC_TYPE { get; set; }


        /// <summary>
        /// 起草日期
        /// </summary>
        [Column("DRAFTERS_DATE")]
        [Unicode(false)]
        public DateTime? DRAFTERS_DATE { get; set; }

        /// <summary>
        /// "文档流程状态  1未审核2已提交3已核准"
        /// </summary>
        [Column("DOC_PROCCESS_STATE")]
        [StringLength(20, ErrorMessage = "文档流程状态  1未审核2已提交3已核准4废止提交5废止审核长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? DOC_PROCCESS_STATE { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("ISSUED_NO")]
        [StringLength(50, ErrorMessage = "ISSUED_NO长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? ISSUED_NO { get; set; }

        /// <summary>
        /// 文档标题
        /// </summary>
        [Column("DOC_TITLE")]
        [StringLength(100, ErrorMessage = "文档标题长度不能超出100字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? DOC_TITLE { get; set; }

        /// <summary>
        /// 文档名称
        /// </summary>
        [Column("DOC_NAME")]
        [StringLength(100, ErrorMessage = "文档名称长度不能超出100字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? DOC_NAME { get; set; }

        /// <summary>
        /// "文档状态 1在用 0禁用"
        /// </summary>
        [Column("DOC_STATE")]
        [StringLength(20, ErrorMessage = "文档状态 1在用 0禁用长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? DOC_STATE { get; set; }

        /// <summary>
        /// 文档描述
        /// </summary>
        [Column("DOC_DESC")]
        [StringLength(200, ErrorMessage = "文档描述长度不能超出200字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? DOC_DESC { get; set; }

        /// <summary>
        /// 保管日期
        /// </summary>
        [Column("CUSTODY_DATE")]
        [Unicode(false)]
        public DateTime? CUSTODY_DATE { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("ISSUED_TIME")]
        [Unicode(false)]
        public DateTime? ISSUED_TIME { get; set; }

        /// <summary>
        /// 文档排序号
        /// </summary>
        [Column("DOC_SORT")]
        [StringLength(20, ErrorMessage = "文档排序号长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? DOC_SORT { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("DOC_KEYWORD")]
        [StringLength(50, ErrorMessage = "DOC_KEYWORD长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? DOC_KEYWORD { get; set; }

        /// <summary>
        /// 分类ID
        /// </summary>
        [Column("CLASS_ID")]
        [Required(ErrorMessage = "分类ID不允许为空")]

        [StringLength(20, ErrorMessage = "分类ID长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string CLASS_ID { get; set; }

        /// <summary>
        /// 起草者
        /// </summary>
        [Column("DOC_DRAFTERS")]
        [StringLength(50, ErrorMessage = "起草者长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? DOC_DRAFTERS { get; set; }

        /// <summary>
        /// 保管者
        /// </summary>
        [Column("CUSTODY_PERSON")]
        [StringLength(50, ErrorMessage = "保管者长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CUSTODY_PERSON { get; set; }

        /// <summary>
        /// 文档编号
        /// </summary>
        [Column("DOC_CODE")]
        [StringLength(50, ErrorMessage = "文档编号长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? DOC_CODE { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Column("REMARK")]
        [StringLength(200, ErrorMessage = "备注长度不能超出200字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? REMARK { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("ISSUED_PERSON")]
        [StringLength(50, ErrorMessage = "ISSUED_PERSON长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? ISSUED_PERSON { get; set; }

        /// <summary>
        /// 提交时间
        /// </summary>
        [Column("SUBMIT_TIME")]
        [Unicode(false)]
        public DateTime? SUBMIT_TIME { get; set; }

        /// <summary>
        /// 提交电脑
        /// </summary>
        [Column("SUBMIT_COMPUTER")]
        [StringLength(50, ErrorMessage = "提交电脑长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? SUBMIT_COMPUTER { get; set; }

        /// <summary>
        /// 核准日期
        /// </summary>
        [Column("CHECK_TIME")]
        [Unicode(false)]
        public DateTime? CHECK_TIME { get; set; }

        /// <summary>
        /// 文档标签
        /// </summary>
        [Column("DOC_LABEL")]
        [StringLength(50, ErrorMessage = "文档标签长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? DOC_LABEL { get; set; }

        /// <summary>
        /// 首次登记时间
        /// </summary>
        [Column("FIRST_RTIME")]
        [Unicode(false)]
        public DateTime? FIRST_RTIME { get; set; }

        /// <summary>
        /// 最后修改人员
        /// </summary>
        [Column("LAST_MPERSON")]
        [StringLength(50, ErrorMessage = "最后修改人员长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? LAST_MPERSON { get; set; }

        /// <summary>
        /// 文档版本号
        /// </summary>
        [Column("DOC_VERSION")]
        [StringLength(50, ErrorMessage = "文档版本号长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? DOC_VERSION { get; set; }

        /// <summary>
        /// 文档份数
        /// </summary>
        [Column("DOC_COPIES")]
        [Unicode(false)]
        public decimal? DOC_COPIES { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Column("PGROUP_ID")]
        [StringLength(20, ErrorMessage = "PGROUP_ID长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? PGROUP_ID { get; set; }

        /// <summary>
        /// 医疗机构ID
        /// </summary>
        [Column("HOSPITAL_ID")]
        [Required(ErrorMessage = "医疗机构ID不允许为空")]

        [StringLength(20, ErrorMessage = "医疗机构ID长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string HOSPITAL_ID { get; set; }

        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? ABOLISH_SUBMIT_PERSON { get; set; }//废止提交人
        public DateTime? ABOLISH_SUBMIT_TIME { get; set; }//废止提交时间
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? ABOLISH_CHECK_PERSON { get; set; }//废止审核人
        public DateTime? ABOLISH_CHECK_TIME { get; set; }//废止审核时间
        /// <summary>
        /// 核准电脑
        /// </summary>
        [Column("CHECK_COMPUTER")]
        [StringLength(50, ErrorMessage = "核准电脑长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CHECK_COMPUTER { get; set; }


        /// <summary>
        /// 出版者
        /// </summary>
        [Column("PUBLISH_RPERSON")]
        [StringLength(20, ErrorMessage = "出版者长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? PUBLISH_RPERSON { get; set; }



        /// <summary>
        /// 出版时间
        /// </summary>
        public DateTime? PUBLISH_TIME { get; set; }

        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? PUBLISH_RPERSON_NAME { get; set; }

        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]

        public string? CHECK_PERSON_NAME { get; set; }

        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? SUBMIT_PERSON_NAME { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string DOC_TYPE_NAME { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? DOC_PROCCESS_STATE_NAME { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? DOC_STATE_NAME { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string CLASS_NAME { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? DOC_DRAFTERS_NAME { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? LAB_NAME { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? PGROUP_NAME { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? ISSUED_PERSON_NAME { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FILE_TYPE { get; set; }

        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FILE_NAME { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FILE_SUFFIX { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? COLLECT_ID { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public int? IF_REVISE { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? REJECT_REASON { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? REJECT_PERSON { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? REJECT_TIME { get; set; }

        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? REJECT_REVOCATION { get; set; }

        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FILE_ORIGIN { get; set; }

        public string SMBL_FLAG { get; set; }

    }
}
