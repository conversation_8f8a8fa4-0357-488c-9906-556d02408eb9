using H.Utility;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using XH.H81.API.Extensions;
using XH.H81.IServices;
using XH.H81.IServices.Pms;
using XH.H81.Models.Dtos.Pms;
using XH.H81.Models.Entities.Pms;

namespace XH.H81.API.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize]
    public class PageSettingController : ControllerBase
    {
        private IPageSettingService _IPageSettingService;
        private readonly IBaseDataServices _IBaseDataService;
        public PageSettingController(IPageSettingService iPageSettingService, IBaseDataServices ibasedataService)
        {
            _IPageSettingService = iPageSettingService;
            _IBaseDataService = ibasedataService;
        }

        /// <summary>
        /// 获取分类信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [CustomResponseType(typeof(List<SysSetUpInfoDto>))]
        public IActionResult GetClassInfo()
        {
            var claim = this.User.ToClaimsDto();
            var Result = _IPageSettingService.GetClassInfo(claim.HOSPITAL_ID);
            return Ok(Result);
        }

        /// <summary>
        /// 获取分类表格信息 
        /// </summary>
        /// <returns></returns>
        [HttpGet]
       
         [CustomResponseType(typeof(List<SysSetUpInfoDto>))]
         public IActionResult GetClassTableInfo()
        {
            var claim = this.User.ToClaimsDto();
            var Result = _IPageSettingService.GetClassTableInfo(claim.HOSPITAL_ID);
            return Ok(Result);
        }
        /// <summary>
        /// 获取分类属性
        /// </summary>
        /// <param name="setUpId">分类主键id</param>
        /// <returns></returns>
        [HttpGet]
        [CustomResponseType(typeof(List<SysClassAttributeDto>))]
        public IActionResult GetClassAttributeInfo(string setUpId)
        {
            var claim = this.User.ToClaimsDto();
            var Result = _IPageSettingService.GetClassAttributeInfo(setUpId, claim.HOSPITAL_ID);
            return Ok(Result);
        }
        /// <summary>
        /// 保存分类信息
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult SaveClassInfo(SysSetUpInfoDto dto)
        {
            var claim = this.User.ToClaimsDto();
            var Result = _IPageSettingService.SaveClassInfo(dto, claim);
            return Ok(Result);
        }
        /// <summary>
        /// 删除分类信息
        /// </summary>
        /// <param name="setUpId">主键id</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult DeleteClassInfo(string setUpId)
        {
            var claim = this.User.ToClaimsDto();
            var Result = _IPageSettingService.DeleteClassInfo(setUpId, claim);
            return Ok(Result);
        }

        /// <summary>
        /// 保存分类排序
        /// </summary>
        /// <param name="listDto">分类集合</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult SaveClassInfoSort(List<SysSetUpInfoDto> listDto)
        {
            var claim = this.User.ToClaimsDto();
            var Result = _IPageSettingService.SaveClassInfoSort(listDto, claim);
            return Ok(Result);
        }


        /// <summary>
        /// 保存分类属性排序
        /// </summary>
        /// <param name="listDto">分类属性集合</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult SaveClassAttributeInfoSort(List<SysClassAttributeDto> listDto)
        {
            var claim = this.User.ToClaimsDto();
            var Result = _IPageSettingService.SaveClassAttributeInfoSort(listDto, claim);
            return Ok(Result);
        }
        /// <summary>
        /// 保存分类属性信息
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult SaveClassAttributeInfo(SysClassAttributeDto dto)
        {
            var claim = this.User.ToClaimsDto();
            var Result = _IPageSettingService.SaveClassAttributeInfo(dto, claim);
            return Ok(Result);
        }

        /// <summary>
        /// 获取分类日期类型下拉
        /// </summary>
        /// <param name="classId"></param>
        /// <returns></returns>
        [HttpGet]

        [CustomResponseType(typeof(List<CommboxInfo>))]
        public IActionResult GetClassDateType(string classId, string? hospitalId)
        {
            if (hospitalId.IsNullOrEmpty())
                hospitalId = this.User.ToClaimsDto().HOSPITAL_ID;
            var Result = _IPageSettingService.GetClassDateType(classId, hospitalId);
            return Ok(Result);
        }



        [HttpGet]
        public IActionResult InsertPersonInfoSetting()
        {
            var claim = this.User.ToClaimsDto();
            var Result = _IPageSettingService.InsertPersonInfoSetting();
            return Ok(Result);
        }
    }
}
