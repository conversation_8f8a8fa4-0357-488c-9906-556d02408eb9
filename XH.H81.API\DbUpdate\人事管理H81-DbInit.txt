create table XH_OA.PMS_CHANGE_LOG
(
    LOG_ID                VARCHAR2(20) not null
        constraint PK_LOG_ID
            primary key,
    HOSPITAL_ID           VARCHAR2(20) not null,
    MODULE_ID             VARCHAR2(20) not null,
    CHAN<PERSON>_DATE           DATE         not null,
    CHANGE_TYPE           VARCHAR2(50) not null,
    CHANGE_TABLE          VARCHAR2(50) not null,
    FIELD_ID              VARCHAR2(50),
    FIELD_NAME            VARCHAR2(50),
    CHANAGE_MODULE        VARCHAR2(50),
    CAUSE_CLASS           VARCHAR2(50),
    CHANGE_CAUSE          VARCHAR2(500),
    CHANGE_BEFORE_CONTENT VARCHAR2(500),
    CH<PERSON><PERSON>_AFTER_CONTENT  VARCHAR2(500),
    REQ_TIME              DATE,
    REQ_PERSON            VARCHAR2(50),
    CHECK_TIME            DATE,
    CHECK_PERSON          VARCHAR2(50),
    CHANGE_STATE          VARCHAR2(50),
    <PERSON>ANGE_PERSON         VARCHAR2(50),
    <PERSON>ANGE_TIME           VARCHAR2(50),
    CHANGE_COMPUTER       VARCHAR2(50),
    <PERSON><PERSON><PERSON><PERSON>                VARCHAR2(200),
    PERSON_ID             VARCHAR2(20) not null,
    OPERATE_TYPE          VARCHAR2(20)
)
/

comment on column XH_OA.PMS_CHANGE_LOG.LOG_ID is '记录ID'
/

comment on column XH_OA.PMS_CHANGE_LOG.HOSPITAL_ID is '医疗机构ID'
/

comment on column XH_OA.PMS_CHANGE_LOG.MODULE_ID is '模块ID'
/

comment on column XH_OA.PMS_CHANGE_LOG.CHANGE_DATE is '修改日期'
/

comment on column XH_OA.PMS_CHANGE_LOG.CHANGE_TYPE is '修改类型'
/

comment on column XH_OA.PMS_CHANGE_LOG.CHANGE_TABLE is '数据表'
/

comment on column XH_OA.PMS_CHANGE_LOG.FIELD_ID is '修改字段ID'
/

comment on column XH_OA.PMS_CHANGE_LOG.FIELD_NAME is '修改字段名称'
/

comment on column XH_OA.PMS_CHANGE_LOG.CHANAGE_MODULE is '修改模块'
/

comment on column XH_OA.PMS_CHANGE_LOG.CAUSE_CLASS is '原因分类'
/

comment on column XH_OA.PMS_CHANGE_LOG.CHANGE_CAUSE is '修改原因'
/

comment on column XH_OA.PMS_CHANGE_LOG.CHANGE_BEFORE_CONTENT is '修改前内容'
/

comment on column XH_OA.PMS_CHANGE_LOG.CHANGE_AFTER_CONTENT is '修改后内容'
/

comment on column XH_OA.PMS_CHANGE_LOG.REQ_TIME is '申请时间'
/

comment on column XH_OA.PMS_CHANGE_LOG.REQ_PERSON is '申请人员'
/

comment on column XH_OA.PMS_CHANGE_LOG.CHECK_TIME is '审核时间'
/

comment on column XH_OA.PMS_CHANGE_LOG.CHECK_PERSON is '审核人员'
/

comment on column XH_OA.PMS_CHANGE_LOG.CHANGE_STATE is '修改状态'
/

comment on column XH_OA.PMS_CHANGE_LOG.CHANGE_PERSON is '修改人'
/

comment on column XH_OA.PMS_CHANGE_LOG.CHANGE_TIME is '修改时间'
/

comment on column XH_OA.PMS_CHANGE_LOG.CHANGE_COMPUTER is '修改电脑'
/

comment on column XH_OA.PMS_CHANGE_LOG.REMARK is '备注'
/

comment on column XH_OA.PMS_CHANGE_LOG.PERSON_ID is '对应人员ID'
/

comment on column XH_OA.PMS_CHANGE_LOG.OPERATE_TYPE is '操作类型'
/

create table XH_OA.PMS_EDUCATION_LIST
(
    EDUCATION_ID         VARCHAR2(20) not null
        constraint PK_EDUCATION_ID
            primary key,
    PERSON_ID            VARCHAR2(20) not null,
    EDUCATION_SORT       VARCHAR2(20) not null,
    EDUCATION_BACKGROUND VARCHAR2(50),
    EDUCATION_DEGREE     VARCHAR2(50),
    DEGREE_TYPE          VARCHAR2(20),
    ENROLLMENT_TIME      VARCHAR2(20),
    GRADUATE_TIME        VARCHAR2(20),
    EDUCATION_SCHOOL     VARCHAR2(100),
    RESEARCH_DIRECTION   VARCHAR2(200),
    EDUCATION_PROFESSION VARCHAR2(100),
    GRADE_RANK           VARCHAR2(20),
    EDUCATION_ADVISOR    VARCHAR2(20),
    ACADEMIC_PROPERTY    VARCHAR2(20),
    STUDY_FORM           VARCHAR2(20),
    EDUCATION_AFFIX      VARCHAR2(100),
    FIRST_RPERSON        VARCHAR2(50),
    FIRST_RTIME          DATE,
    LAST_MPERSON         VARCHAR2(50),
    LAST_MTIME           DATE,
    REMARK               VARCHAR2(200),
    SELECT_CHECK_PERSON  VARCHAR2(50),
    CHECK_TIME           DATE,
    CHECK_PERSON         VARCHAR2(50),
    CHECK_STATE          VARCHAR2(20),
    EDUCATION_STATE      VARCHAR2(20),
    HOSPITAL_ID          VARCHAR2(20),
    EDUCATION_DURATION   NUMBER
)
/

comment on column XH_OA.PMS_EDUCATION_LIST.EDUCATION_ID is '教育记录ID'
/

comment on column XH_OA.PMS_EDUCATION_LIST.PERSON_ID is '人员ID'
/

comment on column XH_OA.PMS_EDUCATION_LIST.EDUCATION_SORT is '排序号'
/

comment on column XH_OA.PMS_EDUCATION_LIST.EDUCATION_BACKGROUND is '学历'
/

comment on column XH_OA.PMS_EDUCATION_LIST.EDUCATION_DEGREE is '学位'
/

comment on column XH_OA.PMS_EDUCATION_LIST.DEGREE_TYPE is '学位类型'
/

comment on column XH_OA.PMS_EDUCATION_LIST.ENROLLMENT_TIME is '入学时间'
/

comment on column XH_OA.PMS_EDUCATION_LIST.GRADUATE_TIME is '毕业时间'
/

comment on column XH_OA.PMS_EDUCATION_LIST.EDUCATION_SCHOOL is '所在院校'
/

comment on column XH_OA.PMS_EDUCATION_LIST.RESEARCH_DIRECTION is '研究方向'
/

comment on column XH_OA.PMS_EDUCATION_LIST.EDUCATION_PROFESSION is '所学专业'
/

comment on column XH_OA.PMS_EDUCATION_LIST.GRADE_RANK is '成绩排名'
/

comment on column XH_OA.PMS_EDUCATION_LIST.EDUCATION_ADVISOR is '导师'
/

comment on column XH_OA.PMS_EDUCATION_LIST.ACADEMIC_PROPERTY is '学历性质'
/

comment on column XH_OA.PMS_EDUCATION_LIST.STUDY_FORM is '学习形式'
/

comment on column XH_OA.PMS_EDUCATION_LIST.EDUCATION_AFFIX is '附件'
/

comment on column XH_OA.PMS_EDUCATION_LIST.FIRST_RPERSON is '首次登记人'
/

comment on column XH_OA.PMS_EDUCATION_LIST.FIRST_RTIME is '首次登记时间'
/

comment on column XH_OA.PMS_EDUCATION_LIST.LAST_MPERSON is '首次登记时间'
/

comment on column XH_OA.PMS_EDUCATION_LIST.LAST_MTIME is '最后修改时间'
/

comment on column XH_OA.PMS_EDUCATION_LIST.REMARK is '备注'
/

comment on column XH_OA.PMS_EDUCATION_LIST.SELECT_CHECK_PERSON is '指定审核人员'
/

comment on column XH_OA.PMS_EDUCATION_LIST.CHECK_TIME is '审核时间'
/

comment on column XH_OA.PMS_EDUCATION_LIST.CHECK_PERSON is '审核人员'
/

comment on column XH_OA.PMS_EDUCATION_LIST.CHECK_STATE is '审核状态'
/

comment on column XH_OA.PMS_EDUCATION_LIST.EDUCATION_STATE is '状态（0禁用
1在用
）'
/

comment on column XH_OA.PMS_EDUCATION_LIST.HOSPITAL_ID is '医疗机构ID'
/

comment on column XH_OA.PMS_EDUCATION_LIST.EDUCATION_DURATION is '在校时长'
/

create table XH_OA.PMS_EXCHANGE_LIST
(
    EXCHANGE_ID         VARCHAR2(20) not null
        constraint PK_EXCHANGE_ID
            primary key,
    PERSON_ID           VARCHAR2(20) not null,
    EXCHANGE_SORT       VARCHAR2(20) not null,
    EXCHANGE_UNIT       VARCHAR2(100),
    EXCHANGE_GOAL       VARCHAR2(1000),
    EXCHANGE_CONTENT    VARCHAR2(1000),
    EXCHANGE_TIME       VARCHAR2(50),
    EXCHANGE_LEVEL      VARCHAR2(20),
    EXCHANGE_PERSON     VARCHAR2(100),
    EXCHANGE_AFFIX      VARCHAR2(100),
    SELECT_CHECK_PERSON VARCHAR2(50),
    CHECK_TIME          DATE,
    CHECK_PERSON        VARCHAR2(50),
    FIRST_RPERSON       VARCHAR2(50),
    FIRST_RTIME         DATE,
    LAST_MPERSON        VARCHAR2(50),
    LAST_MTIME          DATE,
    REMARK              VARCHAR2(200),
    CHECK_STATE         VARCHAR2(20),
    EXCHANGE_START_DATE DATE,
    EXCHANGE_END_DATE   DATE,
    EXCHANGE_STATE      VARCHAR2(20),
    HOSPITAL_ID         VARCHAR2(20)
)
/

comment on column XH_OA.PMS_EXCHANGE_LIST.EXCHANGE_ID is '交流记录ID'
/

comment on column XH_OA.PMS_EXCHANGE_LIST.PERSON_ID is '人员ID'
/

comment on column XH_OA.PMS_EXCHANGE_LIST.EXCHANGE_SORT is '排序号'
/

comment on column XH_OA.PMS_EXCHANGE_LIST.EXCHANGE_UNIT is '交流单位'
/

comment on column XH_OA.PMS_EXCHANGE_LIST.EXCHANGE_GOAL is '交流目的'
/

comment on column XH_OA.PMS_EXCHANGE_LIST.EXCHANGE_CONTENT is '交流内容'
/

comment on column XH_OA.PMS_EXCHANGE_LIST.EXCHANGE_TIME is '交流时间'
/

comment on column XH_OA.PMS_EXCHANGE_LIST.EXCHANGE_LEVEL is '交流级别'
/

comment on column XH_OA.PMS_EXCHANGE_LIST.EXCHANGE_PERSON is '交流参加人员'
/

comment on column XH_OA.PMS_EXCHANGE_LIST.EXCHANGE_AFFIX is '附件'
/

comment on column XH_OA.PMS_EXCHANGE_LIST.SELECT_CHECK_PERSON is '指定审核人员'
/

comment on column XH_OA.PMS_EXCHANGE_LIST.CHECK_TIME is '审核时间'
/

comment on column XH_OA.PMS_EXCHANGE_LIST.CHECK_PERSON is '审核人员'
/

comment on column XH_OA.PMS_EXCHANGE_LIST.FIRST_RPERSON is '首次登记人'
/

comment on column XH_OA.PMS_EXCHANGE_LIST.FIRST_RTIME is '首次登记时间'
/

comment on column XH_OA.PMS_EXCHANGE_LIST.LAST_MPERSON is '最后修改人员'
/

comment on column XH_OA.PMS_EXCHANGE_LIST.LAST_MTIME is '最后修改时间'
/

comment on column XH_OA.PMS_EXCHANGE_LIST.REMARK is '备注'
/

comment on column XH_OA.PMS_EXCHANGE_LIST.CHECK_STATE is '审核状态'
/

comment on column XH_OA.PMS_EXCHANGE_LIST.EXCHANGE_START_DATE is '交流开始日期'
/

comment on column XH_OA.PMS_EXCHANGE_LIST.EXCHANGE_END_DATE is '交流结束日期'
/

comment on column XH_OA.PMS_EXCHANGE_LIST.EXCHANGE_STATE is '状态（0禁用
1在用
）'
/

comment on column XH_OA.PMS_EXCHANGE_LIST.HOSPITAL_ID is '医疗机构ID'
/

create table XH_OA.PMS_EXPATRIATE_LIST
(
    EXPATRIATE_ID         VARCHAR2(20) not null
        constraint PK_EXPATRIATE_ID
            primary key,
    HOSPITAL_ID           VARCHAR2(20) not null,
    PERSON_ID             VARCHAR2(20) not null,
    EXPATRIATE_SORT       VARCHAR2(20) not null,
    EXPATRIATE_UNIT       VARCHAR2(100),
    EXPATRIATE            VARCHAR2(50),
    EXPATRIATE_PERIOD     VARCHAR2(50),
    EXPATRIATE_START_DATE VARCHAR2(50),
    EXPATRIATE_END_DATE   VARCHAR2(50),
    IF_FINISH             VARCHAR2(10),
    EXPATRIATE_AFFIX      VARCHAR2(100),
    SELECT_CHECK_PERSON   VARCHAR2(50),
    CHECK_TIME            DATE,
    CHECK_PERSON          VARCHAR2(50),
    FIRST_RPERSON         VARCHAR2(50),
    FIRST_RTIME           DATE,
    LAST_MPERSON          VARCHAR2(50),
    LAST_MTIME            DATE,
    REMARK                VARCHAR2(200),
    CHECK_STATE           VARCHAR2(20),
    EXPATRIATE_STATE      VARCHAR2(20)
)
/

comment on column XH_OA.PMS_EXPATRIATE_LIST.EXPATRIATE_ID is '记录ID'
/

comment on column XH_OA.PMS_EXPATRIATE_LIST.HOSPITAL_ID is '医疗机构ID'
/

comment on column XH_OA.PMS_EXPATRIATE_LIST.PERSON_ID is '人员ID'
/

comment on column XH_OA.PMS_EXPATRIATE_LIST.EXPATRIATE_SORT is '排序号'
/

comment on column XH_OA.PMS_EXPATRIATE_LIST.EXPATRIATE_UNIT is '外派单位'
/

comment on column XH_OA.PMS_EXPATRIATE_LIST.EXPATRIATE is '外派性质'
/

comment on column XH_OA.PMS_EXPATRIATE_LIST.EXPATRIATE_PERIOD is '外派周期'
/

comment on column XH_OA.PMS_EXPATRIATE_LIST.EXPATRIATE_START_DATE is '起始日期'
/

comment on column XH_OA.PMS_EXPATRIATE_LIST.EXPATRIATE_END_DATE is '结束日期'
/

comment on column XH_OA.PMS_EXPATRIATE_LIST.IF_FINISH is '是否完成'
/

comment on column XH_OA.PMS_EXPATRIATE_LIST.EXPATRIATE_AFFIX is '附件'
/

comment on column XH_OA.PMS_EXPATRIATE_LIST.SELECT_CHECK_PERSON is '指定审核人员'
/

comment on column XH_OA.PMS_EXPATRIATE_LIST.CHECK_TIME is '审核时间'
/

comment on column XH_OA.PMS_EXPATRIATE_LIST.CHECK_PERSON is '审核人员'
/

comment on column XH_OA.PMS_EXPATRIATE_LIST.FIRST_RPERSON is '首次登记人'
/

comment on column XH_OA.PMS_EXPATRIATE_LIST.FIRST_RTIME is '首次登记时间'
/

comment on column XH_OA.PMS_EXPATRIATE_LIST.LAST_MPERSON is '最后修改人员'
/

comment on column XH_OA.PMS_EXPATRIATE_LIST.LAST_MTIME is '最后修改时间'
/

comment on column XH_OA.PMS_EXPATRIATE_LIST.REMARK is '备注'
/

comment on column XH_OA.PMS_EXPATRIATE_LIST.CHECK_STATE is '审核状态'
/

comment on column XH_OA.PMS_EXPATRIATE_LIST.EXPATRIATE_STATE is '状态（0禁用
1在用
）'
/

create table XH_OA.PMS_INTELLECTUAL_LIST
(
    INTELLECTUAL_ID     VARCHAR2(20) not null
        constraint PK_INTELLECTUAL_ID
            primary key,
    PERSON_ID           VARCHAR2(20) not null,
    INTELLECTUAL_SORT   VARCHAR2(20),
    INTELLECTUAL_CLASS  VARCHAR2(20),
    INTELLECTUAL_NAME   VARCHAR2(200),
    INTELLECTUAL_DATE   VARCHAR2(50),
    INTELLECTUAL_AFFIX  VARCHAR2(100),
    FIRST_RPERSON       VARCHAR2(50),
    FIRST_RTIME         DATE,
    LAST_MPERSON        VARCHAR2(50),
    LAST_MTIME          DATE,
    REMARK              VARCHAR2(200),
    SELECT_CHECK_PERSON VARCHAR2(50),
    CHECK_TIME          DATE,
    CHECK_PERSON        VARCHAR2(50),
    CHECK_STATE         VARCHAR2(20),
    REGISTER_NO         VARCHAR2(50),
    CERTIFICATE_NO      VARCHAR2(50),
    INTELLECTUAL_STATE  VARCHAR2(20),
    HOSPITAL_ID         VARCHAR2(20)
)
/

comment on column XH_OA.PMS_INTELLECTUAL_LIST.INTELLECTUAL_ID is '知识产权记录ID'
/

comment on column XH_OA.PMS_INTELLECTUAL_LIST.PERSON_ID is '人员ID'
/

comment on column XH_OA.PMS_INTELLECTUAL_LIST.INTELLECTUAL_SORT is '知识产权排序'
/

comment on column XH_OA.PMS_INTELLECTUAL_LIST.INTELLECTUAL_CLASS is '知识产权分类'
/

comment on column XH_OA.PMS_INTELLECTUAL_LIST.INTELLECTUAL_NAME is '知识产权名称'
/

comment on column XH_OA.PMS_INTELLECTUAL_LIST.INTELLECTUAL_DATE is '知识产权获得日期'
/

comment on column XH_OA.PMS_INTELLECTUAL_LIST.INTELLECTUAL_AFFIX is '附件'
/

comment on column XH_OA.PMS_INTELLECTUAL_LIST.FIRST_RPERSON is '首次登记人'
/

comment on column XH_OA.PMS_INTELLECTUAL_LIST.FIRST_RTIME is '首次登记时间'
/

comment on column XH_OA.PMS_INTELLECTUAL_LIST.LAST_MPERSON is '最后修改人员'
/

comment on column XH_OA.PMS_INTELLECTUAL_LIST.LAST_MTIME is '最后修改时间'
/

comment on column XH_OA.PMS_INTELLECTUAL_LIST.REMARK is '备注'
/

comment on column XH_OA.PMS_INTELLECTUAL_LIST.SELECT_CHECK_PERSON is '指定审核人员'
/

comment on column XH_OA.PMS_INTELLECTUAL_LIST.CHECK_TIME is '审核时间'
/

comment on column XH_OA.PMS_INTELLECTUAL_LIST.CHECK_PERSON is '审核人员'
/

comment on column XH_OA.PMS_INTELLECTUAL_LIST.CHECK_STATE is '审核状态'
/

comment on column XH_OA.PMS_INTELLECTUAL_LIST.REGISTER_NO is '登记号'
/

comment on column XH_OA.PMS_INTELLECTUAL_LIST.CERTIFICATE_NO is '证书号'
/

comment on column XH_OA.PMS_INTELLECTUAL_LIST.INTELLECTUAL_STATE is '状态（0禁用
1在用
）'
/

comment on column XH_OA.PMS_INTELLECTUAL_LIST.HOSPITAL_ID is '医疗机构ID'
/

create table XH_OA.PMS_PERSON_FILE
(
    FILE_ID       VARCHAR2(20) not null
        constraint PK_FILE_ID
            primary key,
    PERSON_ID     VARCHAR2(20) not null,
    HOSPITAL_ID   VARCHAR2(20) not null,
    FILE_CLASS    VARCHAR2(50) not null,
    FILE_NUM      VARCHAR2(50),
    FILE_CNAME    VARCHAR2(100),
    FILE_SORT     VARCHAR2(20),
    FILE_NAME     VARCHAR2(100),
    FILE_PATH     VARCHAR2(500),
    FILE_SUFFIX   VARCHAR2(20),
    FILE_TYPE     VARCHAR2(20),
    FILE_STATE    VARCHAR2(20),
    FIRST_RPERSON VARCHAR2(50),
    FIRST_RTIME   VARCHAR2(50),
    LAST_MPERSON  VARCHAR2(50),
    LAST_MTIME    VARCHAR2(50),
    REMARK        VARCHAR2(200)
)
/

comment on column XH_OA.PMS_PERSON_FILE.FILE_ID is '文件ID'
/

comment on column XH_OA.PMS_PERSON_FILE.PERSON_ID is '人员ID'
/

comment on column XH_OA.PMS_PERSON_FILE.HOSPITAL_ID is '医疗机构ID'
/

comment on column XH_OA.PMS_PERSON_FILE.FILE_CLASS is '文件分类'
/

comment on column XH_OA.PMS_PERSON_FILE.FILE_NUM is '文件编号'
/

comment on column XH_OA.PMS_PERSON_FILE.FILE_CNAME is '文件别名'
/

comment on column XH_OA.PMS_PERSON_FILE.FILE_SORT is '文件排序号'
/

comment on column XH_OA.PMS_PERSON_FILE.FILE_NAME is '文件名称'
/

comment on column XH_OA.PMS_PERSON_FILE.FILE_PATH is '文件路径'
/

comment on column XH_OA.PMS_PERSON_FILE.FILE_SUFFIX is '文件后缀'
/

comment on column XH_OA.PMS_PERSON_FILE.FILE_TYPE is '文件类型'
/

comment on column XH_OA.PMS_PERSON_FILE.FILE_STATE is '文件状态（0未处理
1已处理
2禁用
）'
/

comment on column XH_OA.PMS_PERSON_FILE.FIRST_RPERSON is '首次登记人'
/

comment on column XH_OA.PMS_PERSON_FILE.FIRST_RTIME is '首次登记时间'
/

comment on column XH_OA.PMS_PERSON_FILE.LAST_MPERSON is '最后修改人员'
/

comment on column XH_OA.PMS_PERSON_FILE.LAST_MTIME is '后修改时间'
/

comment on column XH_OA.PMS_PERSON_FILE.REMARK is '备注'
/

create table XH_OA.PMS_PERSON_INFO
(
    PERSON_ID            VARCHAR2(20) not null
        constraint PK_PERSON_ID
            primary key,
    PGROUP_ID            VARCHAR2(20) not null,
    HOSPITAL_ID          VARCHAR2(20) not null,
    USER_ID              VARCHAR2(20),
    PERSON_TYPE          VARCHAR2(20),
    USER_NAME            VARCHAR2(50),
    SEX                  VARCHAR2(10),
    AGE                  VARCHAR2(20),
    BIRTHDAY             VARCHAR2(20),
    NATION               VARCHAR2(50),
    NATIVE_PLACE         VARCHAR2(50),
    POLITICIAN           VARCHAR2(50),
    PROFESSION           VARCHAR2(100),
    HIGHEST_DEGREE       VARCHAR2(100),
    DEGREE_TIME          VARCHAR2(50),
    HIGHEST_DIPLOMA      VARCHAR2(100),
    DIPLOMA_TIME         VARCHAR2(50),
    WORK_TIME            VARCHAR2(50),
    IN_HOSPITAL_DATE     DATE,
    DUTIES               VARCHAR2(20),
    TECH_POST            VARCHAR2(20),
    ACADEMIC_POST        VARCHAR2(20),
    COMM_ADDR            VARCHAR2(200),
    HOME_TEL             VARCHAR2(50),
    PHONE                VARCHAR2(50),
    CORNET               VARCHAR2(50),
    BIRTH_PLACE          VARCHAR2(200),
    HEIGHT               VARCHAR2(20),
    EYESIGHT             VARCHAR2(20),
    ENGLISH_RANK_SCORE   VARCHAR2(20),
    MARITAL_STATUS       VARCHAR2(50),
    CHILDREN_CONDITION   VARCHAR2(20),
    CARD_TYPE            VARCHAR2(20),
    ID_CARD              VARCHAR2(50),
    DOMICILE_PLACE       VARCHAR2(200),
    EMERGENCY_CONTACT    VARCHAR2(50),
    ECONTACT_RELACTION   VARCHAR2(50),
    ECONTACT_PHONE       VARCHAR2(50),
    CURRENT_ADDRESS      VARCHAR2(50),
    HEALTH               VARCHAR2(50),
    E_MAIL               VARCHAR2(50),
    OFFICE_PHONE         VARCHAR2(50),
    EMPLOYMENT_UNIT      VARCHAR2(50),
    TECHNOLOGY_TYPE      VARCHAR2(50),
    TECH_CERTIFICE_TIME  DATE,
    TECH_POST_PROFESSION VARCHAR2(50),
    EMPLOYMENT_SOURE     VARCHAR2(50),
    PROFESSION_EXPERTISE VARCHAR2(500),
    EMPLOY_TIME          DATE,
    RETIRE_TIME          DATE,
    IN_HOSPITAL_TIME     DATE,
    OUT_HOSPITAL_TIME    DATE,
    REEMPLOY_TIME        DATE,
    REGISTER_MODE        VARCHAR2(20),
    REGISTER_PERSON      VARCHAR2(50),
    REGISTER_TIME        VARCHAR2(20),
    SUBMIT_PERSON        VARCHAR2(50),
    SUBMIT_TIME          VARCHAR2(20),
    CHECK_PERSON         VARCHAR2(50),
    CHECK_TIME           VARCHAR2(20),
    CHECK_COMPUTER       VARCHAR2(50),
    DOC_PLACE            VARCHAR2(50),
    PERSON_DOC_STATE     VARCHAR2(20),
    PERSON_PHOTO_PATH    VARCHAR2(200),
    PERSON_STATE         VARCHAR2(20),
    FIRST_RPERSON        VARCHAR2(50),
    FIRST_RTIME          VARCHAR2(50),
    LAST_MPERSON         VARCHAR2(50),
    LAST_MTIME           VARCHAR2(50),
    REMARK               VARCHAR2(200),
    ENGLISH_RANK         VARCHAR2(20),
    LOGID                VARCHAR2(20),
    IN_LAB_TIME          VARCHAR2(50),
    OUT_LAB_TIME         VARCHAR2(50),
    USER_ENAME           VARCHAR2(20),
    HIS_ID               VARCHAR2(20),
    IF_EMPLOYMENT        VARCHAR2(10),
    LENGTH_LAB           NUMBER,
    LENGTH_HOSPITAL      NUMBER,
    LENGTH_SERVICE       NUMBER
)
/

comment on column XH_OA.PMS_PERSON_INFO.PERSON_ID is '人员ID'
/

comment on column XH_OA.PMS_PERSON_INFO.PGROUP_ID is '管理单元ID'
/

comment on column XH_OA.PMS_PERSON_INFO.HOSPITAL_ID is '医疗机构ID'
/

comment on column XH_OA.PMS_PERSON_INFO.USER_ID is '账号ID'
/

comment on column XH_OA.PMS_PERSON_INFO.PERSON_TYPE is '人员类型'
/

comment on column XH_OA.PMS_PERSON_INFO.USER_NAME is '姓名'
/

comment on column XH_OA.PMS_PERSON_INFO.SEX is '性别'
/

comment on column XH_OA.PMS_PERSON_INFO.AGE is '年龄'
/

comment on column XH_OA.PMS_PERSON_INFO.BIRTHDAY is '出生年月'
/

comment on column XH_OA.PMS_PERSON_INFO.NATION is '民族'
/

comment on column XH_OA.PMS_PERSON_INFO.NATIVE_PLACE is '籍贯'
/

comment on column XH_OA.PMS_PERSON_INFO.POLITICIAN is '政治面貌'
/

comment on column XH_OA.PMS_PERSON_INFO.PROFESSION is '毕业专业'
/

comment on column XH_OA.PMS_PERSON_INFO.HIGHEST_DEGREE is '最高学历'
/

comment on column XH_OA.PMS_PERSON_INFO.DEGREE_TIME is '学历取得时间'
/

comment on column XH_OA.PMS_PERSON_INFO.HIGHEST_DIPLOMA is '最高学位'
/

comment on column XH_OA.PMS_PERSON_INFO.DIPLOMA_TIME is '学位取得时间'
/

comment on column XH_OA.PMS_PERSON_INFO.WORK_TIME is '参加工作时间'
/

comment on column XH_OA.PMS_PERSON_INFO.IN_HOSPITAL_DATE is '进院日期'
/

comment on column XH_OA.PMS_PERSON_INFO.DUTIES is '行政职务'
/

comment on column XH_OA.PMS_PERSON_INFO.TECH_POST is '职称级别'
/

comment on column XH_OA.PMS_PERSON_INFO.ACADEMIC_POST is '学术职称'
/

comment on column XH_OA.PMS_PERSON_INFO.COMM_ADDR is '通信地址'
/

comment on column XH_OA.PMS_PERSON_INFO.HOME_TEL is '家庭电话'
/

comment on column XH_OA.PMS_PERSON_INFO.PHONE is '手机'
/

comment on column XH_OA.PMS_PERSON_INFO.CORNET is '短号'
/

comment on column XH_OA.PMS_PERSON_INFO.BIRTH_PLACE is '出生地'
/

comment on column XH_OA.PMS_PERSON_INFO.HEIGHT is '身高'
/

comment on column XH_OA.PMS_PERSON_INFO.EYESIGHT is '视力'
/

comment on column XH_OA.PMS_PERSON_INFO.ENGLISH_RANK_SCORE is '英语等级成绩'
/

comment on column XH_OA.PMS_PERSON_INFO.MARITAL_STATUS is '婚姻状况'
/

comment on column XH_OA.PMS_PERSON_INFO.CHILDREN_CONDITION is '有无子女'
/

comment on column XH_OA.PMS_PERSON_INFO.CARD_TYPE is '证件类型'
/

comment on column XH_OA.PMS_PERSON_INFO.ID_CARD is '身份证'
/

comment on column XH_OA.PMS_PERSON_INFO.DOMICILE_PLACE is '户籍所在地'
/

comment on column XH_OA.PMS_PERSON_INFO.EMERGENCY_CONTACT is '紧急联系人'
/

comment on column XH_OA.PMS_PERSON_INFO.ECONTACT_RELACTION is '与紧急联系人的关系'
/

comment on column XH_OA.PMS_PERSON_INFO.ECONTACT_PHONE is '紧急联系人电话'
/

comment on column XH_OA.PMS_PERSON_INFO.CURRENT_ADDRESS is '现居住地'
/

comment on column XH_OA.PMS_PERSON_INFO.HEALTH is '健康状况'
/

comment on column XH_OA.PMS_PERSON_INFO.E_MAIL is '邮箱'
/

comment on column XH_OA.PMS_PERSON_INFO.OFFICE_PHONE is '办公电话'
/

comment on column XH_OA.PMS_PERSON_INFO.EMPLOYMENT_UNIT is '聘任职称评定单位'
/

comment on column XH_OA.PMS_PERSON_INFO.TECHNOLOGY_TYPE is '职称类型'
/

comment on column XH_OA.PMS_PERSON_INFO.TECH_CERTIFICE_TIME is '专业技术资格取得时间'
/

comment on column XH_OA.PMS_PERSON_INFO.TECH_POST_PROFESSION is '职称专业'
/

comment on column XH_OA.PMS_PERSON_INFO.EMPLOYMENT_SOURE is '招聘来源'
/

comment on column XH_OA.PMS_PERSON_INFO.PROFESSION_EXPERTISE is '专业特长'
/

comment on column XH_OA.PMS_PERSON_INFO.EMPLOY_TIME is '聘用时间'
/

comment on column XH_OA.PMS_PERSON_INFO.RETIRE_TIME is '退休时间'
/

comment on column XH_OA.PMS_PERSON_INFO.IN_HOSPITAL_TIME is '来院时间'
/

comment on column XH_OA.PMS_PERSON_INFO.OUT_HOSPITAL_TIME is '离院时间'
/

comment on column XH_OA.PMS_PERSON_INFO.REEMPLOY_TIME is '返聘时间'
/

comment on column XH_OA.PMS_PERSON_INFO.REGISTER_MODE is '登记模式'
/

comment on column XH_OA.PMS_PERSON_INFO.REGISTER_PERSON is '登记人员'
/

comment on column XH_OA.PMS_PERSON_INFO.REGISTER_TIME is '登记时间'
/

comment on column XH_OA.PMS_PERSON_INFO.SUBMIT_PERSON is '提交人员'
/

comment on column XH_OA.PMS_PERSON_INFO.SUBMIT_TIME is '提交时间'
/

comment on column XH_OA.PMS_PERSON_INFO.CHECK_PERSON is '审核人员'
/

comment on column XH_OA.PMS_PERSON_INFO.CHECK_TIME is '审核时间'
/

comment on column XH_OA.PMS_PERSON_INFO.CHECK_COMPUTER is '审核电脑'
/

comment on column XH_OA.PMS_PERSON_INFO.DOC_PLACE is '放置场所'
/

comment on column XH_OA.PMS_PERSON_INFO.PERSON_DOC_STATE is '人员性质（1在职
2离职
3退休
4死亡
）'
/

comment on column XH_OA.PMS_PERSON_INFO.PERSON_PHOTO_PATH is '人员图片地址'
/

comment on column XH_OA.PMS_PERSON_INFO.PERSON_STATE is '人员状态（0已登记
1已审核
）'
/

comment on column XH_OA.PMS_PERSON_INFO.FIRST_RPERSON is '首次登记人'
/

comment on column XH_OA.PMS_PERSON_INFO.FIRST_RTIME is '首次登记时间'
/

comment on column XH_OA.PMS_PERSON_INFO.LAST_MPERSON is '最后修改人员'
/

comment on column XH_OA.PMS_PERSON_INFO.LAST_MTIME is '最后修改时间'
/

comment on column XH_OA.PMS_PERSON_INFO.REMARK is '备注'
/

comment on column XH_OA.PMS_PERSON_INFO.ENGLISH_RANK is '英语等级'
/

comment on column XH_OA.PMS_PERSON_INFO.LOGID is '登录ID'
/

comment on column XH_OA.PMS_PERSON_INFO.IN_LAB_TIME is '进科时间'
/

comment on column XH_OA.PMS_PERSON_INFO.OUT_LAB_TIME is '离科时间'
/

comment on column XH_OA.PMS_PERSON_INFO.USER_ENAME is '英文名'
/

comment on column XH_OA.PMS_PERSON_INFO.HIS_ID is 'HISID'
/

comment on column XH_OA.PMS_PERSON_INFO.IF_EMPLOYMENT is '是否应聘'
/

comment on column XH_OA.PMS_PERSON_INFO.LENGTH_LAB is '来科时间'
/

comment on column XH_OA.PMS_PERSON_INFO.LENGTH_HOSPITAL is '院龄'
/

comment on column XH_OA.PMS_PERSON_INFO.LENGTH_SERVICE is '连续工龄'
/

create table XH_OA.PMS_PROFESSIONAL_LIST
(
    PROFESSIONAL_ID     VARCHAR2(20) not null
        constraint PK_PROFESSIONAL_ID
            primary key,
    HOSPITAL_ID         VARCHAR2(20) not null,
    PERSON_ID           VARCHAR2(20) not null,
    PROFESSIONAL_SORT   VARCHAR2(20) not null,
    PROFESSIONAL_UNIT   VARCHAR2(100),
    PROFESSIONAL_LEVEL  VARCHAR2(50),
    TECHNOLOGY_TYPE     VARCHAR2(50),
    EVALUATE_UNIT       VARCHAR2(50),
    EVALUATE_DATE       VARCHAR2(50),
    PROFESSIONAL_AFFIX  VARCHAR2(100),
    SELECT_CHECK_PERSON VARCHAR2(50),
    CHECK_TIME          DATE,
    CHECK_PERSON        VARCHAR2(50),
    FIRST_RPERSON       VARCHAR2(50),
    FIRST_RTIME         DATE,
    LAST_MPERSON        VARCHAR2(50),
    LAST_MTIME          DATE,
    REMARK              VARCHAR2(200),
    CHECK_STATE         VARCHAR2(20),
    PROFESSIONAL_NAME   VARCHAR2(50),
    PROFESSIONAL_STATE  VARCHAR2(20)
)
/

comment on column XH_OA.PMS_PROFESSIONAL_LIST.PROFESSIONAL_ID is '记录ID'
/

comment on column XH_OA.PMS_PROFESSIONAL_LIST.HOSPITAL_ID is '医疗机构ID'
/

comment on column XH_OA.PMS_PROFESSIONAL_LIST.PERSON_ID is '人员ID'
/

comment on column XH_OA.PMS_PROFESSIONAL_LIST.PROFESSIONAL_SORT is '排序号'
/

comment on column XH_OA.PMS_PROFESSIONAL_LIST.PROFESSIONAL_UNIT is '职称专业'
/

comment on column XH_OA.PMS_PROFESSIONAL_LIST.PROFESSIONAL_LEVEL is '职称级别'
/

comment on column XH_OA.PMS_PROFESSIONAL_LIST.TECHNOLOGY_TYPE is '职称类型'
/

comment on column XH_OA.PMS_PROFESSIONAL_LIST.EVALUATE_UNIT is '职称评定单位'
/

comment on column XH_OA.PMS_PROFESSIONAL_LIST.EVALUATE_DATE is '职称评定日期'
/

comment on column XH_OA.PMS_PROFESSIONAL_LIST.PROFESSIONAL_AFFIX is '附件'
/

comment on column XH_OA.PMS_PROFESSIONAL_LIST.SELECT_CHECK_PERSON is '指定审核人员'
/

comment on column XH_OA.PMS_PROFESSIONAL_LIST.CHECK_TIME is '审核时间'
/

comment on column XH_OA.PMS_PROFESSIONAL_LIST.CHECK_PERSON is '审核人员'
/

comment on column XH_OA.PMS_PROFESSIONAL_LIST.FIRST_RPERSON is '首次登记人'
/

comment on column XH_OA.PMS_PROFESSIONAL_LIST.FIRST_RTIME is '首次登记时间'
/

comment on column XH_OA.PMS_PROFESSIONAL_LIST.LAST_MPERSON is '最后修改人员'
/

comment on column XH_OA.PMS_PROFESSIONAL_LIST.LAST_MTIME is '最后修改时间'
/

comment on column XH_OA.PMS_PROFESSIONAL_LIST.REMARK is '备注'
/

comment on column XH_OA.PMS_PROFESSIONAL_LIST.CHECK_STATE is '审核状态'
/

comment on column XH_OA.PMS_PROFESSIONAL_LIST.PROFESSIONAL_NAME is '职称名称'
/

comment on column XH_OA.PMS_PROFESSIONAL_LIST.PROFESSIONAL_STATE is '状态（0禁用
1在用
）'
/

create table XH_OA.PMS_RESEARCH_LIST
(
    RESEARCH_ID          VARCHAR2(20) not null
        constraint PK_RESEARCH_ID
            primary key,
    PERSON_ID            VARCHAR2(20) not null,
    RESEARCH_SORT        VARCHAR2(20) not null,
    RESEARCH_CLASS       VARCHAR2(20),
    RESEARCH_LEVEL       VARCHAR2(20),
    RESEARCH_NAME        VARCHAR2(100),
    RESEARCH_ITEM_NAME   VARCHAR2(500),
    RESEARCH_AFFIX       VARCHAR2(100),
    FIRST_RPERSON        VARCHAR2(50),
    FIRST_RTIME          DATE,
    LAST_MPERSON         VARCHAR2(50),
    LAST_MTIME           DATE,
    REMARK               VARCHAR2(200),
    SELECT_CHECK_PERSON  VARCHAR2(50),
    CHECK_TIME           DATE,
    CHECK_PERSON         VARCHAR2(50),
    CHECK_STATE          VARCHAR2(20),
    RESEARCH_STATE       VARCHAR2(20),
    HOSPITAL_ID          VARCHAR2(20),
    RESEARCH_TYPE        VARCHAR2(50),
    COMPETENT_DEPT       VARCHAR2(50),
    PART_TYPE            VARCHAR2(50),
    RESEARCH_MEMBER      VARCHAR2(100),
    RESEARCH_EXPENDITURE NUMBER(18, 4),
    RESEARCH_YEAR        VARCHAR2(32)
)
/

comment on column XH_OA.PMS_RESEARCH_LIST.RESEARCH_ID is '课题记录ID'
/

comment on column XH_OA.PMS_RESEARCH_LIST.PERSON_ID is '人员ID'
/

comment on column XH_OA.PMS_RESEARCH_LIST.RESEARCH_SORT is '排序号'
/

comment on column XH_OA.PMS_RESEARCH_LIST.RESEARCH_CLASS is '课题分类'
/

comment on column XH_OA.PMS_RESEARCH_LIST.RESEARCH_LEVEL is '课题级别'
/

comment on column XH_OA.PMS_RESEARCH_LIST.RESEARCH_NAME is '课题名称'
/

comment on column XH_OA.PMS_RESEARCH_LIST.RESEARCH_ITEM_NAME is '课题项目名称'
/

comment on column XH_OA.PMS_RESEARCH_LIST.RESEARCH_AFFIX is '附件'
/

comment on column XH_OA.PMS_RESEARCH_LIST.FIRST_RPERSON is '首次登记人'
/

comment on column XH_OA.PMS_RESEARCH_LIST.FIRST_RTIME is '首次登记人'
/

comment on column XH_OA.PMS_RESEARCH_LIST.LAST_MPERSON is '最后修改人员'
/

comment on column XH_OA.PMS_RESEARCH_LIST.LAST_MTIME is '最后修改时间'
/

comment on column XH_OA.PMS_RESEARCH_LIST.REMARK is '备注'
/

comment on column XH_OA.PMS_RESEARCH_LIST.SELECT_CHECK_PERSON is '指定审核人员'
/

comment on column XH_OA.PMS_RESEARCH_LIST.CHECK_TIME is '审核时间'
/

comment on column XH_OA.PMS_RESEARCH_LIST.CHECK_PERSON is '审核人员'
/

comment on column XH_OA.PMS_RESEARCH_LIST.CHECK_STATE is '状态（0禁用
1在用
）'
/

comment on column XH_OA.PMS_RESEARCH_LIST.RESEARCH_STATE is '审核状态'
/

comment on column XH_OA.PMS_RESEARCH_LIST.HOSPITAL_ID is '医疗机构ID'
/

comment on column XH_OA.PMS_RESEARCH_LIST.RESEARCH_TYPE is '课题类型'
/

comment on column XH_OA.PMS_RESEARCH_LIST.COMPETENT_DEPT is '主管部门'
/

comment on column XH_OA.PMS_RESEARCH_LIST.PART_TYPE is '参与类型'
/

comment on column XH_OA.PMS_RESEARCH_LIST.RESEARCH_MEMBER is '课题成员'
/

comment on column XH_OA.PMS_RESEARCH_LIST.RESEARCH_EXPENDITURE is '课题经费'
/

create table XH_OA.PMS_RESUME_LIST
(
    RESUME_ID           VARCHAR2(20) not null
        constraint PK_RESUME_ID
            primary key,
    PERSON_ID           VARCHAR2(20) not null,
    RESUME_SORT         VARCHAR2(20) not null,
    RESUME_UNIT         VARCHAR2(100),
    WORK_DEPT           VARCHAR2(100),
    WORK_START_TIME     VARCHAR2(50),
    WORK_END_TIME       VARCHAR2(50),
    WORK_PROFESSION     VARCHAR2(50),
    RESUME_AFFIX        VARCHAR2(100),
    FIRST_RPERSON       VARCHAR2(50),
    FIRST_RTIME         DATE,
    LAST_MPERSON        VARCHAR2(50),
    LAST_MTIME          DATE,
    REMARK              VARCHAR2(200),
    SELECT_CHECK_PERSON VARCHAR2(50),
    CHECK_TIME          DATE,
    CHECK_PERSON        VARCHAR2(50),
    CHECK_STATE         VARCHAR2(20),
    WORK_POST           VARCHAR2(100),
    RESUME_STATE        VARCHAR2(20),
    HOSPITAL_ID         VARCHAR2(20),
    WORK_YEAR           NUMBER
)
/

comment on column XH_OA.PMS_RESUME_LIST.RESUME_ID is '履历记录ID'
/

comment on column XH_OA.PMS_RESUME_LIST.PERSON_ID is '人员ID'
/

comment on column XH_OA.PMS_RESUME_LIST.RESUME_SORT is '排序号'
/

comment on column XH_OA.PMS_RESUME_LIST.RESUME_UNIT is '履历单位'
/

comment on column XH_OA.PMS_RESUME_LIST.WORK_DEPT is '工作部门'
/

comment on column XH_OA.PMS_RESUME_LIST.WORK_START_TIME is '工作经历开始时间'
/

comment on column XH_OA.PMS_RESUME_LIST.WORK_END_TIME is '工作经历开始时间'
/

comment on column XH_OA.PMS_RESUME_LIST.WORK_PROFESSION is '专业'
/

comment on column XH_OA.PMS_RESUME_LIST.RESUME_AFFIX is '附件'
/

comment on column XH_OA.PMS_RESUME_LIST.FIRST_RPERSON is '首次登记人'
/

comment on column XH_OA.PMS_RESUME_LIST.FIRST_RTIME is '首次登记时间'
/

comment on column XH_OA.PMS_RESUME_LIST.LAST_MPERSON is '最后修改人员'
/

comment on column XH_OA.PMS_RESUME_LIST.LAST_MTIME is '最后修改时间'
/

comment on column XH_OA.PMS_RESUME_LIST.REMARK is '备注'
/

comment on column XH_OA.PMS_RESUME_LIST.SELECT_CHECK_PERSON is '指定审核人员'
/

comment on column XH_OA.PMS_RESUME_LIST.CHECK_TIME is '审核时间'
/

comment on column XH_OA.PMS_RESUME_LIST.CHECK_PERSON is '审核人员'
/

comment on column XH_OA.PMS_RESUME_LIST.CHECK_STATE is '审核状态'
/

comment on column XH_OA.PMS_RESUME_LIST.WORK_POST is '工作岗位名称'
/

comment on column XH_OA.PMS_RESUME_LIST.RESUME_STATE is '状态（0禁用
1在用
）'
/

comment on column XH_OA.PMS_RESUME_LIST.HOSPITAL_ID is '医疗机构ID'
/

comment on column XH_OA.PMS_RESUME_LIST.WORK_YEAR is '年限'
/

create table XH_OA.PMS_REWARD_LIST
(
    REWARD_ID           VARCHAR2(20) not null
        constraint PK_REWARD_ID
            primary key,
    PERSON_ID           VARCHAR2(20) not null,
    REWARD_SORT         VARCHAR2(20) not null,
    REWARD_LEVEL        VARCHAR2(20),
    REWARD_NAME         VARCHAR2(100),
    REWARD_ITEM_NAME    VARCHAR2(500),
    REWARD_AFFIX        VARCHAR2(100),
    FIRST_RPERSON       VARCHAR2(50),
    FIRST_RTIME         DATE,
    LAST_MPERSON        VARCHAR2(50),
    LAST_MTIME          DATE,
    REMARK              VARCHAR2(200),
    SELECT_CHECK_PERSON VARCHAR2(50),
    CHECK_TIME          DATE,
    CHECK_PERSON        VARCHAR2(50),
    CHECK_STATE         VARCHAR2(20),
    REWARD_STATE        VARCHAR2(20),
    HOSPITAL_ID         VARCHAR2(20),
    REWARD_YEAR         NUMBER
)
/

comment on column XH_OA.PMS_REWARD_LIST.REWARD_ID is '奖惩记录ID'
/

comment on column XH_OA.PMS_REWARD_LIST.PERSON_ID is '人员ID'
/

comment on column XH_OA.PMS_REWARD_LIST.REWARD_SORT is '排序号'
/

comment on column XH_OA.PMS_REWARD_LIST.REWARD_LEVEL is '奖惩级别'
/

comment on column XH_OA.PMS_REWARD_LIST.REWARD_NAME is '奖惩名称'
/

comment on column XH_OA.PMS_REWARD_LIST.REWARD_ITEM_NAME is '奖惩项目名称'
/

comment on column XH_OA.PMS_REWARD_LIST.REWARD_AFFIX is '附件'
/

comment on column XH_OA.PMS_REWARD_LIST.FIRST_RPERSON is '首次登记人'
/

comment on column XH_OA.PMS_REWARD_LIST.FIRST_RTIME is '首次登记时间'
/

comment on column XH_OA.PMS_REWARD_LIST.LAST_MPERSON is '最后修改人员'
/

comment on column XH_OA.PMS_REWARD_LIST.LAST_MTIME is '最后修改时间'
/

comment on column XH_OA.PMS_REWARD_LIST.REMARK is '备注'
/

comment on column XH_OA.PMS_REWARD_LIST.SELECT_CHECK_PERSON is '指定审核人员'
/

comment on column XH_OA.PMS_REWARD_LIST.CHECK_TIME is '审核时间'
/

comment on column XH_OA.PMS_REWARD_LIST.CHECK_PERSON is '审核人员'
/

comment on column XH_OA.PMS_REWARD_LIST.CHECK_STATE is '审核状态'
/

comment on column XH_OA.PMS_REWARD_LIST.REWARD_STATE is '状态（0禁用
1在用
）'
/

comment on column XH_OA.PMS_REWARD_LIST.HOSPITAL_ID is '医疗机构ID'
/

comment on column XH_OA.PMS_REWARD_LIST.REWARD_YEAR is '奖惩年份'
/

create table XH_OA.PMS_SKILL_CERTIFICATE_LIST
(
    CERTIFICATE_ID       VARCHAR2(20) not null
        constraint PK_CERTIFICATE_ID
            primary key,
    PERSON_ID            VARCHAR2(20) not null,
    CERTIFICATE_SORT     VARCHAR2(20) not null,
    CERTIFICATE_TYPE     VARCHAR2(20),
    CERTIFICATE_LEVEL    VARCHAR2(20),
    CERTIFICATE_NAME     VARCHAR2(100),
    CERTIFICATE_UNIT     VARCHAR2(100),
    OBTAIN_TIME          VARCHAR2(50),
    CERTIFICATE_DATE     VARCHAR2(50),
    CERTIFICATE_NUMBER   VARCHAR2(50),
    CERTIFICATE_AFFIX    VARCHAR2(100),
    FIRST_RPERSON        VARCHAR2(50),
    FIRST_RTIME          DATE,
    LAST_MPERSON         VARCHAR2(50),
    LAST_MTIME           DATE,
    REMARK               VARCHAR2(200),
    SELECT_CHECK_PERSON  VARCHAR2(50),
    CHECK_TIME           DATE,
    CHECK_PERSON         VARCHAR2(50),
    CHECK_STATE          VARCHAR2(20),
    CERTIFICATE_VALIDITY VARCHAR2(50),
    CERTIFICATE_STATE    VARCHAR2(20),
    HOSPITAL_ID          VARCHAR2(20)
)
/

comment on column XH_OA.PMS_SKILL_CERTIFICATE_LIST.CERTIFICATE_ID is '证书记录ID'
/

comment on column XH_OA.PMS_SKILL_CERTIFICATE_LIST.PERSON_ID is '人员ID'
/

comment on column XH_OA.PMS_SKILL_CERTIFICATE_LIST.CERTIFICATE_SORT is '排序号'
/

comment on column XH_OA.PMS_SKILL_CERTIFICATE_LIST.CERTIFICATE_TYPE is '证书类型'
/

comment on column XH_OA.PMS_SKILL_CERTIFICATE_LIST.CERTIFICATE_LEVEL is '证书级别'
/

comment on column XH_OA.PMS_SKILL_CERTIFICATE_LIST.CERTIFICATE_NAME is '证书名称'
/

comment on column XH_OA.PMS_SKILL_CERTIFICATE_LIST.CERTIFICATE_UNIT is '证书名称'
/

comment on column XH_OA.PMS_SKILL_CERTIFICATE_LIST.OBTAIN_TIME is '获取时间'
/

comment on column XH_OA.PMS_SKILL_CERTIFICATE_LIST.CERTIFICATE_DATE is '发证日期'
/

comment on column XH_OA.PMS_SKILL_CERTIFICATE_LIST.CERTIFICATE_NUMBER is '证书编号'
/

comment on column XH_OA.PMS_SKILL_CERTIFICATE_LIST.CERTIFICATE_AFFIX is '附件'
/

comment on column XH_OA.PMS_SKILL_CERTIFICATE_LIST.FIRST_RPERSON is '首次登记人'
/

comment on column XH_OA.PMS_SKILL_CERTIFICATE_LIST.FIRST_RTIME is '首次登记时间'
/

comment on column XH_OA.PMS_SKILL_CERTIFICATE_LIST.LAST_MPERSON is '最后修改人员'
/

comment on column XH_OA.PMS_SKILL_CERTIFICATE_LIST.LAST_MTIME is '最后修改时间'
/

comment on column XH_OA.PMS_SKILL_CERTIFICATE_LIST.REMARK is '备注'
/

comment on column XH_OA.PMS_SKILL_CERTIFICATE_LIST.SELECT_CHECK_PERSON is '指定审核人员'
/

comment on column XH_OA.PMS_SKILL_CERTIFICATE_LIST.CHECK_TIME is '审核时间'
/

comment on column XH_OA.PMS_SKILL_CERTIFICATE_LIST.CHECK_PERSON is '审核人员'
/

comment on column XH_OA.PMS_SKILL_CERTIFICATE_LIST.CHECK_STATE is '审核状态'
/

comment on column XH_OA.PMS_SKILL_CERTIFICATE_LIST.CERTIFICATE_VALIDITY is '证书有效期'
/

comment on column XH_OA.PMS_SKILL_CERTIFICATE_LIST.CERTIFICATE_STATE is '状态（0禁用
1在用
）'
/

comment on column XH_OA.PMS_SKILL_CERTIFICATE_LIST.HOSPITAL_ID is '医疗机构ID'
/

create table XH_OA.PMS_SOCIAL_OFFICE_LIST
(
    SOFFICE_ID          VARCHAR2(20) not null
        constraint PK_SOFFICE_ID
            primary key,
    PERSON_ID           VARCHAR2(20) not null,
    SOFFICE_SORT        VARCHAR2(20) not null,
    SOFFICE_UNIT        VARCHAR2(100),
    SOFFICE_LEVEL       VARCHAR2(20),
    SOFFICE_POST        VARCHAR2(20),
    SOFFICE_START_TIME  VARCHAR2(50),
    SOFFICE_LEAVE_TIME  VARCHAR2(50),
    IF_SOFFICE          VARCHAR2(20),
    SOFFICE_AFFIX       VARCHAR2(100),
    FIRST_RPERSON       VARCHAR2(50),
    FIRST_RTIME         DATE,
    LAST_MPERSON        VARCHAR2(50),
    LAST_MTIME          DATE,
    REMARK              VARCHAR2(200),
    SELECT_CHECK_PERSON VARCHAR2(50),
    CHECK_TIME          DATE,
    CHECK_PERSON        VARCHAR2(50),
    CHECK_STATE         VARCHAR2(20),
    SOFFICE_STATE       VARCHAR2(20),
    HOSPITAL_ID         VARCHAR2(20),
    SOFFICE_DURATION    NUMBER
)
/

comment on column XH_OA.PMS_SOCIAL_OFFICE_LIST.SOFFICE_ID is '社会任职记录ID'
/

comment on column XH_OA.PMS_SOCIAL_OFFICE_LIST.PERSON_ID is '人员ID'
/

comment on column XH_OA.PMS_SOCIAL_OFFICE_LIST.SOFFICE_SORT is '排序号'
/

comment on column XH_OA.PMS_SOCIAL_OFFICE_LIST.SOFFICE_UNIT is '任职机构'
/

comment on column XH_OA.PMS_SOCIAL_OFFICE_LIST.SOFFICE_LEVEL is '任职机构等级'
/

comment on column XH_OA.PMS_SOCIAL_OFFICE_LIST.SOFFICE_POST is '任职职务'
/

comment on column XH_OA.PMS_SOCIAL_OFFICE_LIST.SOFFICE_START_TIME is '上任时间'
/

comment on column XH_OA.PMS_SOCIAL_OFFICE_LIST.SOFFICE_LEAVE_TIME is '离任时间'
/

comment on column XH_OA.PMS_SOCIAL_OFFICE_LIST.IF_SOFFICE is '当前是否在职'
/

comment on column XH_OA.PMS_SOCIAL_OFFICE_LIST.SOFFICE_AFFIX is '附件'
/

comment on column XH_OA.PMS_SOCIAL_OFFICE_LIST.FIRST_RPERSON is '首次登记人'
/

comment on column XH_OA.PMS_SOCIAL_OFFICE_LIST.FIRST_RTIME is '首次登记时间'
/

comment on column XH_OA.PMS_SOCIAL_OFFICE_LIST.LAST_MPERSON is '最后修改人员'
/

comment on column XH_OA.PMS_SOCIAL_OFFICE_LIST.LAST_MTIME is '最后修改时间'
/

comment on column XH_OA.PMS_SOCIAL_OFFICE_LIST.REMARK is '备注'
/

comment on column XH_OA.PMS_SOCIAL_OFFICE_LIST.SELECT_CHECK_PERSON is '指定审核人员'
/

comment on column XH_OA.PMS_SOCIAL_OFFICE_LIST.CHECK_TIME is '审核时间'
/

comment on column XH_OA.PMS_SOCIAL_OFFICE_LIST.CHECK_PERSON is '审核人员'
/

comment on column XH_OA.PMS_SOCIAL_OFFICE_LIST.CHECK_STATE is '审核状态'
/

comment on column XH_OA.PMS_SOCIAL_OFFICE_LIST.SOFFICE_STATE is '状态（0禁用
1在用
）'
/

comment on column XH_OA.PMS_SOCIAL_OFFICE_LIST.HOSPITAL_ID is '医疗机构ID'
/

comment on column XH_OA.PMS_SOCIAL_OFFICE_LIST.SOFFICE_DURATION is '在任时长'
/

create table XH_OA.PMS_STUDY_LIST
(
    STUDY_ID            VARCHAR2(20) not null
        constraint PK_STUDY_ID
            primary key,
    PERSON_ID           VARCHAR2(20) not null,
    STUDY_SORT          VARCHAR2(20) not null,
    STUDY_CLASS         VARCHAR2(20),
    STUDY_UNIT          VARCHAR2(50),
    STUDY_DEPT          VARCHAR2(50),
    STUDY_CONTENT       VARCHAR2(1000),
    STUDY_AFFIX         VARCHAR2(100),
    FIRST_RPERSON       VARCHAR2(50),
    FIRST_RTIME         DATE,
    LAST_MPERSON        VARCHAR2(50),
    LAST_MTIME          DATE,
    REMARK              VARCHAR2(200),
    SELECT_CHECK_PERSON VARCHAR2(50),
    CHECK_TIME          DATE,
    CHECK_PERSON        VARCHAR2(50),
    CHECK_STATE         VARCHAR2(20),
    STUDY_START_DATE    DATE,
    STUDY_END_DATE      DATE,
    STUDY_STATE         VARCHAR2(20),
    HOSPITAL_ID         VARCHAR2(20),
    STUDY_DURATION      NUMBER
)
/

comment on column XH_OA.PMS_STUDY_LIST.STUDY_ID is '进修记录ID'
/

comment on column XH_OA.PMS_STUDY_LIST.PERSON_ID is '人员ID'
/

comment on column XH_OA.PMS_STUDY_LIST.STUDY_SORT is '排序号'
/

comment on column XH_OA.PMS_STUDY_LIST.STUDY_CLASS is '进修分类'
/

comment on column XH_OA.PMS_STUDY_LIST.STUDY_UNIT is '进修单位'
/

comment on column XH_OA.PMS_STUDY_LIST.STUDY_DEPT is '进修部门'
/

comment on column XH_OA.PMS_STUDY_LIST.STUDY_CONTENT is '进修内容'
/

comment on column XH_OA.PMS_STUDY_LIST.STUDY_AFFIX is '附件'
/

comment on column XH_OA.PMS_STUDY_LIST.FIRST_RPERSON is '首次登记人'
/

comment on column XH_OA.PMS_STUDY_LIST.FIRST_RTIME is '首次登记时间'
/

comment on column XH_OA.PMS_STUDY_LIST.LAST_MPERSON is '最后修改人员'
/

comment on column XH_OA.PMS_STUDY_LIST.LAST_MTIME is '最后修改时间'
/

comment on column XH_OA.PMS_STUDY_LIST.REMARK is '备注'
/

comment on column XH_OA.PMS_STUDY_LIST.SELECT_CHECK_PERSON is '指定审核人员'
/

comment on column XH_OA.PMS_STUDY_LIST.CHECK_TIME is '审核时间'
/

comment on column XH_OA.PMS_STUDY_LIST.CHECK_PERSON is '审核人员'
/

comment on column XH_OA.PMS_STUDY_LIST.CHECK_STATE is '审核状态'
/

comment on column XH_OA.PMS_STUDY_LIST.STUDY_START_DATE is '进修开始日期'
/

comment on column XH_OA.PMS_STUDY_LIST.STUDY_END_DATE is '进修结束日期'
/

comment on column XH_OA.PMS_STUDY_LIST.STUDY_STATE is '状态(0禁用
1在用
)'
/

comment on column XH_OA.PMS_STUDY_LIST.HOSPITAL_ID is '医疗机构ID'
/

comment on column XH_OA.PMS_STUDY_LIST.STUDY_DURATION is '进修时长'
/

create table XH_OA.PMS_TEACH_LIST
(
    TEACH_ID            VARCHAR2(20) not null
        constraint PK_TEACH_ID
            primary key,
    PERSON_ID           VARCHAR2(20) not null,
    TEACH_SORT          VARCHAR2(20) not null,
    TEACH_CLASS         VARCHAR2(20),
    TEACH_LEVEL         VARCHAR2(20),
    TEACH_NAME          VARCHAR2(100),
    TEACH_AFFIX         VARCHAR2(100),
    FIRST_RPERSON       VARCHAR2(50),
    FIRST_RTIME         DATE,
    LAST_MPERSON        VARCHAR2(50),
    LAST_MTIME          DATE,
    REMARK              VARCHAR2(200),
    SELECT_CHECK_PERSON VARCHAR2(50),
    CHECK_TIME          DATE,
    CHECK_PERSON        VARCHAR2(50),
    CHECK_STATE         VARCHAR2(20),
    TEACH_STATE         VARCHAR2(20),
    HOSPITAL_ID         VARCHAR2(20),
    TEACH_HOUR          NUMBER,
    TEACH_PNUM          NUMBER,
    TEACH_YEAR          NUMBER
)
/

comment on column XH_OA.PMS_TEACH_LIST.TEACH_ID is '教学记录ID'
/

comment on column XH_OA.PMS_TEACH_LIST.PERSON_ID is '人员ID'
/

comment on column XH_OA.PMS_TEACH_LIST.TEACH_SORT is '排序号'
/

comment on column XH_OA.PMS_TEACH_LIST.TEACH_CLASS is '教学分类'
/

comment on column XH_OA.PMS_TEACH_LIST.TEACH_LEVEL is '教学级别'
/

comment on column XH_OA.PMS_TEACH_LIST.TEACH_NAME is '教学记录'
/

comment on column XH_OA.PMS_TEACH_LIST.TEACH_AFFIX is '附件'
/

comment on column XH_OA.PMS_TEACH_LIST.FIRST_RPERSON is '首次登记人'
/

comment on column XH_OA.PMS_TEACH_LIST.FIRST_RTIME is '首次登记时间'
/

comment on column XH_OA.PMS_TEACH_LIST.LAST_MPERSON is '最后修改人员'
/

comment on column XH_OA.PMS_TEACH_LIST.LAST_MTIME is '最后修改时间'
/

comment on column XH_OA.PMS_TEACH_LIST.REMARK is '备注'
/

comment on column XH_OA.PMS_TEACH_LIST.SELECT_CHECK_PERSON is '指定审核人员'
/

comment on column XH_OA.PMS_TEACH_LIST.CHECK_TIME is '审核时间'
/

comment on column XH_OA.PMS_TEACH_LIST.CHECK_PERSON is '审核人员'
/

comment on column XH_OA.PMS_TEACH_LIST.CHECK_STATE is '审核状态'
/

comment on column XH_OA.PMS_TEACH_LIST.TEACH_STATE is '状态(0禁用
1在用
)'
/

comment on column XH_OA.PMS_TEACH_LIST.HOSPITAL_ID is '医疗机构ID'
/

comment on column XH_OA.PMS_TEACH_LIST.TEACH_HOUR is '教学课时'
/

comment on column XH_OA.PMS_TEACH_LIST.TEACH_PNUM is '教学人数'
/

comment on column XH_OA.PMS_TEACH_LIST.TEACH_YEAR is '教学年份'
/

create table XH_OA.PMS_THESIS_LIST
(
    THESIS_ID           VARCHAR2(20) not null
        constraint PK_THESIS_ID
            primary key,
    PERSON_ID           VARCHAR2(20) not null,
    THESIS_SORT         VARCHAR2(20) not null,
    THESIS_TYPE         VARCHAR2(20),
    THESIS_LEVEL        VARCHAR2(20),
    THESIS_PUBLISH      VARCHAR2(100),
    THESIS_NAME         VARCHAR2(100),
    ISSUED_TIME         VARCHAR2(50),
    THESIS_ITEM_NAME    VARCHAR2(500),
    IF_SCI              VARCHAR2(20),
    JCR_RANGE           VARCHAR2(20),
    IF_VALUE            VARCHAR2(20),
    THESIS_PERSON_SORT  VARCHAR2(20),
    THESIS_AFFIX        VARCHAR2(100),
    FIRST_RPERSON       VARCHAR2(50),
    FIRST_RTIME         DATE,
    LAST_MPERSON        VARCHAR2(50),
    LAST_MTIME          DATE,
    REMARK              VARCHAR2(200),
    SELECT_CHECK_PERSON VARCHAR2(50),
    CHECK_TIME          DATE,
    CHECK_PERSON        VARCHAR2(50),
    CHECK_STATE         VARCHAR2(20),
    THESIS_STATE        VARCHAR2(20),
    HOSPITAL_ID         VARCHAR2(20)
)
/

comment on column XH_OA.PMS_THESIS_LIST.THESIS_ID is '论文记录ID'
/

comment on column XH_OA.PMS_THESIS_LIST.PERSON_ID is '人员ID'
/

comment on column XH_OA.PMS_THESIS_LIST.THESIS_SORT is '排序号'
/

comment on column XH_OA.PMS_THESIS_LIST.THESIS_TYPE is '论文类型'
/

comment on column XH_OA.PMS_THESIS_LIST.THESIS_LEVEL is '论文级别'
/

comment on column XH_OA.PMS_THESIS_LIST.THESIS_PUBLISH is '刊物/出版社'
/

comment on column XH_OA.PMS_THESIS_LIST.THESIS_NAME is '论文名称'
/

comment on column XH_OA.PMS_THESIS_LIST.ISSUED_TIME is '发布时间'
/

comment on column XH_OA.PMS_THESIS_LIST.THESIS_ITEM_NAME is '论文项目名称'
/

comment on column XH_OA.PMS_THESIS_LIST.IF_SCI is '是否SCI记录'
/

comment on column XH_OA.PMS_THESIS_LIST.JCR_RANGE is 'JCR分区'
/

comment on column XH_OA.PMS_THESIS_LIST.IF_VALUE is 'IF值'
/

comment on column XH_OA.PMS_THESIS_LIST.THESIS_PERSON_SORT is '论文作者排序'
/

comment on column XH_OA.PMS_THESIS_LIST.THESIS_AFFIX is '附件'
/

comment on column XH_OA.PMS_THESIS_LIST.FIRST_RPERSON is '首次登记人'
/

comment on column XH_OA.PMS_THESIS_LIST.FIRST_RTIME is '首次登记时间'
/

comment on column XH_OA.PMS_THESIS_LIST.LAST_MPERSON is '最后修改人员'
/

comment on column XH_OA.PMS_THESIS_LIST.LAST_MTIME is '最后修改时间'
/

comment on column XH_OA.PMS_THESIS_LIST.REMARK is '备注'
/

comment on column XH_OA.PMS_THESIS_LIST.SELECT_CHECK_PERSON is '指定审核人员'
/

comment on column XH_OA.PMS_THESIS_LIST.CHECK_TIME is '审核时间'
/

comment on column XH_OA.PMS_THESIS_LIST.CHECK_PERSON is '审核人员'
/

comment on column XH_OA.PMS_THESIS_LIST.CHECK_STATE is '审核状态'
/

comment on column XH_OA.PMS_THESIS_LIST.THESIS_STATE is '状态(0禁用
1在用
)'
/

comment on column XH_OA.PMS_THESIS_LIST.HOSPITAL_ID is '医疗机构ID'
/

create table XH_OA.PMS_TRAIN_LIST
(
    TRAIN_ID            VARCHAR2(20) not null
        constraint PK_TRAIN_ID
            primary key,
    PERSON_ID           VARCHAR2(20) not null,
    TRAIN_SORT          VARCHAR2(20) not null,
    TRAIN_UNIT          VARCHAR2(100),
    TRAIN_START_TIME    VARCHAR2(50),
    TRAIN_END_TIME      VARCHAR2(50),
    TRAIN_CONTENT       VARCHAR2(1000),
    SCORE_TYPE          VARCHAR2(20),
    IF_FINISH           VARCHAR2(20),
    TRAIN_AFFIX         VARCHAR2(100),
    FIRST_RPERSON       VARCHAR2(50),
    FIRST_RTIME         DATE,
    LAST_MPERSON        VARCHAR2(50),
    LAST_MTIME          DATE,
    REMARK              VARCHAR2(200),
    SELECT_CHECK_PERSON VARCHAR2(50),
    CHECK_TIME          DATE,
    CHECK_PERSON        VARCHAR2(50),
    CHECK_STATE         VARCHAR2(20),
    TRAIN_STATE         VARCHAR2(20),
    HOSPITAL_ID         VARCHAR2(20),
    TRAIN_SCORE         NUMBER(18, 1)
)
/

comment on column XH_OA.PMS_TRAIN_LIST.TRAIN_ID is '证书记录ID'
/

comment on column XH_OA.PMS_TRAIN_LIST.PERSON_ID is '人员ID'
/

comment on column XH_OA.PMS_TRAIN_LIST.TRAIN_SORT is '排序号'
/

comment on column XH_OA.PMS_TRAIN_LIST.TRAIN_UNIT is '培训机构'
/

comment on column XH_OA.PMS_TRAIN_LIST.TRAIN_START_TIME is '起始时间'
/

comment on column XH_OA.PMS_TRAIN_LIST.TRAIN_END_TIME is '结束时间'
/

comment on column XH_OA.PMS_TRAIN_LIST.TRAIN_CONTENT is '培训内容'
/

comment on column XH_OA.PMS_TRAIN_LIST.SCORE_TYPE is '学分类型'
/

comment on column XH_OA.PMS_TRAIN_LIST.IF_FINISH is '是否完成'
/

comment on column XH_OA.PMS_TRAIN_LIST.TRAIN_AFFIX is '附件'
/

comment on column XH_OA.PMS_TRAIN_LIST.FIRST_RPERSON is '首次登记人'
/

comment on column XH_OA.PMS_TRAIN_LIST.FIRST_RTIME is '首次登记时间'
/

comment on column XH_OA.PMS_TRAIN_LIST.LAST_MPERSON is '最后修改人员'
/

comment on column XH_OA.PMS_TRAIN_LIST.LAST_MTIME is '最后修改时间'
/

comment on column XH_OA.PMS_TRAIN_LIST.REMARK is '备注'
/

comment on column XH_OA.PMS_TRAIN_LIST.SELECT_CHECK_PERSON is '指定审核人员'
/

comment on column XH_OA.PMS_TRAIN_LIST.CHECK_TIME is '审核时间'
/

comment on column XH_OA.PMS_TRAIN_LIST.CHECK_PERSON is '审核人员'
/

comment on column XH_OA.PMS_TRAIN_LIST.CHECK_STATE is '审核状态'
/

comment on column XH_OA.PMS_TRAIN_LIST.TRAIN_STATE is '状态(0禁用
1在用
)'
/

comment on column XH_OA.PMS_TRAIN_LIST.HOSPITAL_ID is '医疗机构ID'
/

comment on column XH_OA.PMS_TRAIN_LIST.TRAIN_SCORE is '学分'
/

create table XH_OA.PMS_CLASS_INFO
(
    CLASS_ID       VARCHAR2(50) not null
        constraint PK_ACLASS_ID
            primary key,
    CLASS_TYPE     VARCHAR2(20) not null,
    HOSPITAL_ID    VARCHAR2(20) not null,
    FATHER_CLASSID VARCHAR2(50) not null,
    CLASS_NAME     VARCHAR2(50),
    CLASS_SORT     VARCHAR2(20),
    CLASS_DES      VARCHAR2(500),
    CLASS_STATE    VARCHAR2(20),
    FIRST_RPERSON  VARCHAR2(50),
    FIRST_RTIME    DATE,
    LAST_MPERSON   VARCHAR2(50),
    LAST_MTIME     DATE,
    REMARK         VARCHAR2(200)
)
/

comment on table XH_OA.PMS_CLASS_INFO is '评估分类字典表'
/

comment on column XH_OA.PMS_CLASS_INFO.CLASS_ID is '分类ID'
/

comment on column XH_OA.PMS_CLASS_INFO.CLASS_TYPE is '类型'
/

comment on column XH_OA.PMS_CLASS_INFO.HOSPITAL_ID is '医疗机构ID'
/

comment on column XH_OA.PMS_CLASS_INFO.FATHER_CLASSID is '父级分类ID'
/

comment on column XH_OA.PMS_CLASS_INFO.CLASS_NAME is '分类名称'
/

comment on column XH_OA.PMS_CLASS_INFO.CLASS_SORT is '排序号'
/

comment on column XH_OA.PMS_CLASS_INFO.CLASS_DES is '分类描述'
/

comment on column XH_OA.PMS_CLASS_INFO.CLASS_STATE is '状态0禁用1在用2删除
'
/

comment on column XH_OA.PMS_CLASS_INFO.FIRST_RPERSON is '首次登记人'
/

comment on column XH_OA.PMS_CLASS_INFO.FIRST_RTIME is '首次登记时间'
/

comment on column XH_OA.PMS_CLASS_INFO.LAST_MPERSON is '最后修改人员'
/

comment on column XH_OA.PMS_CLASS_INFO.LAST_MTIME is '最后修改时间'
/

comment on column XH_OA.PMS_CLASS_INFO.REMARK is '备注'
/

create table XH_OA.PMS_FORM_ITEM
(
    FORM_ID         VARCHAR2(50) not null,
    ITEM_ID         VARCHAR2(20) not null,
    ITEM_ONE_CLASS  VARCHAR2(50) not null,
    ITEM_TWO_CLASS  VARCHAR2(50) not null,
    ITEM_SORT       VARCHAR2(20),
    ITEM_CNAME      VARCHAR2(500),
    ITEM_DESC       VARCHAR2(4000),
    ITEM_SCORE_TYPE VARCHAR2(10),
    ITEM_GRADE      VARCHAR2(10),
    ASSOCIATED_DATE VARCHAR2(200),
    TRIGGER_EVENT   VARCHAR2(200),
    ITEM_STATE      VARCHAR2(10),
    FIRST_RPERSON   VARCHAR2(50),
    FIRST_RTIME     DATE,
    LAST_MPERSON    VARCHAR2(50),
    LAST_MTIME      DATE,
    REMARK          VARCHAR2(200),
    constraint PK_FORM_ITEM
        primary key (FORM_ID, ITEM_ID)
)
/

comment on table XH_OA.PMS_FORM_ITEM is '评估记录项目表'
/

comment on column XH_OA.PMS_FORM_ITEM.FORM_ID is '记录单ID'
/

comment on column XH_OA.PMS_FORM_ITEM.ITEM_ID is '项目ID'
/

comment on column XH_OA.PMS_FORM_ITEM.ITEM_ONE_CLASS is '一级分类'
/

comment on column XH_OA.PMS_FORM_ITEM.ITEM_TWO_CLASS is '二级分类'
/

comment on column XH_OA.PMS_FORM_ITEM.ITEM_SORT is '排序号'
/

comment on column XH_OA.PMS_FORM_ITEM.ITEM_CNAME is '中文名称'
/

comment on column XH_OA.PMS_FORM_ITEM.ITEM_DESC is '项目描述'
/

comment on column XH_OA.PMS_FORM_ITEM.ITEM_SCORE_TYPE is '分值类型'
/

comment on column XH_OA.PMS_FORM_ITEM.ITEM_GRADE is '评估项等级'
/

comment on column XH_OA.PMS_FORM_ITEM.ASSOCIATED_DATE is '关联资料'
/

comment on column XH_OA.PMS_FORM_ITEM.TRIGGER_EVENT is '触发事件'
/

comment on column XH_OA.PMS_FORM_ITEM.ITEM_STATE is '状态0禁用1在用2删除
'
/

comment on column XH_OA.PMS_FORM_ITEM.FIRST_RPERSON is '首次登记人'
/

comment on column XH_OA.PMS_FORM_ITEM.FIRST_RTIME is '首次登记时间'
/

comment on column XH_OA.PMS_FORM_ITEM.LAST_MPERSON is '最后修改人员'
/

comment on column XH_OA.PMS_FORM_ITEM.LAST_MTIME is '最后修改时间'
/

comment on column XH_OA.PMS_FORM_ITEM.REMARK is '备注'
/

create table XH_OA.PMS_ARCHITECTURE_INFO
(
    ARCHITECTURE_ID    VARCHAR2(20) not null
        constraint PK_ARCHITECTURE_ID
            primary key,
    MODULE_ID          VARCHAR2(20),
    HOSPITAL_ID        VARCHAR2(20),
    LAB_ID             VARCHAR2(20),
    ARCHITECTURE_PATH  VARCHAR2(200),
    ARCHITECTURE_STATE VARCHAR2(20),
    FIRST_RPERSON      VARCHAR2(50),
    FIRST_RTIME        VARCHAR2(50),
    LAST_MPERSON       VARCHAR2(50),
    LAST_MTIME         VARCHAR2(50),
    REMARK             VARCHAR2(200)
)
/

comment on column XH_OA.PMS_ARCHITECTURE_INFO.ARCHITECTURE_ID is '架构ID'
/

comment on column XH_OA.PMS_ARCHITECTURE_INFO.MODULE_ID is '模块ID'
/

comment on column XH_OA.PMS_ARCHITECTURE_INFO.HOSPITAL_ID is '医疗机构ID'
/

comment on column XH_OA.PMS_ARCHITECTURE_INFO.LAB_ID is '科室ID'
/

comment on column XH_OA.PMS_ARCHITECTURE_INFO.ARCHITECTURE_PATH is '架构图片地址'
/

comment on column XH_OA.PMS_ARCHITECTURE_INFO.ARCHITECTURE_STATE is '架构状态'
/

comment on column XH_OA.PMS_ARCHITECTURE_INFO.FIRST_RPERSON is '首次登记人'
/

comment on column XH_OA.PMS_ARCHITECTURE_INFO.FIRST_RTIME is '首次登记时间'
/

comment on column XH_OA.PMS_ARCHITECTURE_INFO.LAST_MPERSON is '最后修改人'
/

comment on column XH_OA.PMS_ARCHITECTURE_INFO.LAST_MTIME is '最后修改时间'
/

comment on column XH_OA.PMS_ARCHITECTURE_INFO.REMARK is '备注'
/



--2024-02-20 cxp新增人员附加记录表
CREATE TABLE XH_OA.PMS_ADDN_RECORD(
    RECORD_ID VARCHAR2(50) NOT NULL,
	HOSPITAL_ID VARCHAR2(20) NOT NULL,
    PERSON_ID VARCHAR2(50) NOT NULL,
    CLASS_ID VARCHAR2(50) NOT NULL,    
    RECORD_AFFIX VARCHAR2(500),
    RECORD_STATE VARCHAR2(10) DEFAULT '1',
    RECORD_DATA CLOB,
	SELECT_CHECK_PERSON VARCHAR2(50),
	CHECK_STATE VARCHAR2(10) DEFAULT '0',
    CHECK_PERSON VARCHAR2(50),
    CHECK_TIME DATE,
	FIRST_RPERSON VARCHAR2(50),
    FIRST_RTIME DATE,
    LAST_MPERSON VARCHAR2(50),
    LAST_TIME DATE,
    REMARK VARCHAR2(200)
);
alter table XH_OA.PMS_ADDN_RECORD
add constraint PK_PMS_ADDN_RECORD primary key (RECORD_ID);

COMMENT ON TABLE XH_OA.PMS_ADDN_RECORD IS '人员附加记录表';
COMMENT ON COLUMN XH_OA.PMS_ADDN_RECORD.RECORD_ID IS '主键id';
COMMENT ON COLUMN XH_OA.PMS_ADDN_RECORD.HOSPITAL_ID IS '院区id';
COMMENT ON COLUMN XH_OA.PMS_ADDN_RECORD.PERSON_ID IS '人员id';
COMMENT ON COLUMN XH_OA.PMS_ADDN_RECORD.CLASS_ID IS '分类id,对应PMS_ADDN_CLASS_INFO的主键ID';
COMMENT ON COLUMN XH_OA.PMS_ADDN_RECORD.RECORD_AFFIX IS '附件ID拼接';
COMMENT ON COLUMN XH_OA.PMS_ADDN_RECORD.RECORD_STATE IS '状态;1启用 0禁用';
COMMENT ON COLUMN XH_OA.PMS_ADDN_RECORD.RECORD_DATA IS '字段和所属值';
COMMENT ON COLUMN XH_OA.PMS_ADDN_RECORD.CHECK_STATE IS '审核状态:0是录入、1是提交、2是通过';
COMMENT ON COLUMN XH_OA.PMS_ADDN_RECORD.CHECK_PERSON IS '审核人';
COMMENT ON COLUMN XH_OA.PMS_ADDN_RECORD.CHECK_TIME IS '审核时间';
COMMENT ON COLUMN XH_OA.PMS_ADDN_RECORD.FIRST_RPERSON IS '首次记录人';
COMMENT ON COLUMN XH_OA.PMS_ADDN_RECORD.FIRST_RTIME IS '首次记录时间';
COMMENT ON COLUMN XH_OA.PMS_ADDN_RECORD.LAST_MPERSON IS '最后操作人';
COMMENT ON COLUMN XH_OA.PMS_ADDN_RECORD.LAST_TIME IS '最后操作时间';
COMMENT ON COLUMN XH_OA.PMS_ADDN_RECORD.REMARK IS '备注';

grant select, insert, update, delete on XH_OA.PMS_ADDN_RECORD to XH_COM;

--2024-02-20 cxp新增人员档案分类信息表
CREATE TABLE XH_OA.PMS_ADDN_CLASS_INFO(
    CLASS_ID VARCHAR2(50) NOT NULL,
	HOSPITAL_ID VARCHAR2(20) NOT NULL,
    CLASS_TYPE VARCHAR2(10) DEFAULT '0',
    CLASS_NAME VARCHAR2(50),    
    CLASS_SORT VARCHAR2(10),
    CLASS_STATE VARCHAR2(10) DEFAULT '1',
    FORM_SETUP_ID VARCHAR2(50),
	TABLE_SETUP_ID VARCHAR2(50),
	CLASS_TABLE_NAME VARCHAR2(100),
    CLASS_ADDN_CONFIG VARCHAR2(500),
	FIRST_RPERSON VARCHAR2(50),
    FIRST_RTIME DATE,
    LAST_MPERSON VARCHAR2(50),
    LAST_TIME DATE,
    REMARK VARCHAR2(200)
);
alter table XH_OA.PMS_ADDN_CLASS_INFO
add constraint PK_PMS_ADDN_CLASS_INFO primary key (CLASS_ID);

COMMENT ON TABLE XH_OA.PMS_ADDN_CLASS_INFO IS '人员档案分类信息表';
COMMENT ON COLUMN XH_OA.PMS_ADDN_CLASS_INFO.CLASS_ID IS '主键id';
COMMENT ON COLUMN XH_OA.PMS_ADDN_CLASS_INFO.HOSPITAL_ID IS '医疗机构ID';
COMMENT ON COLUMN XH_OA.PMS_ADDN_CLASS_INFO.CLASS_TYPE IS '分类类型0:固定 1:扩展 默认0';
COMMENT ON COLUMN XH_OA.PMS_ADDN_CLASS_INFO.CLASS_NAME IS '分类名';
COMMENT ON COLUMN XH_OA.PMS_ADDN_CLASS_INFO.CLASS_SORT IS '分类排序';
COMMENT ON COLUMN XH_OA.PMS_ADDN_CLASS_INFO.CLASS_STATE IS '分类状态0禁用1在用2删除,默认1';
COMMENT ON COLUMN XH_OA.PMS_ADDN_CLASS_INFO.FORM_SETUP_ID IS '表单配置记录id';
COMMENT ON COLUMN XH_OA.PMS_ADDN_CLASS_INFO.TABLE_SETUP_ID IS '表格配置记录id';
COMMENT ON COLUMN XH_OA.PMS_ADDN_CLASS_INFO.CLASS_TABLE_NAME IS '分类数据存储表名';
COMMENT ON COLUMN XH_OA.PMS_ADDN_CLASS_INFO.CLASS_ADDN_CONFIG IS '分类扩展配置内容';
COMMENT ON COLUMN XH_OA.PMS_ADDN_RECORD.FIRST_RPERSON IS '首次记录人';
COMMENT ON COLUMN XH_OA.PMS_ADDN_RECORD.FIRST_RTIME IS '首次记录时间';
COMMENT ON COLUMN XH_OA.PMS_ADDN_RECORD.LAST_MPERSON IS '最后操作人';
COMMENT ON COLUMN XH_OA.PMS_ADDN_RECORD.LAST_TIME IS '最后操作时间';
COMMENT ON COLUMN XH_OA.PMS_ADDN_RECORD.REMARK IS '备注';
grant select, insert, update, delete on XH_OA.PMS_ADDN_CLASS_INFO to XH_COM;



--2024-02-19  PMS_EDUCATION_LIST新增 RECORD_DATA 
ALTER TABLE XH_OA.PMS_EDUCATION_LIST ADD RECORD_DATA   CLOB;
COMMENT ON COLUMN XH_OA.PMS_EDUCATION_LIST.RECORD_DATA IS '扩展字段和所属值';

--2024-02-19  PMS_EXCHANGE_LIST新增 RECORD_DATA 
ALTER TABLE XH_OA.PMS_EXCHANGE_LIST ADD RECORD_DATA   CLOB;
COMMENT ON COLUMN XH_OA.PMS_EXCHANGE_LIST.RECORD_DATA IS '扩展字段和所属值';

--2024-02-19  PMS_EXPATRIATE_LIST新增 RECORD_DATA 
ALTER TABLE XH_OA.PMS_EXPATRIATE_LIST ADD RECORD_DATA   CLOB;
COMMENT ON COLUMN XH_OA.PMS_EXPATRIATE_LIST.RECORD_DATA IS '扩展字段和所属值';

--2024-02-19  PMS_INTELLECTUAL_LIST新增 RECORD_DATA 
ALTER TABLE XH_OA.PMS_INTELLECTUAL_LIST ADD RECORD_DATA   CLOB;
COMMENT ON COLUMN XH_OA.PMS_INTELLECTUAL_LIST.RECORD_DATA IS '扩展字段和所属值';

--2024-02-19  PMS_PROFESSIONAL_LIST新增 RECORD_DATA 
ALTER TABLE XH_OA.PMS_PROFESSIONAL_LIST ADD RECORD_DATA   CLOB;
COMMENT ON COLUMN XH_OA.PMS_PROFESSIONAL_LIST.RECORD_DATA IS '扩展字段和所属值';

--2024-02-19  PMS_RESEARCH_LIST新增 RECORD_DATA 
ALTER TABLE XH_OA.PMS_RESEARCH_LIST ADD RECORD_DATA   CLOB;
COMMENT ON COLUMN XH_OA.PMS_RESEARCH_LIST.RECORD_DATA IS '扩展字段和所属值';

--2024-02-19  PMS_RESUME_LIST新增 RECORD_DATA 
ALTER TABLE XH_OA.PMS_RESUME_LIST ADD RECORD_DATA   CLOB;
COMMENT ON COLUMN XH_OA.PMS_RESUME_LIST.RECORD_DATA IS '扩展字段和所属值';

--2024-02-19  PMS_REWARD_LIST新增 RECORD_DATA 
ALTER TABLE XH_OA.PMS_REWARD_LIST ADD RECORD_DATA   CLOB;
COMMENT ON COLUMN XH_OA.PMS_REWARD_LIST.RECORD_DATA IS '扩展字段和所属值';


--2024-02-19  PMS_SKILL_CERTIFICATE_LIST新增 RECORD_DATA 
ALTER TABLE XH_OA.PMS_SKILL_CERTIFICATE_LIST ADD RECORD_DATA   CLOB;
COMMENT ON COLUMN XH_OA.PMS_SKILL_CERTIFICATE_LIST.RECORD_DATA IS '扩展字段和所属值';

--2024-02-19  PMS_SOCIAL_OFFICE_LIST新增 RECORD_DATA 
ALTER TABLE XH_OA.PMS_SOCIAL_OFFICE_LIST ADD RECORD_DATA   CLOB;
COMMENT ON COLUMN XH_OA.PMS_SOCIAL_OFFICE_LIST.RECORD_DATA IS '扩展字段和所属值';


--2024-02-19  PMS_STUDY_LIST新增 RECORD_DATA 
ALTER TABLE XH_OA.PMS_STUDY_LIST ADD RECORD_DATA   CLOB;
COMMENT ON COLUMN XH_OA.PMS_STUDY_LIST.RECORD_DATA IS '扩展字段和所属值';

--2024-02-19  PMS_TEACH_LIST新增 RECORD_DATA 
ALTER TABLE XH_OA.PMS_TEACH_LIST ADD RECORD_DATA   CLOB;
COMMENT ON COLUMN XH_OA.PMS_TEACH_LIST.RECORD_DATA IS '扩展字段和所属值';

--2024-02-19  PMS_THESIS_LIST新增 RECORD_DATA 
ALTER TABLE XH_OA.PMS_THESIS_LIST ADD RECORD_DATA   CLOB;
COMMENT ON COLUMN XH_OA.PMS_THESIS_LIST.RECORD_DATA IS '扩展字段和所属值';

--2024-02-19  PMS_TRAIN_LIST新增 RECORD_DATA 
ALTER TABLE XH_OA.PMS_TRAIN_LIST ADD RECORD_DATA   CLOB;
COMMENT ON COLUMN XH_OA.PMS_TRAIN_LIST.RECORD_DATA IS '扩展字段和所属值';





-- 2024-02-21 wgn 插入固定分类数据
INSERT INTO XH_OA.PMS_ADDN_CLASS_INFO (CLASS_ID, HOSPITAL_ID, CLASS_TYPE, CLASS_NAME, CLASS_SORT, CLASS_STATE, FORM_SETUP_ID, TABLE_SETUP_ID, CLASS_TABLE_NAME, CLASS_ADDN_CONFIG, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_TIME, REMARK)  VALUES('PMS_RESUME_LIST', '33A001', '0', '履历记录', '00000', '1', 'H810101', 'H810115CT', 'PMS_RESUME_LIST', '', '', '', '', '', '');
INSERT INTO XH_OA.PMS_ADDN_CLASS_INFO (CLASS_ID, HOSPITAL_ID, CLASS_TYPE, CLASS_NAME, CLASS_SORT, CLASS_STATE, FORM_SETUP_ID, TABLE_SETUP_ID, CLASS_TABLE_NAME, CLASS_ADDN_CONFIG, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_TIME, REMARK)  VALUES('PMS_PROFESSIONAL_LIST', '33A001', '0', '职称记录', '00012', '1', 'H810113', 'H810119CT', 'PMS_PROFESSIONAL_LIST', '', '', '', '', '', '');
INSERT INTO XH_OA.PMS_ADDN_CLASS_INFO (CLASS_ID, HOSPITAL_ID, CLASS_TYPE, CLASS_NAME, CLASS_SORT, CLASS_STATE, FORM_SETUP_ID, TABLE_SETUP_ID, CLASS_TABLE_NAME, CLASS_ADDN_CONFIG, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_TIME, REMARK)  VALUES('PMS_REWARD_LIST', '33A001', '0', '奖励记录', '00004', '1', 'H810105', 'H810122CT', 'PMS_REWARD_LIST', '', '', '', '', '', '');
INSERT INTO XH_OA.PMS_ADDN_CLASS_INFO (CLASS_ID, HOSPITAL_ID, CLASS_TYPE, CLASS_NAME, CLASS_SORT, CLASS_STATE, FORM_SETUP_ID, TABLE_SETUP_ID, CLASS_TABLE_NAME, CLASS_ADDN_CONFIG, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_TIME, REMARK)  VALUES('PMS_TEACH_LIST', '33A001', '0', '教学记录', '00005', '1', 'H810106', 'H810123CT', 'PMS_TEACH_LIST', '', '', '', '', '', '');
INSERT INTO XH_OA.PMS_ADDN_CLASS_INFO (CLASS_ID, HOSPITAL_ID, CLASS_TYPE, CLASS_NAME, CLASS_SORT, CLASS_STATE, FORM_SETUP_ID, TABLE_SETUP_ID, CLASS_TABLE_NAME, CLASS_ADDN_CONFIG, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_TIME, REMARK)  VALUES('PMS_STUDY_LIST', '33A001', '0', '进修记录', '00006', '1', 'H810107', 'H810116CT', 'PMS_STUDY_LIST', '', '', '', '', '', '');
INSERT INTO XH_OA.PMS_ADDN_CLASS_INFO (CLASS_ID, HOSPITAL_ID, CLASS_TYPE, CLASS_NAME, CLASS_SORT, CLASS_STATE, FORM_SETUP_ID, TABLE_SETUP_ID, CLASS_TABLE_NAME, CLASS_ADDN_CONFIG, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_TIME, REMARK)  VALUES('PMS_RESEARCH_LIST', '33A001', '0', '课题记录', '00007', '1', 'H810108', 'H810124CT', 'PMS_RESEARCH_LIST', '', '', '', '', '', '');
INSERT INTO XH_OA.PMS_ADDN_CLASS_INFO (CLASS_ID, HOSPITAL_ID, CLASS_TYPE, CLASS_NAME, CLASS_SORT, CLASS_STATE, FORM_SETUP_ID, TABLE_SETUP_ID, CLASS_TABLE_NAME, CLASS_ADDN_CONFIG, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_TIME, REMARK)  VALUES('PMS_THESIS_LIST', '33A001', '0', '论著记录', '00008', '1', 'H810109', 'H810127CT', 'PMS_THESIS_LIST', '', '', '', '', '', '');
INSERT INTO XH_OA.PMS_ADDN_CLASS_INFO (CLASS_ID, HOSPITAL_ID, CLASS_TYPE, CLASS_NAME, CLASS_SORT, CLASS_STATE, FORM_SETUP_ID, TABLE_SETUP_ID, CLASS_TABLE_NAME, CLASS_ADDN_CONFIG, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_TIME, REMARK)  VALUES('PMS_EDUCATION_LIST', '33A001', '0', '教育背景', '00009', '1', 'H810110', 'H810117CT', 'PMS_EDUCATION_LIST', '', '', '', '', '', '');
INSERT INTO XH_OA.PMS_ADDN_CLASS_INFO (CLASS_ID, HOSPITAL_ID, CLASS_TYPE, CLASS_NAME, CLASS_SORT, CLASS_STATE, FORM_SETUP_ID, TABLE_SETUP_ID, CLASS_TABLE_NAME, CLASS_ADDN_CONFIG, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_TIME, REMARK)  VALUES('PMS_SKILL_CERTIFICATE_LIST', '33A001', '0', '技能证书记录', '00010', '1', 'H810111', 'H810126CT', 'PMS_SKILL_CERTIFICATE_LIST', '', '', '', '', '', '');
INSERT INTO XH_OA.PMS_ADDN_CLASS_INFO (CLASS_ID, HOSPITAL_ID, CLASS_TYPE, CLASS_NAME, CLASS_SORT, CLASS_STATE, FORM_SETUP_ID, TABLE_SETUP_ID, CLASS_TABLE_NAME, CLASS_ADDN_CONFIG, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_TIME, REMARK)  VALUES('PMS_TRAIN_LIST', '33A001', '0', '培训记录', '00011', '1', 'H810112', 'H810118CT', 'PMS_TRAIN_LIST', '', '', '', '', '', '');
INSERT INTO XH_OA.PMS_ADDN_CLASS_INFO (CLASS_ID, HOSPITAL_ID, CLASS_TYPE, CLASS_NAME, CLASS_SORT, CLASS_STATE, FORM_SETUP_ID, TABLE_SETUP_ID, CLASS_TABLE_NAME, CLASS_ADDN_CONFIG, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_TIME, REMARK)  VALUES('PMS_SOCIAL_OFFICE_LIST', '33A001', '0', '社会任职', '00001', '1', 'H810102', 'H810121CT', 'PMS_SOCIAL_OFFICE_LIST', '', '', '', '', '', '');
INSERT INTO XH_OA.PMS_ADDN_CLASS_INFO (CLASS_ID, HOSPITAL_ID, CLASS_TYPE, CLASS_NAME, CLASS_SORT, CLASS_STATE, FORM_SETUP_ID, TABLE_SETUP_ID, CLASS_TABLE_NAME, CLASS_ADDN_CONFIG, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_TIME, REMARK)  VALUES('PMS_INTELLECTUAL_LIST', '33A001', '0', '知识产权', '00002', '1', 'H810103', 'H810125CT', 'PMS_INTELLECTUAL_LIST', '', '', '', '', '', '');
INSERT INTO XH_OA.PMS_ADDN_CLASS_INFO (CLASS_ID, HOSPITAL_ID, CLASS_TYPE, CLASS_NAME, CLASS_SORT, CLASS_STATE, FORM_SETUP_ID, TABLE_SETUP_ID, CLASS_TABLE_NAME, CLASS_ADDN_CONFIG, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_TIME, REMARK)  VALUES('PMS_EXCHANGE_LIST', '33A001', '0', '访问交流记录', '00003', '1', 'H810104', 'H810128CT', 'PMS_EXCHANGE_LIST', '', '', '', '', '', '');
INSERT INTO XH_OA.PMS_ADDN_CLASS_INFO (CLASS_ID, HOSPITAL_ID, CLASS_TYPE, CLASS_NAME, CLASS_SORT, CLASS_STATE, FORM_SETUP_ID, TABLE_SETUP_ID, CLASS_TABLE_NAME, CLASS_ADDN_CONFIG, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_TIME, REMARK)  VALUES('PMS_EXPATRIATE_LIST', '33A001', '0', '外派记录', '00013', '1', 'H810114', 'H810120CT', 'PMS_EXPATRIATE_LIST', '', '', '', '', '', '');
