﻿服务接口层
此层仅包含抽象接口约定信息,入参及出参必须明确指示类型
如:public List<BI_MENU_DICT> GetModuleMenu(string hospitalId, string moduleId);
不得使用装箱类型,如:public ResultDto GetModuleMenu(object p);
一个接口可以对用多个实现,程序启动时配置该接口应该使用哪个实现,所以接口设计时尽量考虑扩展性

建议按领域模型切分服务,如:
账号相关:IAccountService
基础数据相关:IBaseDataService
样本相关:ISampleService
	复杂度较高时可以拆分: ISampleInfoService ISampleInspectionService


ISystemService 内置了常用统一调用方法,注意:此方法请保持不变,由框架方统一维护,以供后续直接覆盖更新
IBaseDataServices 内置了所有基础数据调用方法,注意:此方法请保持不变,由余祥琼统一维护,以供后续直接覆盖更新