﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Dtos.Tag
{

    public class PropslistJson
    {
        /// <summary>
        /// 基本
        /// </summary>
        public string group { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string formId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string icon { get; set; }
        /// <summary>
        /// 单行文本
        /// </summary>
        public string formName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string dataType { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool isForm { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public List<Rules> rules { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public object wigetProps { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public object propslist { get; set; }
    }
}
