//using System;
//using Microsoft.EntityFrameworkCore;
//using System.ComponentModel.DataAnnotations;
//using System.ComponentModel.DataAnnotations.Schema;
//using SqlSugar;
//using H.Utility.SqlSugarInfra;

//namespace XH.H81.Models.ExternalEntity
//{
//    [Table("LIS6_INSTRUMENT_ITEM")]
//    [DBOwner("XH_SYS")]
//    public class LIS6_INSTRUMENT_ITEM
//	{
//		/// <summary>
//		/// 
//		/// </summary>
//		[Key]
//        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
//        //[Column("CHANNEL_ID")]
//		[Required(ErrorMessage = "不允许为空")]

//		[StringLength(20, ErrorMessage = "CHANNEL_ID长度不能超出20字符")]
//		//[Unicode(false)]
//		public string CHANNEL_ID { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("FIRST_RPERSON")]
//		[StringLength(50, ErrorMessage = "FIRST_RPERSON长度不能超出50字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? FIRST_RPERSON { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("LAST_MTIME")]
//		//[Unicode(false)]
//		public DateTime? LAST_MTIME { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("CHANNEL_STATE")]
//		[StringLength(20, ErrorMessage = "CHANNEL_STATE长度不能超出20字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? CHANNEL_STATE { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("UNIT_ID")]
//		[Required(ErrorMessage = "不允许为空")]

//		[StringLength(20, ErrorMessage = "UNIT_ID长度不能超出20字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string UNIT_ID { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("RATIO")]
//		[StringLength(20, ErrorMessage = "RATIO长度不能超出20字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? RATIO { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("TEST_METHOD")]
//		[StringLength(100, ErrorMessage = "TEST_METHOD长度不能超出100字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? TEST_METHOD { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("INSTRUMENT_ID")]
//		[Required(ErrorMessage = "不允许为空")]

//		[StringLength(20, ErrorMessage = "INSTRUMENT_ID长度不能超出20字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string INSTRUMENT_ID { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("INSTRUMENT_SNUM")]
//		[Required(ErrorMessage = "不允许为空")]

//		[StringLength(20, ErrorMessage = "INSTRUMENT_SNUM长度不能超出20字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string INSTRUMENT_SNUM { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("ITEM_NAME")]
//		[StringLength(50, ErrorMessage = "ITEM_NAME长度不能超出50字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? ITEM_NAME { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("CHANNEL")]
//		[StringLength(50, ErrorMessage = "CHANNEL长度不能超出50字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? CHANNEL { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("TEST_ITEM_UNIT")]
//		[StringLength(20, ErrorMessage = "TEST_ITEM_UNIT长度不能超出20字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? TEST_ITEM_UNIT { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("SAMPLE_CLASS")]
//		[StringLength(50, ErrorMessage = "SAMPLE_CLASS长度不能超出50字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? SAMPLE_CLASS { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("REMARK")]
//		[StringLength(200, ErrorMessage = "REMARK长度不能超出200字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? REMARK { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("ITEM_SORT")]
//		[StringLength(20, ErrorMessage = "ITEM_SORT长度不能超出20字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? ITEM_SORT { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("CHANNEL_TYPE")]
//		[StringLength(20, ErrorMessage = "CHANNEL_TYPE长度不能超出20字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? CHANNEL_TYPE { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("FIRST_RTIME")]
//		//[Unicode(false)]
//		public DateTime? FIRST_RTIME { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("LAST_MPERSON")]
//		[StringLength(50, ErrorMessage = "LAST_MPERSON长度不能超出50字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? LAST_MPERSON { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("TEST_DURATION")]
//		[StringLength(100, ErrorMessage = "TEST_DURATION长度不能超出100字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? TEST_DURATION { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("ITEM_CODE")]
//		[StringLength(50, ErrorMessage = "ITEM_CODE长度不能超出50字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? ITEM_CODE { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("ITEM_ID")]
//		[Required(ErrorMessage = "不允许为空")]

//		[StringLength(20, ErrorMessage = "ITEM_ID长度不能超出20字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string ITEM_ID { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("VALUE_TYPE")]
//		[StringLength(20, ErrorMessage = "VALUE_TYPE长度不能超出20字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? VALUE_TYPE { get; set; }


//	}
//}
