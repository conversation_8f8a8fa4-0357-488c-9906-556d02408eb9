﻿using Elastic.Clients.Elasticsearch;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XH.LAB.UTILS.Models;

namespace XH.H81.Models.Dtos.Pms
{
    public class GroupPersonTreeNodeDto // : AreaGroupTreeNode
    {
        public string PERSON_ID { get; set; }

        public string USER_ID { get; set; }

        public string PERSON_PHOTO_PATH { get; set; }

        public string PERSON_DUTIES_NAME { get; set; }

        public bool PERSON_IF_COMPLETE { get; set; }

        public string? HIS_NAME { get; set; }

        public string? SEX { get; set; }
    }
}
