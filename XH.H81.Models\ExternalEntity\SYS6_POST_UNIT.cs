//using System;
//using Microsoft.EntityFrameworkCore;
//using System.ComponentModel.DataAnnotations;
//using System.ComponentModel.DataAnnotations.Schema;
//using SqlSugar;
//using H.Utility.SqlSugarInfra;

//namespace XH.H81.Models.ExternalEntity
//{
//    [Table("SYS6_POST_UNIT")]
//    [DBOwner("XH_SYS")]
//    public class SYS6_POST_UNIT
//	{
//		/// <summary>
//		/// 
//		/// </summary>
//		[Key]
//        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
//        //[Column("UPOST_ID")]
//		[Required(ErrorMessage = "不允许为空")]

//		[StringLength(20, ErrorMessage = "UPOST_ID长度不能超出20字符")]
//		//[Unicode(false)]
//		public string UPOST_ID { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("POST_ID")]
//		[Required(ErrorMessage = "不允许为空")]

//		[StringLength(20, ErrorMessage = "POST_ID长度不能超出20字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string POST_ID { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("FIRST_RPERSON")]
//		[StringLength(50, ErrorMessage = "FIRST_RPERSON长度不能超出50字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? FIRST_RPERSON { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("LAST_MTIME")]
//		//[Unicode(false)]
//		public DateTime? LAST_MTIME { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("UNITCOM_ID")]
//		[StringLength(20, ErrorMessage = "UNITCOM_ID长度不能超出20字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? UNITCOM_ID { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("UNIT_ID")]
//		[Required(ErrorMessage = "不允许为空")]

//		[StringLength(20, ErrorMessage = "UNIT_ID长度不能超出20字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string UNIT_ID { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("UNIT_CLASS")]
//		[Required(ErrorMessage = "不允许为空")]

//		[StringLength(20, ErrorMessage = "UNIT_CLASS长度不能超出20字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string UNIT_CLASS { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("REMARK")]
//		[StringLength(200, ErrorMessage = "REMARK长度不能超出200字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? REMARK { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("FIRST_RTIME")]
//		//[Unicode(false)]
//		public DateTime? FIRST_RTIME { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("LAST_MPERSON")]
//		[StringLength(50, ErrorMessage = "LAST_MPERSON长度不能超出50字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? LAST_MPERSON { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("UNIT_NAME")]
//		[StringLength(50, ErrorMessage = "UNIT_NAME长度不能超出50字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? UNIT_NAME { get; set; }


//	}
//}
