﻿using Newtonsoft.Json;

namespace XH.H81.Models.Dtos.IoTDevices.Access;
/// <summary>
/// 门禁授权周计划
/// </summary>
public class AccessWeekPlanDto
{
    /// <summary>
    /// ID 编号，最好从1自增
    /// </summary>
    [JsonProperty("id")]
    public string Id { get; set; }
    /// <summary>
    /// 模板名称
    /// </summary>
    [JsonProperty("name")]
    public string Name { get; set; }
    /// <summary>
    /// 是否开启：true生效
    /// </summary>
    [JsonProperty("enable")]
    public bool Enable { get; set; }
    /// <summary>
    /// 周计划配置信息
    /// </summary>
    [JsonProperty("WeekPlanCfg")]
    public List<AccessWeekPlanDetailDto> WeekPlanDetail { get; set; }
}