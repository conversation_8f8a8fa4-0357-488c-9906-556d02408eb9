﻿using H.BASE;
using H.Utility;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using XH.H81.IServices;

namespace XH.H81.API.Controllers._demos
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize]
    [ApiExplorerSettings(GroupName = "DEMO")]
    public class TokenTestController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        private readonly ISystemService _systemService;
        //private IEasyCachingProvider redisC31;
        public TokenTestController(IConfiguration configuration, /*IEasyCachingProviderFactory cacheFactory,*/ ISystemService systemService)
        {

            _configuration = configuration;
            _systemService = systemService;
            // redisC31 = cacheFactory.GetCachingProvider("C31");
        }

        /// <summary>
        /// 模拟统一登录请求S01颁发token
        /// 注意:此方法仅演示使用,除特殊项目外生产环境严禁向外部暴露token获取接口
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IActionResult CreateToken(string user_no)
        {
            var tokenId = IDGenHelper.CreateGuid().ToString();
            var res = _systemService.GetIssueTokenInfo(user_no, tokenId, "H81", "");
            return Ok(res);
        }

        [AllowAnonymous]
        [HttpGet]
        public IActionResult ReNewToken(string expiredToken, string refreshToken)
        {
            var claim = this.User.ToClaimsDto();
            var res = _systemService.ReNewToken(expiredToken, refreshToken);
            return Ok(res);
        }


        [HttpGet]
        public IActionResult AuthorizeApi()
        {
            return Ok(new ResultDto() { data = "Auth OK!" });
        }

        /// <summary>
        /// 不需要角色验证的接口
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [NonRoleAuthorize]
        public IActionResult NonRoleAuthorizeTest()
        {
            return Ok();
        }


    }
}
