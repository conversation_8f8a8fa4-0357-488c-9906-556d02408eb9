﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Entities
{
        /// <summary>
        /// 岗位表 （通过PGROUP_ID限定所属专业组)
        /// </summary>
        [Table("SYS6_POST")]
        [DBOwner("XH_SYS")]
        public class SYS6_POST
        {
            /// <summary>
            /// 
            /// </summary>
            [SugarColumn(IsPrimaryKey = true)]

            public string POST_ID { get; set; }


            public string LAB_ID { get; set; }


            public string? FIRST_RPERSON { get; set; }



            public string? POST_NAME { get; set; }


            public DateTime? LAST_MTIME { get; set; }


            public string? POST_TYPE { get; set; }

            public string? POST_SORT { get; set; }


            public string? POST_STATE { get; set; }


            public string? REMARK { get; set; }


            public DateTime? FIRST_RTIME { get; set; }



            public string? LAST_MPERSON { get; set; }


            public string? POST_CLASS { get; set; }

            public string? POST_DESC { get; set; }

            public string? PGROUP_ID { get; set; }


            public string HOSPITAL_ID { get; set; }

            public string? POST_DUTY { get; set; }

            public string? POST_ULEVEL { get; set; }
            /// <summary>
            /// 是否规评：0否  1是
            /// </summary>
            public string IF_ASSESS { get; set; }

        }
}
