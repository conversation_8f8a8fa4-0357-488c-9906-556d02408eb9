﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Dtos.Pms
{
    public class RecordOperLogDto
    {
        /// <summary>
        /// 医疗机构ID
        /// </summary>
        public string? HOSPITAL_ID { get; set; }

        /// <summary>
        /// 记录ID
        /// </summary>
        public string? RECORD_ID { get; set; }

        /// <summary>
        /// 操作名称
        /// </summary>
        public string? OPER_NAME { get; set; }

        /// <summary>
        /// 操作人员
        /// </summary>
        public string? OPER_PERSON { get; set; }

        /// <summary>
        /// 操作时间
        /// </summary>
        public DateTime? OPER_TIME { get; set; }

        /// <summary>
        /// 操作意见
        /// </summary>
        public string? OPER_CAUSE { get; set; }

        /// <summary>
        /// 操作名称
        /// </summary>
        public bool? REVERSE_FLAG { get; set; }
    }
}
