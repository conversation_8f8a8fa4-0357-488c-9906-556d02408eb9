﻿using H.Utility;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XH.H81.Models.Dtos;
using XH.H81.Models.Dtos.EvaluatePlan;
using XH.H81.Models.Dtos.Pms;
using XH.H81.Models.Entities.EvaluatePlan;
using XH.LAB.UTILS.Models;
using XH.LAB.UTILS.Models.Dto;

namespace XH.H81.IServices
{
    public interface IEvaluatePlanService
    {
        List<OaEvaluatePlanDict> GetEvaluatePlanDict(string HOSPITAL_ID, string EPLAN_TYPE, string EPLAN_STATE, string LAB_PGROUP_ID, string SEARCH_KEY, List<string> EPLAN_ID_LIST = null);
        string SaveEvaluatePlanDict(OaEvaluatePlanDict dto, string hisName, string newEplanId = null);
        string EnableEvaluatePlanDict(string eplanId, bool isDisable, string hisName);
        string DeleteEvaluatePlanDict(string eplanId, string hisName);
        string UnDeleteEvaluatePlanDict(string eplanId, string hisName);
        bool SortEvaluatePlanDict(List<OaEvaluatePlanDict> eplans);
        List<SYS6_LIMIT_ROLE_DICT> GetLimitRoleDict(string HOSPITAL_ID, string LROLE_STATE, string SEARCH_KEY);
        string SaveLimitRoleDict(SYS6_LIMIT_ROLE_DICT dto, string hisName);
        string EnableLimitRoleDict(string lroleNo, bool isDisable, string hisName);
        string DeleteLimitRoleDict(string lroleNo, string hisName);
        bool SortLimitRoleDict(List<string> lroleNos);
        List<Sys6SoftModuleInfoDto> GetSoftModuleInfo(string hospitalId, string? searchKey);
        ModuleLimitRoleMenuListResult GetModuleLimitRoleMenuList(string hospitalId, string limitRoleId, string moduleId);
        List<MenuDto> GetLimitRoleMenuList(List<string> limitRoleIds);
        string DeleteLimitRoleMenu(string limitRoleId, string menuId);
        string SaveModuleLimitRoleMenuList(string limitRoleId, string moduleId, List<string> checkedMenuIds, string hisName);

        List<OaCertificateDict> GetCertificateDict(string hospitalId, string cerType, string state, string level, string ePlanFlag, string searchKey, List<string> CERTIFICATE_DID_LIST = null);
        List<OaCertificateDict> GetCertificateDictAndTag(string hospitalId, string cerType, string state, string ePlanFlag, string searchKey, string tagId);
        string SaveCertificateDict(OaCertificateDict dto, string hisName);
        string SaveCertificateDictAndTag(OaCertificateDict dto, string hisName);

        string DeleteCertificateDict(string certificateDid, string hisName);

        string EnableCertificateDict(string certificateDid, bool isDisable, string hisName);

        bool SortCertificateDict(List<string> certificateIds);

        List<OaEvaluatePlanSetup> GetEvaluatePlanSetupList(string HOSPITAL_ID, string EPLAN_TYPE, string PROLE_COM_ID, string EPLAN_SSTATE, string LAB_PGROUP_ID, string SEARCH_KEY, string? LIMIT_PROLE_TYPE, string? EPLAN_APPLY_TYPE, string? UNIT_ID, bool IS_INCLUDE_UNIT = true, bool IS_INCLUDE_EMPTY = false, List<string> EPLAN_ID_LIST = null, List<string> EPLAN_SID_LIST = null);

        string SaveEvaluatePlanSetup(List<OaEvaluatePlanSetup> dtos, string operateType, string hisName);

        OrgTree GetEvaluatePlanSetupPostTreeByPostRole(string hospitalId, string? areaId, string? labId, string? pgroupId, string? keyWord);

        OrgTree GetEvaluatePlanSetupPostTreeByPgroup(string eplanApplyType, string hospitalId, string? areaId, string? labId, string? pgroupId, string? keyWord);

        string SaveEvaluatePlanSetupApplyUnit(string? eplanSid, string eplanId, string eplanApplyType, List<string> unitIds, string hisName);
        string AddEvaluatePlanSetupApplyUnit(List<string> eplanIds, string eplanApplyType, List<string> unitIds, string hisName);

        List<PostInfoDto> GetUserPostInfoList(string personId, string? postClass, string? startDate, string? endDate, string keyWord);

        List<PostInfoDto> GetUserPostInfoListByPersonIds(List<string> personIds, string? postClass, string? startDate, string? endDate, string keyWord);

        /// <summary>
        /// 获取岗位角色规评结果
        /// </summary>
        /// <param name="personId"></param>
        /// <param name="postRoleId"></param>
        /// <returns></returns>
        public List<PersonPostRoleEPlanResult> GetPersonPostRoleEPlanResultList(string? personId, string postRoleId);

        public List<PersonPostInfoDto> BatchGetUserPostInfoList(List<string> personIds, string? userPostState, string? startDate, string? endDate, string? keyWord);

        /// <summary>
        /// 获取岗位授权信息
        /// </summary>
        /// <param name="hospitalId"></param>
        /// <param name="postId"></param>
        /// <param name="postRoleId"></param>
        /// <returns></returns>
        /// <exception cref="BizException"></exception>
        public ResultDto GetPostRoleAndUnitInfo(string? hospitalId, string postId, string postRoleId);

        /// <summary>
        /// 写入人员规评结果（一般在生效环节调用本方法）
        /// </summary>
        /// <param name="soa"></param>
        /// <param name="userId">用户ID</param>
        /// <param name="ePlanSid">规评方案主键</param>
        /// <param name="dataClass">业务数据类型，由业务系统自定义，比如“EXAM”</param>
        /// <param name="dataId">业务数据主键</param>
        /// <param name="result">业务数据评价结果：1-通过 2-未通过</param>
        /// <param name="affectDate">评价生效日期</param>
        /// <returns>是否成功</returns>
        /// <exception cref="Exception"></exception>
        public bool WriteEvaluatePlanUserResult(List<EvaluatePlanUserResultParm> users);

        public bool HandleEvaluatePlanCurrentChanged(List<string>? userIds = null, List<string>? eplanSids = null, string? operatorHisName = null);
        /// <summary>
        /// 创建规评方案变动事件
        /// </summary>
        /// <param name="parms"></param>
        /// <returns></returns>
        public bool CreateOaEvaluatePlanEvent(List<OaEvaluatePlanEventParm> parms);

        /// <summary>
        /// 处理规评变动事件
        /// </summary>
        /// <returns></returns>
        public bool HandleOaEvaluatePlanEvent();
    }
}
