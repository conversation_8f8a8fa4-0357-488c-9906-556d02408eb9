﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text;

namespace XH.H81.Models.Entities.Pms
{
    /// <summary>
    ///人员文件信息表
    /// </summary>
    [DBOwner("XH_OA")]
    public class PMS_PERSON_FILE
    {
        /// <summary>
        ///文件ID
        /// </summary>
        [Key]
        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string FILE_ID { get; set; }
        /// <summary>
        ///人员ID
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? PERSON_ID { get; set; }
        /// <summary>
        ///医疗机构ID
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? HOSPITAL_ID { get; set; }
        /// <summary>
        ///文件分类
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FILE_CLASS { get; set; }
        /// <summary>
        ///文件编号
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FILE_NUM { get; set; }
        /// <summary>
        ///文件别名
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FILE_CNAME { get; set; }
        /// <summary>
        ///文件排序号
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FILE_SORT { get; set; }
        /// <summary>
        ///文件名称
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FILE_NAME { get; set; }
        /// <summary>
        ///文件路径
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FILE_PATH { get; set; }
        /// <summary>
        ///文件后缀
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FILE_SUFFIX { get; set; }
        /// <summary>
        ///文件类型
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FILE_TYPE { get; set; }
        /// <summary>
        ///文件状态
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FILE_STATE { get; set; }

        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FILE_STATE_NAME { get; set; }
        /// <summary>
        ///首次登记人
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FIRST_RPERSON { get; set; }
        /// <summary>
        ///首次登记时间
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FIRST_RTIME { get; set; }
        /// <summary>
        ///最后修改人员
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? LAST_MPERSON { get; set; }
        /// <summary>
        ///最后修改时间
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? LAST_MTIME { get; set; }
        /// <summary>
        ///备注
        /// </summary>
        public string? REMARK { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? HTTP_FILE_PATH { get; set; }
       
    }
}
