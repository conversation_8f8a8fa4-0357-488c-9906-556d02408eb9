﻿using H.Utility;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using XH.H81.IServices.Pms;
using XH.H81.IServices;
using XH.H81.Models.Dtos.Pms;
using XH.H81.Models.Entities.EvaluatePlan;
using XH.LAB.UTILS.Models.Dto;
using XH.H81.Models.Dtos.EvaluatePlan;
using XH.H81.Models.Dtos;
using System.Security.Claims;
using XH.H81.API.Extensions;

namespace XH.H81.API.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [ApiExplorerSettings(GroupName = "人员规评相关")]
    [Authorize]
    public class EvaluatePlanController : ControllerBase
    {
        private IUserService _IUserService;
        private readonly IBaseDataServices _IBaseDataService;
        private readonly IEvaluatePlanService _IEvaluatePlanService;
        public EvaluatePlanController(IUserService iUserService, IBaseDataServices ibasedataService, IEvaluatePlanService iEvaluatePlanService)
        {
            _IUserService = iUserService;
            _IBaseDataService = ibasedataService;
            _IEvaluatePlanService = iEvaluatePlanService;
        }

        /// <summary>
        /// 获取规评方案列表
        /// </summary>
        /// <param name="EPLAN_TYPE">规评方案类型。1-学习 2-培训 3-评估 4-考试 5-资质证书</param>
        /// <param name="EPLAN_STATE">状态</param>
        /// <param name="LAB_PGROUP_ID">科室ID或专业组ID</param>
        /// <param name="SEARCH_KEY">关键字</param>
        /// <returns></returns>
        [HttpGet]

        [CustomResponseType(typeof(List<OaEvaluatePlanDict>))]
        public IActionResult GetEvaluatePlanDict(string HOSPITAL_ID, string? EPLAN_TYPE, string? EPLAN_STATE, string? LAB_PGROUP_ID, string? SEARCH_KEY)
        {
            var data = _IEvaluatePlanService.GetEvaluatePlanDict(HOSPITAL_ID, EPLAN_TYPE, EPLAN_STATE, LAB_PGROUP_ID, SEARCH_KEY);
            return Ok(data.ToResultDto());
        }   
        /// <summary>
        /// 保存规评方案
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult SaveEvaluatePlanDict(OaEvaluatePlanDict dto)
        {
            var claim = this.User.ToClaimsDto();
            var data = _IEvaluatePlanService.SaveEvaluatePlanDict(dto, claim.HIS_NAME);
            return Ok(data.ToResultDto());
        }
        /// <summary>
        /// 启用/禁用规评方案
        /// </summary>
        /// <param name="eplanId">规评方案ID</param>
        /// <param name="isDisable">是否禁用</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult EnableEvaluatePlanDict(string eplanId, bool isDisable)
        {
            var claim = this.User.ToClaimsDto();
            var data = _IEvaluatePlanService.EnableEvaluatePlanDict(eplanId, isDisable, claim.HIS_NAME);
            return Ok(data.ToResultDto());
        }
        /// <summary>
        /// 删除规评方案
        /// </summary>
        /// <param name="eplanId">规评方案ID</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult DeleteEvaluatePlanDict(string eplanId)
        {
            var claim = this.User.ToClaimsDto();
            var data = _IEvaluatePlanService.DeleteEvaluatePlanDict(eplanId, claim.HIS_NAME);
            return Ok(data.ToResultDto());
        }

        /// <summary>
        /// 规评方案排序
        /// </summary>
        /// <param name="eplanId">规评方案ID</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult SortEvaluatePlanDict(List<OaEvaluatePlanDict> eplans)
        {
            var data = _IEvaluatePlanService.SortEvaluatePlanDict(eplans);
            return Ok(data.ToResultDto());
        }


        /// <summary>
        /// 获取限权组合列表
        /// </summary>
        /// <param name="LROLE_TYPE">限权组合类型。1-学习 2-培训 3-评估 4-考试 5-资质证书</param>
        /// <param name="LROLE_STATE">状态</param>
        /// <param name="LAB_PGROUP_ID">科室ID或专业组ID</param>
        /// <param name="SEARCH_KEY">关键字</param>
        /// <returns></returns>
        [HttpGet]

        [CustomResponseType(typeof(List<SYS6_LIMIT_ROLE_DICT>))]
        public IActionResult GetLimitRoleDict(string HOSPITAL_ID, string? LROLE_STATE, string? SEARCH_KEY)
        {
            var data = _IEvaluatePlanService.GetLimitRoleDict(HOSPITAL_ID, LROLE_STATE, SEARCH_KEY);
            return Ok(data.ToResultDto());
        }
        /// <summary>
        /// 保存限权组合
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult SaveLimitRoleDict(SYS6_LIMIT_ROLE_DICT dto)
        {
            var claim = this.User.ToClaimsDto();
            var data = _IEvaluatePlanService.SaveLimitRoleDict(dto, claim.HIS_NAME);
            return Ok(data.ToResultDto());
        }
        /// <summary>
        /// 启用/禁用限权组合
        /// </summary>
        /// <param name="lroleNo">限权组合ID</param>
        /// <param name="isDisable">是否禁用</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult EnableLimitRoleDict(string lroleNo, bool isDisable)
        {
            var claim = this.User.ToClaimsDto();
            var data = _IEvaluatePlanService.EnableLimitRoleDict(lroleNo, isDisable, claim.HIS_NAME);
            return Ok(data.ToResultDto());
        }
        /// <summary>
        /// 删除限权组合
        /// </summary>
        /// <param name="lroleNo">限权组合ID</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult DeleteLimitRoleDict(string lroleNo)
        {
            var claim = this.User.ToClaimsDto();
            var data = _IEvaluatePlanService.DeleteLimitRoleDict(lroleNo, claim.HIS_NAME);
            return Ok(data.ToResultDto());
        }

        /// <summary>
        /// 限权组合排序
        /// </summary>
        /// <param name="lroleNos">限权组合ID</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult SortLimitRoleDict(List<string> lroleNos)
        {
            var data = _IEvaluatePlanService.SortLimitRoleDict(lroleNos);
            return Ok(data.ToResultDto());
        }

        /// <summary>
        /// 保存限权组合限权菜单
        /// </summary>
        /// <param name="limitRoleId"></param>
        /// <param name="moduleMenus"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult SaveModuleLimitRoleMenuList(string limitRoleId, string moduleId, List<string> checkedMenuIds)
        {
            var claim = this.User.ToClaimsDto();
            var data = _IEvaluatePlanService.SaveModuleLimitRoleMenuList(limitRoleId, moduleId, checkedMenuIds, claim.HIS_NAME);
            return Ok(data.ToResultDto());
        }

        /// <summary>
        /// 获取限权组合限权菜单
        /// </summary>
        /// <param name="hospitalId"></param>
        /// <param name="limitRoleId"></param>
        /// <param name="moduleId"></param>
        /// <returns></returns>
        [HttpGet]

        [CustomResponseType(typeof(ModuleLimitRoleMenuListResult))]
        public IActionResult GetModuleLimitRoleMenuList(string hospitalId, string limitRoleId, string moduleId)
        {
            var data = _IEvaluatePlanService.GetModuleLimitRoleMenuList(hospitalId, limitRoleId, moduleId);
            return Ok(data.ToResultDto());
        }

        /// <summary>
        /// 获取限权组合菜单
        /// </summary>
        /// <param name="limitRoleId"></param>
        /// <returns></returns>
        [HttpGet]

        [CustomResponseType(typeof(List<MenuDto>))]
        public IActionResult GetLimitRoleMenuList(string limitRoleId)
        {
            var data = _IEvaluatePlanService.GetLimitRoleMenuList(new List<string> { limitRoleId });
            return Ok(data.ToResultDto());
        }

        /// <summary>
        /// 获取多个限权组合菜单
        /// </summary>
        /// <param name="limitRoleIds"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult GetLimitRoleMenuListByIds(List<string> limitRoleIds)
        {
            var data = _IEvaluatePlanService.GetLimitRoleMenuList(limitRoleIds);
            return Ok(data.ToResultDto());
        }
        /// <summary>
        /// 删除限权组合菜单
        /// </summary>
        /// <param name="hospitalId"></param>
        /// <param name="limitRoleId"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult DeleteLimitRoleMenu(string limitRoleId, string menuId)
        {
            var data = _IEvaluatePlanService.DeleteLimitRoleMenu(limitRoleId, menuId);
            return Ok(data.ToResultDto());
        }

        /// <summary>
        /// 获取模块列表
        /// </summary>
        /// <param name="hospitalId"></param>
        /// <param name="searchKey"></param>
        /// <returns></returns>
        [HttpGet]
        [CustomResponseType(typeof(List<Sys6SoftModuleInfoDto>))]
        public IActionResult GetSoftModuleInfo(string hospitalId, string? searchKey)
        {
            var data = _IEvaluatePlanService.GetSoftModuleInfo(hospitalId, searchKey);
            return Ok(data.ToResultDto());
        }

        /// <summary>
        /// 获取规评设置列表
        /// </summary>
        /// <param name="HOSPITAL_ID"></param>
        /// <param name="EPLAN_TYPE">规评方案类型 (1-学习, 2-培训, 3-评估, 4-考试, 5-资质证书)</param>
        /// <param name="PROLE_COM_ID">限权组合ID</param>
        /// <param name="EPLAN_SSTATE">规评设置状态</param>
        /// <param name="LAB_PGROUP_ID">科室/专业组ID</param>
        /// <param name="SEARCH_KEY">搜索关键字</param>
        /// <param name="LIMIT_PROLE_TYPE">限权类型 (0-禁用, 1-限权, 2-停岗)</param>
        /// <param name="EPLAN_APPLY_TYPE">规评方案适用类型（注意值传英文编码）LAB-检验科室 ；PGROUP-检验专业组；MGROUP-管理专业组；POST-岗位；PROLE-岗位角色</param>
        /// <param name="UNIT_ID">适用单元ID</param>
        /// <param name="IS_INCLUDE_UNIT">是否加载适用范围单位ID（科室ID、管理专业组ID、检验专业组ID、岗位ID、岗位角色ID）</param>
        /// <param name="IS_INCLUDE_EMPTY">是否加载设置空白的规评方案</param>
        /// <returns></returns>
        [HttpGet]
        [CustomResponseType(typeof(List<OaEvaluatePlanSetup>))]
  
        public IActionResult GetEvaluatePlanSetupList(string HOSPITAL_ID, string? EPLAN_TYPE, string? PROLE_COM_ID, string? EPLAN_SSTATE, string? LAB_PGROUP_ID, string? SEARCH_KEY, string? LIMIT_PROLE_TYPE, string? EPLAN_APPLY_TYPE, string? UNIT_ID, bool? IS_INCLUDE_UNIT, bool? IS_INCLUDE_EMPTY)
        {
            var data = _IEvaluatePlanService.GetEvaluatePlanSetupList(HOSPITAL_ID, EPLAN_TYPE, PROLE_COM_ID, EPLAN_SSTATE, LAB_PGROUP_ID, SEARCH_KEY, LIMIT_PROLE_TYPE, EPLAN_APPLY_TYPE, UNIT_ID, IS_INCLUDE_UNIT ?? true, IS_INCLUDE_EMPTY ?? false);
            return Ok(data.ToResultDto());
        }


        /// <summary>
        /// 查询资质证书字典列表
        /// </summary>
        /// <param name="HOSPITAL_ID"></param>
        /// <param name="STATE_FLAG"></param>
        /// <param name="LEVEL"></param>
        /// <param name="EPLAN_FLAG"></param>
        /// <param name="SEARCH_KEY"></param>
        /// <returns></returns>
        [HttpGet]

        [CustomResponseType(typeof(List<OaCertificateDict>))]
        public IActionResult GetCertificateDict(string HOSPITAL_ID, string? CER_TYPE, string? STATE_FLAG, string? LEVEL, string? EPLAN_FLAG, string? SEARCH_KEY)
        {
            var result = _IEvaluatePlanService.GetCertificateDict(HOSPITAL_ID, CER_TYPE, STATE_FLAG, LEVEL, EPLAN_FLAG, SEARCH_KEY);
            return Ok(result.ToResultDto());
        }

        /// <summary>
        /// 获取维护数据项证书类型
        /// </summary>
        /// <param name="hospitalId"></param>
        /// <param name="cerType"></param>
        /// <param name="state"></param>
        /// <param name="ePlanFlag"></param>
        /// <param name="searchKey"></param>
        /// <param name="tagId"></param>
        /// <returns></returns>
        [HttpGet]
        [CustomResponseType(typeof(List<OaCertificateDict>))]
        public IActionResult GetCertificateDictAndTag(string HOSPITAL_ID, string? CER_TYPE, string? STATE_FLAG, string? EPLAN_FLAG, string? SEARCH_KEY, string? PERSON_TAG_ID)
        {
            var result = _IEvaluatePlanService.GetCertificateDictAndTag(HOSPITAL_ID, CER_TYPE, STATE_FLAG, EPLAN_FLAG, SEARCH_KEY, PERSON_TAG_ID);
            return Ok(result.ToResultDto());
        }

        /// <summary>
        /// 保存证书字典
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult SaveCertificateType(OaCertificateDict dto)
        {
            var claim = this.User.ToClaimsDto();
            var result = _IEvaluatePlanService.SaveCertificateDict(dto, claim.HIS_NAME);
            return Ok(result.ToResultDto());
        }

        /// <summary>
        /// 保存维护数据项证书类型
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult SaveCertificateDictAndTag(OaCertificateDict dto)
        {
            var claim = this.User.ToClaimsDto();
            var result = _IEvaluatePlanService.SaveCertificateDictAndTag(dto, claim.HIS_NAME);
            return Ok(result.ToResultDto());
        }

        /// <summary>
        /// 启用禁用证书
        /// </summary>
        /// <param name="CERTIFICATE_DID"></param>
        /// <param name="IS_DISABLE">是否禁用</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult EnableCertificateDict(string CERTIFICATE_DID, bool IS_DISABLE)
        {
            var claim = this.User.ToClaimsDto();
            var result = _IEvaluatePlanService.EnableCertificateDict(CERTIFICATE_DID, IS_DISABLE, claim.HIS_NAME);
            return Ok(result.ToResultDto());
        }
        /// <summary>
        /// 删除证书
        /// </summary>
        /// <param name="CERTIFICATE_DID"></param>
        /// <returns></returns>
        [HttpDelete]
        public IActionResult DeleteCertificateDict(string CERTIFICATE_DID)
        {
            var claim = this.User.ToClaimsDto();
            var result = _IEvaluatePlanService.DeleteCertificateDict(CERTIFICATE_DID, claim.HIS_NAME);
            return Ok(result.ToResultDto());
        }
        /// <summary>
        /// 证书排序
        /// </summary>
        /// <param name="sortedDataIds"></param>
        /// <returns></returns>
        [HttpPut]
        public IActionResult SortCertificateDict(List<string> sortedDataIds)
        {
            var claim = this.User.ToClaimsDto();
            var result = _IEvaluatePlanService.SortCertificateDict(sortedDataIds);
            return Ok(result.ToResultDto());
        }

        /// <summary>
        /// 编辑规评方案效期、预警时长等
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="operateType"></param>
        /// <param name="hisName"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult EditEvaluatePlanSetup(List<OaEvaluatePlanSetup> dtos)
        {
            string OperatType = "Edit";
            var claim = this.User.ToClaimsDto();
            var result = _IEvaluatePlanService.SaveEvaluatePlanSetup(dtos, OperatType, claim.HIS_NAME);
            return Ok(result.ToResultDto());
        }

        /// <summary>
        /// 规评方案设置限权
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="operateType"></param>
        /// <param name="hisName"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult LimitEvaluatePlanSetup(List<OaEvaluatePlanSetup> dtos)
        {
            string OperatType = "Limit";
            var claim = this.User.ToClaimsDto();
            var result = _IEvaluatePlanService.SaveEvaluatePlanSetup(dtos, OperatType, claim.HIS_NAME);
            return Ok(result.ToResultDto());
        }

        /// <summary>
        /// 规评方案设置停岗
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="operateType"></param>
        /// <param name="hisName"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult StopEvaluatePlanSetup(List<OaEvaluatePlanSetup> dtos)
        {
            string OperatType = "Stop";
            var claim = this.User.ToClaimsDto();
            var result = _IEvaluatePlanService.SaveEvaluatePlanSetup(dtos, OperatType, claim.HIS_NAME);
            return Ok(result.ToResultDto());
        }

        /// <summary>
        /// 规评方案设置撤销
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="operateType"></param>
        /// <param name="hisName"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult RevokeEvaluatePlanSetup(List<OaEvaluatePlanSetup> dtos)
        {
            string OperatType = "Revoke";
            var claim = this.User.ToClaimsDto();
            var result = _IEvaluatePlanService.SaveEvaluatePlanSetup(dtos, OperatType, claim.HIS_NAME);
            return Ok(result.ToResultDto());
        }

        /// <summary>
        /// 规评方案设置启用
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="operateType"></param>
        /// <param name="hisName"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult EnableEvaluatePlanSetup(List<OaEvaluatePlanSetup> dtos)
        {
            string OperatType = "Enable";
            var claim = this.User.ToClaimsDto();
            var result = _IEvaluatePlanService.SaveEvaluatePlanSetup(dtos, OperatType, claim.HIS_NAME);
            return Ok(result.ToResultDto());
        }

        /// <summary>
        /// 规评方案设置禁用
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="operateType"></param>
        /// <param name="hisName"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult DisableEvaluatePlanSetup(List<OaEvaluatePlanSetup> dtos)
        {
            string OperatType = "Disable";
            var claim = this.User.ToClaimsDto();
            var result = _IEvaluatePlanService.SaveEvaluatePlanSetup(dtos, OperatType, claim.HIS_NAME);
            return Ok(result.ToResultDto());
        }

        /// <summary>
        /// 按专业组获取规评设置的岗位信息树
        /// </summary>
        /// <param name="eplanSid">规评设置ID</param>
        /// <param name="eplanApplyType">规评设置适应类型</param>
        /// <param name="hospitalId"></param>
        /// <param name="areaId"></param>
        /// <param name="labId"></param>
        /// <param name="pgroupId"></param>
        /// <param name="keyWord">检索关键字</param>
        /// <returns></returns>
        [HttpGet]
        [CustomResponseType(typeof(OrgTree))]
        public IActionResult GetEvaluatePlanSetupPostTreeByPgroup(string eplanApplyType, string hospitalId, string? areaId, string? labId, string? pgroupId, string? keyWord)
        {
            var result = _IEvaluatePlanService.GetEvaluatePlanSetupPostTreeByPgroup(eplanApplyType, hospitalId, areaId, labId, pgroupId, keyWord);
            return Ok(result.ToResultDto());
        }

        /// <summary>
        /// 按岗位角色获取规评设置的岗位信息树
        /// </summary>
        /// <param name="eplanSid">规评设置ID</param>
        /// <param name="hospitalId"></param>
        /// <param name="areaId"></param>
        /// <param name="labId"></param>
        /// <param name="pgroupId"></param>
        /// <param name="pgroupId"></param>
        /// <param name="keyWord">检索关键字</param>
        /// <returns></returns>
        [HttpGet]
        [CustomResponseType(typeof(OrgTree))]
        public IActionResult GetEvaluatePlanSetupPostTreeByPostRole(string hospitalId, string? areaId, string? labId, string? pgroupId, string? keyWord)
        {
            var result = _IEvaluatePlanService.GetEvaluatePlanSetupPostTreeByPostRole(hospitalId, areaId, labId, pgroupId, keyWord);
            return Ok(result.ToResultDto());
        }

        /// <summary>
        /// 保存规评设置的岗位设置
        /// </summary>
        /// <param name="eplanSid">规评设置ID</param>
        /// <param name="eplanApplyType">规评设置适应类型</param>
        /// <param name="unitIds"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult SaveEvaluatePlanSetupApplyUnit(string? eplanSid, string eplanId, string eplanApplyType,[FromBody] List<string> unitIds)
        {
            var claim = this.User.ToClaimsDto();
            var result = _IEvaluatePlanService.SaveEvaluatePlanSetupApplyUnit(eplanSid, eplanId, eplanApplyType, unitIds, claim.HIS_NAME);
            return Ok(result.ToResultDto());
        }

        /// <summary>
        /// 增加规评方案的岗位设置
        /// </summary>
        /// <param name="eplanSid">规评设置ID</param>
        /// <param name="eplanApplyType">规评设置适应类型</param>
        /// <param name="unitIds"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult AddEvaluatePlanSetupApplyUnit(string eplanApplyType, string unitId,  [FromBody] List<string> eplanIds)
        {
            var claim = this.User.ToClaimsDto();
            var result = _IEvaluatePlanService.AddEvaluatePlanSetupApplyUnit(eplanIds, eplanApplyType, new List<string> { unitId }, claim.HIS_NAME);
            return Ok(result.ToResultDto());
        }

        /// <summary>
        /// 获取人员岗位信息
        /// </summary>
        /// <param name="personId">人员ID</param>
        /// <param name="postClass">岗位分类 1-管理岗 2-技术岗 3-业务岗</param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="keyWord"></param>
        /// <returns></returns>
        [HttpGet]
        [CustomResponseType(typeof(List<PostInfoDto>))]
        public IActionResult GetUserPostInfoList(string personId, string? postClass, string? startDate, string? endDate, string? keyWord)
        {
            var result = _IEvaluatePlanService.GetUserPostInfoList(personId, postClass, startDate, endDate, keyWord);
            return Ok(result.ToResultDto());
        }

        /// <summary>
        /// 按专业组批量获取人员岗位信息
        /// </summary>
        /// <param name="personIds">人员ID数组</param>
        /// <param name="postClass">岗位分类 1-管理岗 2-技术岗 3-业务岗</param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="keyWord"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult GetUserPostInfoListByPersonIds(List<string> personIds, string? postClass, string? startDate, string? endDate, string? keyWord)
        {
            var result = _IEvaluatePlanService.GetUserPostInfoListByPersonIds(personIds, postClass, startDate, endDate, keyWord);
            return Ok(result.ToResultDto());
        }

        /// <summary>
        /// 批量获取岗位记录信息
        /// </summary>
        /// <param name="personIds"></param>
        /// <param name="userPostState">  "0"-"停岗","1"-"在用","2"-"结束"</param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="keyWord"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult BatchGetUserPostInfoList(List<string> personIds, string? userPostState, string? startDate, string? endDate, string? keyWord)
        {
            var result = _IEvaluatePlanService.BatchGetUserPostInfoList(personIds, userPostState, startDate, endDate, keyWord);
            return Ok(result.ToResultDto());
        }

        /// <summary>
        /// 获取岗位角色规评结果
        /// </summary>
        /// <param name="personId"></param>
        /// <param name="postRoleId"></param>
        /// <returns></returns>
        [HttpGet]
        [CustomResponseType(typeof(List<PersonPostRoleEPlanResult>))]
        public IActionResult GetPersonPostRoleEPlanResultList(string? personId, string postRoleId)
        {
            var result = _IEvaluatePlanService.GetPersonPostRoleEPlanResultList(personId, postRoleId);
            return Ok(result.ToResultDto());
        }

        /// <summary>
        /// 获取岗位授权信息
        /// </summary>
        /// <param name="hospitalId"></param>
        /// <param name="postId"></param>
        /// <param name="postRoleId"></param>
        /// <returns></returns>
        /// <exception cref="BizException"></exception>
        [HttpGet]
        public IActionResult GetPostRoleAndUnitInfo(string? hospitalId, string postId, string postRoleId)
        {
            var result = _IEvaluatePlanService.GetPostRoleAndUnitInfo(hospitalId, postId, postRoleId);
            return Ok(result);
        }

        /// <summary>
        /// 写入人员规评结果（一般在生效环节调用本方法）
        /// </summary>
        /// <param name="soa"></param>
        /// <param name="userId">用户ID</param>
        /// <param name="ePlanSid">规评方案主键</param>
        /// <param name="dataClass">业务数据类型，由业务系统自定义，比如“EXAM”</param>
        /// <param name="dataId">业务数据主键</param>
        /// <param name="result">业务数据评价结果：1-通过 2-未通过</param>
        /// <param name="affectDate">评价生效日期</param>
        /// <returns>是否成功</returns>
        /// <exception cref="Exception"></exception>
        [HttpPost]
        public IActionResult WriteEvaluatePlanUserResult(List<EvaluatePlanUserResultParm> users)
        {
            var result = _IEvaluatePlanService.WriteEvaluatePlanUserResult(users);
            return Ok(result.ToResultDto());
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userIds"></param>
        /// <param name="eplanSids"></param>
        /// <param name="operatorHisName"></param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult HandleEvaluatePlanCurrentChanged(string? userIds, string? eplanSids)
        {
            List<string> userList = userIds?.Split('+').ToList();
            List<string> eplanSidList = eplanSids?.Split('+').ToList();
            string operatorHisName = this.User.ToClaimsDto()?.HIS_NAME;
            var result = _IEvaluatePlanService.HandleEvaluatePlanCurrentChanged(userList, eplanSidList, operatorHisName);
            return Ok(result.ToResultDto());
        }


        /// <summary>
        /// 创建规评方案变动事件
        /// </summary>
        /// <param name="parms"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult CreateOaEvaluatePlanEvent(List<OaEvaluatePlanEventParm> parms)
        {
            var result = _IEvaluatePlanService.CreateOaEvaluatePlanEvent(parms);
            return Ok(result.ToResultDto());
        }
    }
}
