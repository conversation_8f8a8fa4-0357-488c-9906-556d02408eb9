﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Entities.Eguard
{
    /// <summary>
    /// 科外访问人员表
    /// </summary>
    [DBOwner("XH_OA")]
    public class PMS_VISITOR_INFO
    {
        /// <summary>
        /// 人员ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string VISITOR_ID { get; set; }
        /// <summary>
        /// 医疗机构ID
        /// </summary>
        public string HOSPITAL_ID { get; set; }
        /// <summary>
        /// 微信账户标识
        /// </summary>
        public string? OPEN_ID { get; set; }
        /// <summary>
        /// 姓名
        /// </summary>
        public string? VISITOR_NAME { get; set; }
        /// <summary>
        /// 手机
        /// </summary>
        public string? PHONE { get; set; }
        /// <summary>
        /// 图片地址
        /// </summary>
        public string? PHOTO_PATH { get; set; }
        /// <summary>
        /// 工作单位
        /// </summary>
        public string? WORK_UNIT { get; set; }
        /// <summary>
        /// 扩展字段
        /// </summary>
        public string? VISITOR_JSON { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public string? VISITOR_STATE { get; set; }
        public string? FIRST_RPERSON { get; set; }
        public DateTime? FIRST_RTIME { get; set; }
        public string? LAST_MPERSON { get; set; }
        public DateTime? LAST_MTIME { get; set; }
        public string? REMARK { get; set; }
    }
}
