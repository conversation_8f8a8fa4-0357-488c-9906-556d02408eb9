﻿using AutoMapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XH.H81.Models.Dtos.Tag;
using XH.H81.Models.Entities.Pms;
using XH.H81.Models.Exam;

namespace XH.H81.Models.Dtos.Pms
{
    [AutoMap(typeof(PMS_ADDN_CLASS_INFO), ReverseMap = true)]
    public class PmsClassInfoDto
    {
        public string MODULE_CLASSID { get; set; }
        //
        public string? CLASS_ID { get; set; }
        //
        public string? CLASS_TYPE { get; set; }
        //
        public string? HOSPITAL_ID { get; set; }
        //
        public string? MODULE_ID { get; set; }
        //
        public string? MODULE_NAME { get; set; }
        //
        public string? CLASS_NAME { get; set; }
        //
        public string? CLASS_SORT { get; set; }
        //
        public string? IF_BROWSE { get; set; }
        //
        public string? CLASS_DES { get; set; }
        //
        public string? ARCHIVE_TABLE { get; set; }
        //
        public string? CLASS_STATE { get; set; }
        //
        public string? FIRST_RPERSON { get; set; }
        //
        public string? FIRST_RTIME { get; set; }
        //
        public string? LAST_MPERSON { get; set; }
        //
        public string? LAST_MTIME { get; set; }
        //
        public string? REMARK { get; set; }
        /// <summary>
        /// 是否支持附件上传：'1'是支持，'0'是不支持
        /// </summary>
        public string? IF_UPLOAD_FILE { get; set; }

        /// <summary>
        /// 是否支持附件上传：'1'是支持，'0'是不支持
        /// </summary>
        public string? IF_MUST_UPLOAD_FILE { get; set; }
        /// <summary>
        /// 是否无数据
        /// </summary>
        public bool? IS_EMPTY { get; set; }
        /// <summary>
        /// 是否存在驳回
        /// </summary>
        public bool? HAS_REJECT { get; set; }
        /// <summary>
        /// 是否过期
        /// </summary>
        public bool? HAS_DATE_EXPIRED { get; set; }
        /// <summary>
        /// 是否隐藏 0-否 1-是； 默认否
        /// </summary>
        public int IS_HIDE { get; set; }

        /// <summary>
        /// 是否可编辑属性 0-否 1-是； 默认是
        /// </summary>
        public int IS_PROP_EDITABLE { get; set; }

        /// <summary>
        /// 是否有审核流程 0-否 1-是； 默认否 表示审核页面是否可见该分类
        /// </summary>
        public int IS_AUDITABLE { get; set; }

        /// <summary>
        /// 子分类
        /// </summary>
        public List<PmsClassInfoDto> CHILDREN { get; set; }=new List<PmsClassInfoDto>();

        /// <summary>
        ///  分类归属 0-普通 1-健康档案
        /// </summary>
        public string CLASS_KIND { get; set; }
        /// <summary>
        ///  分类归属 0-普通 1-健康档案
        /// </summary>
        public string CLASS_KIND_NAME { get; set; }
        /// <summary>
        ///  生安标志
        /// </summary>
        public string SMBL_FLAG { get; set; } = "0";

        /// <summary>
        ///  记录生安标记
        /// </summary>
        public string SMBL_REC_FLAG { get; set; }

        public List<PmsPersonTagDictDto>? TAGS { get; set; } = new List<PmsPersonTagDictDto>();
    }
}
