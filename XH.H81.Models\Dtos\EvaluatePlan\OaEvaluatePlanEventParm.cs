﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Dtos.EvaluatePlan
{
    public class OaEvaluatePlanEventParm
    {

        /// <summary>
        /// 医疗机构ID
        /// </summary>
        public string HOSPITAL_ID { get; set; }

        /// <summary>
        /// 事件类型：1-方案修改 2-人员结果变动 3-定时作业 4-岗位角色变动 5-人员的岗位角色变动
        /// </summary>
        public string EVENT_TYPE { get; set; }

        /// <summary>
        /// 规评方案ID
        /// </summary>
        public string? EPLAN_ID { get; set; }

        /// <summary>
        /// 用户ID串
        /// </summary>
        public string? USER_SID { get; set; }

        /// <summary>
        /// 变更来源。方案修改时：填规评方案设置表（OA_EVALUATE_PLAN_SETUP）主键； 人员结果变动时：填规评方案用户结果表（OA_EVALUATE_PLAN_USER）主键
        /// </summary>
        public string? SOURCE_ID { get; set; }

        /// <summary>
        /// 事件信息
        /// </summary>
        public string? EVENT_INFO { get; set; }

        /// <summary>
        /// 调整人员
        /// </summary>
        public string? CHANGE_PERSON { get; set; }

        /// <summary>
        /// 调整时间
        /// </summary>
        public DateTime CHANGE_TIME { get; set; }

    }
}
