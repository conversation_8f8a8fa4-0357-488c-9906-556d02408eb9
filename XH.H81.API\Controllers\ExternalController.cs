﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using XH.H81.IServices.Pms;
using XH.H81.IServices;
using H.Utility;
using XH.H81.Services;
using XH.H81.Models.Dtos.External;

namespace XH.H81.API.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [ApiExplorerSettings(GroupName = "对外接口")]
    [Authorize]
    public class ExternalController : ControllerBase
    {
        private IUserService _IUserService;
        private readonly IExternalService _IExternalService;
        private readonly IBaseDataServices _IBaseDataService;
        private readonly IPmsService _IPmsService;
        private readonly IHttpContextAccessor _httpContext;
        public ExternalController(IUserService iUserService, IBaseDataServices ibasedataService, IPmsService iPmsService, IHttpContextAccessor ihttpContext, IExternalService ExternalService)
        {
            _IUserService = iUserService;
            _IBaseDataService = ibasedataService;
            _IPmsService = iPmsService;
            _httpContext = ihttpContext;
            _IExternalService = ExternalService;
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="parm"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult Test(BloodCollectPersonInput parm)
        {
            return Ok("".ToResultDto());
        }

        /// <summary>
        /// 保存采血外援人员信息
        /// </summary>
        /// <param name="hospitalId"></param>
        /// <param name="userName"></param>
        /// <param name="hisId"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult RegisterBloodCollectSupportPerson(string hospitalId, string userName, string hisId)
        {
            var result = _IExternalService.RegisterBloodCollectSupportPerson(hospitalId, userName, hisId);
            return Ok(result.ToResultDto());
        }

        /// <summary>
        /// 新增或保存采血外援人员
        /// </summary>
        /// <param name="parm"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult SaveBloodCollectPerson(Dictionary<string, object> parm)
        {
            var result = _IExternalService.SaveBloodCollectPerson(parm);
            return Ok(result.ToResultDto());
        }

        /// <summary>
        ///  获取采血外援人员列表
        /// </summary>
        /// <param name="parm"></param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetBloodCollectPersonList(string searchKey)
        {
            var result = _IExternalService.GetBloodCollectPersonList(searchKey, null);
            return Ok(result.ToResultDto());
        }

        /// <summary>
        /// 上传采血外援人员证件文件
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadBloodCollectPersonFile([FromForm] BloodCollectUploadFileInput file)
        {
            var result = _IExternalService.UploadBloodCollectPersonFile(file);
            return Ok(result.ToResultDto());
        }
        /// <summary>
        ///  删除采血外援人员证件文件
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult DeleteBloodCollectPersonFile(BloodCollectUploadFileOutput file)
        {
            var result = _IExternalService.DeleteBloodCollectPersonFile(file);
            return Ok(result.ToResultDto());
        }


    }
}