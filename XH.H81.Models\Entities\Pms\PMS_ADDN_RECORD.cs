﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using H.Utility.SqlSugarInfra;
using SqlSugar;
namespace XH.H81.Models.Entities.Pms
{
    /// <summary>
    /// 附加分类记录表
    /// </summary>
    [DBOwner("XH_OA")]
    public class PMS_ADDN_RECORD
    {
        /// <summary>
        /// 附加号
        /// </summary>
        [SugarColumn(/*IsIdentity = true,*/ IsPrimaryKey = true)]
        public string RECORD_ID { get; set; }

        /// <summary>
        /// 分类id,对应PMS_ADDN_CLASS_INFO的主键ID
        /// </summary>
        public string CLASS_ID { get; set; }

        /// <summary>
        /// 人事档案id
        /// </summary>
        public string PERSON_ID { get; set; }

        /// <summary>
        /// 首次记录人
        /// </summary>
        public string FIRST_RPERSON { get; set; }

        /// <summary>
        /// 首次记录时间
        /// </summary>
        public DateTime? FIRST_RTIME { get; set; }

        /// <summary>
        /// 最后操作人
        /// </summary>
        public string LAST_MPERSON { get; set; }

        /// <summary>
        /// 最后操作时间
        /// </summary>
        public DateTime? LAST_TIME { get; set; }

        /// <summary>
        /// 审核人
        /// </summary>
        public string CHECK_PERSON { get; set; }

        /// <summary>
        /// 审核状态
        /// </summary>
        public string CHECK_STATE { get; set; }

        /// <summary>
        /// 提交者选择的审核人
        /// </summary>
        public string SELECT_CHECK_PERSON { get; set; }

        /// <summary>
        /// 审核时间
        /// </summary>
        public DateTime? CHECK_TIME { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string REMARK { get; set; }

        /// <summary>
        /// 院区id
        /// </summary>
        public string HOSPITAL_ID { get; set; }

        /// <summary>
        /// 状态;1启用 0禁用
        /// </summary>
        public string RECORD_STATE { get; set; }

        /// <summary>
        /// 字段和所属值
        /// </summary>
        public string RECORD_DATA { get; set; }

        /// <summary>
        /// 附件ID拼接
        /// </summary>
        public string RECORD_AFFIX { get; set; }
        /// <summary>
        /// 排序号
        /// </summary>
        public string RECORD_SORT { get; set; }
        /// <summary>
        /// 生安标志
        /// </summary>
        public string SMBL_FLAG { get; set; } = "0";
    }
}
