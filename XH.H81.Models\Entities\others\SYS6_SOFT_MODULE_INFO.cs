﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Entities.others
{
    [DBOwner("XH_SYS")]
    public class SYS6_SOFT_MODULE_INFO
    {
        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string MODULE_ID { get; set; }
        public string MODULE_NO { get; set; }
        public string MODULE_CODE { get; set; }
        public string MODULE_NAME { get; set; }
        public string SOFT_KEY { get; set; }
        public string MODULE_STATE { get; set; }
        //public string FIRST_RPERSON { get; set; }
        //public DateTime FIRST_RTIME { get; set; }
        //public string LAST_MPERSON { get; set; }
        //public DateTime LAST_MTIME { get; set; }
        //public string REMARK { get; set; }
        //public string REGISTER_PERSON { get; set; }
        //public string REGISTER_TIME { get; set; }
        //public string PATIENT_CODE { get; set; }
        //public string MODULE_CNAME { get; set; }
        //public string PROGRAM_TYPE { get; set; }
        //public string MODULE_PATH { get; set; }
        //public string DATABASE_ID { get; set; }
        //public string IIS_SERVER { get; set; }
        //public string MODULE_PERSON { get; set; }
        //public string MODULE_TIME { get; set; }
        //public string MODULE_VER { get; set; }
        //public string MODULE_CLASS { get; set; }
        //public string ROLE_TYPE { get; set; }
        //public string PROGRAM_URL { get; set; }
        //public string SYSTEM_ID { get; set; }
        //public string MODULE_PORT { get; set; }
        //public string INSTANCE_ID { get; set; }
        //public string CALL_MODULE { get; set; }
        //public string IF_MAIN_MODULE { get; set; }
        //public string IF_MAIN_PROGRAM { get; set; }
        //public string IF_REGISTER { get; set; }
        //public string REGISTER_STATE { get; set; }
        //public string SETUP_UNIT { get; set; }
        //public string TWO_FACTOR_MODE { get; set; }
        //public string IF_USE_ELK { get; set; }
        //public string DATABASE_ID2 { get; set; }
        //public string XT_MENU_CLASS { get; set; }
        //public decimal DATABASE_NUM { get; set; }
        //public string IF_SPEC_LINK { get; set; }
        //public string ONLYREAD_SERVER { get; set; }
        //public string IF_SON_ADD { get; set; }
        //public string IF_SETUP { get; set; }
        //public string IF_THIRD { get; set; }
        //public string IF_REDIS { get; set; }
        //public string MODULE_ICON { get; set; }
        //public string CLIENT_SETUP { get; set; }
        //public string STAT_REPORT { get; set; }
        //public string APPLICATION_CODE { get; set; }
        //public string WIDTH_HEIGHT { get; set; }
    }
}
