﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using SqlSugar.DbConvert;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Exam
{
    /// <summary>
    /// 试卷信息表
    /// </summary>
    [DBOwner("XH_OA")]
    public class EMP_PAPER_INFO
    {
        /// <summary>
        /// 试卷ID[P00000001]
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Key]
        public string? PAPER_ID { get; set; }
        /// <summary>
        /// 医疗机构ID
        /// </summary>
        public string? HOSPITAL_ID { get; set; }
        /// <summary>
        /// 专业组ID
        /// </summary>
        public string? PGROUP_ID { get; set; }
        /// <summary>
        /// 试卷名称
        /// </summary>
        public string? PAPER_NAME { get; set; }
        /// <summary>
        /// 试卷编号
        /// </summary>
        public string? PAPER_NO { get; set; }
        /// <summary>
        /// 试卷排序号
        /// </summary>
        public string? PAPER_SORT { get; set; }
        /// <summary>
        /// 试卷分类[1练习2考试]
        /// </summary>
        public string? PAPER_CLASS { get; set; }

        /// <summary>
        /// 人员评估 评估方案ID 默认为0 代表考试系统
        /// </summary>
        public string? SOURCE_INFO_ID { get; set; }
        /// <summary>
        /// 试卷难度系数[1 1级★2 2级★★3 3级★★★4 4级★★★★5 5级★★★★★固定基础数据]
        /// </summary>
        public string? PAPER_LEVEL { get; set; }
        /// <summary>
        /// 答题时长[分钟]
        /// </summary>
        public int? ANSWER_DURATION { get; set; }
        /// <summary>
        /// 试卷关键字
        /// </summary>
        public string? PAPER_KEYWORD { get; set; }
        /// <summary>
        /// 评分规则
        /// </summary>
        public string? SCORING_RULES { get; set; }
        /// <summary>
        /// 试卷总分
        /// </summary>
        public decimal? PAPER_SCORE { get; set; }
        /// <summary>
        /// 及格分数
        /// </summary>
        public decimal? PASSING_GRADE { get; set; }
        /// <summary>
        /// 试卷总题
        /// </summary>
        public int? PAPER_ITEMNUM { get; set; }
        /// <summary>
        /// 审核者
        /// </summary>
        public string? CHECK_PERSON { get; set; }
        /// <summary>
        /// 审核时间
        /// </summary>
        public DateTime? CHECK_TIME { get; set; }
        /// <summary>
        /// 批准者
        /// </summary>
        public string? APPROVAL_PERSON { get; set; }
        /// <summary>
        /// 批准日期
        /// </summary>
        public DateTime? APPROVAL_DATE { get; set; }
        /// <summary>
        /// 作废人员
        /// </summary>
        public string? CANCEL_PERSON { get; set; }
        /// <summary>
        /// 作废时间
        /// </summary>
        public DateTime? CANCEL_TIME { get; set; }
        /// <summary>
        /// 试卷题目
        /// </summary>
        [SugarColumn(SqlParameterDbType = typeof(NClobPropertyConvert))]
        public string? PAPER_QUESTION { get; set; }
        /// <summary>
        /// 试卷状态[0禁用1启用]
        /// </summary>
        public string? PAPER_STATE { get; set; }
        /// <summary>
        /// 首次登记人
        /// </summary>
        public string? FIRST_RPERSON { get; set; }
        /// <summary>
        /// 首次登记时间
        /// </summary>
        public DateTime? FIRST_RTIME { get; set; }
        /// <summary>
        /// 最后修改人员
        /// </summary>
        public string? LAST_MPERSON { get; set; }
        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime? LAST_MTIME { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string? REMARK { get; set; }

        /// <summary>
        /// 所属科室id
        /// </summary>
        public string? EMP_LAB_ID { get; set; }


        /// <summary>
        /// 所属类型 0科室 1专业组 2机构 3管理专业组 4备案实验室
        /// </summary>
        public string? LAB_PGROUP_TYPE { get; set; }


        /// <summary>
        /// 生安标志
        /// </summary>
        public string? SMBL_FLAG { get; set; }
    }
}
