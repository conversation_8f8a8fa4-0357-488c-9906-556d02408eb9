#See https://aka.ms/containerfastmode to understand how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:6.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:6.0 AS build
WORKDIR /src
COPY ["XH.H81.API/XH.H81.API.csproj", "XH.H81.API/"]
RUN dotnet restore "XH.H81.API/XH.H81.API.csproj"
COPY . .
WORKDIR "/src/XH.H81.API"
RUN dotnet build "XH.H81.API.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "XH.H81.API.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "XH.H81.API.dll"]