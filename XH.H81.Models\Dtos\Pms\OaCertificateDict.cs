﻿using AutoMapper;
using AutoMapper.Configuration.Annotations;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using XH.H81.Models.Entities.EvaluatePlan;
using XH.LAB.UTILS.Models.Entites;
using OA_CERTIFICATE_DICT = XH.H81.Models.Entities.EvaluatePlan.OA_CERTIFICATE_DICT;

namespace XH.H81.Models.Dtos
{
    [AutoMap(typeof(OA_CERTIFICATE_DICT), ReverseMap = true)]
    public class OaCertificateDict
    {
        /// <summary>
        /// 证书字典ID
        /// </summary>
        public string? CERTIFICATE_DID { get; set; }

        /// <summary>
        /// 医疗机构ID
        /// </summary>
        public string HOSPITAL_ID { get; set; }

        /// <summary>
        /// 证书名称
        /// </summary>
        public string? CERTIFICATE_DNAME { get; set; }
        /// <summary>
        /// 排序号
        /// </summary>
        public string? CERTIFICATE_DSORT { get; set; }

        /// <summary>
        /// 证书类型，固定基础数据
        /// </summary>
        public string CERTIFICATE_DTYPE { get; set; }
        /// <summary>
        /// 证书类型，固定基础数据
        /// </summary>
        public string? CERTIFICATE_DTYPE_NAME { get; set; }
        /// <summary>
        /// 证书类型顺序，固定基础数据
        /// </summary>
        public string? CERTIFICATE_DTYPE_SORT { get; set; }

        /// <summary>
        /// 证书级别
        /// </summary>
        public string? CERTIFICATE_DLEVEL { get; set; }

        /// <summary>
        /// 状态：0-禁用, 1-在用, 2-删除
        /// </summary>
        public string? CERTIFICATE_DSTATE { get; set; }

        /// <summary>
        /// 规评标志：0-否 1-是
        /// </summary>
        public string? EPLAN_FLAG { get; set; }

        /// <summary>
        /// 首次登记人
        /// </summary>
        public string? FIRST_RPERSON { get; set; }

        /// <summary>
        /// 首次登记时间
        /// </summary>
        public DateTime? FIRST_RTIME { get; set; }

        /// <summary>
        /// 最后修改人员
        /// </summary>
        public string? LAST_MPERSON { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime? LAST_MTIME { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? REMARK { get; set; }


        /// <summary>
        /// 有效日期数据
        /// </summary>
        [RegularExpression(@"^\-?\d+$", ErrorMessage = "必须为整数")]
        public string? CER_LIFE_VALUE { get; set; }

        /// <summary>
        /// 有效日期单位：M-月 Q-季度 Y-年
        /// </summary>
        public string? CER_LIFE_UNIT { get; set; }

        ///// <summary>
        ///// 失效日期，空值表示永久
        ///// </summary>
        //public DateTime? EXPIRE_DATE { get; set; }


        /// <summary>
        /// 人员标签ID
        /// </summary>
        public List<string>? PERSON_TAG_IDS { get; set; }
        /// <summary>
        /// 人员标签名称
        /// </summary>
        public string? PERSON_TAG_NAMES { get; set; }

    }

}
