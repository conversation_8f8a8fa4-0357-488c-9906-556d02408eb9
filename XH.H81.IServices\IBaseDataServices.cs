﻿using H.Utility;
using Microsoft.AspNetCore.Http;
using XH.H81.Models.Dtos;
using XH.H81.Models.Dtos.Pms;
using XH.H81.Models.Dtos.Template;
using XH.H81.Models.Entities.Exam;
using XH.H81.Models.Entities.Pms;
using XH.H81.Models.ExternalEntity;
using XH.LAB.UTILS.Models;
using XH.LAB.UTILS.Models.Entites;

namespace XH.H81.IServices
{
    /// <summary>
    /// 基础数据服务接口
    /// 注意各自项目不要修改此接口
    /// 由余祥琼统一维护
    /// </summary>
    public interface IBaseDataServices
    {
        /// <summary>
        /// 调用系统数据管理获取固定基础数据
        /// </summary>
        /// <param name="classId"></param>
        /// <param name="systemId"></param>
        /// <param name="moduleId"></param>
        /// <param name="oneClass"></param>
        /// <param name="getType"><see cref="EnumBaseDataGetType"/></param>
        /// <returns></returns>
        public List<Lis5BaseDataDto> GetLis5BaseData(string classId, string systemId, string moduleId, string oneClass, EnumBaseDataGetType getType);

        /// <summary>
        /// 获取组织机构数据
        /// 组织机构数据包括：系统实例,外部系统,医疗机构,院区、分院/检验科室/位置字典/检验专业组/管理专业组
        /// </summary>
        /// <param name="hospitalId">机构ID</param>
        /// <param name="getType">1,缓存数据，2,表数据</param>
        /// <returns></returns>
        OrganizeDataDto GetOrganizeData(string hospitalId, EnumBaseDataGetType getType);

        /// <summary>
        /// 基础数据
        /// 病人科别,病人病区,护理单元,医院护工表,医院医生表,医院护士表,公司信息,公司联系人
        /// </summary>
        /// <param name="classId">多个用英文逗号隔开</param>
        /// <param name="hospitalId">医疗机构ID</param>
        /// <param name="labId">科室ID</param>
        /// <param name="getType">1,缓存数据，2,表数据</param>
        /// <returns></returns>
        LisBaseDataDto GetLisBaseData(string classId, string hospitalId, string labId, EnumBaseDataGetType getType);

        /// <summary>
        ///获取基础数据名称
        /// </summary>
        /// <param name="class_id"></param>
        /// <param name="data_id"></param>
        /// <returns></returns>
        public string RecordClassBaseName(string class_id, string data_id);

        /// <summary>
        ///获取人事基础数据名称(人事系统自行控制)
        /// </summary>
        /// <param name="class_id"></param>
        /// <param name="data_id"></param>
        /// <returns></returns>
        public string RecordClassOaBaseName(string class_id, string data_id);

        /// <summary>
        /// 菜单按钮API权限
        /// </summary>
        /// <param name="moduleId"></param>
        /// <param name="getType">1,缓存数据，2,表数据</param>
        /// <returns></returns>
        MenuButtonApiDto GetMenuButtonApi(string moduleId, EnumBaseDataGetType getType);


        /// <summary>
        /// 账号信息
        /// </summary>
        /// <param name="hospitalId">医疗机构ID</param>
        /// <param name="getType">1,缓存数据，2,表数据</param>
        /// <returns></returns>
        AccountInfoDto AccountInfo(string hospitalId, EnumBaseDataGetType getType);

        /// <summary>
        /// 获取设置值
        /// </summary>
        /// <param name="moduleId">模块ID</param>
        /// <param name="unitId">单元ID</param>
        /// <param name="unitType">单元类型</param>
        /// <param name="setupClass">设置分类</param>
        /// <param name="hospitalId">医疗机构ID</param>
        /// <param name="setupNo">设置ID</param>
        /// <param name="getType">1,缓存数据，2,表数据</param>
        /// <returns></returns>
        List<Lis5SetupDictCacheDto> GetLis5SetupDicts(string moduleId, string hospitalId, string unitId,
           string unitType, string setupClass, string setupNo, EnumBaseDataGetType getType);

        /// <summary>
        /// 最大值
        /// </summary>
        /// <param name="tableName"></param>
        /// <param name="addCount"></param>
        /// <param name="ifTableMax">1.默认redis最大值;2、表中最大值</param>
        /// <returns></returns>
        ResultDto GetTableMaxPK(string tableName, string fileName,int addCount = 1, int ifTableMax = 1);

        /// <summary>
        /// 获取主键最大值（精确到字段）
        /// </summary>
        /// <param name="tableName"></param>
        /// <param name="addCount"></param>
        /// <param name="ifTableMax">1.默认redis最大值;2、表中最大值</param>
        /// <param name="fieldName">主键字段名</param>
        /// <returns></returns>
        ResultDto GetTableMax(string tableName, string fieldName="", int addCount = 1, int ifTableMax = 1, string dBOwner = "XH_OA");

        /// <summary>
        /// 获取主键最大值（精确到字段）
        /// </summary>
        /// <param name="tableName"></param>
        /// <param name="addCount"></param>
        /// <param name="ifTableMax">1.默认redis最大值;2、表中最大值</param>
        /// <param name="fieldName">主键字段名</param>
        ResultDto GetTableMaxNumber(string tableName, string fieldName = "", int addCount = 1, int ifTableMax = 1, string dBOwner = "XH_OA");

        /// <summary>
        /// 错误后最大值处理（精确到字段）
        /// </summary>
        /// <param name="tableName"></param>
        /// <param name="ex"></param>
        /// <returns></returns>
        ResultDto GetErrorTableMax(string tableName, Exception ex, string fieldName = "", string dBOwner = "XH_OA");

        //ResultDto UserVerify(string jsonStr);

        /// <summary>
        /// 通过BASE64方式上传文件到S28
        /// </summary>
        /// <param name="jsonStr"></param>
        /// <returns></returns>
        ResultDto UploadPathFile(string jsonStr);

        /// <summary>
        /// 通过FormData方式上传文件到S28
        /// </summary>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        ResultDto UploadPathFormDataFile(string folderName, string FileName, byte[] bytes, bool iFCover = true);

        ResultDto DeleteUploadFile(string jsonStr);


        ResultDto ProductInit();
        ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////




        List<PMS_PERSON_INFO> GetPmsPersonInfo();

        List<SYS6_INSPECTION_PGROUP> GetInspectionPgroup();

        List<DropDowDto> GetUserDropDowByLabId(string hospital_id, string lab_id);

        List<SYS6_BASE_DATA> GetSys6BaseData();
        List<OA_BASE_DATA> GetOaBaseData();

        List<RecordRejectDto> GetRecordRejectInfo(List<string> person_ids);


        /// <summary>
        /// 获取基础数据
        /// </summary>
        /// <param name="classId"></param>
        List<OA_BASE_DATA> GetOaBaseData(string classId, string stateFlag = "1");

        List<BaseDataClassDto> GetBaseDataClassList();
        string SaveBaseDataClass(BaseDataClassDto dto, string hisName);
        string DeleteBaseDataClass(string dataClassId, string hisName);

        /// <summary>
        /// 新增、修改实验管理基础数据
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        string AddOrModifyOaBaseData(OaBaseDataDto data);

        /// <summary>
        /// 删除实验管理基础数据
        /// </summary>
        /// <param name="DataId"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        bool DelOaBaseData(OaBaseDataDto Data);

        /// <summary>
        /// 启用实验管理基础数据
        /// </summary>
        /// <param name="Data"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public bool EnableOaBaseData(OaBaseDataDto Data);

        /// <summary>
        /// 禁用实验管理基础数据
        /// </summary>
        /// <param name="Data"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public bool DisableOaBaseData(OaBaseDataDto Data);


        /// 基础数据重新排序
        /// </summary>
        /// <param name="sortedDataIds"></param>
        /// <returns></returns>
        bool SortOaBaseData(List<OaBaseDataDto> sortedData);


        /// <summary>
        /// 修改实验管理基础数据状态
        /// </summary>
        /// <param name="DataId"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        bool ForbidOaBaseData(string type, string DataId);


        /// <summary>
        /// 调用115接口装载模板数据
        /// </summary>
        /// <param name="oaExcelFillDataDto"></param>
        /// <returns></returns>
        ResultDto LoadExcelData(OaExcelFillDataDto oaExcelFillDataDto);

        /// <summary>
        /// 调用H115获取预览文件
        /// </summary>
        /// <param name="templateFillDataDto"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        byte[] PreviewFile(StyleTemplateFillDataDto templateFillDataDto);

        /// <summary>
        /// 调用115填充execl页眉页脚
        /// </summary>
        /// <param name="STYLE_ID"></param>
        /// <param name="FILE"></param>
        /// <param name="FIELDS"></param>
        /// <returns></returns>
        byte[] ExportExcelFile(string STYLE_ID, IFormFile FILE, string FIELDS);


        /// <summary>
        /// 调用H115获取导出pdf文件
        /// </summary>
        /// <param name="templateFillDataDto"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        byte[] ExportStylePDFFile(StyleTemplateFillDataDto templateFillDataDto);
        /// <summary>
        /// 获取系统设置
        /// </summary>
        /// <param name="hospitalId"></param>
        /// <param name="areaId"></param>
        /// <returns></returns>

        Task<List<SetupValue>> GetSetupValue(string hospitalId, string areaId);

        /// <summary>
        /// 获取人事技能证书类型
        /// </summary>
        /// <param name="cert"></param>
        /// <returns></returns>
        string GetSkillCertificateName(PMS_SKILL_CERTIFICATE_LIST cert);

        /// <summary>
        /// 获取考试分类信息
        /// </summary>
        /// <param name="hospital_id">医疗机构id</param>
        /// <returns></returns>
        List<OA_DATA_CLASS> GetExamClassInfo(string? hospital_id);

    }
}
