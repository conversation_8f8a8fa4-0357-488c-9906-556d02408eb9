﻿//using H.Utility.SqlSugarInfra;
//using SqlSugar;
//using System;
//using System.Collections.Generic;
//using System.ComponentModel.DataAnnotations;
//using System.Linq;
//using System.Text;
//using System.Threading.Tasks;

//namespace XH.H81.Models.Entities.EvaluatePlan
//{
//    [DBOwner("XH_OA")]
//    public class OA_EVALUATE_PLAN_SETUP
//    {
//        [Key]
//        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
//        /// <summary>
//        /// 规评方案设置ID (主键)
//        /// </summary>
//        public string EPLAN_SID { get; set; }
//        [Key]
//        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
//        /// <summary>
//        /// 规评方案ID (主键)
//        /// </summary>
//        public string EPLAN_ID { get; set; }

//        /// <summary>
//        /// 医疗机构ID
//        /// </summary>
//        public string HOSPITAL_ID { get; set; }

//        /// <summary>
//        /// 科室ID
//        /// </summary>
//        public string LAB_ID { get; set; }

//        /// <summary>
//        /// 权限组合ID
//        /// </summary>
//        public string PROLE_COM_SID { get; set; }

//        /// <summary>
//        /// 规评方案适用类型
//        /// </summary>
//        public string EPLAN_APPLY_TYPE { get; set; }

//        /// <summary>
//        /// 规评效期
//        /// </summary>
//        public decimal? EPLAN_SHELF_LIFE { get; set; }

//        /// <summary>
//        /// 规评效期单位类型 (1-年, 2-月, 3-日)
//        /// </summary>
//        public string SHELF_LIFE_UTYPE { get; set; }

//        /// <summary>
//        /// 规评预警时长
//        /// </summary>
//        public string WARN_DURATION { get; set; }

//        /// <summary>
//        /// 预警时长单位类型 (1-年, 2-月, 3-日)
//        /// </summary>
//        public string WARN_UTYPE { get; set; }

//        /// <summary>
//        /// 限权类型 (0-禁用, 1-限权, 2-停岗)
//        /// </summary>
//        public string LIMIT_PROLE_TYPE { get; set; }

//        /// <summary>
//        /// 排序号
//        /// </summary>
//        public string EPLAN_SSORT { get; set; }

//        /// <summary>
//        /// 状态 (0-禁用, 1-在用, 2-删除)
//        /// </summary>
//        public string EPLAN_SSTATE { get; set; }

//        /// <summary>
//        /// 首次登记人
//        /// </summary>
//        public string FIRST_RPERSON { get; set; }

//        /// <summary>
//        /// 首次登记时间
//        /// </summary>
//        public DateTime? FIRST_RTIME { get; set; }

//        /// <summary>
//        /// 最后修改人员
//        /// </summary>
//        public string LAST_MPERSON { get; set; }

//        /// <summary>
//        /// 最后修改时间
//        /// </summary>
//        public DateTime? LAST_MTIME { get; set; }

//        /// <summary>
//        /// 备注
//        /// </summary>
//        public string REMARK { get; set; }
//    }
//}