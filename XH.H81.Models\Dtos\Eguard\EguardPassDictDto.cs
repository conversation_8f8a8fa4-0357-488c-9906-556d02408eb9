﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace XH.H81.Models.Dtos.Eguard
{
    /// <summary>
    /// 时间维护计划
    /// </summary>
    public class EguardPassDictDto
    {
        
        public string? EGUARD_PASS_ID { get; set; }
        /// <summary>
        /// 时间计划类型
        /// </summary>
        public string? EGUARD_PASS_TYPE { get; set; }
        /// <summary>
        /// 时间计划类型名称
        /// </summary>
        public string? EGUARD_PASS_TYPE_NAME { get; set; }
        /// <summary>
        /// 机构ID
        /// </summary>
        public string? HOSPITAL_ID { get; set; }
        /// <summary>
        /// 计划名称
        /// </summary>
        public string? EGUARD_PASS_NAME { get; set; }
        /// <summary>
        /// 通过时间JSON
        /// </summary>
        public string? PASS_TIME_JSON { get; set; }
        /// <summary>
        /// 门禁时间计划内容
        /// </summary>
        public string? PASS_TIME_CONTENT { get; set; }
        /// <summary>
        /// 有效时间段
        /// </summary>
        public PassTimeJson? PassTimeJson { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public string? EGUARD_PASS_STATE { get; set; }
        public string? FIRST_RPERSON { get; set; }
        public DateTime? FIRST_RTIME { get; set; }
        public string? LAST_MPERSON { get; set; }
        public DateTime? LAST_MTIME { get; set; }
        public string? REMARK { get; set; }
    }


    /// <summary>
    /// 生效时间
    /// </summary>
    public class PassTimeJson
    {
        public int? PlanId { get; set; }
        public int? Id  { get; set; }
        /// <summary>
        /// 星期
        /// </summary>
        public string? TimeType { get; set; }  //day  / week

        /// <summary>
        /// 按周 有效时间段
        /// </summary>
        public List<WeekTime>? WeekTime { get; set; } = new List<WeekTime>();
        /// <summary>
        /// 按天 有效时间段
        /// </summary>
        public List<DayTime>? DayTime { get; set; } = new List<DayTime>();
    }

    /// <summary>
    /// 按周的时间存储
    /// </summary>
    public class WeekTime
    {
        /// <summary>
        /// 周几
        /// </summary>
        public string? Week { get; set; }

        /// <summary>
        /// 时间段
        /// </summary>
        [JsonProperty("EffectiveTime")]
        public List<DayTime>? Period { get; set; } = new List<DayTime>();
    }

    /// <summary>
    /// 按日期的时间存储
    /// </summary>
    public class DayTime 
    {
        public string? Period { get; set; }
    }


    public class DeleteEguardPassDictDto
    {
        /// <summary>
        /// 门禁计划ID
        /// </summary>
        [Required(ErrorMessage = "门禁计划ID不能为空")]
        public string EGUARD_PASS_ID { get; set; }

    }

    /// <summary>
    /// 启用禁用 门禁计划
    /// </summary>
    public class ChangeEguardPassDictDto
    {
        [Required(ErrorMessage = "门禁计划ID不能为空")]
        public string EGUARD_PASS_ID { get; set; }
        /// <summary>
        /// 0禁用 1启用
        /// </summary>
        [Range(0, 1, ErrorMessage = "状态码入参错误")]
        public string EGUARD_PASS_STATE { get; set; }
    }



}
