﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Dtos.Eguard
{
    public class UpostEguardCom
    {
        /// <summary>
        /// 门禁组合ID
        /// </summary>
        public string EGUARD_COM_ID { get; set; }
        /// <summary>
        /// 组合名称
        /// </summary>
        public string? EGUARD_COM_NAME { get; set; }
        public List<UpostEguardDto> UserEguardList { get; set; }
    }
    public class UpostEguardDto
    {
        /// <summary>
        /// 门禁ID
        /// </summary>
        public string EGUARD_ID { get; set; }
        /// <summary>
        /// 门禁组合ID
        /// </summary>
        public string EGUARD_COM_ID { get; set; }
        /// <summary>
        /// 组合名称
        /// </summary>
        public string? EGUARD_COM_NAME { get; set; }
        /// <summary>
        /// 门禁地点
        /// </summary>
        public string? EGRUAD_ADDR { get; set; }
        /// <summary>
        /// 门禁名称
        /// </summary>
        public string? EGUARD_NAME { get; set; }
        /// <summary>
        /// 通过时间
        /// </summary>
        public string? PASS_TIME_JSON { get; set; }
        //适用范围
        public string? EPLAN_APPLY_TYPE { get; set; }
        //授权期限
        public string? AUTH_LIMIT_TYPE { get; set; }
        //门禁描述
        public string? EGUARD_DESC { get; set; }

    }
}
