﻿using System.Net;
using AutoMapper;
using Elastic.Clients.Elasticsearch;
using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Spire.Xls;
using SqlSugar;
using XH.H81.Base.Helper;
using XH.H81.IServices;
using XH.H81.Models.Dtos.Eguard;
using XH.H81.Models.Dtos.IoTDevices.Access;
using XH.H81.Models.Dtos.IoTDevices.Platform;
using XH.H81.Models.Entities.Eguard;
using XH.H81.Models.Entities.Pms;
using XH.H81.Models.ExternalEntity;
using XH.H81.Models.SugarDbContext;
using XH.H82.Models.BusinessModuleClient.Platform;
using XH.LAB.UTILS.Helpers;
using XH.LAB.UTILS.Interface;
using XH.LAB.UTILS.Models;
using XH.LAB.UTILS.Models.Dto;
using XH.LAB.UTILS.Models.Entites;
using Log = Serilog.Log;
using SYS6_USER_POST = XH.H81.Models.Entities.SYS6_USER_POST;

namespace XH.H81.Services
{
    public class EguardControlService : IEguardControlService
    {
        private readonly ISqlSugarUow<SugarDbContext_Master> _uow;
        private readonly IUploadFileService _IUploadFileService;
        private readonly string file_preview = "/H81pdf/api";
        private readonly IMapper _mapper;
        private readonly IConfiguration _configuration;
        private readonly IHttpContextAccessor _httpContext;
        public EguardControlService(ISqlSugarUow<SugarDbContext_Master> uow, IMapper mapper,
            IUploadFileService iUploadFileService, IConfiguration configuration, IHttpContextAccessor httpContext)
        {
            _uow = uow;
            _IUploadFileService = iUploadFileService;
            _configuration = configuration;
            _httpContext = httpContext;
            _mapper = mapper;
        }
        public ResultDto Test()
        {
            return new ResultDto { success = true, data = _uow.Db.Queryable<PMS_VISIT_REQUEST_LIST>().First() };
        }

        #region 门禁组合

        /// <summary>
        /// 获取门禁组合列表
        /// </summary>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        public async Task<ResultDto> GetEguardComList(string hospitalId)
        {
            //门禁组合列表
            var res = await _uow.Db.Queryable<PMS_EGUARD_COM_DICT>().Where(p => p.HOSPITAL_ID == hospitalId && p.EGUARD_COM_STATE != "2").Select(i => new EguardComDto
            {
                EGUARD_COM_ID = i.EGUARD_COM_ID,
                EGUARD_COM_NAME = i.EGUARD_COM_NAME,
                EGUARD_COM_STATE = i.EGUARD_COM_STATE,
                EGUARD_COM_DESC = i.EGUARD_COM_DESC,
                FIRST_RPERSON = i.FIRST_RPERSON,
                FIRST_RTIME = i.FIRST_RTIME,
                LAST_MPERSON = i.LAST_MPERSON,
                LAST_MTIME = i.LAST_MTIME,
            }).ToListAsync();
            //门禁组合设备表
            var eguardComList = await _uow.Db.Queryable<PMS_EGUARD_COM_LIST>().Where(p => res.Select(i => i.EGUARD_COM_ID).Contains(p.EGUARD_COM_ID) && p.ECOMBINE_STATE == "1").ToListAsync();
            res.ForEach(item =>
            {
                item.EGUARD_IDS = eguardComList.Where(p => p.EGUARD_COM_ID == item.EGUARD_COM_ID).Select(i => i.EQUIPMENT_ID).ToList();
                item.EGUARD_AMOUNT = item.EGUARD_IDS.Count;
            });
            return new ResultDto { success = true, data = res };
        }

        /// <summary>
        /// 门禁组合删除/禁用前校验
        /// </summary>
        /// <param name="eguardComId"></param>
        /// <returns></returns>
        public async Task<ResultDto> EguardComUpdateVerify(string eguardComId)
        {
            var res = new List<EguardComUpdateVerifyDto>();
            var query = await _uow.Db.Queryable<PMS_EGUARD_AUTH>()
                .InnerJoin<PMS_EGUARD_AUTH_COM>((a, b) => a.EGUARD_AUTH_ID == b.EGUARD_AUTH_ID)
                .InnerJoin<PMS_EGUARD_AUTH_POST>((a, b, c) => a.EGUARD_AUTH_ID == c.EGUARD_AUTH_ID)
                .LeftJoin<PMS_EGUARD_PASS_DICT>((a,b,c,d) => a.EGUARD_PASS_ID == d.EGUARD_PASS_ID && d.EGUARD_PASS_STATE != "2")
                .Where((a, b, c, d) => a.EGUARD_AUTH_STATE != "2" && b.EGUARD_AUTH_CSTATE != "2" && c.EGUARD_AUTH_PSTATE != "2" && b.EGUARD_DATA_ID == eguardComId && b.EGUARD_DATA_TYPE == "1")
                .Select((a, b, c, d) => new
                {
                    a.EGUARD_AUTH_ID,
                    c.EGUARD_AUTH_PID,
                    c.EGUARD_AUTH_TYPE,
                    c.EPLAN_APPLY_TYPE,
                    c.EGUARD_DATA_ID,
                    a.EGUARD_PASS_JSON,
                    d.PASS_TIME_JSON,
                    a.AUTH_LIMIT_TYPE,
                    a.AUTH_START_DATE,
                    a.AUTH_END_DATE,
                }).ToListAsync();
            //查询授权对象信息
            //科室
            var queryLab = await _uow.Db.Queryable<SYS6_INSPECTION_LAB>().Where(p => query.Where(i => i.EPLAN_APPLY_TYPE == "LAB" && i.EGUARD_AUTH_TYPE == "1")
            .Select(x => x.EGUARD_DATA_ID).Contains(p.LAB_ID)).ToListAsync();
            //检验专业组
            var queryPgroup = await _uow.Db.Queryable<SYS6_INSPECTION_PGROUP>().Where(p => query.Where(i => i.EPLAN_APPLY_TYPE == "PGROUP" && i.EGUARD_AUTH_TYPE == "1")
            .Select(x => x.EGUARD_DATA_ID).Contains(p.PGROUP_ID)).ToListAsync();
            //管理专业组
            var queryMgroup = await _uow.Db.Queryable<SYS6_INSPECTION_MGROUP>().Where(p => query.Where(i => i.EPLAN_APPLY_TYPE == "MGROUP" && i.EGUARD_AUTH_TYPE == "1")
            .Select(x => x.EGUARD_DATA_ID).Contains(p.MGROUP_ID)).ToListAsync();
            //岗位
            var queryPost = await _uow.Db.Queryable<SYS6_POST>().Where(p => query.Where(i => i.EPLAN_APPLY_TYPE == "POST" && i.EGUARD_AUTH_TYPE == "1")
            .Select(x => x.EGUARD_DATA_ID).Contains(p.POST_ID)).ToListAsync();
            //岗位角色
            var queryProle = await _uow.Db.Queryable<SYS6_POST_ROLE>()
                .InnerJoin<SYS6_POST_ROLE_DICT>((a, b) => a.PROLE_DICT_ID == b.PROLE_DICT_ID)
                .Where((a, b) => query.Where(p => p.EPLAN_APPLY_TYPE == "PROLE" && p.EGUARD_AUTH_TYPE == "1").Select(i => i.EGUARD_DATA_ID).Contains(a.POSTROLE_ID))
                .Select((a, b) => new
                {
                    a.POSTROLE_ID,
                    b.POSTROLE_NAME,
                    b.POSTROLE_SORT
                }).ToListAsync();
            //人员
            var queryPerson = await _uow.Db.Queryable<PMS_PERSON_INFO>().Where(p => query.Where(i => i.EGUARD_AUTH_TYPE == "2")
            .Select(i => i.EGUARD_DATA_ID).Contains(p.PERSON_ID)).ToListAsync();


            foreach (var item in query.DistinctBy(p => p.EGUARD_AUTH_ID))
            {
                var itemQuery = query.Where(p => p.EGUARD_AUTH_ID == item.EGUARD_AUTH_ID).ToList();
                var itemLabQuery = itemQuery.Where(p => p.EPLAN_APPLY_TYPE == "LAB" && p.EGUARD_AUTH_TYPE == "1").ToList();
                if(itemLabQuery.Count > 0)
                {
                    res.Add(new EguardComUpdateVerifyDto
                    {
                        ROWKEY = IDGenHelper.CreateGuid(),
                        EGUARD_AUTH_TYPE = item.EGUARD_AUTH_TYPE,
                        EGUARD_DATA_NAME = string.Join(",", queryLab.Where(p => itemLabQuery.Select(i => i.EGUARD_DATA_ID).Contains(p.LAB_ID)).DistinctBy(x => x.LAB_ID).OrderBy(x => x.LAB_SORT).Select(x => x.LAB_NAME)),
                        EGUARD_PASS_JSON = item.PASS_TIME_JSON ?? item.EGUARD_PASS_JSON,
                        AUTH_LIMIT_TYPE = item.AUTH_LIMIT_TYPE,
                        AUTH_START_DATE = item.AUTH_START_DATE,
                        AUTH_END_DATE = item.AUTH_END_DATE,
                        EGUARD_AUTH_ID = item.EGUARD_AUTH_ID,
                        EPLAN_APPLY_TYPE = "LAB"
                    });
                }
                var itemPgroupQuery = itemQuery.Where(p => p.EPLAN_APPLY_TYPE == "PGROUP" && p.EGUARD_AUTH_TYPE == "1").ToList();
                if (itemPgroupQuery.Count > 0)
                {
                    res.Add(new EguardComUpdateVerifyDto
                    {
                        ROWKEY = IDGenHelper.CreateGuid(),
                        EGUARD_AUTH_TYPE = item.EGUARD_AUTH_TYPE,
                        EGUARD_DATA_NAME = string.Join(",", queryPgroup.Where(p => itemPgroupQuery.Select(i => i.EGUARD_DATA_ID).Contains(p.PGROUP_ID)).DistinctBy(x => x.PGROUP_ID).OrderBy(x => x.PGROUP_SORT).Select(x => x.PGROUP_NAME)),
                        EGUARD_PASS_JSON = item.PASS_TIME_JSON ?? item.EGUARD_PASS_JSON,
                        AUTH_LIMIT_TYPE = item.AUTH_LIMIT_TYPE,
                        AUTH_START_DATE = item.AUTH_START_DATE,
                        AUTH_END_DATE = item.AUTH_END_DATE,
                        EGUARD_AUTH_ID = item.EGUARD_AUTH_ID,
                        EPLAN_APPLY_TYPE = "PGROUP"
                    });
                }
                var itemMgroupQuery = itemQuery.Where(p => p.EPLAN_APPLY_TYPE == "MGROUP" && p.EGUARD_AUTH_TYPE == "1").ToList();
                if (itemMgroupQuery.Count > 0)
                {
                    res.Add(new EguardComUpdateVerifyDto
                    {
                        ROWKEY = IDGenHelper.CreateGuid(),
                        EGUARD_AUTH_TYPE = item.EGUARD_AUTH_TYPE,
                        EGUARD_DATA_NAME = string.Join(",", queryMgroup.Where(p => itemMgroupQuery.Select(i => i.EGUARD_DATA_ID).Contains(p.MGROUP_ID)).DistinctBy(x => x.MGROUP_ID).OrderBy(x => x.MGROUP_SORT).Select(x => x.MGROUP_NAME)),
                        EGUARD_PASS_JSON = item.PASS_TIME_JSON ?? item.EGUARD_PASS_JSON,
                        AUTH_LIMIT_TYPE = item.AUTH_LIMIT_TYPE,
                        AUTH_START_DATE = item.AUTH_START_DATE,
                        AUTH_END_DATE = item.AUTH_END_DATE,
                        EGUARD_AUTH_ID = item.EGUARD_AUTH_ID,
                        EPLAN_APPLY_TYPE = "MGROUP"
                    });
                }
                var itemPostQuery = itemQuery.Where(p => p.EPLAN_APPLY_TYPE == "POST" && p.EGUARD_AUTH_TYPE == "1").ToList();
                if (itemPostQuery.Count > 0)
                {
                    res.Add(new EguardComUpdateVerifyDto
                    {
                        ROWKEY = IDGenHelper.CreateGuid(),
                        EGUARD_AUTH_TYPE = item.EGUARD_AUTH_TYPE,
                        EGUARD_DATA_NAME = string.Join(",", queryPost.Where(p => itemPostQuery.Select(i => i.EGUARD_DATA_ID).Contains(p.POST_ID)).DistinctBy(x => x.POST_ID).OrderBy(x => x.POST_SORT).Select(x => x.POST_NAME)),
                        EGUARD_PASS_JSON = item.PASS_TIME_JSON ?? item.EGUARD_PASS_JSON,
                        AUTH_LIMIT_TYPE = item.AUTH_LIMIT_TYPE,
                        AUTH_START_DATE = item.AUTH_START_DATE,
                        AUTH_END_DATE = item.AUTH_END_DATE,
                        EGUARD_AUTH_ID = item.EGUARD_AUTH_ID,
                        EPLAN_APPLY_TYPE = "POST"
                    });
                }
                var itemProleQuery = itemQuery.Where(p => p.EPLAN_APPLY_TYPE == "PROLE" && p.EGUARD_AUTH_TYPE == "1").ToList();
                if (itemProleQuery.Count > 0)
                {
                    res.Add(new EguardComUpdateVerifyDto
                    {
                        ROWKEY = IDGenHelper.CreateGuid(),
                        EGUARD_AUTH_TYPE = item.EGUARD_AUTH_TYPE,
                        EGUARD_DATA_NAME = string.Join(",", queryProle.Where(p => itemProleQuery.Select(i => i.EGUARD_DATA_ID).Contains(p.POSTROLE_ID)).DistinctBy(x => x.POSTROLE_ID).OrderBy(x => x.POSTROLE_SORT).Select(x => x.POSTROLE_NAME)),
                        EGUARD_PASS_JSON = item.PASS_TIME_JSON ?? item.EGUARD_PASS_JSON,
                        AUTH_LIMIT_TYPE = item.AUTH_LIMIT_TYPE,
                        AUTH_START_DATE = item.AUTH_START_DATE,
                        AUTH_END_DATE = item.AUTH_END_DATE,
                        EGUARD_AUTH_ID = item.EGUARD_AUTH_ID,
                        EPLAN_APPLY_TYPE = "PROLE"
                    });
                }
                var itemPersonQuery = itemQuery.Where(p => p.EGUARD_AUTH_TYPE == "2").ToList();
                if(itemPersonQuery.Count() > 0)
                {
                    res.Add(new EguardComUpdateVerifyDto
                    {
                        ROWKEY = IDGenHelper.CreateGuid(),
                        EGUARD_AUTH_TYPE = item.EGUARD_AUTH_TYPE,
                        EGUARD_DATA_NAME = string.Join(",", queryPerson.Where(p => itemPersonQuery.Select(i => i.EGUARD_DATA_ID).Contains(p.PERSON_ID)).DistinctBy(x => x.PERSON_ID).OrderBy(x => x.USER_NAME).Select(x => x.USER_NAME)),
                        EGUARD_PASS_JSON = item.PASS_TIME_JSON ?? item.EGUARD_PASS_JSON,
                        AUTH_LIMIT_TYPE = item.AUTH_LIMIT_TYPE,
                        AUTH_START_DATE = item.AUTH_START_DATE,
                        AUTH_END_DATE = item.AUTH_END_DATE,
                        EGUARD_AUTH_ID = item.EGUARD_AUTH_ID,
                        EPLAN_APPLY_TYPE = "PROLE"
                    });
                }
            }
            return new ResultDto { success = true, data = res };
        }

        /// <summary>
        /// 删除已有门禁组合授权
        /// </summary>
        /// <returns></returns>
        public async Task<ResultDto> DeleteEguardAuthCom(string eguardComId,string operPerson)
        {
            var operTime = _uow.Db.GetDate();
            var deleteEguardAuthCom = await _uow.Db.Queryable<PMS_EGUARD_AUTH_COM>().Where(p => p.EGUARD_DATA_ID == eguardComId && p.EGUARD_DATA_TYPE == "1" && p.EGUARD_AUTH_CSTATE != "2").ToListAsync();
            //foreach(var item in deleteEguardAuthCom)
            //{
            //    item.EGUARD_AUTH_CSTATE = "2";
            //    item.LAST_MPERSON = operPerson;
            //    item.LAST_MTIME = operTime;
            //}
            await _uow.Db.Updateable<PMS_EGUARD_AUTH_COM>().SetColumns(p => new PMS_EGUARD_AUTH_COM
            {
                EGUARD_AUTH_CSTATE = "2",
                LAST_MPERSON = operPerson,
                LAST_MTIME = operTime
            }).Where(p => deleteEguardAuthCom.Select(i => i.EGUARD_AUTH_CID).Contains(p.EGUARD_AUTH_CID)).ExecuteCommandAsync();
            //查询授权是否还有其他数据
            var authIds = deleteEguardAuthCom.Select(p => p.EGUARD_AUTH_ID).Distinct().ToList();
            var deleteEguardAuth = await _uow.Db.Queryable<PMS_EGUARD_AUTH>()
                .LeftJoin<PMS_EGUARD_AUTH_COM>((a, b) => a.EGUARD_AUTH_ID == b.EGUARD_AUTH_ID && b.EGUARD_AUTH_CSTATE != "2")
                .Where((a, b) => authIds.Contains(a.EGUARD_AUTH_ID) && a.EGUARD_AUTH_STATE != "2" && !SqlFunc.HasValue(b.EGUARD_AUTH_CID))
                .Select((a, b) => new PMS_EGUARD_AUTH
                {
                    EGUARD_AUTH_ID = a.EGUARD_AUTH_ID,
                    EGUARD_AUTH_STATE = "2",
                    LAST_MPERSON = operPerson,
                    LAST_MTIME = operTime
                }).ToListAsync();
            //await _uow.Db.Updateable(deleteEguardAuth).UpdateColumns(p => new PMS_EGUARD_AUTH
            //{
            //    EGUARD_AUTH_STATE = p.EGUARD_AUTH_STATE,
            //    LAST_MPERSON = p.LAST_MPERSON,
            //    LAST_MTIME = p.LAST_MTIME
            //}).ExecuteCommandAsync();
            await DeleteAccessControl(deleteEguardAuth.Select(i => i.EGUARD_AUTH_ID).ToArray(),operPerson);
            await _uow.Db.Updateable<PMS_EGUARD_AUTH_POST>().SetColumns(p => new PMS_EGUARD_AUTH_POST
            {
                EGUARD_AUTH_PSTATE = "2",
                LAST_MPERSON = operPerson,
                LAST_MTIME = operTime
            }).Where(p => deleteEguardAuth.Select(i => i.EGUARD_AUTH_ID).Contains(p.EGUARD_AUTH_ID)).ExecuteCommandAsync();
            return new ResultDto { success = true };
        }

        /// <summary>
        /// 获取门禁列表树
        /// </summary>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        public async Task<ResultDto> GetEguardTreeList(string hospitalId)
        {
            //门禁列表
            var eguardList = await _uow.Db.Queryable<SYS6_POSITION_DICT>()
                .InnerJoin<EMS_EQUIPMENT_INFO>((a, b) => a.POSITION_ID == b.POSITION_ID)
                .Where((a, b) => a.HOSPITAL_ID == hospitalId && b.SMBL_CLASS == "8" && a.POSITION_STATE == "1" && b.EQUIPMENT_STATE == "1")
                .Select((a, b) => new
                {
                    a.POSITION_ID,
                    a.POSITION_NAME,
                    b.EQUIPMENT_ID,
                    b.EQUIPMENT_NAME
                }).ToListAsync();
            var res = new List<EguardTreeDto>();
            foreach (var item in eguardList.DistinctBy(p => p.POSITION_ID))
            {
                res.Add(new EguardTreeDto
                {
                    POSITION_ID = item.POSITION_ID,
                    POSITION_NAME = item.POSITION_NAME,
                    EguardTreeSecondNode = eguardList.Where(p => p.POSITION_ID == item.POSITION_ID).Select(i => new EguardInfoDto
                    {
                        EGUARD_ID = i.EQUIPMENT_ID,
                        EGUARD_NAME = i.EQUIPMENT_NAME ?? i.EQUIPMENT_ID
                    }).ToList()
                });
            }
            return new ResultDto { success = true, data = res };
        }



        /// <summary>
        /// 获取门禁列表树
        /// </summary>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        public async Task<ResultDto> GetEguardTableList(string hospitalId)
        {
            var result = new ResultDto();
            try
            {
                //门禁列表
                var eguardList = await _uow.Db.Queryable<SYS6_POSITION_DICT>()
                    .InnerJoin<EMS_EQUIPMENT_INFO>((a, b) => a.POSITION_ID == b.POSITION_ID)
                    .InnerJoin<PMS_EGUARD_COM_LIST>((a, b, c) => b.EQUIPMENT_ID == c.EQUIPMENT_ID)
                    .InnerJoin<PMS_EGUARD_COM_DICT>((a, b, c, d) => c.EGUARD_COM_ID == d.EGUARD_COM_ID)
                    .Where((a, b, c, d) => a.HOSPITAL_ID == hospitalId && b.SMBL_CLASS == "8" && a.POSITION_STATE != "2" && b.EQUIPMENT_STATE == "1" && c.ECOMBINE_STATE != "2" && d.EGUARD_COM_STATE != "2")
                    .Select((a, b, c, d) => new
                    {
                        a.POSITION_ID,
                        a.POSITION_NAME,
                        b.EQUIPMENT_ID,
                        b.EQUIPMENT_NAME,
                        b.EQUIPMENT_STATE,
                        d.EGUARD_COM_ID,
                        c.FIRST_RPERSON,
                        c.FIRST_RTIME,
                        c.LAST_MPERSON,
                        c.LAST_MTIME,
                        c.ECOMBINE_STATE
                    }).ToListAsync();

                var EguardTableDtoList = new List<EguardTableDto>();
                if (eguardList != null && eguardList.Count > 0)
                {
                    var groupList = eguardList.GroupBy(p => new { p.POSITION_ID, p.POSITION_NAME, p.EQUIPMENT_ID, p.EQUIPMENT_NAME, p.EQUIPMENT_STATE,p.ECOMBINE_STATE }).ToList();
                    foreach (var group in groupList)
                    {
                        var EguardTableDto = new EguardTableDto()
                        {
                            //POSITION_ID = group.Key.POSITION_ID,
                            //POSITION_NAME = group.Key.POSITION_NAME,
                            //EQUIPMENT_ID = group.Key.EQUIPMENT_ID,
                            //EQUIPMENT_NAME = group.Key.EQUIPMENT_NAME,
                            //EQUIPMENT_STATE = group.Key.EQUIPMENT_STATE,

                            EGRUAD_ADDR_ID = group.Key.POSITION_ID,
                            EGRUAD_ADDR = group.Key.POSITION_NAME,
                            EGUARD_ID = group.Key.EQUIPMENT_ID,
                            EGUARD_NAME = group.Key.EQUIPMENT_NAME,
                            EGUARD_STATE = group.Key.EQUIPMENT_STATE,
                            ECOMBINE_STATE = group.Key.ECOMBINE_STATE,
                            EGUARD_COM_ID = group.ToList().Select(p => p.EGUARD_COM_ID).ToList(),
                            LAST_MPERSON = group.ToList().OrderBy(p => p.LAST_MTIME).FirstOrDefault()?.FIRST_RPERSON,
                            LAST_MTIME = group.ToList().OrderBy(p => p.LAST_MTIME).FirstOrDefault()?.LAST_MTIME,
                            FIRST_RPERSON = group.ToList().OrderBy(p => p.FIRST_RTIME).FirstOrDefault()?.FIRST_RPERSON,
                            FIRST_RTIME = group.ToList().OrderBy(p => p.FIRST_RTIME).FirstOrDefault()?.LAST_MTIME,
                            EGUARD_COM_AMOUNT = group.ToList().Select(p => p.EGUARD_COM_ID).ToList().Count(),
                        };
                        EguardTableDtoList.Add(EguardTableDto);
                    }
                }
                result.data = EguardTableDtoList;
                result.success = true;
            }
            catch (Exception ex)
            {
                result.success = true;
                result.msg = $"获取数据失败！{ex.Message}";
            }
            return result;
        }

        /// <summary>
        /// 启用禁用单个门禁
        /// </summary>
        /// <returns></returns>
        public async Task<ResultDto> ChangeEguardTableState(EditEguardTableDto input, ClaimsDto user)
        {
            var result = new ResultDto();
            var operVerify = await EguardComOperVerify(null,input.EQUIPMENT_ID, input.EQUIPMENT_STATE == "1" ? "启用" : "禁用");
            if (operVerify.success == false)
            {
                return operVerify;
            }
            try
            {
                var sysDate = _uow.Db.GetDate();
                if (input.EQUIPMENT_ID.IsNotNullOrEmpty())
                {
                    await _uow.GetRepository<PMS_EGUARD_COM_LIST>().UpdateAsync(p => p.EQUIPMENT_ID == input.EQUIPMENT_ID, p => new PMS_EGUARD_COM_LIST()
                    {
                        ECOMBINE_STATE = input.EQUIPMENT_STATE,
                        LAST_MPERSON = $"{user.LOGID}_{user.USER_NAME}",
                        LAST_MTIME = sysDate
                    });
                    await _uow.SaveChangesAsync();
                }
                result.msg = (input.EQUIPMENT_STATE == "1" ? "启用" : "禁用") + "成功";
                result.success = true;
            }
            catch (Exception ex)
            {
                result.msg = $"操作失败！{ex.Message}";
                result.success = false;
            }
            return result;
        }

        /// <summary>
        /// 已选待选组合
        /// </summary>
        /// <returns></returns>
        public async Task<ResultDto> UpdateEguardComTable(EditEguardTableDto input, ClaimsDto user)
        {
            var result = new ResultDto();
            var sysDate = _uow.Db.GetDate();
            var operVerify = await EguardComOperVerify(input.EGUARD_COM_IDs,null, input.OPERATE_TYPE == "add" ? "" : "禁用");
            if (operVerify.success == false)
            {
                return operVerify;
            }
            try
            {
                if (input.OPERATE_TYPE == "add") //添加组合
                {
                    //插入设备明细表
                    if (input.EGUARD_COM_IDs != null && input.EGUARD_COM_IDs.Count > 0)
                    {
                        var list = new List<PMS_EGUARD_COM_LIST>();
                        foreach (var item in input.EGUARD_COM_IDs)
                        {
                            var data = new PMS_EGUARD_COM_LIST();
                            data.EGUARD_COM_ID = item;
                            data.EQUIPMENT_ID = input.EQUIPMENT_ID;
                            data.ECOMBINE_STATE = "1";
                            data.FIRST_RTIME = sysDate;
                            data.FIRST_RPERSON = $"{user.LOGID}_{user.USER_NAME}";
                            data.LAST_MTIME = sysDate;
                            data.LAST_MPERSON = $"{user.LOGID}_{user.USER_NAME}";
                            list.Add(data);
                        }
                        await _uow.GetRepository<PMS_EGUARD_COM_LIST>().BulkInsertAsync(list);
                    }
                }
                else if (input.OPERATE_TYPE == "delete") //删除组合
                {
                    //删除设备明细表
                    if (input.EGUARD_COM_IDs != null && input.EGUARD_COM_IDs.Count > 0)
                    {
                        var list = new List<PMS_EGUARD_COM_LIST>();
                        foreach (var item in input.EGUARD_COM_IDs)
                        {
                            await _uow.GetRepository<PMS_EGUARD_COM_LIST>().DeleteAsync(p => p.EGUARD_COM_ID == item && p.EQUIPMENT_ID == input.EQUIPMENT_ID);
                        }
                    }
                }
                await _uow.SaveChangesAsync();
                result.success = true;
                result.msg = "保存成功";
            }
            catch (Exception ex)
            {
                result.success = true;
                result.msg = $"保存失败!{ex.Message}";
            }
            return result;
        }

        #endregion



        #region 门禁增删改查
        /// <summary>
        /// 新增/修改 门禁组合信息
        /// </summary>
        /// <returns></returns>
        public async Task<ResultDto> SaveEguardInfo(EguardComDto input, ClaimsDto user)
        {
            var result = new ResultDto();
            var sysDate = _uow.Db.GetDate();
            var query = _uow.GetRepository<PMS_EGUARD_COM_DICT>();
            try
            {
                if (input.EGUARD_COM_ID.IsNotNullOrEmpty()) //修改门禁组合
                {
                    var operVerify = await EguardComOperVerify(new List<string>() { input.EGUARD_COM_ID}, null,"修改");
                    if(operVerify.success == false)
                    {
                        return operVerify;
                    }
                    var count = await query.DbSet().CountAsync(p => p.EGUARD_COM_ID == input.EGUARD_COM_ID && p.EGUARD_COM_STATE == "1");
                    if (count > 0)
                    {
                        await query.UpdateAsync(result => result.EGUARD_COM_ID == input.EGUARD_COM_ID,
                            result => new PMS_EGUARD_COM_DICT()
                            {
                                EGUARD_COM_NAME = input.EGUARD_COM_NAME,
                                EGUARD_COM_DESC = input.EGUARD_COM_DESC,
                                LAST_MTIME = sysDate,
                                LAST_MPERSON = $"{user.LOGID}_{user.USER_NAME}"
                            });
                    }
                    //删除所有明细设备ID ，重新插入
                    if (input.EGUARD_IDS != null)
                    {
                        await _uow.GetRepository<PMS_EGUARD_COM_LIST>().DeleteAsync(p => p.EGUARD_COM_ID == input.EGUARD_COM_ID);
                        var list = new List<PMS_EGUARD_COM_LIST>();
                        foreach (var item in input.EGUARD_IDS)
                        {
                            var model = new PMS_EGUARD_COM_LIST();
                            model.EGUARD_COM_ID = input.EGUARD_COM_ID;
                            model.EQUIPMENT_ID = item;
                            model.ECOMBINE_STATE = "1";
                            model.FIRST_RTIME = sysDate;
                            model.FIRST_RPERSON = $"{user.LOGID}_{user.USER_NAME}";
                            model.LAST_MTIME = sysDate;
                            model.LAST_MPERSON = $"{user.LOGID}_{user.USER_NAME}";
                            list.Add(model);
                        }
                        if(list.Count > 0)
                        {
                            await _uow.GetRepository<PMS_EGUARD_COM_LIST>().BulkInsertAsync(list);
                        }
                    }
                }
                else //新增门禁组合
                {
                    var model = new PMS_EGUARD_COM_DICT()
                    {
                        EGUARD_COM_ID = IDGenHelper.CreateGuid().Substring(0, 20),
                        EGUARD_COM_STATE = "1",
                        EGUARD_COM_DESC = input.EGUARD_COM_DESC,
                        LAST_MTIME = sysDate,
                        EGUARD_COM_NAME = input.EGUARD_COM_NAME,
                        FIRST_RPERSON = $"{user.LOGID}_{user.USER_NAME}",
                        FIRST_RTIME = sysDate,
                        HOSPITAL_ID = user.HOSPITAL_ID,
                        LAST_MPERSON = $"{user.LOGID}_{user.USER_NAME}",
                        REMARK = string.Empty
                    };
                    input.EGUARD_COM_ID = model.EGUARD_COM_ID;
                    await query.InsertAsync(model);
                    //插入设备明细表
                    if (input.EGUARD_IDS != null && input.EGUARD_IDS.Count > 0)
                    {
                        await _uow.GetRepository<PMS_EGUARD_COM_LIST>().DeleteAsync(p => p.EGUARD_COM_ID == input.EGUARD_COM_ID);
                        var list = new List<PMS_EGUARD_COM_LIST>();
                        foreach (var item in input.EGUARD_IDS)
                        {
                            var data = new PMS_EGUARD_COM_LIST();
                            data.EGUARD_COM_ID = input.EGUARD_COM_ID;
                            data.EQUIPMENT_ID = item;
                            data.ECOMBINE_STATE = "1";
                            data.FIRST_RTIME = sysDate;
                            data.FIRST_RPERSON = $"{user.LOGID}_{user.USER_NAME}";
                            data.LAST_MTIME = sysDate;
                            data.LAST_MPERSON = $"{user.LOGID}_{user.USER_NAME}";
                            list.Add(data);
                        }
                        await _uow.GetRepository<PMS_EGUARD_COM_LIST>().BulkInsertAsync(list);
                    }
                }
                await _uow.SaveChangesAsync();
                result.msg = "保存成功！";
                result.success = true;
            }
            catch (Exception ex)
            {
                result.msg = $"保存失败！{ex.Message}";
                result.success = false;
            }
            return result;
        }



        /// <summary>
        /// 启用/禁用门禁信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<ResultDto> ChangeEguardState(ChangeEguardComStateDto input, ClaimsDto user)
        {
            var result = new ResultDto();
            var sysDate = _uow.Db.GetDate();
            //var operVerify = await EguardComOperVerify(new List<string>() { input.EGUARD_COM_ID }, null, input.EGUARD_COM_STATE == "1" ? "启用" : "禁用");
            //if (operVerify.success == false)
            //{
            //    return operVerify;
            //}
            try
            {
                var query = _uow.GetRepository<PMS_EGUARD_COM_DICT>();
                if (query.Any(p => p.EGUARD_COM_ID == input.EGUARD_COM_ID && p.EGUARD_COM_STATE != "2"))
                {
                    await query.UpdateAsync(p => p.EGUARD_COM_ID == input.EGUARD_COM_ID && p.EGUARD_COM_STATE != input.EGUARD_COM_STATE,
                        result => new PMS_EGUARD_COM_DICT
                        {
                            EGUARD_COM_STATE = input.EGUARD_COM_STATE,
                            LAST_MPERSON = $"{user.LOGID}_{user.USER_NAME}",
                            LAST_MTIME = sysDate
                        });
                    await _uow.SaveChangesAsync();
                    result.success = true;
                    result.msg = (input.EGUARD_COM_STATE == "1" ? "启用" : "禁用") + "成功";
                }
                else
                {
                    result.success = false;
                    result.msg = "操作失败！请刷新数据后再操作";
                }
            }
            catch (Exception ex)
            {
                result.success = false;
                result.msg = $"操作失败！{ex.Message}";
            }
            return result;
        }

        /// <summary>
        /// 删除门禁信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<ResultDto> DeleteEguardInfo(DeleteEguardComDto input, ClaimsDto user)
        {
            var result = new ResultDto();
            var sysDate = _uow.Db.GetDate();
            //var operVerify = await EguardComOperVerify(new List<string>() { input.EGUARD_COM_ID }, null, "删除");
            //if (operVerify.success == false)
            //{
            //    return operVerify;
            //}
            try
            {
                var info = await _uow.GetRepository<PMS_EGUARD_COM_DICT>().DbSet().Where(p => p.EGUARD_COM_ID == input.EGUARD_COM_ID).FirstAsync();
                await _uow.GetRepository<PMS_EGUARD_COM_DICT>().UpdateAsync(p => p.EGUARD_COM_ID == input.EGUARD_COM_ID, p => new PMS_EGUARD_COM_DICT()
                {
                    EGUARD_COM_STATE = "2",
                    LAST_MPERSON = $"{user.LOGID}_{user.USER_NAME}",
                    LAST_MTIME = sysDate
                });
                //门禁组合下的明细也要删除
                await _uow.GetRepository<PMS_EGUARD_COM_LIST>().UpdateAsync(p => p.EGUARD_COM_ID == input.EGUARD_COM_ID, p => new PMS_EGUARD_COM_LIST()
                {
                    ECOMBINE_STATE = "2",
                    LAST_MPERSON = $"{user.LOGID}_{user.USER_NAME}",
                    LAST_MTIME = sysDate
                });

                result.success = true;
                result.msg = "删除成功";
                await _uow.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                result.success = false;
                result.msg = $"删除失败！{ex.Message}";
            }
            return result;
        }

        #endregion


        #region 门禁授权相关

        /// <summary>
        /// 按岗位获取门禁列表
        /// </summary>
        /// <param name="authType">LAB-检验科室 ；PGROUP-检验专业组；MGROUP-管理专业组；POST-岗位；PROLE-岗位角色;User-用户id</param>
        /// <param name="dataId">树选中的id</param>
        /// <returns></returns>
        public async Task<ResultDto> GetUserEguardList(string authType, string dataId)
        {
            var userEguardAuthList = _uow.Db.Queryable<PMS_EGUARD_AUTH_POST>()
                .InnerJoin<PMS_EGUARD_AUTH>((a, b) => a.EGUARD_AUTH_ID == b.EGUARD_AUTH_ID)
                .InnerJoin<PMS_EGUARD_AUTH_COM>((a, b, c) => a.EGUARD_AUTH_ID == c.EGUARD_AUTH_ID)
                .LeftJoin<PMS_EGUARD_PASS_DICT>((a, b, c, d) => b.EGUARD_PASS_ID == d.EGUARD_PASS_ID)
                .WhereIF(authType != "User", (a, b, c, d) => a.EGUARD_AUTH_TYPE == authType)
                .Where((a, b, c, d) => a.EGUARD_AUTH_PSTATE == "1" && b.EGUARD_AUTH_STATE == "1" && c.EGUARD_AUTH_CSTATE == "1" && d.EGUARD_PASS_STATE == "1" && a.EGUARD_DATA_ID == dataId)
                .Select((a, b, c, d) => new
                {
                    a.EPLAN_APPLY_TYPE,
                    c.EGUARD_DATA_TYPE,
                    c.EGUARD_DATA_ID,
                    d.PASS_TIME_JSON,
                    b.AUTH_LIMIT_TYPE,
                    b.EGUARD_PASS_ID,
                    b.EGUARD_PASS_JSON,
                });
            //按门禁组合
            var res_com = userEguardAuthList.Where(p => p.EGUARD_DATA_TYPE == "1");
            var res1 = await res_com.InnerJoin<PMS_EGUARD_COM_DICT>((a, b) => a.EGUARD_DATA_ID == b.EGUARD_COM_ID)
                .InnerJoin<PMS_EGUARD_COM_LIST>((a, b, c) => b.EGUARD_COM_ID == c.EGUARD_COM_ID)
                .InnerJoin<EMS_EQUIPMENT_INFO>((a, b, c, d) => c.EQUIPMENT_ID == d.EQUIPMENT_ID)
                .LeftJoin<SYS6_POSITION_DICT>((a, b, c, d, e) => d.POSITION_ID == e.POSITION_ID)
                .Where((a, b, c, d, e) => b.EGUARD_COM_STATE == "1" && c.ECOMBINE_STATE == "1" && e.POSITION_STATE == "1")
                .Select((a, b, c, d, e) => new UpostEguardDto
                {
                    EGUARD_ID = d.EQUIPMENT_ID,
                    EGUARD_COM_ID = b.EGUARD_COM_ID,
                    EGRUAD_ADDR = e.POSITION_NAME,
                    PASS_TIME_JSON = SqlFunc.HasValue(a.PASS_TIME_JSON) ? a.PASS_TIME_JSON : a.EGUARD_PASS_JSON,
                    AUTH_LIMIT_TYPE = a.AUTH_LIMIT_TYPE,
                    EGUARD_NAME = d.EQUIPMENT_NAME,
                    EPLAN_APPLY_TYPE = a.EPLAN_APPLY_TYPE
                }).ToListAsync();

            //按门禁
            var res_eguard = userEguardAuthList.Where(p => p.EGUARD_DATA_TYPE == "2");
            var res2 = await res_eguard.InnerJoin<EMS_EQUIPMENT_INFO>((a, b) => a.EGUARD_DATA_ID == b.EQUIPMENT_ID)
                .InnerJoin<PMS_EGUARD_COM_LIST>((a, b, c) => b.EQUIPMENT_ID == c.EQUIPMENT_ID)
                .InnerJoin<PMS_EGUARD_COM_DICT>((a, b, c, d) => c.EGUARD_COM_ID == d.EGUARD_COM_ID)
                .LeftJoin<SYS6_POSITION_DICT>((a, b, c, d, e) => b.POSITION_ID == e.POSITION_ID)
                .Where((a, b, c, d, e) => d.EGUARD_COM_STATE == "1" && c.ECOMBINE_STATE == "1" && e.POSITION_STATE == "1")
                .Select((a, b, c, d, e) => new UpostEguardDto
                {
                    EGUARD_ID = b.EQUIPMENT_ID,
                    EGUARD_COM_ID = d.EGUARD_COM_ID,
                    EGRUAD_ADDR = e.POSITION_NAME,
                    PASS_TIME_JSON = SqlFunc.HasValue(a.PASS_TIME_JSON) ? a.PASS_TIME_JSON : a.EGUARD_PASS_JSON,
                    AUTH_LIMIT_TYPE = a.AUTH_LIMIT_TYPE,
                    EGUARD_NAME = b.EQUIPMENT_NAME,
                    EPLAN_APPLY_TYPE = a.EPLAN_APPLY_TYPE
                }).ToListAsync();

            var res = new List<UpostEguardCom>();
            res1.AddRange(res2);
            var eguardComList = res1.DistinctBy(p => p.EGUARD_COM_ID).ToList();
            foreach (var item in eguardComList)
            {
                var itemUserEguardList = res1.Where(p => p.EGUARD_COM_ID == item.EGUARD_COM_ID).DistinctBy(i => i.EGUARD_ID).ToList();
                res.Add(new UpostEguardCom
                {
                    EGUARD_COM_ID = item.EGUARD_COM_ID,
                    EGUARD_COM_NAME = item.EGUARD_COM_NAME,
                    UserEguardList = itemUserEguardList
                });
            }
            return new ResultDto { success = true, data = res };
        }

        #endregion


        #region 门禁时间计划

        /// <summary>
        /// 保存门禁时间计划
        /// </summary>
        /// <param name="input"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        public async Task<ResultDto> SaveEguardPassDict(EguardPassDictDto input, ClaimsDto user)
        {
            var result = new ResultDto();
            try
            {
                if (input.EGUARD_PASS_NAME.IsNullOrEmpty())
                {
                    result.msg = "计划名称不能为空!";
                    result.success = false;
                    return result;
                }
                var sysDate = _uow.Db.GetDate();

                if (input.EGUARD_PASS_ID.IsNullOrEmpty())
                {
                    var model = new PMS_EGUARD_PASS_DICT
                    {
                        EGUARD_PASS_ID = IDGenHelper.CreateGuid().Substring(0, 20),
                        EGUARD_PASS_STATE = "1",
                        EGUARD_PASS_NAME = input.EGUARD_PASS_NAME,
                        PASS_TIME_JSON = input.PASS_TIME_JSON,
                        HOSPITAL_ID = user.HOSPITAL_ID,
                        FIRST_RPERSON = user.HIS_NAME,
                        FIRST_RTIME = sysDate,
                        LAST_MPERSON = user.HIS_NAME,
                        LAST_MTIME = sysDate,
                        REMARK = input.REMARK
                    };
                    
                    await _uow.GetRepository<PMS_EGUARD_PASS_DICT>().InsertAsync(model);
                }
                else
                {
                    var model = await _uow.GetRepository<PMS_EGUARD_PASS_DICT>().GetFirstOrDefaultAsync(p => p.EGUARD_PASS_ID == input.EGUARD_PASS_ID);
                    if (model is not null)
                    {
                        var operVerify = await EguardPassOperVerify(input.EGUARD_PASS_ID,"修改");
                        if(operVerify.success == false)
                        {
                            return operVerify;
                        }
                        var data = JsonConvert.DeserializeObject<PassTimeJson>(model.PASS_TIME_JSON);
                        var weekPlanId = data.Id;
                        var json = JsonConvert.DeserializeObject<PassTimeJson>(input.PASS_TIME_JSON);
                        json.Id = weekPlanId;
                        model.EGUARD_PASS_NAME = input.EGUARD_PASS_NAME;
                        model.PASS_TIME_JSON = JsonConvert.SerializeObject(json);
                        model.EGUARD_PASS_STATE = input.EGUARD_PASS_STATE;
                        model.LAST_MPERSON = $"{user.LOGID}_{user.USER_NAME}";
                        model.LAST_MTIME = sysDate;
                        model.REMARK = input.REMARK;
                        await _uow.GetRepository<PMS_EGUARD_PASS_DICT>().UpdateAsync(model);
                    }
                }
                result.msg = "保存成功！";
                result.success = true;
            }
            catch (Exception ex)
            {
                result.msg = $"保存失败！{ex.Message}";
                result.success = false;
            }
            return result;
        }
        /// <summary>
        /// 启用/禁用门禁时间计划
        /// </summary>
        /// <param name="input"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        public async Task<ResultDto> ChangeEguardPassDictState(ChangeEguardPassDictDto input, ClaimsDto user)
        {
            var result = new ResultDto();
            var sysDate = _uow.Db.GetDate();
            var operVerify = await EguardPassOperVerify(input.EGUARD_PASS_ID, input.EGUARD_PASS_STATE == "1" ? "启用" : "禁用");
            if (operVerify.success == false)
            {
                return operVerify;
            }
            try
            {
                var query = _uow.GetRepository<PMS_EGUARD_PASS_DICT>();
                if (query.Any(p => p.EGUARD_PASS_ID == input.EGUARD_PASS_ID && p.EGUARD_PASS_STATE != "2"))
                {
                    await query.UpdateAsync(p => p.EGUARD_PASS_ID == input.EGUARD_PASS_ID && p.EGUARD_PASS_STATE != input.EGUARD_PASS_STATE,
                        result => new PMS_EGUARD_PASS_DICT
                        {
                            EGUARD_PASS_STATE = input.EGUARD_PASS_STATE,
                            LAST_MPERSON = $"{user.LOGID}_{user.USER_NAME}",
                            LAST_MTIME = sysDate
                        });
                    await _uow.SaveChangesAsync();
                    result.success = true;
                    result.msg = input.EGUARD_PASS_STATE == "1" ? "启用" : "禁用" + "成功";
                }
                else
                {
                    result.success = false;
                    result.msg = "操作失败！请刷新数据后再操作";
                }
            }
            catch (Exception ex)
            {
                result.success = false;
                result.msg = $"操作失败！{ex.Message}";
            }
            return result;
        }

        /// <summary>
        /// 删除门禁时间计划
        /// </summary>
        /// <param name="input"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        public async Task<ResultDto> DeleteEguardPassDict(DeleteEguardPassDictDto input, ClaimsDto user)
        {
            var result = new ResultDto();
            var sysDate = _uow.Db.GetDate();
            var operVerify = await EguardPassOperVerify(input.EGUARD_PASS_ID, "删除");
            if (operVerify.success == false)
            {
                return operVerify;
            }
            try
            {
                await _uow.GetRepository<PMS_EGUARD_PASS_DICT>().UpdateAsync(p => p.EGUARD_PASS_ID == input.EGUARD_PASS_ID, p => new PMS_EGUARD_PASS_DICT()
                {
                    EGUARD_PASS_STATE = "2",
                    LAST_MPERSON = $"{user.LOGID}_{user.USER_NAME}",
                    LAST_MTIME = sysDate,
                });

                result.success = true;
                result.msg = "删除成功!";
            }
            catch (Exception ex)
            {
                result.success = false;
                result.msg = $"删除失败！{ex.Message}";
            }
            return result;
        }

        /// <summary>
        /// 获取门禁时间计划列表
        /// </summary>
        /// <param name="input"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        public async Task<ResultDto> GetEguardPassDictList(ClaimsDto user)
        {
            var result = new ResultDto();
            var list = await _uow.GetRepository<PMS_EGUARD_PASS_DICT>().DbSet().Where(p => p.EGUARD_PASS_STATE != "2").ToListAsync();
            var dataList = new List<EguardPassDictDto>();
            foreach (var item in list)
            {
                var data = new EguardPassDictDto();
                data.EGUARD_PASS_ID = item.EGUARD_PASS_ID;
                data.EGUARD_PASS_NAME = item.EGUARD_PASS_NAME;
                data.HOSPITAL_ID = item.HOSPITAL_ID;
                data.EGUARD_PASS_TYPE = AnalyzePassTimeJson(item.PASS_TIME_JSON).TimeType;
                data.EGUARD_PASS_TYPE_NAME = data.EGUARD_PASS_TYPE == "week" ? "按周" : (data.EGUARD_PASS_TYPE == "day" ? "按天" : "-");
                data.PASS_TIME_JSON = item.PASS_TIME_JSON;
                data.EGUARD_PASS_STATE = item.EGUARD_PASS_STATE;
                dataList.Add(data);
            }
            result.data = dataList;
            result.success = true;
            return result;

        }

        /// <summary>
        /// 解析时间计划类型
        /// </summary>
        /// <param name="JSON"></param>
        /// <returns></returns>
        private PassTimeJson AnalyzePassTimeJson(string? JSON)
        {
            var jsonData = new PassTimeJson();
            if (JSON.IsNullOrEmpty())
            {
                return jsonData;
            }
            try
            {
                var data = JsonConvert.DeserializeObject<PassTimeJson>(JSON);
                if (data != null && data.TimeType.IsNotNullOrEmpty())
                {
                    jsonData = data;
                }
            }
            catch (Exception ex)
            {

            }
            return jsonData;
        }



        #endregion


        #region 门禁授权列表
        public async Task<ResultDto> GetEguardList(EguardInput input, ClaimsDto user)
        {
            var result = new ResultDto();
            var list = await GetEguardListByCondtion(input, user);
            result.data = list;
            result.success = true;
            return result;
        }


        /// <summary>
        /// 根据条件查询授权记录
        /// </summary>
        /// <param name="input"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        private async Task<List<EguardOutput>> GetEguardListByCondtion(EguardInput input, ClaimsDto user)
        {
            var list = new List<EguardOutput>();
            //按照门禁组合查询
            var query1 = _uow.GetRepository<PMS_EGUARD_AUTH>().DbSet()
                .InnerJoin<PMS_EGUARD_AUTH_COM>((a, b) => a.EGUARD_AUTH_ID == b.EGUARD_AUTH_ID && b.EGUARD_DATA_TYPE == "1")
                .InnerJoin<PMS_EGUARD_AUTH_POST>((a, b, c) => a.EGUARD_AUTH_ID == c.EGUARD_AUTH_ID)
                .InnerJoin<PMS_EGUARD_COM_DICT>((a, b, c, d) => d.EGUARD_COM_ID == b.EGUARD_DATA_ID)
                .InnerJoin<PMS_EGUARD_COM_LIST>((a, b, c, d, e) => e.EGUARD_COM_ID == d.EGUARD_COM_ID)
                .InnerJoin<EMS_EQUIPMENT_INFO>((a, b, c, d, e, f) => f.EQUIPMENT_ID == e.EQUIPMENT_ID)
                .InnerJoin<SYS6_POSITION_DICT>((a, b, c, d, e, f, g) => g.POSITION_ID == f.POSITION_ID)
                .LeftJoin<PMS_EGUARD_PASS_DICT>((a, b, c, d, e, f, g, h) => a.EGUARD_PASS_ID == h.EGUARD_PASS_ID && h.EGUARD_PASS_STATE == "1")
                .Where((a, b, c, d, e, f, g, h) => a.EGUARD_AUTH_STATE != "2" && b.EGUARD_AUTH_CSTATE != "2" && c.EGUARD_AUTH_PSTATE != "2" && d.EGUARD_COM_STATE == "1" && e.ECOMBINE_STATE == "1" && g.POSITION_STATE != "2")
                .Select((a, b, c, d, e, f, g, h) => new EguardOutput()
                {
                    OnlyId = IDGenHelper.CreateGuid(),
                    EGUARD_COM_ID = d.EGUARD_COM_ID, //门禁组合ID
                    EGUARD_COM_NAME = d.EGUARD_COM_NAME,//门禁组合名称
                    POSITION_ID = g.POSITION_ID, //门禁地点ID
                    POSITION_NAME = g.POSITION_NAME,//--门禁地点名称
                    EQUIPMENT_ID = f.EQUIPMENT_ID,//门禁设备ID
                    EQUIPMENT_NAME = f.EQUIPMENT_NAME,//门禁设备名称
                    EPLAN_APPLY_TYPE = c.EPLAN_APPLY_TYPE,//适用范围
                    AUTH_LIMIT_TYPE = a.AUTH_LIMIT_TYPE,//-授权期限类型 1.长期  2.固定时间 （）
                    EGUARD_PASS_ID = a.EGUARD_PASS_ID,//授权类型是长期的取值  时间字典表
                    EGUARD_PASS_JSON = a.EGUARD_PASS_JSON ?? h.PASS_TIME_JSON,//授权时间是固定时间的 解析这个json串
                    EGUARD_COM_DESC = d.EGUARD_COM_DESC,//门禁组合描述
                    EGUARD_DATA_TYPE = b.EGUARD_DATA_TYPE,//门禁类型
                    EGUARD_DATA_ID = b.EGUARD_DATA_ID,//门禁ID
                    EGUARD_AUTH_TYPE = c.EGUARD_AUTH_TYPE,//岗位人员类型
                    RolePersonId = c.EGUARD_DATA_ID, //--岗位 / 人员ID
                    EGUARD_AUTH_ID = a.EGUARD_AUTH_ID,
                    AUTH_START_DATE = a.AUTH_START_DATE,
                    AUTH_END_DATE = a.AUTH_END_DATE,
                    PUBLISH_STATE = a.PUBLISH_STATE
                }).MergeTable().OrderBy(i => i.EQUIPMENT_NAME);
            var a = query1.ToList();
            //按照门禁ID查询
            var query2 = _uow.GetRepository<PMS_EGUARD_AUTH>().DbSet()
                .InnerJoin<PMS_EGUARD_AUTH_COM>((a, b) => a.EGUARD_AUTH_ID == b.EGUARD_AUTH_ID && b.EGUARD_DATA_TYPE == "2")
                .InnerJoin<PMS_EGUARD_AUTH_POST>((a, b, c) => a.EGUARD_AUTH_ID == c.EGUARD_AUTH_ID)
                .InnerJoin<EMS_EQUIPMENT_INFO>((a, b, c,  f) => f.EQUIPMENT_ID == b.EGUARD_DATA_ID)
                .InnerJoin<SYS6_POSITION_DICT>((a, b, c,  f, g) => g.POSITION_ID == f.POSITION_ID)
                .LeftJoin<PMS_EGUARD_PASS_DICT>((a, b, c, f, g, h) => a.EGUARD_PASS_ID == h.EGUARD_PASS_ID && h.EGUARD_PASS_STATE == "1")
                .Where((a, b, c, f, g, h) => a.EGUARD_AUTH_STATE != "2" && b.EGUARD_AUTH_CSTATE != "2" && c.EGUARD_AUTH_PSTATE != "2" && g.POSITION_STATE != "2")
                .Select((a, b, c, f, g, h) => new EguardOutput()
                {
                    OnlyId = IDGenHelper.CreateGuid(),
                    //EGUARD_COM_ID = e.EGUARD_COM_ID, //门禁组合ID
                    //EGUARD_COM_NAME = e.EGUARD_COM_NAME,//门禁组合名称
                    POSITION_ID = g.POSITION_ID, //门禁地点ID
                    POSITION_NAME = g.POSITION_NAME,//--门禁地点名称
                    EQUIPMENT_ID = f.EQUIPMENT_ID,//门禁设备ID
                    EQUIPMENT_NAME = f.EQUIPMENT_NAME,//门禁设备名称
                    EPLAN_APPLY_TYPE = c.EPLAN_APPLY_TYPE,//适用范围
                    AUTH_LIMIT_TYPE = a.AUTH_LIMIT_TYPE,//-授权期限类型 1.长期  2.固定时间 （）
                    EGUARD_PASS_ID = a.EGUARD_PASS_ID,//授权类型是长期的取值  时间字典表
                    EGUARD_PASS_JSON = a.EGUARD_PASS_JSON ?? h.PASS_TIME_JSON,//授权时间是固定时间的 解析这个json串
                    EGUARD_DATA_TYPE = b.EGUARD_DATA_TYPE,//门禁类型
                    EGUARD_DATA_ID = b.EGUARD_DATA_ID,//门禁ID
                    EGUARD_AUTH_TYPE = c.EGUARD_AUTH_TYPE,//岗位人员类型
                    RolePersonId = c.EGUARD_DATA_ID, //--岗位 / 人员ID
                    EGUARD_AUTH_ID = a.EGUARD_AUTH_ID,
                    AUTH_START_DATE = a.AUTH_START_DATE,
                    AUTH_END_DATE = a.AUTH_END_DATE,
                    PUBLISH_STATE = a.PUBLISH_STATE
                }).MergeTable().OrderBy(i => i.EQUIPMENT_NAME);
            if (input.EGUARD_COM_ID.IsNotNullOrEmpty())
            {
                query1 = query1.Where(a => a.EGUARD_COM_ID == input.EGUARD_COM_ID);
                query2 = query2.Where(a => a.EGUARD_COM_ID == input.EGUARD_COM_ID);
            }
            if (input.POSITION_ID.IsNotNullOrEmpty())
            {
                query1 = query1.Where(a => a.POSITION_ID == input.POSITION_ID);
                query2 = query2.Where(a => a.POSITION_ID == input.POSITION_ID);
            }
            if (input.EQUIPMENT_NAME.IsNotNullOrEmpty())
            {
                query1 = query1.Where(a => a.EQUIPMENT_NAME != null && a.EQUIPMENT_NAME.Contains(input.EQUIPMENT_NAME));
                query2 = query2.Where(a => a.EQUIPMENT_NAME != null && a.EQUIPMENT_NAME.Contains(input.EQUIPMENT_NAME));
            }
            if (input.RoleIds != null && input.RoleIds.Count > 0)
            {
                query1 = query1.Where(a => input.RoleIds.Contains(a.RolePersonId));
                query2 = query2.Where(a => input.RoleIds.Contains(a.RolePersonId));
            }
            //按人员，需要展示关联的科室、管理专业组、检验专业组、岗位、岗位角色
            if (input.PersonIds != null && input.PersonIds.Count > 0)
            {
                var userIds = await _uow.Db.Queryable<PMS_PERSON_INFO>().InnerJoin<SYS6_USER>((a, b) => a.USER_ID == b.USER_NO)
                    .Where((a, b) => input.PersonIds.Contains(a.PERSON_ID) && a.PERSON_STATE == "1").Select((a, b) => b.USER_NO).ToListAsync();
                //岗位角色
                var roleIds = await _uow.Db.Queryable<SYS6_USER_POST>()
                    .InnerJoin<SYS6_POST_ROLE>((a, b) => a.POST_ID == b.POSTROLE_ID)
                    .Where((a, b) => userIds.Contains(a.USER_NO) && a.UPOST_STATE == "1").Select((a, b) => b.POSTROLE_ID).ToListAsync();
                //岗位
                var postList = await _uow.Db.Queryable<SYS6_POST_ROLE>()
                    .InnerJoin<SYS6_POST>((a, b) => a.POST_ID == b.POST_ID)
                    .Where((a, b) => roleIds.Contains(a.POSTROLE_ID) && b.POST_STATE == "1")
                    .Select((a, b) => new
                    {
                        b.LAB_ID,
                        b.POST_ULEVEL,
                        b.PGROUP_ID,
                        b.POST_ID
                    }).ToListAsync();
                var postIds = postList.Select(p => p.POST_ID).ToList();
                //检验专业组
                var pgroupIds = postList.Where(p => p.POST_ULEVEL.IsNullOrEmpty() || p.POST_ULEVEL == "P").Select(i => i.PGROUP_ID).ToList();
                var pgroupList = await _uow.Db.Queryable<SYS6_INSPECTION_PGROUP>().Where(p => pgroupIds.Contains(p.PGROUP_ID) && p.PGROUP_STATE == "1").Select(i => new
                {
                    i.MGROUP_ID,
                    i.PGROUP_ID
                }).ToListAsync();
                pgroupIds = pgroupList.Select(p => p.PGROUP_ID).ToList();
                //管理专业组
                var mgroupIds = postList.Where(p => p.POST_ULEVEL == "M").Select(i => i.PGROUP_ID).ToList();
                mgroupIds.AddRange(pgroupList.Where(p => p.MGROUP_ID.IsNotNullOrEmpty()).Select(i => i.MGROUP_ID).ToList());
                var mgroupList = await _uow.Db.Queryable<SYS6_INSPECTION_MGROUP>().Where(p => mgroupIds.Contains(p.MGROUP_ID) && p.MGROUP_STATE == "1").Select(i => new
                {
                    i.MGROUP_ID,
                    i.LAB_ID,
                }).ToListAsync();
                mgroupIds = mgroupList.Select(i => i.MGROUP_ID).ToList();
                //科室
                var labIds = await _uow.Db.Queryable<SYS6_INSPECTION_LAB>().Where(p => postList.Select(i => i.LAB_ID).Contains(p.LAB_ID) && p.STATE_FLAG == "1").Select(i => i.LAB_ID).ToListAsync();
                query1 = query1.Where(a => input.PersonIds.Contains(a.RolePersonId)
                || (a.EPLAN_APPLY_TYPE == "PROLE" && roleIds.Contains(a.RolePersonId))
                || (a.EPLAN_APPLY_TYPE == "POST" && postIds.Contains(a.RolePersonId))
                || (a.EPLAN_APPLY_TYPE == "PGROUP" && pgroupIds.Contains(a.RolePersonId))
                || (a.EPLAN_APPLY_TYPE == "MGROUP" && mgroupIds.Contains(a.RolePersonId))
                || (a.EPLAN_APPLY_TYPE == "LAB" && labIds.Contains(a.RolePersonId)));

                query2 = query2.Where(a => input.PersonIds.Contains(a.RolePersonId)
                || (a.EPLAN_APPLY_TYPE == "PROLE" && roleIds.Contains(a.RolePersonId))
                || (a.EPLAN_APPLY_TYPE == "POST" && postIds.Contains(a.RolePersonId))
                || (a.EPLAN_APPLY_TYPE == "PGROUP" && pgroupIds.Contains(a.RolePersonId))
                || (a.EPLAN_APPLY_TYPE == "MGROUP" && mgroupIds.Contains(a.RolePersonId))
                || (a.EPLAN_APPLY_TYPE == "LAB" && labIds.Contains(a.RolePersonId)));
            }

            var list1 = await query1.ToListAsync();
            var list2 = await query2.ToListAsync();
            if (list1 != null && list1.Count > 0)
            {
                list.AddRange(list1);
            }
            if (list2 != null && list2.Count > 0)
            {
                list.AddRange(list2);
            }
            list = list.OrderByDescending(p => p.EGUARD_COM_ID).ThenByDescending(p => p.EQUIPMENT_ID).ToList();
            if (list != null && list.Count > 0)
            {
                ///*查询时间节点*/
                //var passList = await _uow.GetRepository<PMS_EGUARD_PASS_DICT>().DbSet().Where(a => a.EGUARD_PASS_STATE == "1").ToListAsync();

                foreach (var item in list)
                {
                    item.OnlyId = IDGenHelper.CreateGuid();
                }
                var listCom = list.Where(p => p.EGUARD_COM_ID.IsNotNullOrEmpty()).DistinctBy(p => p.EGUARD_COM_ID).ToList();
                foreach(var item in listCom)
                {
                    item.ROWNO_MERGE_COUNT = list.Where(p => p.EGUARD_COM_ID == item.EGUARD_COM_ID).Count();
                }
                var listEquipment = list.Where(p => p.EGUARD_COM_ID.IsNullOrEmpty()).DistinctBy(p => p.EQUIPMENT_ID).ToList();
                foreach(var item in listEquipment)
                {
                    item.ROWNO_MERGE_COUNT = list.Where(p => p.EQUIPMENT_ID == item.EQUIPMENT_ID && p.EGUARD_COM_ID.IsNullOrEmpty()).Count();
                }
                listCom.AddRange(listEquipment);
                foreach (var item in list)
                {
                    if (listCom.Select(p => p.OnlyId).Contains(item.OnlyId))
                    {
                        item.ROWCOM_MERGE_COUNT = listCom.Where(p => p.OnlyId == item.OnlyId).FirstOrDefault()?.ROWNO_MERGE_COUNT;
                        item.ROWNO_MERGE_COUNT = listCom.Where(p => p.OnlyId == item.OnlyId).FirstOrDefault()?.ROWNO_MERGE_COUNT;
                    }
                }
            }
            return list;
        }

        public async Task<ResultDto> NewDeleteEguard(UpdateEguardAuth request,string operPeron)
        {
            //var eguardAuth = await _uow.Db.Queryable<PMS_EGUARD_AUTH_POST>()
            //    .InnerJoin<PMS_EGUARD_AUTH>((a, b) => a.EGUARD_AUTH_ID == a.EGUARD_AUTH_ID)
            //    .InnerJoin<PMS_EGUARD_AUTH_COM>((a, b, c) => a.EGUARD_AUTH_ID == c.EGUARD_AUTH_ID && c.EGUARD_DATA_TYPE == request.EGUARD_COM_TYPE)
            //    .Where((a, b, c) => a.EGUARD_AUTH_PSTATE == "1" && b.EGUARD_AUTH_STATE == "1" && c.EGUARD_AUTH_CSTATE == "1" && request.EGUARD_DATA_ID.Contains(a.EGUARD_DATA_ID)
            //    && 
            //    && a.EGUARD_AUTH_TYPE == request.EGUARD_AUTH_TYPE && a.EPLAN_APPLY_TYPE == request.EPLAN_APPLY_TYPE)
            //    .Select((a, b, c) => new
            //    {
            //        a.EGUARD_AUTH_ID,
            //        c.EGUARD_DATA_ID,
            //        b.AUTH_LIMIT_TYPE,
            //        b.AUTH_START_DATE,
            //        b.AUTH_END_DATE,
            //        b.EGUARD_PASS_ID,
            //        b.EGUARD_PASS_JSON
            //    }).MergeTable().ToListAsync();
            var eguardAuth = await _uow.Db.Queryable<PMS_EGUARD_AUTH>()
                .InnerJoin<PMS_EGUARD_AUTH_COM>((a,b) => a.EGUARD_AUTH_ID == b.EGUARD_AUTH_ID)
                .InnerJoin<PMS_EGUARD_AUTH_POST>((a,b,c) => a.EGUARD_AUTH_ID == c.EGUARD_AUTH_ID)
                .Where((a,b,c) => b.EGUARD_DATA_TYPE == request.EGUARD_COM_TYPE 
                && a.EGUARD_AUTH_STATE == "1" && b.EGUARD_AUTH_CSTATE == "1" 
                && c.EGUARD_AUTH_PSTATE == "1" && request.EGUARD_DATA_ID.Contains(c.EGUARD_DATA_ID)
                && b.EGUARD_DATA_ID == request.EGUARD_COM_ID && b.EGUARD_DATA_TYPE == request.EGUARD_COM_TYPE
                && c.EGUARD_AUTH_TYPE == request.EGUARD_AUTH_TYPE && c.EPLAN_APPLY_TYPE == request.EPLAN_APPLY_TYPE)
                .Select((a,b,c) => new
                {
                    c.EGUARD_AUTH_ID,
                    b.EGUARD_DATA_ID,
                    a.AUTH_LIMIT_TYPE,
                    a.AUTH_START_DATE,
                    a.AUTH_END_DATE,
                    a.EGUARD_PASS_ID,
                    a.EGUARD_PASS_JSON
                }).MergeTable().ToListAsync();
            var eguardIds = eguardAuth.Select(i => i.EGUARD_AUTH_ID).Distinct().ToArray();
            await DeleteAccessControl(eguardIds, operPeron);
            return new ResultDto { success = true };

        }
        public async Task<ResultDto> GetUpdateEguardAuth(UpdateEguardAuth request)
        {
            var eguardAuth = await _uow.Db.Queryable<PMS_EGUARD_AUTH>()
                .InnerJoin<PMS_EGUARD_AUTH_COM>((a, b) => a.EGUARD_AUTH_ID == b.EGUARD_AUTH_ID && b.EGUARD_DATA_TYPE == request.EGUARD_COM_TYPE)
                .InnerJoin<PMS_EGUARD_AUTH_POST>((a, b, c) => a.EGUARD_AUTH_ID == c.EGUARD_AUTH_ID && c.EGUARD_AUTH_TYPE == request.EGUARD_AUTH_TYPE && c.EPLAN_APPLY_TYPE == request.EPLAN_APPLY_TYPE)
                .Where((a, b, c) => a.EGUARD_AUTH_STATE == "1" && b.EGUARD_AUTH_CSTATE == "1" && c.EGUARD_AUTH_PSTATE == "1" 
                && request.EGUARD_DATA_ID.Contains(c.EGUARD_DATA_ID) && request.EGUARD_COM_ID.Contains(b.EGUARD_DATA_ID))
                .Select((a, b, c) => new
                {
                    a.EGUARD_AUTH_ID,
                    b.EGUARD_DATA_ID,
                    a.AUTH_LIMIT_TYPE,
                    a.AUTH_START_DATE,
                    a.AUTH_END_DATE,
                    a.EGUARD_PASS_ID,
                    a.EGUARD_PASS_JSON
                }).MergeTable().ToListAsync();
            var eguardComId = eguardAuth.Select(i => i.EGUARD_DATA_ID).Distinct().ToList();
            var res = new NewEguardSaveDto
            {
                EGUARD_DATA_TYPE = request.EGUARD_COM_TYPE,
                EGUARD_COM_IDs = request.EGUARD_COM_TYPE == "1" ? eguardComId : null,
                EQUIPMENT_IDs = request.EGUARD_COM_TYPE == "2" ? eguardComId : null,
                EGUARD_AUTH_TYPE = request.EGUARD_AUTH_TYPE,
                RoleIds = request.EGUARD_AUTH_TYPE == "1" ? request.EGUARD_DATA_ID : null,
                PersonIds = request.EGUARD_AUTH_TYPE == "2" ? request.EGUARD_DATA_ID : null,
                EPLAN_APPLY_TYPE = request.EPLAN_APPLY_TYPE
            };
            foreach(var item in eguardAuth.DistinctBy(p => p.EGUARD_AUTH_ID))
            {
                res.EguardSaveItem.Add(new NewEguardSaveItem
                {
                    EGUARD_AUTH_ID = item.EGUARD_AUTH_ID,
                    AUTH_LIMIT_TYPE = item.AUTH_LIMIT_TYPE,
                    AUTH_START_DATE = item.AUTH_START_DATE,
                    AUTH_END_DATE = item.AUTH_END_DATE,
                    EGUARD_PASS_ID = item.EGUARD_PASS_ID,
                    EGUARD_PASS_JSON = item.EGUARD_PASS_JSON
                });
            }
            return new ResultDto { success = true, data = res };
        }
        /// <summary>
        /// 保存授权设置（保存修改）
        /// </summary>
        /// <param name="input"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        public async Task<ResultDto> SaveEguard(EguardSaveDto input, ClaimsDto user)
        {
            var result = new ResultDto();
            var sysDate = _uow.Db.GetDate();
            result.data = input.EGUARD_AUTH_ID;
            try
            {
                using (_uow.Begin())
                {
                    if (input.EGUARD_AUTH_ID.IsNullOrEmpty()) //新增
                    {
                        //门禁授权字典
                        var eguardAuth = new PMS_EGUARD_AUTH()
                        {
                            EGUARD_AUTH_ID = IDGenHelper.CreateGuid().Substring(0, 20),
                            HOSPITAL_ID = user.HOSPITAL_ID,
                            EGUARD_TYPE = input.EGUARD_DATA_TYPE,
                            AUTH_LIMIT_TYPE = input.AUTH_LIMIT_TYPE,
                            AUTH_START_DATE = input.AUTH_START_DATE,
                            AUTH_END_DATE = input.AUTH_END_DATE,
                            EGUARD_PASS_ID = input.EGUARD_PASS_ID,
                            EGUARD_PASS_JSON = input.EGUARD_PASS_JSON,
                            EGUARD_AUTH_STATE = "1",
                            FIRST_RPERSON = $"{user.LOGID}_{user.USER_NAME}",
                            FIRST_RTIME = sysDate,
                            LAST_MPERSON = $"{user.LOGID}_{user.USER_NAME}",
                            LAST_MTIME = sysDate,
                            PUBLISH_STATE = "0"
                        };
                        input.EGUARD_AUTH_ID = eguardAuth.EGUARD_AUTH_ID;
                        result.data = eguardAuth.EGUARD_AUTH_ID;
                        await _uow.Db.Insertable(eguardAuth).ExecuteCommandAsync();
                    }
                    else //修改
                    {
                        if (_uow.GetRepository<PMS_EGUARD_AUTH>().DbSet().Count(p => p.EGUARD_AUTH_ID == input.EGUARD_AUTH_ID) > 0)
                        {
                            //修改授权字典表
                            await _uow.GetRepository<PMS_EGUARD_AUTH>().UpdateAsync(p => p.EGUARD_AUTH_ID == input.EGUARD_AUTH_ID, p => new PMS_EGUARD_AUTH()
                            {
                                EGUARD_TYPE = input.EGUARD_DATA_TYPE,
                                AUTH_LIMIT_TYPE = input.AUTH_LIMIT_TYPE,
                                AUTH_START_DATE = input.AUTH_START_DATE,
                                AUTH_END_DATE = input.AUTH_END_DATE,
                                EGUARD_PASS_ID = input.EGUARD_PASS_ID,
                                EGUARD_PASS_JSON = input.EGUARD_PASS_JSON,
                                EGUARD_AUTH_STATE = "1",
                                LAST_MPERSON = $"{user.LOGID}_{user.USER_NAME}",
                                LAST_MTIME = sysDate
                            });
                        }
                    }
                    
                    if (input.EGUARD_DATA_TYPE == "1" && input.EGUARD_COM_IDs != null && input.EGUARD_COM_IDs.Count > 0)
                    {
                        var oldEguardAuthCom = await _uow.Db.Queryable<PMS_EGUARD_AUTH_COM>()
                            .Where(p => p.EGUARD_AUTH_ID == input.EGUARD_AUTH_ID && input.EGUARD_COM_IDs.Contains(p.EGUARD_DATA_ID) && p.EGUARD_AUTH_CSTATE == "1" && p.EGUARD_DATA_TYPE == "1").ToListAsync();
                        var eguardAuthComId = input.EGUARD_COM_IDs.Where(p => !oldEguardAuthCom.Select(i => i.EGUARD_DATA_ID).Contains(p)).ToList();
                        //【按照门禁组合】门禁授权设备/组合关系
                        var eguardAuthCom = new List<PMS_EGUARD_AUTH_COM>();
                        foreach (var comId in eguardAuthComId)
                        {
                            eguardAuthCom.Add(new PMS_EGUARD_AUTH_COM()
                            {
                                EGUARD_AUTH_CID = IDGenHelper.CreateGuid(),
                                EGUARD_AUTH_ID = input.EGUARD_AUTH_ID,
                                EGUARD_DATA_ID = comId,
                                EGUARD_DATA_TYPE = "1",//门禁组合
                                EGUARD_AUTH_CSTATE = "1",
                                FIRST_RPERSON = $"{user.LOGID}_{user.USER_NAME}",
                                FIRST_RTIME = sysDate,
                                LAST_MPERSON = $"{user.LOGID}_{user.USER_NAME}",
                                LAST_MTIME = sysDate
                            });
                        }
                        if (eguardAuthCom.Count > 0)
                        {
                            await _uow.Db.Insertable(eguardAuthCom).UseParameter().ExecuteCommandAsync();
                        }
                    }
                    else if (input.EGUARD_DATA_TYPE == "2" && input.EQUIPMENT_IDs != null && input.EQUIPMENT_IDs.Count > 0)
                    {
                        var oldEguardAuthCom = await _uow.Db.Queryable<PMS_EGUARD_AUTH_COM>()
                            .Where(p => p.EGUARD_AUTH_ID == input.EGUARD_AUTH_ID && input.EQUIPMENT_IDs.Contains(p.EGUARD_DATA_ID) && p.EGUARD_AUTH_CSTATE == "1" && p.EGUARD_DATA_TYPE == "2").ToListAsync();
                        var eguardAuthComId = input.EQUIPMENT_IDs.Where(p => !oldEguardAuthCom.Select(i => i.EGUARD_DATA_ID).Contains(p)).ToList();
                        //【按照门禁】门禁授权设备/组合关系
                        var eguardAuthCom = new List<PMS_EGUARD_AUTH_COM>();
                        foreach (var comId in eguardAuthComId)
                        {
                            eguardAuthCom.Add(new PMS_EGUARD_AUTH_COM()
                            {
                                EGUARD_AUTH_CID = IDGenHelper.CreateGuid(),
                                EGUARD_AUTH_ID = input.EGUARD_AUTH_ID,
                                EGUARD_DATA_ID = comId,
                                EGUARD_DATA_TYPE = "2",//门禁
                                EGUARD_AUTH_CSTATE = "1",
                                FIRST_RPERSON = $"{user.LOGID}_{user.USER_NAME}",
                                FIRST_RTIME = sysDate,
                                LAST_MPERSON = $"{user.LOGID}_{user.USER_NAME}",
                                LAST_MTIME = sysDate
                            });
                        }
                        if (eguardAuthCom.Count > 0)
                        {
                            await _uow.Db.Insertable(eguardAuthCom).UseParameter().ExecuteCommandAsync();
                        }
                    }

                    if (input.EGUARD_AUTH_TYPE == "1" && input.RoleIds != null && input.RoleIds.Count > 0)
                    {
                        var oldEguardAuthPost = await _uow.Db.Queryable<PMS_EGUARD_AUTH_POST>()
                            .Where(p => p.EGUARD_AUTH_ID == input.EGUARD_AUTH_ID && input.RoleIds.Contains(p.EGUARD_DATA_ID) && p.EGUARD_AUTH_PSTATE == "1" && p.EGUARD_AUTH_TYPE == "1").ToListAsync();
                        var eguardAuthPostId = input.RoleIds.Where(p => !oldEguardAuthPost.Select(i => i.EGUARD_DATA_ID).Contains(p)).ToList();
                        //按照岗位保存
                        var eguardAuthPost = new List<PMS_EGUARD_AUTH_POST>();
                        foreach (var roleId in eguardAuthPostId)
                        {
                            eguardAuthPost.Add(new PMS_EGUARD_AUTH_POST()
                            {
                                EGUARD_AUTH_PID = IDGenHelper.CreateGuid(),
                                EGUARD_AUTH_ID = input.EGUARD_AUTH_ID,
                                EGUARD_AUTH_TYPE = "1",
                                EPLAN_APPLY_TYPE = input.EPLAN_APPLY_TYPE,/*GetApplyTypeName(input.EPLAN_APPLY_TYPE),*/
                                EGUARD_DATA_ID = roleId,
                                EGUARD_AUTH_PSTATE = "1",
                                FIRST_RPERSON = $"{user.LOGID}_{user.USER_NAME}",
                                FIRST_RTIME = sysDate,
                                LAST_MPERSON = $"{user.LOGID}_{user.USER_NAME}",
                                LAST_MTIME = sysDate
                            });
                        }
                        if (eguardAuthPost.Count > 0)
                        {
                            await _uow.Db.Insertable(eguardAuthPost).UseParameter().ExecuteCommandAsync();
                        }
                    }
                    else if (input.EGUARD_AUTH_TYPE == "2" && input.PersonIds != null && input.PersonIds.Count > 0)
                    {
                        var oldEguardAuthPost = await _uow.Db.Queryable<PMS_EGUARD_AUTH_POST>()
                            .Where(p => p.EGUARD_AUTH_ID == input.EGUARD_AUTH_ID && input.PersonIds.Contains(p.EGUARD_DATA_ID) && p.EGUARD_AUTH_PSTATE == "1" && p.EGUARD_AUTH_TYPE == "2").ToListAsync();
                        var eguardAuthPostId = input.PersonIds.Where(p => !oldEguardAuthPost.Select(i => i.EGUARD_DATA_ID).Contains(p)).ToList();
                        //按照人员保存
                        var eguardAuthPost = new List<PMS_EGUARD_AUTH_POST>();
                        foreach (var personId in eguardAuthPostId)
                        {
                            eguardAuthPost.Add(new PMS_EGUARD_AUTH_POST()
                            {
                                EGUARD_AUTH_PID = IDGenHelper.CreateGuid(),
                                EGUARD_AUTH_ID = input.EGUARD_AUTH_ID,
                                EGUARD_AUTH_TYPE = "2",
                                EPLAN_APPLY_TYPE = input.EPLAN_APPLY_TYPE,/* GetApplyTypeName(input.EPLAN_APPLY_TYPE),*/
                                EGUARD_DATA_ID = personId,
                                EGUARD_AUTH_PSTATE = "1",
                                FIRST_RPERSON = $"{user.LOGID}_{user.USER_NAME}",
                                FIRST_RTIME = sysDate,
                                LAST_MPERSON = $"{user.LOGID}_{user.USER_NAME}",
                                LAST_MTIME = sysDate
                            });
                        }
                        if (eguardAuthPost.Count > 0)
                        {
                            await _uow.Db.Insertable(eguardAuthPost).UseParameter().ExecuteCommandAsync();
                        }
                    }
                    
                    await _uow.SaveChangesAsync();
                    result.success = true;
                    result.msg = "保存成功";
                }
            }
            catch (Exception ex)
            {
                result.success = false;
                result.msg = $"保存失败！{ex.Message}";
            }
            return result;
        }

        public async Task<ResultDto> NewEguardSave(NewEguardSaveDto request, ClaimsDto user)
        {
            var result = new ResultDto();
            var sysDate = _uow.Db.GetDate();
            var eguardAuth_insert = new List<PMS_EGUARD_AUTH>();
            var eguardAuth_update = new List<PMS_EGUARD_AUTH>();
            var eguardAuthCom_delete = new List<PMS_EGUARD_AUTH_COM>();
            var eguardAuthCom_insert = new List<PMS_EGUARD_AUTH_COM>();
            var eguardAuthPost_insert = new List<PMS_EGUARD_AUTH_POST>();
            var publishAuthIds = new List<string>();
            var operPerson = $"{user.LOGID}_{user.USER_NAME}";
            //授权新增
            foreach (var item in request.EguardSaveItem.Where(p => p.EGUARD_AUTH_ID.IsNullOrEmpty()))
            {
                var itemEguardAuth = new PMS_EGUARD_AUTH
                {
                    EGUARD_AUTH_ID = IDGenHelper.CreateGuid().Substring(0, 20),
                    HOSPITAL_ID = user.HOSPITAL_ID,
                    EGUARD_TYPE = request.EGUARD_DATA_TYPE,
                    AUTH_LIMIT_TYPE = item.AUTH_LIMIT_TYPE,
                    AUTH_START_DATE = item.AUTH_START_DATE,
                    AUTH_END_DATE = item.AUTH_END_DATE,
                    EGUARD_PASS_ID = item.EGUARD_PASS_ID,
                    EGUARD_PASS_JSON = item.EGUARD_PASS_JSON,
                    EGUARD_AUTH_STATE = "1",
                    FIRST_RPERSON = operPerson,
                    FIRST_RTIME = sysDate,
                    LAST_MPERSON = operPerson,
                    LAST_MTIME = sysDate,
                };
                eguardAuth_insert.Add(itemEguardAuth);
                item.ID = itemEguardAuth.EGUARD_AUTH_ID;
                publishAuthIds.Add(itemEguardAuth.EGUARD_AUTH_ID);
            }
            //授权修改
            var updateAuthId = request.EguardSaveItem.Where(p => p.EGUARD_AUTH_ID.IsNotNullOrEmpty()).Select(i => i.EGUARD_AUTH_ID).ToList();
            var oldEguardAuth = await _uow.Db.Queryable<PMS_EGUARD_AUTH>().Where(p => updateAuthId.Contains(p.EGUARD_AUTH_ID)).ToListAsync();
            foreach (var item in oldEguardAuth)
            {
                var itemUpdateAuth = request.EguardSaveItem.Where(p => p.EGUARD_AUTH_ID == item.EGUARD_AUTH_ID).First();
                if (itemUpdateAuth != null)
                {
                    //若授权内容有修改，发布状态改为“0”（未发布）
                    if (item.AUTH_LIMIT_TYPE != itemUpdateAuth.AUTH_LIMIT_TYPE || item.AUTH_START_DATE != itemUpdateAuth.AUTH_START_DATE || item.AUTH_END_DATE != itemUpdateAuth.AUTH_END_DATE
                        || item.EGUARD_PASS_ID != itemUpdateAuth.EGUARD_PASS_ID || item.EGUARD_PASS_JSON != itemUpdateAuth.EGUARD_PASS_JSON)
                    {
                        publishAuthIds.Add(item.EGUARD_AUTH_ID);
                    }
                    item.EGUARD_TYPE = request.EGUARD_DATA_TYPE;
                    item.AUTH_LIMIT_TYPE = itemUpdateAuth.AUTH_LIMIT_TYPE;
                    item.AUTH_START_DATE = itemUpdateAuth.AUTH_START_DATE;
                    item.AUTH_END_DATE = itemUpdateAuth.AUTH_END_DATE;
                    item.EGUARD_PASS_ID = itemUpdateAuth.EGUARD_PASS_ID;
                    item.EGUARD_PASS_JSON = itemUpdateAuth.EGUARD_PASS_JSON;
                    item.EGUARD_AUTH_STATE = "1";
                    item.LAST_MPERSON = operPerson;
                    item.LAST_MTIME = sysDate;
                    eguardAuth_update.Add(item);
                }
            }
            //门禁组合授权关联
            if (request.EGUARD_DATA_TYPE == "1" && request.EGUARD_COM_IDs != null && request.EGUARD_COM_IDs.Count > 0)
            {
                var oldEguardAuthCom = await _uow.Db.Queryable<PMS_EGUARD_AUTH_COM>()
                    .Where(p => request.EguardSaveItem.Select(i => i.EGUARD_AUTH_ID).Contains(p.EGUARD_AUTH_ID) && p.EGUARD_AUTH_CSTATE == "1" && p.EGUARD_DATA_TYPE == "1").ToListAsync();
                foreach (var item in request.EguardSaveItem)
                {
                    if (item.EGUARD_AUTH_ID.IsNotNullOrEmpty())
                    {
                        var itemEguardAuthCom = oldEguardAuthCom.Where(p => p.EGUARD_AUTH_ID == item.EGUARD_AUTH_ID).ToList();
                        var eguardAuthDataId = request.EGUARD_COM_IDs.Where(p => !itemEguardAuthCom.Select(i => i.EGUARD_DATA_ID).Contains(p)).ToList();
                        var deleteCom = itemEguardAuthCom.Where(p => !request.EGUARD_COM_IDs.Contains(p.EGUARD_DATA_ID)).ToList();
                        foreach (var itemAuthData in eguardAuthDataId)
                        {
                            eguardAuthCom_insert.Add(new PMS_EGUARD_AUTH_COM
                            {
                                EGUARD_AUTH_CID = IDGenHelper.CreateGuid(),
                                EGUARD_AUTH_ID = item.EGUARD_AUTH_ID,
                                EGUARD_DATA_ID = itemAuthData,
                                EGUARD_DATA_TYPE = "1",//门禁组合
                                EGUARD_AUTH_CSTATE = "1",
                                FIRST_RPERSON = operPerson,
                                FIRST_RTIME = sysDate,
                                LAST_MPERSON = operPerson,
                                LAST_MTIME = sysDate
                            });
                        }
                        foreach(var itemDelete in deleteCom)
                        {
                            itemDelete.EGUARD_AUTH_CSTATE = "2";
                            itemDelete.LAST_MPERSON = operPerson;
                            itemDelete.LAST_MTIME = sysDate;
                            eguardAuthCom_delete.Add(itemDelete);
                        }
                    }
                    else
                    {
                        foreach(var itemCom in request.EGUARD_COM_IDs)
                        {
                            eguardAuthCom_insert.Add(new PMS_EGUARD_AUTH_COM
                            {
                                EGUARD_AUTH_CID = IDGenHelper.CreateGuid(),
                                EGUARD_AUTH_ID = item.ID,
                                EGUARD_DATA_ID = itemCom,
                                EGUARD_DATA_TYPE = "1",//门禁组合
                                EGUARD_AUTH_CSTATE = "1",
                                FIRST_RPERSON = operPerson,
                                FIRST_RTIME = sysDate,
                                LAST_MPERSON = operPerson,
                                LAST_MTIME = sysDate
                            });
                        }
                    }
                }
            }
            //门禁授权关联
            if (request.EGUARD_DATA_TYPE == "2" && request.EQUIPMENT_IDs != null && request.EQUIPMENT_IDs.Count > 0)
            {
                var oldEguardAuthCom = await _uow.Db.Queryable<PMS_EGUARD_AUTH_COM>()
                    .Where(p => request.EguardSaveItem.Select(i => i.EGUARD_AUTH_ID).Contains(p.EGUARD_AUTH_ID) && p.EGUARD_AUTH_CSTATE == "1" && p.EGUARD_DATA_TYPE == "1").ToListAsync();
                foreach (var item in request.EguardSaveItem)
                {
                    if (item.EGUARD_AUTH_ID.IsNotNullOrEmpty())
                    {
                        var itemEguardAuthCom = oldEguardAuthCom.Where(p => p.EGUARD_AUTH_ID == item.EGUARD_AUTH_ID).ToList();
                        var eguardAuthDataId = request.EQUIPMENT_IDs.Where(p => !itemEguardAuthCom.Select(i => i.EGUARD_DATA_ID).Contains(p)).ToList();
                        var deleteCom = itemEguardAuthCom.Where(p => !request.EQUIPMENT_IDs.Contains(p.EGUARD_DATA_ID)).ToList();
                        foreach (var itemAuthData in eguardAuthDataId)
                        {
                            eguardAuthCom_insert.Add(new PMS_EGUARD_AUTH_COM
                            {
                                EGUARD_AUTH_CID = IDGenHelper.CreateGuid(),
                                EGUARD_AUTH_ID = item.EGUARD_AUTH_ID,
                                EGUARD_DATA_ID = itemAuthData,
                                EGUARD_DATA_TYPE = "2",//门禁组合
                                EGUARD_AUTH_CSTATE = "1",
                                FIRST_RPERSON = operPerson,
                                FIRST_RTIME = sysDate,
                                LAST_MPERSON = operPerson,
                                LAST_MTIME = sysDate
                            });
                        }
                        foreach (var itemDelete in deleteCom)
                        {
                            itemDelete.EGUARD_AUTH_CSTATE = "2";
                            itemDelete.LAST_MPERSON = operPerson;
                            itemDelete.LAST_MTIME = sysDate;
                            eguardAuthCom_delete.Add(itemDelete);
                        }
                    }
                    else
                    {
                        foreach(var itemEquip in request.EQUIPMENT_IDs)
                        {
                            eguardAuthCom_insert.Add(new PMS_EGUARD_AUTH_COM
                            {
                                EGUARD_AUTH_CID = IDGenHelper.CreateGuid(),
                                EGUARD_AUTH_ID = item.ID,
                                EGUARD_DATA_ID = itemEquip,
                                EGUARD_DATA_TYPE = "2",//门禁组合
                                EGUARD_AUTH_CSTATE = "1",
                                FIRST_RPERSON = operPerson,
                                FIRST_RTIME = sysDate,
                                LAST_MPERSON = operPerson,
                                LAST_MTIME = sysDate
                            });
                        }
                    }
                }
            }
            //门禁组合授权关联人员
            if (request.EGUARD_AUTH_TYPE == "1" && request.RoleIds != null && request.RoleIds.Count > 0)
            {
                var oldEguardAuthPost = await _uow.Db.Queryable<PMS_EGUARD_AUTH_POST>()
                    .Where(p => request.EguardSaveItem.Select(i => i.EGUARD_AUTH_ID).Contains(p.EGUARD_AUTH_ID) && p.EGUARD_AUTH_PSTATE == "1" && p.EGUARD_AUTH_TYPE == "1").ToListAsync();
                foreach (var item in request.EguardSaveItem)
                {
                    if (item.EGUARD_AUTH_ID.IsNotNullOrEmpty())
                    {
                        var itemEguardAuthpost = oldEguardAuthPost.Where(p => p.EGUARD_AUTH_ID == item.EGUARD_AUTH_ID).ToList();
                        var eguardAuthDataId = request.RoleIds.Where(p => !itemEguardAuthpost.Select(i => i.EGUARD_DATA_ID).Contains(p)).ToList();
                        foreach (var itemAuthData in eguardAuthDataId)
                        {
                            eguardAuthPost_insert.Add(new PMS_EGUARD_AUTH_POST
                            {
                                EGUARD_AUTH_PID = IDGenHelper.CreateGuid(),
                                EGUARD_AUTH_ID = item.EGUARD_AUTH_ID,
                                EGUARD_AUTH_TYPE = "1",
                                EPLAN_APPLY_TYPE = request.EPLAN_APPLY_TYPE,
                                EGUARD_DATA_ID = itemAuthData,
                                EGUARD_AUTH_PSTATE = "1",
                                FIRST_RPERSON = operPerson,
                                FIRST_RTIME = sysDate,
                                LAST_MPERSON = operPerson,
                                LAST_MTIME = sysDate
                            });
                        }
                    }
                    else
                    {
                        foreach(var itemRole in request.RoleIds)
                        {
                            eguardAuthPost_insert.Add(new PMS_EGUARD_AUTH_POST
                            {
                                EGUARD_AUTH_PID = IDGenHelper.CreateGuid(),
                                EGUARD_AUTH_ID = item.ID,
                                EGUARD_AUTH_TYPE = "1",
                                EPLAN_APPLY_TYPE = request.EPLAN_APPLY_TYPE,
                                EGUARD_DATA_ID = itemRole,
                                EGUARD_AUTH_PSTATE = "1",
                                FIRST_RPERSON = operPerson,
                                FIRST_RTIME = sysDate,
                                LAST_MPERSON = operPerson,
                                LAST_MTIME = sysDate
                            });
                        }
                    }
                }
            }
            //门禁授权关联人员
            if (request.EGUARD_AUTH_TYPE == "2" && request.PersonIds != null && request.PersonIds.Count > 0)
            {
                var oldEguardAuthPost = await _uow.Db.Queryable<PMS_EGUARD_AUTH_POST>()
                    .Where(p => request.EguardSaveItem.Select(i => i.EGUARD_AUTH_ID).Contains(p.EGUARD_AUTH_ID) && p.EGUARD_AUTH_PSTATE == "1" && p.EGUARD_AUTH_TYPE == "1").ToListAsync();
                foreach (var item in request.EguardSaveItem)
                {
                    if (item.EGUARD_AUTH_ID.IsNotNullOrEmpty())
                    {
                        var itemEguardAuthpost = oldEguardAuthPost.Where(p => p.EGUARD_AUTH_ID == item.EGUARD_AUTH_ID).ToList();
                        var eguardAuthDataId = request.PersonIds.Where(p => !itemEguardAuthpost.Select(i => i.EGUARD_DATA_ID).Contains(p)).ToList();
                        foreach (var itemAuthData in eguardAuthDataId)
                        {
                            eguardAuthPost_insert.Add(new PMS_EGUARD_AUTH_POST
                            {
                                EGUARD_AUTH_PID = IDGenHelper.CreateGuid(),
                                EGUARD_AUTH_ID = item.EGUARD_AUTH_ID,
                                EGUARD_AUTH_TYPE = "2",
                                EPLAN_APPLY_TYPE = request.EPLAN_APPLY_TYPE,
                                EGUARD_DATA_ID = itemAuthData,
                                EGUARD_AUTH_PSTATE = "1",
                                FIRST_RPERSON = operPerson,
                                FIRST_RTIME = sysDate,
                                LAST_MPERSON = operPerson,
                                LAST_MTIME = sysDate
                            });
                        }
                    }
                    else
                    {
                        foreach(var itemPeron in request.PersonIds)
                        {
                            eguardAuthPost_insert.Add(new PMS_EGUARD_AUTH_POST
                            {
                                EGUARD_AUTH_PID = IDGenHelper.CreateGuid(),
                                EGUARD_AUTH_ID = item.ID,
                                EGUARD_AUTH_TYPE = "2",
                                EPLAN_APPLY_TYPE = request.EPLAN_APPLY_TYPE,
                                EGUARD_DATA_ID = itemPeron,
                                EGUARD_AUTH_PSTATE = "1",
                                FIRST_RPERSON = operPerson,
                                FIRST_RTIME = sysDate,
                                LAST_MPERSON = operPerson,
                                LAST_MTIME = sysDate
                            });
                        }
                    }
                }
            }
            try
            {
                using (_uow.Begin())
                {
                    if (eguardAuth_insert.Count > 0)
                    {
                        await _uow.Db.Insertable(eguardAuth_insert).UseParameter().ExecuteCommandAsync();
                    }
                    if (eguardAuth_update.Count > 0)
                    {
                        await _uow.Db.Updateable(eguardAuth_update).ExecuteCommandAsync();
                    }
                    if (eguardAuthCom_insert.Count > 0)
                    {
                        await _uow.Db.Insertable(eguardAuthCom_insert).UseParameter().ExecuteCommandAsync();
                    }
                    if(request.IF_UPDATE == "0")
                    {
                        if (eguardAuthCom_delete.Count > 0)
                        {
                            await _uow.Db.Updateable(eguardAuthCom_delete).ExecuteCommandAsync();
                        }
                    }
                    if (eguardAuthPost_insert.Count > 0)
                    {
                        await _uow.Db.Insertable(eguardAuthPost_insert).UseParameter().ExecuteCommandAsync();
                    }
                    await _uow.SaveChangesAsync();
                }
                await AddorUpdateAccessControl(publishAuthIds.ToArray(), operPerson);
                foreach(var item in request.EguardSaveItem)
                {
                    if (item.EGUARD_AUTH_ID.IsNullOrEmpty())
                    {
                        item.EGUARD_AUTH_ID = item.ID;
                    }
                }
                return new ResultDto { success = true, data = request };
            }
            catch (Exception ex)
            {
                return new ResultDto { success = false, msg = $"保存失败！{ex.Message}" };
            }
        }
        /// <summary>
        /// 复制岗位授权
        /// </summary>
        /// <param name="request"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        public async Task<ResultDto> CopyEguardAuth(CopyEguardAuthRequest request,string userName)
        {
            var eguardAuth_insert = new List<PMS_EGUARD_AUTH>();
            var eguardAuthCom_insert = new List<PMS_EGUARD_AUTH_COM>();
            var eguardAuthPost_insert = new List<PMS_EGUARD_AUTH_POST>();
            var copyEguardUnion = new List<CopyUnionData>();
            var operTime = _uow.Db.GetDate();
            var oldEguardAuth = await _uow.Db.Queryable<PMS_EGUARD_AUTH>()
                .Where(p => request.EGUARD_AUTH_ID.Contains(p.EGUARD_AUTH_ID) && p.EGUARD_AUTH_STATE != "2")
                .ToListAsync();
            foreach(var item in oldEguardAuth)
            {
                foreach(var itemData in request.EGUARD_DATA_ID)
                {
                    var oldAuthId = item.EGUARD_AUTH_ID;
                    var newEguardAuth = new PMS_EGUARD_AUTH
                    {
                        EGUARD_AUTH_ID = IDGenHelper.CreateGuid().Substring(0, 20),
                        HOSPITAL_ID = item.HOSPITAL_ID,
                        EGUARD_TYPE = item.EGUARD_TYPE,
                        AUTH_LIMIT_TYPE = item.AUTH_LIMIT_TYPE,
                        AUTH_START_DATE = item.AUTH_START_DATE,
                        AUTH_END_DATE = item.AUTH_END_DATE,
                        EGUARD_PASS_ID = item.EGUARD_PASS_ID,
                        EGUARD_PASS_JSON = item.EGUARD_PASS_JSON,
                        PUBLISH_STATE = item.PUBLISH_STATE,
                        EGUARD_AUTH_STATE = "1",
                        FIRST_RPERSON = userName,
                        FIRST_RTIME = operTime,
                        LAST_MPERSON = userName,
                        LAST_MTIME = operTime,
                        REMARK = item.REMARK
                    };
                    eguardAuth_insert.Add(newEguardAuth);
                    copyEguardUnion.Add(new CopyUnionData
                    {
                        NEW_EGUARD_AUTH_ID = newEguardAuth.EGUARD_AUTH_ID,
                        OLD_EGUARD_AUTH_ID = oldAuthId
                    });
                }
            }
            var oldEguardAuthCom = await _uow.Db.Queryable<PMS_EGUARD_AUTH_COM>()
                .Where(p => request.EGUARD_AUTH_ID.Contains(p.EGUARD_AUTH_ID) && p.EGUARD_AUTH_CSTATE != "2")
                .ToListAsync();
            foreach(var item in oldEguardAuthCom)
            {
                var eguardAuthUnion = copyEguardUnion.Where(p => p.OLD_EGUARD_AUTH_ID == item.EGUARD_AUTH_ID).ToList();
                foreach(var itemUnion in eguardAuthUnion)
                {
                    var newEguardAuthCom = new PMS_EGUARD_AUTH_COM
                    {
                        EGUARD_AUTH_CID = IDGenHelper.CreateGuid(),
                        EGUARD_AUTH_ID = itemUnion.NEW_EGUARD_AUTH_ID,
                        EGUARD_DATA_ID = item.EGUARD_DATA_ID,
                        EGUARD_DATA_TYPE = item.EGUARD_DATA_TYPE,
                        EGUARD_AUTH_CSTATE = "1",
                        FIRST_RPERSON = userName,
                        FIRST_RTIME = operTime,
                        LAST_MPERSON = userName,
                        LAST_MTIME = operTime,
                        REMARK = item.REMARK
                    };
                    eguardAuthCom_insert.Add(newEguardAuthCom);
                }
            }
            foreach(var item in copyEguardUnion.DistinctBy(p => p.OLD_EGUARD_AUTH_ID))
            {
                var i = 0;
                
                foreach(var itemData in request.EGUARD_DATA_ID)
                {
                    var itemEguard = copyEguardUnion.Where(p => p.OLD_EGUARD_AUTH_ID == item.OLD_EGUARD_AUTH_ID).ToList()[i];
                    eguardAuthPost_insert.Add(new PMS_EGUARD_AUTH_POST
                    {
                        EGUARD_AUTH_PID = IDGenHelper.CreateGuid(),
                        EGUARD_AUTH_ID = itemEguard.NEW_EGUARD_AUTH_ID,
                        EGUARD_AUTH_TYPE = request.EGUARD_AUTH_TYPE,
                        EPLAN_APPLY_TYPE = request.EPLAN_APPLY_TYPE,
                        EGUARD_DATA_ID = itemData,
                        EGUARD_AUTH_PSTATE = "1",
                        FIRST_RPERSON = userName,
                        FIRST_RTIME = operTime,
                        LAST_MPERSON = userName,
                        LAST_MTIME = operTime
                    });
                    i++;
                }
            }
            //foreach(var item in request.EGUARD_DATA_ID)
            //{
            //    foreach (var itemUnion in copyEguardUnion.DistinctBy(p => p.OLD_EGUARD_AUTH_ID))
            //    {
            //        eguardAuthPost_insert.Add(new PMS_EGUARD_AUTH_POST
            //        {
            //            EGUARD_AUTH_PID = IDGenHelper.CreateGuid(),
            //            EGUARD_AUTH_ID = itemUnion.NEW_EGUARD_AUTH_ID,
            //            EGUARD_AUTH_TYPE = request.EGUARD_AUTH_TYPE,
            //            EPLAN_APPLY_TYPE = request.EPLAN_APPLY_TYPE,
            //            EGUARD_DATA_ID = item,
            //            EGUARD_AUTH_PSTATE = "1",
            //            FIRST_RPERSON = userName,
            //            FIRST_RTIME = operTime,
            //            LAST_MPERSON = userName,
            //            LAST_MTIME = operTime
            //        });
            //    }
            //}
            try
            {
                using (_uow.Begin())
                {
                    if(eguardAuth_insert.Count > 0)
                    {
                        await _uow.Db.Insertable(eguardAuth_insert).UseParameter().ExecuteCommandAsync();
                    }
                    if (eguardAuthCom_insert.Count > 0)
                    {
                        await _uow.Db.Insertable(eguardAuthCom_insert).UseParameter().ExecuteCommandAsync();
                    }
                    if (eguardAuthPost_insert.Count > 0)
                    {
                        await _uow.Db.Insertable(eguardAuthPost_insert).UseParameter().ExecuteCommandAsync();
                    }
                    await _uow.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                return new ResultDto { success = false, msg = ex.Message };
            }
            return new ResultDto { success = true, data = "复制成功" };
        }

        public  async Task<List<(PMS_PERSON_INFO person, DateTime? startTime, DateTime? endTime, List<string> roomSns, PassTimeJson plan)>> GetUserAuthRecords(PMS_PERSON_INFO person)
        {
            var result = new List<(PMS_PERSON_INFO person, DateTime? startTime, DateTime? endTime, List<string> roomSns, PassTimeJson plan)>();
            var input = new EguardInput()
            {
                PersonIds = new List<string>() { person.PERSON_ID }
            };
            var list =  await GetEguardListByCondtion(input, _httpContext.HttpContext.User.ToClaimsDto());

            var authRecords = list.GroupBy(x =>  new { x.EGUARD_AUTH_ID ,x.EGUARD_PASS_JSON,x.AUTH_START_DATE,x.AUTH_END_DATE }).ToList();

            foreach (var authRecord in authRecords)
            {
                var key = authRecord.Key;
                var rooms = authRecord.Select(x=>x.POSITION_ID);
                var roomSns = _uow.Db.Queryable<SYS6_HGROUP_HISID>()
                    .Where(x => rooms.Contains(x.DATA_ID))
                    .Select(x => x.HIS_ID).ToList();
                result .Add((person, key.AUTH_START_DATE, key.AUTH_END_DATE, roomSns, JsonConvert.DeserializeObject<PassTimeJson>(key.EGUARD_PASS_JSON)));
            }
            return result;
        }



        /// <summary>
        /// 判断人员或者岗位是否可以选择门禁或门禁组合
        /// </summary>
        /// <param name="input"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        public async Task<ResultDto> JudgEguardCom(JudgEguardCom input, ClaimsDto user)
        {
            var result = new ResultDto();
            var sysDate = _uow.Db.GetDate();
            try
            {
                if (input.EGUARD_DATA_TYPE == "1")
                {
                    //按照门禁组合查询
                    var query1 = _uow.Db.Queryable<PMS_EGUARD_AUTH>()
                        .InnerJoin<PMS_EGUARD_AUTH_COM>((a, b) => a.EGUARD_AUTH_ID == b.EGUARD_AUTH_ID && b.EGUARD_DATA_TYPE == "1")
                        .InnerJoin<PMS_EGUARD_AUTH_POST>((a, b, c) => a.EGUARD_AUTH_ID == c.EGUARD_AUTH_ID)
                        .InnerJoin<PMS_EGUARD_COM_DICT>((a, b, c, d) => d.EGUARD_COM_ID == b.EGUARD_DATA_ID)
                        .InnerJoin<PMS_EGUARD_COM_LIST>((a, b, c, d, e) => e.EGUARD_COM_ID == d.EGUARD_COM_ID)
                        .InnerJoin<EMS_EQUIPMENT_INFO>((a, b, c, d, e, f) => f.EQUIPMENT_ID == e.EQUIPMENT_ID)
                        .InnerJoin<SYS6_POSITION_DICT>((a, b, c, d, e, f, g) => g.POSITION_ID == f.POSITION_ID)
                        .WhereIF(input.RoleIds != null && input.RoleIds.Count > 0, (a, b, c, d, e, f, g) => input.RoleIds.Contains(c.EGUARD_DATA_ID))
                        .WhereIF(input.PersonIds != null && input.PersonIds.Count > 0, (a, b, c, d, e, f, g) => input.PersonIds.Contains(c.EGUARD_DATA_ID))
                        .Where((a, b, c, d, e, f, g) => a.EGUARD_AUTH_ID == input.EGUARD_AUTH_ID)
                        .Select((a, b, c, d, e, f, g) => new EguardOutput()
                        {
                            EGUARD_COM_ID = d.EGUARD_COM_ID, //门禁组合ID
                            EGUARD_COM_NAME = d.EGUARD_COM_NAME,//门禁组合名称
                            POSITION_ID = g.POSITION_ID, //门禁地点ID
                            POSITION_NAME = g.POSITION_NAME,//--门禁地点名称
                            EQUIPMENT_ID = f.EQUIPMENT_ID,//门禁设备ID
                            EQUIPMENT_NAME = f.EQUIPMENT_NAME,//门禁设备名称
                            EPLAN_APPLY_TYPE = c.EPLAN_APPLY_TYPE,//适用范围
                            AUTH_LIMIT_TYPE = a.AUTH_LIMIT_TYPE,//-授权期限类型 1.长期  2.固定时间 （）
                            EGUARD_PASS_ID = a.EGUARD_PASS_ID,//授权类型是长期的取值  时间字典表
                            EGUARD_PASS_JSON = a.EGUARD_PASS_JSON,//授权时间是固定时间的 解析这个json串
                            EGUARD_COM_DESC = d.EGUARD_COM_DESC,//门禁组合描述
                            EGUARD_DATA_TYPE = b.EGUARD_DATA_TYPE,//门禁类型
                            EGUARD_DATA_ID = b.EGUARD_DATA_ID,//门禁ID
                            EGUARD_AUTH_TYPE = c.EGUARD_AUTH_TYPE,//岗位人员类型
                            RolePersonId = c.EGUARD_DATA_ID, //--岗位 / 人员ID
                            EGUARD_AUTH_ID = a.EGUARD_AUTH_ID,
                            AUTH_START_DATE = a.AUTH_START_DATE,
                            AUTH_END_DATE = a.AUTH_END_DATE
                        }).MergeTable();

                    var list1 = await query1.Select(a => a.EGUARD_COM_ID).ToListAsync();
                    result.data = list1;
                    result.success = true;
                }
                else if (input.EGUARD_DATA_TYPE == "2")
                {
                    //按照门禁ID查询
                    var query2 = _uow.Db.Queryable<PMS_EGUARD_AUTH>()
                        .InnerJoin<PMS_EGUARD_AUTH_COM>((a, b) => a.EGUARD_AUTH_ID == b.EGUARD_AUTH_ID && b.EGUARD_DATA_TYPE == "2")
                        .InnerJoin<PMS_EGUARD_AUTH_POST>((a, b, c) => a.EGUARD_AUTH_ID == c.EGUARD_AUTH_ID)
                        .InnerJoin<PMS_EGUARD_COM_LIST>((a, b, c, d) => d.EQUIPMENT_ID == b.EGUARD_DATA_ID)
                        .InnerJoin<PMS_EGUARD_COM_DICT>((a, b, c, d, e) => e.EGUARD_COM_ID == d.EGUARD_COM_ID)
                        .InnerJoin<EMS_EQUIPMENT_INFO>((a, b, c, d, e, f) => f.EQUIPMENT_ID == d.EQUIPMENT_ID)
                        .InnerJoin<SYS6_POSITION_DICT>((a, b, c, d, e, f, g) => g.POSITION_ID == f.POSITION_ID)
                        .WhereIF(input.RoleIds != null && input.RoleIds.Count > 0, (a, b, c, d, e, f, g) => input.RoleIds.Contains(c.EGUARD_DATA_ID))
                        .WhereIF(input.PersonIds != null && input.PersonIds.Count > 0, (a, b, c, d, e, f, g) => input.PersonIds.Contains(c.EGUARD_DATA_ID))
                        .Where((a, b, c, d, e, f, g) => a.EGUARD_AUTH_ID == input.EGUARD_AUTH_ID)
                        .Select((a, b, c, d, e, f, g) => new EguardOutput()
                        {
                            EGUARD_COM_ID = e.EGUARD_COM_ID, //门禁组合ID
                            EGUARD_COM_NAME = e.EGUARD_COM_NAME,//门禁组合名称
                            POSITION_ID = g.POSITION_ID, //门禁地点ID
                            POSITION_NAME = g.POSITION_NAME,//--门禁地点名称
                            EQUIPMENT_ID = f.EQUIPMENT_ID,//门禁设备ID
                            EQUIPMENT_NAME = f.EQUIPMENT_NAME,//门禁设备名称
                            EPLAN_APPLY_TYPE = c.EPLAN_APPLY_TYPE,//适用范围
                            AUTH_LIMIT_TYPE = a.AUTH_LIMIT_TYPE,//-授权期限类型 1.长期  2.固定时间 （）
                            EGUARD_PASS_ID = a.EGUARD_PASS_ID,//授权类型是长期的取值  时间字典表
                            EGUARD_PASS_JSON = a.EGUARD_PASS_JSON,//授权时间是固定时间的 解析这个json串
                            EGUARD_COM_DESC = e.EGUARD_COM_DESC,//门禁组合描述
                            EGUARD_DATA_TYPE = b.EGUARD_DATA_TYPE,//门禁类型
                            EGUARD_DATA_ID = b.EGUARD_DATA_ID,//门禁ID
                            EGUARD_AUTH_TYPE = c.EGUARD_AUTH_TYPE,//岗位人员类型
                            RolePersonId = c.EGUARD_DATA_ID, //--岗位 / 人员ID
                            EGUARD_AUTH_ID = a.EGUARD_AUTH_ID,
                            AUTH_START_DATE = a.AUTH_START_DATE,
                            AUTH_END_DATE = a.AUTH_END_DATE
                        }).MergeTable();
                    var list2 = await query2.Select(a => a.EQUIPMENT_ID).ToListAsync();
                    result.data = list2;
                    result.success = true;
                }
            }
            catch (Exception ex)
            {
                result.success = false;
                result.msg = $"操作失败！{ex.Message}";
            }
            return result;

        }



        /// <summary>
        /// 获取是用范围名称   
        /// </summary>
        /// <returns></returns>
        private string GetApplyTypeName(string? applyType)
        {
            var strName = string.Empty;
            if (applyType.IsNullOrEmpty())
            {
                strName = "";
            }
            else
            {
                switch (applyType)
                {
                    case "LAB": strName = "检验科室"; break;
                    case "PGROUP": strName = "检验专业组"; break;
                    case "MGROUP": strName = "管理专业组"; break;
                    case "POST": strName = "岗位"; break;
                    case "PROLE": strName = "岗位角色"; break;
                    default: strName = ""; break;
                }
            }
            return strName;
        }

        /// <summary>
        /// 删除授权设置
        /// </summary>
        /// <param name="input"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        public async Task<ResultDto> DeleteEguard(EguardSaveDto input, ClaimsDto user)
        {
            var result = new ResultDto();
            var sysDate = _uow.Db.GetDate();
            if (input.EGUARD_AUTH_ID.IsNullOrEmpty())
            {
                result.success = false;
                result.msg = "删除失败！没有查询到门禁授权数据";
            }
            try
            {
                using (_uow.Begin())
                {
                    //如果只传了授权id，则删除全部授权信息
                    if(input.EGUARD_AUTH_TYPE.IsNullOrEmpty())
                    {
                        await _uow.GetRepository<PMS_EGUARD_AUTH>().DeleteAsync(p => p.EGUARD_AUTH_ID == input.EGUARD_AUTH_ID);
                        await _uow.GetRepository<PMS_EGUARD_AUTH_COM>().DeleteAsync(p => p.EGUARD_AUTH_ID == input.EGUARD_AUTH_ID);
                        await _uow.GetRepository<PMS_EGUARD_AUTH_POST>().DeleteAsync(p => p.EGUARD_AUTH_ID == input.EGUARD_AUTH_ID);
                    }
                    //如果传了授权类型，则删除对应关联关系
                    if (input.EGUARD_AUTH_TYPE.IsNotNullOrEmpty())
                    {
                        var eguardAuthPostList = await _uow.Db.Queryable<PMS_EGUARD_AUTH_POST>().Where(p => p.EGUARD_AUTH_ID == input.EGUARD_AUTH_ID && p.EGUARD_AUTH_PSTATE != "2").ToListAsync();
                        var inputDeleteList = input.EGUARD_AUTH_TYPE == "1" ? 
                        input.RoleIds.Select(i => new PMS_EGUARD_AUTH_POST
                        {
                            EGUARD_DATA_ID = i,EGUARD_AUTH_TYPE = input.EGUARD_AUTH_TYPE,EGUARD_AUTH_ID = input.EGUARD_AUTH_ID,EPLAN_APPLY_TYPE = input.EPLAN_APPLY_TYPE
                        }).ToList() : 
                        input.PersonIds.Select(i => new PMS_EGUARD_AUTH_POST
                        {
                            EGUARD_DATA_ID = i,EGUARD_AUTH_TYPE = input.EGUARD_AUTH_TYPE,EGUARD_AUTH_ID = input.EGUARD_AUTH_ID,EPLAN_APPLY_TYPE = input.EPLAN_APPLY_TYPE
                        });
                        var deleteList = new List<PMS_EGUARD_AUTH_POST>();
                        foreach(var item in inputDeleteList)
                        {
                            var deleteInfo = eguardAuthPostList.Where(p => p.EGUARD_DATA_ID == item.EGUARD_DATA_ID && p.EGUARD_AUTH_ID == item.EGUARD_AUTH_ID && p.EGUARD_AUTH_TYPE == item.EGUARD_AUTH_TYPE).ToList();
                            deleteList.AddRange(deleteInfo);
                        }
                        await _uow.GetRepository<PMS_EGUARD_AUTH_POST>().DeleteAsync(deleteList);
                        //如果删除关联关系后，没有其他关联关系，则删除全部授权信息
                        if(eguardAuthPostList.Except(deleteList).Count() == 0)
                        {
                            await _uow.GetRepository<PMS_EGUARD_AUTH>().DeleteAsync(p => p.EGUARD_AUTH_ID == input.EGUARD_AUTH_ID);
                            await _uow.GetRepository<PMS_EGUARD_AUTH_COM>().DeleteAsync(p => p.EGUARD_AUTH_ID == input.EGUARD_AUTH_ID);
                        }
                    }
                    

                    await _uow.SaveChangesAsync();
                    result.success = true;
                    result.msg = "删除成功";
                }
            }
            catch (Exception ex)
            {
                result.success = false;
                result.msg = $"删除失败！{ex.Message}";
            }
            return result;
        }

        #endregion

        #region 访客核准
        /// <summary>
        /// 访客申请列表
        /// </summary>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        public async Task<ResultDto> GetVistorRequest(DateTime startTime, DateTime endTime, string hospitalId)
        {
            var res = await _uow.Db.Queryable<PMS_VISIT_REQUEST_LIST>()
                .LeftJoin<PMS_VISITOR_INFO>((a, b) => a.PERSON_ID == b.VISITOR_ID)
                .LeftJoin<PMS_PERSON_INFO>((a, b, c) => a.VISIT_PERSON_ID == c.PERSON_ID)
                .LeftJoin<SYS6_POSITION_DICT>((a, b, c, d) => a.VISIT_ROOM_ID == d.POSITION_ID)
                .LeftJoin<OA_BASE_DATA>((a, b, c, d, e) => a.VISIT_REQ_TYPE == e.DATA_ID && e.CLASS_ID == "申请类型" && e.STATE_FLAG == "1")
                .Where((a, b, c, d) => a.FIRST_RTIME >= startTime && a.FIRST_RTIME <= endTime && a.HOSPITAL_ID == hospitalId && PMS_VISIT_REQUEST_LIST.EguardState_PC.Contains(a.VISIT_REQ_STATE))
                .Select((a, b, c, d) => new VistorRequestDto
                {
                    VISIT_REQ_ID = a.VISIT_REQ_ID,
                    VISIT_REQ_STATE = a.VISIT_REQ_STATE == "12" ? "11" : a.VISIT_REQ_STATE,
                    PHONE_NO = b.PHONE,
                    PERSON_NAME = b.VISITOR_NAME,
                    PHOTO = file_preview + b.PHOTO_PATH,//todu
                    WORK_UNIT = b.WORK_UNIT,
                    VISIT_REQ_TYPE = a.VISIT_REQ_TYPE,
                    VISIT_PERSON_NAME = c.USER_NAME,
                    VISIT_ROOM_ADDR = d.POSITION_NAME,
                    FELLOW_PERSON = a.FELLOW_PERSON,
                    FELLOW_PERSON_NUM = a.FELLOW_PERSON_NUM,
                    VISIT_REQ_REASON = a.VISIT_REQ_REASON,
                    VISIT_TIME_FRAME = SqlFunc.HasValue(a.VISIT_START_TIME) && SqlFunc.HasValue(a.VISIT_END_TIME)
                    ? $"{((DateTime)a.VISIT_START_TIME).ToString("yyyy-MM-dd HH:mm:ss")} ~ {((DateTime)a.VISIT_END_TIME).ToString("yyyy-MM-dd HH:mm:ss")}" : "",
                    REQ_TIME = a.FIRST_RTIME,
                    VISIT_PERSON_ID = a.VISIT_PERSON_ID,
                    VISIT_ROOM_ID = a.VISIT_ROOM_ID,
                    PERSON_ID = a.PERSON_ID,
                    VISIT_ORIGIN = a.VISIT_ORIGIN,
                }).ToListAsync();
            var res_operLog1 = await _uow.Db.Queryable<PMS_VISIT_OPER_LOG>()
                .InnerJoin<PMS_VISITOR_INFO>((a, b) => a.OPER_PERSON == b.OPEN_ID)
                .Where((a, b) => res.Select(i => i.VISIT_REQ_ID).Contains(a.VISIT_REQ_ID) && SqlFunc.HasValue(a.CURRENT_STATE) && PMS_VISIT_REQUEST_LIST.EguardApproval_PC.Contains(a.CURRENT_STATE))
                .Select((a, b) => new PMS_VISIT_OPER_LOG
                {
                    LOG_ID = a.LOG_ID,
                    OPER_CAUSE = a.OPER_CAUSE,
                    OPER_PERSON = b.VISITOR_NAME ?? a.OPER_PERSON,
                    OPER_TIME = a.OPER_TIME,
                    CURRENT_STATE = a.CURRENT_STATE,
                    VISIT_REQ_ID = a.VISIT_REQ_ID
                }).ToListAsync();

            var res_operLog2 = await _uow.Db.Queryable<PMS_VISIT_OPER_LOG>()
                .InnerJoin<SYS6_USER>((a, b) => a.OPER_PERSON == b.USER_NO)
                .Where((a, b) => res.Select(i => i.VISIT_REQ_ID).Contains(a.VISIT_REQ_ID) && SqlFunc.HasValue(a.CURRENT_STATE) && PMS_VISIT_REQUEST_LIST.EguardApproval_PC.Contains(a.CURRENT_STATE))
                .Select((a, b) => new PMS_VISIT_OPER_LOG
                {
                    LOG_ID = a.LOG_ID,
                    OPER_CAUSE = a.OPER_CAUSE,
                    OPER_PERSON = b.USERNAME ?? a.OPER_PERSON,
                    OPER_TIME = a.OPER_TIME,
                    CURRENT_STATE = a.CURRENT_STATE,
                    VISIT_REQ_ID = a.VISIT_REQ_ID
                }).ToListAsync();
            res_operLog1.AddRange(res_operLog2);
            var res_operLog = res_operLog1.OrderByDescending(p => p.OPER_TIME).ToList();
            foreach (var item in res)
            {
                var itemLog = res_operLog.Where(p => p.VISIT_REQ_ID == item.VISIT_REQ_ID).ToList();
                //待审核
                if (item.VISIT_REQ_STATE == "1")
                {
                    continue;
                }
                var checkLog = itemLog.Where(p => p.CURRENT_STATE == "2").FirstOrDefault();
                //待审批
                if (item.VISIT_REQ_STATE == "2")
                {
                    item.CHECK_PERSON = checkLog?.OPER_PERSON;
                    item.CHECK_TIME = checkLog?.OPER_TIME;
                    continue;
                }
                var approveLog = itemLog.Where(p => p.CURRENT_STATE == "3").FirstOrDefault();
                //通过
                if (item.VISIT_REQ_STATE == "3")
                {
                    item.CHECK_PERSON = checkLog?.OPER_PERSON;
                    item.CHECK_TIME = checkLog?.OPER_TIME;
                    item.APPROVE_PERSON = approveLog?.OPER_PERSON;
                    item.APPROVE_TIME = approveLog?.OPER_TIME;
                    continue;
                }
                //驳回
                if (item.VISIT_REQ_STATE == "5")
                {
                    var rejectLog = itemLog.Where(p => p.CURRENT_STATE == "5").FirstOrDefault();
                    if (rejectLog != null)
                    {
                        int index = itemLog.FindIndex(p => p.LOG_ID == rejectLog.LOG_ID);
                        if (index == 0)
                        {
                            item.CHECK_PERSON = rejectLog.OPER_PERSON;
                            item.CHECK_TIME = rejectLog.OPER_TIME;
                            item.CHECK_REJECT_CAUSE = rejectLog.OPER_CAUSE;
                        }
                        else if (index > 0)
                        {
                            //上一个条操作数据
                            var lastOperLog = itemLog[index - 1];
                            //上一条也是驳回
                            if (lastOperLog.CURRENT_STATE == "5")
                            {
                                item.CHECK_PERSON = rejectLog.OPER_PERSON;
                                item.CHECK_TIME = rejectLog.OPER_TIME;
                                item.CHECK_REJECT_CAUSE = rejectLog.OPER_CAUSE;
                            }
                            //上一条是待审批
                            if (lastOperLog.CURRENT_STATE == "2")
                            {
                                item.CHECK_PERSON = lastOperLog.OPER_PERSON;
                                item.CHECK_TIME = lastOperLog.OPER_TIME;
                                item.APPROVE_PERSON = rejectLog.OPER_PERSON;
                                item.APPROVE_TIME = rejectLog.OPER_TIME;
                                item.APPROVE_REJECT_CAUSE = rejectLog.OPER_CAUSE;
                            }
                        }
                    }

                }
            }
            return new ResultDto { success = true, data = res.OrderBy(p => Convert.ToInt16(p.VISIT_REQ_STATE)).ThenBy(p => p.FIRST_RTIME).ToList()};
        }

        /// <summary>
        /// 保存申请记录
        /// </summary>
        /// <param name="request"></param>
        /// <param name="hospitalId"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        public async Task<ResultDto> SaveVistorRequest(SaveVistorRequestRequest request, string hospitalId, string userName)
        {
            var operTime = _uow.Db.GetDate();
            var old = await _uow.Db.Queryable<PMS_VISIT_REQUEST_LIST>().Where(p => p.VISIT_REQ_ID == request.VISIT_REQ_ID).FirstAsync();
            var uploadFileName = $"PMS_VISITOR_REQUEST";
            var visitStartTime = Convert.ToDateTime(request.VISIT_TIME_FRAME.Split("~")[0]);
            var visitEndTime = Convert.ToDateTime(request.VISIT_TIME_FRAME.Split("~")[1]);
            var vistorInfo = new PMS_VISITOR_INFO();
            var ifVistorInsert = false;
            try
            {
                using (_uow.Begin())
                {
                    //判断人员 是否需要新增
                    if(request.PERSON_ID.IsNullOrEmpty() && request.PERSON_NAME.IsNotNullOrEmpty())
                    {
                        var ifExisit = await _uow.Db.Queryable<PMS_VISITOR_INFO>().AnyAsync(p => p.PHONE == request.PHONE_NO && p.VISITOR_STATE != "2");
                        if (ifExisit)
                        {
                            return new ResultDto { success = false, msg = "手机号已被使用，请检查后重试" };
                        }
                        vistorInfo.VISITOR_ID = IDGenHelper.CreateGuid();
                        vistorInfo.HOSPITAL_ID = hospitalId;
                        vistorInfo.VISITOR_NAME = request.PERSON_NAME;
                        vistorInfo.PHONE = request.PHONE_NO;
                        vistorInfo.WORK_UNIT = request.WORK_UNIT;
                        vistorInfo.VISITOR_STATE = "1";
                        vistorInfo.FIRST_RPERSON = userName;
                        vistorInfo.FIRST_RTIME = operTime;
                        vistorInfo.LAST_MPERSON = userName;
                        vistorInfo.LAST_MTIME = operTime;
                        ifVistorInsert = true;
                        request.PERSON_ID = vistorInfo.VISITOR_ID;
                    }
                    //新增
                    if (old == null)
                    {
                        var visitRequest_insert = new PMS_VISIT_REQUEST_LIST
                        {
                            VISIT_REQ_ID = IDGenHelper.CreateGuid(),
                            PERSON_ID = request.PERSON_ID,
                            HOSPITAL_ID = hospitalId,
                            VISIT_REQ_TYPE = request.VISIT_REQ_TYPE,
                            BATCH_ID = "0",//tudo
                            VISIT_PERSON_ID = request.VISIT_PERSON_ID,
                            VISIT_ROOM_ID = request.VISIT_ROOM_ID,
                            FELLOW_PERSON = request.FELLOW_PERSON,
                            FELLOW_PERSON_NUM = request.FELLOW_PERSON_NUM,
                            VISIT_REQ_REASON = request.VISIT_REQ_REASON,
                            VISIT_START_TIME = visitStartTime,
                            VISIT_END_TIME = visitEndTime,
                            VISIT_ORIGIN = "2",
                            VISIT_REQ_STATE = "1",
                            FIRST_RPERSON = userName,
                            FIRST_RTIME = operTime,
                            LAST_MPERSON = userName,
                            LAST_MTIME = operTime,
                        };
                        await _uow.Db.Insertable(visitRequest_insert).ExecuteCommandAsync();
                    }
                    else
                    {
                        old.PERSON_ID = request.PERSON_ID;
                        old.VISIT_REQ_TYPE = request.VISIT_REQ_TYPE;
                        old.VISIT_PERSON_ID = request.VISIT_PERSON_ID;
                        old.VISIT_ROOM_ID = request.VISIT_ROOM_ID;
                        old.FELLOW_PERSON = request.FELLOW_PERSON;
                        old.FELLOW_PERSON_NUM = request.FELLOW_PERSON_NUM;
                        old.VISIT_REQ_REASON = request.VISIT_REQ_REASON;
                        old.VISIT_START_TIME = visitStartTime;
                        old.VISIT_END_TIME = visitEndTime;
                        old.LAST_MPERSON = userName;
                        old.LAST_MTIME = operTime;
                        old.VISIT_REQ_STATE = "1";
                        await _uow.Db.Updateable(old).ExecuteCommandAsync();
                    }
                    if (request.PERSON_ID.IsNotNullOrEmpty() && !ifVistorInsert)
                    {
                        //修改人员信息
                        vistorInfo = await _uow.Db.Queryable<PMS_VISITOR_INFO>().Where(p => p.VISITOR_ID == request.PERSON_ID).FirstAsync();
                        if (vistorInfo == null)
                        {
                            throw new Exception("访问人员信息不存在，请检查后重试");
                        }
                        var ifPhoneExist = await _uow.Db.Queryable<PMS_VISITOR_INFO>().Where(p => p.VISITOR_ID != request.PERSON_ID && p.PHONE == request.PHONE_NO && p.VISITOR_STATE != "2").AnyAsync();
                        if (ifPhoneExist)
                        {
                            throw new Exception("输入的电话重复，请检查后重试");
                        }
                    }

                    if (request.FILE != null && request.FILE_SUFFIX.IsNotNullOrEmpty())
                    {
                        var fileSuffix = request.FILE_SUFFIX.ToLower();
                        uploadFileName = uploadFileName + fileSuffix;
                        var uploadRequest = new UploadFileDto
                        {
                            FILE = request.FILE,
                            UPLOAD_FILE_NAME = uploadFileName,
                            FILE_SUFFIX = fileSuffix,
                            UPLOAD_FOLDER_NAME = OfficeHelper.PathCombine("PMS", request.PERSON_ID)

                        };
                        var uploadResDto = _IUploadFileService.UploadFileOperate(uploadRequest);
                        if (uploadResDto.success == true)
                        {
                            vistorInfo.PHOTO_PATH = uploadResDto.data.ToString();
                        }
                        else
                        {
                            throw new Exception(uploadResDto.msg);
                        }
                    }
                    if (ifVistorInsert)
                    {
                        await _uow.Db.Insertable(vistorInfo).ExecuteCommandAsync();
                    }
                    else
                    {
                        vistorInfo.PHONE = request.PHONE_NO;
                        vistorInfo.WORK_UNIT = request.WORK_UNIT;
                        vistorInfo.LAST_MPERSON = userName;
                        vistorInfo.LAST_MTIME = operTime;
                        await _uow.Db.Updateable(vistorInfo).ExecuteCommandAsync();
                    }
                    await _uow.SaveChangesAsync();
                    return new ResultDto { success = true, data = "操作成功" };
                }
            }
            catch (Exception ex)
            {
                return new ResultDto { success = false, msg = ex.Message };
            }
        }

        /// <summary>
        /// 操作申请记录
        /// </summary>
        /// <param name="request"></param>
        /// <param name="userName"></param>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        public async Task<ResultDto> VisitRequestApproval(VisitApprovalRequest request, string userName, string hospitalId)
        {
            var operTime = _uow.Db.GetDate();
            var visitRequestList = await _uow.Db.Queryable<PMS_VISIT_REQUEST_LIST>()
                .Where(p => request.VISIT_REQ_IDS.Contains(p.VISIT_REQ_ID))
                .ToListAsync();
            var oldState = visitRequestList.Select(p => p.VISIT_REQ_STATE).Distinct().ToList();
            if (oldState.Count > 1)
            {
                return new ResultDto { success = false, msg = "勾选的数据存在不同的状态，请检查后重试" };
            }
            var newState = "";
            var operContent = "";
            //通过
            if (request.OPER_TYPE == "1")
            {
                newState = oldState.First() == "0" ? "1" : oldState.First() == "1" ? "2" : "";
                operContent = newState == "1" ? "审核通过" : newState == "2" ? "审批通过" : "";
            }
            if (request.OPER_TYPE == "2")
            {
                newState = "3";
                operContent = oldState.First() == "1" ? "审核驳回" : oldState.First() == "2" ? "审批驳回" : "";
            }
            if (newState == "")
            {
                return new ResultDto { success = false, msg = "勾选的数据状态有误，请检查后重试" };
            }
            var operLog = new List<PMS_VISIT_OPER_LOG>();
            foreach (var item in visitRequestList)
            {
                item.VISIT_REQ_STATE = newState;
                item.LAST_MPERSON = userName;
                item.LAST_MTIME = operTime;
                operLog.Add(new PMS_VISIT_OPER_LOG
                {
                    LOG_ID = IDGenHelper.CreateGuid(),
                    VISIT_REQ_ID = item.VISIT_REQ_ID,
                    HOSPITAL_ID = hospitalId,
                    OPER_ID = "0",//todu
                    OPER_CONTENT = operContent,
                    OPER_TIME = operTime,
                    OPER_PERSON = userName,
                    OPER_COMPUTER = request.OPER_COMPUTER,
                    OPER_CAUSE = request.OPER_CAUSE,
                    CURRENT_STATE = newState,
                    OPER_STATE = "1"
                });
            }
            await _uow.Db.Updateable(visitRequestList).ExecuteCommandAsync();
            if (operLog.Count > 0)
            {
                await _uow.Db.Insertable(operLog).UseParameter().ExecuteCommandAsync();
            }
            return new ResultDto { success = true, data = "操作成功" };
        }


        /// <summary>
        /// 删除申请记录
        /// </summary>
        /// <param name="visitReqId"></param>
        /// <param name="userName"></param>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        public async Task<ResultDto> DeleteVisitRequest(string visitReqId, string userName, string hospitalId)
        {
            var visitRequestInfo = await _uow.Db.Queryable<PMS_VISIT_REQUEST_LIST>().Where(p => p.VISIT_REQ_ID == visitReqId).FirstAsync();
            if (visitRequestInfo == null)
            {
                return new ResultDto { success = false, msg = "未找到当前申请记录数据，请检查后重试" };
            }
            visitRequestInfo.VISIT_REQ_STATE = "2";
            visitRequestInfo.LAST_MPERSON = userName;
            visitRequestInfo.LAST_MTIME = _uow.Db.GetDate();
            await _uow.Db.Updateable<PMS_VISIT_REQUEST_LIST>(visitRequestInfo).UpdateColumns(p => new PMS_VISIT_REQUEST_LIST
            {
                VISIT_REQ_STATE = p.VISIT_REQ_STATE,
                LAST_MPERSON = p.LAST_MPERSON,
                LAST_MTIME = p.LAST_MTIME,
            }).ExecuteCommandAsync();
            return new ResultDto { success = true, data = "删除成功" };
        }

        /// <summary>
        /// 获取科外人员列表
        /// </summary>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        public async Task<ResultDto> GetVisitorList(string hospitalId)
        {
            var res = await _uow.Db.Queryable<PMS_VISITOR_INFO>().Where(p => p.HOSPITAL_ID == hospitalId && p.VISITOR_STATE == "1").Select(i => new visitordto
            {
                VISITOR_ID = i.VISITOR_ID,
                VISITOR_NAME = i.VISITOR_NAME,
                PHONE = i.PHONE,
                PHOTO_PATH = i.PHOTO_PATH,
                WORK_UNIT = i.WORK_UNIT,
                OPEN_ID = i.OPEN_ID
            }).ToListAsync();
            return new ResultDto { success = true, data = res };
        }

        /// <summary>
        /// 上传科外人员图片
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<ResultDto> UploadVisitorImg(UploadVisitorImg request, string userName)
        {
            var operTime = _uow.Db.GetDate();
            var personInfo = await _uow.Db.Queryable<PMS_VISITOR_INFO>().Where(p => p.VISITOR_ID == request.PERSON_ID).FirstAsync();
            if (personInfo == null)
            {
                return new ResultDto { success = false, msg = "未找到科外人员信息，请检查后重试" };
            }
            var uploadFileName = $"PMS_VISITOR_REQUEST";
            var fileSuffix = request.FILE_SUFFIX.ToLower();
            uploadFileName = uploadFileName + fileSuffix;
            var uploadRequest = new UploadFileDto
            {
                FILE = request.FILE,
                UPLOAD_FILE_NAME = uploadFileName,
                FILE_SUFFIX = fileSuffix,
                UPLOAD_FOLDER_NAME = OfficeHelper.PathCombine("PMS", request.PERSON_ID)
            };
            var uploadResDto = _IUploadFileService.UploadFileOperate(uploadRequest);
            if (uploadResDto.success == true)
            {
                personInfo.PHOTO_PATH = uploadResDto.data.ToString();
                personInfo.LAST_MPERSON = userName;
                personInfo.LAST_MTIME = operTime;
                await _uow.Db.Updateable(personInfo).ExecuteCommandAsync();
                return new ResultDto { success = true, data = personInfo.PHOTO_PATH };
            }
            else
            {
                return new ResultDto { success = false, msg = $"文件上传失败，失败原因：“{uploadResDto.msg}”" };
            }
        }

        #endregion
        #region 访客日志

        /// <summary>
        /// 获取全部访问日志
        /// </summary>
        /// <param name="startTime">检索开始时间</param>
        /// <param name="endTime">检索结束时间</param>
        /// <returns></returns>
        public async Task<ResultDto> GetAllVisitorLog(DateTime startTime, DateTime endTime, string hospitalId)
        {
            var res = await _uow.Db.Queryable<PMS_VISIT_REQUEST_LIST>()
                .LeftJoin<PMS_VISITOR_INFO>((a, b) => a.PERSON_ID == b.VISITOR_ID)
                .LeftJoin<PMS_PERSON_INFO>((a, b, c) => a.VISIT_PERSON_ID == c.USER_ID)
                .LeftJoin<SYS6_POSITION_DICT>((a, b, c, d) => a.VISIT_ROOM_ID == d.POSITION_ID)
                .LeftJoin<OA_BASE_DATA>((a, b, c, d, e) => a.VISIT_REQ_TYPE == e.DATA_ID && e.CLASS_ID == "申请类型" && e.STATE_FLAG == "1")
                .LeftJoin<SYS6_BASE_DATA>((a, b, c, d, e, f) => c.DUTIES == f.DATA_ID && f.CLASS_ID == "职务" && f.DATA_STATE == "1")
                .LeftJoin<SYS6_INSPECTION_PGROUP>((a, b, c, d, e, f, g) => c.PGROUP_ID == g.PGROUP_ID && g.PGROUP_STATE == "1")
                .LeftJoin<SYS6_BASE_DATA>((a, b, c, d, e, f, g, h) => c.PERSON_TYPE == f.DATA_ID && f.CLASS_ID == "人员类型" && f.DATA_STATE == "1")
                .Where((a, b, c, d, e, f, g, h) => a.FIRST_RTIME >= startTime && a.FIRST_RTIME <= endTime && a.VISIT_REQ_STATE == "3" && a.HOSPITAL_ID == hospitalId)
                .Select((a, b, c, d, e, f, g, h) => new VisitorLogDto
                {
                    VISIT_REQ_ID = a.VISIT_REQ_ID,
                    VISITOR_NAME = b.VISITOR_NAME,
                    PHONE_NO = b.PHONE,
                    WORK_UNIT = b.WORK_UNIT,
                    VISIT_PERSON_NAME = c.USER_NAME,
                    DUTIES = f.DATA_CNAME,
                    DEPT_CODE_NAME = g.PGROUP_NAME,
                    SMBL_LAB_NAME = d.POSITION_NAME,//todu
                    PERSON_TYPE = h.DATA_CNAME,
                    VISIT_REQ_TYPE_ID = e.DATA_ID,
                    VISIT_REQ_TYPE = e.DATA_NAME,
                    FELLOW_PERSON = a.FELLOW_PERSON,
                    FELLOW_PERSON_NUM = a.FELLOW_PERSON_NUM,
                    VISIT_ROOM_ID = d.POSITION_ID,
                    VISIT_ROOM_ADDR = d.POSITION_NAME,
                    VISIT_REQ_REASON = a.VISIT_REQ_REASON,
                    VISIT_TIME_FRAME = SqlFunc.HasValue(a.VISIT_START_TIME) && SqlFunc.HasValue(a.VISIT_END_TIME)
                    ? $"{((DateTime)a.VISIT_START_TIME).ToString("yyyy-MM-dd HH:mm:ss")} ~ {((DateTime)a.VISIT_END_TIME).ToString("yyyy-MM-dd HH:mm:ss")}" : ""
                }).ToListAsync();
            return new ResultDto { success = true, data = res };
        }

        /// <summary>
        /// 获取访客日志（按访客）
        /// </summary>
        /// <param name="startTime">检索开始时间</param>
        /// <param name="endTime">检索结束时间</param>
        /// <returns></returns>
        public async Task<ResultDto> GetVisitorListByVisitor(DateTime startTime, DateTime endTime, string hospitalId)
        {
            var visitRequestList = await _uow.Db.Queryable<PMS_VISIT_REQUEST_LIST>()
                .InnerJoin<PMS_VISITOR_INFO>((a, b) => a.PERSON_ID == b.VISITOR_ID)
                .InnerJoin<PMS_PERSON_INFO>((a, b, c) => a.VISIT_PERSON_ID == c.USER_ID)
                .LeftJoin<OA_BASE_DATA>((a, b, c, d) => a.VISIT_REQ_TYPE == d.DATA_ID && d.CLASS_ID == "申请类型" && d.STATE_FLAG == "1")
                .LeftJoin<SYS6_POSITION_DICT>((a, b, c, d, e) => a.VISIT_ROOM_ID == e.POSITION_ID)
                .Where((a, b, c, d, e) => a.FIRST_RTIME >= startTime && a.FIRST_RTIME <= endTime && a.VISIT_REQ_STATE == "3" && a.HOSPITAL_ID == hospitalId)
                .Select((a, b, c, d, e) => new
                {
                    VISITOR_ID = a.PERSON_ID,
                    b.VISITOR_NAME,
                    PHONE_NO = b.PHONE,
                    b.WORK_UNIT,
                    a.VISIT_REQ_ID,
                    VISIT_PERSON_NAME = c.USER_NAME,
                    VISIT_REQ_TYPE_ID = d.DATA_ID,
                    VISIT_REQ_TYPE = d.DATA_NAME,
                    a.VISIT_REQ_REASON,
                    a.FELLOW_PERSON,
                    a.FELLOW_PERSON_NUM,
                    a.VISIT_ROOM_ID,
                    VISIT_ROOM_ADDR = e.POSITION_NAME,
                    VISIT_TIME_FRAME = SqlFunc.HasValue(a.VISIT_START_TIME) && SqlFunc.HasValue(a.VISIT_END_TIME)
                    ? $"{((DateTime)a.VISIT_START_TIME).ToString("yyyy-MM-dd HH:mm:ss")} ~ {((DateTime)a.VISIT_END_TIME).ToString("yyyy-MM-dd HH:mm:ss")}" : ""
                }).ToListAsync();
            var res = new List<VisitorListByVistor>();
            foreach (var item in visitRequestList.DistinctBy(p => p.VISITOR_ID).ToList())
            {
                var itemLabPersonList = visitRequestList.Where(p => p.VISITOR_ID == item.VISITOR_ID).Select(i => new LabPersonListByVistor
                {
                    VISIT_REQ_ID = i.VISIT_REQ_ID,
                    VISIT_PERSON_NAME = i.VISIT_PERSON_NAME,
                    VISIT_REQ_TYPE_ID = i.VISIT_REQ_TYPE_ID,
                    VISIT_REQ_TYPE = i.VISIT_REQ_TYPE,
                    VISIT_REQ_REASON = i.VISIT_REQ_REASON,
                    FELLOW_PERSON = i.FELLOW_PERSON,
                    FELLOW_PERSON_NUM = i.FELLOW_PERSON_NUM,
                    VISIT_ROOM_ID = i.VISIT_ROOM_ID,
                    VISIT_ROOM_ADDR = i.VISIT_ROOM_ADDR,
                    VISIT_TIME_FRAME = i.VISIT_TIME_FRAME
                }).ToList();
                res.Add(new VisitorListByVistor
                {
                    VISITOR_ID = item.VISITOR_ID,
                    PHONE_NO = item.PHONE_NO,
                    VISITOR_NAME = item.VISITOR_NAME,
                    WORK_UNIT = item.WORK_UNIT,
                    VISIT_AMOUNT = itemLabPersonList.Count,
                    LabPersonListByVistor = itemLabPersonList
                });
            }
            return new ResultDto { success = true, data = res };
        }

        /// <summary>
        /// 获取访客日志（按科室人员）
        /// </summary>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        public async Task<ResultDto> GetLabPersonListByLabPerson(DateTime startTime, DateTime endTime, string hospitalId)
        {
            var labPersonList = await _uow.Db.Queryable<PMS_VISIT_REQUEST_LIST>()
                .InnerJoin<PMS_VISITOR_INFO>((a, b) => a.PERSON_ID == b.VISITOR_ID)
                .InnerJoin<PMS_PERSON_INFO>((a, b, c) => a.VISIT_PERSON_ID == c.USER_ID)
                .LeftJoin<OA_BASE_DATA>((a, b, c, d) => a.VISIT_REQ_TYPE == d.DATA_ID && d.CLASS_ID == "申请类型" && d.STATE_FLAG == "1")
                .LeftJoin<SYS6_POSITION_DICT>((a, b, c, d, e) => a.VISIT_ROOM_ID == e.POSITION_ID)
                .LeftJoin<SYS6_BASE_DATA>((a, b, c, d, e, f) => c.DUTIES == f.DATA_ID && f.CLASS_ID == "职务" && f.DATA_STATE == "1")
                .LeftJoin<SYS6_INSPECTION_PGROUP>((a, b, c, d, e, f, g) => c.PGROUP_ID == g.PGROUP_ID && g.PGROUP_STATE == "1")
                .LeftJoin<SYS6_BASE_DATA>((a, b, c, d, e, f, g, h) => c.PERSON_TYPE == f.DATA_ID && f.CLASS_ID == "人员类型" && f.DATA_STATE == "1")
                .Where((a, b, c, d, e, f, g, h) => a.FIRST_RTIME >= startTime && a.FIRST_RTIME <= endTime && a.VISIT_REQ_STATE == "3" && a.HOSPITAL_ID == hospitalId)
                .Select((a, b, c, d, e, f, g, h) => new
                {
                    b.VISITOR_NAME,
                    PHONE_NO = b.PHONE,
                    b.WORK_UNIT,
                    a.VISIT_REQ_ID,
                    a.VISIT_PERSON_ID,
                    VISIT_PERSON_NAME = c.USER_NAME,
                    VISIT_REQ_TYPE_ID = d.DATA_ID,
                    VISIT_REQ_TYPE = d.DATA_NAME,
                    a.VISIT_REQ_REASON,
                    a.FELLOW_PERSON,
                    a.FELLOW_PERSON_NUM,
                    VISIT_ROOM_ADDR = e.POSITION_NAME,
                    DUTIES = f.DATA_CNAME,
                    VISIT_TIME_FRAME = SqlFunc.HasValue(a.VISIT_START_TIME) && SqlFunc.HasValue(a.VISIT_END_TIME)
                    ? $"{((DateTime)a.VISIT_START_TIME).ToString("yyyy-MM-dd HH:mm:ss")} ~ {((DateTime)a.VISIT_END_TIME).ToString("yyyy-MM-dd HH:mm:ss")}" : "",
                    DEPT_CODE = c.PGROUP_ID,
                    DEPT_CODE_NAME = g.PGROUP_NAME,
                    PERSON_TYPE = h.DATA_CNAME
                }).ToListAsync();
            var res = new List<LabPersonListByLabPerson>();
            foreach (var item in labPersonList.DistinctBy(p => p.VISIT_PERSON_ID).ToList())
            {
                var itemVisitorList = labPersonList.Where(p => p.VISIT_PERSON_ID == item.VISIT_PERSON_ID).Select(i => new VisitorListByLabPerson
                {
                    VISIT_REQ_ID = i.VISIT_REQ_ID,
                    VISITOR_NAME = i.VISITOR_NAME,
                    PHONE_NO = i.PHONE_NO,
                    WORK_UNIT = i.WORK_UNIT,
                    VISIT_REQ_TYPE = i.VISIT_REQ_TYPE,
                    VISIT_REQ_TYPE_ID = i.VISIT_REQ_TYPE_ID,
                    FELLOW_PERSON = i.FELLOW_PERSON,
                    FELLOW_PERSON_NUM = i.FELLOW_PERSON_NUM,
                    VISIT_REQ_REASON = i.VISIT_REQ_REASON,
                    VISIT_ROOM_ADDR = i.VISIT_ROOM_ADDR,
                    VISIT_TIME_FRAME = i.VISIT_TIME_FRAME
                }).ToList();
                res.Add(new LabPersonListByLabPerson
                {
                    VISIT_PERSON_ID = item.VISIT_PERSON_ID,
                    VISIT_PERSON_NAME = item.VISIT_PERSON_NAME,
                    DUTIES = item.DUTIES,
                    DEPT_CODE = item.DEPT_CODE,
                    DEPT_CODE_NAME = item.DEPT_CODE_NAME,
                    SMBL_LAB_NAME = item.VISIT_ROOM_ADDR,
                    PERSON_TYPE = item.PERSON_TYPE,
                    VISITED_AMOUNT = itemVisitorList.Count,
                    VisitorListByLabPerson = itemVisitorList
                });
            }
            return new ResultDto { success = true, data = res };
        }

        /// <summary>
        /// 获取访客日志（按门禁地点）
        /// </summary>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        public async Task<ResultDto> GetVisitRoomListByRoom(DateTime startTime, DateTime endTime, string hospitalId)
        {
            var visitRoomList = await _uow.Db.Queryable<PMS_VISIT_REQUEST_LIST>()
                .InnerJoin<SYS6_POSITION_DICT>((a, b) => a.VISIT_ROOM_ID == b.POSITION_ID)
                .InnerJoin<PMS_VISITOR_INFO>((a, b, c) => a.PERSON_ID == c.VISITOR_ID)
                .InnerJoin<PMS_PERSON_INFO>((a, b, c, d) => a.VISIT_PERSON_ID == d.USER_ID)
                .LeftJoin<OA_BASE_DATA>((a, b, c, d, e) => a.VISIT_REQ_TYPE == e.DATA_ID && e.CLASS_ID == "申请类型" && e.STATE_FLAG == "1")
                .Where((a, b, c, d, e) => a.FIRST_RTIME >= startTime && a.FIRST_RTIME <= endTime && a.VISIT_REQ_STATE == "3" && a.HOSPITAL_ID == hospitalId)
                .Select((a, b, c, d, e) => new
                {
                    a.VISIT_ROOM_ID,
                    VISIT_ROOM_ADDR = b.POSITION_NAME,
                    a.VISIT_REQ_ID,
                    PHONE_NO = c.PHONE,
                    c.WORK_UNIT,
                    c.VISITOR_NAME,
                    VISIT_PERSON_NAME = d.USER_NAME,
                    VISIT_PERSON_ID = d.USER_ID,
                    VISIT_REQ_TYPE_ID = e.DATA_ID,
                    VISIT_REQ_TYPE = e.DATA_NAME,
                    a.VISIT_REQ_REASON,
                    a.FELLOW_PERSON,
                    a.FELLOW_PERSON_NUM,
                    VISIT_TIME_FRAME = SqlFunc.HasValue(a.VISIT_START_TIME) && SqlFunc.HasValue(a.VISIT_END_TIME)
                    ? $"{((DateTime)a.VISIT_START_TIME).ToString("yyyy-MM-dd HH:mm:ss")} ~ {((DateTime)a.VISIT_END_TIME).ToString("yyyy-MM-dd HH:mm:ss")}" : ""
                }).ToListAsync();
            var res = new List<VisitRoomListByRoom>();
            foreach (var item in visitRoomList.DistinctBy(p => p.VISIT_ROOM_ID).ToList())
            {
                var itemVisitorList = visitRoomList.Where(p => p.VISIT_ROOM_ID == item.VISIT_ROOM_ID).Select(i => new VisitorListByRoom
                {
                    VISIT_REQ_ID = i.VISIT_REQ_ID,
                    VISITOR_NAME = i.VISITOR_NAME,
                    PHONE_NO = i.PHONE_NO,
                    WORK_UNIT = i.WORK_UNIT,
                    VISIT_PERSON_ID = i.VISIT_PERSON_ID,
                    VISIT_PERSON_NAME = i.VISIT_PERSON_NAME,
                    VISIT_REQ_TYPE_ID = i.VISIT_REQ_TYPE_ID,
                    VISIT_REQ_TYPE = i.VISIT_REQ_TYPE,
                    VISIT_REQ_REASON = i.VISIT_REQ_REASON,
                    FELLOW_PERSON = i.FELLOW_PERSON,
                    FELLOW_PERSON_NUM = i.FELLOW_PERSON_NUM,
                    VISIT_ROOM_ADDR = i.VISIT_ROOM_ADDR,
                    VISIT_TIME_FRAME = i.VISIT_TIME_FRAME
                }).ToList();
                res.Add(new VisitRoomListByRoom
                {
                    VISIT_ROOM_ID = item.VISIT_ROOM_ID,
                    VISIT_ROOM_ADDR = item.VISIT_ROOM_ADDR,
                    VISITED_AMOUNT = itemVisitorList.Count,
                    VisitorListByRoom = itemVisitorList
                });
            }
            return new ResultDto { success = true, data = res };
        }
        #endregion

        #region 门禁日志
        /// <summary>
        /// 获取门禁日志（全部）
        /// </summary>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        public async Task<ResultDto> GetAllOutInLogList(DateTime startTime, DateTime endTime, string hospitalId)
        {
            var strDataBase = new string[] { "人员类型", "职务", "职称", "职称级别", "性别" };
            var dataBaseList = await _uow.Db.Queryable<SYS6_BASE_DATA>()
                .Where(p => strDataBase.Contains(p.CLASS_ID) && p.DATA_STATE == "1").ToListAsync();
            var res = await _uow.Db.Queryable<PMS_EGUARD_OUTIN_LIST>()
                .InnerJoin<EMS_EQUIPMENT_INFO>((a, b) => a.EGUARD_DICT_ID == b.EQUIPMENT_ID)
                .InnerJoin<PMS_PERSON_INFO>((a, b, c) => a.PERSON_ID == c.USER_ID)
                .LeftJoin<SYS6_INSPECTION_PGROUP>((a, b, c, d) => c.PGROUP_ID == d.PGROUP_ID)
                .LeftJoin<SYS6_POSITION_DICT>((a, b, c, d, e) => b.POSITION_ID == e.POSITION_ID)
                .Where((a, b, c, d, e) => a.SNAP_TIME >= startTime && a.SNAP_TIME <= endTime && a.HOSPITAL_ID == hospitalId && a.EGUARD_OUTIN_STATE == "1" && a.PERSON_TYPE == "1")
                .Select((a, b, c, d, e) => new OutInLogDto
                {
                    EGUARD_OUTIN_ID = a.EGUARD_OUTIN_ID,
                    PERSON_NAME = c.USER_NAME,
                    PERSON_TYPE_ID = c.PERSON_TYPE,
                    ACCESS_TIME = a.SNAP_TIME,
                    SNAP_PIC = a.SNAP_PIC,
                    DEPT_CODE = c.PGROUP_ID,
                    DEPT_CODE_NAME = d.PGROUP_NAME,
                    SMBL_LAB = e.POSITION_ID,
                    SMBL_LAB_NAME = e.POSITION_NAME,
                    DUTIES = c.DUTIES,
                    PHONE_NO = c.PHONE,
                    VISIT_ROOM_ADDR = e.POSITION_NAME,
                    TECH_POST_LEVEL = c.TECH_POST,
                    VISIT_ROOM_ID = e.POSITION_ID,
                    EGUARD_NAME = b.EQUIPMENT_NAME,
                }).ToListAsync();

            foreach (var item in res)
            {
                item.PERSON_TYPE = dataBaseList.Where(p => p.CLASS_ID == "人员类型" && p.DATA_ID == item.PERSON_TYPE_ID).FirstOrDefault()?.DATA_CNAME;
                item.DUTIES = dataBaseList.Where(p => p.CLASS_ID == "职务" && p.DATA_ID == item.DUTIES).FirstOrDefault()?.DATA_CNAME;
                item.TECH_POST_NAME = dataBaseList.Where(p => p.CLASS_ID == "职称" && p.DATA_ID == item.TECH_POST).FirstOrDefault()?.DATA_CNAME;
                item.TECH_POST_LEVEL = dataBaseList.Where(p => p.CLASS_ID == "职称级别" && p.DATA_ID == item.TECH_POST).FirstOrDefault()?.DATA_CNAME;
                item.SEX = dataBaseList.Where(p => p.CLASS_ID == "性别" && p.DATA_ID == item.SEX).FirstOrDefault()?.DATA_CNAME;
            }
            var res1 = await _uow.Db.Queryable<PMS_EGUARD_OUTIN_LIST>()
                .InnerJoin<EMS_EQUIPMENT_INFO>((a, b) => a.EGUARD_DICT_ID == b.EQUIPMENT_ID)
                .InnerJoin<PMS_VISITOR_INFO>((a, b, c) => a.PERSON_ID == c.VISITOR_ID)
                .LeftJoin<SYS6_POSITION_DICT>((a, b, c, d) => b.POSITION_ID == d.POSITION_ID)
                .Where((a, b, c, d) => a.SNAP_TIME >= startTime && a.SNAP_TIME <= endTime && a.HOSPITAL_ID == hospitalId && a.EGUARD_OUTIN_STATE == "1" && a.PERSON_TYPE == "0")
                .Select((a, b, c, d) => new OutInLogDto
                {
                    EGUARD_OUTIN_ID = a.EGUARD_OUTIN_ID,
                    PERSON_NAME = c.VISITOR_NAME,
                    PERSON_TYPE_ID = "访客",
                    PERSON_TYPE = "访客",
                    ACCESS_TIME = a.SNAP_TIME,
                    SNAP_PIC = a.SNAP_PIC,
                    SMBL_LAB = d.POSITION_ID,
                    SMBL_LAB_NAME = d.POSITION_NAME,
                    PHONE_NO = c.PHONE,
                    VISIT_ROOM_ADDR = d.POSITION_NAME,
                    VISIT_ROOM_ID = d.POSITION_ID,
                    EGUARD_NAME = b.EQUIPMENT_NAME,
                }).ToListAsync();

            res.AddRange(res1);
            return new ResultDto { success = true, data = res };

        }
        /// <summary>
        /// 获取门禁日志（按人员）
        /// </summary>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        public async Task<ResultDto> GetPersonListByPerson(DateTime startTime, DateTime endTime, string hospitalId)
        {
            var strDataBase = new string[] { "人员类型", "职务", "职称", "职称级别", "性别" };
            var dataBaseList = await _uow.Db.Queryable<SYS6_BASE_DATA>()
                .Where(p => strDataBase.Contains(p.CLASS_ID) && p.DATA_STATE == "1").ToListAsync();
            var res1 = await _uow.Db.Queryable<PMS_EGUARD_OUTIN_LIST>()
                .InnerJoin<EMS_EQUIPMENT_INFO>((a, b) => a.EGUARD_DICT_ID == b.EQUIPMENT_ID)
                .InnerJoin<PMS_PERSON_INFO>((a, b, c) => a.PERSON_ID == c.USER_ID)
                .LeftJoin<SYS6_INSPECTION_PGROUP>((a, b, c, d) => c.PGROUP_ID == d.PGROUP_ID)
                .LeftJoin<SYS6_POSITION_DICT>((a, b, c, d, e) => b.POSITION_ID == e.POSITION_ID)
                .Where((a, b, c, d, e) => a.SNAP_TIME >= startTime && a.SNAP_TIME <= endTime && a.HOSPITAL_ID == hospitalId && a.EGUARD_OUTIN_STATE == "1" && a.PERSON_TYPE == "1")
                .Select((a, b, c, d, e) => new OutInLogDto
                {
                    EGUARD_OUTIN_ID = a.EGUARD_OUTIN_ID,
                    PERSON_NAME = c.USER_NAME,
                    PERSON_TYPE_ID = c.PERSON_TYPE,
                    ACCESS_TIME = a.SNAP_TIME,
                    SNAP_PIC = a.SNAP_PIC,
                    DEPT_CODE = c.PGROUP_ID,
                    DEPT_CODE_NAME = d.PGROUP_NAME,
                    SMBL_LAB = e.POSITION_ID,
                    SMBL_LAB_NAME = e.POSITION_NAME,
                    DUTIES = c.DUTIES,
                    PHONE_NO = c.PHONE,
                    VISIT_ROOM_ADDR = e.POSITION_NAME,
                    TECH_POST_LEVEL = c.TECH_POST,
                    VISIT_ROOM_ID = e.POSITION_ID,
                    EGUARD_NAME = b.EQUIPMENT_NAME,
                    PERSON_ID = a.PERSON_ID
                }).ToListAsync();
            foreach (var item in res1)
            {
                item.PERSON_TYPE = dataBaseList.Where(p => p.CLASS_ID == "人员类型" && p.DATA_ID == item.PERSON_TYPE_ID).FirstOrDefault()?.DATA_CNAME;
                item.DUTIES = dataBaseList.Where(p => p.CLASS_ID == "职务" && p.DATA_ID == item.DUTIES).FirstOrDefault()?.DATA_CNAME;
                item.TECH_POST_NAME = dataBaseList.Where(p => p.CLASS_ID == "职称" && p.DATA_ID == item.TECH_POST).FirstOrDefault()?.DATA_CNAME;
                item.TECH_POST_LEVEL = dataBaseList.Where(p => p.CLASS_ID == "职称级别" && p.DATA_ID == item.TECH_POST).FirstOrDefault()?.DATA_CNAME;
                item.SEX = dataBaseList.Where(p => p.CLASS_ID == "性别" && p.DATA_ID == item.SEX).FirstOrDefault()?.DATA_CNAME;
            }
            var res2 = await _uow.Db.Queryable<PMS_EGUARD_OUTIN_LIST>()
                .InnerJoin<EMS_EQUIPMENT_INFO>((a, b) => a.EGUARD_DICT_ID == b.EQUIPMENT_ID)
                .InnerJoin<PMS_VISITOR_INFO>((a, b, c) => a.PERSON_ID == c.VISITOR_ID)
                .LeftJoin<SYS6_POSITION_DICT>((a, b, c, d) => b.POSITION_ID == d.POSITION_ID)
                .Where((a, b, c, d) => a.SNAP_TIME >= startTime && a.SNAP_TIME <= endTime && a.HOSPITAL_ID == hospitalId && a.EGUARD_OUTIN_STATE == "1" && a.PERSON_TYPE == "0")
                .Select((a, b, c, d) => new OutInLogDto
                {
                    EGUARD_OUTIN_ID = a.EGUARD_OUTIN_ID,
                    PERSON_NAME = c.VISITOR_NAME,
                    PERSON_TYPE_ID = "访客",
                    PERSON_TYPE = "访客",
                    ACCESS_TIME = a.SNAP_TIME,
                    SNAP_PIC = a.SNAP_PIC,
                    SMBL_LAB = d.POSITION_ID,
                    SMBL_LAB_NAME = d.POSITION_NAME,
                    PHONE_NO = c.PHONE,
                    VISIT_ROOM_ADDR = d.POSITION_NAME,
                    VISIT_ROOM_ID = d.POSITION_ID,
                    EGUARD_NAME = b.EQUIPMENT_NAME,
                    PERSON_ID = a.PERSON_ID
                }).ToListAsync();

            res1.AddRange(res2);
            var res = new List<PersonListByPerson>();
            foreach (var item in res1.DistinctBy(p => p.PERSON_ID).ToList())
            {
                var itemEguardList = res1.Where(p => p.PERSON_ID == item.PERSON_ID).Select(i => new EguardListByPerson
                {
                    EGUARD_OUTIN_ID = i.EGUARD_OUTIN_ID,
                    SNAP_PIC = i.SNAP_PIC,
                    ACCESS_TIME = i.ACCESS_TIME,
                    VISIT_ROOM_ADDR = i.VISIT_ROOM_ADDR,
                    VISIT_ROOM_ID = i.VISIT_ROOM_ID
                }).ToList();
                res.Add(new PersonListByPerson
                {
                    PERSON_ID = item.PERSON_ID,
                    PERSON_NAME = item.PERSON_NAME,
                    PERSON_TYPE = item.PERSON_TYPE,
                    PERSON_TYPE_ID = item.PERSON_TYPE_ID,
                    DEPT_CODE = item.DEPT_CODE,
                    DEPT_CODE_NAME = item.DEPT_CODE_NAME,
                    DUTIES = item.DUTIES,
                    SMBL_LAB = item.SMBL_LAB,
                    SMBL_LAB_NAME = item.SMBL_LAB_NAME,
                    TECH_POST = item.TECH_POST,
                    TECH_POST_NAME = item.TECH_POST_NAME,
                    SEX = item.SEX,
                    TECH_POST_LEVEL = item.TECH_POST_LEVEL,
                    PHONE_NO = item.PHONE_NO,
                    WORK_UNIT = item.WORK_UNIT,
                    ACCESS_AMOUNT = itemEguardList.Count,
                    EguardListByPerson = itemEguardList
                });
            }
            return new ResultDto { success = true, data = res };
        }

        /// <summary>
        /// 获取门禁（按门禁）
        /// </summary>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        public async Task<ResultDto> GetEguardListByEguard(DateTime startTime, DateTime endTime, string hospitalId)
        {
            var strDataBase = new string[] { "人员类型", "职务", "职称", "职称级别", "性别" };
            var dataBaseList = await _uow.Db.Queryable<SYS6_BASE_DATA>()
                .Where(p => strDataBase.Contains(p.CLASS_ID) && p.DATA_STATE == "1").ToListAsync();
            var res1 = await _uow.Db.Queryable<PMS_EGUARD_OUTIN_LIST>()
                .InnerJoin<EMS_EQUIPMENT_INFO>((a, b) => a.EGUARD_DICT_ID == b.EQUIPMENT_ID)
                .InnerJoin<PMS_PERSON_INFO>((a, b, c) => a.PERSON_ID == c.USER_ID)
                .LeftJoin<SYS6_INSPECTION_PGROUP>((a, b, c, d) => c.PGROUP_ID == d.PGROUP_ID)
                .LeftJoin<SYS6_POSITION_DICT>((a, b, c, d, e) => b.POSITION_ID == e.POSITION_ID)
                .Where((a, b, c, d, e) => a.SNAP_TIME >= startTime && a.SNAP_TIME <= endTime && a.HOSPITAL_ID == hospitalId && a.EGUARD_OUTIN_STATE == "1" && a.PERSON_TYPE == "1")
                .Select((a, b, c, d, e) => new OutInLogDto
                {
                    EGUARD_OUTIN_ID = a.EGUARD_OUTIN_ID,
                    PERSON_NAME = c.USER_NAME,
                    PERSON_TYPE_ID = c.PERSON_TYPE,
                    ACCESS_TIME = a.SNAP_TIME,
                    SNAP_PIC = a.SNAP_PIC,
                    DEPT_CODE = c.PGROUP_ID,
                    DEPT_CODE_NAME = d.PGROUP_NAME,
                    SMBL_LAB = e.POSITION_ID,
                    SMBL_LAB_NAME = e.POSITION_NAME,
                    DUTIES = c.DUTIES,
                    PHONE_NO = c.PHONE,
                    VISIT_ROOM_ADDR = e.POSITION_NAME,
                    TECH_POST_LEVEL = c.TECH_POST,
                    VISIT_ROOM_ID = e.POSITION_ID,
                    EGUARD_NAME = b.EQUIPMENT_NAME,
                    PERSON_ID = a.PERSON_ID,
                    EGUARD_ID = b.EQUIPMENT_ID
                }).ToListAsync();
            foreach (var item in res1)
            {
                item.PERSON_TYPE = dataBaseList.Where(p => p.CLASS_ID == "人员类型" && p.DATA_ID == item.PERSON_TYPE_ID).FirstOrDefault()?.DATA_CNAME;
                item.DUTIES = dataBaseList.Where(p => p.CLASS_ID == "职务" && p.DATA_ID == item.DUTIES).FirstOrDefault()?.DATA_CNAME;
                item.TECH_POST_NAME = dataBaseList.Where(p => p.CLASS_ID == "职称" && p.DATA_ID == item.TECH_POST).FirstOrDefault()?.DATA_CNAME;
                item.TECH_POST_LEVEL = dataBaseList.Where(p => p.CLASS_ID == "职称级别" && p.DATA_ID == item.TECH_POST).FirstOrDefault()?.DATA_CNAME;
                item.SEX = dataBaseList.Where(p => p.CLASS_ID == "性别" && p.DATA_ID == item.SEX).FirstOrDefault()?.DATA_CNAME;
            }
            var res2 = await _uow.Db.Queryable<PMS_EGUARD_OUTIN_LIST>()
                .InnerJoin<EMS_EQUIPMENT_INFO>((a, b) => a.EGUARD_DICT_ID == b.EQUIPMENT_ID)
                .InnerJoin<PMS_VISITOR_INFO>((a, b, c) => a.PERSON_ID == c.VISITOR_ID)
                .LeftJoin<SYS6_POSITION_DICT>((a, b, c, d) => b.POSITION_ID == d.POSITION_ID)
                .Where((a, b, c, d) => a.SNAP_TIME >= startTime && a.SNAP_TIME <= endTime && a.HOSPITAL_ID == hospitalId && a.EGUARD_OUTIN_STATE == "1" && a.PERSON_TYPE == "0")
                .Select((a, b, c, d) => new OutInLogDto
                {
                    EGUARD_OUTIN_ID = a.EGUARD_OUTIN_ID,
                    PERSON_NAME = c.VISITOR_NAME,
                    PERSON_TYPE_ID = "访客",
                    PERSON_TYPE = "访客",
                    ACCESS_TIME = a.SNAP_TIME,
                    SNAP_PIC = a.SNAP_PIC,
                    SMBL_LAB = d.POSITION_ID,
                    SMBL_LAB_NAME = d.POSITION_NAME,
                    PHONE_NO = c.PHONE,
                    VISIT_ROOM_ADDR = d.POSITION_NAME,
                    VISIT_ROOM_ID = d.POSITION_ID,
                    EGUARD_NAME = b.EQUIPMENT_NAME,
                    PERSON_ID = a.PERSON_ID,
                    EGUARD_ID = b.EQUIPMENT_ID
                }).ToListAsync();
            res1.AddRange(res2);
            var res = new List<EguardListByEguard>();
            foreach (var item in res1.DistinctBy(p => p.VISIT_ROOM_ID).ToList())
            {
                var itemPersonList = res1.Where(p => p.EGUARD_ID == item.EGUARD_ID).Select(i => new PersonListByEguard
                {
                    PERSON_ID = item.PERSON_ID,
                    PERSON_NAME = item.PERSON_NAME,
                    PERSON_TYPE = item.PERSON_TYPE,
                    PERSON_TYPE_ID = item.PERSON_TYPE_ID,
                    DEPT_CODE = item.DEPT_CODE,
                    DEPT_CODE_NAME = item.DEPT_CODE_NAME,
                    DUTIES = item.DUTIES,
                    SMBL_LAB = item.SMBL_LAB,
                    SMBL_LAB_NAME = item.SMBL_LAB_NAME,
                    TECH_POST = item.TECH_POST,
                    TECH_POST_NAME = item.TECH_POST_NAME,
                    SEX = item.SEX,
                    TECH_POST_LEVEL = item.TECH_POST_LEVEL,
                    PHONE_NO = item.PHONE_NO,
                    WORK_UNIT = item.WORK_UNIT,
                    SNAP_PIC = item.SNAP_PIC,
                    ACCESS_TIME = i.ACCESS_TIME,
                    EGUARD_OUTIN_ID = i.EGUARD_OUTIN_ID
                }).ToList();
                res.Add(new EguardListByEguard
                {
                    EGUARD_ID = item.EGUARD_ID,
                    EGUARD_NAME = item.EGUARD_NAME,
                    VISIT_ROOM_ADDR = item.VISIT_ROOM_ADDR,
                    ACCESS_AMOUNT = itemPersonList.Count,
                    PersonListByEguard = itemPersonList
                });
            }
            return new ResultDto { success = true, data = res };
        }

        /// <summary>
        /// 门禁组合操作校验
        /// </summary>
        /// <param name="eguardComIds"></param>
        /// <param name="operType"></param>
        /// <returns></returns>
        public async Task<ResultDto> EguardComOperVerify(List<string>? eguardComIds, string? eguardId, string operType)
        {
            if(eguardId.IsNotNullOrEmpty())
            {
                eguardComIds = await _uow.Db.Queryable<PMS_EGUARD_COM_LIST>().Where(p => p.EQUIPMENT_ID == eguardId && p.ECOMBINE_STATE == "1").Select(i => i.EGUARD_COM_ID).ToListAsync();
            }
            if(eguardComIds != null && eguardComIds.Count > 0)
            {
                var ifUse = await _uow.Db.Queryable<PMS_EGUARD_AUTH_COM>().AnyAsync(p => p.EGUARD_DATA_TYPE == "1" && eguardComIds.Contains(p.EGUARD_DATA_ID) && p.EGUARD_AUTH_CSTATE == "1");
                if (ifUse)
                {
                    return new ResultDto { success = false, msg = eguardId.IsNotNullOrEmpty() ? $"该门禁关联的组合已有授权信息，无法{operType}" : $"所选门禁组合已有授权信息，无法{operType}" };
                }
            }
            return new ResultDto { success = true };
        }

        /// <summary>
        /// 门禁计划操作校验
        /// </summary>
        /// <param name="eguardPassId"></param>
        /// <param name="operType"></param>
        /// <returns></returns>
        public async Task<ResultDto> EguardPassOperVerify(string? eguardPassId, string operType)
        {
            var ifUse = await _uow.Db.Queryable<PMS_EGUARD_AUTH>().AnyAsync(p => p.EGUARD_PASS_ID == eguardPassId && p.EGUARD_AUTH_STATE == "1");
            if (ifUse)
            {
                return new ResultDto { success = false, msg =  $"该门禁计划已有授权信息，无法{operType}"};
            }
            return new ResultDto { success = true };
        }
        #endregion


        #region 获取人员信息
        /// <summary>
        /// 获取门禁用户信息
        /// </summary>
        /// <param name="postId"></param>
        /// <param name="nameOrLogid"></param>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        public async Task<ResultDto> GetNewPostUserInfo(string postId, string? nameOrLogid, string? hospitalId)
        {
            var result = new ResultDto();
            try
            {
                var returnlist = await _uow.Db.Queryable<SYS6_USER_POST>()
                      .InnerJoin<SYS6_USER>((a, b) => a.USER_NO == b.USER_NO)
                      .InnerJoin<PMS_PERSON_INFO>((a, b, c) => a.USER_NO == c.USER_ID)
                      .Where((a, b) => a.POST_ID == postId && b.ACCOUNT_TYPE == "2" && b.STATE_FLAG == "1" && (a.UPOST_STATE == "0" || a.UPOST_STATE == "1"))
                      .WhereIF(hospitalId.IsNotNullOrEmpty(), a => a.HOSPITAL_ID == hospitalId)
                      .Select((a, b, c) => new
                      {
                          UPOST_ID = a.UPOST_ID,
                          POST_ID = a.POST_ID,
                          POSTROLE_ID = a.POST_ID,
                          UPOST_USESDATE = a.UPOST_USE_SDATE,
                          UPOST_USEEDATE = a.UPOST_USE_EDATE,
                          UPOST_END_DATE = a.UPOST_END_DATE,
                          UPOST_STATE = a.UPOST_STATE,
                          USER_NO = a.USER_NO,
                          HOSPITAL_ID = a.HOSPITAL_ID,
                          USERNAME = b.USERNAME,
                          LOGID = b.LOGID,
                          LAB_ID = c.LAB_ID,
                          PERSON_TYPE = c.PERSON_TYPE,
                          PGROUP_ID = c.PGROUP_ID,
                          PERSON_ID = c.PERSON_ID,
                          ss = b.PHONE_NO,
                          PHONE = c.PHONE,
                          SEX = c.SEX
                      }).ToListAsync();
                returnlist = returnlist.OrderBy(p => p.LOGID).ToList();

                if (nameOrLogid.IsNotNullOrEmpty())
                {
                    returnlist = returnlist.Where(p => p.USERNAME.Contains(nameOrLogid) || p.LOGID.Contains(nameOrLogid)).ToList();
                }
                result.success = true;
                result.data = returnlist.DistinctBy(p => p.PERSON_ID).ToList();
            }
            catch (Exception ex)
            {
                result.msg = $"获取人员信息失败！{ex.Message}";
                result.success = false;
            }
            return result;
        }

        /// <summary>
        /// 获取当个岗位/岗位角色涉及的规评
        /// </summary>
        /// <param name="postInfo"></param>
        /// <param name="postroleId"></param>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        public List<OA_EVALUATE_PLAN_UNIT> GetUnitEvaluatelist(SYS6_POST postInfo, string postroleId, string hospitalId)
        {
            List<OA_EVALUATE_PLAN_UNIT> unitEvaluatelist = new List<OA_EVALUATE_PLAN_UNIT>();
            {

            }

            if (postInfo.POST_ULEVEL == "L")
            {
                unitEvaluatelist = _uow.Db.Queryable<OA_EVALUATE_PLAN_UNIT>().Where(o => o.EPLAN_USTATE == "1" &&
               ((o.UNIT_ID == postroleId && o.UNIT_TYPE == "PROLE") || (o.UNIT_ID == postInfo.POST_ID && o.UNIT_TYPE == "POST") || (o.UNIT_ID == postInfo.LAB_ID && o.UNIT_TYPE == "LAB"))).ToList();
            }
            else if (postInfo.POST_ULEVEL == "M")
            {
                unitEvaluatelist = _uow.Db.Queryable<OA_EVALUATE_PLAN_UNIT>().Where(o => o.EPLAN_USTATE == "1" &&
              ((o.UNIT_ID == postroleId && o.UNIT_TYPE == "PROLE") || (o.UNIT_ID == postInfo.POST_ID && o.UNIT_TYPE == "POST") || (o.UNIT_ID == postInfo.LAB_ID && o.UNIT_TYPE == "LAB")
              || (o.UNIT_ID == postInfo.PGROUP_ID && o.UNIT_TYPE == "MGROUP"))).ToList();
            }
            else if (postInfo.POST_ULEVEL == "P")
            {
                //查岗位所属管理专业组
                string mGroupId = "";
                SYS6_INSPECTION_PGROUP pgroupInfo = _uow.Db.Queryable<SYS6_INSPECTION_PGROUP>().First(o => o.PGROUP_ID == postInfo.PGROUP_ID);
                if (pgroupInfo != null)
                {
                    mGroupId = pgroupInfo.MGROUP_ID;
                }
                else
                {
                    mGroupId = "";
                }
                unitEvaluatelist = _uow.Db.Queryable<OA_EVALUATE_PLAN_UNIT>().Where(o => o.EPLAN_USTATE == "1" &&
                ((o.UNIT_ID == postroleId && o.UNIT_TYPE == "PROLE") || (o.UNIT_ID == postInfo.POST_ID && o.UNIT_TYPE == "POST") || (o.UNIT_ID == postInfo.LAB_ID && o.UNIT_TYPE == "LAB")
                || (o.UNIT_ID == mGroupId && o.UNIT_TYPE == "MGROUP") || (o.UNIT_ID == postInfo.PGROUP_ID && o.UNIT_TYPE == "PGROUP"))).ToList();
            }
            return unitEvaluatelist;
        }

        public List<SYS6_INSPECTION_PGROUP> GetLisInspectionPGroup(string lisPgroup)
        {
            if (lisPgroup == "1")
            {
                List<SYS6_INSPECTION_PGROUP> pgrouplist = _uow.Db.Queryable<SYS6_INSPECTION_PGROUP>().ToList();
                List<SYS6_INSPECTION_LAB> lablist = _uow.Db.Queryable<SYS6_INSPECTION_LAB>().Where(o => o.LAB_CLASS != "2").ToList();
                List<string> labIds = lablist.Select(o => "+" + o.LAB_ID + "+").ToList();
                pgrouplist = pgrouplist.Where(o => labIds.Contains("+" + o.LAB_ID + "+")).ToList();
                return pgrouplist;
            }
            else
            {
                return _uow.Db.Queryable<SYS6_INSPECTION_PGROUP>().ToList();
            }
        }

        #endregion
        /// <summary>
        /// 获取门禁下拉基础数据
        /// </summary>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        public async Task<Dictionary<string, List<BaseDataConfigDicDto>>> GetEguardDropDownDataList(string hospitalId,string labId)
        {
            Dictionary<string, List<BaseDataConfigDicDto>> res = new();
            //专业组
            var PGROUP_LIST = await _uow.Db.Queryable<SYS6_INSPECTION_PGROUP>().Where(p => p.HOSPITAL_ID == hospitalId && p.PGROUP_STATE == "1").Select(i => new BaseDataConfigDicDto
            {
                DATA_ID = i.PGROUP_ID,
                DATA_NAME = i.PGROUP_NAME,
                DATA_TYPE = "专业组/分类",
                DATA_SORT = i.PGROUP_SORT
            }).OrderBy(i => i.DATA_SORT).ToListAsync();
            res.Add("专业组/分类", PGROUP_LIST);

            //门禁组合
            var EGUARD_COM = await _uow.Db.Queryable<PMS_EGUARD_COM_DICT>().Where(p => p.HOSPITAL_ID == hospitalId && p.EGUARD_COM_STATE == "1").Select(i => new BaseDataConfigDicDto
            {
                DATA_ID = i.EGUARD_COM_ID,
                DATA_NAME = i.EGUARD_COM_NAME,
                DATA_TYPE = "门禁组合",
            }).OrderBy(i => i.DATA_NAME).ToListAsync();
            res.Add("门禁组合", EGUARD_COM);

            //门禁地点
            var EGUARD_ADDR = await _uow.Db.Queryable<SYS6_POSITION_DICT>().Where(p => p.HOSPITAL_ID == hospitalId && p.POSITION_STATE == "1").Select(i => new BaseDataConfigDicDto
            {
                DATA_ID = i.POSITION_ID,
                DATA_NAME = i.POSITION_NAME,
                DATA_TYPE = "门禁地点",
                DATA_SORT = i.POSITION_SORT
            }).OrderBy(i => i.DATA_SORT).ToListAsync();
            res.Add("门禁地点", EGUARD_ADDR);

            //申请类型
            var VISIT_REQ_TYPE = await _uow.Db.Queryable<OA_BASE_DATA>().Where(p => p.CLASS_ID == "申请类型" && p.STATE_FLAG == "1").Select(i => new BaseDataConfigDicDto
            {
                DATA_ID = i.DATA_ID,
                DATA_NAME = i.DATA_NAME,
                DATA_TYPE = "申请类型",
                DATA_SORT = i.DATA_SORT
            }).OrderBy(i => i.DATA_SORT).ToListAsync();
            res.Add("申请类型", VISIT_REQ_TYPE);

            //科室人员
            var LAB_PERSON_LIST = await _uow.Db.Queryable<PMS_PERSON_INFO>().Where(p => p.HOSPITAL_ID == hospitalId && p.PERSON_STATE == "1" && p.LAB_ID == labId && SqlFunc.HasValue(p.USER_ID)).Select(i => new BaseDataConfigDicDto
            {
                DATA_ID = i.USER_ID,
                DATA_NAME = i.USER_NAME,
                DATA_TYPE = "科室人员",
            }).OrderBy(i => i.DATA_NAME).ToListAsync();
            res.Add("科室人员", LAB_PERSON_LIST);

            //人员类型
            var PERSON_TYPE = await _uow.Db.Queryable<SYS6_BASE_DATA>().Where(p => p.CLASS_ID == "人员类型" && p.DATA_STATE == "1").Select(i => new BaseDataConfigDicDto
            {
                DATA_ID = i.DATA_ID,
                DATA_NAME = i.DATA_CNAME,
                DATA_TYPE = "人员类型",
                DATA_SORT = i.DATA_SORT
            }).OrderBy(i => i.DATA_SORT).ToListAsync();
            res.Add("人员类型", PERSON_TYPE);

            //院区
            var AREA_LIST = await _uow.Db.Queryable<SYS6_INSPECTION_AREA>().Where(p => p.HOSPITAL_ID == hospitalId && p.STATE_FLAG == "1").Select(i => new BaseDataConfigDicDto
            {
                DATA_ID = i.AREA_ID,
                DATA_NAME = i.AREA_NAME,
                DATA_TYPE = "院区",
                DATA_SORT = i.AREA_SORT
            }).OrderBy(i => i.DATA_SORT).ToListAsync();
            res.Add("院区", AREA_LIST);
            return res;
        }

        /// <summary>
        /// 发布按钮是否为new
        /// </summary>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        public async Task<ResultDto> PublishButtonIfNew(string hospitalId)
        {
            var ifNew = await _uow.Db.Queryable<PMS_EGUARD_AUTH>().AnyAsync(p => p.HOSPITAL_ID == hospitalId && p.PUBLISH_STATE == "0");
            return new ResultDto { success = true, data = ifNew };
        }

        /// <summary>
        /// 授权发布
        /// </summary>
        /// <param name="eguardAuthIds"></param>
        /// <returns></returns>
        public async Task<ResultDto> EguardAuthPublish(string hospitalId)
        {
            var eguardAuthIds = await _uow.Db.Queryable<PMS_EGUARD_AUTH>()
                .Where(p => p.HOSPITAL_ID == hospitalId && p.PUBLISH_STATE == "0").Select(i => i.EGUARD_AUTH_ID).ToArrayAsync();
            var res = await StatisticalUserAccessControlTasks(eguardAuthIds);
            return new ResultDto { success = true,data = res};
        }

        /// <summary>
        /// 删除授权设置
        /// </summary>
        /// <param name="eguardAuthIds"></param>
        /// <param name="operPerson"></param>
        /// <returns></returns>
        public async Task<ResultDto> NewDeleteEguardAuth(List<string> eguardAuthIds,string operPerson)
        {
            await DeleteAccessControl(eguardAuthIds.ToArray(),operPerson);
            return new ResultDto { success = true };
        }

         /// <summary>
        /// 新增授权设置或者更新某批授权设置完后，需要调用此方法 空设置id不需要调用 请判断好authIds
        /// </summary>
        /// <param name="authIds">授权设置ids</param>
        /// <exception cref="ArgumentNullException"></exception>
        public async Task AddorUpdateAccessControl(string[] authIds,string operPerson)
        {
            if (authIds == null) throw new ArgumentNullException(nameof(authIds));
            var operTime = _uow.Db.GetDate();
            //意味着这个授权设置需要发布
            await _uow.Db.Updateable<PMS_EGUARD_AUTH>()
                .SetColumns(x => new PMS_EGUARD_AUTH { PUBLISH_STATE = "0", EGUARD_AUTH_STATE = "1",LAST_MPERSON = operPerson,LAST_MTIME = operTime })
                .Where(x => authIds.Contains(x.EGUARD_AUTH_ID))
                .ExecuteCommandAsync();
            //此次为发布
            //重新计算所有需要发布的授权设置任务
            // await StatisticalUserAccessControlTasks( Array.Empty<string>());
        }
        
        /// <summary>
        /// 新删除后，需要调用此方法 空设置id不需要调用 请判断好authIds
        /// </summary>
        /// <param name="authIds">授权设置ids</param>
        /// <exception cref="ArgumentNullException"></exception>
        public async Task DeleteAccessControl(string[] authIds,string operPerson)
        {
            if (authIds == null) throw new ArgumentNullException(nameof(authIds));
            var operTime = _uow.Db.GetDate();
            //设置删除后，先把授权设置状态改变
            await _uow.Db.Updateable<PMS_EGUARD_AUTH>()
                .SetColumns(x => new PMS_EGUARD_AUTH { PUBLISH_STATE = "0", EGUARD_AUTH_STATE = "2",LAST_MPERSON = operPerson,LAST_MTIME = operTime })
                .Where(x => authIds.Contains(x.EGUARD_AUTH_ID))
                .ExecuteCommandAsync();
            //找出剩下的未发布的任务重新计算任务
            // var auths = _uow.Db.Queryable<PMS_EGUARD_AUTH>()
            //     .Where(x => !authIds.Contains(x.EGUARD_AUTH_ID))
            //     .Where(x => x.EGUARD_AUTH_STATE == "0" && x.PUBLISH_STATE == "1")
            //     .Select(x => x.EGUARD_AUTH_ID)
            //     .ToArray();
            // if (auths.Length>0)
            // {
            //     await StatisticalUserAccessControlTasks(authIds);
            // }
        }

        /// <summary>
        /// 授权设置禁用后（包括被影响的组合禁用后，涉及到的授权设置，精确到每一次），需要调用此方法 空设置id不需要调用 请判断好authIds
        /// </summary>
        /// <param name="authIds"></param>
        public async Task DisableAccessControl(string[] authIds)
        {
            if (authIds == null) throw new ArgumentNullException(nameof(authIds));
            
            //授权设置停用后，需要提出授权任务列表
            await _uow.Db.Updateable<PMS_EGUARD_AUTH>()
                .SetColumns(x => new PMS_EGUARD_AUTH { PUBLISH_STATE = "0", EGUARD_AUTH_STATE = "0" })
                .Where(x => authIds.Contains(x.EGUARD_AUTH_ID))
                .ExecuteCommandAsync();
            // //找出剩下的未发布的任务重新计算任务
            // var auths = _uow.Db.Queryable<PMS_EGUARD_AUTH>()
            //     .Where(x => !authIds.Contains(x.EGUARD_AUTH_ID))
            //     .Where(x => x.EGUARD_AUTH_STATE == "0" && x.PUBLISH_STATE == "1")
            //     .Select(x => x.EGUARD_AUTH_ID)
            //     .ToArray();
            // if (auths.Length>0)
            // {
            //     await StatisticalUserAccessControlTasks(authIds);
            // }
        }

        
        
    
        //todo
        /// <summary>
        /// 按时间下发指定的门禁授权任务
        /// </summary>
        /// <param name="time"></param>
        /// <returns></returns>
        public async Task<List<PMS_EGUARD_AUTH_USER>> PerformAccessControTasks(DateTime time)
        {
            var authTasks = await _uow.Db.Queryable<PMS_EGUARD_AUTH_USER>()
                .Where(x=>x.AUTH_START_DATE<=time)
                .Where(x=>x.AUTH_END_DATE>=time)
                .Where(x=>x.AUTH_USER_STATE == "0")
                .Where(x=>!x.ACTIVE_DATE.HasValue)
                .ToListAsync();
            var tasks = new List<Task<PMS_EGUARD_AUTH_USER>>();
            foreach (var task in authTasks)
            {
                tasks.Add(ProcessTaskAsync(task));
            }
            var result=  await Task.WhenAll(tasks);

            foreach (var item in result)
            {
                item.ACTIVE_DATE = DateTime.Now;
            }
            
            await _uow.Db.Updateable(result).UseParameter().ExecuteCommandAsync();
            return result.ToList();
        }

        public async Task<List<bool>> CancelAccessControTasks()
        {
            var authTasks = await _uow.Db.Queryable<PMS_EGUARD_AUTH_USER>()
                .Where(x=>x.AUTH_USER_STATE == "2")
                .OrderByDescending(x=>x.ACTIVE_DATE)
                .ToListAsync();
            var tasks = new List<Task<bool>>();
            foreach (var task in authTasks)
            {
                tasks.Add(CancelTaskAsync(task));
            }
            var result=  await Task.WhenAll(tasks);
            return result.ToList();
        }
        
        private async Task<PMS_EGUARD_AUTH_USER> ProcessTaskAsync(PMS_EGUARD_AUTH_USER item)
        {
             var person = await _uow.Db.Queryable<PMS_PERSON_INFO>()
                .Where(x => x.USER_ID== item.USER_ID)
                .FirstAsync();
             var weekPlanName =
                 $"{item.AUTH_START_DATE!.Value.ToString("yyyy-MM-dd")}至{item.AUTH_END_DATE!.Value.ToString("yyyy-MM-dd")}";
             var planDict = new PMS_EGUARD_PASS_DICT()
             {
                 PASS_TIME_JSON = item.PLAN_JSON,
                 EGUARD_PASS_NAME = weekPlanName
             };
             if (person.PERSON_ID.IsNullOrEmpty())
             {
                 item.PLAN_JSON = planDict.PASS_TIME_JSON;
                 item.AUTH_USER_STATE = "10";
                 item.FAIL_REASON = "当前用户需要在系统管理建立账号";
                 item.REMARK = "PERSON_ID为空";
                 return item;
             }
             try
             {
                 await AccessWeekPlans(planDict);
                 await AccessPlan(planDict);
             }
             catch (BizException e)
             {
                 item.PLAN_JSON = planDict.PASS_TIME_JSON;
                 item.AUTH_USER_STATE = "10";
                 item.FAIL_REASON = $"门禁计划存在异常信息：{e.Message}";
                 item.REMARK = "门禁计划存在异常";
                 return item;
             }
             catch (Exception e)
             {
                 Log.Error($"门禁下发失败:{e}");
                 item.PLAN_JSON = planDict.PASS_TIME_JSON;
                 item.AUTH_USER_STATE = "10";
                 item.FAIL_REASON = $"门禁计划存在异常信息：{e.Message}";
                 item.REMARK = "AccessWeekPlans/AccessPlan";
                 return item;
             }
            
             var plan = JsonConvert.DeserializeObject<PassTimeJson>(planDict.PASS_TIME_JSON);

             var roomSn = GetRoomSn(item.DEVICE_SN);
             if (roomSn.IsNullOrEmpty())
             {
                 item.PLAN_JSON = planDict.PASS_TIME_JSON;
                 item.AUTH_USER_STATE = "10";
                 item.FAIL_REASON = $"{item.DEVICE_SN}未维护房间信息";
                 item.REMARK = "未维护房间信息";
                 return item;
             }
             await DelectDelAccessPerson(person.PERSON_ID, new List<string>(){roomSn});
             try
             {
                var accessResult =  await AccessPersonGrant(person , item.AUTH_START_DATE, item.AUTH_END_DATE, new List<string>(){roomSn} , new List<PassTimeJson>(){plan} ,new List<string>(){item.DEVICE_SN});
                if (accessResult.Count>0)
                {
                    item.PLAN_JSON = planDict.PASS_TIME_JSON;
                    item.AUTH_USER_STATE = accessResult[0].syncStatus == "0" ? "1" : "10";
                    item.FAIL_REASON = accessResult[0].syncStatus == "0" ? "" : accessResult[0].failReason;
                    item.REMARK = accessResult[0].personCode;
                }
                return item;
             }
             catch (BizException e)
             {
                Log.Error($"门禁下发失败:{e}");
                item.PLAN_JSON = planDict.PASS_TIME_JSON;
                item.AUTH_USER_STATE = "10";
                item.FAIL_REASON = $"{e.Message}";
                item.REMARK = "AccessPersonGrant";
                return item;
             }
             catch (Exception e)
             {
               Log.Error($"门禁下发失败:{e}");
               item.PLAN_JSON = planDict.PASS_TIME_JSON;
               item.AUTH_USER_STATE = "10";
               item.FAIL_REASON = "调用电信接口失败";
               item.REMARK = "AccessPersonGrant";
               return item;
             }
        }
        
        private async Task<bool> CancelTaskAsync(PMS_EGUARD_AUTH_USER item)
        {
            var person = await _uow.Db.Queryable<PMS_PERSON_INFO>()
                .Where(x => x.PERSON_ID == item.USER_ID)
                .FirstAsync();
            var roomSn = GetRoomSn(item.DEVICE_SN);
            if (roomSn.IsNullOrEmpty())
            {
                Log.Error($"{item.DEVICE_SN}未维护房间信息");
                return false;
            }
            return await DelectDelAccessPerson(person.PERSON_ID, new List<string>(){roomSn});
        }
        
        /// <summary>
        /// 按人统计门禁计划
        /// </summary>
        /// <param name="authId">具体哪次授权设置</param>
        /// <param name="user">具体哪个人</param>
        /// <returns>List<PMS_EGUARD_AUTH_USER></returns>
        /// <remarks> 发布按钮点击 -  StatisticalUserAccessControlTasks(Array.Empty<string>()) </remarks>
        public async Task<List<PMS_EGUARD_AUTH_USER>> StatisticalUserAccessControlTasks(string[] authIds, PMS_PERSON_INFO? user = null)
        {
            var result = new List<PMS_EGUARD_AUTH_USER>();
            var authRecords = new List<PMS_EGUARD_AUTH>();
            var userAuthsDict = await GetUserAuthorizationsAsync(authIds);
            if (user is null)
            {
                foreach (var userAuths in userAuthsDict)
                {
                    var tasks = CombinedAuthTasks(userAuths.Key , userAuths.Value);
                    result.AddRange(tasks);
                    authRecords.AddRange(userAuths.Value);
                }
            }
            else
            {
                if (userAuthsDict.TryGetValue(user.PERSON_ID, out var auths))
                {
                    var tasks = CombinedAuthTasks(user.USER_ID,auths);
                    result.AddRange(tasks);
                    authRecords.AddRange(auths);
                }
            }
            using (_uow.Begin())
            {
                //未发布，全部删除
                await _uow.Db.Deleteable<PMS_EGUARD_AUTH_USER>()
                    .Where(x => x.AUTH_USER_STATE == "0")
                    .ExecuteCommandAsync();
                //已发布，需要删除电信授权
                await _uow.Db.Updateable<PMS_EGUARD_AUTH_USER>()
                    .SetColumns(x =>  new PMS_EGUARD_AUTH_USER{ AUTH_USER_STATE = "2" , FAIL_REASON = "设置更新"  })
                    .Where(x=>x.AUTH_USER_STATE == "1")
                    .Where(x=>x.ACTIVE_DATE.HasValue)
                    .Where(x=>x.ACTIVE_DATE < DateTime.Now && x.AUTH_END_DATE > DateTime.Now)
                    .ExecuteCommandAsync();

                //点击发布按钮的时候，将未发布状态的授权设置为已发布
                await _uow.Db.Updateable<PMS_EGUARD_AUTH>()
                    .SetColumns(x => new PMS_EGUARD_AUTH
                    {
                        PUBLISH_STATE = "1"
                    })
                    //.Where(x=>x.EGUARD_AUTH_STATE == "1")
                    .Where(x=>x.PUBLISH_STATE == "0")
                    .ExecuteCommandAsync();
                
                if (result.Count>0)
                {
                    await _uow.Db.Insertable(result).UseParameter().ExecuteCommandAsync();
                }
                await _uow.SaveChangesAsync();
            }
           
            return result;
        }

        /// <summary>
        /// 门禁授权到电信
        /// </summary>
        /// <param name="person"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <param name="roomIds"></param>
        /// <param name="plans"></param>
        /// <returns></returns>
        private async Task<List<AccessPersonResult>> AccessPersonGrant(PMS_PERSON_INFO person , DateTime? startTime, DateTime? endTime,  List<string> roomIds, List<PassTimeJson> plans , List<string> sns)
        {
            var head = XhXmlRoot.CreatRoot("AccessPersonGrant");
            var body = BuildPersonGrant(person, startTime, endTime, roomIds, plans,sns);
            if (body.RoomPlans.Count == 0 )
            {
                Log.Error($"门禁授权下发失败 调用S01参数为: {JsonConvert.SerializeObject(body)}");
                return new ();
            }
            var request = XhPlatformRequest.Create(head, body);
            Console.WriteLine(JsonConvert.SerializeObject(request, Formatting.Indented));
            var ip = _configuration["S10"];
            var client = new PlatformClient(ip, _httpContext);//XhPlatformResponse<PlatformAccessWeekPlanDto>
            var result = await client.ClientPostAsync<XhPlatformRequest, XhPlatformResponse1>("S10/API", request, false);
            Console.WriteLine("JSON数据");
            Console.WriteLine(JsonConvert.SerializeObject(result, Formatting.Indented));
            if (result.ResultCode == "1")
            { 
                Log.Information($"门禁授权下发成功模板创建/更新成功 调用电信参数为: {JsonConvert.SerializeObject(body)}");
                Log.Information($"门禁授权下发成功模板创建/更新成功 调用S01参数为: {JsonConvert.SerializeObject(request)}");
                Log.Information($"门禁授权下发成功模板创建/更新成功 调用S01返回结果为: {JsonConvert.SerializeObject(result)}");
                //todo 需要返回授权结果
                return  JsonConvert.DeserializeObject<List<AccessPersonResult>>(result.Items.ToString());
            }
            else
            {
                //todo 需要返回授权结果
                Log.Error($"门禁授权下发成功模板创建/更新成功 调用电信参数为: {JsonConvert.SerializeObject(body)}");
                Log.Error($"门禁授权下发失败 调用S01参数为: {JsonConvert.SerializeObject(request)}");
                return  JsonConvert.DeserializeObject<List<AccessPersonResult>>(result.Items.ToString());
            }
        }

        /// <summary>
        /// 构建人员授权对象
        /// </summary>
        /// <param name="person"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <param name="RoomIds"></param>
        /// <param name="plans"></param>
        /// <param name="sns"></param>
        /// <returns></returns>
        private XhXmlAccessPersonGrantDto BuildPersonGrant(PMS_PERSON_INFO person, DateTime? startTime,
            DateTime? endTime, List<string> RoomIds, List<PassTimeJson> plans, List<string> sns)
        {

            if (person.FACE_CLIP_PHOTO.IsNullOrEmpty())
            {
                Log.Information($"人员照片为空,人员ID:{person.USER_ID}");
                throw new BizException("人员照片为空");
            }
            var path = OfficeHelper.PathCombine(_configuration.GetSection("S54").Value, person.FACE_CLIP_PHOTO);
            var photoBytes = DownLoadFileToLocal(path);
            var result = new XhXmlAccessPersonGrantDto
            {
                Code = $"XHL{person.PERSON_ID}",
                Name = person.USER_NAME ?? string.Empty,
                Sex = person.SEX is not null ? int.Parse(person.SEX!)  == 1  ? 1 : 0  : null,
                IdCard = person.ID_CARD is not  null ? person.ID_CARD! : null ,
                Phone = person.PHONE is not  null ? person.PHONE! : null,
                Type = 3,
                UserId = person.USER_ID,
                 FilePath = PicBase64Helper.CompressBase64Image(PicBase64Helper.CompressImageToBase64String(photoBytes),200),
                //FilePath = "",
                RoomPlans = new List<RoomPlan>(),
                Sns = sns
            };
            foreach (var roomId in RoomIds)
            {
                var roomPlan = new RoomPlan
                {
                    RoomId = long.Parse(roomId),
                    StartTime = startTime.HasValue ?  startTime.Value.ToString("yyyy-MM-dd HH:mm:ss") : null,
                    EndTime = endTime.HasValue? endTime.Value.ToString("yyyy-MM-dd HH:mm:ss") : null,
                    PlanId = String.Empty ,
                };
                var tempPlans = new List<string>();
                foreach (var plan in plans)
                {
                    if (plan.PlanId.HasValue)
                    {
                        tempPlans.Add(plan.PlanId.Value.ToString());
                    }
                }

                if (tempPlans.Count>0)
                {
                    roomPlan.PlanId = string.Join(",", tempPlans);
                    result.RoomPlans.Add(roomPlan);
                }
               
            }
            return result;
        }
        /// <summary>
        /// 调用电信下发周计划模板
        /// </summary>
        /// <param name="weekPlansDict"></param>
        /// <exception cref="BizException"></exception>
        private  async Task AccessWeekPlans(PMS_EGUARD_PASS_DICT weekPlansDict)
        {
            var head = XhXmlRoot.CreatRoot("AccessWeekPlan");
            var body =  BuildWeekPlanTemplate(weekPlansDict);
            var request = XhPlatformRequest.Create(head, body);
            Console.WriteLine(JsonConvert.SerializeObject(request, Formatting.Indented));
            var ip = _configuration["S10"];
            var client = new PlatformClient(ip, _httpContext);
            var result = await client.ClientPostAsync<XhPlatformRequest, XhPlatformResponse1>("S10/API", request, false);
            Console.WriteLine("JSON数据");
            Console.WriteLine(JsonConvert.SerializeObject(result, Formatting.Indented));
            if (result.ResultCode == "1")
            { 
                var  json = JsonConvert.DeserializeObject<PassTimeJson>(weekPlansDict.PASS_TIME_JSON);
                if (!json.Id.HasValue)
                {
                    JArray jsonArray = JArray.Parse(result.Items.ToString());
                    // 遍历数组并提取id值
                    foreach (JObject item in jsonArray)
                    {
                        json.Id = item["id"].Value<int>();
                    }
                    weekPlansDict.PASS_TIME_JSON = JsonConvert.SerializeObject(json);
                }
            }
            else
            {
                Log.Error($"门禁周计划模板创建/更新失败 调用S01参数为: {JsonConvert.SerializeObject(request)}");
                throw new BizException("门禁周计划模板创建/更新失败");
            }
        }
        
        private XhXmlAccessPlanDto BuildPlanTemplate(PMS_EGUARD_PASS_DICT weekPlansDict)
        {
            var weekPlan = JsonConvert.DeserializeObject<PassTimeJson>(weekPlansDict.PASS_TIME_JSON);
            var result = new XhXmlAccessPlanDto {
                enable = "true",
                templateName = weekPlansDict.EGUARD_PASS_NAME,
                weekPlanNo = weekPlan.Id.Value.ToString(),
            };

            if (weekPlan.PlanId.HasValue)
            {
                result.id = weekPlan.PlanId.Value;
            }
            return result;
        }
        
        private async Task AccessPlan(PMS_EGUARD_PASS_DICT weekPlansDict)
        {
            var head = XhXmlRoot.CreatRoot("AccessPlan");
            var body = BuildPlanTemplate(weekPlansDict);
            var request = XhPlatformRequest.Create(head, body);
            Console.WriteLine(JsonConvert.SerializeObject(request, Formatting.Indented));
            var ip = _configuration["S10"];
            var client = new PlatformClient(ip, _httpContext);//XhPlatformResponse<PlatformAccessWeekPlanDto>
            var result =  await client.ClientPostAsync<XhPlatformRequest, XhPlatformResponse1>("S10/API", request, false);
            Console.WriteLine("JSON数据");
            Console.WriteLine(JsonConvert.SerializeObject(result, Formatting.Indented));
            if (result.ResultCode == "1")
            { 
                var  json = JsonConvert.DeserializeObject<PassTimeJson>(weekPlansDict.PASS_TIME_JSON);
                if (!json.PlanId.HasValue)
                {

                    JArray jsonArray = JArray.Parse(result.Items.ToString());
                    // 遍历数组并提取id值
                    foreach (JObject item in jsonArray)
                    {
                        json.PlanId = item["id"].Value<int>();
                    }
                    weekPlansDict.PASS_TIME_JSON = JsonConvert.SerializeObject(json);
                }
            }
            else
            {
                Log.Error($"门禁计划模板创建/更新失败 调用S01参数为: {JsonConvert.SerializeObject(request)}");
                throw new BizException("门禁计划模板创建/更新失败");
            }
        }
        
        private async Task<bool> DelectDelAccessPerson(string userId , List<string> roomIds)
        {
            var rooms = new List<int>();
            foreach (var roomId in roomIds)
            {
                rooms.Add(int.Parse(roomId));
            }
            XhXmlDelAccessPersonGrantDto delAccessPersonGrantDto = new XhXmlDelAccessPersonGrantDto
            {
                code = $"XHL{userId}",
                roomIds = rooms,
                type = 1
            };
            var head = XhXmlRoot.CreatRoot("DelAccessPerson");
            var body = delAccessPersonGrantDto;
            var request = XhPlatformRequest.Create(head, body);
            Console.WriteLine(JsonConvert.SerializeObject(request, Formatting.Indented));
            var ip = _configuration["S10"];
            var client = new PlatformClient(ip, _httpContext);//XhPlatformResponse<PlatformAccessWeekPlanDto>
            var result = await client.ClientPostAsync<XhPlatformRequest, XhPlatformResponse1>("S10/API", request, false);
            Console.WriteLine("JSON数据");
            Console.WriteLine(JsonConvert.SerializeObject(result, Formatting.Indented));
            if (result.ResultCode == "1")
            {
                //TODO 门禁授权被删除后是否需要记录到表里
                Log.Information($"门禁删除成功 调用S01参数为: {JsonConvert.SerializeObject(request)}");
                Log.Information($"门禁删除成功 调用S01参数为: {JsonConvert.SerializeObject(result)}");
                return true;
            }
            else
            {
                Log.Error($"门禁删除失败 调用S01参数为: {JsonConvert.SerializeObject(request)}");
                Log.Error($"门禁删除成功 S01小响应参数为: {JsonConvert.SerializeObject(result)}");
                return false;
            }
        }

        /// <summary>
        /// 删除电信的周计划模板
        /// </summary>
        /// <param name="id"></param>
        private async Task<bool> DelAccessWeekPlanAsync(int id)
        {
            var head = XhXmlRoot.CreatRoot("DelAccessWeekPlanAsync");
            var body = new XhXmlDelAccessWeekPlanDto()
            {
                id = id,
            };
            var request = XhPlatformRequest.Create(head, body);
            Console.WriteLine(JsonConvert.SerializeObject(request, Formatting.Indented));
            var ip = _configuration["S10"];
            var client = new PlatformClient(ip, _httpContext);//XhPlatformResponse<PlatformAccessWeekPlanDto>
            var result = await client.ClientPostAsync<XhPlatformRequest, XhPlatformResponse1>("S10/API", request, false);
            Console.WriteLine("JSON数据");
            Console.WriteLine(JsonConvert.SerializeObject(result, Formatting.Indented));
            if (result.ResultCode == "1")
            {
                Log.Information($"周计划删除成功 调用S01参数为: {JsonConvert.SerializeObject(request)}");
                Log.Information($"周计划删除成功 S01响应参数为: {JsonConvert.SerializeObject(result)}");
                return true;
            }
            else
            {
                Log.Error($"周计划删除失败 调用S01参数为: {JsonConvert.SerializeObject(request)}");
                Log.Error($"周计划删除成功 S01响应参数为: {JsonConvert.SerializeObject(result)}");
                return false;
            }
        }

        private XhXmlAccessWeekPlanDto BuildWeekPlanTemplate(PMS_EGUARD_PASS_DICT weekPlansDict)
        {
            var weekPlan = JsonConvert.DeserializeObject<PassTimeJson>(weekPlansDict.PASS_TIME_JSON);
            var result = new XhXmlAccessWeekPlanDto()
            {
                Name = weekPlansDict.EGUARD_PASS_NAME,
                Enable = true,
                WeekPlanDetail = new List<AccessWeekPlanDetailDto>()
            };
            if (weekPlan.Id.HasValue)
            {
                result.Id = weekPlan.Id.Value;
            }
            if (weekPlan.TimeType  ==  "day")
            {
                for (int i = 1; i <= 7; i++)
                {
                    if (weekPlan.DayTime is  not null) 
                    {
                        var plan = new AccessWeekPlanDetailDto()
                        {
                            Week = AccessWeekTypeEnum.Monday + i - 1,
                            TimeSegment = new List<TimeSegment>()
                        };
                        AddDayTimeToPlan(plan.TimeSegment, weekPlan.DayTime);
                        result.WeekPlanDetail.Add(plan);
                    }
                }
            }
            if (weekPlan.TimeType  ==  "week")
            {
                if ( weekPlan.WeekTime is not  null)
                {
                    foreach (var week in weekPlan.WeekTime)
                    {
                        var plan = new AccessWeekPlanDetailDto()
                        {
                            Week = Enum.Parse<AccessWeekTypeEnum>(week.Week!) ,
                            TimeSegment = new List<TimeSegment>()
                        };
                        AddDayTimeToPlan(plan.TimeSegment, week.Period);
                        result.WeekPlanDetail.Add(plan);
                    }
                }
            }
            Log.Information("周计划模板下发 AccessWeekPlans: {0}", JsonConvert.SerializeObject(result));
            return result;
        }
        private void  AddDayTimeToPlan(List<TimeSegment> timeSegment , List<DayTime>? periods )
        {
            var i = 0;
            foreach (var dayTime in periods)
            {
                if (dayTime.Period != null)
                {
                    string[] timeParts = dayTime.Period.Split("~");
                    if (timeParts.Length == 2)
                    {
                        TimeSpan startTime = TimeSpan.Parse(timeParts[0].Trim());
                        TimeSpan endTime = TimeSpan.Parse(timeParts[1].Trim());
                        timeSegment.Add(new TimeSegment()
                        {
                            Id = ++i,
                            Enable = true,
                            BeginTime = startTime.ToString(),
                            EndTime = endTime.ToString()
                        });
                    }
                }
            }
        }


        /// <summary>
        /// 获取授权设置含盖的所有用户
        /// </summary>
        /// <param name="authIds">具体某次授权设置，传null是全部</param>
        /// <returns></returns>
        private async Task<Dictionary<string, List<PMS_EGUARD_AUTH>>> GetUserAuthorizationsAsync(string[] authIds)
        {
            var longTimeStart = new DateTime(2025,1,1);
            var longTimeEnd = new DateTime(2099,1,1);
            
            var result = new Dictionary<string, List<PMS_EGUARD_AUTH>>();
            var authPostMap = await _uow.Db.Queryable<PMS_EGUARD_AUTH_POST>()
                .InnerJoin<PMS_EGUARD_AUTH>((authPost, auth) => authPost.EGUARD_AUTH_ID == auth.EGUARD_AUTH_ID)
                .LeftJoin<PMS_EGUARD_PASS_DICT>((authPost, auth,dict) => auth.EGUARD_PASS_ID == dict.EGUARD_PASS_ID)
                .Where((authPost, auth,dict) => auth.PUBLISH_STATE == "0") //发布状态的授权设置
                .Where((authPost, auth,dict)=>authPost.EGUARD_AUTH_PSTATE == "1") //在用状态的授权设置
                .WhereIF(authIds.Length>0,(authPost, auth,dict)=> authIds.Contains(authPost.EGUARD_AUTH_ID))
                .Select((authPost, auth,dict) => new  PMS_EGUARD_AUTH
                {
                    EGUARD_AUTH_ID  =  auth.EGUARD_AUTH_ID,
                    HOSPITAL_ID = auth.HOSPITAL_ID,
                    EGUARD_TYPE = auth.EGUARD_TYPE,
                    AUTH_LIMIT_TYPE = auth.AUTH_LIMIT_TYPE,
                    AUTH_START_DATE = auth.AUTH_START_DATE.HasValue ?  auth.AUTH_START_DATE :  longTimeStart,
                    AUTH_END_DATE = auth.AUTH_END_DATE.HasValue ? auth.AUTH_END_DATE: longTimeEnd,
                    EGUARD_PASS_ID = auth.EGUARD_PASS_ID,
                    EGUARD_PASS_JSON = auth.EGUARD_PASS_ID != "0" ? auth.EGUARD_PASS_JSON : dict.PASS_TIME_JSON,
                    EGUARD_AUTH_STATE = auth.EGUARD_AUTH_STATE,
                    FIRST_RPERSON = auth.FIRST_RPERSON,
                    FIRST_RTIME = auth.FIRST_RTIME,
                    LAST_MPERSON = auth.LAST_MPERSON,
                    LAST_MTIME = auth.LAST_MTIME,
                    REMARK = auth.REMARK,
                    PUBLISH_STATE = auth.PUBLISH_STATE,
                    EPLAN_APPLY_TYPE = authPost.EPLAN_APPLY_TYPE,
                    EGUARD_DATA_ID = authPost.EGUARD_DATA_ID,
                    EGUARD_AUTH_TYPE = authPost.EGUARD_AUTH_TYPE,
                })
                .ToListAsync();
            var authPostMapGroups = authPostMap.GroupBy(x => new {x.EGUARD_DATA_ID , x.EPLAN_APPLY_TYPE ,x.EGUARD_AUTH_TYPE});
            foreach (var authPostMapGroup in authPostMapGroups)
            {

                if (authPostMapGroup.Key.EGUARD_AUTH_TYPE == "2")
                {
                    //必须过滤好userId为空的人
                    var userId =  await _uow.Db.Queryable<PMS_PERSON_INFO>()
                        .Where(persosn => persosn.PERSON_ID == authPostMapGroup.Key.EGUARD_DATA_ID)
                        .Select(persosn=> persosn.PERSON_ID)
                        .Distinct()
                        .FirstAsync();
                    if (userId .IsNotNullOrEmpty())
                    {
                        if ( result.TryGetValue(userId!, out var list))
                        {
                            list.AddRange(authPostMapGroup.ToList());
                        }
                        else
                        {
                            result[userId!] = authPostMapGroup.ToList();
                        }
                    }
                }
                else
                {
                    if (authPostMapGroup.Key.EPLAN_APPLY_TYPE == "PROLE" )
                    {
                         var persons =  await _uow.Db.Queryable<PMS_PERSON_INFO>()
                             .InnerJoin<SYS6_USER>((person, user) => person.USER_ID == user.USER_NO)
                             .InnerJoin<SYS6_USER_POST>((person, user,  post) => post.USER_NO == person.USER_ID && post.UPOST_STATE != "0"  && post.UPOST_STATE != "2" )
                             .Where((person, user,  post) => user.STATE_FLAG == "1" && user.ACCOUNT_TYPE == "1")
                             .Where((person, user,  post) => post.POST_ID == authPostMapGroup.Key.EGUARD_DATA_ID)
                             .Select((person, user,  post)=> person.PERSON_ID)
                             .Distinct()
                             .ToListAsync();
                         persons.ForEach(x =>
                             {
                                 if (x is not null)
                                 {
                                     if ( result.TryGetValue(x, out var list))
                                     {
                                         list.AddRange(authPostMapGroup.ToList());
                                     }
                                     else
                                     {
                                         result[x] = authPostMapGroup.ToList();
                                     }
                                 }
                             });
                    }
                    if (authPostMapGroup.Key.EPLAN_APPLY_TYPE == "PROLE" && authPostMapGroup.Key.EGUARD_AUTH_TYPE == "1")
                    {
                        var persons = await _uow.Db.Queryable<PMS_PERSON_INFO>()
                            .InnerJoin<SYS6_USER>((person, user) => person.USER_ID == user.USER_NO)
                            .InnerJoin<SYS6_USER_POST>((person, user,  post) => post.USER_NO == person.USER_ID && post.UPOST_STATE != "0"  && post.UPOST_STATE != "2" )
                            .Where((person, user,  post) => user.STATE_FLAG == "1" && user.ACCOUNT_TYPE == "2")
                            .Where((person, user,  post) => post.POST_ID == authPostMapGroup.Key.EGUARD_DATA_ID)
                            .Select((person, user,  post)=> person.PERSON_ID)
                            .Distinct()
                            .ToListAsync();
                        persons.ForEach(x =>
                        {
                            if (x is not null)
                            {
                                if ( result.TryGetValue(x, out var list))
                                {
                                    list.AddRange(authPostMapGroup.ToList());
                                }
                                else
                                {
                                    result[x] = authPostMapGroup.ToList();
                                }
                            }
                        });
                    }
                    if (authPostMapGroup.Key.EPLAN_APPLY_TYPE == "LAB" )
                    {
                      var persons =  await _uow.Db.Queryable<PMS_PERSON_INFO>()
                            .InnerJoin<SYS6_INSPECTION_LAB>((person, lab) => lab.LAB_ID == person.LAB_ID && lab.STATE_FLAG == "1")
                            .Where((person, lab) => lab.LAB_ID == authPostMapGroup.Key.EGUARD_DATA_ID)
                            .Select((person, lab)=> person.PERSON_ID)
                            .Distinct()
                            .ToListAsync();
                      persons.ForEach(x =>
                            {
                                if (x is not null)
                                {
                                    if ( result.TryGetValue(x, out var list))
                                    {
                                        list.AddRange(authPostMapGroup.ToList());
                                    }
                                    else
                                    {
                                        result[x] = authPostMapGroup.ToList();
                                    }
                                }
                                
                            });
                        
                    }
                    if (authPostMapGroup.Key.EPLAN_APPLY_TYPE == "PGROUP")
                    {
                        var persons = await _uow.Db.Queryable<PMS_PERSON_INFO>()
                            .InnerJoin<SYS6_INSPECTION_PGROUP>((person, pgroup) =>
                                pgroup.PGROUP_ID == person.PGROUP_ID && pgroup.PGROUP_STATE == "1")
                            .Where((person, pgroup) => pgroup.PGROUP_ID == authPostMapGroup.Key.EGUARD_DATA_ID)
                            .Select((person, pgroup)=> person.PERSON_ID)
                            .Distinct()
                            .ToListAsync();
                        
                        persons.ForEach(x =>
                            {
                                if (x is not null)
                                {
                                    if ( result.TryGetValue(x, out var list))
                                    {
                                        list.AddRange(authPostMapGroup.ToList());
                                    }
                                    else
                                    {
                                        result[x] = authPostMapGroup.ToList();
                                    }
                                }
                            });
                        
                    }
                    if (authPostMapGroup.Key.EPLAN_APPLY_TYPE == "MGROUP")
                    {
                        var persons = await _uow.Db.Queryable<PMS_PERSON_INFO>()
                            .InnerJoin<SYS6_INSPECTION_PGROUP>((person, pgroup) =>
                                person.PGROUP_ID == pgroup.PGROUP_ID && pgroup.PGROUP_STATE == "1")
                            .InnerJoin<SYS6_INSPECTION_MGROUP>((person, pgroup, mgroup) =>
                                mgroup.MGROUP_ID == pgroup.MGROUP_ID && mgroup.MGROUP_STATE == "1")
                            .Where((person, pgroup, mgroup) => mgroup.MGROUP_ID == authPostMapGroup.Key.EGUARD_DATA_ID)
                            .Select((person, pgroup, mgroup)=> person.PERSON_ID)
                            .Distinct()
                            .ToListAsync();
                        persons.ForEach(x =>
                            {
                                if (x is not null)
                                {
                                    if ( result.TryGetValue(x, out var list))
                                    {
                                        list.AddRange(authPostMapGroup.ToList());
                                    }
                                    else
                                    {
                                        result[x] = authPostMapGroup.ToList();
                                    }
                                }
                            });
                    }
                    if (authPostMapGroup.Key.EPLAN_APPLY_TYPE == "POST")
                    {
                        var persons = await _uow.Db.Queryable<PMS_PERSON_INFO>()
                            .InnerJoin<SYS6_USER_POST>((person, post) =>
                                post.USER_NO == person.USER_ID && post.UPOST_STATE != "0" && post.UPOST_STATE != "2")
                            .InnerJoin<SYS6_POST_ROLE>((person, post, postRole) => post.POST_ID == postRole.POST_ID)
                            .Where((person, post, postRole) => postRole.POST_ID == authPostMapGroup.Key.EGUARD_DATA_ID)
                            .Select((person, post, postRole) => person.PERSON_ID)
                            .Distinct()
                            .ToListAsync();
                        persons.ForEach(x =>
                            {
                                if (x is not null)
                                {
                                    if ( result.TryGetValue(x, out var list))
                                    {
                                        list.AddRange(authPostMapGroup.ToList());
                                    }
                                    else
                                    {
                                        result[x] = authPostMapGroup.ToList();
                                    }
                                }
                            });
                    }
                }

            }
            return result;
        }
        
        private List<PMS_EGUARD_AUTH_USER> CombinedAuthTasks(string userId ,  List<PMS_EGUARD_AUTH> auths)
        {
            var result = new List<PMS_EGUARD_AUTH_USER>();
            
            var equipmentsAuthsDict = GetAuthRoomEquipments(auths);
            foreach (var equipmentsAuths in equipmentsAuthsDict)
            {
                var intervals = new List<(DateTime Start, DateTime End)>();
                foreach (var auth in equipmentsAuths.Value)
                {
                    intervals.Add((auth.AUTH_START_DATE.Value, auth.AUTH_END_DATE.Value));
                }
                var subIntervals = GetAllSubIntervals(intervals);

                foreach (var subInterval in subIntervals)
                {
                    var subIntervalAuths = equipmentsAuths.Value.Where(x =>
                        x.AUTH_START_DATE <= subInterval.Start && x.AUTH_END_DATE > subInterval.Start)
                        .ToList();
                    //多个通过时间合并成一个电信授权需要的周计划   
                    var weekPlans =  GetAuthWeekPlansByPassTimes(subIntervalAuths.Select(x=>JsonConvert.DeserializeObject<PassTimeJson>(x.EGUARD_PASS_JSON)).ToList());
                    //授权任务表需要存我们格式的通过时间  所以反转为我们的类型PassTimeJson
                    var passTimeJsons  = GetPassTimeJson(weekPlans);
                    //通过下面的方法可以继续反转为电信的周计划
                    //var week = GetAuthWeekPlansByPassTimes(new List<PassTimeJson>() { passTimeJsons });
                    // var weekPlanName =
                    //     $"{subInterval.Start.ToString("yyyy-MM-dd")}至{subInterval.End.ToString("yyyy-MM-dd")}";
                    //
                    // var allWeekPlan = new XhXmlAccessWeekPlanDto()
                    // {
                    //     Name = weekPlanName,
                    //     Enable = true,
                    //     WeekPlanDetail = weekPlans
                    // };
                    var task = new PMS_EGUARD_AUTH_USER
                    {
                        AUTH_USER_ID = IDGenHelper.CreateGuid().Substring(0,18),
                        HOSPITAL_ID =_httpContext.GetHospitalId() ?? "H000",
                        AUTH_TYPE = "2",
                        USER_ID = userId,
                        DEVICE_SN = equipmentsAuths.Key,
                        AUTH_START_DATE = subInterval.Start,
                        AUTH_END_DATE = subInterval.End,
                        PLAN_JSON = JsonConvert.SerializeObject(passTimeJsons),
                        AUTH_USER_STATE = "0",
                        FAIL_REASON = "",
                        FIRST_RPERSON = "system",
                        FIRST_RTIME = DateTime.Now,
                        LAST_MPERSON = "system",
                        LAST_MTIME = DateTime.Now,
                        REMARK = "",
                    };
                    result.Add(task);
                }
                
            }
            return result;
        }

        private PassTimeJson GetPassTimeJson(List<AccessWeekPlanDetailDto> weekPlans)
        {
            var result = new PassTimeJson{
                PlanId = null,
                Id = null,
                TimeType = "week",
                WeekTime = new List<WeekTime>(),
                DayTime = new List<DayTime>()
            };
            foreach (var weekPlan in weekPlans)
            {
                var weekTime = new WeekTime()
                {
                    Week = ((int)weekPlan.Week).ToString(),
                    Period = weekPlan.TimeSegment.Select(x => new DayTime()
                    {
                        Period = x.BeginTime + "~" + x.EndTime
                    }).ToList()
                };
                result.WeekTime.Add(weekTime);
            }
            return result;
        }


        /// <summary>
        /// 授权的门禁设备的所有授权设置
        /// </summary>
        /// <param name="auths"></param>
        /// <returns></returns>
        private Dictionary<string,List<PMS_EGUARD_AUTH>> GetAuthRoomEquipments(List<PMS_EGUARD_AUTH> auths)
        {
            var result = new Dictionary<string, List<PMS_EGUARD_AUTH>>();
            var comAuths = auths.Where(x => x.EGUARD_TYPE == "1");
            var equipmentAuths = auths.Where(x => x.EGUARD_TYPE == "2");
            
            _uow.Db.Queryable<PMS_EGUARD_AUTH>()
                .InnerJoin<PMS_EGUARD_AUTH_COM>((auth, authCom) => auth.EGUARD_AUTH_ID == authCom.EGUARD_AUTH_ID)
                .InnerJoin<PMS_EGUARD_COM_LIST>((auth, authCom, com) => com.EGUARD_COM_ID == authCom.EGUARD_DATA_ID)
                .InnerJoin<EMS_EQUIPMENT_INFO>((auth, authCom, com,equipment)=> com.EQUIPMENT_ID == equipment.EQUIPMENT_ID)
                .Where((auth, authCom, com,equipment) => comAuths.Select(x => x.EGUARD_AUTH_ID).Contains(auth.EGUARD_AUTH_ID)
                                                         && authCom.EGUARD_AUTH_CSTATE == "1" && authCom.EGUARD_DATA_TYPE == "1")
                .Select((auth, authCom, com,equipment) => new { auth, equipment })
                .ForEach(x =>
                {
                    var auth = auths.FirstOrDefault(y=>y.EGUARD_AUTH_ID == x.auth.EGUARD_AUTH_ID);
                    if (auth is not null)
                    {
                        if (x.equipment.SERIAL_NUMBER.IsNotNullOrEmpty())
                        {
                            if (result.TryGetValue(x.equipment.SERIAL_NUMBER, out var list))
                            {
                                list.Add(auth);
                            }
                            else
                            {
                                result[x.equipment.SERIAL_NUMBER] = new List<PMS_EGUARD_AUTH>() { auth };
                            }
                        }
                    }
                });
            _uow.Db.Queryable<PMS_EGUARD_AUTH>()
                .InnerJoin<PMS_EGUARD_AUTH_COM>((auth, authCom) => auth.EGUARD_AUTH_ID == authCom.EGUARD_AUTH_ID)
                .InnerJoin<EMS_EQUIPMENT_INFO>((auth, authCom, equipment) => equipment.EQUIPMENT_ID == authCom.EGUARD_DATA_ID)
                .Where((auth, authCom, equipment) => equipmentAuths.Select(x => x.EGUARD_AUTH_ID).Contains(auth.EGUARD_AUTH_ID)
                                                     && authCom.EGUARD_AUTH_CSTATE == "1" && authCom.EGUARD_DATA_TYPE == "2")
                .Select((auth, authCom, equipment) => new { auth, equipment })
                .ForEach(x =>
                {
                    var auth = auths.FirstOrDefault(y=>y.EGUARD_AUTH_ID == x.auth.EGUARD_AUTH_ID);
                    if (auth is not null)
                    {
                        if (x.equipment.SERIAL_NUMBER.IsNotNullOrEmpty())
                        {
                            if (result.TryGetValue(x.equipment.SERIAL_NUMBER, out var list))
                            {
                                list.Add(auth);
                            }
                            else
                            {
                                result[x.equipment.SERIAL_NUMBER] = new List<PMS_EGUARD_AUTH>() { auth };
                            }
                        }
                    }
                });
            return result;
        }

        /// <summary>
        /// 获取门禁的对照信息
        /// </summary>
        /// <param name="equipmentId"></param>
        /// <returns></returns>
        private string? GetRoomSn(string equipmentSn)
        {
            var roomSn = _uow.Db.Queryable<EMS_EQUIPMENT_INFO>()
                .Where(equipment => equipmentSn == equipment.SERIAL_NUMBER)
                .InnerJoin<SYS6_POSITION_DICT>((equipment, positon) => positon.POSITION_ID == equipment.POSITION_ID)
                .InnerJoin<SYS6_HGROUP_HISID>((equipment, positon, his) => his.DATA_ID == positon.POSITION_ID)
                .Select((equipment, positon, his) => his.HIS_ID)
                .First();
            return roomSn;
        }
    
        /// <summary>
        /// 获取每个星期的授权计划
        /// </summary>
        /// <param name="auths"></param>
        /// <returns></returns>
        private List<AccessWeekPlanDetailDto> GetAuthWeekPlans(List<PMS_EGUARD_AUTH> auths)
        {
            var  result = new List<AccessWeekPlanDetailDto>();
            foreach (var auth in auths)
            {
                var weekPlan = JsonConvert.DeserializeObject<PassTimeJson>(auth.EGUARD_PASS_JSON!);
                if (weekPlan!.TimeType  ==  "day")
                {
                    for (int i = 1; i <= 7; i++)
                    {
                        if (weekPlan.DayTime is  not null) 
                        {
                            var plan = new AccessWeekPlanDetailDto()
                            {
                                Week = AccessWeekTypeEnum.Monday + i - 1,
                                TimeSegment = new List<TimeSegment>()
                            };
                            AddDayTimeToPlan(plan.TimeSegment, weekPlan.DayTime);
                            result.Add(plan);
                        }
                    }
                }
                if (weekPlan.TimeType  ==  "week")
                {
                    if ( weekPlan.WeekTime is not  null)
                    {
                        foreach (var week in weekPlan.WeekTime)
                        {
                            var plan = new AccessWeekPlanDetailDto()
                            {
                                Week = Enum.Parse<AccessWeekTypeEnum>(week.Week!) ,
                                TimeSegment = new List<TimeSegment>()
                            };
                            AddDayTimeToPlan(plan.TimeSegment, week.Period);
                            result.Add(plan);
                        }
                    }
                }
            }

            //根据List<AccessWeekPlanDetailDto>计算一个新的结果，如果同一个星期的时间段有交集，则合并为一个时间段，并且删除重复的时间段。
            return MergeAndRemoveDuplicates(result);
        }

        private List<AccessWeekPlanDetailDto> GetAuthWeekPlansByPassTimes(List<PassTimeJson> passTimes)
        {
            var  result = new List<AccessWeekPlanDetailDto>();
            foreach (var passTime in passTimes)
            {
                var weekPlan = passTime;
                if (weekPlan!.TimeType  ==  "day")
                {
                    for (int i = 1; i <= 7; i++)
                    {
                        if (weekPlan.DayTime is  not null) 
                        {
                            var plan = new AccessWeekPlanDetailDto()
                            {
                                Week = AccessWeekTypeEnum.Monday + i - 1,
                                TimeSegment = new List<TimeSegment>()
                            };
                            AddDayTimeToPlan(plan.TimeSegment, weekPlan.DayTime);
                            result.Add(plan);
                        }
                    }
                }
                if (weekPlan.TimeType  ==  "week")
                {
                    if ( weekPlan.WeekTime is not  null)
                    {
                        foreach (var week in weekPlan.WeekTime)
                        {
                            var plan = new AccessWeekPlanDetailDto()
                            {
                                Week = Enum.Parse<AccessWeekTypeEnum>(week.Week!) ,
                                TimeSegment = new List<TimeSegment>()
                            };
                            AddDayTimeToPlan(plan.TimeSegment, week.Period);
                            result.Add(plan);
                        }
                    }
                }
            }
            return MergeAndRemoveDuplicates(result);
        }

        /// <summary>
        /// 按星期的时间来计算时间段
        /// </summary>
        /// <param name="plans"></param>
        /// <returns></returns>
        private List<AccessWeekPlanDetailDto> MergeAndRemoveDuplicates(List<AccessWeekPlanDetailDto> plans)
        {
            var mergedPlans = new Dictionary<AccessWeekTypeEnum, List<TimeSegment>>();

            foreach (var plan in plans)
            {
                if (!mergedPlans.ContainsKey(plan.Week))
                {
                    mergedPlans[plan.Week] = new List<TimeSegment>();
                }

                // 合并时间段
                foreach (var segment in plan.TimeSegment)
                {
                    mergedPlans[plan.Week].Add(segment);
                }
            }

            // 合并和去重
            foreach (var week in mergedPlans.Keys.ToList())
            {
                mergedPlans[week] = MergeTimeSegments(mergedPlans[week]);
            }

            // 转换回 List<AccessWeekPlanDetailDto>
            var finalResult = new List<AccessWeekPlanDetailDto>();
            foreach (var kvp in mergedPlans)
            {
                finalResult.Add(new AccessWeekPlanDetailDto
                {
                    Week = kvp.Key,
                    TimeSegment = kvp.Value
                });
            }

            return finalResult;
        }
        /// <summary>
        /// 合并合去重时间段
        /// </summary>
        /// <param name="segments"></param>
        /// <returns></returns>
        private List<TimeSegment> MergeTimeSegments(List<TimeSegment> segments)
        {
            // 按开始时间排序
            segments.Sort((a, b) => TimeSpan.Parse(a.BeginTime).CompareTo(TimeSpan.Parse(b.BeginTime)));

            var mergedSegments = new List<TimeSegment>();
            if (segments.Count == 0) return mergedSegments;

            var currentSegment = segments[0];

            
            for (int i = 1; i < segments.Count; i++)
            {
                var nextSegment = segments[i];
                if (TimeSpan.Parse(currentSegment.EndTime) >= TimeSpan.Parse(nextSegment.BeginTime))
                {
                    // 合并时间段
                    currentSegment.EndTime = TimeSpan.FromTicks(Math.Max(TimeSpan.Parse(currentSegment.EndTime).Ticks, TimeSpan.Parse(nextSegment.EndTime).Ticks)).ToString();
                    
                }
                else
                {
                    // 添加当前时间段并开始新的时间段
                    mergedSegments.Add(currentSegment);
                    currentSegment = nextSegment;
                }
            }
            // 添加最后一个时间段
            mergedSegments.Add(currentSegment);

            
            var id = 0;
            foreach (var mergedSegment in mergedSegments)
            {
                mergedSegment.Id = ++id;
            }
            
            return mergedSegments;
        }

        // 获取所有时间子区间
        private List<(DateTime Start, DateTime End)> GetAllSubIntervals(List<(DateTime Start, DateTime End)> intervals)
        {
            if (intervals == null || intervals.Count == 0)
                return new List<(DateTime Start, DateTime End)>();

            // 收集所有时间点
            var allPoints = new List<DateTime>();
            foreach (var interval in intervals)
            {
                allPoints.Add(interval.Start);
                allPoints.Add(interval.End);
            }

            // 去重并排序
            var uniquePoints = allPoints.Distinct().OrderBy(p => p).ToList();

            // 生成子区间
            var subIntervals = new List<(DateTime Start, DateTime End)>();
            for (int i = 0; i < uniquePoints.Count - 1; i++)
            {
                subIntervals.Add((uniquePoints[i], uniquePoints[i + 1]));
            }

            return subIntervals;
        }

        /// <summary>
        /// 从远程服务器上下载读取文件转成byte[]
        /// </summary>
        /// <param name="serviceUrl">远程服务地</param>
        /// <returns></returns>
        private byte[] DownLoadFileToLocal(string serviceUrl)
        {
            try
            {
                ServicePointManager.ServerCertificateValidationCallback = delegate { return true; };
                using WebClient client = new WebClient();
                ServicePointManager.SecurityProtocol = (SecurityProtocolType)192 | (SecurityProtocolType)768 | (SecurityProtocolType)3072;
                byte[] buffer = client.DownloadData(serviceUrl);
                return buffer;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "DownLoadFileToLocal:从远程服务器上下载读取文件转成byte[]出错");
            }
            return null;
        }
    }
}
