﻿//using H.Utility.SqlSugarInfra;
//using SqlSugar;
//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Text;
//using System.Threading.Tasks;

//namespace XH.H81.Models.Entities.Pms
//{
//    /// <summary>
//    /// 访客申请表
//    /// </summary>
//    [DBOwner("XH_OA")]
//    public class PMS_VISIT_REQUEST_LIST
//    {
//        /// <summary>
//        /// 申请ID
//        /// </summary>
//        [SugarColumn(IsPrimaryKey = true)]
//        public string VISIT_REQ_ID { get; set; }

//        /// <summary>
//        /// 医疗机构ID
//        /// </summary>
//        public string HOSPITAL_ID { get; set; }

//        /// <summary>
//        /// 人员ID
//        /// </summary>
//        public string PERSON_ID { get; set; }

//        /// <summary>
//        /// 批次ID
//        /// </summary>
//        public string BATCH_ID { get; set; }

//        /// <summary>
//        /// 申请类型
//        /// </summary>
//        public string VISIT_REQ_TYPE { get; set; }

//        /// <summary>
//        /// 科室人员
//        /// </summary>
//        public string VISIT_PERSON_ID { get; set; }

//        /// <summary>
//        /// 访问地址
//        /// </summary>
//        public string VISIT_ROOM_ID { get; set; }

//        /// <summary>
//        /// 同行人员
//        /// </summary>
//        public string FELLOW_PERSON { get; set; }

//        /// <summary>
//        /// 同行人数
//        /// </summary>
//        public string FELLOW_PERSON_NUM { get; set; }

//        /// <summary>
//        /// 访问理由
//        /// </summary>
//        public string VISIT_REQ_REASON { get; set; }

//        /// <summary>
//        /// 开始时间
//        /// </summary>
//        public DateTime VISIT_START_TIME { get; set; }

//        /// <summary>
//        /// 结束时间
//        /// </summary>
//        public DateTime VISIT_END_TIME { get; set; }

//        /// <summary>
//        /// 状态
//        /// </summary>
//        public string VISIT_REQ_STATE { get; set; }

//        /// <summary>
//        /// 申请来源;1 i检验  2 PC录入
//        /// </summary>
//        public string VISIT_ORIGIN { get; set; }

//        /// <summary>
//        /// 创建人
//        /// </summary>
//        public string FIRST_RPERSON { get; set; }

//        /// <summary>
//        /// 创建时间
//        /// </summary>
//        public DateTime FIRST_RTIME { get; set; }

//        /// <summary>
//        /// 更新人
//        /// </summary>
//        public string LAST_MPERSON { get; set; }

//        /// <summary>
//        /// 更新时间
//        /// </summary>
//        public DateTime LAST_MTIME { get; set; }

//        /// <summary>
//        /// 备注
//        /// </summary>
//        public string REMARK { get; set; }

//    }

//}
