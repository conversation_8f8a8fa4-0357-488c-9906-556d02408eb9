﻿using AutoMapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XH.H81.Models.Entities.Pms;
using XH.H81.Models.Exam;

namespace XH.H81.Models.Dtos.Exam
{

    [AutoMap(typeof(EMP_EXAM_USER_ANSWER), ReverseMap = true)]
    public class EmpExamUserAnswerDto
    {

        public EmpExamUserAnswerDto()
        {
            EXAM_FLAG = 0;
            RECENT_FLAG = 0;
        }
        /// <summary>
        /// 人员考试ID
        /// </summary>
        public string? EXAM_USERID { get; set; }

        /// <summary>
        /// 人员ID
        /// </summary>
        public string? USER_ID { get; set; }
       
        /// <summary>
        /// 考试开始时间 
        /// </summary>
        public DateTime? EXAM_START_DATE { get; set; }

        /// <summary>
        /// 考试成绩
        /// </summary>
        public decimal? EXAM_SCORE { get; set; }

        /// <summary>
        /// 综合评价
        /// </summary>
        public string? COMPREHENSIVE_ASSESS { get; set; }
       

        /// <summary>
        /// 状态[0已报名1已报名确认2未交卷3已交卷4已评卷5已提交6已审核未通过7已审核 10撤销]
        /// </summary>
        public string? EXAM_USER_STATE { get; set; }

        /// <summary>
        /// 考试开始时间 (考生开考时间,非考试设置的开考时间！！)
        /// </summary>
        public DateTime? EXAM_START_TIME { get; set; }

        /// <summary>
        /// 考试标识:0:正常 1补考
        /// </summary>
        public int? EXAM_FLAG { get; set; }

        /// <summary>
        /// 最新标识:0:最新有效 1历史
        /// </summary>
        public int? RECENT_FLAG { get; set; }

        /// <summary>
        /// 科室id
        /// </summary>

        public string? LAB_ID { get; set; }


        /// <summary>
        /// 专业组ID
        /// </summary>

        public string? PGROUP_ID { get; set; }

        /// <summary>
        /// 工号
        /// </summary>

        public string? HIS_ID { get; set; }

        /// <summary>
        /// 名字
        /// </summary>

        public string? PERSON_NAME { get; set; }

        /// <summary>
        /// 专业组名称
        /// </summary>
        public string? PGROUP_NAME { get; set; }

        /// <summary>
        /// 数据来源类型
        /// </summary>

        public string? SOURCE_TYPE { get; set; }

        /// <summary>
        /// 规评组合ID
        /// </summary>

        public string? STD_GROUP_ID { get; set; }

        /// <summary>
        /// 规评方案ID
        /// </summary>

        public string? STD_SCHEME_ID { get; set; }

        /// <summary>
        /// 考试名称
        /// </summary>

        public string? EXAM_NAME { get; set; }


        /// <summary>
        /// 培训ID
        /// </summary>

        public string? TRAIN_ID { get; set; }

        /// <summary>
        /// 资质证书iD
        /// </summary>

        public string? CER_ID { get; set; }


        /// <summary>
        /// 附件
        /// </summary>

        public string? EVALUATE_AFFIX { get; set; }

        /// <summary>
        /// 生安标志
        /// </summary>

        public string? SMBL_FLAG { get; set; }

        /// <summary>
        /// 规评方案适用类型
        /// </summary>
        public string? EPLAN_APPLY_TYPE { get; set; }

        /// <summary>
        /// 归评效期
        /// </summary>
        public decimal? EPLAN_SHELF_LIFE { get; set; }


        /// <summary>
        /// 规评效期单位类型(1-年,2-月,3-日)
        /// </summary>
        public string? SHELF_LIFE_UTYPE { get; set; }

        /// <summary>
        /// 规评预警时长
        /// </summary>
        public decimal? WARN_DURATION { get; set; }

        /// <summary>
        /// 预警时长单位类型(1-年,2-月,3-日)
        /// </summary>
        public string? WARN_UTYPE { get; set; }


        /// <summary>
        /// 备注(用于缺考说明)
        /// </summary>
        public string? REMARK { get; set; }

        /// <summary>
        /// 附件名称
        /// </summary>
        public string? AFFIX_NAME { get; set; }


        /// <summary>
        /// 试卷名称
        /// </summary>
        public string? PAPER_NAME { get; set; }


        /// <summary>
        /// 规评方案名称
        /// </summary>
        public string? EPLAN_NAME { get; set; }

        /// <summary>
        /// 考试分类
        /// </summary>
        public string? EXAM_CLASS { get; set; }

        /// <summary>
        /// 考试分类名称
        /// </summary>
        public string? EXAM_CLASS_NAME { get; set; }

        public string? REJECT_REASON { get; set; }
        public string? REJECT_DATE { get; set; }
        public string? REJECT_PERSON { get; set; }

        /// <summary>
        /// 考试分类id
        /// </summary>
        public string? EVAL_STAGE_CLASS { get; set; }

        /// <summary>
        /// 附件
        /// </summary>
        public List<PMS_PERSON_FILE>? PMS_PERSON_FILE { get; set; }

        public string? PERSON_ID { get; set; }
        /// <summary>
        /// 关联证书对象
        /// </summary>
        public PMS_SKILL_CERTIFICATE_LIST? CERTIFICATE { get; set; }
    }
}
