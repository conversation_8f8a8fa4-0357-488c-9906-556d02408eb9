﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Entities.Eguard
{
    /// <summary>
    /// 访问申请操作日志表
    /// </summary>
    [DBOwner("XH_OA")]
    public class PMS_VISIT_OPER_LOG
    {
        /// <summary>
        /// 日志ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string LOG_ID { get; set; }
        /// <summary>
        /// 访问申请ID
        /// </summary>
        public string VISIT_REQ_ID { get; set; }
        /// <summary>
        /// 医疗机构ID
        /// </summary>
        public string HOSPITAL_ID { get; set; }
        /// <summary>
        /// 操作ID
        /// </summary>
        public string OPER_ID { get; set; }
        /// <summary>
        /// 操作内容
        /// </summary>
        public string? OPER_CONTENT { get; set; }
        /// <summary>
        /// 操作时间
        /// </summary>
        public DateTime? OPER_TIME { get; set; }
        /// <summary>
        /// 操作人
        /// </summary>
        public string? OPER_PERSON { get; set; }
        /// <summary>
        /// 操作电脑
        /// </summary>
        public string? OPER_COMPUTER { get; set; }
        /// <summary>
        /// 原因分类  多个原因分类用逗号分开，不能修改原来分类
        /// </summary>
        public string? CAUSE_TYPE { get; set; }
        /// <summary>
        /// 操作原因
        /// </summary>
        public string? OPER_CAUSE { get; set; }
        /// <summary>
        /// 当前状态
        /// </summary>
        public string? CURRENT_STATE { get; set; }
        /// <summary>
        /// 下一个操作人
        /// </summary>
        public string? OPER_PERSON_NEXT { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public string? OPER_STATE { get; set; }
        public string? FIRST_RPERSON { get; set; }
        public DateTime? FIRST_RTIME { get; set; }
        public string? LAST_MPERSON { get; set; }
        public DateTime? LAST_MTIME { get; set; }
        public string? REMARK { get; set; }
    }
}
