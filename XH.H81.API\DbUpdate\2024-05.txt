﻿-- 人事管理主键增加长度
ALTER TABLE XH_OA.PMS_ARCHITECTURE_INFO MODIFY (ARCHITECTURE_ID VARCHAR2(50));
ALTER TABLE XH_OA.PMS_ASSESS_EXPERT_ITEM MODIFY (ITEM_ID VARCHAR2(50));
ALTER TABLE XH_OA.PMS_ASSESS_FORM_ITEM MODIFY (ITEM_ID VARCHAR2(50));
ALTER TABLE XH_OA.PMS_ASSESS_PLAN_ITEM MODIFY (ITEM_ID VARCHAR2(50));
ALTER TABLE XH_OA.PMS_CHANGE_LOG MODIFY (LOG_ID VARCHAR2(50));
ALTER TABLE XH_OA.PMS_EDUCATION_LIST MODIFY (EDUCATION_ID VARCHAR2(50));
ALTER TABLE XH_OA.PMS_EXCHANGE_LIST MODIFY (EXCHANGE_ID VARCHAR2(50));
ALTER TABLE XH_OA.PMS_EXPATRIATE_LIST MODIFY (EXPATRIATE_ID VARCHAR2(50));
ALTER TABLE XH_OA.PMS_FORM_ITEM MODIFY (ITEM_ID VARCHAR2(50));
ALTER TABLE XH_OA.PMS_INTELLECTUAL_LIST MODIFY (INTELLECTUAL_ID VARCHAR2(50));
ALTER TABLE XH_OA.PMS_PERSON_FILE MODIFY (FILE_ID VARCHAR2(50));
ALTER TABLE XH_OA.PMS_PERSON_INFO MODIFY (PERSON_ID VARCHAR2(50));
ALTER TABLE XH_OA.PMS_PROFESSIONAL_LIST MODIFY (PROFESSIONAL_ID VARCHAR2(50));
ALTER TABLE XH_OA.PMS_RESEARCH_LIST MODIFY (RESEARCH_ID VARCHAR2(50));
ALTER TABLE XH_OA.PMS_RESUME_LIST MODIFY (RESUME_ID VARCHAR2(50));
ALTER TABLE XH_OA.PMS_REWARD_LIST MODIFY (REWARD_ID VARCHAR2(50));





-- 20240530  人事相关ID长度增长脚本
ALTER TABLE  XH_OA.PMS_CHANGE_LOG  MODIFY ( PERSON_ID VARCHAR2(50)); 
ALTER TABLE  XH_OA.PMS_CHANGE_LOG  MODIFY ( HOSPITAL_ID VARCHAR2(50)); 
ALTER TABLE  XH_OA.PMS_CHANGE_LOG  MODIFY ( MODULE_ID VARCHAR2(50)); 
ALTER TABLE  XH_OA.PMS_CLASS_INFO  MODIFY ( HOSPITAL_ID VARCHAR2(50)); 
ALTER TABLE  XH_OA.PMS_EDUCATION_LIST  MODIFY ( PERSON_ID VARCHAR2(50)); 
ALTER TABLE  XH_OA.PMS_EDUCATION_LIST  MODIFY ( HOSPITAL_ID VARCHAR2(50)); 
ALTER TABLE  XH_OA.PMS_EXCHANGE_LIST  MODIFY ( PERSON_ID VARCHAR2(50)); 
ALTER TABLE  XH_OA.PMS_EXCHANGE_LIST  MODIFY ( HOSPITAL_ID VARCHAR2(50)); 
ALTER TABLE  XH_OA.PMS_EXPATRIATE_LIST  MODIFY ( PERSON_ID VARCHAR2(50)); 
ALTER TABLE  XH_OA.PMS_EXPATRIATE_LIST  MODIFY ( HOSPITAL_ID VARCHAR2(50)); 
ALTER TABLE  XH_OA.PMS_INTELLECTUAL_LIST  MODIFY ( PERSON_ID VARCHAR2(50)); 
ALTER TABLE  XH_OA.PMS_INTELLECTUAL_LIST  MODIFY ( HOSPITAL_ID VARCHAR2(50)); 
ALTER TABLE  XH_OA.PMS_PERSON_INFO  MODIFY ( PGROUP_ID VARCHAR2(50)); 
ALTER TABLE  XH_OA.PMS_PERSON_INFO  MODIFY ( HOSPITAL_ID VARCHAR2(50)); 
ALTER TABLE  XH_OA.PMS_PERSON_INFO  MODIFY ( USER_ID VARCHAR2(50)); 
ALTER TABLE  XH_OA.PMS_PERSON_INFO  MODIFY ( LOGID VARCHAR2(50)); 
ALTER TABLE  XH_OA.PMS_PERSON_INFO  MODIFY ( HIS_ID VARCHAR2(50)); 
ALTER TABLE  XH_OA.PMS_PROFESSIONAL_LIST  MODIFY ( HOSPITAL_ID VARCHAR2(50)); 
ALTER TABLE  XH_OA.PMS_PROFESSIONAL_LIST  MODIFY ( PERSON_ID VARCHAR2(50)); 
ALTER TABLE  XH_OA.PMS_RESEARCH_LIST  MODIFY ( PERSON_ID VARCHAR2(50)); 
ALTER TABLE  XH_OA.PMS_RESEARCH_LIST  MODIFY ( HOSPITAL_ID VARCHAR2(50)); 
ALTER TABLE  XH_OA.PMS_RESUME_LIST  MODIFY ( PERSON_ID VARCHAR2(50)); 
ALTER TABLE  XH_OA.PMS_RESUME_LIST  MODIFY ( HOSPITAL_ID VARCHAR2(50)); 
ALTER TABLE  XH_OA.PMS_ADDN_CLASS_INFO  MODIFY ( HOSPITAL_ID VARCHAR2(50)); 
ALTER TABLE  XH_OA.PMS_ADDN_RECORD  MODIFY ( HOSPITAL_ID VARCHAR2(50)); 
ALTER TABLE  XH_OA.PMS_ARCHITECTURE_INFO  MODIFY ( MODULE_ID VARCHAR2(50)); 
ALTER TABLE  XH_OA.PMS_ARCHITECTURE_INFO  MODIFY ( HOSPITAL_ID VARCHAR2(50)); 
ALTER TABLE  XH_OA.PMS_ARCHITECTURE_INFO  MODIFY ( LAB_ID VARCHAR2(50)); 
ALTER TABLE  XH_OA.PMS_SKILL_CERTIFICATE_LIST  MODIFY ( PERSON_ID VARCHAR2(50)); 
ALTER TABLE  XH_OA.PMS_SKILL_CERTIFICATE_LIST  MODIFY ( HOSPITAL_ID VARCHAR2(50)); 
ALTER TABLE  XH_OA.PMS_SOCIAL_OFFICE_LIST  MODIFY ( PERSON_ID VARCHAR2(50)); 
ALTER TABLE  XH_OA.PMS_SOCIAL_OFFICE_LIST  MODIFY ( HOSPITAL_ID VARCHAR2(50)); 
ALTER TABLE  XH_OA.PMS_TRAIN_LIST  MODIFY ( PERSON_ID VARCHAR2(50)); 
ALTER TABLE  XH_OA.PMS_TRAIN_LIST  MODIFY ( HOSPITAL_ID VARCHAR2(50)); 
ALTER TABLE  XH_OA.PMS_THESIS_LIST  MODIFY ( PERSON_ID VARCHAR2(50)); 
ALTER TABLE  XH_OA.PMS_THESIS_LIST  MODIFY ( HOSPITAL_ID VARCHAR2(50)); 
ALTER TABLE  XH_OA.PMS_TEACH_LIST  MODIFY ( PERSON_ID VARCHAR2(50)); 
ALTER TABLE  XH_OA.PMS_TEACH_LIST  MODIFY ( HOSPITAL_ID VARCHAR2(50)); 
ALTER TABLE  XH_OA.PMS_STUDY_LIST  MODIFY ( PERSON_ID VARCHAR2(50)); 
ALTER TABLE  XH_OA.PMS_STUDY_LIST  MODIFY ( HOSPITAL_ID VARCHAR2(50)); 
ALTER TABLE  XH_OA.PMS_REWARD_LIST  MODIFY ( PERSON_ID VARCHAR2(50)); 
ALTER TABLE  XH_OA.PMS_REWARD_LIST  MODIFY ( HOSPITAL_ID VARCHAR2(50)); 
ALTER TABLE  XH_OA.PMS_RESUME_LIST  MODIFY ( PERSON_ID VARCHAR2(50)); 
ALTER TABLE  XH_OA.PMS_REWARD_LIST  MODIFY ( HOSPITAL_ID VARCHAR2(50)); 
ALTER TABLE  XH_OA.PMS_ADDN_CLASS_INFO  MODIFY ( HOSPITAL_ID VARCHAR2(50)); 
ALTER TABLE  XH_OA.PMS_PERSON_FILE  MODIFY ( HOSPITAL_ID VARCHAR2(50));


