﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Dtos.Eguard
{
    public class OutInLogDto
    {
        public string EGUARD_OUTIN_ID { get; set; }
        public string? PERSON_NAME { get; set; }
        public string? PERSON_TYPE { get; set; }
        public DateTime? ACCESS_TIME { get; set; }
        public string? SNAP_PIC { get; set; }
        public string? DEPT_CODE_NAME { get; set; }
        public string? SMBL_LAB_NAME { get; set; }
        public string? DUTIES { get; set; }
        public string? TECH_POST_NAME { get; set; }
        public string? PHONE_NO { get; set; }
        public string? VISIT_ROOM_ADDR { get; set; }
        public string? EGUARD_NAME { get; set; }
        public string? WORK_UNIT { get; set; }
        public string? TECH_POST { get; set; }
        public string? TECH_POST_LEVEL { get; set; }
        public string? SEX { get; set; }
        public string? VISIT_ROOM_ID { get; set; }
        public string? PERSON_TYPE_ID { get; set; }
        public string? DEPT_CODE { get; set; }
        public string? SMBL_LAB { get; set; }
        public string? PERSON_ID { get; set; }
        public string? EGUARD_ID { get; set; }
    }
    public class PersonListByPerson
    {
        public string PERSON_ID { get; set; }
        public string? PERSON_NAME { get; set; }
        public string? PERSON_TYPE { get; set; }
        public string? PERSON_TYPE_ID { get; set; }
        public string? DEPT_CODE_NAME { get; set; }
        public string? DEPT_CODE { get; set; }
        public string? SMBL_LAB_NAME { get; set; }
        public string? SMBL_LAB { get; set; }
        public string? DUTIES { get; set; }
        public string? TECH_POST_NAME { get; set; }
        public int? ACCESS_AMOUNT { get; set; }
        public string? PHONE_NO { get; set; }
        public string? WORK_UNIT { get; set; }
        public string? TECH_POST { get; set; }
        public string? TECH_POST_LEVEL { get; set; }
        public string? SEX { get; set; }
        public List<EguardListByPerson> EguardListByPerson { get; set; }
    }
    public class EguardListByPerson
    {
        public string EGUARD_OUTIN_ID { get; set; }
        public string? VISIT_ROOM_ADDR { get; set; }
        public string? VISIT_ROOM_ID { get; set; }
        public DateTime? ACCESS_TIME { get; set; }
        public string? SNAP_PIC { get; set; }
    }

    public class EguardListByEguard
    {
        public string EGUARD_ID { get; set; }
        public string? VISIT_ROOM_ADDR { get; set; }
        public string? EGUARD_NAME { get; set; }
        public int? ACCESS_AMOUNT { get; set; }
        public List<PersonListByEguard> PersonListByEguard { get; set; }
    }
    public class PersonListByEguard
    {
        public string EGUARD_OUTIN_ID { get; set; }
        public string PERSON_ID { get; set; }
        public string? PERSON_NAME { get; set; }
        public string? PHONE_NO { get; set; }
        public DateTime? ACCESS_TIME { get; set; }
        public string? SNAP_PIC { get; set; }
        public string? PERSON_TYPE { get; set; }
        public string? PERSON_TYPE_ID { get; set; }
        public string? DEPT_CODE_NAME { get; set; }
        public string? DEPT_CODE { get; set; }
        public string? SMBL_LAB_NAME { get; set; }
        public string? SMBL_LAB { get; set; }
        public string? DUTIES { get; set; }
        public string? TECH_POST_NAME { get; set; }
        public string? WORK_UNIT { get; set; }
        public string? TECH_POST { get; set; }
        public string? TECH_POST_LEVEL { get; set; }
        public string? SEX { get; set; }
    }
}
