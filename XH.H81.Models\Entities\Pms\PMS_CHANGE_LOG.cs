﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text;

namespace XH.H81.Models.Entities.Pms
{
    [DBOwner("XH_OA")]
    public class PMS_CHANGE_LOG
    {
        [Key]
        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string LOG_ID { get; set; }
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? HOSPITAL_ID { get; set; }
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? MODULE_ID { get; set; }
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? PERSON_ID { get; set; }
        public DateTime? CHANGE_DATE { get; set; }
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? OPERATE_TYPE { get; set; }
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CHANGE_TYPE { get; set; }
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CHANGE_TABLE { get; set; }
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FIELD_ID { get; set; }
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FIELD_NAME { get; set; }
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CHANAGE_MODULE { get; set; }
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CAUSE_CLASS { get; set; }
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CHANGE_CAUSE { get; set; }
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CHANGE_BEFORE_CONTENT { get; set; }
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CHANGE_AFTER_CONTENT { get; set; }
        public DateTime? REQ_TIME { get; set; }
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? REQ_PERSON { get; set; }
        public DateTime? CHECK_TIME { get; set; }
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CHECK_PERSON { get; set; }
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CHANGE_STATE { get; set; }
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CHANGE_PERSON { get; set; }
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CHANGE_TIME { get; set; }
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CHANGE_COMPUTER { get; set; }
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? REMARK { get; set; }
        /// <summary>
        /// 档案记录ID
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? RECORD_ID { get; set; }
        /// <summary>
        /// 撤销标志(0-否，1-是。表示本次操作是否因撤销操作而失效，默认0。)
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? REVERSE_FLAG { get; set; }

    }
}
