﻿using AutoMapper;
using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Npoi.Mapper;
using Serilog;
using Spire.Doc;
using Spire.Doc.Documents;
using Spire.Doc.Fields;
using SqlSugar;
using System.Collections.Concurrent;
using System.Data;
using System.Drawing;
using System.Net;
using System.Reflection;
using XH.H81.Base.Helper;
using XH.H81.IServices;
using XH.H81.IServices.Pms;
using XH.H81.Models;
using XH.H81.Models.Dtos;
using XH.H81.Models.Dtos.Pms;
using XH.H81.Models.Dtos.Tag;
using XH.H81.Models.Dtos.Template;
using XH.H81.Models.Entites;
using XH.H81.Models.Entities.Pms;
using XH.H81.Models.Exam;
using XH.H81.Models.ExternalEntity;
using XH.H81.Models.SugarDbContext;
using XH.LAB.UTILS.Helpers;
using XH.LAB.UTILS.Interface;
using XH.LAB.UTILS.Models;
using XH.LAB.UTILS.Models.Dto;
using XH.LAB.UTILS.Models.Entites;
using Section = Spire.Doc.Section;

namespace XH.H81.Services.Pms
{
    public class UserService : IUserService
    {
        private readonly ISqlSugarUow<SugarDbContext_Master> _soa;
        private readonly IMapper _mapper;
        private readonly IBaseDataServices _IBaseDataServices;
        private readonly IAuthorityService _IAuthorityService;
        private readonly IAuthorityService2 _IAuthorityService2;
        private readonly IOrganizationTreeService _IOrganizationTreeService;
        private readonly IOrganizationTreeService2 _IOrganizationTreeService2;
        private readonly IPmsTagService _IPmsTagService;
        private readonly IHostingEnvironment _hostingEnvironment;
        private readonly IConfiguration _configuration;
        private readonly string file_preview_address = "";
        private readonly string FileHttpUrl = "/H81pdf/api";
        private readonly IModuleLabGroupService _IModuleLabGroupService;
        private readonly IUploadFileService _IUploadFileService;
        private readonly ILogger<UserService> _logger;
        private readonly IHttpContextAccessor _httpContext;
        static private List<SMBL_POST_PERSON> _SMBL_POST_PERSON_CACHE;
        static private List<SMBL_DEPT_POST> _SMBL_DEPT_POST_CACHE;
        static private List<SMBL_DEPT_NODE> _SMBL_DEPT_NODE_CACHE;
        static private List<SMBL_LAB> _SMBL_LAB_CACHE;
        static private object _SMBL_CACHE_LOCK = new object();
        public UserService(ISqlSugarUow<SugarDbContext_Master> suow, IMapper mapper, IBaseDataServices iBaseDataServices,
            IHostingEnvironment hostingEnvironment, IModuleLabGroupService iModuleLabGroupService, IConfiguration configuration,
            IUploadFileService IUploadFileService, IAuthorityService iAuthorityService, IAuthorityService2 iAuthorityService2, ILogger<UserService> logger,
            IHttpContextAccessor httpContextAccessor, IOrganizationTreeService iOrganizationTreeService, IOrganizationTreeService2 iOrganizationTreeService2,
            IPmsTagService iPmsTagService)
        {
            _soa = suow;
            _mapper = mapper;
            _IBaseDataServices = iBaseDataServices;
            _hostingEnvironment = hostingEnvironment;
            _IModuleLabGroupService = iModuleLabGroupService;
            _configuration = configuration;
            _IUploadFileService = IUploadFileService;
            _IAuthorityService = iAuthorityService;
            _IAuthorityService2 = iAuthorityService2;
            _IOrganizationTreeService = iOrganizationTreeService;
            _IOrganizationTreeService2 = iOrganizationTreeService2;
            _IPmsTagService = iPmsTagService;
            _httpContext = httpContextAccessor;
            _logger = logger;
            file_preview_address = _configuration["S54"];
            ExecutingChangeSqlHelper.ExecutingChangeSql(_soa, configuration, true);
            _IOrganizationTreeService = iOrganizationTreeService;
        }
        void LoadSmblListCache()
        {
            lock (_SMBL_CACHE_LOCK)
            {
                if (_SMBL_POST_PERSON_CACHE == null)
                    _SMBL_POST_PERSON_CACHE = _soa.Db.Queryable<SMBL_POST_PERSON>().Where(a => a.POST_PSTATE == "1" && a.DEPT_POST_ID != null).ToList();

                if (_SMBL_DEPT_POST_CACHE == null)
                    _SMBL_DEPT_POST_CACHE = _soa.Db.Queryable<SMBL_DEPT_POST>().Where(a => a.DEPT_POST_STATE == "1").ToList();

                if (_SMBL_DEPT_NODE_CACHE == null)
                    _SMBL_DEPT_NODE_CACHE = _soa.Db.Queryable<SMBL_DEPT_NODE>().Where(a => a.CURR_NODE_STATE == "1").ToList();

                if (_SMBL_LAB_CACHE == null)
                    _SMBL_LAB_CACHE = _soa.Db.Queryable<SMBL_LAB>().Where(a => a.SMBL_LAB_STATE == "1" && a.PGROUP_SID != null).ToList();
            }
        }

        //统一的科内人员过滤条件（尽量使用）
        //public Expression<Func<PMS_PERSON_INFO, bool>> StandardLabPersonFilter
        //{
        //    get
        //    {
        //        return person =>
        //           person.PERSON_STATE == "1"
        //           && (person.PERSON_TYPE == "1" || person.PERSON_TYPE == null) //兼容旧系统数据，PERSON_TYPE为空
        //                                                                        //&& person.LAB_ID != null //科室不为空
        //                                                                        //&& person.HIS_ID != null  //工号不为空
        //           && (person.HIS_ID == null || (person.HIS_ID != "0000" && person.HIS_ID != "00000"))   //排除0000工号
        //                                                                                                 //&& u.MANAGE_CLASS != null && u.MANAGE_CLASS == "4") //非机构级、分组级管理账号)
        //           && (person.PERSON_DOC_STATE == "1" || person.PERSON_DOC_STATE == "4")
        //           && (new List<string> { "1", "2", "3", "4", "5", "6", "7" }.Contains(person.USER_TYPE));
        //    }
        //}

        /// <summary>
        /// 获取人员结构信息
        /// </summary>
        /// <param name="hospital_id"></param>
        /// <returns></returns>
        public ResultDto GetPersonStructureInfo_old(string hospital_id, string lab_id, string area_id, string user_id)
        {
            //权限校验
            //_IAuthorityService.CheckUserMenuPermission(_soa, user_id, "H8102", hospital_id, lab_id, isThrowException: true);
            _IAuthorityService2.CheckUserMenuPermission(_soa, "H81", "H8102", isThrowException: true);
            List<SYS6_BASE_DATA> lis5_base_data = _IBaseDataServices.GetSys6BaseData();
            var pGroups = _soa.Db.Queryable<SYS6_INSPECTION_PGROUP>().Where(p => p.HOSPITAL_ID == hospital_id && p.LAB_ID == lab_id && p.PGROUP_STATE == "1" && p.PGROUP_CLASS != "3" && p.MGROUP_ID != null)//人员统计排除临床专业组  24-4-21排除没维护管理专业组的
                .WhereIF(area_id.IsNotNullOrEmpty(), p => p.AREA_ID == area_id)
                .Select(s => new
                {
                    PGROUP_ID = s.PGROUP_ID,
                    PGROUP_NAME = s.PGROUP_NAME,
                    MGROUP_ID = s.MGROUP_ID
                }).ToList();
            List<string> mGroupsIDs = pGroups.Select(o => o.MGROUP_ID).ToList();
            var mGroups = _soa.Db.Queryable<SYS6_INSPECTION_MGROUP>().Where(p => p.MGROUP_STATE == "1" && mGroupsIDs.Contains(p.MGROUP_ID)) //管理专业组可以跨院区  24-4-21增加科室条件过滤
                .Select(s => new
                {
                    MGROUP_ID = s.MGROUP_ID,
                    MGROUP_NAME = s.MGROUP_NAME
                }).ToList();
            List<string> pGroupsIDs = pGroups.Select(o => o.PGROUP_ID).ToList();
            //var users = _soa.Db.Queryable<SYS6_USER>().Where(u => u.STATE_FLAG == "1" && pGroupsIDs.Contains(u.DEPT_CODE))
            //            .Select<SYS6_USER>().ToList();
            //List<PMS_PERSON_INFO> pms_person_info = _soa.Db.Queryable<PMS_PERSON_INFO>().Where(p => p.HOSPITAL_ID == hospital_id && pGroupsIDs.Contains(p.PGROUP_ID)).ToList();

            //连接用户表进行查询，避免人员信息表同步问题导致的数据不正确
            List<PMS_PERSON_INFO> pms_person_info = _soa.Db.Queryable<SYS6_USER>().InnerJoin<PMS_PERSON_INFO>((u, p) => u.USER_NO == p.USER_ID)
                .Where((u, p) => u.STATE_FLAG == "1" && u.HOSPITAL_ID == hospital_id && pGroupsIDs.Contains(u.DEPT_CODE)
                  && u.LAB_ID != null && u.HIS_ID != null  //科室、工号都不为空
                  && u.HIS_ID != "0000" && u.HIS_ID != "00000"   //排除0000工号
                  && u.MANAGE_CLASS != null && u.MANAGE_CLASS == "4") //非机构级、分组级管理账号)
                .Select((u, p) => p).ToList().Distinct().ToList();

            //为保证统计数据一致性，使分量数据之和等于总数据，统计前作以下限定
            pms_person_info = pms_person_info.Where(p => (p.PERSON_DOC_STATE == "1" || p.PERSON_DOC_STATE == "4") && (new List<string> { "1", "2", "3", "4", "5", "6", "7" }.Contains(p.USER_TYPE))).ToList();

            ////以HisID去重
            //pms_person_info = pms_person_info.GroupBy(g => g.HIS_ID).Select(g => g.OrderBy(year => year.FIRST_RTIME).FirstOrDefault()).ToList();

            #region 以下为旧统计逻辑，后续要替换成接着的新逻辑

            string TraineeNum = pms_person_info.Where(p => p.PERSON_DOC_STATE == "1" && p.USER_TYPE == "5").ToList().Count().ToString();//进修人数
            string PracticeNum = pms_person_info.Where(p => p.PERSON_DOC_STATE == "1" && p.USER_TYPE == "4").ToList().Count().ToString();//实习人数
            string TrainingNum = pms_person_info.Where(p => p.PERSON_DOC_STATE == "1" && p.USER_TYPE == "6").ToList().Count().ToString();//规培人数
            string OfficialNum = pms_person_info.Where(p => p.PERSON_DOC_STATE == "1" && p.USER_TYPE == "1").ToList().Count().ToString();//正式员工
            string ContractNum = pms_person_info.Where(p => p.PERSON_DOC_STATE == "1" && p.USER_TYPE == "2").ToList().Count().ToString();//合同职工
            string RehiringNum = pms_person_info.Where(p => p.PERSON_DOC_STATE == "1" && p.USER_TYPE == "3").ToList().Count().ToString();//返聘职工
            string TemporaryNum = pms_person_info.Where(p => p.PERSON_DOC_STATE == "1" && p.USER_TYPE == "7").ToList().Count().ToString();//临聘人数
            string RetirementNum = pms_person_info.Where(p => p.PERSON_DOC_STATE == "4").ToList().Count().ToString();//退休职工

            string DepartmentNum = pms_person_info.Where(p => p.PERSON_DOC_STATE == "1" && new List<string> { "1", "2", "7" }.Contains(p.USER_TYPE)).Count().ToString();//科室总数: 1.正式员工 + 2.合同职工 + 7.临聘人数

            string ManNum = pms_person_info.Where(p => p.PERSON_DOC_STATE == "1" && p.SEX == "1").ToList().Count().ToString();//男
            string WomanNum = pms_person_info.Where(p => p.PERSON_DOC_STATE == "1" && p.SEX == "2").ToList().Count().ToString();//女
            string SexUnsetNum = pms_person_info.Where(p => p.PERSON_DOC_STATE == "1" && p.SEX.IsNullOrEmpty()).ToList().Count().ToString();//未维护
            string TechnicianNum = pms_person_info.Where(p => p.PERSON_DOC_STATE == "1" && p.TECHNOLOGY_TYPE == "1").ToList().Count().ToString();//技师
            string PhysicianNum = pms_person_info.Where(p => p.PERSON_DOC_STATE == "1" && p.TECHNOLOGY_TYPE == "2").ToList().Count().ToString();//医师
            string NurseNum = pms_person_info.Where(p => p.PERSON_DOC_STATE == "1" && p.TECHNOLOGY_TYPE == "3").ToList().Count().ToString();//护士
            string TechUnsetNum = pms_person_info.Where(p => p.PERSON_DOC_STATE == "1" && p.TECHNOLOGY_TYPE.IsNullOrEmpty()).ToList().Count().ToString();//未维护
                                                                                                                                                         //检验专业组
            List<object> pgrouplist = new List<object>();
            foreach (var pgroup in pGroups)
            {
                var userList = pms_person_info.Where(u => u.PGROUP_ID == pgroup.PGROUP_ID);
                string pgroupname = pgroup.PGROUP_NAME;
                string pgroupnum = userList.Count().ToString();
                var pGroupItem = new { PGROUPNUM = pgroupnum, PGROUPNAME = pgroupname };
                pgrouplist.Add(pGroupItem);
            }
            //管理专业组
            List<object> mgrouplist = new List<object>();
            foreach (var mgroup in mGroups)
            {
                var underPGroups = pGroups.Where(p => p.MGROUP_ID == mgroup.MGROUP_ID).ToList();
                var userList = pms_person_info.Where(u => underPGroups.Select(g => g.PGROUP_ID).Contains(u.PGROUP_ID));
                string mgroupname = mgroup.MGROUP_NAME;
                string mgroupnum = userList.Count().ToString();
                var mGroupItem = new { MGROUPNUM = mgroupnum, MGROUPNAME = mgroupname };
                mgrouplist.Add(mGroupItem);
            }

            //职称
            List<SYS6_BASE_DATA> lis5basedata = lis5_base_data.Where(x => x.CLASS_ID == "职称级别").OrderBy(p => p.DATA_SORT).ToList();
            List<object> techpostlist = new List<object>();
            foreach (var job in lis5basedata)
            {
                string techpostname = job.DATA_CNAME;
                string techpostnum = pms_person_info.Where(p => p.TECH_POST == job.DATA_ID).ToList().Count().ToString();
                var TECHPOSTS = new { TECHPOSTNUM = techpostnum, TECHPOSTNAME = techpostname };
                techpostlist.Add(TECHPOSTS);
            }
            //年龄
            List<object> agelist = new List<object>();
            for (int i = 0; i < 5; i++)
            {
                string agenum = string.Empty;
                string agename = string.Empty;
                if (i == 0)
                {
                    agename = "20-30岁";
                    agenum = pms_person_info.Where(p => p.PERSON_DOC_STATE == "1" && GetAge(p) != null && GetAge(p) >= 20 && GetAge(p) <= 30).ToList().Count().ToString();
                }
                if (i == 1)
                {
                    agename = "30-40岁";
                    agenum = pms_person_info.Where(p => p.PERSON_DOC_STATE == "1" && GetAge(p) != null && GetAge(p) > 30 && GetAge(p) <= 40).ToList().Count().ToString();
                }
                if (i == 2)
                {
                    agename = "40-50岁";
                    agenum = pms_person_info.Where(p => p.PERSON_DOC_STATE == "1" && GetAge(p) != null && GetAge(p) > 40 && GetAge(p) <= 50).ToList().Count().ToString();
                }
                if (i == 3)
                {
                    agename = "50-60岁";
                    agenum = pms_person_info.Where(p => p.PERSON_DOC_STATE == "1" && GetAge(p) != null && GetAge(p) > 50 && GetAge(p) <= 60).ToList().Count().ToString();
                }
                if (i == 4)
                {
                    agename = "60岁以上";
                    agenum = pms_person_info.Where(p => p.PERSON_DOC_STATE == "1" && GetAge(p) != null && GetAge(p) > 60).ToList().Count().ToString();
                }
                var AGES = new { AGENUM = agenum, AGENAME = agename };
                agelist.Add(AGES);
            }
            //学历
            List<SYS6_BASE_DATA> lis5basedatah = lis5_base_data.Where(x => x.CLASS_ID == "最高学历").OrderBy(item => item.DATA_SORT).ToList();

            List<object> highestdegreelist = new List<object>();
            foreach (var job in lis5basedatah)
            {
                string highestdegreename = job.DATA_CNAME;
                string highestdegreenum = pms_person_info.Where(p => p.PERSON_DOC_STATE == "1" && p.HIGHEST_DEGREE == job.DATA_ID).ToList().Count().ToString();
                var HIGHESTDEGREE = new { HIGHESTDEGREENUM = highestdegreenum, HIGHESTDEGREENAME = highestdegreename };
                highestdegreelist.Add(HIGHESTDEGREE);
            }
            var result = new
            {
                TRAINEENUM = TraineeNum,
                PRACTICENUM = PracticeNum,
                TRAININGNUM = TrainingNum,
                DEPARTMENTNUM = DepartmentNum,
                OFFICIALNUM = OfficialNum,
                CONTRACTNUM = ContractNum,
                REHIRINGNUM = RehiringNum,
                RETIREMENTNUM = RetirementNum,
                TEMPORARYNUM = TemporaryNum,
                MANNUM = ManNum,
                WOMANNUM = WomanNum,
                SEXUNSETNUM = SexUnsetNum,
                TECHNICIANNUM = TechnicianNum,
                PHYSICIANNUM = PhysicianNum,
                NURSERNUM = NurseNum,
                TECHUNSETNUM = TechUnsetNum,
                PGROUPLIST = pgrouplist,
                MGROUPLIST = mgrouplist,
                TECHPOSTLIST = techpostlist,
                AGELIST = agelist,
                HIGHESTDEGREELIST = highestdegreelist
            };
            return new ResultDto { data = result };
        }

        #endregion
        /// <summary>
        /// 获取人员结构信息
        /// </summary>
        /// <param name="hospital_id"></param>
        /// <returns></returns>
        public ResultDto GetPersonStructureInfo(string hospital_id, string lab_id, string area_id, string smblLabId, string user_id)
        {

            #region 以下为旧统计逻辑，后续要替换成接着的新逻辑
            ////权限校验
            //_authorityService.CheckUserMenuPermission(_soa, user_id, "H8102", hospital_id, lab_id, isThrowException: true);
            //List<SYS6_BASE_DATA> lis5_base_data = _IBaseDataServices.GetSys6BaseData();
            //var pGroups = _soa.Db.Queryable<SYS6_INSPECTION_PGROUP>().Where(p => p.HOSPITAL_ID == hospital_id && p.LAB_ID == lab_id && p.PGROUP_STATE == "1" && p.PGROUP_CLASS != "3" && p.MGROUP_ID != null)//人员统计排除临床专业组  24-4-21排除没维护管理专业组的
            //    .WhereIF(area_id.IsNotNullOrEmpty(), p => p.AREA_ID == area_id)
            //    .Select(s => new
            //    {
            //        PGROUP_ID = s.PGROUP_ID,
            //        PGROUP_NAME = s.PGROUP_NAME,
            //        MGROUP_ID = s.MGROUP_ID
            //    }).ToList();
            //List<string> mGroupsIDs = pGroups.Select(o => o.MGROUP_ID).ToList();
            //var mGroups = _soa.Db.Queryable<SYS6_INSPECTION_MGROUP>().Where(p => p.MGROUP_STATE == "1" && mGroupsIDs.Contains(p.MGROUP_ID)) //管理专业组可以跨院区  24-4-21增加科室条件过滤
            //    .Select(s => new
            //    {
            //        MGROUP_ID = s.MGROUP_ID,
            //        MGROUP_NAME = s.MGROUP_NAME
            //    }).ToList();
            //List<string> pGroupsIDs = pGroups.Select(o => o.PGROUP_ID).ToList();
            ////var users = _soa.Db.Queryable<SYS6_USER>().Where(u => u.STATE_FLAG == "1" && pGroupsIDs.Contains(u.DEPT_CODE))
            ////            .Select<SYS6_USER>().ToList();
            ////List<PMS_PERSON_INFO> department_person_infos = _soa.Db.Queryable<PMS_PERSON_INFO>().Where(p => p.HOSPITAL_ID == hospital_id && pGroupsIDs.Contains(p.PGROUP_ID)).ToList();

            ////连接用户表进行查询，避免人员信息表同步问题导致的数据不正确
            //List<PMS_PERSON_INFO> pms_person_info = _soa.Db.Queryable<SYS6_USER>().InnerJoin<PMS_PERSON_INFO>((u, p) => u.USER_NO == p.USER_ID)
            //    .Where((u, p) => u.STATE_FLAG == "1" && u.HOSPITAL_ID == hospital_id && pGroupsIDs.Contains(u.DEPT_CODE)
            //      && u.LAB_ID != null && u.HIS_ID != null  //科室、工号都不为空
            //      && u.HIS_ID != "0000" && u.HIS_ID != "00000"   //排除0000工号
            //      && u.MANAGE_CLASS != null && u.MANAGE_CLASS == "4") //非机构级、分组级管理账号
            //    .Select((u, p) => p).ToList().Distinct().ToList();

            ////为保证统计数据一致性，使分量数据之和等于总数据，统计前作以下限定
            //pms_person_info = pms_person_info.Where(p => (p.PERSON_DOC_STATE == "1" || p.PERSON_DOC_STATE == "4") && (new List<string> { "1", "2", "3", "4", "5", "6", "7" }.Contains(p.USER_TYPE))).ToList();

            ////以HisID去重
            //pms_person_info = pms_person_info.GroupBy(g => g.HIS_ID).Select(g => g.OrderBy(year => year.FIRST_RTIME).FirstOrDefault()).ToList();


            //    string TraineeNum = department_person_infos.Where(p => p.PERSON_DOC_STATE == "1" & & p.USER_TYPE == "5").ToList().Count().ToString();//进修人数
            //    string PracticeNum = department_person_infos.Where(p => /* p.PERSON_DOC_STATE == "1" && */ p.USER_TYPE == "4").ToList().Count().ToString();//实习人数
            //    string TrainingNum = department_person_infos.Where(p => /* p.PERSON_DOC_STATE == "1" && */ p.USER_TYPE == "6").ToList().Count().ToString();//规培人数
            //    string OfficialNum = department_person_infos.Where(p => /* p.PERSON_DOC_STATE == "1" && */ p.USER_TYPE == "1").ToList().Count().ToString();//正式员工
            //    string ContractNum = department_person_infos.Where(p => /* p.PERSON_DOC_STATE == "1" && */ p.USER_TYPE == "2").ToList().Count().ToString();//合同职工
            //    string RehiringNum = department_person_infos.Where(p => /* p.PERSON_DOC_STATE == "1" && */ p.USER_TYPE == "3").ToList().Count().ToString();//返聘职工
            //    string TemporaryNum = department_person_infos.Where(p => /* p.PERSON_DOC_STATE == "1" && */ p.USER_TYPE == "7").ToList().Count().ToString();//临聘人数
            //    string RetirementNum = department_person_infos.Where(p => p.PERSON_DOC_STATE == "4").ToList().Count().ToString();//退休职工

            //    string DepartmentNum = department_person_infos.Where(p => /* p.PERSON_DOC_STATE == "1" && */ new List<string> { "1", "2", "7" }.Contains(p.USER_TYPE)).Count().ToString();//科室总数: 1.正式员工 + 2.合同职工 + 7.临聘人数

            //    string ManNum = department_person_infos.Where(p => /* p.PERSON_DOC_STATE == "1" && */ p.SEX == "1").ToList().Count().ToString();//男
            //    string WomanNum = department_person_infos.Where(p => /* p.PERSON_DOC_STATE == "1" && */ p.SEX == "2").ToList().Count().ToString();//女
            //    string SexUnsetNum = department_person_infos.Where(p => /* p.PERSON_DOC_STATE == "1" && */ p.SEX.IsNullOrEmpty()).ToList().Count().ToString();//未维护
            //    string TechnicianNum = department_person_infos.Where(p => /* p.PERSON_DOC_STATE == "1" && */ p.TECHNOLOGY_TYPE == "1").ToList().Count().ToString();//技师
            //    string PhysicianNum = department_person_infos.Where(p => /* p.PERSON_DOC_STATE == "1" && */ p.TECHNOLOGY_TYPE == "2").ToList().Count().ToString();//医师
            //    string NurseNum = department_person_infos.Where(p => /* p.PERSON_DOC_STATE == "1" && */ p.TECHNOLOGY_TYPE == "3").ToList().Count().ToString();//护士
            //    string TechUnsetNum = department_person_infos.Where(p => /* p.PERSON_DOC_STATE == "1" && */ p.TECHNOLOGY_TYPE.IsNullOrEmpty()).ToList().Count().ToString();//未维护
            //    //检验专业组
            //    List<object> pgrouplist = new List<object>();
            //    foreach (var pgroup in pGroups)
            //    {
            //        var userList = department_person_infos.Where(u => u.PGROUP_ID == pgroup.PGROUP_ID);
            //        string pgroupname = pgroup.PGROUP_NAME;
            //        string pgroupnum = userList.Count().ToString();
            //        var pGroupItem = new { PGROUPNUM = pgroupnum, PGROUPNAME = pgroupname };
            //        pgrouplist.Add(pGroupItem);
            //    }
            //    //管理专业组
            //    List<object> mgrouplist = new List<object>();
            //    foreach (var mgroup in mGroups)
            //    {
            //        var underPGroups = pGroups.Where(p => p.MGROUP_ID == mgroup.MGROUP_ID).ToList();
            //        var userList = department_person_infos.Where(u => underPGroups.Select(g => g.PGROUP_ID).Contains(u.PGROUP_ID));
            //        string mgroupname = mgroup.MGROUP_NAME;
            //        string mgroupnum = userList.Count().ToString();
            //        var mGroupItem = new { MGROUPNUM = mgroupnum, MGROUPNAME = mgroupname };
            //        mgrouplist.Add(mGroupItem);
            //    }

            //    //职称
            //    List<SYS6_BASE_DATA> lis5basedata = lis5_base_data.Where(x => x.CLASS_ID == "职称级别").OrderBy(p => p.DATA_SORT).ToList();
            //    List<object> techpostlist = new List<object>();
            //    foreach (var high in lis5basedata)
            //    {
            //        string techpostname = high.DATA_CNAME;
            //        string techpostnum = department_person_infos.Where(p => p.TECH_POST == high.DATA_ID).ToList().Count().ToString();
            //        var TECHPOSTS = new { TECHPOSTNUM = techpostnum, TECHPOSTNAME = techpostname };
            //        techpostlist.Add(TECHPOSTS);
            //    }
            //    //年龄
            //    List<object> agelist = new List<object>();
            //    for (int i = 0; i < 5; i++)
            //    {
            //        string agenum = string.Empty;
            //        string agename = string.Empty;
            //        if (i == 0)
            //        {
            //            agename = "20-30岁";
            //            agenum = department_person_infos.Where(p => /* p.PERSON_DOC_STATE == "1" && */ GetAge(p) != null && GetAge(p) >= 20 && GetAge(p) <= 30).ToList().Count().ToString();
            //        }
            //        if (i == 1)
            //        {
            //            agename = "30-40岁";
            //            agenum = department_person_infos.Where(p => /* p.PERSON_DOC_STATE == "1" && */ GetAge(p) != null && GetAge(p) > 30 && GetAge(p) <= 40).ToList().Count().ToString();
            //        }
            //        if (i == 2)
            //        {
            //            agename = "40-50岁";
            //            agenum = department_person_infos.Where(p => /* p.PERSON_DOC_STATE == "1" && */ GetAge(p) != null && GetAge(p) > 40 && GetAge(p) <= 50).ToList().Count().ToString();
            //        }
            //        if (i == 3)
            //        {
            //            agename = "50-60岁";
            //            agenum = department_person_infos.Where(p => /* p.PERSON_DOC_STATE == "1" && */ GetAge(p) != null && GetAge(p) > 50 && GetAge(p) <= 60).ToList().Count().ToString();
            //        }
            //        if (i == 4)
            //        {
            //            agename = "60岁以上";
            //            agenum = department_person_infos.Where(p => /* p.PERSON_DOC_STATE == "1" && */ GetAge(p) != null && GetAge(p) > 60).ToList().Count().ToString();
            //        }
            //        var AGES = new { AGENUM = agenum, AGENAME = agename };
            //        agelist.Add(AGES);
            //    }
            //    //学历
            //    List<SYS6_BASE_DATA> lis5basedatah = lis5_base_data.Where(x => x.CLASS_ID == "最高学历").OrderBy(trainBDitem => trainBDitem.DATA_SORT).ToList();

            //    List<object> DegreeList = new List<object>();
            //    foreach (var high in lis5basedatah)
            //    {
            //        string highestdegreename = high.DATA_CNAME;
            //        string highestdegreenum = department_person_infos.Where(p => /* p.PERSON_DOC_STATE == "1" && */ p.HIGHEST_DEGREE == high.DATA_ID).ToList().Count().ToString();
            //        var HIGHESTDEGREE = new { HIGHESTDEGREENUM = highestdegreenum, HIGHESTDEGREENAME = highestdegreename };
            //        DegreeList.Add(HIGHESTDEGREE);
            //    }
            //    var resultDict = new
            //    {
            //        TRAINEENUM = TraineeNum,
            //        PRACTICENUM = PracticeNum,
            //        TRAININGNUM = TrainingNum,
            //        DEPARTMENTNUM = DepartmentNum,
            //        OFFICIALNUM = OfficialNum,
            //        CONTRACTNUM = ContractNum,
            //        REHIRINGNUM = RehiringNum,
            //        RETIREMENTNUM = RetirementNum,
            //        TEMPORARYNUM = TemporaryNum,
            //        MANNUM = ManNum,
            //        WOMANNUM = WomanNum,
            //        SEXUNSETNUM = SexUnsetNum,
            //        TECHNICIANNUM = TechnicianNum,
            //        PHYSICIANNUM = PhysicianNum,
            //        NURSERNUM = NurseNum,
            //        TECHUNSETNUM = TechUnsetNum,
            //        PGROUPLIST = pgrouplist,
            //        MGROUPLIST = mgrouplist,
            //        TECHPOSTLIST = techpostlist,
            //        AGELIST = agelist,
            //        HIGHESTDEGREELIST = DegreeList
            //    };
            //    return new ResultDto { data = resultDict };
            //}

            #endregion

            #region 以下为新统计逻辑，#6.24.4启用
            //权限校验
            _IAuthorityService2.CheckUserMenuPermission(_soa, "H81", "H8102",/* hospital_id, lab_id,*/ isThrowException: true);
            var orgParm = new OrgUserParams { hospital_id = hospital_id, lab_id = lab_id, area_id = area_id, smbl_lab_id = smblLabId, user_id = user_id };
            List<SYS6_BASE_DATA> lis5_base_data = _IBaseDataServices.GetSys6BaseData();
            //人员列表源数据
            List<PMS_PERSON_INFO> pms_person_info = null;
            //科室人员列表
            List<PMS_PERSON_INFO> department_person_infos = null;
            //一人多职称时，分作两人，用来统计职称
            List<PMS_PERSON_INFO> manyTechTypePersons = null;
            //检验专业组
            List<SYS6_INSPECTION_PGROUP> pGroups = null;
            List<PgroupInfo> pgrouplist = new List<PgroupInfo>();
            //管理专业组
            List<object> mgrouplist = new List<object>();
            //备案实验室
            List<object> smbllablist = new List<object>();
            //人事档案类型填写分布
            List<object> pmsclasslist = new List<object>();

            if (_httpContext.GetSmblFlag() == "1")
            {
                HandlePersonList_SMBL();//走生安逻辑
            }
            else
            {
                HandlePersonList_ISO();//走ISO逻辑
            }
            //走ISO逻辑
            void HandlePersonList_ISO()
            {
                //人员列表源数据
                //pms_person_info = _IModuleLabGroupService.GetRangePersonList(orgParm, null, "H8102", out var sysuserList, out var areaGroupTree, out var groupLine);

                //2025-6-10日注销代码：原判断逻辑带有权限，现去掉权限判断
                //var personRange = _IModuleLabGroupService.GetPgroupRangePersonList(orgParm, null, "H8102");
                //pms_person_info = personRange.PersonList;


                var personRange = _IModuleLabGroupService.GetPgroupRangePersonList(new OrgUserParams { hospital_id = hospital_id, lab_id = lab_id, area_id = area_id, smbl_lab_id = smblLabId }, null, "H81", ifCheckPremission: false);
                pms_person_info = personRange.PersonList;
             
                List<string> smblPgroupIds = null;
                if (smblLabId.IsNotNullOrEmpty())
                {
                    smblPgroupIds = _soa.Db.Queryable<SMBL_LAB>()
                        .Where(a => a.SMBL_LAB_STATE == "1" && a.PGROUP_SID != null)
                        .Select(a => a.PGROUP_SID)
                        .ToList()
                        .Where(a => a.IsNotNullOrEmpty())
                        .SelectMany(a => a?.Split(',') ?? Array.Empty<string>())
                        .Where(a => a != null)
                        .Distinct()
                        .ToList();
                    pms_person_info = pms_person_info.FindAll(a => smblPgroupIds.Contains(a.PGROUP_ID));
                }
                //科室人员列表
                department_person_infos = pms_person_info.Where(p => /* p.PERSON_DOC_STATE == "1" && */ p.PERSON_TYPE == "1" && new List<string> { "1", "2", "7" }.Contains(p.USER_TYPE)).ToList();  //新人岗权不用PERSON_DOC_STATE  //USER_TYPE为1正式、2合同、7临聘、12退休

                //一人多职称时，分作两人，用来统计职称
                manyTechTypePersons = GetManyTechTypePersons(department_person_infos);

                //2025-6-10日注销代码：原判断逻辑带有权限，现去掉权限判断
                //pGroups = personRange.Tree.GetAllNodes(s => s.NODE_TYPE == GroupTreeNodeTypeEnum.PGROUP.ToIntStr())
                //    .WhereIF(smblPgroupIds != null, s => smblPgroupIds.Contains(s.SOURCE_ID))
                //    .Select(s => s.SOURCE as SYS6_INSPECTION_PGROUP)
                //    .ToList();

                pGroups = _soa.Db.Queryable<SYS6_INSPECTION_PGROUP>().Where(pgroup => personRange.PgroupIds.Contains(pgroup.PGROUP_ID))
                    .WhereIF(smblPgroupIds != null, pgroup => smblPgroupIds.Contains(pgroup.PGROUP_ID)).ToList();

                List<string> mGroupsIDs = pGroups.Select(o => o.MGROUP_ID).ToList();
                var mGroups = _soa.Db.Queryable<SYS6_INSPECTION_MGROUP>().Where(p => p.MGROUP_STATE == "1" && mGroupsIDs.Contains(p.MGROUP_ID)) //管理专业组可以跨院区  24-4-21增加科室条件过滤
                    .Select(s => new
                    {
                        MGROUP_ID = s.MGROUP_ID,
                        MGROUP_NAME = s.MGROUP_NAME
                    }).ToList();


                //检验专业组
                foreach (var pgroup in pGroups)
                {
                    var userList = department_person_infos.Where(u => u.PGROUP_ID == pgroup.PGROUP_ID);
                    string pgroupname = pgroup.PGROUP_NAME;
                    int pgroupnum = userList.Count();
                    string mgroupname = mGroups.Find(a => a.MGROUP_ID == pgroup.MGROUP_ID)?.MGROUP_NAME ?? "";
                    var pGroupItem = new PgroupInfo { PGROUPNUM = pgroupnum.ToString(), PGROUPNAME = pgroupname, MGROUPNAME = mgroupname };
                    if (pgroupnum > 0)
                        pgrouplist.Add(pGroupItem);
                }
                //按拥有检验专业组的数量排序
                pgrouplist = pgrouplist.GroupBy(a => a.MGROUPNAME).OrderByDescending(g => g.Count()).SelectMany(g => g).ToList();
                //管理专业组
                foreach (var mgroup in mGroups)
                {
                    var underPGroups = pGroups.Where(p => p.MGROUP_ID == mgroup.MGROUP_ID).ToList();
                    var userList = department_person_infos.Where(u => underPGroups.Select(g => g.PGROUP_ID).Contains(u.PGROUP_ID));
                    string mgroupname = mgroup.MGROUP_NAME;
                    int mgroupnum = userList.Count();
                    var mGroupItem = new { MGROUPNUM = mgroupnum.ToString(), MGROUPNAME = mgroupname };
                    if (mgroupnum > 0)
                        mgrouplist.Add(mGroupItem);
                }
            }
            //走生安逻辑
            void HandlePersonList_SMBL()
            {
                var tree = GetSMBLPersonTree_Lab(orgParm, null, "H8102", false, false).FirstOrDefault() ?? new OrgTreeNode();
                //备案实验室树在科室层级会带出更多专业组，所以要缩小范围
                if (smblLabId.IsNotNullOrEmpty())
                    tree = tree.GetAllNodes(a => a.NODE_TYPE == GroupTreeNodeTypeEnum.SMBL_LAB.ToIntStr() && a.SOURCE_ID == smblLabId).FirstOrDefault()
                        ?? throw new BizException("找不到该备案实验室！");

                //人员列表源数据
                pms_person_info = tree.GetAllNodesByType(PMSTreeNodeTypeEnum.PERSON.ToIntStr())
                    .Select(a => a.SOURCE as PMS_PERSON_INFO)
                    .Where(a => a != null)
                    .DistinctBy(a => a.PERSON_ID)
                    .ToList();

                //科室人员列表
                department_person_infos = pms_person_info.Where(p => /* p.PERSON_DOC_STATE == "1" && */ new List<string> { "1", "2", "7", "12" }.Contains(p.USER_TYPE)).ToList();  //PERSON_DOC_STATE新人岗权不用 //USER_TYPE为1正式、2合同、7临聘、12退休
                //一人多职称时，分作两人，用来统计职称
                manyTechTypePersons = GetManyTechTypePersons(department_person_infos);

                pGroups = tree.GetAllNodesByType(GroupTreeNodeTypeEnum.PGROUP.ToIntStr())
                    .Select(s => s.SOURCE as SYS6_INSPECTION_PGROUP)
                    .DistinctBy(s => s.PGROUP_ID)
                    .ToList();

                List<string> mGroupsIDs = pGroups.Select(o => o.MGROUP_ID).ToList();
                var mGroups = _soa.Db.Queryable<SYS6_INSPECTION_MGROUP>().Where(p => p.MGROUP_STATE == "1" && mGroupsIDs.Contains(p.MGROUP_ID)) //管理专业组可以跨院区  24-4-21增加科室条件过滤
                    .Select(s => new
                    {
                        MGROUP_ID = s.MGROUP_ID,
                        MGROUP_NAME = s.MGROUP_NAME
                    }).ToList();

                //检验专业组
                foreach (var pgroup in pGroups)
                {
                    var userList = department_person_infos.Where(u => u.PGROUP_ID == pgroup.PGROUP_ID);
                    string pgroupname = pgroup.PGROUP_NAME;
                    int pgroupnum = userList.Count();
                    string mgroupname = mGroups.Find(a => a.MGROUP_ID == pgroup.MGROUP_ID)?.MGROUP_NAME ?? "";
                    var pGroupItem = new PgroupInfo { PGROUPNUM = pgroupnum.ToString(), PGROUPNAME = pgroupname, MGROUPNAME = mgroupname };
                    if (pgroupnum > 0)
                        pgrouplist.Add(pGroupItem);
                }
                //按拥有检验专业组的数量排序
                pgrouplist = pgrouplist.GroupBy(a => a.MGROUPNAME).OrderByDescending(g => g.Count()).SelectMany(g => g).ToList();
                //管理专业组
                foreach (var mgroup in mGroups)
                {
                    var underPGroups = pGroups.Where(p => p.MGROUP_ID == mgroup.MGROUP_ID).ToList();
                    var userList = department_person_infos.Where(u => underPGroups.Select(g => g.PGROUP_ID).Contains(u.PGROUP_ID));
                    string mgroupname = mgroup.MGROUP_NAME;
                    int mgroupnum = userList.Count();
                    var mGroupItem = new { MGROUPNUM = mgroupnum.ToString(), MGROUPNAME = mgroupname };
                    if (mgroupnum > 0)
                        mgrouplist.Add(mGroupItem);
                }

                //备案实验室
                foreach (var smblLabNode in tree.GetAllNodesByType(GroupTreeNodeTypeEnum.SMBL_LAB.ToIntStr()))
                {
                    var smblLabItem = new { SMBLLABNUM = smblLabNode.NUM.ToString(), SMBLLABNAME = smblLabNode.NAME };
                    if (smblLabNode.NUM > 0)
                        smbllablist.Add(smblLabItem);
                }

                //计算档案记录分布
                HandlePmsClassData(department_person_infos);
            }
            //生成档案记录分布数据
            void HandlePmsClassData(List<PMS_PERSON_INFO> personList)
            {
                var addClassInfos = _IModuleLabGroupService.GetPmsAddnClassInfo();
                var personIds = personList.Select(a => a.PERSON_ID).ToList();
                bool smblFlag = _httpContext.GetSmblFlag() == "1";
                var result = new List<string>();

                //考试记录
                pmsclasslist.Add(new
                {
                    PMSCLASSNUM = _soa.Db.Queryable<EMP_EXAM_USER_ANSWER>().Where(a => a.SOURCE_TYPE == "1" && a.EXAM_USER_STATE != "10")//TODO：personId改回userId
                    .WhereIF(smblFlag, a => a.SMBL_FLAG != null && a.SMBL_FLAG == "1").Select(a => a.USER_ID).Distinct().ToList().Where(a => personIds.Contains(a)).Count().ToString(),
                    PMSCLASSNAME = addClassInfos.Find(a => a.CLASS_ID == "PMS_EXAM_LIST")?.CLASS_NAME
                });

                //评估记录
                pmsclasslist.Add(new
                {
                    PMSCLASSNUM = _soa.Db.Queryable<PMS_ASSESS_PLAN_PERSON>().Where(a => a.SOURCE_TYPE == "1" && a.EVALUATE_STATE != "4")//TODO：personId改回userId
                     .WhereIF(smblFlag, a => a.SMBL_FLAG != null && a.SMBL_FLAG == "1").Select(a => a.USER_ID).Distinct().ToList().Where(a => personIds.Contains(a)).Count().ToString(),
                    PMSCLASSNAME = addClassInfos.Find(a => a.CLASS_ID == "PMS_ASSESS_LIST")?.CLASS_NAME
                });

                //动态类型
                _soa.Db.Queryable<PMS_ADDN_RECORD>()
                .WhereIF(smblFlag, a => a.SMBL_FLAG != null && a.SMBL_FLAG == "1")
                .Select(a => new { a.CLASS_ID, a.PERSON_ID }).Distinct().ToList()
                .GroupBy(a => a.CLASS_ID)
                .ForEach(c =>
                {
                    var clas = addClassInfos.Find(a => a.CLASS_ID == c.Key);
                    if (clas != null && clas.CLASS_KIND == "0")
                        pmsclasslist.Add(new
                        {
                            PMSCLASSNUM = c.Where(a => personIds.Contains(a.PERSON_ID)).Count().ToString(),
                            PMSCLASSNAME = clas.CLASS_NAME
                        });
                });

                //固定分类
                pmsclasslist.Add(new
                {
                    PMSCLASSNUM = _soa.Db.Queryable<PMS_REWARD_LIST>().WhereIF(smblFlag, a => a.SMBL_FLAG != null && a.SMBL_FLAG == "1").Select(a => a.PERSON_ID).Distinct().ToList().Where(a => personIds.Contains(a)).Count().ToString(),
                    PMSCLASSNAME = addClassInfos.Find(a => a.CLASS_ID == "PMS_REWARD_LIST")?.CLASS_NAME
                });
                pmsclasslist.Add(new
                {
                    PMSCLASSNUM = _soa.Db.Queryable<PMS_TEACH_LIST>().WhereIF(smblFlag, a => a.SMBL_FLAG != null && a.SMBL_FLAG == "1").Select(a => a.PERSON_ID).Distinct().ToList().Where(a => personIds.Contains(a)).Count().ToString(),
                    PMSCLASSNAME = addClassInfos.Find(a => a.CLASS_ID == "PMS_TEACH_LIST")?.CLASS_NAME
                });
                pmsclasslist.Add(new
                {
                    PMSCLASSNUM = _soa.Db.Queryable<PMS_RESUME_LIST>().WhereIF(smblFlag, a => a.SMBL_FLAG != null && a.SMBL_FLAG == "1").Select(a => a.PERSON_ID).Distinct().ToList().Where(a => personIds.Contains(a)).Count().ToString(),
                    PMSCLASSNAME = addClassInfos.Find(a => a.CLASS_ID == "PMS_RESUME_LIST")?.CLASS_NAME
                });
                pmsclasslist.Add(new
                {
                    PMSCLASSNUM = _soa.Db.Queryable<PMS_STUDY_LIST>().WhereIF(smblFlag, a => a.SMBL_FLAG != null && a.SMBL_FLAG == "1").Select(a => a.PERSON_ID).Distinct().ToList().Where(a => personIds.Contains(a)).Count().ToString(),
                    PMSCLASSNAME = addClassInfos.Find(a => a.CLASS_ID == "PMS_STUDY_LIST")?.CLASS_NAME
                });
                pmsclasslist.Add(new
                {
                    PMSCLASSNUM = _soa.Db.Queryable<PMS_RESEARCH_LIST>().WhereIF(smblFlag, a => a.SMBL_FLAG != null && a.SMBL_FLAG == "1").Select(a => a.PERSON_ID).Distinct().ToList().Where(a => personIds.Contains(a)).Count().ToString(),
                    PMSCLASSNAME = addClassInfos.Find(a => a.CLASS_ID == "PMS_RESEARCH_LIST")?.CLASS_NAME
                });
                pmsclasslist.Add(new
                {
                    PMSCLASSNUM = _soa.Db.Queryable<PMS_THESIS_LIST>().WhereIF(smblFlag, a => a.SMBL_FLAG != null && a.SMBL_FLAG == "1").Select(a => a.PERSON_ID).Distinct().ToList().Where(a => personIds.Contains(a)).Count().ToString(),
                    PMSCLASSNAME = addClassInfos.Find(a => a.CLASS_ID == "PMS_THESIS_LIST")?.CLASS_NAME
                });
                pmsclasslist.Add(new
                {
                    PMSCLASSNUM = _soa.Db.Queryable<PMS_EDUCATION_LIST>().WhereIF(smblFlag, a => a.SMBL_FLAG != null && a.SMBL_FLAG == "1").Select(a => a.PERSON_ID).Distinct().ToList().Where(a => personIds.Contains(a)).Count().ToString(),
                    PMSCLASSNAME = addClassInfos.Find(a => a.CLASS_ID == "PMS_EDUCATION_LIST")?.CLASS_NAME
                });
                pmsclasslist.Add(new
                {
                    PMSCLASSNUM = _soa.Db.Queryable<PMS_SKILL_CERTIFICATE_LIST>().WhereIF(smblFlag, a => a.SMBL_FLAG != null && a.SMBL_FLAG == "1").Select(a => a.PERSON_ID).Distinct().ToList().Where(a => personIds.Contains(a)).Count().ToString(),
                    PMSCLASSNAME = addClassInfos.Find(a => a.CLASS_ID == "PMS_SKILL_CERTIFICATE_LIST")?.CLASS_NAME
                });
                pmsclasslist.Add(new
                {
                    PMSCLASSNUM = _soa.Db.Queryable<PMS_EXPATRIATE_LIST>().WhereIF(smblFlag, a => a.SMBL_FLAG != null && a.SMBL_FLAG == "1").Select(a => a.PERSON_ID).Distinct().ToList().Where(a => personIds.Contains(a)).Count().ToString(),
                    PMSCLASSNAME = addClassInfos.Find(a => a.CLASS_ID == "PMS_EXPATRIATE_LIST")?.CLASS_NAME
                });
                pmsclasslist.Add(new
                {
                    PMSCLASSNUM = _soa.Db.Queryable<PMS_EXCHANGE_LIST>().WhereIF(smblFlag, a => a.SMBL_FLAG != null && a.SMBL_FLAG == "1").Select(a => a.PERSON_ID).Distinct().ToList().Where(a => personIds.Contains(a)).Count().ToString(),
                    PMSCLASSNAME = addClassInfos.Find(a => a.CLASS_ID == "PMS_EXCHANGE_LIST")?.CLASS_NAME
                });
                pmsclasslist.Add(new
                {
                    PMSCLASSNUM = _soa.Db.Queryable<PMS_INTELLECTUAL_LIST>().WhereIF(smblFlag, a => a.SMBL_FLAG != null && a.SMBL_FLAG == "1").Select(a => a.PERSON_ID).Distinct().ToList().Where(a => personIds.Contains(a)).Count().ToString(),
                    PMSCLASSNAME = addClassInfos.Find(a => a.CLASS_ID == "PMS_INTELLECTUAL_LIST")?.CLASS_NAME
                });
                pmsclasslist.Add(new
                {
                    PMSCLASSNUM = _soa.Db.Queryable<PMS_SOCIAL_OFFICE_LIST>().WhereIF(smblFlag, a => a.SMBL_FLAG != null && a.SMBL_FLAG == "1").Select(a => a.PERSON_ID).Distinct().ToList().Where(a => personIds.Contains(a)).Count().ToString(),
                    PMSCLASSNAME = addClassInfos.Find(a => a.CLASS_ID == "PMS_SOCIAL_OFFICE_LIST")?.CLASS_NAME
                });
                pmsclasslist.Add(new
                {
                    PMSCLASSNUM = _soa.Db.Queryable<PMS_TRAIN_LIST>().WhereIF(smblFlag, a => a.SMBL_FLAG != null && a.SMBL_FLAG == "1").Select(a => a.PERSON_ID).Distinct().ToList().Where(a => personIds.Contains(a)).Count().ToString(),
                    PMSCLASSNAME = addClassInfos.Find(a => a.CLASS_ID == "PMS_TRAIN_LIST")?.CLASS_NAME
                });
                pmsclasslist.Add(new
                {
                    PMSCLASSNUM = _soa.Db.Queryable<PMS_PROFESSIONAL_LIST>().WhereIF(smblFlag, a => a.SMBL_FLAG != null && a.SMBL_FLAG == "1").Select(a => a.PERSON_ID).Distinct().ToList().Where(a => personIds.Contains(a)).Count().ToString(),
                    PMSCLASSNAME = addClassInfos.Find(a => a.CLASS_ID == "PMS_PROFESSIONAL_LIST")?.CLASS_NAME
                });
            }

            string DepartmentNum = department_person_infos.Where(p => /* p.PERSON_DOC_STATE == "1" && */ p.PERSON_TYPE == "1" && (p.USER_TYPE == "1" || p.USER_TYPE == "2" || p.USER_TYPE == "7")).Count().ToString();//科室总数: 1.正式员工 + 2.合同职工 + 7.临聘人数

            string OfficialNum = department_person_infos.Where(p => /* p.PERSON_DOC_STATE == "1" && */ p.PERSON_TYPE == "1" && p.USER_TYPE == "1").Count().ToString();//正式员工
            string ContractNum = department_person_infos.Where(p => /* p.PERSON_DOC_STATE == "1" && */ p.PERSON_TYPE == "1" && p.USER_TYPE == "2").Count().ToString();//合同职工
            string TemporaryNum = department_person_infos.Where(p => /* p.PERSON_DOC_STATE == "1" && */p.PERSON_TYPE == "1" && p.USER_TYPE == "7").Count().ToString();//临聘人数
            string TraineeNum = pms_person_info.Where(p => /* p.PERSON_DOC_STATE == "1" && */ p.PERSON_TYPE == "5"/* && p.USER_TYPE == "5"*/).Count().ToString();//进修人数
            string PracticeNum = pms_person_info.Where(p => /* p.PERSON_DOC_STATE == "1" && */ p.PERSON_TYPE == "4"/* && p.USER_TYPE == "4"*/).Count().ToString();//实习人数
            string TrainingNum = pms_person_info.Where(p => /* p.PERSON_DOC_STATE == "1" && */ p.PERSON_TYPE == "6" /*&& p.USER_TYPE == "6"*/).Count().ToString();//规培人数
            string RehiringNum = pms_person_info.Where(p => /* p.PERSON_DOC_STATE == "1" && */ p.USER_TYPE == "3").Count().ToString();//返聘职工
            string RetirementNum = pms_person_info.Where(p => p.PERSON_DOC_STATE == "4" && p.USER_TYPE == "12").Count().ToString();//退休职工

            string ManNum = department_person_infos.Where(p => /* p.PERSON_DOC_STATE == "1" && */ p.SEX == "1").Count().ToString();//男
            string WomanNum = department_person_infos.Where(p => /* p.PERSON_DOC_STATE == "1" && */ p.SEX == "2").Count().ToString();//女
            var SexUnsetPersons = department_person_infos.Where(p => /* p.PERSON_DOC_STATE == "1" && */ p.SEX.IsNullOrEmpty()).ToList();
            string SexUnsetNum = SexUnsetPersons.Count().ToString();//未维护

            string TechnicianNum = manyTechTypePersons.Where(p => /* p.PERSON_DOC_STATE == "1" && */ p.TECHNOLOGY_TYPE == "1").ToList().Count().ToString();//技师
            string PhysicianNum = manyTechTypePersons.Where(p => /* p.PERSON_DOC_STATE == "1" && */ p.TECHNOLOGY_TYPE == "2").ToList().Count().ToString();//医师
            string NurseNum = manyTechTypePersons.Where(p => /* p.PERSON_DOC_STATE == "1" && */ p.TECHNOLOGY_TYPE == "3").ToList().Count().ToString();//护士
            string ResearcherNum = manyTechTypePersons.Where(p => /* p.PERSON_DOC_STATE == "1" && */ p.TECHNOLOGY_TYPE == "4").ToList().Count().ToString();//研究员
            string DoubleTechNum = manyTechTypePersons.Where(p => /* p.PERSON_DOC_STATE == "1" && */ p.TECHNOLOGY_TYPE.IsNotNullOrEmpty()).GroupBy(p => p.PERSON_ID).Where(g => g.Count() > 1).Count().ToString();//双证
            List<PMS_PERSON_INFO> TechUnsetPersons = department_person_infos.Where(p => /* p.PERSON_DOC_STATE == "1" && */ p.TECHNOLOGY_TYPE.IsNullOrEmpty()).ToList();
            string TechUnsetNum = TechUnsetPersons.Count().ToString();//未维护

            //职称
            List<SYS6_BASE_DATA> techPostdata = lis5_base_data.Where(x => x.CLASS_ID == "职称级别").OrderByDescending(p => p.DATA_SORT).ToList();
            Dictionary<string, List<object>> allTechPostDict = new Dictionary<string, List<object>>();
            List<object> techpostlist = new List<object>();
            foreach (var post in techPostdata)
            {
                string techpostname = post.DATA_CNAME;
                string techpostnum = manyTechTypePersons.Where(p => p.TECH_POST == post.DATA_ID).ToList().Count().ToString();
                var TECHPOSTS = new { TECHPOSTNUM = techpostnum, TECHPOSTNAME = techpostname };
                techpostlist.Add(TECHPOSTS);
            }
            allTechPostDict["全部"] = techpostlist;
            //旧逻辑，只显示“初级、中级、副高、正高”
            //List<SYS6_BASE_DATA> techTypedata = lis5_base_data.Where(x => x.CLASS_ID == "职称类型" && x.DATA_ID != "5").OrderByDescending(p => p.DATA_SORT).ToList();
            //foreach (var techType in techTypedata)
            //{
            //    List<object> techpostlist2 = new List<object>();
            //    int count = 0;
            //    foreach (var post in techPostdata)
            //    {
            //        string techpostname = post.DATA_CNAME;
            //        int techpostCount = manyTechTypePersons.Where(p => p.TECHNOLOGY_TYPE == techType.DATA_ID && p.TECH_POST == post.DATA_ID).ToList().Count();
            //        string techpostnum = techpostCount.ToString();
            //        count += techpostCount;
            //        var TECHPOSTS = new { TECHPOSTNUM = techpostnum, TECHPOSTNAME = techpostname };
            //        techpostlist2.Add(TECHPOSTS);
            //    }
            //    string techTypeName = techType.DATA_CNAME;
            //    if (count > 0)
            //        allTechPostDict[techTypeName] = techpostlist2;
            //}
            //新逻辑，显示具体职称名称
            //技师
            List<SYS6_BASE_DATA> technicianPostdata = lis5_base_data.Where(x => x.CLASS_ID == "技师职称" && x.DATA_CNAME != "无职称").OrderBy(p => p.DATA_SORT).ToList();
            List<object> technicianpostlist = new List<object>();
            int count = 0;
            foreach (var post in technicianPostdata)
            {
                string techpostname = post.DATA_CNAME;
                int techpostnum = manyTechTypePersons.Where(p => p.ACADEMIC_POST == post.DATA_ID).ToList().Count();
                var TECHPOSTS = new { TECHPOSTNUM = techpostnum.ToString(), TECHPOSTNAME = techpostname };
                count += techpostnum;
                if (techpostnum > 0)
                    technicianpostlist.Add(TECHPOSTS);
            }
            if (count > 0)
                allTechPostDict["技师"] = technicianpostlist;

            //医师
            List<SYS6_BASE_DATA> physicianPostdata = lis5_base_data.Where(x => x.CLASS_ID == "医师职称" && x.DATA_CNAME != "无职称").OrderBy(p => p.DATA_SORT).ToList();
            List<object> physicianpostlist = new List<object>();
            count = 0;
            foreach (var post in physicianPostdata)
            {
                string techpostname = post.DATA_CNAME;
                int techpostnum = manyTechTypePersons.Where(p => p.ACADEMIC_POST == post.DATA_ID).ToList().Count();
                var TECHPOSTS = new { TECHPOSTNUM = techpostnum.ToString(), TECHPOSTNAME = techpostname };
                count += techpostnum;
                if (techpostnum > 0)
                    physicianpostlist.Add(TECHPOSTS);
            }
            if (count > 0)
                allTechPostDict["医师"] = physicianpostlist;

            //护士
            List<SYS6_BASE_DATA> nursePostdata = lis5_base_data.Where(x => x.CLASS_ID == "护士职称" && x.DATA_CNAME != "无职称").OrderBy(p => p.DATA_SORT).ToList();
            List<object> nursepostlist = new List<object>();
            count = 0;
            foreach (var post in nursePostdata)
            {
                string techpostname = post.DATA_CNAME;
                int techpostnum = manyTechTypePersons.Where(p => p.ACADEMIC_POST == post.DATA_ID).ToList().Count();
                var TECHPOSTS = new { TECHPOSTNUM = techpostnum.ToString(), TECHPOSTNAME = techpostname };
                count += techpostnum;
                if (techpostnum > 0)
                    nursepostlist.Add(TECHPOSTS);
            }
            if (count > 0)
                allTechPostDict["护士"] = nursepostlist;

            //研发员
            List<SYS6_BASE_DATA> researcherPostdata = lis5_base_data.Where(x => x.CLASS_ID == "研究员职称" && x.DATA_CNAME != "无职称").OrderBy(p => p.DATA_SORT).ToList();
            List<object> researcherpostlist = new List<object>();
            count = 0;
            foreach (var post in researcherPostdata)
            {
                string techpostname = post.DATA_CNAME;
                int techpostnum = manyTechTypePersons.Where(p => p.ACADEMIC_POST == post.DATA_ID).ToList().Count();
                var TECHPOSTS = new { TECHPOSTNUM = techpostnum.ToString(), TECHPOSTNAME = techpostname };
                count += techpostnum;
                if (techpostnum > 0)
                    researcherpostlist.Add(TECHPOSTS);
            }
            if (count > 0)
                allTechPostDict["研究员"] = researcherpostlist;

            List<PMS_PERSON_INFO> TechPostUnsetPersons = department_person_infos.Where(p => /* p.PERSON_DOC_STATE == "1" && */ p.TECH_POST.IsNullOrEmpty()).ToList();//未维护

            //年龄
            int age_20_30 = 0;
            int age_30_40 = 0;
            int age_40_50 = 0;
            int age_50_60 = 0;
            int age_60_more = 0;
            List<PMS_PERSON_INFO> AgeUnsetPersons = new List<PMS_PERSON_INFO>();
            foreach (var person in department_person_infos)
            {
                int? age = GetAge(person);
                if (age == null)
                {
                    AgeUnsetPersons.Add(person);
                }
                else if (age >= 20 && age <= 30)
                {
                    age_20_30++;
                }
                else if (age > 30 && age <= 40)
                {
                    age_30_40++;
                }
                else if (age > 40 && age <= 50)
                {
                    age_40_50++;
                }
                else if (age > 50 && age <= 60)
                {
                    age_50_60++;
                }
                else if (age > 60)
                {
                    age_60_more++;
                }
                else
                {
                    AgeUnsetPersons.Add(person);
                }
            }
            List<object> agelist = new List<object>
            {
                new { AGENAME =  "20-30岁", AGENUM = age_20_30.ToString() },
                new { AGENAME =  "30-40岁", AGENUM = age_30_40.ToString() },
                new { AGENAME =  "40-50岁", AGENUM = age_40_50.ToString() },
                new { AGENAME =  "50-60岁", AGENUM = age_50_60.ToString() },
                new { AGENAME =  "60岁以上", AGENUM = age_60_more.ToString() },
                new { AGENAME =  "未维护", AGENUM = AgeUnsetPersons.Count.ToString() },
            };

            //学历
            List<SYS6_BASE_DATA> lis5basedatah = lis5_base_data.Where(x => x.CLASS_ID == "最高学历").OrderBy(item => item.DATA_SORT).ToList();

            List<object> highestdegreelist = new List<object>();
            foreach (var degree in lis5basedatah)
            {
                string highestdegreename = degree.DATA_CNAME;
                string highestdegreenum = department_person_infos.Where(p => /* p.PERSON_DOC_STATE == "1" && */ p.HIGHEST_DEGREE == degree.DATA_ID).ToList().Count().ToString();
                var HIGHESTDEGREE = new { HIGHESTDEGREENUM = highestdegreenum, HIGHESTDEGREENAME = highestdegreename };
                highestdegreelist.Add(HIGHESTDEGREE);
            }
            List<PMS_PERSON_INFO> DegreeUnsetPersons = department_person_infos.Where(p => /* p.PERSON_DOC_STATE == "1" && */ p.HIGHEST_DEGREE.IsNullOrEmpty()).ToList();//未维护
            var HIGHESTDEGREEUNSET = new { HIGHESTDEGREENUM = DegreeUnsetPersons.Count().ToString(), HIGHESTDEGREENAME = "未维护" };
            highestdegreelist.Add(HIGHESTDEGREEUNSET);

            //学位
            List<SYS6_BASE_DATA> lis5basedatai = lis5_base_data.Where(x => x.CLASS_ID == "最高学位").OrderBy(item => item.DATA_SORT).ToList();

            List<object> highestdiplomalist = new List<object>();
            foreach (var diploma in lis5basedatai)
            {
                string highestdiplomaname = diploma.DATA_CNAME;
                string highestdiplomanum = department_person_infos.Where(p => /* p.PERSON_DOC_STATE == "1" && */ p.HIGHEST_DIPLOMA == diploma.DATA_ID).ToList().Count().ToString();
                var HIGHESTDEGREE = new { HIGHESTDIPLOMANUM = highestdiplomanum, HIGHESTDIPLOMANAME = highestdiplomaname };
                highestdiplomalist.Add(HIGHESTDEGREE);
            }
            List<PMS_PERSON_INFO> DiplomaUnsetPersons = department_person_infos.Where(p => /* p.PERSON_DOC_STATE == "1" && */ p.HIGHEST_DIPLOMA.IsNullOrEmpty()).ToList();//未维护
            var HIGHEDIPLOMAUNSET = new { HIGHESTDIPLOMANUM = DiplomaUnsetPersons.Count().ToString(), HIGHESTDIPLOMANAME = "未维护" };
            highestdiplomalist.Add(HIGHEDIPLOMAUNSET);

            //未维护列表
            var UnsetPersonList =
                SexUnsetPersons.Select(p => new { p.USER_ID, p.HIS_ID, p.USER_NAME, p.PGROUP_ID, p.PGROUP_NAME, UNSETCONTEN = "性别" })
                .Union(AgeUnsetPersons.Select(p => new { p.USER_ID, p.HIS_ID, p.USER_NAME, p.PGROUP_ID, p.PGROUP_NAME, UNSETCONTEN = "年龄" }))
                .Union(TechUnsetPersons.Select(p => new { p.USER_ID, p.HIS_ID, p.USER_NAME, p.PGROUP_ID, p.PGROUP_NAME, UNSETCONTEN = "技术类别" }))
                .Union(TechPostUnsetPersons.Select(p => new { p.USER_ID, p.HIS_ID, p.USER_NAME, p.PGROUP_ID, p.PGROUP_NAME, UNSETCONTEN = "职称" }))
                .Union(DegreeUnsetPersons.Select(p => new { p.USER_ID, p.HIS_ID, p.USER_NAME, p.PGROUP_ID, p.PGROUP_NAME, UNSETCONTEN = "学历" }))
                .Union(DiplomaUnsetPersons.Select(p => new { p.USER_ID, p.HIS_ID, p.USER_NAME, p.PGROUP_ID, p.PGROUP_NAME, UNSETCONTEN = "学位" }));

            //if (_httpContext.GetSmblFlag() != "1")//ISO入口
            //    AddUnsetSystemDataUsers();

            var UnsetList = UnsetPersonList.GroupBy(p => p.USER_ID).Select(g => new
            {
                USER_ID = g.First().USER_ID,
                PERSON_NAME = $"{g.First().HIS_ID}_{g.First().USER_NAME}",
                PGROUP_ID = g.First().PGROUP_ID,
                PGROUP_NAME = pGroups.Find(o => o.PGROUP_ID == g.First().PGROUP_ID)?.PGROUP_NAME,
                NUM = g.Select(a => a.UNSETCONTEN).Distinct().Count().ToString(),
                CONTEN = string.Join('、', g.Select(a => a.UNSETCONTEN).Distinct())
            }).ToList();

            void AddUnsetSystemDataUsers()
            {
                var unset_system_data_users = _soa.Db.Queryable<SYS6_USER>().Where(u => u.LAB_ID == lab_id && u.STATE_FLAG == "1" && u.MANAGE_CLASS != null && u.MANAGE_CLASS == "4" //非机构级、分组级管理账号
                      && ((u.DEPT_CODE == null || u.DEPT_CODE == "")  // 对应人员表PERSON_DOC_STATE字段为空
                      || (u.JOB_STATE == null || u.JOB_STATE == "") // 对应人员表PERSON_DOC_STATE在职类型字段为空
                      || (u.USER_TYPE == null || u.USER_TYPE == "") // 对应人员表USER_TYPE用工类型字段为空
                      || (u.HIS_ID == null || u.HIS_ID == "") // 对应人员表工号字段为空
                      ))
                    .Select(u => new { u.USER_NO, u.HIS_ID, u.USERNAME, u.DEPT_CODE })
                    .ToList();
                UnsetPersonList = UnsetPersonList.Union(unset_system_data_users.Select(u => new { USER_ID = u.USER_NO, u.HIS_ID, USER_NAME = u.USERNAME, PGROUP_ID = u.DEPT_CODE, PGROUP_NAME = "", UNSETCONTEN = "系统数据未维护(在职类型/用工类型/专业组/工号)" }));
            }

            var result = new
            {
                USER_TYPE_LIST = new
                {
                    TRAINEENUM = TraineeNum, //进修人数
                    PRACTICENUM = PracticeNum, //实习人数
                    TRAININGNUM = TrainingNum, //规培人数
                    DEPARTMENTNUM = DepartmentNum, //科室总数
                    OFFICIALNUM = OfficialNum,  //正式员工人数
                    CONTRACTNUM = ContractNum,  //合同职工人数
                    REHIRINGNUM = RehiringNum,  //返聘职工人数
                    RETIREMENTNUM = RetirementNum,  //退休职工人数
                    TEMPORARYNUM = TemporaryNum,  //临聘职工人数
                },

                SEX_LIST = new
                {
                    MANNUM = ManNum,  //男职工人数
                    WOMANNUM = WomanNum, //女职工人数
                    SEXUNSETNUM = SexUnsetNum,  //未维护性别数
                },

                TECH_TYPE_LIST = new
                {
                    TECHNICIANNUM = TechnicianNum, //技师人数
                    PHYSICIANNUM = PhysicianNum,  //医师人数
                    NURSERNUM = NurseNum, //护士人数
                    RESEARCHERNUM = ResearcherNum,  //研究员人数
                    DOUBLETECHNUM = DoubleTechNum,  //双证人数
                    TECHUNSETNUM = TechUnsetNum,  //技术类型未维护人数
                },

                PGROUP_LIST = pgrouplist, //检验专业组人数分布
                MGROUP_LIST = mgrouplist, //管理专业组人数分布
                SMBL_LAB_LIST = smbllablist,//备案实验室人数分布
                PMS_CLASS_LIST = pmsclasslist,//档案记录分布

                TECHPOST_LIST = allTechPostDict,//职称人数分布
                AGE_LIST = agelist, //年龄人数分布
                HIGHEST_DEGREE_LIST = highestdegreelist, //学历人数分布
                HIGHEST_DIPLOMA_LIST = highestdiplomalist, //学位人数分布

                UNSET_LIST = UnsetList, //未维护列表
                UNSET_LIST_COUNT = UnsetList.Count().ToString(), //未维护列表人数
            };
            return new ResultDto { data = result };

        }
        private class PgroupInfo
        {
            public string PGROUPNUM { get; set; }
            public string PGROUPNAME { get; set; }
            public string MGROUPNAME { get; set; }
        }
        #endregion


        private Dictionary<string, object> DeserializeObject(string json)
        {
            try
            {
                return json == null ? new Dictionary<string, object>() : JsonConvert.DeserializeObject<Dictionary<string, object>>(json);
            }
            catch
            {
                return new Dictionary<string, object>();
            }
        }


        public ResultDto GetPersonStructureInfo_SMBL(string hospital_id, string lab_id, string area_id, string smblLabId, string user_id)
        {
            ////权限校验
            //_IAuthorityService.CheckUserMenuPermission(_soa, user_id, "H8102", hospital_id, lab_id, isThrowException: true);
            var orgParm = new OrgUserParams { hospital_id = hospital_id, lab_id = lab_id, area_id = area_id, smbl_lab_id = smblLabId, user_id = user_id };
            List<SYS6_BASE_DATA> lis5_base_data = _IBaseDataServices.GetSys6BaseData();
            //人员列表源数据
            List<PMS_PERSON_INFO> pms_person_info = null;
            //科室人员列表
            List<PMS_PERSON_INFO> department_person_infos = null;
            //一人多职称时，分作两人，用来统计职称
            List<PMS_PERSON_INFO> manyTechTypePersons = null;
            //检验专业组
            List<SYS6_INSPECTION_PGROUP> pGroups = null;
            List<PgroupInfo> pgrouplist = new List<PgroupInfo>();
            //管理专业组
            List<object> mgrouplist = new List<object>();
            //备案实验室
            List<SMBL_LAB> smbllablist = new List<SMBL_LAB>();
            //人事档案类型填写分布
            List<object> pmsclasslist = new List<object>();

            var tree = GetSMBLPersonTree_Lab(orgParm, null, "H8102", false).FirstOrDefault() ?? new OrgTreeNode();

            //备案实验室树在科室层级会带出更多专业组，所以要缩小范围
            if (smblLabId.IsNotNullOrEmpty())
                tree = tree.GetAllNodes(a => a.NODE_TYPE == GroupTreeNodeTypeEnum.SMBL_LAB.ToIntStr() && a.SOURCE_ID == smblLabId).FirstOrDefault()
                    ?? throw new BizException("找不到该备案实验室！");

            //人员列表源数据
            pms_person_info = tree.GetAllNodesByType(PMSTreeNodeTypeEnum.PERSON.ToIntStr())
                .Select(a => a.SOURCE as PMS_PERSON_INFO)
                .Where(a => a != null)
                .DistinctBy(a => a.PERSON_ID)
                .ToList();

            //科室人员列表
            department_person_infos = pms_person_info.Where(p => /* p.PERSON_DOC_STATE == "1" && */ p.PERSON_TYPE == "1" && new List<string> { "1", "2", "7" }.Contains(p.USER_TYPE)).ToList();  //新人岗权不用PERSON_DOC_STATE  //USER_TYPE为1正式、2合同、7临聘、12退休
            List<string> department_person_ids = department_person_infos.Select(a => a.PERSON_ID).ToList();
            //一人多职称时，分作两人，用来统计职称
            manyTechTypePersons = GetManyTechTypePersons(department_person_infos);

            //人员分布
            var SmblLabPersonList = new List<object>();

            //从机构进入
            if (_httpContext.GetLabId().IsNullOrEmpty() && _httpContext.GetSmblLabId().IsNullOrEmpty())
            {
                smbllablist = tree.GetAllNodes(a => a.NODE_TYPE == GroupTreeNodeTypeEnum.SMBL_LAB.ToIntStr())
                    .Select(a => a.SOURCE as SMBL_LAB)
                    .Where(a => a != null)
                    .DistinctBy(a => a.LAB_ID)
                    .OrderBy(a => a.SMBL_LAB_SORT)
                    .ToList();

                var smblLabPersonObject = smbllablist.Select(a =>
                {
                    string pgroupIds = $",{a.PGROUP_SID},";
                    return new
                    {
                        SMBL_LAB_ID = a.SMBL_LAB_ID,
                        NAME = a.SMBL_LAB_CNAME,
                        NUM = department_person_infos.Where(a => pgroupIds.Contains($",{a.PGROUP_ID},")).Count()
                    };
                }).Where(a => a.NUM != 0).ToList();

                int SmblLabPersonNumTotal = smblLabPersonObject.Sum(a => a.NUM);
                decimal SmblLabPersonPercentageAdd = 0;

                for (int i = 0; i < smblLabPersonObject.Count; i++)
                {
                    decimal precentage = smblLabPersonObject.Count - 1 == i ? 100 - SmblLabPersonPercentageAdd //最后一条，用100减剩余
                          : Math.Round(smblLabPersonObject[i].NUM * 100m / SmblLabPersonNumTotal);
                    SmblLabPersonPercentageAdd += precentage; //累计百分比
                    SmblLabPersonList.Add(new { smblLabPersonObject[i].NAME, smblLabPersonObject[i].NUM, PERCENTAGE = $"{precentage}%" });
                }
            }

            //年龄
            int age_20_30 = 0;
            int age_30_40 = 0;
            int age_40_50 = 0;
            int age_50_60 = 0;
            int age_60_more = 0;
            List<PMS_PERSON_INFO> AgeUnsetPersons = new List<PMS_PERSON_INFO>();
            foreach (var person in department_person_infos)
            {
                int? age = GetAge(person);
                if (age == null)
                {
                    AgeUnsetPersons.Add(person);
                }
                else if (age >= 20 && age <= 30)
                {
                    age_20_30++;
                }
                else if (age > 30 && age <= 40)
                {
                    age_30_40++;
                }
                else if (age > 40 && age <= 50)
                {
                    age_40_50++;
                }
                else if (age > 50 && age <= 60)
                {
                    age_50_60++;
                }
                else if (age > 60)
                {
                    age_60_more++;
                }
                else
                {
                    AgeUnsetPersons.Add(person);
                }
            }
            int ageTotal = age_20_30 + age_30_40 + age_40_50 + age_50_60 + age_60_more;
            decimal per_20_30 = ageTotal == 0 ? 0 : Math.Round(age_20_30 * 100m / ageTotal);
            decimal per_30_40 = ageTotal == 0 ? 0 : Math.Round(age_30_40 * 100m / ageTotal);
            decimal per_40_50 = ageTotal == 0 ? 0 : Math.Round(age_40_50 * 100m / ageTotal);
            decimal per_50_60 = ageTotal == 0 ? 0 : Math.Round(age_50_60 * 100m / ageTotal);
            decimal per_60_more = ageTotal == 0 ? 0 : 100 - (per_20_30 + per_30_40 + per_40_50 + per_50_60); //最后一条，用100减剩余
            List<object> AgeList = new List<object>
            {
                new { NAME =  "20-30岁", NUM = age_20_30.ToString() ,PERCENTAGE = $"{per_20_30}%"},
                new { NAME =  "30-40岁", NUM = age_30_40.ToString() ,PERCENTAGE = $"{per_30_40}%"},
                new { NAME =  "40-50岁", NUM = age_40_50.ToString() ,PERCENTAGE = $"{per_40_50}%"},
                new { NAME =  "50-60岁", NUM = age_50_60.ToString() ,PERCENTAGE = $"{per_50_60}%"},
                new { NAME =  "60岁以上", NUM = age_60_more.ToString() ,PERCENTAGE = $"{per_60_more}%"},
                //new { NAME =  "未维护", NUM = AgeUnsetPersons.Count.ToString() },
            };

            int ManNum = department_person_infos.Where(p => /* p.PERSON_DOC_STATE == "1" && */ p.SEX == "1").ToList().Count();//男
            int WomanNum = department_person_infos.Where(p => /* p.PERSON_DOC_STATE == "1" && */ p.SEX == "2").ToList().Count();//女
            List<object> SexList = new List<object>
            {
                new {NAME = "男性", NUM = ManNum,PERCENTAGE = (ManNum+WomanNum) == 0 ? "0%" : $"{Math.Round(ManNum * 100m / (ManNum+WomanNum))}%"},
                new {NAME = "女性", NUM = WomanNum,PERCENTAGE = (ManNum+WomanNum) == 0 ? "0%" : $"{100-Math.Round(ManNum * 100m / (ManNum+WomanNum))}%"}, //最后一条，用100减剩余
            };
            var SexUnsetPersons = department_person_infos.Where(p => /* p.PERSON_DOC_STATE == "1" && */ p.SEX.IsNullOrEmpty()).ToList();

            List<PMS_PERSON_INFO> TechUnsetPersons = department_person_infos.Where(p => /* p.PERSON_DOC_STATE == "1" && */ p.TECHNOLOGY_TYPE.IsNullOrEmpty()).ToList();

            List<PMS_PERSON_INFO> TechPostUnsetPersons = department_person_infos.Where(p => /* p.PERSON_DOC_STATE == "1" && */ p.TECH_POST.IsNullOrEmpty()).ToList();//未维护

            List<PMS_PERSON_INFO> DegreeUnsetPersons = department_person_infos.Where(p => /* p.PERSON_DOC_STATE == "1" && */ p.HIGHEST_DEGREE.IsNullOrEmpty()).ToList();//未维护

            //近一年来科人员
            List<PMS_PERSON_INFO> newPerson = department_person_infos.Where(p => p.IN_LAB_TIME.IsNotNullOrEmpty() && DateTime.TryParse(p.IN_LAB_TIME, out var inLabDate) && inLabDate >= DateTime.Now.AddYears(-1)).OrderBy(w => DateTime.Parse(w.IN_LAB_TIME)).ToList();

            List<SMBL_LAB> varSmbl = _soa.Db.Queryable<SMBL_LAB>().Where(w => w.SMBL_LAB_STATE == "1" && w.HOSPITAL_ID == hospital_id).ToList();

            // 按日期、管理单元分组
            var GroupedData = newPerson
                .GroupBy(p => new
                {
                    Date = p.IN_LAB_TIME,
                    GroupId = p.PGROUP_ID
                })
                .Select(g => new PersonnelSummaryDto
                {
                    Date = g.Key.Date,
                    GroupName = varSmbl.Find(w => w.PGROUP_SID.Contains(g.Key.GroupId))?.SMBL_LAB_CNAME,
                    Count = g.Count(),
                    Names = g.Select(p => p.USER_NAME).Where(name => !string.IsNullOrEmpty(name)).ToList()
                })
                .ToList();

            //未维护列表
            var UnsetPersonList =
                SexUnsetPersons.Select(p => new { p.USER_ID, p.HIS_ID, p.USER_NAME, p.PGROUP_ID, p.PGROUP_NAME, UNSETCONTEN = "性别" })
                .Union(AgeUnsetPersons.Select(p => new { p.USER_ID, p.HIS_ID, p.USER_NAME, p.PGROUP_ID, p.PGROUP_NAME, UNSETCONTEN = "年龄" }))
                .Union(TechUnsetPersons.Select(p => new { p.USER_ID, p.HIS_ID, p.USER_NAME, p.PGROUP_ID, p.PGROUP_NAME, UNSETCONTEN = "技术类别" }))
                .Union(TechPostUnsetPersons.Select(p => new { p.USER_ID, p.HIS_ID, p.USER_NAME, p.PGROUP_ID, p.PGROUP_NAME, UNSETCONTEN = "职称" }))
                .Union(DegreeUnsetPersons.Select(p => new { p.USER_ID, p.HIS_ID, p.USER_NAME, p.PGROUP_ID, p.PGROUP_NAME, UNSETCONTEN = "学历" }));

            //var UnsetList = UnsetPersonList.GroupBy(p => p.USER_ID)
            //    .Select(g => new
            //{
            //    USER_ID = g.First().USER_ID,
            //    PERSON_NAME = $"{g.First().HIS_ID}_{g.First().USER_NAME}",
            //    PGROUP_ID = g.First().PGROUP_ID,
            //    PGROUP_NAME = pGroups.Find(o => o.PGROUP_ID == g.First().PGROUP_ID)?.PGROUP_NAME,
            //    NUM = g.Select(smbllab => smbllab.UNSETCONTEN).Distinct().Count().ToString(),
            //    CONTEN = string.Join('、', g.Select(smbllab => smbllab.UNSETCONTEN).Distinct())
            //}).ToList();

            //总人数
            var TotalPersonNum = department_person_infos.Count();
            //当日在岗
            var OnDutyPersonNum = TotalPersonNum;
            //未到岗
            var OffDutyPersonNum = 0;

            var UnsetPersonNum = UnsetPersonList.GroupBy(p => p.USER_ID).Count();
            //顶部数据
            var TopData = new { TotalPersonNum = TotalPersonNum, OnDutyPersonNum = OnDutyPersonNum, OffDutyPersonNum = OffDutyPersonNum, UnsetPersonNum };

            //学历
            List<SYS6_BASE_DATA> lis5basedatah = lis5_base_data.Where(x => x.CLASS_ID == "最高学历").OrderBy(item => item.DATA_SORT).ToList();
            List<object> DegreeList = new List<object>();
            int highestdegreeTotal = department_person_infos.Where(p => lis5basedatah.Select(d => d.DATA_ID).Contains(p.HIGHEST_DEGREE)).Count();
            decimal highestdegreePercentageAdd = 0;
            for (int i = 0; i < lis5basedatah.Count; i++)
            {
                string highestdegreename = lis5basedatah[i].DATA_CNAME;
                int highestdegreenum = department_person_infos.Where(p => p.HIGHEST_DEGREE == lis5basedatah[i].DATA_ID).Count();

                decimal percentage = highestdegreeTotal == 0 ? 0
                    : i == lis5basedatah.Count - 1 ? 100 - highestdegreePercentageAdd //最后一条，用100减剩余
                    : Math.Round(highestdegreenum * 100m / highestdegreeTotal);

                highestdegreePercentageAdd += percentage;

                var HIGHESTDEGREE = new { NAME = highestdegreename, NUM = highestdegreenum.ToString(), PERCENTAGE = $"{percentage}%" };
                if (highestdegreenum > 0)
                    DegreeList.Add(HIGHESTDEGREE);
            }

            //人员类型
            List<OA_BASE_DATA> postNowData = _IBaseDataServices.GetOaBaseData("现从事岗位").OrderByDescending(p => p.DATA_SORT).ToList();
            List<object> PersonPostList = new List<object>();
            List<string> postNowValues = department_person_infos.Select(a => GetFieldFromJsonData(a, "SMBL_POST_NOW")).ToList();
            int postNowTotal = postNowValues.Where(a => postNowData.Select(p => p.DATA_ID).Contains(a)).Count();
            decimal postNowPrecentageAdd = 0;
            for (int i = 0; i < postNowData.Count; i++)
            {
                string postNowName = postNowData[i].DATA_NAME;
                int postNowNum = postNowValues.Where(a => a == postNowData[i].DATA_ID).Count();
                decimal percentage = postNowTotal == 0 ? 0
                    : i == postNowData.Count - 1 ? 100 - postNowPrecentageAdd //最后一条，用100减剩余
                    : Math.Round(postNowNum * 100m / postNowTotal);
                postNowPrecentageAdd += percentage;
                var POSTNOW = new { NAME = postNowName, NUM = postNowNum.ToString(), PERCENTAGE = $"{percentage}%" };
                if (postNowNum > 0)
                    PersonPostList.Add(POSTNOW);
            }

            //职称情况    
            List<SYS6_BASE_DATA> techTypedata = lis5_base_data.Where(x => x.CLASS_ID == "职称级别" && x.DATA_ID != "5").OrderByDescending(p => p.DATA_SORT).ToList();
            List<object> TechTypelist = new List<object>();
            int techTypeTotal = department_person_infos.Where(p => /* p.PERSON_DOC_STATE == "1" && */ techTypedata.Select(d => d.DATA_ID).Contains(p.TECH_POST)).Count();
            decimal techTypePrecentAdd = 0;
            for (int i = 0; i < techTypedata.Count; i++)
            {
                string techTypeName = techTypedata[i].DATA_CNAME;
                int techTypeNum = department_person_infos.Where(p => /* p.PERSON_DOC_STATE == "1" && */ p.TECH_POST == techTypedata[i].DATA_ID).Count();
                decimal percentage = techTypeTotal == 0 ? 0
                   : i == techTypedata.Count -1 ? 100 - techTypePrecentAdd //最后一条，用100减剩余
                   : Math.Round(techTypeNum * 100m / techTypeTotal);
                techTypePrecentAdd += percentage;
                var TECHTYPE = new { NAME = techTypeName, NUM = techTypeNum.ToString(), PERCENTAGE = $"{percentage}%" };
                if (techTypeNum > 0)
                    TechTypelist.Add(TECHTYPE);
            }

            //资质情况
            //职称     
            var cerTypedata = new List<SYS6_BASE_DATA> {
                new SYS6_BASE_DATA { DATA_ID = "XBD00000011" , DATA_CNAME="生物安全证"},
                new SYS6_BASE_DATA { DATA_ID = "XBD00000013" , DATA_CNAME="PCR上岗证"},
                new SYS6_BASE_DATA { DATA_ID = "XBD00000026" , DATA_CNAME="卫生专业技术资格证"},
                new SYS6_BASE_DATA { DATA_ID = "XBD00000020" , DATA_CNAME="艾滋病上岗证"},
                new SYS6_BASE_DATA { DATA_ID = "XBD00000022" , DATA_CNAME="肠道门诊检验人员上岗证"},
                new SYS6_BASE_DATA { DATA_ID = "XBD00000027" , DATA_CNAME="医师执业证书"},
                new SYS6_BASE_DATA { DATA_ID = "XBD00000028" , DATA_CNAME="高等学校教师资格证"},
                };
            List<PMS_SKILL_CERTIFICATE_LIST> cerList = _soa.Db.Queryable<PMS_SKILL_CERTIFICATE_LIST>().Where(a => department_person_ids.Contains(a.PERSON_ID)
                && cerTypedata.Select(a => a.DATA_ID).Contains(a.CERTIFICATE_TYPE) && a.CHECK_STATE == "2").ToList();

            var CerTypeList = cerTypedata.Select(cerType =>
            {
                var personCerDate = cerList.Where(a => a.CERTIFICATE_TYPE == cerType.DATA_ID)
                    .GroupBy(a => a.PERSON_ID)
                    .Select(g => g.Max(c => DateTime.TryParse(c.CERTIFICATE_VALIDITY, out var endDate) ? endDate : DateTime.MaxValue))
                    .Where(date => date > DateTime.Now.Date) //今天还有效
                    .ToList();

                return new
                {
                    NAME = cerType.DATA_CNAME, //资质类型
                    NUMBER = $"{personCerDate.Count}/{department_person_infos.Count}", //持证情况
                    PERCENTAGE = department_person_infos.Count == 0 ? "0%" : $"{personCerDate.Count * 100 / department_person_infos.Count}%", //持证占比
                    EXPIRED60DAY = personCerDate.Count(date => date < DateTime.Now.Date.AddDays(61)),//近60天到期数
                    COUNT = personCerDate.Count,
                };
            })
            .OrderByDescending(a => a.COUNT)
            .ToList();

            //培训信息
            var trainData = new List<SYS6_BASE_DATA> {
                new SYS6_BASE_DATA { DATA_ID = "XBD00000801" , DATA_CNAME="管理人员"},
                new SYS6_BASE_DATA { DATA_ID = "XBD00000802" , DATA_CNAME="骨干人员"},
                new SYS6_BASE_DATA { DATA_ID = "XBD00000803" , DATA_CNAME="新进人员"},
                };

            DateTime dateTime;
            List<PMS_TRAIN_LIST> trainList = _soa.Db.Queryable<PMS_TRAIN_LIST>()
                .Where(a => department_person_ids.Contains(a.PERSON_ID))
                .Where(a => trainData.Select(a => a.DATA_ID).Contains(a.TRAIN_TYPE) && a.CHECK_STATE == "2")
                .ToList()
                .Where(a => DateTime.TryParse(a.TRAIN_END_TIME, out dateTime)) //只保留日期可识别的
                .ToList();

            //创建年份
            List<int> years = trainList.Select(a => DateTime.Parse(a.TRAIN_END_TIME).Year).Distinct().OrderBy(a => a).ToList();

            var TrainNumberList = new List<object>();
            //汇总
            //格式：{key:2025年，value:8}
            var yearItemTotal = years.Select(year =>
                new KeyValuePair<string, int>($"{year}年", trainList.Where(t => DateTime.Parse(t.TRAIN_END_TIME).Year == year).GroupBy(t => t.PERSON_ID).Count()));
            var trainTotal = new { ITEM_NAME = "汇总", ITEM_DATA = yearItemTotal };
            TrainNumberList.Add(trainTotal);

            foreach (var trainBDitem in trainData)
            {
                var thistrainList = trainList.Where(a => a.TRAIN_TYPE == trainBDitem.DATA_ID).ToList();
                //格式：{key:2025年，value:8}
                var yearItem = years.Select(year =>
                    new KeyValuePair<string, int>($"{year}年", thistrainList.Where(t => DateTime.Parse(t.TRAIN_END_TIME).Year == year).GroupBy(t => t.PERSON_ID).Count()));
                var trainItem = new { ITEM_NAME = trainBDitem.DATA_CNAME, ITEM_DATA = yearItem };
                TrainNumberList.Add(trainItem);
            }

            //培训记录明细
            var personIds = trainList.Select(a => a.PERSON_ID).Distinct().ToList();
            var persons = _soa.Db.Queryable<PMS_PERSON_INFO>().Where(a => personIds.Contains(a.PERSON_ID)).ToList();
            var TrainInfoList = trainList.Select(a => new
            {
                CONTENT = a.TRAIN_CONTENT,
                PERSON = persons.Find(p => p.PERSON_ID == a.PERSON_ID)?.USER_NAME,
                MARK = "合格",
                DATE = DateTime.Parse(a.TRAIN_END_TIME).ToString("yyyy-MM-dd")
            }).ToList();

            var result = new
            {
                TopData,//顶部数据
                DegreeList,//学历分布
                PersonPostList,//人员类型
                TechTypelist, //职称类型
                AgeList,//年龄
                SmblLabPersonList,//人员分布
                SexList,//性别
                CerTypeList, //资质情况
                TrainNumberList,//培训情况-分布
                TrainInfoList, //培训情况-明细
                GroupedData
            };
            return result.ToResultDto();
        }

        private List<PMS_PERSON_INFO> GetManyTechTypePersons(List<PMS_PERSON_INFO> persons)
        {
            var manyTechTypePersons = new List<PMS_PERSON_INFO>();
            foreach (var item in persons)
            {
                if (item.TECHNOLOGY_TYPE.IsNotNullOrEmpty())
                {
                    manyTechTypePersons.Add(item);
                }
                if (item.RECORD_DATA.IsNotNullOrEmpty())
                {
                    IDictionary<string, object> dataDic = DeserializeObject(item.RECORD_DATA);

                    if (dataDic != null && dataDic.ContainsKey("TechList") && dataDic["TechList"] != null)
                    {
                        //取最新的职称评定
                        List<TechList> techList = JsonConvert.DeserializeObject<List<TechList>>(dataDic["TechList"].ToString());
                        foreach (var tech in techList)
                        {
                            var newTech = new PMS_PERSON_INFO();
                            newTech.PERSON_ID = item.PERSON_ID;
                            newTech.PERSON_DOC_STATE = item.PERSON_DOC_STATE;
                            newTech.TECH_POST = tech.TECH_POST;
                            newTech.ACADEMIC_POST = tech.ACADEMIC_POST;
                            newTech.EMPLOYMENT_UNIT = tech.EMPLOYMENT_UNIT;
                            newTech.TECHNOLOGY_TYPE = tech.TECHNOLOGY_TYPE;
                            newTech.TECH_POST_PROFESSION = tech.TECH_POST_PROFESSION;
                            newTech.TECH_CERTIFICE_TIME = tech.TECH_CERTIFICE_TIME;
                            manyTechTypePersons.Add(newTech);
                        }
                    }
                }
            }
            return manyTechTypePersons;
        }
        private int GetAge(string? AgeString)
        {
            AgeString = AgeString?.Trim(new char[] { ' ', '岁' });
            if (AgeString == null || !int.TryParse(AgeString, out var ageInt))
                return 0;
            return ageInt;
        }


        public int? GetAge(PMS_PERSON_INFO person)
        {
            int? age = null;
            if (person.ID_CARD != null && person.ID_CARD.Length == 18)
            {
                person.BIRTHDAY = person.ID_CARD.Substring(6, 4) + "-" + person.ID_CARD.Substring(10, 2) + "-" + person.ID_CARD.Substring(12, 2);
                age = CommonHelper.CalculateAge(person.BIRTHDAY);
            }
            if (age == null)
            {
                age = CommonHelper.CalculateAge(person.BIRTHDAY);
            }
            return age;
        }

        /// <summary>
        /// 根据人员ID获取人员信息
        /// </summary>
        /// <param name="person_id"></param>
        /// <param name="hospital_id"></param>
        /// <param name="pgroup_id"></param>
        /// <returns></returns>
        public PersonInfoDto GetPersonInfoById(string person_id, string user_no, string hospital_id, string load_mode)
        {
            List<PMS_PERSON_INFO> pms_person_info = new List<PMS_PERSON_INFO>();
            //PMS_PERSON_INFO pmsPersonInfo = null;

            if (person_id.IsNotNullOrEmpty())
                pms_person_info = _soa.Db.Queryable<PMS_PERSON_INFO>().Where(p => p.PERSON_ID == person_id && p.HOSPITAL_ID == hospital_id).ToList();
            else
                pms_person_info = _soa.Db.Queryable<PMS_PERSON_INFO>().Where(p => p.USER_ID == user_no && p.HOSPITAL_ID == hospital_id && p.PERSON_STATE == "1").ToList();

            #region 删除同步人员逻辑
            //if (pmsPersonInfo != null)
            //{
            //    person_id = pmsPersonInfo.PERSON_ID;
            //    var ssList = _soa.Db.Queryable<SYS6_USER>().First(p => p.STATE_FLAG == "1" && p.USER_NO == pmsPersonInfo.USER_ID);
            //    if (pmsPersonInfo.PGROUP_ID != ssList.DEPT_CODE || pmsPersonInfo.DUTIES != ssList.POWER || pmsPersonInfo.ACADEMIC_POST != ssList.TECH_POST
            //    || pmsPersonInfo.USER_TYPE != ssList.USER_TYPE || pmsPersonInfo.HIS_ID != ssList.HIS_ID || pmsPersonInfo.PERSON_DOC_STATE != ssList.JOB_STATE)
            //    {
            //        pmsPersonInfo.PGROUP_ID = ssList.DEPT_CODE;
            //        pmsPersonInfo.DUTIES = ssList.POWER;
            //        pmsPersonInfo.ACADEMIC_POST = ssList.TECH_POST;
            //        pmsPersonInfo.USER_TYPE = ssList.USER_TYPE;

            //        pmsPersonInfo.LOGID = ssList.LOGID;
            //        pmsPersonInfo.HIS_ID = ssList.HIS_ID;
            //        pmsPersonInfo.PERSON_DOC_STATE = ssList.JOB_STATE;
            //        _soa.Db.Updateable<PMS_PERSON_INFO>(pmsPersonInfo).ExecuteCommand();
            //    }
            //    pms_person_info = _soa.Db.Queryable<PMS_PERSON_INFO>().Where(p => p.PERSON_ID == person_id && p.HOSPITAL_ID == hospital_id).Select<PMS_PERSON_INFO>().ToList();
            //}
            //else
            //{
            //    if (load_mode == "2")
            //    {
            //        try
            //        {
            //            var ssList = _soa.Db.Queryable<SYS6_USER>().First(p => p.STATE_FLAG == "1" && p.USER_NO == user_no);
            //            PMS_PERSON_INFO personItem = new PMS_PERSON_INFO();
            //            string personid = _IBaseDataServices.GetTableMax("PMS_PERSON_INFO", "PERSON_ID", 1, 1).data.ToString();
            //            personItem.PERSON_ID = personid;
            //            personItem.PGROUP_ID = ssList.DEPT_CODE;
            //            personItem.HOSPITAL_ID = hospital_id;
            //            personItem.USER_ID = ssList.USER_NO;
            //            personItem.USER_NAME = ssList.USERNAME;
            //            personItem.USER_TYPE = ssList.USER_TYPE;
            //            personItem.DUTIES = ssList.POWER;
            //            personItem.ACADEMIC_POST = ssList.TECH_POST;
            //            personItem.ID_CARD = ssList.ID_CARD == null ? "" : ssList.ID_CARD;
            //            personItem.LOGID = ssList.LOGID;
            //            personItem.HIS_ID = ssList.HIS_ID;
            //            personItem.PERSON_STATE = "1";
            //            personItem.PERSON_DOC_STATE = ssList.JOB_STATE;
            //            _soa.Db.Insertable<PMS_PERSON_INFO>(personItem).ExecuteCommand();
            //            pms_person_info = _soa.Db.Queryable<PMS_PERSON_INFO>().Where(p => p.PERSON_ID == personid && p.HOSPITAL_ID == hospital_id).Select<PMS_PERSON_INFO>().ToList();
            //        }
            //        catch (Exception ex)
            //        {
            //            _IBaseDataServices.GetErrorTableMax("PMS_PERSON_INFO", ex, "PERSON_ID");
            //        }

            //    }
            //}
            #endregion
            var sys6InspectionPgroup = _IBaseDataServices.GetInspectionPgroup();
            List<SYS6_BASE_DATA> lis5_base_data = _IBaseDataServices.GetSys6BaseData();
            return CommGetPersonInfo(pms_person_info, sys6InspectionPgroup, lis5_base_data, isLoadSmblFile: _httpContext.GetSmblFlag() == "1");
        }

        /// <summary>
        /// 根据手机号获取人员信息
        /// </summary>
        /// <param name="phone_num"></param>
        /// <param name="hospital_id"></param>
        /// <returns></returns>
        public PersonInfoDto GetPersonInfoByPhoneNum(string phone_num, string hospital_id, string user_name)
        {
            List<PMS_PERSON_INFO> pms_person_info = _soa.Db.Queryable<PMS_PERSON_INFO>().Where(p => p.PHONE == phone_num && p.HOSPITAL_ID == hospital_id
            && p.USER_NAME == user_name).Select<PMS_PERSON_INFO>().ToList();
            var sys6InspectionPgroup = _IBaseDataServices.GetInspectionPgroup();
            List<SYS6_BASE_DATA> lis5_base_data = _IBaseDataServices.GetSys6BaseData();
            return CommGetPersonInfo(pms_person_info, sys6InspectionPgroup, lis5_base_data);
        }

        /// <summary>
        /// 获取人员下拉信息
        /// </summary>
        /// <param name="hospital_id"></param>
        /// <returns></returns>
        public ResultDto GetPersonDropDownInfo(string hospital_id)
        {
            var pms_person_info = _soa.Db.Queryable<PMS_PERSON_INFO>().Where(p => p.HOSPITAL_ID == hospital_id && p.PERSON_DOC_STATE != "0")
                .Select(s => new
                {
                    PERSON_ID = s.PERSON_ID,
                    USER_NAME = s.PGROUP_NAME
                }).ToList();
            var USER_NAMES = pms_person_info.Select(o => new { key = o.PERSON_ID, value = o.USER_NAME });
            var result = new { USER_NAMES_DLIST = USER_NAMES };
            return new ResultDto { data = result };
        }

        /// <summary>
        /// 获取所有人员信息
        /// </summary>
        /// <returns></returns>
        public List<PMS_PERSON_INFO> GetAllPersonInfo()
        {
            List<PMS_PERSON_INFO> pms_person_info = _soa.Db.Queryable<PMS_PERSON_INFO>().Where(p => p.PERSON_DOC_STATE != "0").Select<PMS_PERSON_INFO>().ToList();
            return pms_person_info;
        }

        /// <summary>
        /// 根据医疗机构ID获取所有人员信息
        /// </summary>
        /// <returns></returns>
        public List<PersonInfoDto> GetAllPersonInfoByHospitalId(string hospital_id)
        {
            List<PMS_PERSON_INFO> pms_person_info = _IBaseDataServices.GetPmsPersonInfo();
            pms_person_info.Where(x => x.HOSPITAL_ID == hospital_id).ToList();
            return CommGetPersonListInfo(pms_person_info);
        }


        /// <summary>
        /// 生成人员文件信息
        /// </summary>
        /// <param name="person_id"></param>
        /// <returns></returns>
        public ResultDto GeneratePersonFileInfo(string person_id, string hospitalId)
        {
            OfficeHelper oh = new OfficeHelper();
            string contentRootPath = _hostingEnvironment.ContentRootPath;
            string path = Path.Combine(contentRootPath, "ExampleFile", "PersonInfo.docx");
            string imgPath = string.Empty;
            //Stopwatch sw = new Stopwatch();
            //sw.Start();
            using (Document document = new Document())
            {
                document.LoadFromFile(path);
                Section section = null;
                List<PMS_PERSON_INFO> pms_person_info = _soa.Db.Queryable<PMS_PERSON_INFO>().Where(p => p.PERSON_ID == person_id).Select<PMS_PERSON_INFO>().ToList();
                string guid = Guid.NewGuid().ToString("N");
                string newFilePath = guid + ".docx";
                string newPdfFilePath = guid + ".pdf";//保存路径
                var sys_class_info = _IModuleLabGroupService.GetPmsAddnClassInfo().Select(s => new PMS_ADDN_CLASS_INFO
                {
                    CLASS_ID = s.CLASS_ID,
                    CLASS_TYPE = s.CLASS_TYPE,
                    FORM_SETUP_ID = s.FORM_SETUP_ID,
                    TABLE_SETUP_ID = s.TABLE_SETUP_ID,
                    CLASS_NAME = s.CLASS_NAME,
                    CLASS_SORT = s.CLASS_SORT
                }).OrderBy(w => w.CLASS_SORT).ToList();
                var sys6InspectionPgroup = _IBaseDataServices.GetInspectionPgroup();
                List<SYS6_BASE_DATA> lis5_base_data = _IBaseDataServices.GetSys6BaseData();
                var Result = CommGetPersonInfo(pms_person_info, sys6InspectionPgroup, lis5_base_data);
                if (Result != null)
                {
                    var data = Result;
                    foreach (var proName in Result.GetType().GetProperties())
                    {
                        document.Replace("{" + proName.Name + "}", data.GetType().GetProperty(proName.Name).GetValue(data, null) != null ? data.GetType().GetProperty(proName.Name).GetValue(data, null).ToString() : "", false, true);
                    }
                    imgPath = Result.PHOTO_PATH?.Replace(FileHttpUrl, "");
                    string path1 = file_preview_address + imgPath;
                    string file_path1 = path1.Replace(@"\", "/");

                    //string aFirstName = imgPath?.Substring(imgPath.LastIndexOf("\\") + 1, (imgPath.LastIndexOf(".") - imgPath.LastIndexOf("\\") - 1));
                    //string aLastName = imgPath.Substring(imgPath.LastIndexOf(".") + 1, (imgPath.Length - imgPath.LastIndexOf(".") - 1));
                    byte[] img = GetConvertType(file_path1);


                    DocPicture picture = document.Sections[0].Tables[0].Rows[0].Cells[0].Paragraphs[0].AppendPicture(img);
                    picture.Width = 120f;
                    picture.Height = 170f;
                    picture.TextWrappingStyle = TextWrappingStyle.Inline;
                    picture.HorizontalAlignment = ShapeHorizontalAlignment.Center;
                }
                // Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                //.Information($"查询基本信息和添加图片耗时:{sw.ElapsedMilliseconds}ms");
                BookmarksNavigator bookmarksNavigator = new BookmarksNavigator(document);
                //返回给前端 用于现场人员标准格式
                ClassPropSetting returnpPopSetting = new ClassPropSetting();
                returnpPopSetting.Setting = new List<AutoClassProp>();

                ConcurrentBag<AutoClassProp> classBag = new ConcurrentBag<AutoClassProp>();
                //读取文件获取设置
                ClassPropSetting tableSetting = new ClassPropSetting();
                string settingPath = Path.Combine(contentRootPath, "", "TableSetting.txt");
                if (File.Exists(settingPath))
                {
                    // 读取文件内容
                    string content = File.ReadAllText(settingPath);
                    // 将文本内容转换为JSON对象
                    tableSetting = JsonConvert.DeserializeObject<ClassPropSetting>(content);
                }
                // Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                //.Information($"读取文件TableSetting耗时:{sw.ElapsedMilliseconds}ms");
                int itemCount = 2;
                for (int T = 0; T < sys_class_info.Count; T++)
                {
                    if (document.Sections.Count == 0)
                    {
                        section = document.AddSection();
                    }
                    section = document.Sections[0];
                    #region 旧代码
                    //Table table1 = null;
                    //TableRow tableRow = null;
                    //string bookmarks = "PO_" + sys_class_info[T].CLASS_ID.ToUpper();
                    ////获取书签
                    //if (document.Bookmarks.FindByName(bookmarks) != null)
                    //{
                    //    bookmarksNavigator.MoveToBookmark(bookmarks);
                    //    TextBodyPart part = bookmarksNavigator.GetBookmarkContent();
                    //    //履历记录
                    //    if (sys_class_info[T].CLASS_ID.ToUpper() == "PMS_RESUME_LIST")
                    //    {
                    //        for (int i = 0; i < part.BodyItems.Count; i++)
                    //        {
                    //            if (part.BodyItems[i].ToString() == "Spire.Doc.Table")
                    //            {
                    //                List<PMS_RESUME_LIST> PMS_RESUME_LIST = _soa.Db.Queryable<PMS_RESUME_LIST>().Where(p => p.PERSON_ID == person_id && p.CHECK_STATE == "2").Select<PMS_RESUME_LIST>().ToList();
                    //                table1 = (Spire.Doc.Table)part.BodyItems[i];
                    //                int starIndex = table1.Rows.Count - 1;

                    //                if (PMS_RESUME_LIST.Count > 0)
                    //                {
                    //                    for (int year = 0; year < PMS_RESUME_LIST.Count; year++)
                    //                    {
                    //                        tableRow = table1.Rows[starIndex].Clone();
                    //                        for (int j = 0; j < tableRow.Cells.Count; j++)//列循环
                    //                        {
                    //                            tableRow.Cells[j].Paragraphs[0].Text = "";
                    //                            Paragraph p = tableRow.Cells[j].Paragraphs[0];
                    //                            TextRange textRange = null;
                    //                            if (j == 0)
                    //                            {
                    //                                textRange = p.AppendText(PMS_RESUME_LIST[year].RESUME_UNIT);
                    //                            }
                    //                            if (j == 1)
                    //                            {
                    //                                textRange = p.AppendText(PMS_RESUME_LIST[year].WORK_DEPT);
                    //                            }
                    //                            if (j == 2)
                    //                            {
                    //                                textRange = p.AppendText(PMS_RESUME_LIST[year].WORK_START_TIME + "-" + PMS_RESUME_LIST[year].WORK_END_TIME);
                    //                            }
                    //                            if (j == 3)
                    //                            {
                    //                                textRange = p.AppendText(PMS_RESUME_LIST[year].WORK_YEAR.ToString() + "年");
                    //                            }
                    //                            if (j == 4)
                    //                            {
                    //                                textRange = p.AppendText(PMS_RESUME_LIST[year].WORK_PROFESSION);
                    //                            }
                    //                            textRange.ApplyCharacterFormat(p.BreakCharacterFormat);//保持表头样式
                    //                            textRange.CharacterFormat.FontSize = 10;
                    //                            textRange.CharacterFormat.Bold = false;
                    //                        }
                    //                        table1.Rows.Add(tableRow);
                    //                    }
                    //                }
                    //                else
                    //                {
                    //                    tableRow = table1.Rows[starIndex].Clone();
                    //                    for (int j = 0; j < tableRow.Cells.Count; j++)//列循环
                    //                    {
                    //                        tableRow.Cells[j].Paragraphs[0].Text = "";
                    //                        Paragraph p = tableRow.Cells[j].Paragraphs[0];
                    //                        if (j == 0)
                    //                        {
                    //                            tableRow.Cells[j].Paragraphs[0].Text = "暂无数据";
                    //                        }
                    //                    }
                    //                    table1.Rows.Add(tableRow);
                    //                }
                    //                part.BodyItems.Add(table1);
                    //                bookmarksNavigator.ReplaceBookmarkContent(part);
                    //                break;
                    //            }
                    //        }
                    //    }
                    //    //职称记录
                    //    if (sys_class_info[T].CLASS_ID.ToUpper() == "PMS_PROFESSIONAL_LIST")
                    //    {
                    //        for (int i = 0; i < part.BodyItems.Count; i++)
                    //        {
                    //            if (part.BodyItems[i].ToString() == "Spire.Doc.Table")
                    //            {
                    //                List<PMS_PROFESSIONAL_LIST> PMS_PROFESSIONAL_LIST = _soa.Db.Queryable<PMS_PROFESSIONAL_LIST>().Where(p => p.PERSON_ID == person_id && p.CHECK_STATE == "2").Select<PMS_PROFESSIONAL_LIST>().ToList();
                    //                table1 = (Spire.Doc.Table)part.BodyItems[i];
                    //                int starIndex = table1.Rows.Count - 1;

                    //                if (PMS_PROFESSIONAL_LIST.Count > 0)
                    //                {
                    //                    for (int year = 0; year < PMS_PROFESSIONAL_LIST.Count; year++)
                    //                    {
                    //                        tableRow = table1.Rows[starIndex].Clone();
                    //                        for (int j = 0; j < tableRow.Cells.Count; j++)//列循环
                    //                        {
                    //                            tableRow.Cells[j].Paragraphs[0].Text = "";
                    //                            Paragraph p = tableRow.Cells[j].Paragraphs[0];
                    //                            TextRange textRange = null;
                    //                            if (j == 0)
                    //                            {
                    //                                textRange = p.AppendText(PMS_PROFESSIONAL_LIST[year].PROFESSIONAL_UNIT);
                    //                            }
                    //                            if (j == 1)
                    //                            {
                    //                                textRange = p.AppendText(RecordClassBaseName("职称级别", PMS_PROFESSIONAL_LIST[year].PROFESSIONAL_LEVEL));
                    //                            }
                    //                            if (j == 2)
                    //                            {
                    //                                textRange = p.AppendText(RecordClassBaseName("职称类型", PMS_PROFESSIONAL_LIST[year].TECHNOLOGY_TYPE));
                    //                            }
                    //                            if (j == 3)
                    //                            {
                    //                                textRange = p.AppendText(PMS_PROFESSIONAL_LIST[year].EVALUATE_UNIT);
                    //                            }
                    //                            if (j == 4)
                    //                            {
                    //                                textRange = p.AppendText(PMS_PROFESSIONAL_LIST[year].EVALUATE_DATE.ToString());
                    //                            }
                    //                            textRange.ApplyCharacterFormat(p.BreakCharacterFormat);//保持表头样式
                    //                            textRange.CharacterFormat.FontSize = 10;
                    //                            textRange.CharacterFormat.Bold = false;
                    //                        }
                    //                        table1.Rows.Add(tableRow);
                    //                    }
                    //                }
                    //                else
                    //                {
                    //                    tableRow = table1.Rows[starIndex].Clone();
                    //                    for (int j = 0; j < tableRow.Cells.Count; j++)//列循环
                    //                    {
                    //                        tableRow.Cells[j].Paragraphs[0].Text = "";
                    //                        Paragraph p = tableRow.Cells[j].Paragraphs[0];
                    //                        if (j == 0)
                    //                        {
                    //                            tableRow.Cells[j].Paragraphs[0].Text = "暂无数据";
                    //                        }
                    //                    }
                    //                    table1.Rows.Add(tableRow);
                    //                }
                    //                part.BodyItems.Add(table1);
                    //                bookmarksNavigator.ReplaceBookmarkContent(part);
                    //                break;
                    //            }
                    //        }
                    //    }
                    //    //奖惩记录
                    //    if (sys_class_info[T].CLASS_ID.ToUpper() == "PMS_REWARD_LIST")
                    //    {
                    //        for (int i = 0; i < part.BodyItems.Count; i++)
                    //        {
                    //            if (part.BodyItems[i].ToString() == "Spire.Doc.Table")
                    //            {
                    //                List<PMS_REWARD_LIST> PMS_REWARD_LIST = _soa.Db.Queryable<PMS_REWARD_LIST>().Where(p => p.PERSON_ID == person_id && p.CHECK_STATE == "2").Select<PMS_REWARD_LIST>().ToList();
                    //                table1 = (Spire.Doc.Table)part.BodyItems[i];
                    //                int starIndex = table1.Rows.Count - 1;
                    //                if (PMS_REWARD_LIST.Count > 0)
                    //                {
                    //                    for (int year = 0; year < PMS_REWARD_LIST.Count; year++)
                    //                    {
                    //                        tableRow = table1.Rows[starIndex].Clone();
                    //                        for (int j = 0; j < tableRow.Cells.Count; j++)//列循环
                    //                        {
                    //                            tableRow.Cells[j].Paragraphs[0].Text = "";
                    //                            Paragraph p = tableRow.Cells[j].Paragraphs[0];
                    //                            TextRange textRange = null;
                    //                            if (j == 0)
                    //                            {
                    //                                textRange = p.AppendText(PMS_REWARD_LIST[year].REWARD_YEAR.ToString());
                    //                            }
                    //                            if (j == 1)
                    //                            {
                    //                                textRange = p.AppendText(RecordClassBaseName("奖惩级别", PMS_REWARD_LIST[year].REWARD_LEVEL));
                    //                            }
                    //                            if (j == 2)
                    //                            {
                    //                                textRange = p.AppendText(PMS_REWARD_LIST[year].REWARD_NAME);
                    //                            }
                    //                            if (j == 3)
                    //                            {
                    //                                textRange = p.AppendText(PMS_REWARD_LIST[year].REWARD_ITEM_NAME);
                    //                            }
                    //                            textRange.ApplyCharacterFormat(p.BreakCharacterFormat);//保持表头样式
                    //                            textRange.CharacterFormat.FontSize = 10;
                    //                            textRange.CharacterFormat.Bold = false;
                    //                        }
                    //                        table1.Rows.Add(tableRow);
                    //                    }
                    //                }
                    //                else
                    //                {
                    //                    tableRow = table1.Rows[starIndex].Clone();
                    //                    for (int j = 0; j < tableRow.Cells.Count; j++)//列循环
                    //                    {
                    //                        tableRow.Cells[j].Paragraphs[0].Text = "";
                    //                        Paragraph p = tableRow.Cells[j].Paragraphs[0];
                    //                        if (j == 0)
                    //                        {
                    //                            tableRow.Cells[j].Paragraphs[0].Text = "暂无数据";
                    //                        }
                    //                    }
                    //                    table1.Rows.Add(tableRow);
                    //                }
                    //                part.BodyItems.Add(table1);
                    //                bookmarksNavigator.ReplaceBookmarkContent(part);
                    //                break;
                    //            }
                    //        }
                    //    }
                    //    //教学记录
                    //    if (sys_class_info[T].CLASS_ID.ToUpper() == "PMS_TEACH_LIST")
                    //    {
                    //        for (int i = 0; i < part.BodyItems.Count; i++)
                    //        {
                    //            if (part.BodyItems[i].ToString() == "Spire.Doc.Table")
                    //            {
                    //                List<PMS_TEACH_LIST> PMS_TEACH_LIST = _soa.Db.Queryable<PMS_TEACH_LIST>().Where(p => p.PERSON_ID == person_id && p.CHECK_STATE == "2").Select<PMS_TEACH_LIST>().ToList();
                    //                table1 = (Spire.Doc.Table)part.BodyItems[i];
                    //                int starIndex = table1.Rows.Count - 1;
                    //                if (PMS_TEACH_LIST.Count > 0)
                    //                {
                    //                    for (int year = 0; year < PMS_TEACH_LIST.Count; year++)
                    //                    {
                    //                        tableRow = table1.Rows[starIndex].Clone();
                    //                        for (int j = 0; j < tableRow.Cells.Count; j++)//列循环
                    //                        {
                    //                            tableRow.Cells[j].Paragraphs[0].Text = "";
                    //                            Paragraph p = tableRow.Cells[j].Paragraphs[0];
                    //                            TextRange textRange = null;
                    //                            if (j == 0)
                    //                            {
                    //                                textRange = p.AppendText(PMS_TEACH_LIST[year].TEACH_YEAR.ToString());
                    //                            }
                    //                            if (j == 1)
                    //                            {
                    //                                textRange = p.AppendText(PMS_TEACH_LIST[year].TEACH_NAME);
                    //                            }
                    //                            if (j == 2)
                    //                            {
                    //                                textRange = p.AppendText(PMS_TEACH_LIST[year].TEACH_HOUR + "/" + PMS_TEACH_LIST[year].TEACH_PNUM);
                    //                            }
                    //                            textRange.ApplyCharacterFormat(p.BreakCharacterFormat);//保持表头样式
                    //                            textRange.CharacterFormat.FontSize = 10;
                    //                            textRange.CharacterFormat.Bold = false;
                    //                        }
                    //                        table1.Rows.Add(tableRow);
                    //                    }
                    //                }
                    //                else
                    //                {
                    //                    tableRow = table1.Rows[starIndex].Clone();
                    //                    for (int j = 0; j < tableRow.Cells.Count; j++)//列循环
                    //                    {
                    //                        tableRow.Cells[j].Paragraphs[0].Text = "";
                    //                        Paragraph p = tableRow.Cells[j].Paragraphs[0];
                    //                        if (j == 0)
                    //                        {
                    //                            tableRow.Cells[j].Paragraphs[0].Text = "暂无数据";
                    //                        }
                    //                    }
                    //                    table1.Rows.Add(tableRow);
                    //                }
                    //                part.BodyItems.Add(table1);
                    //                bookmarksNavigator.ReplaceBookmarkContent(part);
                    //                break;
                    //            }
                    //        }
                    //    }
                    //    //进修记录
                    //    if (sys_class_info[T].CLASS_ID.ToUpper() == "PMS_STUDY_LIST")
                    //    {
                    //        for (int i = 0; i < part.BodyItems.Count; i++)
                    //        {
                    //            if (part.BodyItems[i].ToString() == "Spire.Doc.Table")
                    //            {
                    //                List<PMS_STUDY_LIST> PMS_STUDY_LIST = _soa.Db.Queryable<PMS_STUDY_LIST>().Where(p => p.PERSON_ID == person_id && p.CHECK_STATE == "2").Select<PMS_STUDY_LIST>().ToList();
                    //                table1 = (Spire.Doc.Table)part.BodyItems[i];
                    //                int starIndex = table1.Rows.Count - 1;

                    //                if (PMS_STUDY_LIST.Count > 0)
                    //                {
                    //                    for (int year = 0; year < PMS_STUDY_LIST.Count; year++)
                    //                    {
                    //                        tableRow = table1.Rows[starIndex].Clone();
                    //                        for (int j = 0; j < tableRow.Cells.Count; j++)//列循环
                    //                        {
                    //                            tableRow.Cells[j].Paragraphs[0].Text = "";
                    //                            Paragraph p = tableRow.Cells[j].Paragraphs[0];
                    //                            TextRange textRange = null;
                    //                            if (j == 0)
                    //                            {
                    //                                textRange = p.AppendText(PMS_STUDY_LIST[year].STUDY_DURATION.ToString() + "年");
                    //                            }
                    //                            if (j == 1)
                    //                            {
                    //                                textRange = p.AppendText(PMS_STUDY_LIST[year].STUDY_UNIT);
                    //                            }
                    //                            if (j == 2)
                    //                            {
                    //                                textRange = p.AppendText(PMS_STUDY_LIST[year].STUDY_DEPT);
                    //                            }
                    //                            if (j == 3)
                    //                            {
                    //                                textRange = p.AppendText(PMS_STUDY_LIST[year].STUDY_CONTENT);
                    //                            }
                    //                            textRange.ApplyCharacterFormat(p.BreakCharacterFormat);//保持表头样式
                    //                            textRange.CharacterFormat.FontSize = 10;
                    //                            textRange.CharacterFormat.Bold = false;
                    //                        }
                    //                        table1.Rows.Add(tableRow);
                    //                    }
                    //                }
                    //                else
                    //                {
                    //                    tableRow = table1.Rows[starIndex].Clone();
                    //                    for (int j = 0; j < tableRow.Cells.Count; j++)//列循环
                    //                    {
                    //                        tableRow.Cells[j].Paragraphs[0].Text = "";
                    //                        Paragraph p = tableRow.Cells[j].Paragraphs[0];
                    //                        if (j == 0)
                    //                        {
                    //                            tableRow.Cells[j].Paragraphs[0].Text = "暂无数据";
                    //                        }
                    //                    }
                    //                    table1.Rows.Add(tableRow);
                    //                }
                    //                part.BodyItems.Add(table1);
                    //                bookmarksNavigator.ReplaceBookmarkContent(part);
                    //                break;
                    //            }
                    //        }
                    //    }
                    //    //课题记录
                    //    if (sys_class_info[T].CLASS_ID.ToUpper() == "PMS_RESEARCH_LIST")
                    //    {
                    //        for (int i = 0; i < part.BodyItems.Count; i++)
                    //        {
                    //            if (part.BodyItems[i].ToString() == "Spire.Doc.Table")
                    //            {
                    //                List<PMS_RESEARCH_LIST> PMS_RESEARCH_LIST = _soa.Db.Queryable<PMS_RESEARCH_LIST>().Where(p => p.PERSON_ID == person_id && p.CHECK_STATE == "2").Select<PMS_RESEARCH_LIST>().ToList();
                    //                table1 = (Spire.Doc.Table)part.BodyItems[i];
                    //                int starIndex = table1.Rows.Count - 1;
                    //                if (PMS_RESEARCH_LIST.Count > 0)
                    //                {
                    //                    for (int year = 0; year < PMS_RESEARCH_LIST.Count; year++)
                    //                    {
                    //                        tableRow = table1.Rows[starIndex].Clone();
                    //                        for (int j = 0; j < tableRow.Cells.Count; j++)//列循环
                    //                        {
                    //                            tableRow.Cells[j].Paragraphs[0].Text = "";
                    //                            Paragraph p = tableRow.Cells[j].Paragraphs[0];
                    //                            TextRange textRange = null;
                    //                            if (j == 0)
                    //                            {
                    //                                textRange = p.AppendText(PMS_RESEARCH_LIST[year].RESEARCH_YEAR.ToString());
                    //                            }
                    //                            if (j == 1)
                    //                            {
                    //                                textRange = p.AppendText(RecordClassBaseName("课题分类", PMS_RESEARCH_LIST[year].RESEARCH_CLASS));
                    //                            }
                    //                            if (j == 2)
                    //                            {
                    //                                textRange = p.AppendText(RecordClassBaseName("课题级别", PMS_RESEARCH_LIST[year].RESEARCH_LEVEL));
                    //                            }
                    //                            if (j == 3)
                    //                            {
                    //                                textRange = p.AppendText(PMS_RESEARCH_LIST[year].RESEARCH_NAME);
                    //                            }
                    //                            if (j == 4)
                    //                            {
                    //                                textRange = p.AppendText(PMS_RESEARCH_LIST[year].RESEARCH_EXPENDITURE.ToString());
                    //                            }
                    //                            textRange.ApplyCharacterFormat(p.BreakCharacterFormat);//保持表头样式
                    //                            textRange.CharacterFormat.FontSize = 10;
                    //                            textRange.CharacterFormat.Bold = false;
                    //                        }
                    //                        table1.Rows.Add(tableRow);
                    //                    }
                    //                }
                    //                else
                    //                {
                    //                    tableRow = table1.Rows[starIndex].Clone();
                    //                    for (int j = 0; j < tableRow.Cells.Count; j++)//列循环
                    //                    {
                    //                        tableRow.Cells[j].Paragraphs[0].Text = "";
                    //                        Paragraph p = tableRow.Cells[j].Paragraphs[0];
                    //                        if (j == 0)
                    //                        {
                    //                            tableRow.Cells[j].Paragraphs[0].Text = "暂无数据";
                    //                        }
                    //                    }
                    //                    table1.Rows.Add(tableRow);
                    //                }
                    //                part.BodyItems.Add(table1);
                    //                bookmarksNavigator.ReplaceBookmarkContent(part);
                    //                break;
                    //            }
                    //        }
                    //    }
                    //    //论文记录
                    //    if (sys_class_info[T].CLASS_ID.ToUpper() == "PMS_THESIS_LIST")
                    //    {
                    //        for (int i = 0; i < part.BodyItems.Count; i++)
                    //        {
                    //            if (part.BodyItems[i].ToString() == "Spire.Doc.Table")
                    //            {
                    //                List<PMS_THESIS_LIST> PMS_THESIS_LIST = _soa.Db.Queryable<PMS_THESIS_LIST>().Where(p => p.PERSON_ID == person_id && p.CHECK_STATE == "2").Select<PMS_THESIS_LIST>().ToList();
                    //                table1 = (Spire.Doc.Table)part.BodyItems[i];
                    //                int starIndex = table1.Rows.Count - 1;
                    //                if (PMS_THESIS_LIST.Count > 0)
                    //                {
                    //                    for (int year = 0; year < PMS_THESIS_LIST.Count; year++)
                    //                    {
                    //                        tableRow = table1.Rows[starIndex].Clone();
                    //                        for (int j = 0; j < tableRow.Cells.Count; j++)//列循环
                    //                        {
                    //                            tableRow.Cells[j].Paragraphs[0].Text = "";
                    //                            Paragraph p = tableRow.Cells[j].Paragraphs[0];
                    //                            TextRange textRange = null;
                    //                            if (j == 0)
                    //                            {
                    //                                textRange = p.AppendText(RecordClassBaseName("论著类型", PMS_THESIS_LIST[year].THESIS_TYPE));
                    //                            }
                    //                            if (j == 1)
                    //                            {
                    //                                textRange = p.AppendText(PMS_THESIS_LIST[year].THESIS_PUBLISH);
                    //                            }
                    //                            if (j == 2)
                    //                            {
                    //                                textRange = p.AppendText(PMS_THESIS_LIST[year].ISSUED_TIME.ToString());
                    //                            }
                    //                            if (j == 3)
                    //                            {
                    //                                textRange = p.AppendText(PMS_THESIS_LIST[year].THESIS_ITEM_NAME);
                    //                            }
                    //                            if (j == 4)
                    //                            {
                    //                                textRange = p.AppendText(RecordClassBaseName("是否SCI记录", PMS_THESIS_LIST[year].IF_SCI));
                    //                            }
                    //                            if (j == 5)
                    //                            {
                    //                                textRange = p.AppendText(PMS_THESIS_LIST[year].JCR_RANGE);
                    //                            }
                    //                            if (j == 6)
                    //                            {
                    //                                textRange = p.AppendText(PMS_THESIS_LIST[year].IF_VALUE);
                    //                            }
                    //                            if (j == 7)
                    //                            {
                    //                                textRange = p.AppendText(RecordClassBaseName("论著作者排序", PMS_THESIS_LIST[year].THESIS_PERSON_SORT));
                    //                            }
                    //                            textRange.ApplyCharacterFormat(p.BreakCharacterFormat);//保持表头样式
                    //                            textRange.CharacterFormat.FontSize = 10;
                    //                            textRange.CharacterFormat.Bold = false;
                    //                        }
                    //                        table1.Rows.Add(tableRow);
                    //                    }
                    //                }
                    //                else
                    //                {
                    //                    tableRow = table1.Rows[starIndex].Clone();
                    //                    for (int j = 0; j < tableRow.Cells.Count; j++)//列循环
                    //                    {
                    //                        tableRow.Cells[j].Paragraphs[0].Text = "";
                    //                        Paragraph p = tableRow.Cells[j].Paragraphs[0];
                    //                        if (j == 0)
                    //                        {
                    //                            tableRow.Cells[j].Paragraphs[0].Text = "暂无数据";
                    //                        }
                    //                    }
                    //                    table1.Rows.Add(tableRow);
                    //                }
                    //                part.BodyItems.Add(table1);
                    //                bookmarksNavigator.ReplaceBookmarkContent(part);
                    //                break;
                    //            }
                    //        }
                    //    }
                    //    //教育记录
                    //    if (sys_class_info[T].CLASS_ID.ToUpper() == "PMS_EDUCATION_LIST")
                    //    {
                    //        for (int i = 0; i < part.BodyItems.Count; i++)
                    //        {
                    //            if (part.BodyItems[i].ToString() == "Spire.Doc.Table")
                    //            {
                    //                List<PMS_EDUCATION_LIST> PMS_EDUCATION_LIST = _soa.Db.Queryable<PMS_EDUCATION_LIST>().Where(p => p.PERSON_ID == person_id && p.CHECK_STATE == "2").Select<PMS_EDUCATION_LIST>().ToList();
                    //                table1 = (Spire.Doc.Table)part.BodyItems[i];
                    //                int starIndex = table1.Rows.Count - 1;
                    //                if (PMS_EDUCATION_LIST.Count > 0)
                    //                {
                    //                    for (int year = 0; year < PMS_EDUCATION_LIST.Count; year++)
                    //                    {
                    //                        tableRow = table1.Rows[starIndex].Clone();
                    //                        for (int j = 0; j < tableRow.Cells.Count; j++)//列循环
                    //                        {
                    //                            tableRow.Cells[j].Paragraphs[0].Text = "";
                    //                            Paragraph p = tableRow.Cells[j].Paragraphs[0];
                    //                            TextRange textRange = null;
                    //                            if (j == 0)
                    //                            {
                    //                                textRange = p.AppendText(RecordClassBaseName("最高学历", PMS_EDUCATION_LIST[year].EDUCATION_BACKGROUND));
                    //                            }
                    //                            if (j == 1)
                    //                            {
                    //                                textRange = p.AppendText(RecordClassBaseName("最高学位", PMS_EDUCATION_LIST[year].EDUCATION_DEGREE));
                    //                            }
                    //                            if (j == 2)
                    //                            {
                    //                                textRange = p.AppendText(RecordClassBaseName("学位类型", PMS_EDUCATION_LIST[year].DEGREE_TYPE));
                    //                            }
                    //                            if (j == 3)
                    //                            {
                    //                                textRange = p.AppendText(PMS_EDUCATION_LIST[year].ENROLLMENT_TIME);
                    //                            }
                    //                            if (j == 4)
                    //                            {
                    //                                textRange = p.AppendText(PMS_EDUCATION_LIST[year].GRADUATE_TIME);
                    //                            }
                    //                            if (j == 5)
                    //                            {
                    //                                textRange = p.AppendText(PMS_EDUCATION_LIST[year].EDUCATION_SCHOOL);
                    //                            }
                    //                            if (j == 6)
                    //                            {
                    //                                textRange = p.AppendText(PMS_EDUCATION_LIST[year].RESEARCH_DIRECTION);
                    //                            }
                    //                            if (j == 7)
                    //                            {
                    //                                textRange = p.AppendText(PMS_EDUCATION_LIST[year].EDUCATION_PROFESSION);
                    //                            }
                    //                            if (j == 8)
                    //                            {
                    //                                textRange = p.AppendText(RecordClassBaseName("学历性质", PMS_EDUCATION_LIST[year].ACADEMIC_PROPERTY));
                    //                            }

                    //                            textRange.ApplyCharacterFormat(p.BreakCharacterFormat);//保持表头样式
                    //                            textRange.CharacterFormat.FontSize = 10;
                    //                            textRange.CharacterFormat.Bold = false;
                    //                        }
                    //                        table1.Rows.Add(tableRow);
                    //                    }
                    //                }
                    //                else
                    //                {
                    //                    tableRow = table1.Rows[starIndex].Clone();
                    //                    for (int j = 0; j < tableRow.Cells.Count; j++)//列循环
                    //                    {
                    //                        tableRow.Cells[j].Paragraphs[0].Text = "";
                    //                        Paragraph p = tableRow.Cells[j].Paragraphs[0];
                    //                        if (j == 0)
                    //                        {
                    //                            tableRow.Cells[j].Paragraphs[0].Text = "暂无数据";
                    //                        }
                    //                    }
                    //                    table1.Rows.Add(tableRow);
                    //                }
                    //                part.BodyItems.Add(table1);
                    //                bookmarksNavigator.ReplaceBookmarkContent(part);
                    //                break;
                    //            }
                    //        }
                    //    }
                    //    //技能证书记录
                    //    if (sys_class_info[T].CLASS_ID.ToUpper() == "PMS_SKILL_CERTIFICATE_LIST")
                    //    {
                    //        for (int i = 0; i < part.BodyItems.Count; i++)
                    //        {
                    //            if (part.BodyItems[i].ToString() == "Spire.Doc.Table")
                    //            {
                    //                List<PMS_SKILL_CERTIFICATE_LIST> PMS_SKILL_CERTIFICATE_LIST = _soa.Db.Queryable<PMS_SKILL_CERTIFICATE_LIST>().Where(p => p.PERSON_ID == person_id
                    //                && p.CHECK_STATE == "2").Select<PMS_SKILL_CERTIFICATE_LIST>().ToList();
                    //                table1 = (Spire.Doc.Table)part.BodyItems[i];
                    //                int starIndex = table1.Rows.Count - 1;
                    //                if (PMS_SKILL_CERTIFICATE_LIST.Count > 0)
                    //                {
                    //                    for (int year = 0; year < PMS_SKILL_CERTIFICATE_LIST.Count; year++)
                    //                    {
                    //                        tableRow = table1.Rows[starIndex].Clone();
                    //                        for (int j = 0; j < tableRow.Cells.Count; j++)//列循环
                    //                        {
                    //                            tableRow.Cells[j].Paragraphs[0].Text = "";
                    //                            Paragraph p = tableRow.Cells[j].Paragraphs[0];
                    //                            TextRange textRange = null;
                    //                            if (j == 0)
                    //                            {
                    //                                textRange = p.AppendText(PMS_SKILL_CERTIFICATE_LIST[year].CERTIFICATE_NAME);
                    //                            }
                    //                            if (j == 1)
                    //                            {
                    //                                textRange = p.AppendText(PMS_SKILL_CERTIFICATE_LIST[year].CERTIFICATE_UNIT);
                    //                            }
                    //                            if (j == 2)
                    //                            {
                    //                                textRange = p.AppendText(PMS_SKILL_CERTIFICATE_LIST[year].OBTAIN_TIME.ToString());
                    //                            }
                    //                            if (j == 3)
                    //                            {
                    //                                textRange = p.AppendText(PMS_SKILL_CERTIFICATE_LIST[year].CERTIFICATE_DATE.ToString());
                    //                            }
                    //                            if (j == 4)
                    //                            {
                    //                                textRange = p.AppendText(PMS_SKILL_CERTIFICATE_LIST[year].CERTIFICATE_NUMBER);
                    //                            }
                    //                            textRange.ApplyCharacterFormat(p.BreakCharacterFormat);//保持表头样式
                    //                            textRange.CharacterFormat.FontSize = 10;
                    //                            textRange.CharacterFormat.Bold = false;
                    //                        }
                    //                        table1.Rows.Add(tableRow);
                    //                    }
                    //                }
                    //                else
                    //                {
                    //                    tableRow = table1.Rows[starIndex].Clone();
                    //                    for (int j = 0; j < tableRow.Cells.Count; j++)//列循环
                    //                    {
                    //                        tableRow.Cells[j].Paragraphs[0].Text = "";
                    //                        Paragraph p = tableRow.Cells[j].Paragraphs[0];
                    //                        if (j == 0)
                    //                        {
                    //                            tableRow.Cells[j].Paragraphs[0].Text = "暂无数据";
                    //                        }
                    //                    }
                    //                    table1.Rows.Add(tableRow);
                    //                    //section.AddParagraph();
                    //                    //Paragraph para = section.AddParagraph();
                    //                    //para.Format.HorizontalAlignment = HorizontalAlignment.Center;//居中
                    //                    //TextRange tr1 = para.AppendText("暂无数据");
                    //                }
                    //                part.BodyItems.Add(table1);
                    //                bookmarksNavigator.ReplaceBookmarkContent(part);
                    //                break;
                    //            }
                    //        }
                    //    }
                    //    //培训记录
                    //    if (sys_class_info[T].CLASS_ID.ToUpper() == "PMS_TRAIN_LIST")
                    //    {
                    //        for (int i = 0; i < part.BodyItems.Count; i++)
                    //        {
                    //            if (part.BodyItems[i].ToString() == "Spire.Doc.Table")
                    //            {
                    //                List<PMS_TRAIN_LIST> PMS_TRAIN_LIST = _soa.Db.Queryable<PMS_TRAIN_LIST>().Where(p => p.PERSON_ID == person_id && p.CHECK_STATE == "2").Select<PMS_TRAIN_LIST>().ToList();
                    //                table1 = (Spire.Doc.Table)part.BodyItems[i];
                    //                int starIndex = table1.Rows.Count - 1;
                    //                if (PMS_TRAIN_LIST.Count > 0)
                    //                {
                    //                    for (int year = 0; year < PMS_TRAIN_LIST.Count; year++)
                    //                    {
                    //                        tableRow = table1.Rows[starIndex].Clone();
                    //                        for (int j = 0; j < tableRow.Cells.Count; j++)//列循环
                    //                        {
                    //                            tableRow.Cells[j].Paragraphs[0].Text = "";
                    //                            Paragraph p = tableRow.Cells[j].Paragraphs[0];
                    //                            TextRange textRange = null;
                    //                            if (j == 0)
                    //                            {
                    //                                textRange = p.AppendText(PMS_TRAIN_LIST[year].TRAIN_UNIT);
                    //                            }
                    //                            if (j == 1)
                    //                            {
                    //                                textRange = p.AppendText(PMS_TRAIN_LIST[year].TRAIN_START_TIME.ToString());
                    //                            }
                    //                            if (j == 2)
                    //                            {
                    //                                textRange = p.AppendText(PMS_TRAIN_LIST[year].TRAIN_END_TIME.ToString());
                    //                            }
                    //                            if (j == 3)
                    //                            {
                    //                                textRange = p.AppendText(PMS_TRAIN_LIST[year].TRAIN_CONTENT);
                    //                            }
                    //                            if (j == 4)
                    //                            {
                    //                                textRange = p.AppendText(PMS_TRAIN_LIST[year].TRAIN_SCORE.ToString());
                    //                            }
                    //                            if (j == 5)
                    //                            {
                    //                                textRange = p.AppendText(RecordClassBaseName("学分类型", PMS_TRAIN_LIST[year].SCORE_TYPE));
                    //                            }
                    //                            if (j == 6)
                    //                            {
                    //                                textRange = p.AppendText(PMS_TRAIN_LIST[year].IF_FINISH == "1" ? "是" : "否");
                    //                            }
                    //                            textRange.ApplyCharacterFormat(p.BreakCharacterFormat);//保持表头样式
                    //                            textRange.CharacterFormat.FontSize = 10;
                    //                            textRange.CharacterFormat.Bold = false;
                    //                        }
                    //                        table1.Rows.Add(tableRow);
                    //                    }
                    //                }
                    //                else
                    //                {
                    //                    tableRow = table1.Rows[starIndex].Clone();
                    //                    for (int j = 0; j < tableRow.Cells.Count; j++)//列循环
                    //                    {
                    //                        tableRow.Cells[j].Paragraphs[0].Text = "";
                    //                        Paragraph p = tableRow.Cells[j].Paragraphs[0];
                    //                        if (j == 0)
                    //                        {
                    //                            tableRow.Cells[j].Paragraphs[0].Text = "暂无数据";
                    //                        }
                    //                    }
                    //                    table1.Rows.Add(tableRow);
                    //                }
                    //                part.BodyItems.Add(table1);
                    //                bookmarksNavigator.ReplaceBookmarkContent(part);
                    //                break;
                    //            }
                    //        }
                    //    }
                    //    //社会任职记录
                    //    if (sys_class_info[T].CLASS_ID.ToUpper() == "PMS_SOCIAL_OFFICE_LIST")
                    //    {
                    //        for (int i = 0; i < part.BodyItems.Count; i++)
                    //        {
                    //            if (part.BodyItems[i].ToString() == "Spire.Doc.Table")
                    //            {
                    //                List<PMS_SOCIAL_OFFICE_LIST> PMS_SOCIAL_OFFICE_LIST = _soa.Db.Queryable<PMS_SOCIAL_OFFICE_LIST>().Where(p => p.PERSON_ID == person_id && p.CHECK_STATE == "2").Select<PMS_SOCIAL_OFFICE_LIST>().ToList();
                    //                table1 = (Spire.Doc.Table)part.BodyItems[i];
                    //                int starIndex = table1.Rows.Count - 1;
                    //                if (PMS_SOCIAL_OFFICE_LIST.Count > 0)
                    //                {
                    //                    for (int year = 0; year < PMS_SOCIAL_OFFICE_LIST.Count; year++)
                    //                    {
                    //                        tableRow = table1.Rows[starIndex].Clone();
                    //                        for (int j = 0; j < tableRow.Cells.Count; j++)//列循环
                    //                        {
                    //                            tableRow.Cells[j].Paragraphs[0].Text = "";
                    //                            Paragraph p = tableRow.Cells[j].Paragraphs[0];
                    //                            TextRange textRange = null;
                    //                            if (j == 0)
                    //                            {
                    //                                textRange = p.AppendText(PMS_SOCIAL_OFFICE_LIST[year].SOFFICE_UNIT);
                    //                            }
                    //                            if (j == 1)
                    //                            {
                    //                                textRange = p.AppendText(RecordClassBaseName("任职机构等级", PMS_SOCIAL_OFFICE_LIST[year].SOFFICE_LEVEL));
                    //                            }
                    //                            if (j == 2)
                    //                            {
                    //                                textRange = p.AppendText(RecordClassBaseName("任职职务", PMS_SOCIAL_OFFICE_LIST[year].SOFFICE_POST));
                    //                            }
                    //                            if (j == 3)
                    //                            {
                    //                                textRange = p.AppendText(PMS_SOCIAL_OFFICE_LIST[year].SOFFICE_START_TIME?.ToString() ?? "");
                    //                            }
                    //                            if (j == 4)
                    //                            {
                    //                                string text = string.Empty;
                    //                                if (PMS_SOCIAL_OFFICE_LIST[year].SOFFICE_LEAVE_TIME == null)
                    //                                {
                    //                                    text = "";
                    //                                }
                    //                                else
                    //                                {
                    //                                    text = PMS_SOCIAL_OFFICE_LIST[year].SOFFICE_LEAVE_TIME.ToString();
                    //                                }
                    //                                textRange = p.AppendText(text);
                    //                            }
                    //                            if (j == 5)
                    //                            {
                    //                                textRange = p.AppendText(PMS_SOCIAL_OFFICE_LIST[year].SOFFICE_DURATION.ToString() + "年");
                    //                            }
                    //                            if (j == 6)
                    //                            {
                    //                                textRange = p.AppendText(PMS_SOCIAL_OFFICE_LIST[year].IF_SOFFICE == "1" ? "是" : "否");
                    //                            }
                    //                            textRange.ApplyCharacterFormat(p.BreakCharacterFormat);//保持表头样式
                    //                            textRange.CharacterFormat.FontSize = 10;
                    //                            textRange.CharacterFormat.Bold = false;
                    //                        }
                    //                        table1.Rows.Add(tableRow);
                    //                    }
                    //                }
                    //                else
                    //                {
                    //                    tableRow = table1.Rows[starIndex].Clone();
                    //                    for (int j = 0; j < tableRow.Cells.Count; j++)//列循环
                    //                    {
                    //                        tableRow.Cells[j].Paragraphs[0].Text = "";
                    //                        Paragraph p = tableRow.Cells[j].Paragraphs[0];
                    //                        if (j == 0)
                    //                        {
                    //                            tableRow.Cells[j].Paragraphs[0].Text = "暂无数据";
                    //                        }
                    //                    }
                    //                    table1.Rows.Add(tableRow);
                    //                }
                    //                part.BodyItems.Add(table1);
                    //                bookmarksNavigator.ReplaceBookmarkContent(part);
                    //                break;
                    //            }
                    //        }
                    //    }
                    //    //知识产权记录
                    //    if (sys_class_info[T].CLASS_ID.ToUpper() == "PMS_INTELLECTUAL_LIST")
                    //    {
                    //        for (int i = 0; i < part.BodyItems.Count; i++)
                    //        {
                    //            if (part.BodyItems[i].ToString() == "Spire.Doc.Table")
                    //            {
                    //                List<PMS_INTELLECTUAL_LIST> PMS_INTELLECTUAL_LIST = _soa.Db.Queryable<PMS_INTELLECTUAL_LIST>().Where(p => p.PERSON_ID == person_id && p.CHECK_STATE == "2").Select<PMS_INTELLECTUAL_LIST>().ToList();
                    //                table1 = (Spire.Doc.Table)part.BodyItems[i];
                    //                int starIndex = table1.Rows.Count - 1;
                    //                if (PMS_INTELLECTUAL_LIST.Count > 0)
                    //                {
                    //                    for (int year = 0; year < PMS_INTELLECTUAL_LIST.Count; year++)
                    //                    {
                    //                        tableRow = table1.Rows[starIndex].Clone();
                    //                        for (int j = 0; j < tableRow.Cells.Count; j++)//列循环
                    //                        {
                    //                            tableRow.Cells[j].Paragraphs[0].Text = "";
                    //                            Paragraph p = tableRow.Cells[j].Paragraphs[0];
                    //                            TextRange textRange = null;
                    //                            if (j == 0)
                    //                            {
                    //                                textRange = p.AppendText(RecordClassBaseName("知识产权分类", PMS_INTELLECTUAL_LIST[year].INTELLECTUAL_CLASS));
                    //                            }
                    //                            if (j == 1)
                    //                            {
                    //                                textRange = p.AppendText(PMS_INTELLECTUAL_LIST[year].INTELLECTUAL_NAME);
                    //                            }
                    //                            if (j == 2)
                    //                            {
                    //                                textRange = p.AppendText(PMS_INTELLECTUAL_LIST[year].INTELLECTUAL_DATE.ToString());
                    //                            }
                    //                            if (j == 3)
                    //                            {
                    //                                textRange = p.AppendText(PMS_INTELLECTUAL_LIST[year].INTELLECTUAL_SORT);
                    //                            }
                    //                            textRange.ApplyCharacterFormat(p.BreakCharacterFormat);//保持表头样式
                    //                            textRange.CharacterFormat.FontSize = 10;
                    //                            textRange.CharacterFormat.Bold = false;
                    //                        }
                    //                        table1.Rows.Add(tableRow);
                    //                    }
                    //                }
                    //                else
                    //                {
                    //                    tableRow = table1.Rows[starIndex].Clone();
                    //                    for (int j = 0; j < tableRow.Cells.Count; j++)//列循环
                    //                    {
                    //                        tableRow.Cells[j].Paragraphs[0].Text = "";
                    //                        Paragraph p = tableRow.Cells[j].Paragraphs[0];
                    //                        if (j == 0)
                    //                        {
                    //                            tableRow.Cells[j].Paragraphs[0].Text = "暂无数据";
                    //                        }
                    //                    }
                    //                    table1.Rows.Add(tableRow);
                    //                }
                    //                part.BodyItems.Add(table1);
                    //                bookmarksNavigator.ReplaceBookmarkContent(part);
                    //                break;
                    //            }
                    //        }
                    //    }
                    //    //外派记录
                    //    if (sys_class_info[T].CLASS_ID.ToUpper() == "PMS_EXCHANGE_LIST")
                    //    {
                    //        for (int i = 0; i < part.BodyItems.Count; i++)
                    //        {
                    //            if (part.BodyItems[i].ToString() == "Spire.Doc.Table")
                    //            {
                    //                List<PMS_EXCHANGE_LIST> PMS_EXCHANGE_LIST = _soa.Db.Queryable<PMS_EXCHANGE_LIST>().Where(p => p.PERSON_ID == person_id && p.CHECK_STATE == "2").Select<PMS_EXCHANGE_LIST>().ToList();
                    //                table1 = (Spire.Doc.Table)part.BodyItems[i];
                    //                int starIndex = table1.Rows.Count - 1;
                    //                if (PMS_EXCHANGE_LIST.Count > 0)
                    //                {
                    //                    tableRow = table1.Rows[starIndex].Clone();
                    //                    for (int year = 0; year < PMS_EXCHANGE_LIST.Count; year++)
                    //                    {
                    //                        for (int j = 0; j < tableRow.Cells.Count; j++)//列循环
                    //                        {
                    //                            tableRow.Cells[j].Paragraphs[0].Text = "";
                    //                            Paragraph p = tableRow.Cells[j].Paragraphs[0];
                    //                            TextRange textRange = null;
                    //                            if (j == 0)
                    //                            {
                    //                                textRange = p.AppendText(PMS_EXCHANGE_LIST[year].EXCHANGE_UNIT);
                    //                            }
                    //                            if (j == 1)
                    //                            {
                    //                                textRange = p.AppendText(PMS_EXCHANGE_LIST[year].EXCHANGE_GOAL);
                    //                            }
                    //                            if (j == 2)
                    //                            {
                    //                                textRange = p.AppendText(PMS_EXCHANGE_LIST[year].EXCHANGE_CONTENT);
                    //                            }
                    //                            if (j == 3)
                    //                            {
                    //                                PMS_EXCHANGE_LIST[year].EXCHANGE_TIME = PMS_EXCHANGE_LIST[year].EXCHANGE_START_DATE == null ? "" : PMS_EXCHANGE_LIST[year].EXCHANGE_START_DATE.Value.ToString("yyyy-MM-dd");
                    //                                textRange = p.AppendText(PMS_EXCHANGE_LIST[year].EXCHANGE_TIME);
                    //                            }
                    //                            if (j == 4)
                    //                            {
                    //                                textRange = p.AppendText(RecordClassBaseName("交流级别", PMS_EXCHANGE_LIST[year].EXCHANGE_LEVEL));
                    //                            }
                    //                            textRange.ApplyCharacterFormat(p.BreakCharacterFormat);//保持表头样式
                    //                            textRange.CharacterFormat.FontSize = 10;
                    //                            textRange.CharacterFormat.Bold = false;
                    //                        }
                    //                        table1.Rows.Add(tableRow);
                    //                    }
                    //                }
                    //                else
                    //                {
                    //                    tableRow = table1.Rows[starIndex].Clone();
                    //                    for (int j = 0; j < tableRow.Cells.Count; j++)//列循环
                    //                    {
                    //                        tableRow.Cells[j].Paragraphs[0].Text = "";
                    //                        Paragraph p = tableRow.Cells[j].Paragraphs[0];
                    //                        if (j == 0)
                    //                        {
                    //                            tableRow.Cells[j].Paragraphs[0].Text = "暂无数据";
                    //                        }
                    //                    }
                    //                    table1.Rows.Add(tableRow);
                    //                }
                    //                part.BodyItems.Add(table1);
                    //                bookmarksNavigator.ReplaceBookmarkContent(part);
                    //                break;
                    //            }
                    //        }
                    //    }
                    //    //外派记录
                    //    if (sys_class_info[T].CLASS_ID.ToUpper() == "PMS_EXPATRIATE_LIST")
                    //    {
                    //        for (int i = 0; i < part.BodyItems.Count; i++)
                    //        {
                    //            if (part.BodyItems[i].ToString() == "Spire.Doc.Table")
                    //            {
                    //                List<PMS_EXPATRIATE_LIST> PMS_EXPATRIATE_LIST = _soa.Db.Queryable<PMS_EXPATRIATE_LIST>().Where(p => p.PERSON_ID == person_id && p.CHECK_STATE == "2").Select<PMS_EXPATRIATE_LIST>().ToList();
                    //                table1 = (Spire.Doc.Table)part.BodyItems[i];
                    //                int starIndex = table1.Rows.Count - 1;
                    //                if (PMS_EXPATRIATE_LIST.Count > 0)
                    //                {
                    //                    tableRow = table1.Rows[starIndex].Clone();
                    //                    for (int year = 0; year < PMS_EXPATRIATE_LIST.Count; year++)
                    //                    {
                    //                        for (int j = 0; j < tableRow.Cells.Count; j++)//列循环
                    //                        {
                    //                            tableRow.Cells[j].Paragraphs[0].Text = "";
                    //                            Paragraph p = tableRow.Cells[j].Paragraphs[0];
                    //                            TextRange textRange = null;
                    //                            if (j == 0)
                    //                            {
                    //                                textRange = p.AppendText(PMS_EXPATRIATE_LIST[year].EXPATRIATE_UNIT);
                    //                            }
                    //                            if (j == 1)
                    //                            {
                    //                                textRange = p.AppendText(RecordClassBaseName("外派性质", PMS_EXPATRIATE_LIST[year].EXPATRIATE));
                    //                            }
                    //                            if (j == 2)
                    //                            {
                    //                                textRange = p.AppendText(PMS_EXPATRIATE_LIST[year].EXPATRIATE_PERIOD + "年");
                    //                            }
                    //                            if (j == 3)
                    //                            {
                    //                                textRange = p.AppendText(PMS_EXPATRIATE_LIST[year].EXPATRIATE_START_DATE.ToString());
                    //                            }
                    //                            if (j == 4)
                    //                            {
                    //                                textRange = p.AppendText(PMS_EXPATRIATE_LIST[year].EXPATRIATE_END_DATE.ToString());
                    //                            }
                    //                            textRange.ApplyCharacterFormat(p.BreakCharacterFormat);//保持表头样式
                    //                            textRange.CharacterFormat.FontSize = 10;
                    //                            textRange.CharacterFormat.Bold = false;
                    //                        }
                    //                        table1.Rows.Add(tableRow);
                    //                    }
                    //                }
                    //                else
                    //                {
                    //                    tableRow = table1.Rows[starIndex].Clone();
                    //                    for (int j = 0; j < tableRow.Cells.Count; j++)//列循环
                    //                    {
                    //                        tableRow.Cells[j].Paragraphs[0].Text = "";
                    //                        Paragraph p = tableRow.Cells[j].Paragraphs[0];
                    //                        if (j == 0)
                    //                        {
                    //                            tableRow.Cells[j].Paragraphs[0].Text = "暂无数据";
                    //                        }
                    //                    }
                    //                    table1.Rows.Add(tableRow);
                    //                }
                    //                part.BodyItems.Add(table1);
                    //                bookmarksNavigator.ReplaceBookmarkContent(part);
                    //                break;
                    //            }
                    //        }
                    //    }
                    //}
                    //else
                    //{
                    //    //添加书签 “bookmark”
                    //    //  Paragraph paragraph = new Paragraph(document);
                    //    //section.AddParagraph();
                    //    //section.Paragraphs[section.Paragraphs.Count-1].AppendBookmarkStart("bookmark");
                    //    //section.Paragraphs[section.Paragraphs.Count].AppendBookmarkEnd("bookmark");

                    //    AddTable(section, sys_class_info[T], person_id, hospitalId);
                    //}
                    #endregion
                    (bool success, AutoClassProp autoTable) = AddTable(section, sys_class_info[T], person_id, hospitalId, tableSetting, itemCount);
                    if (success)
                        itemCount++;
                    if (autoTable.Prop != null && autoTable.Prop.Count > 0)
                        returnpPopSetting.Setting.Add(autoTable);
                }
                //Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                //.Information($"填充表格耗时:{sw.ElapsedMilliseconds}ms");
                //自动更新tablesetting
                if (File.Exists(settingPath))
                {
                    foreach (var item in returnpPopSetting.Setting)
                    {
                        AutoClassProp setting = tableSetting.Setting.Find(w => w.ClassCode == item.ClassCode);
                        if (setting != null)
                        {
                            foreach (var propIem in item.Prop)
                            {
                                Properties properties = setting.Prop.Find(w => w.Key == propIem.Key);
                                if (properties != null)
                                    propIem.IsShow = properties.IsShow;
                            }
                        }
                    }
                    string content = JsonConvert.SerializeObject(returnpPopSetting);
                    File.WriteAllText(settingPath, content);
                }
                //Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                // .Information($"写入TableSetting耗时:{sw.ElapsedMilliseconds}ms");
                // document.SaveToFile(contentRootPath + "ExampleFile/" + newFilePath, FileFormat.Docx);
                MemoryStream stream = new MemoryStream();
                document.SaveToStream(stream, FileFormat.PDF);
                //Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                //.Information($"SaveToStream耗时:{sw.ElapsedMilliseconds}ms");
                string filePath = contentRootPath + "ExampleFile/" + newFilePath;
                //   using var stream = new MemoryStream(System.IO.File.ReadAllBytes(filePath).ToArray());
                var formFile = new FormFile(stream, 0, stream.Length, "streamFile", filePath.Split(@"\").Last());
                UploadFileDto fileDto = new UploadFileDto();
                fileDto.FILE = formFile;
                fileDto.FILE_NAME = "PersonInfo.pdf";
                fileDto.FILE_SUFFIX = ".pdf";
                fileDto.FILE_NAME = newFilePath;
                fileDto.UPLOAD_FOLDER_NAME = OfficeHelper.PathCombine("PMS", person_id);
                fileDto.UPLOAD_FILE_NAME = newPdfFilePath;
                fileDto.SAVE_TO_S28 = true;
                fileDto.IFCOVER = true;
                //                Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                //.Information($"上传S28前耗时:{sw.ElapsedMilliseconds}ms");
                ResultDto result = _IUploadFileService.UploadFileOperate(fileDto);
                //                Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                //.Information($"上传S28后耗时:{sw.ElapsedMilliseconds}ms");
                string file_path = string.Empty;
                string filePreviewAddress = "";
                string filePreviewProxy = "";
                string msg = "生成失败！";
                if (result.success == true)
                {
                    // File.Delete(curPatn);
                    file_path = FileHttpUrl + result.data.ToString();
                    filePreviewAddress = file_preview_address + result.data.ToString();
                    filePreviewProxy = OfficeHelper.PathCombine("/S54", result.data.ToString());
                    msg = "生成成功！";
                }
                var strResult = new { FILE_PATH = file_path, MSG = msg, FILE_PREVIEW_ADDRESS = filePreviewAddress, FILE_PREVIEW_PROXY = filePreviewProxy, FILE_SOURCE_PATH = filePreviewAddress.Replace(".pdf", ".docx").Replace(".PDF", ".docx") };
                document.Dispose();//释放占用资源
                                   //sw.Stop();


                ResultDto dto = new ResultDto();
                dto.data = strResult;
                dto.data1 = returnpPopSetting;
                return dto;
            }
        }


        /// <summary>
        /// word生成pdf
        /// </summary>
        /// <param name="sourcePath"></param>
        /// <param name="targetPath"></param>
        /// <returns></returns>
        public byte[] WordToPdfBySpire(byte[] photoBytes, string sourcePath, string targetPath)
        {
            bool result = false;
            byte[] byteBase = null;
            Document doc = new Document();
            try
            {
                File.WriteAllBytes(sourcePath, photoBytes);
                //  doc.LoadFromFile(sourcePath);
                doc.LoadFromFile(sourcePath);
                ToPdfParameterList topdf = new ToPdfParameterList();
                topdf.CreateWordBookmarks = true;
                topdf.CreateWordBookmarksUsingHeadings = true;
                doc.SaveToFile(targetPath, topdf);
                result = true;
            }
            catch (Exception ex)
            {
            }
            finally
            {
                doc.Close();
                File.Delete(sourcePath);
                File.Delete(targetPath);
            }
            return byteBase;
        }


        /// <summary>
        /// 分类动态新增
        /// </summary>
        /// <param name="section"></param>
        /// <param name="classInfo">分类属性信息</param>
        /// <param name="personId">人员id</param>
        /// <param name="hospitalId">院区id </param>
        /// <param name="tableSetting">服务器设置 </param>
        private (bool, AutoClassProp) AddTable(Section section, PMS_ADDN_CLASS_INFO classInfo, string personId, string hospitalId, ClassPropSetting tableSetting, int Sort)
        {
            AutoClassProp AutoClassProp = new AutoClassProp();
            try
            {
                AutoClassProp setting = tableSetting?.Setting?.Find(w => w.ClassCode == classInfo.CLASS_ID);
                (DataTable dataTable, AutoClassProp) = CreateClassInfoTable(classInfo, personId, hospitalId);
                if (dataTable.Rows.Count <= 0 && dataTable.Columns.Count <= 1 && AutoClassProp.Prop.Count <= 0)
                    return (false, AutoClassProp);
                string path = Path.Combine(_hostingEnvironment.ContentRootPath, "ExampleFile", "icon.png");
                byte[] img = GetConvertIconType(path);
                //增加段落
                Paragraph paragraph = section.AddParagraph();
                //增加书签
                paragraph.AppendBookmarkStart("PO_" + classInfo.CLASS_ID.ToUpper());
                paragraph.AppendBookmarkEnd("PO_" + classInfo.CLASS_ID.ToUpper());
                DocPicture pic = paragraph.AppendPicture(img);
                DocPicture docPicture = null;
                Paragraph paragraphFirst = section.Paragraphs[0];
                foreach (DocumentObject item in paragraphFirst.ChildObjects)
                {
                    if (item.DocumentObjectType == DocumentObjectType.Picture)
                    {
                        docPicture = item as DocPicture;
                        break;
                    }
                }
                TextRange textPar = paragraph.AppendText("  " + $"{Sort}、{classInfo.CLASS_NAME}");
                textPar.ApplyCharacterFormat(paragraphFirst.BreakCharacterFormat);
                if (docPicture != null)
                {
                    pic.Width = docPicture.Width;
                    pic.Height = docPicture.Height;
                    pic.Rotation = docPicture.Rotation;
                    pic.ApplyCharacterFormat(docPicture.CharacterFormat);
                }

                textPar.CharacterFormat.TextColor = Color.FromArgb(227, 157, 10);
                textPar.CharacterFormat.FontName = "微软雅黑";
                textPar.CharacterFormat.Bold = true;
                //增加表格
                Spire.Doc.Table table = section.AddTable(true);
                int cellCount = AutoClassProp.Prop.Count;
                List<Properties> listProp = AutoClassProp.Prop;
                if (setting != null && setting.Prop.Count > 0)
                    listProp = setting.Prop;
                if (setting != null && setting.Prop.Count > 0)
                {
                    List<Properties> listHide = listProp.FindAll(w => w.IsShow == 0);
                    foreach (var item in listHide)
                    {
                        dataTable.Columns.Remove(item.Key);
                    }
                    listProp = listProp.FindAll(w => w.IsShow == 1);
                    cellCount = listProp.Count;
                    //当服务器josn配置未更新时 取最新配置
                    if (dataTable.Columns.Count != cellCount)
                        cellCount = dataTable.Columns.Count;
                    //float[] with = new float[setting.Prop.Count];
                    //for (int i = 0; i < setting.Prop.Count; i++)
                    //{
                    //    if (setting.Prop[i].Width > 0)
                    //        with[i] = setting.Prop[i].Width;
                    //    else
                    //        with[i] = 100;
                    //}
                    //table.ColumnWidth = with;
                }
                if (dataTable.Rows.Count > 0 && cellCount > 0)
                    table.ResetCells(dataTable.Rows.Count + 1, cellCount);
                else if (cellCount > 0)
                    table.ResetCells(2, cellCount);
                table.AutoFit(AutoFitBehaviorType.FixedColumnWidths);
                // 创建一个PreferredWidth对象，设置宽度类型为百分比，并设置宽度值为100%
                PreferredWidth percentageWidth = new PreferredWidth(WidthType.Percentage, (short)100);

                // 设置Table的首选宽度为上面创建的PreferredWidth对象
                table.PreferredWidth = percentageWidth;
                // ***************** First Row *************************
                TableRow row = table.Rows[0];

                row.IsHeader = true;
                row.Height = 33;
                row.HeightType = TableRowHeightType.Auto;
                float sumWidth = (float)listProp.Select(w => w.Width).Sum();
                //增加表头
                for (int i = 0; i < listProp.Count; i++)
                {
                    //string poropCode = listProp[i].Key;
                    //Properties properties = setting?.Prop?.Find(w => w.Key == poropCode);
                    ////设置不显示
                    //if (properties != null)
                    //{
                    //    if (!Convert.ToBoolean(properties.IsShow))
                    //        continue;
                    //    //if (properties.Width > 0)
                    //    //{
                    //    //    row.Cells[i].Width= properties.Width; 

                    //    //   // row.Cells[i].SetCellWidth(properties.Width, CellWidthType.Auto);
                    //    //}
                    //}
                    if (i < row.Cells.Count)
                    {
                        row.Cells[i].CellFormat.VerticalAlignment = VerticalAlignment.Middle;
                        //设置超出宽度自动换行
                        row.Cells[i].CellFormat.TextWrap = true;
                        if (listProp[i].Width > 0)
                            row.Cells[i].SetCellWidth((float)listProp[i].Width / sumWidth * 100, CellWidthType.Percentage);
                        //设置超出宽度自动换行
                        row.Cells[i].CellFormat.TextWrap = true;
                        // 设置当前单元格的边框样式为单线
                        row.Cells[i].CellFormat.Borders.BorderType = Spire.Doc.Documents.BorderStyle.Single;
                        // 设置边框的宽度
                        row.Cells[i].CellFormat.Borders.LineWidth = 1F;
                        // 设置边框的颜色 
                        row.Cells[i].CellFormat.Borders.Color = Color.DarkGray;
                        Paragraph p = row.Cells[i].AddParagraph();
                        //设置行距
                        p.Format.LineSpacing = 20;
                        p.Format.LineSpacingRule = LineSpacingRule.Exactly;
                        p.Format.HorizontalAlignment = Spire.Doc.Documents.HorizontalAlignment.Center;
                        TextRange txtRange = p.AppendText(listProp[i].Name);
                        txtRange.CharacterFormat.Bold = true;
                        txtRange.CharacterFormat.FontName = "微软雅黑";
                    }
                }
                //填充表格数据
                if (dataTable.Rows.Count > 0)
                {
                    for (int r = 0; r < dataTable.Rows.Count; r++)
                    {
                        DataRow dr = dataTable.Rows[r];
                        TableRow dataRow = table.Rows[r + 1];
                        dataRow.Height = 33;
                        dataRow.HeightType = TableRowHeightType.Auto;
                        dataRow.RowFormat.BackColor = Color.Empty;
                        for (int c = 0; c < dataTable.Columns.Count; c++)
                        {
                            Properties properties = listProp.Find(w => w.Key == dataTable.Columns[c].ColumnName);
                            ////设置不显示
                            if (properties != null)
                            {
                                //if (!Convert.ToBoolean(properties.IsShow))
                                //    continue;
                                if (properties.Width > 0)
                                {
                                    dataRow.Cells[c].SetCellWidth((float)properties.Width / sumWidth * 100, CellWidthType.Percentage);
                                }
                            }
                            dataRow.Cells[c].CellFormat.VerticalAlignment = VerticalAlignment.Middle;
                            //设置超出宽度自动换行
                            dataRow.Cells[c].CellFormat.TextWrap = true;

                            // 设置当前单元格的边框样式为单线
                            dataRow.Cells[c].CellFormat.Borders.BorderType = Spire.Doc.Documents.BorderStyle.Single;
                            // 设置边框的宽度
                            dataRow.Cells[c].CellFormat.Borders.LineWidth = 1F;
                            // 设置边框的颜色 
                            dataRow.Cells[c].CellFormat.Borders.Color = Color.DarkGray;
                            Paragraph p = dataRow.Cells[c].AddParagraph();
                            if (dataTable.Columns[c].ColumnName.Contains("_AFFIX") && dr[c] != null && dr[c].ToString().IsNotNullOrEmpty())
                            {
                                string filePath = Path.Combine(_hostingEnvironment.ContentRootPath, "ExampleFile", "file.png");
                                byte[] fileImage = GetConvertIconType(filePath);
                                DocPicture picture = p.AppendPicture(fileImage);
                                // 设置图片大小
                                picture.Width = 20; // 宽度
                                picture.Height = 20; // 高度
                                picture.TextWrappingStyle = TextWrappingStyle.Inline;
                                picture.WrapType = TextWrappingStyle.Square;
                                picture.HorizontalAlignment = ShapeHorizontalAlignment.Left;
                                picture.VerticalAlignment = ShapeVerticalAlignment.Center;
                                string linkUrl = $"https://preview?files={dr[c].ToString()}";
                                int count = dr[c].ToString().Split(new char[] { '+' })?.Length ?? 0;
                                p.AppendHyperlink(linkUrl, $"{count}个附件", HyperlinkType.WebLink);
                            }
                            else
                            {
                                //设置行距
                                p.Format.LineSpacing = 20;
                                p.Format.LineSpacingRule = LineSpacingRule.Exactly;
                                p.Format.HorizontalAlignment = Spire.Doc.Documents.HorizontalAlignment.Center;
                                TextRange textRange = p.AppendText(dr[c].ToString());
                                textRange.CharacterFormat.FontName = "微软雅黑";
                            }
                        }
                    }
                }
                else
                {
                    TableRow dataRow = table.Rows[1];
                    dataRow.Height = 33;
                    dataRow.HeightType = TableRowHeightType.Exactly;
                    dataRow.RowFormat.BackColor = Color.Empty;
                    foreach (TableCell item in dataRow.Cells)
                    {
                        // 设置当前单元格的边框样式为单线
                        item.CellFormat.Borders.BorderType = Spire.Doc.Documents.BorderStyle.Single;
                        // 设置边框的宽度
                        item.CellFormat.Borders.LineWidth = 1.0F;
                        // 设置边框的颜色 
                        item.CellFormat.Borders.Color = Color.DarkGray;
                    }

                    dataRow.Cells[0].CellFormat.VerticalAlignment = VerticalAlignment.Middle;
                    TextRange textRange = dataRow.Cells[0].AddParagraph().AppendText("暂无数据");
                    textRange.CharacterFormat.FontName = "微软雅黑";
                    textRange.CharacterFormat.Bold = true;

                }
            }
            catch (Exception ex)
            {
                Log.Error(ex.ToString());
                _logger.LogError(ex.ToString());
            }
            return (true, AutoClassProp);
        }

        /// <summary>
        /// 生成分类信息表格
        /// </summary>
        /// <param name="classInfo">分类信息</param>
        /// <param name="personId">人员id</param>
        /// <param name="hospitalId">医院id</param>
        /// <returns></returns>
        private (DataTable, AutoClassProp) CreateClassInfoTable(PMS_ADDN_CLASS_INFO classInfo, string personId, string hospitalId, bool isViewAll = false)
        {
            AutoClassProp AutoClassProp = new AutoClassProp();
            AutoClassProp.ClassName = classInfo.CLASS_NAME;
            AutoClassProp.ClassCode = classInfo.CLASS_ID;
            AutoClassProp.Prop = new List<Properties>();
            DataTable dataTable = new DataTable();
            dataTable.TableName = classInfo.CLASS_ID;
            var varMoudle = _soa.Db.Queryable<SYS6_MODULE_FUNC_DICT>().Where(p => p.SETUP_ID == classInfo.TABLE_SETUP_ID && p.HOSPITAL_ID == hospitalId)
.Select(s => new
{
    SETUP_ID = s.SETUP_ID,
    FORM_JSON = s.FORM_JSON,
    FORM_COL_JSON = s.FORM_COL_JSON

})?.First();
            if (varMoudle != null)
            {
                //查询工具箱表格属性获取表头
                List<PageSettingTableCol> listCol = JsonConvert.DeserializeObject<List<PageSettingTableCol>>(varMoudle.FORM_COL_JSON);
                dataTable.Columns.Add("NO");
                Properties propertiesNo = new Properties();
                propertiesNo.Name = "NO";
                propertiesNo.Key = "NO";
                propertiesNo.IsShow = 1;
                propertiesNo.Width = 50;
                AutoClassProp.Prop.Add(propertiesNo);
                foreach (PageSettingTableCol item in listCol)
                {
                    if (!isViewAll)
                    {
                        if (item.headerName != "排序号" && item.hide != null && !item.hide.Value)
                        {
                            Properties properties = new Properties();
                            properties.Name = item.headerName;
                            properties.Key = item.field;
                            properties.IsShow = 1;
                            properties.Width = item.width;
                            AutoClassProp.Prop.Add(properties);
                            dataTable.Columns.Add(item.field);
                        }
                    }
                    else
                    {
                        Properties properties = new Properties();
                        properties.Name = item.headerName;
                        properties.Key = item.field;
                        properties.Width = item.width;
                        properties.IsShow = 1;
                        AutoClassProp.Prop.Add(properties);
                        dataTable.Columns.Add(item.field);
                    }
                }

            }
            List<PMS_PERSON_FILE> listFile = GetRecordFileByPersonId(classInfo.CLASS_ID, personId);
            if (classInfo.CLASS_TYPE != "0")
            {
                List<PMS_ADDN_RECORD> recordList = _soa.Db.Queryable<PMS_ADDN_RECORD>().Where(s => s.PERSON_ID == personId && s.CLASS_ID == classInfo.CLASS_ID && s.CHECK_STATE == "2").OrderBy(item => item.RECORD_SORT).ToList();
                foreach (var item in recordList)
                {
                    IDictionary<string, object> recordData = DeserializeObject(item.RECORD_DATA);
                    recordData.Add("NO", recordList.FindIndex(w => w.RECORD_ID == item.RECORD_ID) + 1);
                    AddRow(recordData, dataTable);
                }
            }
            else
            {
                switch (classInfo.CLASS_ID.ToUpper())
                {
                    case "PMS_RESUME_LIST":
                        //履历记录
                        {
                            List<PMS_RESUME_LIST> pmsResumeList = _soa.Db.Queryable<PMS_RESUME_LIST>().Where(s => s.PERSON_ID == personId && s.CHECK_STATE == "2").OrderBy(item => item.RESUME_SORT).Select<PMS_RESUME_LIST>().ToList();
                            pmsResumeList = pmsResumeList.FindAll(w => w.RESUME_UNIT.IsNotNullOrEmpty());
                            foreach (var item in pmsResumeList)
                            {
                                if (item.RESUME_AFFIX.IsNotNullOrEmpty())
                                {
                                    List<PMS_PERSON_FILE> listFileTemp = listFile.FindAll(w => item.RESUME_AFFIX.Contains(w.FILE_ID));
                                    item.RESUME_AFFIX = string.Join("+", listFileTemp.Select(w => w.HTTP_FILE_PATH));
                                }
                                IDictionary<string, object> recordDic = item.AsDictionary();
                                IDictionary<string, object> dataDic = DeserializeObject(item.RECORD_DATA);
                                recordDic.AddRange(dataDic);
                                recordDic.Remove("RECORD_DATA");
                                recordDic.Add("NO", pmsResumeList.FindIndex(w => w.RESUME_ID == item.RESUME_ID) + 1);
                                AddRow(recordDic, dataTable);
                            }
                        }
                        break;
                    case "PMS_REWARD_LIST":
                        //奖惩记录
                        {
                            List<PMS_REWARD_LIST> pmsRewardList = _soa.Db.Queryable<PMS_REWARD_LIST>().Where(s => s.PERSON_ID == personId && s.CHECK_STATE == "2").Select<PMS_REWARD_LIST>().OrderBy(item => item.REWARD_SORT).ToList();
                            pmsRewardList = pmsRewardList.FindAll(w => w.REWARD_LEVEL.IsNotNullOrEmpty());
                            foreach (var index in pmsRewardList)
                            {
                                index.REWARD_LEVEL_NAME = RecordClassBaseName("奖惩级别", index.REWARD_LEVEL);
                                index.REWARD_STATE_NAME = index.REWARD_STATE == "0" ? "未提交" : "已提交";
                                if (index.REWARD_AFFIX.IsNotNullOrEmpty())
                                {
                                    List<PMS_PERSON_FILE> listFileTemp = listFile.FindAll(w => index.REWARD_AFFIX.Contains(w.FILE_ID));
                                    index.REWARD_AFFIX = string.Join("+", listFileTemp.Select(w => w.HTTP_FILE_PATH));
                                }
                                IDictionary<string, object> recordDic = index.AsDictionary();
                                IDictionary<string, object> dataDic = DeserializeObject(index.RECORD_DATA);
                                recordDic.AddRange(dataDic);
                                recordDic.Remove("RECORD_DATA");
                                recordDic.Add("NO", pmsRewardList.FindIndex(w => w.REWARD_ID == index.REWARD_ID) + 1);
                                AddRow(recordDic, dataTable);
                            }
                        }
                        break;
                    case "PMS_TEACH_LIST":
                        //教学记录
                        {
                            List<PMS_TEACH_LIST> pmsRewardList = _soa.Db.Queryable<PMS_TEACH_LIST>().Where(s => s.PERSON_ID == personId && s.CHECK_STATE == "2").Select<PMS_TEACH_LIST>().OrderBy(item => item.TEACH_SORT).ToList();
                            pmsRewardList = pmsRewardList.FindAll(w => w.TEACH_CLASS.IsNotNullOrEmpty());
                            foreach (var index in pmsRewardList)
                            {
                                index.TEACH_CLASS_NAME = RecordClassBaseName("教学分类", index.TEACH_CLASS);
                                index.TEACH_LEVEL_NAME = RecordClassBaseName("教学级别", index.TEACH_LEVEL);
                                index.TEACH_STATE_NAME = index.TEACH_STATE == "0" ? "未提交" : "已提交";
                                if (index.TEACH_AFFIX.IsNotNullOrEmpty())
                                {
                                    List<PMS_PERSON_FILE> listFileTemp = listFile.FindAll(w => index.TEACH_AFFIX.Contains(w.FILE_ID));
                                    index.TEACH_AFFIX = string.Join("+", listFileTemp.Select(w => w.HTTP_FILE_PATH));
                                }
                                IDictionary<string, object> recordDic = index.AsDictionary();
                                IDictionary<string, object> dataDic = DeserializeObject(index.RECORD_DATA);
                                recordDic.AddRange(dataDic);
                                recordDic.Remove("RECORD_DATA");
                                recordDic.Add("NO", pmsRewardList.FindIndex(w => w.TEACH_ID == index.TEACH_ID) + 1);
                                AddRow(recordDic, dataTable);
                            }
                        }
                        break;

                    case "PMS_STUDY_LIST":
                        //进修记录
                        {
                            List<PMS_STUDY_LIST> pmsRewardList = _soa.Db.Queryable<PMS_STUDY_LIST>().Where(s => s.PERSON_ID == personId && s.CHECK_STATE == "2").Select<PMS_STUDY_LIST>().OrderBy(item => item.STUDY_SORT).ToList();
                            pmsRewardList = pmsRewardList.FindAll(w => w.STUDY_CLASS.IsNotNullOrEmpty());
                            foreach (var index in pmsRewardList)
                            {
                                index.STUDY_CLASS_NAME = RecordClassBaseName("进修分类", index.STUDY_CLASS);
                                index.STUDY_STATE_NAME = index.STUDY_STATE == "0" ? "未提交" : "已提交";
                                if (index.STUDY_AFFIX.IsNotNullOrEmpty())
                                {
                                    List<PMS_PERSON_FILE> listFileTemp = listFile.FindAll(w => index.STUDY_AFFIX.Contains(w.FILE_ID));
                                    index.STUDY_AFFIX = string.Join("+", listFileTemp.Select(w => w.HTTP_FILE_PATH));
                                }
                                IDictionary<string, object> recordDic = index.AsDictionary();
                                IDictionary<string, object> dataDic = DeserializeObject(index.RECORD_DATA);
                                recordDic.AddRange(dataDic);
                                recordDic.Remove("RECORD_DATA");
                                recordDic.Add("NO", pmsRewardList.FindIndex(w => w.STUDY_ID == index.STUDY_ID) + 1);
                                recordDic["STUDY_START_DATE"] = index.STUDY_START_DATE != null ? index.STUDY_START_DATE.Value.ToString("yyyy-MM-dd") : null;
                                recordDic["STUDY_END_DATE"] = index.STUDY_END_DATE != null ? index.STUDY_END_DATE.Value.ToString("yyyy-MM-dd") : null;
                                AddRow(recordDic, dataTable);
                            }
                        }
                        break;
                    case "PMS_RESEARCH_LIST":
                        //课题记录
                        {
                            List<PMS_RESEARCH_LIST> pmsRewardList = _soa.Db.Queryable<PMS_RESEARCH_LIST>().Where(s => s.PERSON_ID == personId && s.CHECK_STATE == "2").Select<PMS_RESEARCH_LIST>().OrderBy(item => item.RESEARCH_SORT).ToList();
                            pmsRewardList = pmsRewardList.FindAll(w => w.RESEARCH_ITEM_NAME.IsNotNullOrEmpty());
                            foreach (var index in pmsRewardList)
                            {
                                index.RESEARCH_CLASS_NAME = RecordClassBaseName("课题分类", index.RESEARCH_CLASS);
                                index.RESEARCH_LEVEL_NAME = RecordClassBaseName("课题级别", index.RESEARCH_LEVEL);
                                index.RESEARCH_TYPE_NAME = RecordClassBaseName("课题类型", index.RESEARCH_TYPE);
                                index.PART_TYPE_NAME = RecordClassBaseName("参与类型", index.PART_TYPE);
                                index.RESEARCH_STATE_NAME = index.RESEARCH_STATE == "0" ? "未提交" : "已提交";
                                if (index.RESEARCH_AFFIX.IsNotNullOrEmpty())
                                {
                                    List<PMS_PERSON_FILE> listFileTemp = listFile.FindAll(w => index.RESEARCH_AFFIX.Contains(w.FILE_ID));
                                    index.RESEARCH_AFFIX = string.Join("+", listFileTemp.Select(w => w.HTTP_FILE_PATH));
                                }
                                IDictionary<string, object> recordDic = index.AsDictionary();
                                IDictionary<string, object> dataDic = DeserializeObject(index.RECORD_DATA);
                                recordDic.AddRange(dataDic);
                                recordDic.Remove("RECORD_DATA");
                                recordDic.Add("NO", pmsRewardList.FindIndex(w => w.RESEARCH_ID == index.RESEARCH_ID) + 1);
                                AddRow(recordDic, dataTable);
                            }
                        }
                        break;
                    case "PMS_THESIS_LIST":
                        //论著记录
                        {
                            List<PMS_THESIS_LIST> pmsRewardList = _soa.Db.Queryable<PMS_THESIS_LIST>().Where(s => s.PERSON_ID == personId && s.CHECK_STATE == "2").Select<PMS_THESIS_LIST>().OrderBy(item => item.THESIS_SORT).ToList();
                            pmsRewardList = pmsRewardList.FindAll(w => w.THESIS_ITEM_NAME.IsNotNullOrEmpty());
                            foreach (var index in pmsRewardList)
                            {
                                index.IF_SCI_NAME = index.IF_SCI == "1" ? "是" : "否";
                                index.JCR_RANGE_NAME = RecordClassBaseName("JCR分区", index.JCR_RANGE);
                                index.IF_VALUE_NAME = index.IF_VALUE == "1" ? "是" : "否";
                                index.THESIS_TYPE_NAME = RecordClassBaseName("论著类型", index.THESIS_TYPE);
                                index.THESIS_LEVEL_NAME = RecordClassBaseName("论著级别", index.THESIS_LEVEL);
                                index.THESIS_STATE_NAME = index.THESIS_STATE == "0" ? "未提交" : "已提交";
                                index.THESIS_PERSON_SORT_NAME = RecordClassBaseName("论著作者排序", index.THESIS_PERSON_SORT);
                                if (index.THESIS_AFFIX.IsNotNullOrEmpty())
                                {
                                    List<PMS_PERSON_FILE> listFileTemp = listFile.FindAll(w => index.THESIS_AFFIX.Contains(w.FILE_ID));
                                    index.THESIS_AFFIX = string.Join("+", listFileTemp.Select(w => w.HTTP_FILE_PATH));
                                }
                                IDictionary<string, object> recordDic = index.AsDictionary();
                                IDictionary<string, object> dataDic = DeserializeObject(index.RECORD_DATA);
                                recordDic.AddRange(dataDic);
                                recordDic.Remove("RECORD_DATA");
                                recordDic.Add("NO", pmsRewardList.FindIndex(w => w.THESIS_ID == index.THESIS_ID) + 1);
                                AddRow(recordDic, dataTable);
                            }
                        }
                        break;
                    case "PMS_EDUCATION_LIST":
                        //教育记录
                        {
                            List<PMS_EDUCATION_LIST> pmsRewardList = _soa.Db.Queryable<PMS_EDUCATION_LIST>().Where(s => s.PERSON_ID == personId && s.CHECK_STATE == "2").Select<PMS_EDUCATION_LIST>().OrderBy(item => item.EDUCATION_SORT).ToList();
                            pmsRewardList = pmsRewardList.FindAll(w => w.EDUCATION_DEGREE.IsNotNullOrEmpty());
                            foreach (var item in pmsRewardList)
                            {
                                item.EDUCATION_BACKGROUND_NAME = RecordClassBaseName("最高学历", item.EDUCATION_BACKGROUND);
                                item.EDUCATION_DEGREE_NAME = RecordClassBaseName("最高学位", item.EDUCATION_DEGREE);
                                item.DEGREE_TYPE_NAME = RecordClassBaseName("学位类型", item.DEGREE_TYPE);
                                item.ACADEMIC_PROPERTY_NAME = RecordClassBaseName("学历性质", item.ACADEMIC_PROPERTY);
                                item.STUDY_FORM_NAME = RecordClassBaseName("学习形式", item.STUDY_FORM);
                                item.EDUCATION_STATE_NAME = item.EDUCATION_STATE == "0" ? "未提交" : "已提交";
                                if (item.EDUCATION_AFFIX.IsNotNullOrEmpty())
                                {
                                    List<PMS_PERSON_FILE> listFileTemp = listFile.FindAll(w => item.EDUCATION_AFFIX.Contains(w.FILE_ID));
                                    item.EDUCATION_AFFIX = string.Join("+", listFileTemp.Select(w => w.HTTP_FILE_PATH));
                                }
                                IDictionary<string, object> recordDic = item.AsDictionary();
                                IDictionary<string, object> dataDic = DeserializeObject(item.RECORD_DATA);
                                recordDic.AddRange(dataDic);
                                recordDic.Remove("RECORD_DATA");
                                if (item.EDUCATION_DURATION == 0 && item.GRADUATE_TIME != null && item.ENROLLMENT_TIME != null)
                                {
                                    item.EDUCATION_DURATION = Convert.ToDateTime(item.GRADUATE_TIME).Year - Convert.ToDateTime(item.ENROLLMENT_TIME).Year;
                                }
                                recordDic.Add("NO", pmsRewardList.FindIndex(w => w.EDUCATION_ID == item.EDUCATION_ID) + 1);
                                AddRow(recordDic, dataTable);
                            }
                        }
                        break;
                    case "PMS_SKILL_CERTIFICATE_LIST":
                        //技能证书记录
                        {
                            List<PMS_SKILL_CERTIFICATE_LIST> pmsRewardList = _soa.Db.Queryable<PMS_SKILL_CERTIFICATE_LIST>().Where(s => s.PERSON_ID == personId && s.CHECK_STATE == "2").Select<PMS_SKILL_CERTIFICATE_LIST>().OrderBy(item => item.CERTIFICATE_SORT).ToList();
                            pmsRewardList = pmsRewardList.FindAll(w => w.CERTIFICATE_NAME.IsNotNullOrEmpty());
                            foreach (var item in pmsRewardList)
                            {
                                item.CERTIFICATE_LEVEL_NAME = RecordClassOaBaseName("证书级别", item.CERTIFICATE_LEVEL);
                                item.CERTIFICATE_TYPE_NAME = RecordClassOaBaseName("人事技能证书类型", item.CERTIFICATE_TYPE);
                                item.CERTIFICATE_STATE_NAME = item.CERTIFICATE_STATE == "0" ? "未提交" : "已提交";
                                if (item.CERTIFICATE_AFFIX.IsNotNullOrEmpty())
                                {
                                    List<PMS_PERSON_FILE> listFileTemp = listFile.FindAll(w => item.CERTIFICATE_AFFIX.Contains(w.FILE_ID));
                                    item.CERTIFICATE_AFFIX = string.Join("+", listFileTemp.Select(w => w.HTTP_FILE_PATH));
                                }
                                //  trainBDitem.AFFIX_NAME = string.Join("+", listFileTemp.Select(w => w.FILE_NAME));
                                IDictionary<string, object> recordDic = item.AsDictionary();
                                IDictionary<string, object> dataDic = DeserializeObject(item.RECORD_DATA);
                                recordDic.AddRange(dataDic);
                                recordDic.Remove("RECORD_DATA");
                                recordDic.Add("NO", pmsRewardList.FindIndex(w => w.CERTIFICATE_ID == item.CERTIFICATE_ID) + 1);
                                AddRow(recordDic, dataTable);
                            }
                        }
                        break;
                    case "PMS_TRAIN_LIST":
                        //培训记录
                        {
                            List<PMS_TRAIN_LIST> pmsRewardList = _soa.Db.Queryable<PMS_TRAIN_LIST>().Where(s => s.PERSON_ID == personId && s.CHECK_STATE == "2").Select<PMS_TRAIN_LIST>().OrderBy(item => item.TRAIN_SORT).ToList();
                            pmsRewardList = pmsRewardList.FindAll(w => w.TRAIN_UNIT.IsNotNullOrEmpty());
                            foreach (var item in pmsRewardList)
                            {
                                item.SCORE_TYPE_NAME = RecordClassBaseName("学分类型", item.SCORE_TYPE);
                                item.IF_FINISH_NAME = item.IF_FINISH switch { "1" => "是", "2" => "否", _ => string.Empty };
                                item.TRAIN_STATE_NAME = item.TRAIN_STATE == "0" ? "未提交" : "已提交";
                                if (item.TRAIN_AFFIX.IsNotNullOrEmpty())
                                {
                                    List<PMS_PERSON_FILE> listFileTemp = listFile.FindAll(w => item.TRAIN_AFFIX.Contains(w.FILE_ID));
                                    item.TRAIN_AFFIX = string.Join("+", listFileTemp.Select(w => w.HTTP_FILE_PATH));
                                }
                                IDictionary<string, object> recordDic = item.AsDictionary();
                                IDictionary<string, object> dataDic = DeserializeObject(item.RECORD_DATA);
                                recordDic.AddRange(dataDic);
                                recordDic.Remove("RECORD_DATA");
                                recordDic.Add("NO", pmsRewardList.FindIndex(w => w.TRAIN_ID == item.TRAIN_ID) + 1);
                                AddRow(recordDic, dataTable);
                            }
                        }
                        break;
                    case "PMS_SOCIAL_OFFICE_LIST":
                        //社会任职记录
                        {
                            List<PMS_SOCIAL_OFFICE_LIST> pmsRewardList = _soa.Db.Queryable<PMS_SOCIAL_OFFICE_LIST>().Where(s => s.PERSON_ID == personId && s.CHECK_STATE == "2").Select<PMS_SOCIAL_OFFICE_LIST>().OrderBy(item => item.SOFFICE_SORT).ToList();
                            pmsRewardList = pmsRewardList.FindAll(w => w.SOFFICE_POST.IsNotNullOrEmpty());
                            foreach (var item in pmsRewardList)
                            {
                                item.SOFFICE_POST_NAME = RecordClassBaseName("任职职务", item.SOFFICE_POST);
                                item.SOFFICE_LEVEL_NAME = RecordClassBaseName("任职机构等级", item.SOFFICE_LEVEL);
                                item.IF_SOFFICE_NAME = item.IF_SOFFICE == "1" ? "是" : "否";
                                item.SOFFICE_STATE_NAME = item.SOFFICE_STATE == "0" ? "未提交" : "已提交";
                                if (item.SOFFICE_AFFIX.IsNotNullOrEmpty())
                                {
                                    List<PMS_PERSON_FILE> listFileTemp = listFile.FindAll(w => item.SOFFICE_AFFIX.Contains(w.FILE_ID));
                                    item.SOFFICE_AFFIX = string.Join("+", listFileTemp.Select(w => w.HTTP_FILE_PATH));
                                }
                                IDictionary<string, object> recordDic = item.AsDictionary();
                                IDictionary<string, object> dataDic = DeserializeObject(item.RECORD_DATA);
                                recordDic.AddRange(dataDic);
                                recordDic.Remove("RECORD_DATA");
                                recordDic.Add("NO", pmsRewardList.FindIndex(w => w.SOFFICE_ID == item.SOFFICE_ID) + 1);
                                AddRow(recordDic, dataTable);
                            }
                        }
                        break;
                    case "PMS_INTELLECTUAL_LIST":
                        //知识产权记录
                        {
                            List<PMS_INTELLECTUAL_LIST> pmsRewardList = _soa.Db.Queryable<PMS_INTELLECTUAL_LIST>().Where(s => s.PERSON_ID == personId && s.CHECK_STATE == "2").Select<PMS_INTELLECTUAL_LIST>().OrderBy(item => item.INTELLECTUAL_SORT).ToList();
                            pmsRewardList = pmsRewardList.FindAll(w => w.INTELLECTUAL_CLASS.IsNotNullOrEmpty());
                            foreach (var item in pmsRewardList)
                            {
                                item.INTELLECTUAL_CLASS_NAME = RecordClassBaseName("知识产权分类", item.INTELLECTUAL_CLASS);
                                item.INTELLECTUAL_STATE_NAME = item.INTELLECTUAL_STATE == "0" ? "未提交" : "已提交";
                                string resume_affix_name = string.Empty;
                                if (item.INTELLECTUAL_AFFIX.IsNotNullOrEmpty())
                                {
                                    List<PMS_PERSON_FILE> listFileTemp = listFile.FindAll(w => item.INTELLECTUAL_AFFIX.Contains(w.FILE_ID));
                                    item.INTELLECTUAL_AFFIX = string.Join("+", listFileTemp.Select(w => w.HTTP_FILE_PATH));
                                }
                                IDictionary<string, object> recordDic = item.AsDictionary();
                                IDictionary<string, object> dataDic = DeserializeObject(item.RECORD_DATA);
                                recordDic.AddRange(dataDic);
                                recordDic.Remove("RECORD_DATA");
                                recordDic.Add("NO", pmsRewardList.FindIndex(w => w.INTELLECTUAL_ID == item.INTELLECTUAL_ID) + 1);
                                AddRow(recordDic, dataTable);
                            }
                        }
                        break;
                    case "PMS_EXCHANGE_LIST":
                        //访问交流记录
                        {
                            List<PMS_EXCHANGE_LIST> pmsRewardList = _soa.Db.Queryable<PMS_EXCHANGE_LIST>().Where(s => s.PERSON_ID == personId && s.CHECK_STATE == "2").Select<PMS_EXCHANGE_LIST>().OrderBy(item => item.EXCHANGE_SORT).ToList();
                            pmsRewardList = pmsRewardList.FindAll(w => w.EXCHANGE_LEVEL.IsNotNullOrEmpty());
                            foreach (var item in pmsRewardList)
                            {
                                item.EXCHANGE_LEVEL_NAME = RecordClassBaseName("交流级别", item.EXCHANGE_LEVEL);
                                item.EXCHANGE_STATE_NAME = item.EXCHANGE_STATE == "0" ? "未提交" : "已提交";
                                item.EXCHANGE_TIME = item.EXCHANGE_START_DATE == null ? "" : item.EXCHANGE_START_DATE.Value.ToString("yyyy-MM-dd");
                                if (item.EXCHANGE_AFFIX.IsNotNullOrEmpty())
                                {
                                    List<PMS_PERSON_FILE> listFileTemp = listFile.FindAll(w => item.EXCHANGE_AFFIX.Contains(w.FILE_ID));
                                    item.EXCHANGE_AFFIX = string.Join("+", listFileTemp.Select(w => w.HTTP_FILE_PATH));
                                }
                                IDictionary<string, object> recordDic = item.AsDictionary();
                                IDictionary<string, object> dataDic = DeserializeObject(item.RECORD_DATA);
                                recordDic.AddRange(dataDic);
                                recordDic.Remove("RECORD_DATA");
                                recordDic.Add("NO", pmsRewardList.FindIndex(w => w.EXCHANGE_ID == item.EXCHANGE_ID) + 1);
                                AddRow(recordDic, dataTable);
                            }
                        }
                        break;
                    case "PMS_PROFESSIONAL_LIST":
                        //职称记录
                        {
                            List<PMS_PROFESSIONAL_LIST> pmsRewardList = _soa.Db.Queryable<PMS_PROFESSIONAL_LIST>().Where(s => s.PERSON_ID == personId && s.CHECK_STATE == "2").Select<PMS_PROFESSIONAL_LIST>().OrderBy(item => item.PROFESSIONAL_SORT).ToList();
                            pmsRewardList = pmsRewardList.FindAll(w => w.PROFESSIONAL_LEVEL.IsNotNullOrEmpty());
                            foreach (var item in pmsRewardList)
                            {
                                item.PROFESSIONAL_STATE_NAME = item.PROFESSIONAL_STATE == "0" ? "未提交" : "已提交";
                                item.PROFESSIONAL_LEVEL_NAME = RecordClassBaseName("职称级别", item.PROFESSIONAL_LEVEL);
                                item.TECHNOLOGY_TYPE_NAME = RecordClassBaseName("职称类型", item.TECHNOLOGY_TYPE);
                                if (item.PROFESSIONAL_AFFIX.IsNotNullOrEmpty())
                                {
                                    List<PMS_PERSON_FILE> listFileTemp = listFile.FindAll(w => item.PROFESSIONAL_AFFIX.Contains(w.FILE_ID));
                                    item.PROFESSIONAL_AFFIX = string.Join("+", listFileTemp.Select(w => w.HTTP_FILE_PATH));
                                }
                                IDictionary<string, object> recordDic = item.AsDictionary();
                                IDictionary<string, object> dataDic = DeserializeObject(item.RECORD_DATA);
                                recordDic.AddRange(dataDic);
                                recordDic.Remove("RECORD_DATA");
                                recordDic.Add("NO", pmsRewardList.FindIndex(w => w.PROFESSIONAL_ID == item.PROFESSIONAL_ID) + 1);
                                AddRow(recordDic, dataTable);
                            }
                        }
                        break;
                    case "PMS_EXPATRIATE_LIST":
                        //外派记录
                        {
                            List<PMS_EXPATRIATE_LIST> pmsRewardList = _soa.Db.Queryable<PMS_EXPATRIATE_LIST>().Where(s => s.PERSON_ID == personId && s.CHECK_STATE == "2").Select<PMS_EXPATRIATE_LIST>().OrderBy(item => item.EXPATRIATE_SORT).ToList();
                            pmsRewardList = pmsRewardList.FindAll(w => w.EXPATRIATE_UNIT.IsNotNullOrEmpty());
                            foreach (var item in pmsRewardList)
                            {
                                item.EXPATRIATE_STATE_NAME = item.EXPATRIATE_STATE == "0" ? "未提交" : "已提交";
                                item.IF_FINISH_NAME = item.IF_FINISH switch { "1" => "是", "2" => "否", _ => string.Empty };
                                item.EXPATRIATE_NAME = RecordClassBaseName("外派性质", item.EXPATRIATE);
                                if (item.EXPATRIATE_AFFIX.IsNotNullOrEmpty())
                                {
                                    List<PMS_PERSON_FILE> listFileTemp = listFile.FindAll(w => item.EXPATRIATE_AFFIX.Contains(w.FILE_ID));
                                    item.EXPATRIATE_AFFIX = string.Join("+", listFileTemp.Select(w => w.HTTP_FILE_PATH));
                                }
                                IDictionary<string, object> recordDic = item.AsDictionary();
                                IDictionary<string, object> dataDic = DeserializeObject(item.RECORD_DATA);
                                recordDic.AddRange(dataDic);
                                recordDic.Remove("RECORD_DATA");
                                recordDic.Add("NO", pmsRewardList.FindIndex(w => w.EXPATRIATE_ID == item.EXPATRIATE_ID) + 1);
                                AddRow(recordDic, dataTable);
                            }
                        }
                        break;
                }
            }
            return (dataTable, AutoClassProp);
        }


        /// <summary>
        /// 生成分类信息表格
        /// </summary>
        /// <param name="classInfo">分类信息</param>
        /// <param name="personId">人员id</param>
        /// <param name="hospitalId">医院id</param>
        /// <returns></returns>
        private (DataTable, AutoClassProp) CreateClassInfoTableByPersonIds(PMS_ADDN_CLASS_INFO classInfo, List<PMS_PERSON_INFO> listPersonInfo, List<SYS6_MODULE_FUNC_DICT> listFuncDict, List<PMS_ADDN_RECORD> listRecordList, List<PMS_PERSON_FILE> listFile, string hospitalId, bool isViewAll = false)
        {
            List<string> personIds = listPersonInfo.Select(W => W.PERSON_ID).Distinct().ToList();
            if (listFile != null)
                listFile = listFile.FindAll(w => w.FILE_CLASS == classInfo.CLASS_ID);
            DataTable dataTable = new DataTable();
            AutoClassProp AutoClassProp = new AutoClassProp();
            try
            {
                AutoClassProp.ClassName = classInfo.CLASS_NAME;
                AutoClassProp.ClassCode = classInfo.CLASS_ID;
                AutoClassProp.Prop = new List<Properties>();

                dataTable.TableName = classInfo.CLASS_ID;
                var varMoudle = listFuncDict.Find(p => p.SETUP_ID == classInfo.TABLE_SETUP_ID);
                if (varMoudle != null)
                {
                    //查询工具箱表格属性获取表头
                    List<PageSettingTableCol> listCol = JsonConvert.DeserializeObject<List<PageSettingTableCol>>(varMoudle.FORM_COL_JSON);
                    foreach (PageSettingTableCol item in listCol)
                    {
                        if (!isViewAll)
                        {
                            if (item.headerName != "排序号" && item.hide != null && !item.hide.Value)
                            {
                                Properties properties = new Properties();
                                properties.Name = item.headerName;
                                properties.Key = item.field;
                                properties.IsShow = 1;
                                AutoClassProp.Prop.Add(properties);
                                dataTable.Columns.Add(item.field);
                            }
                        }
                        else
                        {
                            Properties properties = new Properties();
                            properties.Name = item.headerName;
                            properties.Key = item.field;
                            properties.IsShow = 1;
                            AutoClassProp.Prop.Add(properties);
                            dataTable.Columns.Add(item.field);
                        }
                    }
                }
                if (classInfo.CLASS_TYPE != "0")
                {
                    List<PMS_ADDN_RECORD> recordList = listRecordList.Where(s => s.CLASS_ID == classInfo.CLASS_ID).OrderBy(item => item.RECORD_ID).ToList();
                    if (!dataTable.Columns.Contains("PERSON_ID"))
                        dataTable.Columns.Add("PERSON_ID");
                    foreach (var item in recordList)
                    {
                        DataRow addRow = dataTable.NewRow();
                        IDictionary<string, object> recordData = DeserializeObject(item.RECORD_DATA);
                        foreach (var record in recordData)
                        {
                            if (dataTable.Columns.Contains(record.Key))
                            {
                                addRow[record.Key] = record.Value;
                            }
                        }
                        addRow["PERSON_ID"] = item.PERSON_ID;
                        dataTable.Rows.Add(addRow);
                    }
                }
                else
                {
                    switch (classInfo.CLASS_ID.ToUpper())
                    {
                        case "PMS_RESUME_LIST":
                            //履历记录
                            {
                                List<PMS_RESUME_LIST> pmsResumeList = _soa.Db.Queryable<PMS_RESUME_LIST>().Where(s => personIds.Contains(s.PERSON_ID) && s.CHECK_STATE == "2").OrderBy(item => item.RESUME_SORT).Select<PMS_RESUME_LIST>().ToList();
                                pmsResumeList = pmsResumeList.FindAll(w => w.RESUME_UNIT.IsNotNullOrEmpty());
                                foreach (var item in pmsResumeList)
                                {
                                    if (item.RESUME_AFFIX.IsNotNullOrEmpty() && listFile != null)
                                    {
                                        List<PMS_PERSON_FILE> listFileTemp = listFile.FindAll(w => item.RESUME_AFFIX.Contains(w.FILE_ID));
                                        item.RESUME_AFFIX = string.Join("+", listFileTemp.Select(w => w.HTTP_FILE_PATH));
                                        item.AFFIX_NAME = string.Join("+", listFileTemp.Select(w => w.FILE_NAME));
                                    }
                                    IDictionary<string, object> recordDic = item.AsDictionary();
                                    IDictionary<string, object> dataDic = DeserializeObject(item.RECORD_DATA);
                                    recordDic.AddRange(dataDic);
                                    recordDic.Remove("RECORD_DATA");
                                    AddRow(recordDic, dataTable);
                                }
                            }
                            break;
                        case "PMS_REWARD_LIST":
                            //奖惩记录
                            {
                                List<PMS_REWARD_LIST> pmsRewardList = _soa.Db.Queryable<PMS_REWARD_LIST>().Where(s => personIds.Contains(s.PERSON_ID) && s.CHECK_STATE == "2").Select<PMS_REWARD_LIST>().OrderBy(item => item.REWARD_SORT).ToList();
                                pmsRewardList = pmsRewardList.FindAll(w => w.REWARD_LEVEL.IsNotNullOrEmpty());
                                foreach (var index in pmsRewardList)
                                {
                                    index.REWARD_LEVEL = index.REWARD_LEVEL_NAME = RecordClassBaseName("奖惩级别", index.REWARD_LEVEL);
                                    index.REWARD_STATE_NAME = index.REWARD_STATE == "0" ? "未提交" : "已提交";
                                    if (index.REWARD_AFFIX.IsNotNullOrEmpty() && listFile != null)
                                    {
                                        List<PMS_PERSON_FILE> listFileTemp = listFile.FindAll(w => index.REWARD_AFFIX.Contains(w.FILE_ID));
                                        index.REWARD_AFFIX = string.Join("+", listFileTemp.Select(w => w.HTTP_FILE_PATH));
                                        index.AFFIX_NAME = string.Join("+", listFileTemp.Select(w => w.FILE_NAME));
                                    }
                                    IDictionary<string, object> recordDic = index.AsDictionary();
                                    IDictionary<string, object> dataDic = DeserializeObject(index.RECORD_DATA);
                                    recordDic.AddRange(dataDic);
                                    recordDic.Remove("RECORD_DATA");

                                    AddRow(recordDic, dataTable);
                                }
                            }
                            break;
                        case "PMS_TEACH_LIST":
                            //教学记录
                            {
                                List<PMS_TEACH_LIST> pmsRewardList = _soa.Db.Queryable<PMS_TEACH_LIST>().Where(s => personIds.Contains(s.PERSON_ID) && s.CHECK_STATE == "2").Select<PMS_TEACH_LIST>().OrderBy(item => item.TEACH_SORT).ToList();
                                pmsRewardList = pmsRewardList.FindAll(w => w.TEACH_CLASS.IsNotNullOrEmpty());
                                foreach (var index in pmsRewardList)
                                {
                                    index.TEACH_CLASS = index.TEACH_CLASS_NAME = RecordClassBaseName("教学分类", index.TEACH_CLASS);
                                    index.TEACH_LEVEL = index.TEACH_LEVEL_NAME = RecordClassBaseName("教学级别", index.TEACH_LEVEL);
                                    index.TEACH_STATE_NAME = index.TEACH_STATE == "0" ? "未提交" : "已提交";
                                    if (index.TEACH_AFFIX.IsNotNullOrEmpty() && listFile != null)
                                    {
                                        List<PMS_PERSON_FILE> listFileTemp = listFile.FindAll(w => index.TEACH_AFFIX.Contains(w.FILE_ID));
                                        index.TEACH_AFFIX = string.Join("+", listFileTemp.Select(w => w.HTTP_FILE_PATH));
                                        index.AFFIX_NAME = string.Join("+", listFileTemp.Select(w => w.FILE_NAME));
                                    }
                                    IDictionary<string, object> recordDic = index.AsDictionary();
                                    IDictionary<string, object> dataDic = DeserializeObject(index.RECORD_DATA);
                                    recordDic.AddRange(dataDic);
                                    recordDic.Remove("RECORD_DATA");
                                    AddRow(recordDic, dataTable);
                                }
                            }
                            break;

                        case "PMS_STUDY_LIST":
                            //进修记录
                            {
                                List<PMS_STUDY_LIST> pmsRewardList = _soa.Db.Queryable<PMS_STUDY_LIST>().Where(s => personIds.Contains(s.PERSON_ID) && s.CHECK_STATE == "2").Select<PMS_STUDY_LIST>().OrderBy(item => item.STUDY_SORT).ToList();
                                pmsRewardList = pmsRewardList.FindAll(w => w.STUDY_CLASS.IsNotNullOrEmpty());
                                foreach (var index in pmsRewardList)
                                {
                                    index.STUDY_CLASS = index.STUDY_CLASS_NAME = RecordClassBaseName("进修分类", index.STUDY_CLASS);
                                    index.STUDY_STATE_NAME = index.STUDY_STATE == "0" ? "未提交" : "已提交";
                                    if (index.STUDY_AFFIX.IsNotNullOrEmpty() && listFile != null)
                                    {
                                        List<PMS_PERSON_FILE> listFileTemp = listFile.FindAll(w => index.STUDY_AFFIX.Contains(w.FILE_ID));
                                        index.STUDY_AFFIX = string.Join("+", listFileTemp.Select(w => w.HTTP_FILE_PATH));
                                        index.AFFIX_NAME = string.Join("+", listFileTemp.Select(w => w.FILE_NAME));
                                    }
                                    IDictionary<string, object> recordDic = index.AsDictionary();
                                    IDictionary<string, object> dataDic = DeserializeObject(index.RECORD_DATA);
                                    recordDic.AddRange(dataDic);
                                    recordDic.Remove("RECORD_DATA");
                                    recordDic["STUDY_START_DATE"] = index.STUDY_START_DATE != null ? index.STUDY_START_DATE.Value.ToString("yyyy-MM-dd") : null;
                                    recordDic["STUDY_END_DATE"] = index.STUDY_END_DATE != null ? index.STUDY_END_DATE.Value.ToString("yyyy-MM-dd") : null;
                                    AddRow(recordDic, dataTable);
                                }
                            }
                            break;
                        case "PMS_RESEARCH_LIST":
                            //课题记录
                            {
                                List<PMS_RESEARCH_LIST> pmsRewardList = _soa.Db.Queryable<PMS_RESEARCH_LIST>().Where(s => personIds.Contains(s.PERSON_ID) && s.CHECK_STATE == "2").Select<PMS_RESEARCH_LIST>().OrderBy(item => item.RESEARCH_SORT).ToList();
                                pmsRewardList = pmsRewardList.FindAll(w => w.RESEARCH_CLASS.IsNotNullOrEmpty());
                                foreach (var index in pmsRewardList)
                                {
                                    index.RESEARCH_CLASS = index.RESEARCH_CLASS_NAME = RecordClassBaseName("课题分类", index.RESEARCH_CLASS);
                                    index.RESEARCH_LEVEL = index.RESEARCH_LEVEL_NAME = RecordClassBaseName("课题级别", index.RESEARCH_LEVEL);
                                    index.RESEARCH_TYPE = index.RESEARCH_TYPE_NAME = RecordClassBaseName("课题类型", index.RESEARCH_TYPE);
                                    index.PART_TYPE = index.PART_TYPE_NAME = RecordClassBaseName("参与类型", index.PART_TYPE);
                                    index.RESEARCH_STATE_NAME = index.RESEARCH_STATE == "0" ? "未提交" : "已提交";
                                    if (index.RESEARCH_AFFIX.IsNotNullOrEmpty() && listFile != null)
                                    {
                                        List<PMS_PERSON_FILE> listFileTemp = listFile.FindAll(w => index.RESEARCH_AFFIX.Contains(w.FILE_ID));
                                        index.RESEARCH_AFFIX = string.Join("+", listFileTemp.Select(w => w.HTTP_FILE_PATH));
                                        index.AFFIX_NAME = string.Join("+", listFileTemp.Select(w => w.FILE_NAME));
                                    }
                                    IDictionary<string, object> recordDic = index.AsDictionary();
                                    IDictionary<string, object> dataDic = DeserializeObject(index.RECORD_DATA);
                                    recordDic.AddRange(dataDic);
                                    recordDic.Remove("RECORD_DATA");

                                    AddRow(recordDic, dataTable);
                                }
                            }
                            break;
                        case "PMS_THESIS_LIST":
                            //论著记录
                            {
                                List<PMS_THESIS_LIST> pmsRewardList = _soa.Db.Queryable<PMS_THESIS_LIST>().Where(s => personIds.Contains(s.PERSON_ID) && s.CHECK_STATE == "2").Select<PMS_THESIS_LIST>().OrderBy(item => item.THESIS_SORT).ToList();
                                pmsRewardList = pmsRewardList.FindAll(w => w.THESIS_TYPE.IsNotNullOrEmpty());
                                foreach (var index in pmsRewardList)
                                {
                                    index.IF_SCI_NAME = index.IF_SCI == "1" ? "是" : "否";
                                    index.IF_SCI = index.IF_SCI_NAME;
                                    index.JCR_RANGE_NAME = RecordClassBaseName("JCR分区", index.JCR_RANGE);
                                    index.JCR_RANGE = index.JCR_RANGE_NAME;
                                    index.IF_VALUE_NAME = index.IF_VALUE == "1" ? "是" : "否";
                                    index.IF_VALUE = index.IF_VALUE_NAME;
                                    index.THESIS_TYPE_NAME = RecordClassBaseName("论著类型", index.THESIS_TYPE);
                                    index.THESIS_TYPE = index.THESIS_TYPE_NAME;
                                    index.THESIS_LEVEL_NAME = RecordClassBaseName("论著级别", index.THESIS_LEVEL);
                                    index.THESIS_LEVEL = index.THESIS_LEVEL_NAME;
                                    index.THESIS_STATE_NAME = index.THESIS_STATE == "0" ? "未提交" : "已提交";
                                    index.THESIS_PERSON_SORT_NAME = RecordClassBaseName("论著作者排序", index.THESIS_PERSON_SORT);
                                    index.THESIS_PERSON_SORT = index.THESIS_PERSON_SORT_NAME;
                                    if (index.THESIS_AFFIX.IsNotNullOrEmpty() && listFile != null)
                                    {
                                        List<PMS_PERSON_FILE> listFileTemp = listFile.FindAll(w => index.THESIS_AFFIX.Contains(w.FILE_ID));
                                        index.THESIS_AFFIX = string.Join("+", listFileTemp.Select(w => w.HTTP_FILE_PATH));
                                        index.AFFIX_NAME = string.Join("+", listFileTemp.Select(w => w.FILE_NAME));
                                    }
                                    IDictionary<string, object> recordDic = index.AsDictionary();
                                    IDictionary<string, object> dataDic = DeserializeObject(index.RECORD_DATA);
                                    recordDic.AddRange(dataDic);
                                    recordDic.Remove("RECORD_DATA");
                                    AddRow(recordDic, dataTable);
                                }
                            }
                            break;
                        case "PMS_EDUCATION_LIST":
                            //教育记录
                            {
                                List<PMS_EDUCATION_LIST> pmsRewardList = _soa.Db.Queryable<PMS_EDUCATION_LIST>().Where(s => personIds.Contains(s.PERSON_ID) && s.CHECK_STATE == "2").Select<PMS_EDUCATION_LIST>().OrderBy(item => item.EDUCATION_SORT).ToList();
                                pmsRewardList = pmsRewardList.FindAll(w => w.EDUCATION_DEGREE.IsNotNullOrEmpty());
                                foreach (var item in pmsRewardList)
                                {
                                    item.EDUCATION_BACKGROUND = item.EDUCATION_BACKGROUND_NAME = RecordClassBaseName("最高学历", item.EDUCATION_BACKGROUND);
                                    item.EDUCATION_DEGREE = item.EDUCATION_DEGREE_NAME = RecordClassBaseName("最高学位", item.EDUCATION_DEGREE);
                                    if (!dataTable.Columns.Contains("BACKGROUNDORDEGREE"))
                                        dataTable.Columns.Add("BACKGROUNDORDEGREE");
                                    item.BACKGROUNDORDEGREE = $"{item.EDUCATION_BACKGROUND_NAME}/{item.EDUCATION_DEGREE_NAME}";
                                    item.DEGREE_TYPE = item.DEGREE_TYPE_NAME = RecordClassBaseName("学位类型", item.DEGREE_TYPE);
                                    item.ACADEMIC_PROPERTY = item.ACADEMIC_PROPERTY_NAME = RecordClassBaseName("学历性质", item.ACADEMIC_PROPERTY);
                                    item.STUDY_FORM_NAME = RecordClassBaseName("学习形式", item.STUDY_FORM);
                                    item.EDUCATION_STATE_NAME = item.EDUCATION_STATE == "0" ? "未提交" : "已提交";
                                    if (item.EDUCATION_AFFIX.IsNotNullOrEmpty() && listFile != null)
                                    {
                                        List<PMS_PERSON_FILE> listFileTemp = listFile.FindAll(w => item.EDUCATION_AFFIX.Contains(w.FILE_ID));
                                        item.EDUCATION_AFFIX = string.Join("+", listFileTemp.Select(w => w.HTTP_FILE_PATH));
                                        item.AFFIX_NAME = string.Join("+", listFileTemp.Select(w => w.FILE_NAME));
                                    }
                                    IDictionary<string, object> recordDic = item.AsDictionary();
                                    IDictionary<string, object> dataDic = DeserializeObject(item.RECORD_DATA);
                                    recordDic.AddRange(dataDic);
                                    recordDic.Remove("RECORD_DATA");
                                    if (item.EDUCATION_DURATION == 0 && item.GRADUATE_TIME != null && item.ENROLLMENT_TIME != null)
                                    {
                                        item.EDUCATION_DURATION = Convert.ToDateTime(item.GRADUATE_TIME).Year - Convert.ToDateTime(item.ENROLLMENT_TIME).Year;
                                    }
                                    AddRow(recordDic, dataTable);
                                }
                            }
                            break;
                        case "PMS_SKILL_CERTIFICATE_LIST":
                            //技能证书记录
                            {

                                List<PMS_SKILL_CERTIFICATE_LIST> pmsRewardList = _soa.Db.Queryable<PMS_SKILL_CERTIFICATE_LIST>().Where(s => personIds.Contains(s.PERSON_ID) && s.CHECK_STATE == "2").Select<PMS_SKILL_CERTIFICATE_LIST>().OrderBy(item => item.CERTIFICATE_SORT).ToList();
                                pmsRewardList = pmsRewardList.FindAll(w => w.CERTIFICATE_LEVEL.IsNotNullOrEmpty());
                                foreach (var item in pmsRewardList)
                                {
                                    item.CERTIFICATE_LEVEL_NAME = RecordClassOaBaseName("证书级别", item.CERTIFICATE_LEVEL);
                                    item.CERTIFICATE_TYPE_NAME = RecordClassOaBaseName("人事技能证书类型", item.CERTIFICATE_TYPE);
                                    if (item.CERTIFICATE_LEVEL_NAME.IsNotNullOrEmpty())
                                        item.CERTIFICATE_LEVEL = item.CERTIFICATE_LEVEL_NAME;
                                    if (item.CERTIFICATE_TYPE_NAME.IsNotNullOrEmpty())
                                        item.CERTIFICATE_TYPE = item.CERTIFICATE_TYPE_NAME;
                                    item.CERTIFICATE_STATE_NAME = item.CERTIFICATE_STATE == "0" ? "未提交" : "已提交";
                                    if (item.CERTIFICATE_AFFIX.IsNotNullOrEmpty() && listFile != null)
                                    {
                                        List<PMS_PERSON_FILE> listFileTemp = listFile.FindAll(w => item.CERTIFICATE_AFFIX.Contains(w.FILE_ID));
                                        item.CERTIFICATE_AFFIX = string.Join("+", listFileTemp.Select(w => w.HTTP_FILE_PATH));
                                        item.AFFIX_NAME = string.Join("+", listFileTemp.Select(w => w.FILE_NAME));
                                    }
                                    IDictionary<string, object> recordDic = item.AsDictionary();
                                    IDictionary<string, object> dataDic = DeserializeObject(item.RECORD_DATA);
                                    recordDic.AddRange(dataDic);
                                    recordDic.Remove("RECORD_DATA");

                                    AddRow(recordDic, dataTable);
                                }
                            }
                            break;
                        case "PMS_TRAIN_LIST":
                            //培训记录
                            {
                                List<PMS_TRAIN_LIST> pmsRewardList = _soa.Db.Queryable<PMS_TRAIN_LIST>().Where(s => personIds.Contains(s.PERSON_ID) && s.CHECK_STATE == "2").Select<PMS_TRAIN_LIST>().OrderBy(item => item.TRAIN_SORT).ToList();
                                pmsRewardList = pmsRewardList.FindAll(w => w.TRAIN_UNIT.IsNotNullOrEmpty());
                                foreach (var item in pmsRewardList)
                                {
                                    item.SCORE_TYPE = item.SCORE_TYPE_NAME = RecordClassBaseName("学分类型", item.SCORE_TYPE);
                                    item.IF_FINISH_NAME = item.IF_FINISH switch { "1" => "是", "2" => "否", _ => string.Empty };
                                    item.TRAIN_STATE_NAME = item.TRAIN_STATE == "0" ? "未提交" : "已提交";
                                    if (item.TRAIN_AFFIX.IsNotNullOrEmpty() && listFile != null)
                                    {
                                        List<PMS_PERSON_FILE> listFileTemp = listFile.FindAll(w => item.TRAIN_AFFIX.Contains(w.FILE_ID));
                                        item.TRAIN_AFFIX = string.Join("+", listFileTemp.Select(w => w.HTTP_FILE_PATH));
                                        item.AFFIX_NAME = string.Join("+", listFileTemp.Select(w => w.FILE_NAME));
                                    }
                                    IDictionary<string, object> recordDic = item.AsDictionary();
                                    IDictionary<string, object> dataDic = DeserializeObject(item.RECORD_DATA);
                                    recordDic.AddRange(dataDic);
                                    recordDic.Remove("RECORD_DATA");

                                    AddRow(recordDic, dataTable);
                                }
                            }
                            break;
                        case "PMS_SOCIAL_OFFICE_LIST":
                            //社会任职记录
                            {
                                List<PMS_SOCIAL_OFFICE_LIST> pmsRewardList = _soa.Db.Queryable<PMS_SOCIAL_OFFICE_LIST>().Where(s => personIds.Contains(s.PERSON_ID) && s.CHECK_STATE == "2").Select<PMS_SOCIAL_OFFICE_LIST>().OrderBy(item => item.SOFFICE_SORT).ToList();
                                pmsRewardList = pmsRewardList.FindAll(w => w.SOFFICE_POST.IsNotNullOrEmpty());
                                foreach (var item in pmsRewardList)
                                {
                                    item.SOFFICE_POST = item.SOFFICE_POST_NAME = RecordClassBaseName("任职职务", item.SOFFICE_POST);
                                    item.SOFFICE_LEVEL = item.SOFFICE_LEVEL_NAME = RecordClassBaseName("任职机构等级", item.SOFFICE_LEVEL);
                                    item.IF_SOFFICE_NAME = item.IF_SOFFICE == "1" ? "是" : "否";
                                    item.SOFFICE_STATE_NAME = item.SOFFICE_STATE == "0" ? "未提交" : "已提交";
                                    if (item.SOFFICE_AFFIX.IsNotNullOrEmpty() && listFile != null)
                                    {
                                        List<PMS_PERSON_FILE> listFileTemp = listFile.FindAll(w => item.SOFFICE_AFFIX.Contains(w.FILE_ID));
                                        item.SOFFICE_AFFIX = string.Join("+", listFileTemp.Select(w => w.HTTP_FILE_PATH));
                                        item.AFFIX_NAME = string.Join("+", listFileTemp.Select(w => w.FILE_NAME));
                                    }
                                    IDictionary<string, object> recordDic = item.AsDictionary();
                                    IDictionary<string, object> dataDic = DeserializeObject(item.RECORD_DATA);
                                    recordDic.AddRange(dataDic);
                                    recordDic.Remove("RECORD_DATA");
                                    AddRow(recordDic, dataTable);
                                }
                            }
                            break;
                        case "PMS_INTELLECTUAL_LIST":
                            //知识产权记录
                            {
                                //   List<PMS_PERSON_FILE> pmsPersonFile = _soa.Db.Queryable<PMS_PERSON_FILE>().Where(p => personIds.Contains(p.PERSON_ID) && p.FILE_STATE == "1").Select<PMS_PERSON_FILE>().ToList();
                                List<PMS_INTELLECTUAL_LIST> pmsRewardList = _soa.Db.Queryable<PMS_INTELLECTUAL_LIST>().Where(s => personIds.Contains(s.PERSON_ID) && s.CHECK_STATE == "2").Select<PMS_INTELLECTUAL_LIST>().OrderBy(item => item.INTELLECTUAL_SORT).ToList();
                                pmsRewardList = pmsRewardList.FindAll(w => w.INTELLECTUAL_CLASS.IsNotNullOrEmpty());
                                foreach (var item in pmsRewardList)
                                {
                                    item.INTELLECTUAL_CLASS = item.INTELLECTUAL_CLASS_NAME = RecordClassBaseName("知识产权分类", item.INTELLECTUAL_CLASS);
                                    item.INTELLECTUAL_STATE_NAME = item.INTELLECTUAL_STATE == "0" ? "未提交" : "已提交";
                                    string resume_affix_name = string.Empty;
                                    if (item.INTELLECTUAL_AFFIX != null && item.INTELLECTUAL_AFFIX != "" && listFile != null)
                                    {
                                        List<PMS_PERSON_FILE> listFileTemp = listFile.FindAll(w => item.INTELLECTUAL_AFFIX.Contains(w.FILE_ID));
                                        item.INTELLECTUAL_AFFIX = string.Join("+", listFileTemp.Select(w => w.HTTP_FILE_PATH));
                                        item.AFFIX_NAME = string.Join("+", listFileTemp.Select(w => w.FILE_NAME));
                                    }

                                    IDictionary<string, object> recordDic = item.AsDictionary();
                                    IDictionary<string, object> dataDic = DeserializeObject(item.RECORD_DATA);
                                    recordDic.AddRange(dataDic);
                                    recordDic.Remove("RECORD_DATA");

                                    AddRow(recordDic, dataTable);
                                }
                            }
                            break;
                        case "PMS_EXCHANGE_LIST":
                            //访问交流记录
                            {
                                List<PMS_EXCHANGE_LIST> pmsRewardList = _soa.Db.Queryable<PMS_EXCHANGE_LIST>().Where(s => personIds.Contains(s.PERSON_ID) && s.CHECK_STATE == "2").Select<PMS_EXCHANGE_LIST>().OrderBy(item => item.EXCHANGE_SORT).ToList();
                                pmsRewardList = pmsRewardList.FindAll(w => w.EXCHANGE_LEVEL.IsNotNullOrEmpty());
                                foreach (var item in pmsRewardList)
                                {
                                    item.EXCHANGE_LEVEL = item.EXCHANGE_LEVEL_NAME = RecordClassBaseName("交流级别", item.EXCHANGE_LEVEL);
                                    item.EXCHANGE_STATE_NAME = item.EXCHANGE_STATE == "0" ? "未提交" : "已提交";
                                    item.EXCHANGE_TIME = item.EXCHANGE_START_DATE == null ? "" : item.EXCHANGE_START_DATE.Value.ToString("yyyy-MM-dd");
                                    if (item.EXCHANGE_AFFIX.IsNotNullOrEmpty() && listFile != null)
                                    {
                                        List<PMS_PERSON_FILE> listFileTemp = listFile.FindAll(w => item.EXCHANGE_AFFIX.Contains(w.FILE_ID));
                                        item.EXCHANGE_AFFIX = string.Join("+", listFileTemp.Select(w => w.HTTP_FILE_PATH));
                                        item.AFFIX_NAME = string.Join("+", listFileTemp.Select(w => w.FILE_NAME));
                                    }
                                    IDictionary<string, object> recordDic = item.AsDictionary();
                                    IDictionary<string, object> dataDic = DeserializeObject(item.RECORD_DATA);
                                    recordDic.AddRange(dataDic);
                                    recordDic.Remove("RECORD_DATA");

                                    AddRow(recordDic, dataTable);
                                }
                            }
                            break;
                        case "PMS_PROFESSIONAL_LIST":
                            //职称记录
                            {
                                List<PMS_PROFESSIONAL_LIST> pmsRewardList = _soa.Db.Queryable<PMS_PROFESSIONAL_LIST>().Where(s => personIds.Contains(s.PERSON_ID) && s.CHECK_STATE == "2").Select<PMS_PROFESSIONAL_LIST>().OrderBy(item => item.PROFESSIONAL_SORT).ToList();
                                pmsRewardList = pmsRewardList.FindAll(w => w.PROFESSIONAL_LEVEL.IsNotNullOrEmpty());
                                foreach (var item in pmsRewardList)
                                {
                                    item.PROFESSIONAL_STATE_NAME = item.PROFESSIONAL_STATE == "0" ? "未提交" : "已提交";
                                    item.PROFESSIONAL_LEVEL = item.PROFESSIONAL_LEVEL_NAME = RecordClassBaseName("职称级别", item.PROFESSIONAL_LEVEL);
                                    item.TECHNOLOGY_TYPE = item.TECHNOLOGY_TYPE_NAME = RecordClassBaseName("职称类型", item.TECHNOLOGY_TYPE);
                                    string dutiesName = RecordClassBaseName("职务", listPersonInfo.Find(w => w.PERSON_ID == item.PERSON_ID)?.DUTIES);
                                    item.JOBORTECHNOLOGY = $"{dutiesName}/{item.TECHNOLOGY_TYPE_NAME}";
                                    if (!dataTable.Columns.Contains("JOBORTECHNOLOGY"))
                                        dataTable.Columns.Add("JOBORTECHNOLOGY");
                                    if (item.PROFESSIONAL_AFFIX.IsNotNullOrEmpty() && listFile != null)
                                    {
                                        List<PMS_PERSON_FILE> listFileTemp = listFile.FindAll(w => item.PROFESSIONAL_AFFIX.Contains(w.FILE_ID));
                                        item.PROFESSIONAL_AFFIX = string.Join("+", listFileTemp.Select(w => w.HTTP_FILE_PATH));
                                        item.AFFIX_NAME = string.Join("+", listFileTemp.Select(w => w.FILE_NAME));
                                    }
                                    IDictionary<string, object> recordDic = item.AsDictionary();
                                    IDictionary<string, object> dataDic = DeserializeObject(item.RECORD_DATA);
                                    recordDic.AddRange(dataDic);
                                    recordDic.Remove("RECORD_DATA");

                                    AddRow(recordDic, dataTable);
                                }
                            }
                            break;
                        case "PMS_EXPATRIATE_LIST":
                            //外派记录
                            {
                                List<PMS_EXPATRIATE_LIST> pmsRewardList = _soa.Db.Queryable<PMS_EXPATRIATE_LIST>().Where(s => personIds.Contains(s.PERSON_ID) && s.CHECK_STATE == "2").Select<PMS_EXPATRIATE_LIST>().OrderBy(item => item.EXPATRIATE_SORT).ToList();
                                pmsRewardList = pmsRewardList.FindAll(w => w.EXPATRIATE.IsNotNullOrEmpty());
                                foreach (var item in pmsRewardList)
                                {
                                    item.EXPATRIATE_STATE_NAME = item.EXPATRIATE_STATE == "0" ? "未提交" : "已提交";
                                    item.IF_FINISH_NAME = item.IF_FINISH switch { "1" => "是", "2" => "否", _ => string.Empty };
                                    item.EXPATRIATE = item.EXPATRIATE_NAME = RecordClassBaseName("外派性质", item.EXPATRIATE);
                                    if (item.EXPATRIATE_AFFIX.IsNotNullOrEmpty() && listFile != null)
                                    {
                                        List<PMS_PERSON_FILE> listFileTemp = listFile.FindAll(w => item.EXPATRIATE_AFFIX.Contains(w.FILE_ID));
                                        item.EXPATRIATE_AFFIX = string.Join("+", listFileTemp.Select(w => w.HTTP_FILE_PATH));
                                        item.AFFIX_NAME = string.Join("+", listFileTemp.Select(w => w.FILE_NAME));
                                    }
                                    IDictionary<string, object> recordDic = item.AsDictionary();
                                    IDictionary<string, object> dataDic = DeserializeObject(item.RECORD_DATA);
                                    recordDic.AddRange(dataDic);
                                    recordDic.Remove("RECORD_DATA");
                                    AddRow(recordDic, dataTable);
                                }
                            }
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("CreateClassInfoTableByPersonIds报错:" + ex);
            }

            return (dataTable, AutoClassProp);
        }

        private void AddRow(IDictionary<string, object> recordDic, DataTable dataTable)
        {
            DataRow addRow = dataTable.NewRow();
            foreach (var record in recordDic)
            {
                if (dataTable.Columns.Contains(record.Key))
                {
                    addRow[record.Key] = record.Value;
                }
            }
            dataTable.Rows.Add(addRow);
        }
        /// <summary>
        /// 获取人员信息树
        /// </summary>
        /// <param name="hospital_id"></param>
        /// <returns></returns>
        public IEnumerable<InspectionPgroupDto> GetPersonInfoTree(string hospital_id, string lab_id, string pgroup_id, string person_name, string user_no)
        {
            var query1 = _soa.Db.Queryable<SYS6_USER_POST>()
                            .LeftJoin<SYS6_POST>((a, b) => a.POST_ID == b.POST_ID)
                            .LeftJoin<SYS6_INSPECTION_PGROUP>((a, b, c) => b.PGROUP_ID == c.PGROUP_ID)
                            .Where((a, b, c) => a.HOSPITAL_ID == hospital_id && a.USER_NO == user_no && b.LAB_ID == lab_id && c.PGROUP_STATE == "1")
                            .Select((a, b, c) => new
                            {
                                LAB_ID = c.LAB_ID,
                                PGROUP_ID = c.PGROUP_ID,
                                PGROUP_NAME = c.PGROUP_NAME,
                                PGROUP_SORT = c.PGROUP_SORT
                            }).ToList();
            var query = query1.ToList().Where((x, i) => query1.ToList().FindIndex(s => s.PGROUP_ID == x.PGROUP_ID) == i).ToList();
            if (pgroup_id != "")
            {
                query = query.Where(p => p.PGROUP_ID == pgroup_id).ToList();
            }
            List<InspectionPgroupDto> InspectionPgroupList = new List<InspectionPgroupDto>();
            var sssList = _soa.Db.Queryable<PMS_PERSON_INFO>().Where(p => p.HOSPITAL_ID == hospital_id && p.PERSON_STATE == "1").Select<PMS_PERSON_INFO>().ToList();
            foreach (var dic in query)
            {
                var ssList = _soa.Db.Queryable<SYS6_USER>().Where(p => p.STATE_FLAG == "1" && p.DEPT_CODE == dic.PGROUP_ID).Select<SYS6_USER>().ToList();
                if (person_name != "")
                {
                    ssList = ssList.Where(p => p.DEPT_CODE == dic.PGROUP_ID && p.STATE_FLAG == "1" && p.USERNAME.Contains(person_name)).ToList();
                }
                List<PgroupPersonInfoOption> optionList = new List<PgroupPersonInfoOption>();
                foreach (var item in ssList)
                {
                    var personItemOjb = sssList.Where(p => p.USER_ID == item.USER_NO).FirstOrDefault();
                    PgroupPersonInfoOption odto = new PgroupPersonInfoOption();
                    string datetime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                    if (personItemOjb != null)
                    {
                        if (personItemOjb.PGROUP_ID != item.DEPT_CODE || personItemOjb.DUTIES != item.POWER || personItemOjb.ACADEMIC_POST != item.TECH_POST
                            || personItemOjb.USER_TYPE != item.USER_TYPE || personItemOjb.HIS_ID != item.HIS_ID || personItemOjb.PERSON_DOC_STATE != item.JOB_STATE)
                        {
                            personItemOjb.PGROUP_ID = dic.PGROUP_ID;
                            personItemOjb.DUTIES = item.POWER;
                            personItemOjb.ACADEMIC_POST = item.TECH_POST;
                            personItemOjb.USER_TYPE = item.USER_TYPE;
                            personItemOjb.HIS_ID = item.HIS_ID;
                            personItemOjb.PERSON_DOC_STATE = item.JOB_STATE;
                            if (item.ID_CARD != null)
                            {
                                if (personItemOjb.ID_CARD != item.ID_CARD && item.ID_CARD.Length == 18)
                                {
                                    personItemOjb.ID_CARD = item.ID_CARD;
                                    personItemOjb.BIRTHDAY = item.ID_CARD.Substring(6, 4) + "-" + item.ID_CARD.Substring(10, 2) + "-" + item.ID_CARD.Substring(12, 2);
                                    if (int.TryParse(item.ID_CARD.Substring(14, 3), out int sex))
                                    {
                                        if (sex % 2 == 0)
                                        {
                                            personItemOjb.SEX = "2";
                                        }
                                        else
                                        {
                                            personItemOjb.SEX = "1";
                                        }
                                    }
                                    personItemOjb.AGE = CommonHelper.CalculateAge(personItemOjb.BIRTHDAY).ToString();
                                }
                            }
                            _soa.Db.Updateable<PMS_PERSON_INFO>(personItemOjb).ExecuteCommand();
                        }
                        odto.key = personItemOjb.USER_ID;
                        odto.PERSON_ID = personItemOjb.PERSON_ID;
                        odto.USER_ID = personItemOjb.USER_ID;
                        odto.title = personItemOjb.USER_NAME;
                        odto.PERSON_PHOTO_PATH = personItemOjb.PERSON_PHOTO_PATH;
                        odto.DUTIES_NAME = RecordClassBaseName("职务", personItemOjb.DUTIES);
                        odto.IF_COMPLETE = _IModuleLabGroupService.GitRecordWriteRowNum(personItemOjb.PERSON_ID, hospital_id);
                        optionList.Add(odto);
                    }
                    #region 删除同步人员逻辑
                    //else
                    //{

                    //    //string guid = Guid.NewGuid().ToString("N");
                    //    //string personid = guid.Substring(guid.Length - 10, 10);
                    //    try
                    //    {
                    //        PMS_PERSON_INFO personItem = new PMS_PERSON_INFO();
                    //        string personid = _IBaseDataServices.GetTableMax("PMS_PERSON_INFO", "PERSON_ID", 1, 1).data.ToString();
                    //        personItem.PERSON_ID = personid;
                    //        personItem.PGROUP_ID = dic.PGROUP_ID;
                    //        personItem.HOSPITAL_ID = hospital_id;
                    //        personItem.USER_ID = trainBDitem.USER_NO;
                    //        personItem.USER_NAME = trainBDitem.USERNAME;
                    //        personItem.USER_TYPE = trainBDitem.USER_TYPE;
                    //        personItem.DUTIES = trainBDitem.POWER;
                    //        personItem.ACADEMIC_POST = trainBDitem.TECH_POST;
                    //        personItem.ID_CARD = trainBDitem.ID_CARD == null ? "" : trainBDitem.ID_CARD;
                    //        personItem.LOGID = trainBDitem.LOGID;
                    //        personItem.HIS_ID = trainBDitem.HIS_ID;
                    //        personItem.PERSON_STATE = "1";
                    //        personItem.PERSON_DOC_STATE = trainBDitem.JOB_STATE;
                    //        if (personItem.ID_CARD != "" && personItem.ID_CARD.Length == 18)
                    //        {
                    //            personItem.BIRTHDAY = trainBDitem.ID_CARD.Substring(6, 4) + "-" + trainBDitem.ID_CARD.Substring(10, 2) + "-" + trainBDitem.ID_CARD.Substring(12, 2);
                    //            int sex = int.Parse(trainBDitem.ID_CARD.Substring(14, 3));
                    //            if (sex % 2 == 0)
                    //            {
                    //                personItem.SEX = "2";
                    //            }
                    //            else
                    //            {
                    //                personItem.SEX = "1";
                    //            }
                    //            personItem.AGE = CommonHelper.CalculateAge(personItem.BIRTHDAY).ToString();
                    //        }

                    //        odto.key = trainBDitem.USER_NO;
                    //        odto.USER_ID = trainBDitem.USER_NO;
                    //        odto.title = trainBDitem.USERNAME;
                    //        odto.PERSON_ID = personid;
                    //        odto.PERSON_PHOTO_PATH = "";
                    //        odto.DUTIES_NAME = RecordClassBaseName("职务", trainBDitem.POWER);
                    //        odto.IF_COMPLETE = _IModuleLabGroupService.GitRecordWriteRowNum(personid, hospital_id);
                    //        _soa.Db.Insertable<PMS_PERSON_INFO>(personItem).ExecuteCommand();
                    //    }
                    //    catch (Exception ex)
                    //    {
                    //        _IBaseDataServices.GetErrorTableMax("PMS_PERSON_INFO", ex, "PERSON_ID");
                    //    }
                    //}
                    //optionList.Add(odto);
                    #endregion
                }
                if (optionList.Count() != 0)
                {
                    InspectionPgroupList.Add(new InspectionPgroupDto
                    {
                        key = dic.PGROUP_ID,
                        title = dic.PGROUP_NAME,
                        LAB_ID = dic.LAB_ID,
                        PGROUP_NUM = optionList.Count(),
                        children = optionList
                    });
                }
            }
            return InspectionPgroupList;
        }


        /// <summary>
        /// 根据基础数据用户SYS6_USER同步PMS_PERSON_INFO
        /// </summary>
        /// <param name="user"></param>
        /// <param name="persons"></param>
        /// <param name="isAsyncPerson">是否进行实时同步</param>
        /// <returns></returns>
        public PMS_PERSON_INFO SyncPersonInfo(SYS6_USER user, List<PMS_PERSON_INFO> persons, bool isAsyncPerson = false)
        {
            PMS_PERSON_INFO person = persons.Find(a => a.USER_ID == user.USER_NO);
            string datetime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            if (person == null)
            {
                person = _soa.Db.Queryable<PMS_PERSON_INFO>().Where(p => p.USER_ID == user.USER_NO).First();
            }
            if (isAsyncPerson)//因定时作业AutoSyncPersonData进行全局同步，默认不在业务系统同步
            {
                if (person != null)
                {
                    if (person.HOSPITAL_ID != user.HOSPITAL_ID || user.STATE_FLAG != person.PERSON_STATE || person.PGROUP_ID != user.DEPT_CODE
                        || person.DUTIES != user.POWER || person.USER_TYPE != user.USER_TYPE  //|| person.ACADEMIC_POST != user.TECH_POST  职称不从系统数据同步
                        || person.HIS_ID != user.HIS_ID || person.PERSON_DOC_STATE != user.JOB_STATE || user.LOGID != person.LOGID
                        || person.ID_CARD != user.ID_CARD || person.PERSON_STATE != user.STATE_FLAG || person.USER_NAME != user.USERNAME)
                    {
                        person.PGROUP_ID = user.DEPT_CODE;
                        person.DUTIES = user.POWER;
                        person.ACADEMIC_POST = user.TECH_POST;
                        person.USER_TYPE = user.USER_TYPE;
                        person.LOGID = user.LOGID;
                        person.HIS_ID = user.HIS_ID;
                        person.PERSON_STATE = user.STATE_FLAG;
                        person.HOSPITAL_ID = user.HOSPITAL_ID;
                        person.USER_NAME = user.USERNAME;
                        person.PERSON_DOC_STATE = user.JOB_STATE;
                        if (user.ID_CARD != null && person.ID_CARD != user.ID_CARD && user.ID_CARD.Length == 18)
                        {
                            person.BIRTHDAY = user.ID_CARD.Substring(6, 4) + "-" + user.ID_CARD.Substring(10, 2) + "-" + user.ID_CARD.Substring(12, 2);
                            int sex = int.Parse(user.ID_CARD.Substring(14, 3));
                            if (sex % 2 == 0)
                            {
                                person.SEX = "2";
                            }
                            else
                            {
                                person.SEX = "1";
                            }
                            person.AGE = CommonHelper.CalculateAge(person.BIRTHDAY).ToString();
                        }
                        person.ID_CARD = user.ID_CARD == null ? "" : user.ID_CARD;
                        _soa.Db.Updateable<PMS_PERSON_INFO>(person).ExecuteCommand();
                    }
                }
                else
                {
                    try
                    {
                        person = new PMS_PERSON_INFO();
                        string personid = _IBaseDataServices.GetTableMax("PMS_PERSON_INFO", "PERSON_ID", 1, 1).data.ToString();
                        person.PERSON_ID = personid;
                        person.PGROUP_ID = user.DEPT_CODE;
                        person.HOSPITAL_ID = user.HOSPITAL_ID;
                        person.USER_ID = user.USER_NO;
                        person.USER_NAME = user.USERNAME;
                        person.USER_TYPE = user.USER_TYPE;
                        person.DUTIES = user.POWER;
                        person.ACADEMIC_POST = user.TECH_POST;
                        person.ID_CARD = user.ID_CARD == null ? "" : user.ID_CARD;
                        person.LOGID = user.LOGID;
                        person.HIS_ID = user.HIS_ID;
                        person.PERSON_STATE = "1";
                        person.PERSON_DOC_STATE = user.JOB_STATE;
                        if (person.ID_CARD != "" && person.ID_CARD.Length == 18)
                        {
                            person.BIRTHDAY = user.ID_CARD.Substring(6, 4) + "-" + user.ID_CARD.Substring(10, 2) + "-" + user.ID_CARD.Substring(12, 2);
                            int sex = int.Parse(user.ID_CARD.Substring(14, 3));
                            if (sex % 2 == 0)
                            {
                                person.SEX = "2";
                            }
                            else
                            {
                                person.SEX = "1";
                            }
                            person.AGE = CommonHelper.CalculateAge(person.BIRTHDAY).ToString();
                        }
                        _soa.Db.Insertable<PMS_PERSON_INFO>(person).ExecuteCommand();
                    }
                    catch (Exception ex)
                    {
                        _IBaseDataServices.GetErrorTableMax("PMS_PERSON_INFO", ex, "PERSON_ID");
                    }
                }
            }
            return person;
        }


        //public OrgTree GetGroupPersonTree(OrgUserParams orgParm, string person_name, string permissMenuId, bool isIncludeAll = false) /*string hospital_id, string lab_id, string pgroup_id, */
        //{
        //    OrgTree orgTreeNodes;
        //    List<SYS6_USER> userList;
        //    List<PMS_PERSON_INFO> personList = _IModuleLabGroupService.GetRangePersonList(orgParm, person_name, permissMenuId, out userList, out orgTreeNodes);

        //    IReadOnlyList<OrgTreeNode> groupLine = orgTreeNodes.GetAllNodes();
        //    // 获取节点最大序列（保证后续唯一性）
        //    int nodeNo = groupLine.Any() ? groupLine.Max(a => a.NODE_NO) + 1 : 0;

        //    List<string> allPgroupIds = groupLine.Where(a => a.NODE_TYPE == PMSTreeNodeTypeEnum.PGROUP.ToIntStr()).Select(a => a.SOURCE_ID).ToList();
        //    Dictionary<string, bool> personHasRecord = _IModuleLabGroupService.GetPersonHasRecord(allPgroupIds, orgParm.hospital_id);
        //    var tagDictionary = _IPmsTagService.GetPersonIdPersonTagDict(personList.Select(w => w.PERSON_ID).ToList());
        //    //遍历检验专业组
        //    foreach (var pgroup in groupLine.Where(a => a.NODE_TYPE == PMSTreeNodeTypeEnum.PGROUP.ToIntStr()))// 节点类型：0 - 用户  1 - 检验专业组  2 - 管理专业组  3 - 院区
        //    {
        //        foreach (SYS6_USER user in userList.Where(a => a.DEPT_CODE == pgroup.SOURCE_ID).OrderBy(a => a.USERNAME))
        //        {
        //            PMS_PERSON_INFO person = SyncPersonInfo(user, personList);
        //            if (person == null)
        //                continue;
        //            OrgTreeNode userNode = new OrgTreeNode();

        //            userNode.NAME = person.USER_NAME;
        //            userNode.NODE_NO = nodeNo++;
        //            userNode.NODE_TYPE = "0";
        //            userNode.NODE_TYPE_NAME = "用户";
        //            userNode.SOURCE_ID = person.USER_ID;
        //            userNode.TAGS = tagDictionary[person.PERSON_ID].Select(a => (object)a).ToList();
        //            userNode.SOURCE = new GroupPersonTreeNodeDto
        //            {
        //                PERSON_ID = person.PERSON_ID,
        //                USER_ID = person.USER_ID,
        //                PERSON_PHOTO_PATH = person.PERSON_PHOTO_PATH,
        //                PERSON_DUTIES_NAME = RecordClassBaseName("职务", person.DUTIES),
        //                PERSON_IF_COMPLETE = personHasRecord != null && personHasRecord.ContainsKey(person.PERSON_ID) ? personHasRecord[person.PERSON_ID] : false,
        //                HIS_NAME = $"{person.HIS_ID}_{person.USER_NAME}",
        //            };
        //           // userNode.SOURCE_PATH = OfficeHelper.CombinePath(pgroup.SOURCE_PATH, person.USER_ID);
        //            pgroup.ChildAdd(userNode);
        //            pgroup.NUM++; //计数
        //        }
        //    }

        //    //管理专业组汇总计数
        //    foreach (var mgroup in groupLine.Where(a => a.NODE_TYPE == PMSTreeNodeTypeEnum.MGROUP.ToIntStr()))// 节点类型：0 - 用户  1 - 检验专业组  2 - 管理专业组  3 - 院区
        //    {
        //        mgroup.NUM = mgroup.CHILDREN.Sum(a => (a as AreaGroupTreeNode).NUM);
        //    }
        //    orgTreeNodes.RefreshTreeUnique(w=>w.NODE_TYPE=="0",true);
        //    //清除计数为零的专业组
        //    orgTreeNodes.AllNodesRemove(w => (w.NUM == 0 && w.NODE_TYPE != "0") || (w.NODE_TYPE == "2" && w.NUM == 0));
        //    //foreach (var group in areaGroupTree)
        //    //{
        //    //    if (group.NODE_TYPE == "2")
        //    //        group.CHILDREN.(a => a.NUM == 0);
        //    //}

        //    if (isIncludeAll)
        //    {
        //        //"全部"
        //        OrgTreeNode allNode = new OrgTreeNode
        //        {
        //            NAME = GroupTreeNodeTypeEnum.ALL.ToDesc(),
        //            NODE_NO = nodeNo++,
        //            NODE_TYPE = GroupTreeNodeTypeEnum.ALL.ToIntStr(),
        //            NODE_TYPE_NAME = GroupTreeNodeTypeEnum.ALL.ToDesc(),
        //            //  SOURCE_PATH = GroupTreeNodeTypeEnum.ALL.ToIntStr(),
        //            NUM = orgTreeNodes.GetAllNodes().Sum(a => a.NUM)
        //        };
        //        orgTreeNodes.ChildAddByIndex(0, allNode);
        //        //orgTreeNodes.ch
        //        //resultTree.AddRange(areaGroupTree);
        //    }
    
        //    return orgTreeNodes;
        //}


        /// <summary>
        /// 获取院区-检验专业组下拉列表
        /// </summary>
        /// <param name="_soa"></param>
        /// <param name="parm"></param>
        /// <param name="user_no"></param>
        /// <param name="permissMenuId"></param>
        /// <returns></returns>
        public OrgTree GetAreaGroupDropDownList(OrgUserParams parm, string permissMenuId)
        {
            //旧逻辑
            //return _IAuthorityService.GetAreaGroupDropDownList(_soa, parm, permissMenuId);

            return _IOrganizationTreeService2.GetOrgTreeType_Area_A(_soa, parm, "H81", false);
        }
        /// <summary>
        /// 获取其他类型下拉
        /// </summary>
        /// <param name="labId"></param>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        public List<CommboxInfo> GetOtherTypeDropDwon(string labId, string hospitalId)
        {
            List<CommboxInfo> listCommbox = new List<CommboxInfo>();
            var userType = _soa.Db.Queryable<SYS6_USER_TYPE_DICT>()
                .Where(a => a.HOSPITAL_ID == hospitalId && a.LAB_ID == labId && a.USERTYPE_STATE == "1").ToList();
            var baseData = _soa.Db.Queryable<SYS6_BASE_DATA>()
                .Where(a => a.CLASS_ID == "人员类型" && a.DATA_ID != "1" && a.DATA_ID != "10" && a.DATA_ID != "13" && a.DATA_STATE == "1") //25-6-27省人悦琳/朱刚提出需求，排除【信息维护工程师】、【生物安全】
                .OrderBy(a => a.DATA_SORT).ToList();
            foreach (var item in userType)
            {
                CommboxInfo commboxInfo = new CommboxInfo();
                commboxInfo.ID = item.USERTYPE_ID;
                commboxInfo.NAME = item.USERTYPE_NAME;
                listCommbox.Add(commboxInfo);
            }
            foreach (var item in baseData)
            {
                CommboxInfo commboxInfo = new CommboxInfo();
                commboxInfo.ID = item.DATA_ID;
                commboxInfo.NAME = item.DATA_CNAME;
                listCommbox.Add(commboxInfo);
            }
            return listCommbox;
        }
    
        public OrgTree GetSMBLPersonTree_Lab(OrgUserParams orgParm, string person_name, string permissMenuId, bool isClearZero = true, bool ifCheckPermission = true)
        {
            var hospitalId = orgParm.hospital_id ?? throw new BizException("机构ID不可为空！");
            OrgTree tree = new OrgTree();

            //备案实验室入口，结构：备案实验室 - 检验专业组
            if (_httpContext.GetSmblLabId().IsNotNullOrEmpty())
            {
                var hospitaltree = _IOrganizationTreeService2.GetOrgTreeType_Smbl_A(_soa, orgParm, "H81", ifCheckPermission);
                var labNode = hospitaltree.GetAllNodes(a => a.NODE_TYPE == GroupTreeNodeTypeEnum.SMBL_LAB.ToIntStr() && a.SOURCE_ID == orgParm.smbl_lab_id);
                tree.ChildAddRange(labNode.ToList());
            }
            //科室入口，结构：科室 - 备案实验室 - 检验专业组
            else if (_httpContext.GetLabId().IsNotNullOrEmpty() && _httpContext.GetSmblLabId().IsNullOrEmpty())
            {
                var hospitaltree = _IOrganizationTreeService2.GetOrgTreeType_Smbl_A(_soa, orgParm, "H81", ifCheckPermission);
                var labNode = hospitaltree.GetAllNodes(a => a.NODE_TYPE == GroupTreeNodeTypeEnum.LAB.ToIntStr() && a.SOURCE_ID == orgParm.lab_id);
                tree.ChildAddRange(labNode.ToList());
            }
            else
            {
                tree = _IOrganizationTreeService2.GetOrgTreeType_Smbl_A(_soa, orgParm, "H81", ifCheckPermission);
            }

            var treeAllNode = tree.GetAllNodes();
            var pgroupNodes = treeAllNode.Where(a => a.NODE_TYPE == GroupTreeNodeTypeEnum.PGROUP.ToIntStr()).ToList();
            var pgroupIds = pgroupNodes.Select(a => a.SOURCE_ID).Distinct().ToList();
       
            //旧逻辑
            {
                ////连接用户表进行查询，避免人员信息表同步问题导致的数据不正确
                //var personUserList = _soa.Db.Queryable<SYS6_USER>().InnerJoin<PMS_PERSON_INFO>((u, p) => u.USER_NO == p.USER_ID)
                //   .Where((u, p) => u.STATE_FLAG == "1" && pgroupIds.Contains(u.DEPT_CODE) && p.PERSON_STATE == "1"
                //     && u.LAB_ID != null && u.HIS_ID != null  //科室、工号都不为空
                //     && u.HIS_ID != "0000" && u.HIS_ID != "00000")   //排除0000工号
                //                                                     //&& u.MANAGE_CLASS != null && u.MANAGE_CLASS == "4") //非机构级、分组级管理账号)
                //   .Select((u, p) => new { u, p }).ToList();

                ////为保证统计数据一致性，使分量数据之和等于总数据，统计前作以下限定
                //List<PMS_PERSON_INFO> personList = personUserList.Select(a => a.p)
                //    .Where(p => (p.PERSON_DOC_STATE == "1" || p.PERSON_DOC_STATE == "4") && (new List<string> { "1", "2", "3", "4", "5", "6", "7" }.Contains(p.USER_TYPE)))
                //    .Distinct().ToList();

                //////以HisID去重
                ////personList = personList.GroupBy(g => g.HIS_ID).Select(g => g.OrderBy(year => year.FIRST_RTIME).FirstOrDefault()).ToList();


                ////姓名过滤须放在去重之后，否则结果不稳定
                //if (person_name.IsNotNullOrEmpty())
                //    personList = personList.Where(p => p.USER_NAME.Contains(person_name) || (p.HIS_ID != null && p.HIS_ID.Contains(person_name))).ToList();
            }
            //新逻辑
            List<PMS_PERSON_INFO> personList = _soa.Db.Queryable<PMS_PERSON_INFO>()
                                               .LeftJoin<SYS6_INSPECTION_PGROUP>((person, pgroup) => person.PGROUP_ID == pgroup.PGROUP_ID)
                                               .Where((person, pgroup) => person.PERSON_STATE == "1" && person.HIS_ID != null && person.HIS_ID != "00000" && person.HIS_ID != "0000") //校验
                                               .WhereIF(orgParm.hospital_id.IsNotNullOrEmpty(), (person, pgroup) => person.HOSPITAL_ID == orgParm.hospital_id)
                                               .WhereIF(orgParm.lab_id.IsNotNullOrEmpty(), (person, pgroup) => person.LAB_ID == orgParm.lab_id || pgroup.LAB_ID == orgParm.lab_id)
                                               .WhereIF(orgParm.area_id.IsNotNullOrEmpty(), (person, pgroup) => pgroup.LAB_ID == orgParm.area_id)
                                               .WhereIF(orgParm.mgroup_id.IsNotNullOrEmpty(), (person, pgroup) => pgroup.MGROUP_ID == orgParm.mgroup_id)
                                               .WhereIF(orgParm.pgroup_id.IsNotNullOrEmpty(), (person, pgroup) => person.PGROUP_ID == orgParm.pgroup_id)
                                               .Select((person, pgroup) => person)
                                               .ToList();

            Dictionary<string, bool> personHasRecord = _IModuleLabGroupService.GetPersonHasRecord(pgroupIds, orgParm.hospital_id);
            var smblTagsDictionary = _IPmsTagService.GetPersonIdPersonTagDict(personList.Select(w => w.PERSON_ID).ToList(), systemType: "3");//systemType: "3" - 生安

            void NodeAddPerson(OrgTreeNode node, PMS_PERSON_INFO p)
            {
                var personTags = smblTagsDictionary[p.PERSON_ID];
                if (personTags.Any())
                {
                    var personNode = new OrgTreeNode
                    {
                        NODE_TYPE = PMSTreeNodeTypeEnum.PERSON.ToIntStr(),
                        NODE_TYPE_NAME = PMSTreeNodeTypeEnum.PERSON.ToDesc(),
                        SOURCE_ID = p.PERSON_ID,
                        SOURCE = p,
                        NAME = p.USER_NAME,
                        SORT = p.FIRST_RTIME,
                        TAGS = personTags.Select(a => (object)a).ToList(),
                    };
                    if (personHasRecord == null || !personHasRecord.ContainsKey(p.PERSON_ID) || personHasRecord[p.PERSON_ID] == false)
                        personNode.STATES.Add("uncomplete");

                    node.ChildAdd(personNode);
                }
            }
            foreach (var person in personList)
            {
                bool isAssign = false;

                foreach (var pgroupNode in pgroupNodes.Where(a => person.PGROUP_ID == a.SOURCE_ID))
                {
                    NodeAddPerson(pgroupNode, person);
                    isAssign = true;
                }
                if (!isAssign)
                {
                    if (person.LAB_ID.IsNotNullOrEmpty())
                    {
                        var labLevelNode = treeAllNode.Where(a => a.NODE_TYPE == GroupTreeNodeTypeEnum.LAB_LEVEL.ToIntStr() && a.SOURCE_ID == person.LAB_ID).FirstOrDefault();
                        if (labLevelNode != null)
                        {
                            NodeAddPerson(labLevelNode, person);
                        }
                    }
                    else if (person.HOSPITAL_ID.IsNotNullOrEmpty())
                    {
                        var hospitalLevelNode = treeAllNode.Where(a => a.NODE_TYPE == GroupTreeNodeTypeEnum.HOSPITAL_LEVEL.ToIntStr() && a.SOURCE_ID == person.HOSPITAL_ID).FirstOrDefault();
                        if (hospitalLevelNode != null)
                        {
                            NodeAddPerson(hospitalLevelNode, person);
                        }
                    }
                }
            }
            //自动更新NUM计数、SOURCE_PATH、NODE_NO
            tree.RefreshTreeUnique(a => a.NODE_TYPE == PMSTreeNodeTypeEnum.PERSON.ToIntStr(), true);
            ////清除空节点
            //if (_configuration.GetValue<string>("isSMBLTreeClearZeroNode") != "0")
                tree.AllNodesRemove(a => a.NUM == 0 && a.NODE_TYPE != GroupTreeNodeTypeEnum.ALL.ToIntStr());
            return tree;
        }

        #region H94迎评调用
        public OrgTree GetHistoryPersonTree_SMBL_1(HistoryPersonTreeParm parm)
        {
            if (parm.PGROUP_ID.IsNullOrEmpty())
                throw new Exception("PGROUP_ID不能为空！");

            OrgTree tree = new OrgTree();
            var orgParm = new OrgParams {hospital_id = parm.HOSPITAL_ID, lab_id = parm.LAB_ID, area_id = parm.AREA_ID, pgroup_id = parm.PGROUP_ID, smbl_lab_id = parm.SMBL_LAB_ID};

            //备案实验室入口，结构：备案实验室 - 检验专业组
            if (_httpContext.GetSmblLabId().IsNotNullOrEmpty())
            {
                var hospitaltree = _IOrganizationTreeService2.GetOrgTreeType_Smbl_A(_soa, orgParm, "H81", true);
                var labNode = hospitaltree.GetAllNodes(a => a.NODE_TYPE == GroupTreeNodeTypeEnum.SMBL_LAB.ToIntStr() && a.SOURCE_ID == orgParm.smbl_lab_id);
                tree.ChildAddRange(labNode.ToList());
            }
            //科室入口，结构：科室 - 备案实验室 - 检验专业组
            else if (_httpContext.GetLabId().IsNotNullOrEmpty() && _httpContext.GetSmblLabId().IsNullOrEmpty())
            {
                var hospitaltree = _IOrganizationTreeService2.GetOrgTreeType_Smbl_A(_soa, orgParm, "H81", true);
                var labNode = hospitaltree.GetAllNodes(a => a.NODE_TYPE == GroupTreeNodeTypeEnum.LAB.ToIntStr() && a.SOURCE_ID == orgParm.lab_id);
                tree.ChildAddRange(labNode.ToList());
            }
            else
            {
                tree = _IOrganizationTreeService2.GetOrgTreeType_Smbl_A(_soa, orgParm, "H81", true);
            }

            var treeAllNode = tree.GetAllNodes();
            var pgroupNode = treeAllNode.Where(a => a.NODE_TYPE == GroupTreeNodeTypeEnum.PGROUP.ToIntStr() && a.SOURCE_ID == parm.PGROUP_ID).FirstOrDefault();

            if (pgroupNode == null)
            {
                tree = new OrgTree();
                var historyPgroup = _soa.Db.Queryable<SYS6_INSPECTION_PGROUP>().Where(a => a.PGROUP_ID == parm.PGROUP_ID).First();
                pgroupNode = new OrgTreeNode
                {
                    NODE_TYPE = PMSTreeNodeTypeEnum.PGROUP.ToIntStr(),
                    NODE_TYPE_NAME = PMSTreeNodeTypeEnum.PGROUP.ToDesc(),
                    SOURCE_ID = historyPgroup.PGROUP_ID,
                    SOURCE = historyPgroup,
                    NAME = historyPgroup.PGROUP_NAME,
                };

                tree.ChildAdd(pgroupNode);
            }

            //新逻辑
            List<PMS_PERSON_INFO> personList = _soa.Db.Queryable<PMS_PERSON_INFO>()
                                               .LeftJoin<SYS6_INSPECTION_PGROUP>((person, pgroup) => person.PGROUP_ID == pgroup.PGROUP_ID)
                                               .Where((person, pgroup) => parm.PERSON_IDS.Contains(person.PERSON_ID))
                                               .Where((person, pgroup) => person.PERSON_STATE == "1" && person.HIS_ID != null && person.HIS_ID != "00000" && person.HIS_ID != "0000") //校验
                                               .WhereIF(orgParm.hospital_id.IsNotNullOrEmpty(), (person, pgroup) => person.HOSPITAL_ID == orgParm.hospital_id)
                                               .WhereIF(orgParm.lab_id.IsNotNullOrEmpty(), (person, pgroup) => person.LAB_ID == orgParm.lab_id || pgroup.LAB_ID == orgParm.lab_id)
                                               .WhereIF(orgParm.area_id.IsNotNullOrEmpty(), (person, pgroup) => pgroup.LAB_ID == orgParm.area_id)
                                               .WhereIF(orgParm.mgroup_id.IsNotNullOrEmpty(), (person, pgroup) => pgroup.MGROUP_ID == orgParm.mgroup_id)
                                               .WhereIF(orgParm.pgroup_id.IsNotNullOrEmpty(), (person, pgroup) => person.PGROUP_ID == orgParm.pgroup_id)
                                               .WhereIF(parm.PERSON_NAME.IsNotNullOrEmpty(), (person, pgroup) => person.USER_NAME.Contains(parm.PERSON_NAME))
                                               .Select((person, pgroup) => person)
                                               .ToList();

            Dictionary<string, bool> personHasRecord = _IModuleLabGroupService.GetPersonHasRecord(new List<string> { parm.PGROUP_ID}, parm.HOSPITAL_ID);
            var smblTagsDictionary = _IPmsTagService.GetPersonIdPersonTagDict(personList.Select(w => w.PERSON_ID).ToList(), systemType: "3");//systemType: "3" - 生安

            OrgTreeNode GetPersonNode(PMS_PERSON_INFO p)
            {
                var personNode = new OrgTreeNode
                {
                    NODE_TYPE = PMSTreeNodeTypeEnum.PERSON.ToIntStr(),
                    NODE_TYPE_NAME = PMSTreeNodeTypeEnum.PERSON.ToDesc(),
                    SOURCE_ID = p.PERSON_ID,
                    SOURCE = p,
                    NAME = p.USER_NAME,
                    SORT = p.FIRST_RTIME,
                    TAGS = smblTagsDictionary[p.PERSON_ID].Select(a => (object)a).ToList()
                };
                if (personHasRecord == null || !personHasRecord.ContainsKey(p.PERSON_ID) || personHasRecord[p.PERSON_ID] == false)
                    personNode.STATES.Add("uncomplete");
                return personNode;
            }

            foreach (var person in personList)
            {
                pgroupNode.ChildAdd(GetPersonNode(person));
            }
            //自动更新NUM计数、SOURCE_PATH、NODE_NO
            tree.RefreshTreeUnique(a => a.NODE_TYPE == PMSTreeNodeTypeEnum.PERSON.ToIntStr(), true);
            ////清除空节点
            //if (_configuration.GetValue<string>("isSMBLTreeClearZeroNode") != "0")
            tree.AllNodesRemove(a => a.NUM == 0 && a.NODE_TYPE != GroupTreeNodeTypeEnum.ALL.ToIntStr());
            return tree;
        }
        #endregion

        //private OrgTree GetSMBLPersonTree_SMBLLab(OrgUserParams orgParm, string permissMenuId)
        //{
        //    OrgTree tree = new OrgTree();
        //    //全部
        //    var allNode = new OrgTreeNode
        //    {
        //        NAME = GroupTreeNodeTypeEnum.ALL.ToDesc(),
        //        NODE_TYPE = GroupTreeNodeTypeEnum.ALL.ToIntStr(),
        //        NODE_TYPE_NAME = GroupTreeNodeTypeEnum.ALL.ToDesc(),
        //    };
        //    tree.ChildAdd(allNode);

        //    var smblLabs = _soa.Db.Queryable<SMBL_LAB>()
        //       .Where(year => year.HOSPITAL_ID == orgParm.hospital_id && year.SMBL_LAB_STATE == "1")
        //       .WhereIF(orgParm.area_id.IsNotNullOrEmpty(), year => year.AREA_ID == orgParm.area_id)
        //       .WhereIF(orgParm.smbl_lab_id.IsNotNullOrEmpty(), year => year.SMBL_LAB_ID == orgParm.smbl_lab_id)
        //       .ToList();

        //    var pgroups = _soa.Db.Queryable<SYS6_INSPECTION_PGROUP>()
        //        .Where(year => year.PGROUP_STATE == "1" && year.MGROUP_ID != null && year.PGROUP_CLASS != "3" && year.PGROUP_CLASS != "4")//排除临床、外送专业组
        //        .WhereIF(orgParm.lab_id.IsNotNullOrEmpty(), year => year.LAB_ID == orgParm.lab_id)
        //        .WhereIF(orgParm.area_id.IsNotNullOrEmpty(), year => year.AREA_ID == orgParm.area_id)
        //        .WhereIF(orgParm.mgroup_id.IsNotNullOrEmpty(), year => year.MGROUP_ID == orgParm.mgroup_id)
        //        .WhereIF(orgParm.pgroup_id.IsNotNullOrEmpty(), year => year.PGROUP_ID == orgParm.pgroup_id)
        //        .ToList();

        //    foreach (var smlab in smblLabs)
        //    {
        //        var smlabNode = new OrgTreeNode
        //        {
        //            NODE_TYPE = GroupTreeNodeTypeEnum.SMBL_LAB.ToIntStr(),
        //            NODE_TYPE_NAME = GroupTreeNodeTypeEnum.SMBL_LAB.ToDesc(),
        //            SOURCE_ID = smlab.SMBL_LAB_ID,
        //            NAME = smlab.SMBL_LAB_CNAME,
        //            SORT = smlab.FIRST_RTIME.ToString("yyyyMMddhh"),
        //        };
        //        allNode.ChildAdd(smlabNode);

        //        string[] pgrougIdss = smlab.PGROUP_SID.Split(',');
        //        foreach (var pgroup in pgroups.Where(year => pgrougIdss.Contains(year.PGROUP_ID)))
        //        {
        //            var pgroupNode = new OrgTreeNode
        //            {
        //                NODE_TYPE = GroupTreeNodeTypeEnum.PGROUP.ToIntStr(),
        //                NODE_TYPE_NAME = GroupTreeNodeTypeEnum.PGROUP.ToDesc(),
        //                SOURCE_ID = pgroup.PGROUP_ID,
        //                NAME = pgroup.PGROUP_NAME,
        //                SORT = pgroup.PGROUP_SORT,
        //            };
        //            smlabNode.ChildAdd(pgroupNode);
        //        }
        //    }
        //    return tree;
        //}

        //TODO 权限判断
        public OrgTree GetSMBLPersonTree_Post(OrgUserParams orgParm, string person_name, string permissMenuId, string postId)
        {
            var postTree = GetSmblPostTree(orgParm, permissMenuId);
            var postNodeList = postTree.GetAllNodesByType(PMSTreeNodeTypeEnum.SMBL_POST.ToIntStr());
            List<string> postIds = postNodeList.Select(a => a.SOURCE_ID).Distinct().ToList();

            var persons = _soa.Db.Queryable<PMS_PERSON_INFO>()
                .InnerJoin<SMBL_POST_PERSON>((p, s) => p.PERSON_ID == s.PERSON_ID)
                .Where((p, s) => p.PERSON_STATE == "1" && s.POST_PSTATE == "1")
                .WhereIF(postId.IsNotNullOrEmpty(), (p, s) => s.DEPT_POST_ID == postId)
                .WhereIF(person_name.IsNotNullOrEmpty(), (p, s) => p.USER_NAME.Contains(person_name))
                .Select((p, s) => new SimplePersonInfo
                {
                    POST_ID = s.DEPT_POST_ID,
                    PERSON_ID = p.PERSON_ID.SelectAll()
                })
                .ToList();

            foreach (var postNode in postNodeList)
            {
                foreach (var person in persons.Where(a => a.POST_ID == postNode.SOURCE_ID))
                {
                    var personNode = new OrgTreeNode
                    {
                        NODE_TYPE = PMSTreeNodeTypeEnum.PERSON.ToIntStr(),
                        NODE_TYPE_NAME = PMSTreeNodeTypeEnum.PERSON.ToDesc(),
                        SOURCE_ID = person.PERSON_ID,
                        SOURCE = person,
                        NAME = person.USER_NAME,
                        SORT = person.FIRST_RTIME,
                    };
                    postNode.ChildAdd(personNode);
                }
            }

            //自动更新NUM计数、SOURCE_PATH、NODE_NO
            postTree.RefreshTree(a => a.NODE_TYPE == PMSTreeNodeTypeEnum.PERSON.ToIntStr(), true);
            ////清除空节点
            //postTree.AllNodesRemove(year => year.NUM == 0);
            return postTree;
        }

        public OrgTree GetISOPersonTree_Lab(OrgUserParams orgParm, string person_name, string permissMenuId)
        {
            //增加“全部”节点
            var topNode = new OrgTreeNode
            {
                NAME = GroupTreeNodeTypeEnum.ALL.ToDesc(),
                NODE_TYPE = GroupTreeNodeTypeEnum.ALL.ToIntStr(),
                NODE_TYPE_NAME = GroupTreeNodeTypeEnum.ALL.ToDesc(),
            };
            return GetISOLabPersonTree(orgParm, person_name, permissMenuId, topNode);
        }

        public OrgTree GetISOPersonTree_Area(OrgUserParams orgParm, string person_name, string permissMenuId)
        {
            OrgTree tree = new OrgTree();

            //增加“全部”节点
            var topNode = new OrgTreeNode
            {
                NAME = GroupTreeNodeTypeEnum.ALL.ToDesc(),
                NODE_TYPE = GroupTreeNodeTypeEnum.ALL.ToIntStr(),
                NODE_TYPE_NAME = GroupTreeNodeTypeEnum.ALL.ToDesc(),
            };
            tree.ChildAdd(topNode);

            #region 旧逻辑
            //var pgroups = _IAuthorityService.GetUserPermissionPgroup(_soa, orgParm, permissMenuId);
            //var areaIds = pgroups.Select(g => g.AREA_ID).Distinct().ToList();
            //var areas = _soa.Db.Queryable<SYS6_INSPECTION_AREA>().Where(a => a.STATE_FLAG == "1" && areaIds.Contains(a.AREA_ID)).ToList();

            ////人员分类
            //var userClassList = _soa.Db.Queryable<SYS6_USER_CLASS_DICT>().Where(a => a.USERCLASS_STATE == "1")
            //    .WhereIF(orgParm.hospital_id.IsNotNullOrEmpty(), a => a.HOSPITAL_ID == orgParm.hospital_id)
            //    .WhereIF(orgParm.lab_id.IsNotNullOrEmpty(), a => a.LAB_ID == orgParm.lab_id)
            //    .ToList()
            //    .WhereIF(orgParm.area_id.IsNotNullOrEmpty(), a => a.AREA_SID != null && a.AREA_SID.Split('+').Contains(orgParm.area_id))
            //    .ToList();

            //var pgroupdIds = pgroups.Select(a => a.PGROUP_ID).ToList();
            //pgroupdIds.AddRange(userClassList.Select(a => a.USERCLASS_ID).ToList());
            //Dictionary<string, bool> personHasRecord = _IModuleLabGroupService.GetPersonHasRecord(pgroupdIds, orgParm.hospital_id);

            //var personList = _soa.Db.Queryable<PMS_PERSON_INFO>()
            //    .Where(p => p.PERSON_STATE == "1"
            //    && (p.PERSON_TYPE == "1" || p.PERSON_TYPE == null) //兼容旧系统数据，PERSON_TYPE为空
            //    && pgroupdIds.Contains(p.PGROUP_ID)
            //     //&& p.LAB_ID != null //科室不为空
            //     //&& p.HIS_ID != null  //工号不为空
            //     && p.HIS_ID != "0000" && p.HIS_ID != "00000")   //排除0000工号
            //                                                     //&& u.MANAGE_CLASS != null && u.MANAGE_CLASS == "4") //非机构级、分组级管理账号)
            //    .WhereIF(person_name.IsNotNullOrEmpty(), p => p.USER_NAME.Contains(person_name))
            //    .ToList()
            //    .Where(p => (p.PERSON_DOC_STATE == "1" || p.PERSON_DOC_STATE == "4") && (new List<string> { "1", "2", "3", "4", "5", "6", "7" }.Contains(p.USER_TYPE)))
            //    .OrderBy(p => p.PGROUP_ID).ThenBy(p => p.USER_NAME)
            //    .ToList();

            //var tagsDictionary = _IPmsTagService.GetPersonIdPersonTagDict(personList.Select(w => w.PERSON_ID).ToList());

            //foreach (var area in areas)
            //{
            //    var areaNode = CreateTreeChildNode(area.AREA_ID, area.AREA_NAME, area.AREA_SORT, null, GroupTreeNodeTypeEnum.AREA.ToIntStr(), GroupTreeNodeTypeEnum.AREA.ToDesc());
            //    topNode.ChildAdd(areaNode);

            //    foreach (var pgroup in pgroups.Where(a => a.AREA_ID == area.AREA_ID))
            //    {
            //        var pgroupNode = CreateTreeChildNode(pgroup.PGROUP_ID, pgroup.PGROUP_NAME, pgroup.PGROUP_SORT, pgroup.LAB_ID, GroupTreeNodeTypeEnum.PGROUP.ToIntStr(), GroupTreeNodeTypeEnum.PGROUP.ToDesc());
            //        areaNode.ChildAdd(pgroupNode);

            //        foreach (var person in personList.Where(a => a.PGROUP_ID == pgroup.PGROUP_ID/* && year.AREA_ID == area.AREA_ID*/))
            //        {
            //            pgroupNode.ChildAdd(GetPersonNode(person, tagsDictionary));
            //        }
            //    }

            //    foreach (var userClass in userClassList.Where(a => a.AREA_SID != null && a.AREA_SID.Split('+').Contains(area.AREA_ID)))
            //    {
            //        var userClassNode = CreateTreeChildNode(userClass.USERCLASS_ID, userClass.USERCLASS_NAME, userClass.USERCLASS_SORT, userClass.LAB_ID, GroupTreeNodeTypeEnum.CLASS.ToIntStr(), GroupTreeNodeTypeEnum.CLASS.ToDesc());
            //        areaNode.ChildAdd(userClassNode);

            //        foreach (var person in personList.Where(a => a.PGROUP_ID == userClass.USERCLASS_ID && a.AREA_ID == area.AREA_ID))
            //        {
            //            userClassNode.ChildAdd(GetPersonNode(person, tagsDictionary));
            //        }
            //    }
            //}
            #endregion

            var areaTree = _IOrganizationTreeService2.GetOrgTreeType_Area_A(_soa, orgParm, "H81", true);

            var areaNodes = areaTree.GetAllNodesByType(GroupTreeNodeTypeEnum.AREA.ToIntStr());
            var pgroupNodes = areaTree.GetAllNodesByType(GroupTreeNodeTypeEnum.PGROUP.ToIntStr());
            var pgroupdIds = pgroupNodes.Select(a => a.SOURCE_ID).ToList();

            //人员分类
            var userClassList = _soa.Db.Queryable<SYS6_USER_CLASS_DICT>().Where(a => a.USERCLASS_STATE == "1")
                .WhereIF(orgParm.hospital_id.IsNotNullOrEmpty(), a => a.HOSPITAL_ID == orgParm.hospital_id)
                .WhereIF(orgParm.lab_id.IsNotNullOrEmpty(), a => a.LAB_ID == orgParm.lab_id)
                .ToList()
                .WhereIF(orgParm.area_id.IsNotNullOrEmpty(), a => a.AREA_SID != null && a.AREA_SID.Split('+').Contains(orgParm.area_id))
                .ToList();

            pgroupdIds.AddRange(userClassList.Select(a => a.USERCLASS_ID).ToList());
            Dictionary<string, bool> personHasRecord = _IModuleLabGroupService.GetPersonHasRecord(pgroupdIds, orgParm.hospital_id);

            var personList = _soa.Db.Queryable<PMS_PERSON_INFO>()
                .Where(person => person.PERSON_STATE == "1"
                    && (person.PERSON_TYPE == "1" || person.PERSON_TYPE == null) //兼容旧系统数据，PERSON_TYPE为空 //&& person.LAB_ID != null //科室不为空 //&& person.HIS_ID != null  //工号不为空
                    && (person.HIS_ID != null && person.HIS_ID != "0000" && person.HIS_ID != "00000")   //排除0000工号 //&& u.MANAGE_CLASS != null && u.MANAGE_CLASS == "4") //非机构级、分组级管理账号)
                    //&& (person.PERSON_DOC_STATE == "1" || person.PERSON_DOC_STATE == "4") 
                    && (new string[] { "1", "2", "3", "4", "5", "6", "7" }.Contains(person.USER_TYPE)))
                .Where(p => pgroupdIds.Contains(p.PGROUP_ID))
                .WhereIF(person_name.IsNotNullOrEmpty(), p => p.USER_NAME.Contains(person_name))
                .ToList()
                .OrderBy(p => p.PGROUP_ID).ThenBy(p => p.USER_NAME)
                .ToList();

            var tagsDictionary = _IPmsTagService.GetPersonIdPersonTagDict(personList.Select(w => w.PERSON_ID).ToList());
            //专业组节点
            foreach (var pgroupNode in pgroupNodes)
            {
                foreach (var person in personList.Where(a => a.PGROUP_ID == pgroupNode.SOURCE_ID))
                {
                    pgroupNode.ChildAdd(GetPersonNode(person, tagsDictionary));
                }
            }

            foreach (var areaNode in areaNodes.OrderBy(a => a.SORT))
            {
                //挂上院区节点
                tree.ChildAdd(areaNode);
                //人员分类节点
                foreach (var userClass in userClassList.Where(a => a.AREA_SID != null && a.AREA_SID.Split('+').Contains(areaNode.SOURCE_ID)).OrderBy(a => a.USERCLASS_SORT))
                {
                    var userClassNode = CreateTreeChildNode(userClass.USERCLASS_ID, userClass.USERCLASS_NAME, userClass.USERCLASS_SORT, userClass.LAB_ID, GroupTreeNodeTypeEnum.CLASS.ToIntStr(), GroupTreeNodeTypeEnum.CLASS.ToDesc());
                    areaNode.ChildAdd(userClassNode);

                    foreach (var person in personList.Where(a => a.PGROUP_ID == userClass.USERCLASS_ID/* && a.AREA_ID == areaNode.AREA_ID*/))
                    {
                        userClassNode.ChildAdd(GetPersonNode(person, tagsDictionary));
                    }
                }
            }

            OrgTreeNode GetPersonNode(PMS_PERSON_INFO person, Dictionary<string, List<PmsPersonTagDictDto>> tagsDictionary)
            {
                OrgTreeNode personNode = new OrgTreeNode();
                personNode.NAME = person.USER_NAME;
                personNode.NODE_TYPE = GroupTreeNodeTypeEnum.USER.ToIntStr();
                personNode.NODE_TYPE_NAME = GroupTreeNodeTypeEnum.USER.ToDesc();
                personNode.SOURCE_ID = person.PERSON_ID;
                personNode.SOURCE = new GroupPersonTreeNodeDto
                {
                    PERSON_ID = person.PERSON_ID,
                    USER_ID = person.USER_ID,
                    PERSON_PHOTO_PATH = person.PERSON_PHOTO_PATH,
                    PERSON_DUTIES_NAME = RecordClassBaseName("职务", person.DUTIES),
                    //PERSON_IF_COMPLETE = personHasRecord != null && personHasRecord.ContainsKey(person.PERSON_ID) ? personHasRecord[person.PERSON_ID] : false,
                    HIS_NAME = $"{person.HIS_ID}_{person.USER_NAME}",
                    SEX = person.SEX,

                };
                personNode.TAGS = tagsDictionary[person.PERSON_ID].Select(a => (object)a).ToList();
                if (personHasRecord == null || !personHasRecord.ContainsKey(person.PERSON_ID) || personHasRecord[person.PERSON_ID] == false)
                    personNode.STATES.Add("uncomplete");
                return personNode;
            }

            tree.RefreshTree(GroupTreeNodeTypeEnum.USER.ToIntStr());
            tree.AllNodesRemove(a => a.NUM == 0);
            return tree;
        }

        public OrgTree GetISOLabPersonTree(OrgUserParams orgParm, string person_name, string permissMenuId, OrgTreeNode topNode, bool ifCheckPremission = true)
        {
            OrgTree resultTree = new OrgTree();

            var personrange = _IModuleLabGroupService.GetPgroupRangePersonList(orgParm, person_name, permissMenuId, ifCheckPremission);

            if (topNode != null)
            {
                resultTree.ChildAdd(topNode);
                topNode.ChildAddRange(personrange.Tree.CHILDREN.ToList());
            }
            else
            {
                resultTree = personrange.Tree;
            }

            var pgroupNodes = personrange.Tree.GetAllNodes(a => a.NODE_TYPE == PMSTreeNodeTypeEnum.PGROUP.ToIntStr() || a.NODE_TYPE == GroupTreeNodeTypeEnum.CLASS.ToIntStr());
            Dictionary<string, bool> personHasRecord = _IModuleLabGroupService.GetPersonHasRecord(personrange.PgroupIds, orgParm.hospital_id);
            var tagDictionary = _IPmsTagService.GetPersonIdPersonTagDict(personrange.PersonList.Select(w => w.PERSON_ID).ToList());
            foreach (var person in personrange.PersonList)
            {
                var parentNode = pgroupNodes.Where(a => person.PGROUP_ID == a.SOURCE_ID && person.PERSON_TYPE == "1").FirstOrDefault();
                if (parentNode != null)
                {
                    OrgTreeNode personNode = new OrgTreeNode();
                    personNode.NAME = person.USER_NAME;
                    personNode.NODE_TYPE = GroupTreeNodeTypeEnum.USER.ToIntStr();
                    personNode.NODE_TYPE_NAME = GroupTreeNodeTypeEnum.USER.ToDesc();
                    personNode.SOURCE_ID = person.PERSON_ID;
                    personNode.SOURCE = person;
                    personNode.TAGS = tagDictionary[person.PERSON_ID].Select(a => (object)a).ToList();
                    //personNode.SOURCE = new GroupPersonTreeNodeDto
                    //{
                    //    PERSON_ID = person.PERSON_ID,
                    //    USER_ID = person.USER_ID,
                    //    PERSON_PHOTO_PATH = person.PERSON_PHOTO_PATH,
                    //    //PERSON_DUTIES_NAME = RecordClassBaseName("职务", person.DUTIES),
                    //    //PERSON_IF_COMPLETE = personHasRecord != null && personHasRecord.ContainsKey(person.PERSON_ID) ? personHasRecord[person.PERSON_ID] : false,
                    //    HIS_NAME = $"{person.HIS_ID}_{person.USER_NAME}",
                    //    SEX = person.SEX,
                    //};
                    if (personHasRecord == null || !personHasRecord.ContainsKey(person.PERSON_ID) || personHasRecord[person.PERSON_ID] == false)
                        personNode.STATES.Add("uncomplete");
                    parentNode.ChildAdd(personNode);
                }
            }
            resultTree.RefreshTree(GroupTreeNodeTypeEnum.USER.ToIntStr());
            resultTree.AllNodesRemove(a => a.NUM == 0);

            return resultTree;
        }

        public OrgTree GetISOPersonTree_LabOther(OrgUserParams orgParm, string person_name, string permissMenuId)
        {
            //增加“科室职工”节点
            var topNode = new OrgTreeNode
            {
                NAME = "科室职工",
                NODE_TYPE = "8190",
                SOURCE_ID = "8190",
                NODE_TYPE_NAME = "科室职工",
            };
            var tree = GetISOLabPersonTree(orgParm, person_name, permissMenuId, topNode);

            //增加“其他类型”节点
            var otherNode = new OrgTreeNode
            {
                NAME = "其他类型",
                NODE_TYPE = "8191",
                SOURCE_ID = "8191",
                NODE_TYPE_NAME = "其他类型",
            };
            tree.ChildAdd(otherNode);

            //从人事其它类型树中，获取科室节点下的所有“其它类型”节点
            var otherTree = GetISOPersonTree_Other(orgParm, person_name, permissMenuId, null);
            var otherNodes = otherTree.GetAllNodes(a => a.NODE_TYPE == GroupTreeNodeTypeEnum.TYPE.ToIntStr());
            if (otherNodes.Any())
                otherNode.ChildAddRange(otherNodes.ToList());

            tree.RefreshTree(a => a.NODE_TYPE == GroupTreeNodeTypeEnum.USER.ToIntStr());
            return tree;
        }

        public OrgTree GetISOPersonTree_Other(OrgUserParams orgParm, string person_name, string permissMenuId, string personType)
        {
            var tree = new OrgTree();
            var lab = _soa.Db.Queryable<SYS6_INSPECTION_LAB>()
             .Where(a => a.HOSPITAL_ID == orgParm.hospital_id && a.STATE_FLAG == "1" && a.LAB_ID == orgParm.lab_id)
             .ToList()?.FirstOrDefault();
            if (lab == null)
                return tree;
            var userType = _soa.Db.Queryable<SYS6_USER_TYPE_DICT>()
                .Where(a => a.HOSPITAL_ID == orgParm.hospital_id && a.LAB_ID == lab.LAB_ID && a.USERTYPE_STATE == "1")
                .WhereIF(personType.IsNotNullOrEmpty(), a => a.USERTYPE_ID == personType).ToList();

            var varPerson = _soa.Db.Queryable<PMS_PERSON_INFO>().Where(a => a.PERSON_TYPE != "1" && a.PERSON_TYPE != null && a.PERSON_STATE == "1" && a.HOSPITAL_ID == orgParm.hospital_id)
                .WhereIF(person_name.IsNotNullOrEmpty(), a => a.USER_NAME.Contains(person_name)).ToList();
            var tagDictionary = _IPmsTagService.GetPersonIdPersonTagDict(varPerson.Select(w => w.PERSON_ID).ToList());

            List<string> allPgroupIds = varPerson.Select(a => a.PGROUP_ID).Distinct().ToList();
            Dictionary<string, bool> personHasRecord = _IModuleLabGroupService.GetPersonHasRecord(allPgroupIds, orgParm.hospital_id);
            var baseData = _soa.Db.Queryable<SYS6_BASE_DATA>().Where(a => a.CLASS_ID == "人员类型" && a.DATA_ID != "10" && a.DATA_ID != "13" && a.DATA_STATE == "1") //25-6-27省人悦琳/朱刚提出需求，排除【信息维护工程师】、【生物安全】
                                .WhereIF(personType.IsNotNullOrEmpty(), a => a.DATA_ID == personType).OrderBy(a => a.DATA_SORT).ToList();
            var labNode = CreateTreeChildNode(lab.LAB_ID, lab.LAB_NAME, lab.LAB_SORT, lab.LAB_ID, GroupTreeNodeTypeEnum.LAB.ToIntStr(), GroupTreeNodeTypeEnum.LAB.ToDesc());
            tree.ChildAdd(labNode);

            foreach (var item in baseData)
            {
                var typeNode = CreateTreeChildNode(item.DATA_ID, item.DATA_CNAME, item.DATA_SORT, item.LAB_ID, GroupTreeNodeTypeEnum.TYPE.ToIntStr(), GroupTreeNodeTypeEnum.TYPE.ToDesc());
                labNode.ChildAdd(typeNode);
                List<PMS_PERSON_INFO> listFilter = varPerson.FindAll(w => w.PERSON_TYPE == item.DATA_ID);
                foreach (var person in listFilter)
                {
                    var personNode = CreateTreeChildNode(person.PERSON_ID, person.USER_NAME, "", person.LAB_ID, GroupTreeNodeTypeEnum.USER.ToIntStr(), GroupTreeNodeTypeEnum.USER.ToDesc());
                    personNode.SOURCE = person;
                    personNode.TAGS = tagDictionary[person.PERSON_ID].Select(a => (object)a).ToList();
                    if (personHasRecord == null || !personHasRecord.ContainsKey(person.PERSON_ID) || personHasRecord[person.PERSON_ID] == false)
                        personNode.STATES.Add("uncomplete");
                    typeNode.ChildAdd(personNode);
                }
            }
            foreach (var item in userType)
            {
                var typeNode = CreateTreeChildNode(item.USERTYPE_ID, item.USERTYPE_NAME, item.USERTYPE_SORT, item.LAB_ID, GroupTreeNodeTypeEnum.TYPE.ToIntStr(), GroupTreeNodeTypeEnum.TYPE.ToDesc());
                labNode.ChildAdd(typeNode);
                List<PMS_PERSON_INFO> listFilter = varPerson.FindAll(w => w.PERSON_TYPE == item.USERTYPE_ID);
                foreach (var person in listFilter)
                {
                    var personNode = CreateTreeChildNode(person.PERSON_ID, person.USER_NAME, "", person.LAB_ID, GroupTreeNodeTypeEnum.USER.ToIntStr(), GroupTreeNodeTypeEnum.USER.ToDesc());
                    personNode.SOURCE = person;
                    personNode.TAGS = tagDictionary[person.PERSON_ID].Select(a => (object)a).ToList();
                    if (personHasRecord == null || !personHasRecord.ContainsKey(person.PERSON_ID) || personHasRecord[person.PERSON_ID] == false)
                        personNode.STATES.Add("uncomplete");
                    typeNode.ChildAdd(personNode);
                }
            }
            tree.RefreshTree(a => a.NODE_TYPE == GroupTreeNodeTypeEnum.USER.ToIntStr(), true);
            //清除空节点
            tree.AllNodesRemove(a => a.NUM == 0);
            return tree;
        }
        /// <summary>
        /// 生成树子节点
        /// </summary>
        /// <param name="sourceId"></param>
        /// <param name="name"></param>
        /// <param name="sort"></param>
        /// <param name="labId"></param>
        /// <param name="nodeType"></param>
        /// <param name="nodeTypeName"></param>
        /// <returns></returns>
        private OrgTreeNode CreateTreeChildNode(string sourceId, string name, string sort, string labId, string nodeType, string nodeTypeName)
        {
            var node = new OrgTreeNode();
            node.SOURCE_ID = sourceId;
            node.NAME = name;
            node.SORT = sort;
            node.LAB_ID = labId;
            node.NODE_TYPE = nodeType;
            node.NODE_TYPE_NAME = nodeTypeName;
            return node;
        }

        public OrgTree GetSmblDeptTree(OrgUserParams orgParm, string permissMenuId, string smblDeptNodeId, string deptKeyWord)
        {
            var hospitalId = orgParm.hospital_id ?? throw new BizException("机构ID不可为空！");
            var tree = new OrgTree();
            var org = _soa.Db.Queryable<SMBL_ORG>().Where(a => a.HOSPITAL_ID == hospitalId && a.ORG_STATE == "2").First() ?? new SMBL_ORG();
            var hospital = _soa.Db.Queryable<SMBL_HOSPITAL>().Where(a => a.HOSPITAL_ID == hospitalId && a.REGISTER_STATE == "1").First();//已注册
            var smblNodes = _soa.Db.Queryable<SMBL_DEPT_NODE>()
                .Where(a => a.HOSPITAL_ID == hospitalId && a.CURR_NODE_STATE == "1")
                .WhereIF(org.ORG_ID.IsNotNullOrEmpty(), a => a.ORG_ID == org.ORG_ID)
                .WhereIF(orgParm.smbl_lab_id.IsNotNullOrEmpty(), a => a.NODE_ID == orgParm.smbl_lab_id)
                //.WhereIF(smblDeptNodeId.IsNotNullOrEmpty(), year => year.NODE_ID == smblDeptNodeId)
                //.WhereIF(deptKeyWord.IsNotNullOrEmpty(), year => deptKeyWord.Contains(year.NODE_NAME))
                .OrderBy(a => a.NODE_SORT)
                .ToList();

            var hospitalNode = new OrgTreeNode
            {
                NODE_TYPE = PMSTreeNodeTypeEnum.SMBL_NODE.ToIntStr(),
                NODE_TYPE_NAME = PMSTreeNodeTypeEnum.SMBL_NODE.ToDesc(),
                SOURCE_ID = hospital.HOSPITAL_ID,
                NAME = hospital.HOSPITAL_NAME,
            };
            tree.ChildAdd(hospitalNode);

            var headNode = smblNodes.Find(a => a.PARENT_NODE_ID == "0");
            if (headNode == null)
                return tree;

            const string HOSPITAL_MASTER = "医疗机构法人";

            if (headNode.IF_DISPLAY == 1)
            {
                var headTreeNode = new OrgTreeNode
                {
                    NODE_TYPE = PMSTreeNodeTypeEnum.SMBL_NODE.ToIntStr(),
                    NODE_TYPE_NAME = PMSTreeNodeTypeEnum.SMBL_NODE.ToDesc(),
                    SOURCE_ID = headNode.NODE_ID,
                    NAME = headNode.NODE_NAME.IsNotNullOrEmpty() ? headNode.NODE_NAME : HOSPITAL_MASTER,
                    SORT = headNode.NODE_SORT.ToString().PadLeft(10, '0'),
                };
                hospitalNode.ChildAdd(headTreeNode);
                AppendChildrenNode(headNode, headTreeNode);
            }
            else
            {
                var headNodes = smblNodes.FindAll(a => a.PARENT_NODE_ID == headNode.NODE_ID);
                foreach (var hNode in headNodes)
                {
                    var headTreeNode = new OrgTreeNode
                    {
                        NODE_TYPE = PMSTreeNodeTypeEnum.SMBL_NODE.ToIntStr(),
                        NODE_TYPE_NAME = PMSTreeNodeTypeEnum.SMBL_NODE.ToDesc(),
                        SOURCE_ID = hNode.NODE_ID,
                        NAME = hNode.NODE_NAME,
                        SORT = hNode.NODE_SORT.ToString().PadLeft(10, '0'),
                    };
                    hospitalNode.ChildAdd(headTreeNode);
                    AppendChildrenNode(hNode, headTreeNode);
                }
            }
            void AppendChildrenNode(SMBL_DEPT_NODE node, OrgTreeNode treeNode)
            {
                var childNodes = smblNodes.FindAll(a => a.PARENT_NODE_ID == node.NODE_ID);
                foreach (var childNode in childNodes)
                {
                    var childTreeNode = new OrgTreeNode
                    {
                        NODE_TYPE = PMSTreeNodeTypeEnum.SMBL_NODE.ToIntStr(),
                        NODE_TYPE_NAME = PMSTreeNodeTypeEnum.SMBL_NODE.ToDesc(),
                        SOURCE_ID = childNode.NODE_ID,
                        NAME = childNode.NODE_NAME,
                        SORT = childNode.NODE_SORT.ToString().PadLeft(10, '0'),
                    };
                    treeNode.ChildAdd(childTreeNode);
                    AppendChildrenNode(childNode, childTreeNode);
                }
            }

            //自动更新NUM计数、SOURCE_PATH、NODE_NO
            tree.RefreshTree(a => true, true);
            return tree;
        }

        public OrgTree GetSmblDeptPostPersonTree(string hospitalId, string smblDeptId, string smblPostId, string postKeyword, string personKeyword)
        {
            var tree = new OrgTree();
            var postNodeList = new List<OrgTreeNode>();
            var org = _soa.Db.Queryable<SMBL_ORG>().Where(a => a.HOSPITAL_ID == hospitalId && a.ORG_STATE == "2").First() ?? new SMBL_ORG();
            var posts = _soa.Db.Queryable<SMBL_DEPT_POST>()
                 .Where(d => d.HOSPITAL_ID == hospitalId && d.DEPT_POST_STATE == "1")
                 .WhereIF(org.ORG_ID.IsNotNullOrEmpty(), d => d.ORG_ID == org.ORG_ID)
                 .WhereIF(smblDeptId.IsNotNullOrEmpty(), d => d.SMBL_DEPT_ID == smblDeptId || d.SMBL_LAB_ID == smblDeptId || d.LAB_ID == smblDeptId)
                 .WhereIF(smblPostId.IsNotNullOrEmpty(), d => d.DEPT_POST_ID == smblPostId)
                 .WhereIF(postKeyword.IsNotNullOrEmpty(), d => postKeyword.Contains(d.DEPT_POST_NAME))
                 .ToList();

            //加上岗位节点
            foreach (var post in posts)
            {
                var postNode = new OrgTreeNode
                {
                    NODE_TYPE = PMSTreeNodeTypeEnum.SMBL_POST.ToIntStr(),
                    NODE_TYPE_NAME = PMSTreeNodeTypeEnum.SMBL_POST.ToDesc(),
                    SOURCE_ID = post.DEPT_POST_ID,
                    NAME = post.DEPT_POST_NAME,
                    SORT = post.FIRST_RTIME?.ToString("yyyyMMddhh"),
                };
                tree.ChildAdd(postNode);
                postNodeList.Add(postNode);
            }

            List<string> postIds = postNodeList.Select(a => a.SOURCE_ID).Distinct().ToList();

            var persons = _soa.Db.Queryable<PMS_PERSON_INFO>()
                .InnerJoin<SMBL_POST_PERSON>((p, s) => p.PERSON_ID == s.PERSON_ID)
                .Where((p, s) => p.PERSON_STATE == "1" && s.POST_PSTATE == "1")
                .Select((p, s) => new SimplePersonInfo
                {
                    POST_ID = s.DEPT_POST_ID,
                    PERSON_ID = p.PERSON_ID.SelectAll()
                })
                .ToList();

            foreach (var postNode in postNodeList)
            {
                foreach (var person in persons.Where(a => a.POST_ID == postNode.SOURCE_ID))
                {
                    var personNode = new OrgTreeNode
                    {
                        NODE_TYPE = PMSTreeNodeTypeEnum.PERSON.ToIntStr(),
                        NODE_TYPE_NAME = PMSTreeNodeTypeEnum.PERSON.ToDesc(),
                        SOURCE_ID = person.PERSON_ID,
                        SOURCE = person,
                        NAME = person.USER_NAME,
                        SORT = person.FIRST_RTIME,
                    };
                    postNode.ChildAdd(personNode);
                }
            }

            //自动更新NUM计数、SOURCE_PATH、NODE_NO
            tree.RefreshTree(a => a.NODE_TYPE == PMSTreeNodeTypeEnum.PERSON.ToIntStr(), true);
            ////清除空节点
            //postTree.AllNodesRemove(year => year.NUM == 0);
            return tree;
        }

        public OrgTree GetSmblPostTree(OrgUserParams orgParm, string permissMenuId)
        {
            var hospitalId = orgParm.hospital_id ?? throw new BizException("机构ID不可为空！");
            var tree = new OrgTree();
            var allSmbleNodeList = new List<OrgTreeNode>();
            var org = _soa.Db.Queryable<SMBL_ORG>().Where(a => a.HOSPITAL_ID == hospitalId && a.ORG_STATE == "2").First() ?? new SMBL_ORG();
            var hospital = _soa.Db.Queryable<SMBL_HOSPITAL>().Where(a => a.HOSPITAL_ID == hospitalId && a.REGISTER_STATE == "1").First();//已注册
            var smblNodes = _soa.Db.Queryable<SMBL_DEPT_NODE>()
                .Where(a => a.HOSPITAL_ID == hospitalId && a.CURR_NODE_STATE == "1")
                 .WhereIF(org.ORG_ID.IsNotNullOrEmpty(), a => a.ORG_ID == org.ORG_ID)
                 .WhereIF(orgParm.smbl_lab_id.IsNotNullOrEmpty(), a => a.NODE_ID == orgParm.smbl_lab_id)
                .OrderBy(a => a.NODE_SORT)
                .ToList();

            var posts = _soa.Db.Queryable<SMBL_DEPT_POST>()
                 .Where(d => d.HOSPITAL_ID == hospitalId && d.DEPT_POST_STATE == "1")
                 .WhereIF(org.ORG_ID.IsNotNullOrEmpty(), d => d.ORG_ID == org.ORG_ID)
                 .WhereIF(orgParm.lab_id.IsNotNullOrEmpty(), d => d.LAB_ID == orgParm.lab_id)
                 .WhereIF(orgParm.smbl_lab_id.IsNotNullOrEmpty(), d => d.SMBL_LAB_ID == orgParm.smbl_lab_id)
                 .ToList();

            var headNode = smblNodes.Find(a => a.PARENT_NODE_ID == "0");
            if (headNode == null)
                return tree;

            if (headNode.NODE_NAME.IsNotNullOrEmpty())
            {
                var headTreeNode = new OrgTreeNode
                {
                    NODE_TYPE = PMSTreeNodeTypeEnum.SMBL_NODE.ToIntStr(),
                    NODE_TYPE_NAME = PMSTreeNodeTypeEnum.SMBL_NODE.ToDesc(),
                    SOURCE_ID = headNode.NODE_ID,
                    NAME = headNode.NODE_NAME,
                    SORT = headNode.NODE_SORT.ToString().PadLeft(10, '0'),
                };
                tree.ChildAdd(headTreeNode);
                AppendChildrenNode(headNode, headTreeNode);
                allSmbleNodeList.Add(headTreeNode);
            }
            else
            {
                var headNodes = smblNodes.FindAll(a => a.PARENT_NODE_ID == headNode.NODE_ID);
                foreach (var hNode in headNodes)
                {
                    var headTreeNode = new OrgTreeNode
                    {
                        NODE_TYPE = PMSTreeNodeTypeEnum.SMBL_NODE.ToIntStr(),
                        NODE_TYPE_NAME = PMSTreeNodeTypeEnum.SMBL_NODE.ToDesc(),
                        SOURCE_ID = hNode.NODE_ID,
                        NAME = hNode.NODE_NAME,
                        SORT = hNode.NODE_SORT.ToString().PadLeft(10, '0'),
                    };
                    tree.ChildAdd(headTreeNode);
                    AppendChildrenNode(hNode, headTreeNode);
                    allSmbleNodeList.Add(headTreeNode);
                }
            }
            void AppendChildrenNode(SMBL_DEPT_NODE node, OrgTreeNode treeNode)
            {
                var childNodes = smblNodes.FindAll(a => a.PARENT_NODE_ID == node.NODE_ID);
                foreach (var childNode in childNodes)
                {
                    var childTreeNode = new OrgTreeNode
                    {
                        NODE_TYPE = PMSTreeNodeTypeEnum.SMBL_NODE.ToIntStr(),
                        NODE_TYPE_NAME = PMSTreeNodeTypeEnum.SMBL_NODE.ToDesc(),
                        SOURCE_ID = childNode.NODE_ID,
                        NAME = childNode.NODE_NAME,
                        SORT = childNode.NODE_SORT.ToString().PadLeft(10, '0'),
                    };
                    treeNode.ChildAdd(childTreeNode);
                    AppendChildrenNode(childNode, childTreeNode);
                    allSmbleNodeList.Add(childTreeNode);
                }
            }

            //加上岗位节点
            foreach (var post in posts)
            {
                OrgTreeNode parentNode = null;
                if (post.SMBL_LAB_ID.IsNotNullOrEmpty())
                {
                    parentNode = allSmbleNodeList.Find(a => a.SOURCE_ID == post.SMBL_LAB_ID);
                }
                if (parentNode == null && post.SMBL_DEPT_ID.IsNotNullOrEmpty())
                {
                    parentNode = allSmbleNodeList.Find(a => a.SOURCE_ID == post.SMBL_DEPT_ID);
                }
                if (parentNode != null)
                {
                    var postNode = new OrgTreeNode
                    {
                        NODE_TYPE = PMSTreeNodeTypeEnum.SMBL_POST.ToIntStr(),
                        NODE_TYPE_NAME = PMSTreeNodeTypeEnum.SMBL_POST.ToDesc(),
                        SOURCE_ID = post.DEPT_POST_ID,
                        NAME = post.DEPT_POST_NAME,
                        SORT = post.FIRST_RTIME?.ToString("yyyyMMddhh"),
                    };
                    parentNode.ChildAdd(postNode);
                }

            }
            //自动更新NUM计数、SOURCE_PATH、NODE_NO
            tree.RefreshTree(a => true, true);
            return tree;
        }

        public OrgTree GetSmblLabTree(OrgUserParams orgParm, string permissMenuId)
        {
            var hospitalId = orgParm.hospital_id ?? throw new BizException("机构ID不可为空！");
            OrgTree tree = _IOrganizationTreeService.GetOrgTreeType_Smbl_A(_soa, orgParm, permissMenuId, true);
            //自动更新NUM计数、SOURCE_PATH、NODE_NO
            tree.RefreshTree(a => a.NODE_TYPE == GroupTreeNodeTypeEnum.SMBL_LAB.ToIntStr(), true);
            //清除空节点
            tree.AllNodesRemove(a => a.NUM == 0);
            return tree;
        }


        /// <summary>
        /// 获取院区列表
        /// </summary>
        /// <param name="labId"></param>
        /// <returns></returns>
        public List<SYS6_INSPECTION_AREA> GetAreaList(string hospitalId, string labId)
        {
            if (hospitalId.IsNullOrEmpty())
            {
                if (labId.IsNotNullOrEmpty())
                    hospitalId = _soa.Db.Queryable<SYS6_INSPECTION_LAB>()
                        .Where(a => a.LAB_ID == labId)
                        .Select(a => a.HOSPITAL_ID)
                        .First();
                if (hospitalId.IsNullOrEmpty())
                    hospitalId = _httpContext.GetHospitalId();
                if (hospitalId.IsNullOrEmpty())
                    hospitalId = _httpContext.HttpContext.User.ToClaimsDto().HOSPITAL_ID;
            }
            List<SYS6_INSPECTION_AREA> areaList = _soa.Db.Queryable<SYS6_INSPECTION_AREA>()
                                   .Where(a => a.STATE_FLAG == "1" && a.HOSPITAL_ID == hospitalId)
                                   .OrderBy(a => a.AREA_SORT)
                                   .ToList();

            return areaList;
        }
        /// <summary>
        /// 获取人员列表
        /// </summary>
        /// <param name="pgroupId">专业组ID</param>
        /// <param name="hospitalId">机构ID</param>
        /// <returns></returns>
        public List<PersonInfoDto> GetPersonList(string pgroupId, string hospitalId)
        {
            var list = new List<PersonInfoDto>();
            var resPerson = _soa.Db.Queryable<PMS_PERSON_INFO>()
                .Where(w => w.PGROUP_ID == pgroupId && w.HOSPITAL_ID == hospitalId)
                .ToList();
            foreach (var item in resPerson)
            {
                if (item.RECORD_DATA.IsNotNullOrEmpty())
                {
                    IDictionary<string, object> dataDic = DeserializeObject(item.RECORD_DATA);
                    if (dataDic != null && dataDic.ContainsKey("TechList"))
                    {
                        //取最新的职称评定
                        TechList techList = JsonConvert.DeserializeObject<List<TechList>>(dataDic["TechList"]?.ToString())?.OrderByDescending(w => w.TECH_CERTIFICE_TIME)?.FirstOrDefault();
                        if (techList != null && techList.TECH_CERTIFICE_TIME > item.TECH_CERTIFICE_TIME)
                        {
                            item.TECH_POST = techList.TECH_POST;
                            item.ACADEMIC_POST = techList.ACADEMIC_POST;
                            item.EMPLOYMENT_UNIT = techList.EMPLOYMENT_UNIT;
                            item.TECHNOLOGY_TYPE = techList.TECHNOLOGY_TYPE;
                            item.TECH_POST_PROFESSION = techList.TECH_POST_PROFESSION;
                            item.TECH_CERTIFICE_TIME = techList.TECH_CERTIFICE_TIME;
                        }
                    }
                }
            }
            list = CommGetPersonListInfo(resPerson);
            return list;
        }
        /// <summary>
        /// 加载人员档案execl数据
        /// </summary>
        /// <param name="pgroupId">专业组id</param>
        ///  <param name="mgroupId">管理专业id</param>
        ///  <param name="personId">人员id</param>
        /// <param name="personName">姓名</param>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <param name="styleId">模板id</param>
        /// <param name="hospitalId">机构id</param>
        /// <returns></returns>
        public ResultDto LoadPersonExcelData(LoadOfficeDto loadOfficeDto, string hospitalId)
        {
            ResultDto result = new ResultDto();
            try
            {
                H.Utility.ClaimsDto claim = _httpContext.HttpContext.User.ToClaimsDto();
                var sys_class_info = _IModuleLabGroupService.GetPmsAddnClassInfo().Select(s => new PMS_ADDN_CLASS_INFO
                {
                    CLASS_ID = s.CLASS_ID,
                    CLASS_TYPE = s.CLASS_TYPE,
                    FORM_SETUP_ID = s.FORM_SETUP_ID,
                    TABLE_SETUP_ID = s.TABLE_SETUP_ID,
                    CLASS_NAME = s.CLASS_NAME,
                    CLASS_SORT = s.CLASS_SORT
                }).ToList();
                List<string> listPgroupId = new List<string>();
                if (loadOfficeDto.pgroupId.IsNotNullOrEmpty())
                    listPgroupId.Add(loadOfficeDto.pgroupId);
                if (loadOfficeDto.pgroupIds.IsNotNullOrEmpty())
                    listPgroupId = loadOfficeDto.pgroupIds.Split(',').ToList();
                if (loadOfficeDto.mgroupId.IsNotNullOrEmpty())
                {
                    List<SYS6_INSPECTION_PGROUP> listPgroup = _soa.Db.Queryable<SYS6_INSPECTION_PGROUP>()
                    .Where(pg => pg.PGROUP_STATE == "1" && pg.PGROUP_CLASS != "3" && pg.MGROUP_ID == loadOfficeDto.mgroupId)
                    .ToList();
                    listPgroupId = listPgroup.Select(w => w.PGROUP_ID).ToList();
                }
                else if (loadOfficeDto.labId.IsNotNullOrEmpty())
                {
                    List<SYS6_INSPECTION_PGROUP> listPgroup = _soa.Db.Queryable<SYS6_INSPECTION_PGROUP>()
                  .Where(pg => pg.PGROUP_STATE == "1" && pg.PGROUP_CLASS != "3" && pg.LAB_ID == loadOfficeDto.labId)
                  .ToList();
                    listPgroupId = listPgroup.Select(w => w.PGROUP_ID).ToList();
                }
                else if (loadOfficeDto.areaId.IsNotNullOrEmpty())
                {
                    List<SYS6_INSPECTION_PGROUP> listPgroup = _soa.Db.Queryable<SYS6_INSPECTION_PGROUP>()
                  .Where(pg => pg.PGROUP_STATE == "1" && pg.PGROUP_CLASS != "3" && pg.AREA_ID == loadOfficeDto.areaId)
                  .ToList();
                    listPgroupId = listPgroup.Select(w => w.PGROUP_ID).ToList();
                }
                List<PMS_PERSON_INFO> listFilterPerson = new List<PMS_PERSON_INFO>();
                if (listPgroupId.Count > 0)
                {
                   OrgTree areaGroupTree;
                    List<SYS6_USER> userList;
                    var orgParm = new OrgUserParams { area_id = loadOfficeDto.areaId, hospital_id = hospitalId, lab_id = loadOfficeDto.labId, pgroup_id = loadOfficeDto.pgroupId, user_id = claim.USER_NO };
                    List<PMS_PERSON_INFO> resPerson = _IModuleLabGroupService.GetRangePersonList(orgParm, loadOfficeDto.personName, "H8107", out userList, out areaGroupTree);

                    foreach (SYS6_USER user in userList.Where(a => listPgroupId.Contains(a.DEPT_CODE)).OrderBy(a => a.USERNAME))
                    {
                        PMS_PERSON_INFO person = SyncPersonInfo(user, resPerson);
                        if (person != null)
                            listFilterPerson.Add(person);
                    }
                }
                else if (loadOfficeDto.personId.IsNotNullOrEmpty())
                {
                    List<string> listPersonId = loadOfficeDto.personId.Split(',').ToList();
                    listFilterPerson = _soa.Db.Queryable<PMS_PERSON_INFO>().Where(w => listPersonId.Contains(w.PERSON_ID)).ToList();
                }
                if (loadOfficeDto.dateType.IsNotNullOrEmpty() && loadOfficeDto.startDate.IsNotNullOrEmpty() && loadOfficeDto.endDate.IsNotNullOrEmpty())
                {
                    //listFilterPerson = listFilterPerson.Where(w => w.IN_LAB_TIME.IsNotNullOrEmpty() &&
                    //Convert.ToDateTime(w.IN_LAB_TIME) > Convert.ToDateTime(loadOfficeDto.startDate) &&
                    //Convert.ToDateTime(w.IN_LAB_TIME) < Convert.ToDateTime(loadOfficeDto.endDate)).ToList();

                    DateTime startDateTime = DateTime.Parse(loadOfficeDto.startDate);
                    //时间只有月份 查单月的会有问题 故而增加一个月
                    DateTime endDateTime = DateTime.Parse(loadOfficeDto.endDate).AddMonths(1);

                    // 获取属性信息
                    PropertyInfo propertyInfo = typeof(PMS_PERSON_INFO).GetProperty(loadOfficeDto.dateType);

                    // 检查属性是否存在
                    if (propertyInfo == null)
                    {
                        throw new ArgumentException($"Property {loadOfficeDto.dateType} not found on type {typeof(PMS_PERSON_INFO).Name}");
                    }

                    listFilterPerson = listFilterPerson.AsQueryable()
                                          .Where(item => Convert.ToDateTime(propertyInfo.GetValue(item)) >= startDateTime &&
                                                         Convert.ToDateTime(propertyInfo.GetValue(item)) < endDateTime)
                                          .ToList();
                }
                if (listPgroupId.Count > 0)
                    listFilterPerson = listFilterPerson.Where(w => listPgroupId.Contains(w.PGROUP_ID)).ToList();
                if (loadOfficeDto.personName.IsNotNullOrEmpty())
                    listFilterPerson = listFilterPerson.Where(w => w.USER_NAME.Contains(loadOfficeDto.personName)).ToList();
                OaExcelFillDataDto oaExcelFillDataDto = new OaExcelFillDataDto();
                oaExcelFillDataDto.STYLE_ID = loadOfficeDto.styleId;
                oaExcelFillDataDto.ALL_CLASS_DATAS = new List<AllClassDataDto>();
                oaExcelFillDataDto.OPER_UUID = IDGenHelper.CreateGuid();
                oaExcelFillDataDto.KEY_COLUMN = "PERSON_ID";
                List<OA_FIELD_DICT> varFileData = _soa.Db.Queryable<OA_FIELD_DICT>().Where(w => w.MODULE_ID == "H81").ToList();
                List<string> personIds = listFilterPerson.Select(w => w.PERSON_ID).ToList();
                List<string> userIds = listFilterPerson.Select(w => w.USER_ID).ToList();
                var sys6InspectionPgroup = _IBaseDataServices.GetInspectionPgroup();
                List<SYS6_INSPECTION_LAB> listLab = _soa.Db.Queryable<SYS6_INSPECTION_LAB>().Where(w => w.STATE_FLAG == "1").ToList();
                List<SYS6_INSPECTION_AREA> listArea = _soa.Db.Queryable<SYS6_INSPECTION_AREA>().Where(w => w.STATE_FLAG == "1").ToList();
                List<SYS6_BASE_DATA> lis5_base_data = _IBaseDataServices.GetSys6BaseData();
                ConcurrentBag<DataTable> dataTables = new ConcurrentBag<DataTable>();
                List<UserPostDto> listPost = _soa.Db.Queryable<SYS6_USER>()
                    .InnerJoin<SYS6_USER_POST>((a, b) => a.USER_NO == b.USER_NO)
                    .LeftJoin<SYS6_POST>((a, b, c) => b.POST_ID == c.POST_ID)
                    .Where((a, b, c) => userIds.Contains(a.USER_NO))
                    .Select((a, b, c) => new UserPostDto() { USER_NO = a.USER_NO, POST_NAME = c.POST_NAME, PGROUP_ID = c.PGROUP_ID, LAB_ID = c.LAB_ID, FIRST_RTIME = b.FIRST_RTIME, POST_CLASS = c.POST_CLASS }).ToList();
                List<string> listClass = new List<string>();
                string setupJson = _soa.Db.Queryable<OA_EXCEL_STYLE_TEMPLATE>().Where(w => w.STYLE_ID == loadOfficeDto.styleId).Select(w => w.ITEM_SETUP_JSON).First();
                if (setupJson.IsNotNullOrEmpty())
                {
                    JObject obj = JObject.Parse(setupJson);
                    foreach (var x in obj)
                    {
                        listClass.Add(x.Key);
                    }
                }
                if (listClass.Count > 0)
                    sys_class_info = sys_class_info.FindAll(w => listClass.Contains(w.CLASS_ID));
                List<string> listSetUpId = sys_class_info.Select(w => w.TABLE_SETUP_ID).Distinct().ToList();
                List<SYS6_MODULE_FUNC_DICT> listFuncDict = _soa.Db.Queryable<SYS6_MODULE_FUNC_DICT>().Where(p => listSetUpId.Contains(p.SETUP_ID) && p.HOSPITAL_ID == hospitalId)
.Select(s => new SYS6_MODULE_FUNC_DICT()
{
    SETUP_ID = s.SETUP_ID,
    FORM_JSON = s.FORM_JSON,
    FORM_COL_JSON = s.FORM_COL_JSON

}).ToList();

                List<PMS_ADDN_RECORD> listRecordList = _soa.Db.Queryable<PMS_ADDN_RECORD>().Where(s => personIds.Contains(s.PERSON_ID) && s.CHECK_STATE == "2").OrderBy(item => item.RECORD_ID).ToList();
                Parallel.ForEach(sys_class_info, classInfo =>
                {
                    AutoClassProp AutoClassProp = new AutoClassProp();
                    (DataTable dataTable, AutoClassProp) = CreateClassInfoTableByPersonIds(classInfo, listFilterPerson, listFuncDict, listRecordList, null, hospitalId, true);
                    dataTables.Add(dataTable);
                });
                ConcurrentBag<AllClassDataDto> classDataBag = new ConcurrentBag<AllClassDataDto>();

                Parallel.ForEach(listFilterPerson, item =>
                {
                    PersonInfoDto personInfoDto = CommGetPersonInfo(new List<PMS_PERSON_INFO> { item }, sys6InspectionPgroup, lis5_base_data);
                    personInfoDto.USER_TYPE = personInfoDto.USER_TYPE_NAME;
                    SYS6_INSPECTION_LAB lab = listLab.Find(w => w.LAB_ID == personInfoDto.LAB_ID);
                    SYS6_INSPECTION_AREA area = listArea.Find(w => w.AREA_ID == personInfoDto.AREA_ID);
                    personInfoDto.LAB_NAME = lab?.LAB_NAME;
                    personInfoDto.AREA_NAME = area?.AREA_NAME;
                    personInfoDto.BIRTHDAY_YEAR = personInfoDto.BIRTHDAY.IsNotNullOrEmpty() ? Convert.ToDateTime(personInfoDto.BIRTHDAY).ToString("yyyy") : "";
                    string postName = "";
                    string labPostName = "";
                    string adminPostName = "";
                    string labPostRemark = "";
                    List<UserPostDto> userPost = listPost.FindAll(w => w.USER_NO == item.USER_ID && w.PGROUP_ID == item.PGROUP_ID).OrderBy(w => w.FIRST_RTIME).ToList();
                    //科室岗位
                    List<UserPostDto> userLabPost = listPost.FindAll(w => w.USER_NO == item.USER_ID && w.PGROUP_ID == "PG000" && w.LAB_ID == personInfoDto.LAB_ID).OrderBy(w => w.FIRST_RTIME).ToList();
                    foreach (var post in userPost)
                    {
                        postName += $"/{post.POST_NAME}";
                    }
                    Dictionary<string, string> dicPost = new Dictionary<string, string>()
                    {
                        ["质量负责人"] = "①",
                        ["技术负责人"] = "②",
                        ["内审员"] = "③",
                        ["监督员"] = "④",
                        ["设备管理员"] = "⑤",
                        ["给出意见和解释人员"] = "⑥",
                        ["安全员"] = "⑦"
                    };
                    foreach (var post in userLabPost)
                    {
                        labPostName += $"/{post.POST_NAME}";
                        if (dicPost.ContainsKey(post.POST_NAME))
                            labPostRemark += dicPost[post.POST_NAME];
                    }
                    List<UserPostDto> listAdminPost = new List<UserPostDto>();
                    listAdminPost.AddRange(userPost);
                    //  listAdminPost.AddRange(userLabPost);
                    listAdminPost = listAdminPost.FindAll(w => w.POST_CLASS == "1");
                    foreach (var post in listAdminPost)
                    {
                        adminPostName += $"/{post.POST_NAME}";
                    }
                    DateTime dtLabTime = DateTime.Now;
                   if (userPost != null && userPost.Count > 0 && userPost[0].FIRST_RTIME != null)
                    {
                        int mounth = (DateTime.Now.Year - userPost[0].FIRST_RTIME.Value.Year) * 12 + (DateTime.Now.Month - userPost[0].FIRST_RTIME.Value.Month);
                        personInfoDto.POST_YRAR = mounth > 12 ? (mounth / 12).ToString() : mounth + "月";
                    }
                    if (personInfoDto.IN_LAB_TIME.IsNotNullOrEmpty() &&  DateTime.TryParse(personInfoDto.IN_LAB_TIME, out dtLabTime))
                    {
                        int mounth = (DateTime.Now.Year - dtLabTime.Year) * 12 + (DateTime.Now.Month - dtLabTime.Month);
                        //   personInfoDto.WORK_YEAR = mounth > 12 ? (mounth / 12).ToString() : mounth + "月";
                        personInfoDto.IN_LAB_TIME = $"{dtLabTime.Year}.{dtLabTime.Month}";
                    }
                    DateTime workTime = DateTime.Now;
                    if (personInfoDto.WORK_TIME.IsNotNullOrEmpty() && DateTime.TryParse(personInfoDto.IN_LAB_TIME, out workTime))
                    {
                        int mounth = (DateTime.Now.Year - workTime.Year) * 12 + (DateTime.Now.Month - workTime.Month);
                        personInfoDto.WORK_YEAR = mounth > 12 ? (mounth / 12).ToString() : mounth + "月";
                        //  personInfoDto.IN_LAB_TIME = $"{dtLabTime.Year}.{dtLabTime.Month}";
                    }
                    personInfoDto.WORK_TYPE = "全职";
                    personInfoDto.DEPARTORPOST = $"{personInfoDto.PGROUP_NAME}{postName}";
                    personInfoDto.DEPARTORLABPOST = $"{personInfoDto.PGROUP_NAME}{labPostName}";
                    personInfoDto.DEPARTORADMINPOST = $"{personInfoDto.PGROUP_NAME}{adminPostName}";
                    personInfoDto.LABPOST = labPostName.IsNotNullOrEmpty() ? labPostName.Remove(0, 1) : "";
                    personInfoDto.PGROUPPOST = postName.IsNotNullOrEmpty() ? postName.Remove(0, 1) : "";
                    ;
                    personInfoDto.DEGREE_NAMEOR_DIPLOMA_NAME = $"{personInfoDto.HIGHEST_DEGREE_NAME}/{personInfoDto.HIGHEST_DIPLOMA_NAME}";
                    personInfoDto.LABPOSTREMARK = labPostRemark;
                    string techName = personInfoDto.ACADEMIC_POST_NAME;
                    if (personInfoDto.RECORD_DATA.IsNotNullOrEmpty())
                    {
                        IDictionary<string, object> dataDic = DeserializeObject(item.RECORD_DATA);

                        if (dataDic != null && dataDic.ContainsKey("TechList") && dataDic["TechList"] != null)
                        {
                            //取最新的职称评定
                            List<TechList> techList = JsonConvert.DeserializeObject<List<TechList>>(dataDic["TechList"].ToString());
                            foreach (var tech in techList)
                            {
                                string ACADEMIC_POST_NAME = tech.TECHNOLOGY_TYPE == "1" ? RecordClassBaseName("技师职称", tech.ACADEMIC_POST)
                          : tech.TECHNOLOGY_TYPE == "2" ? RecordClassBaseName("医师职称", tech.ACADEMIC_POST)
                          : tech.TECHNOLOGY_TYPE == "3" ? RecordClassBaseName("护士职称", tech.ACADEMIC_POST)
                          : tech.TECHNOLOGY_TYPE == "4" ? RecordClassBaseName("研究员职称", tech.ACADEMIC_POST)
                          : string.Empty;
                                if (ACADEMIC_POST_NAME.IsNotNullOrEmpty())
                                    techName += "/" + ACADEMIC_POST_NAME;
                            }
                        }
                    }
                    personInfoDto.DUTIES_NAMEORACADEMIC_POST_NAME = $"{personInfoDto.DUTIES_NAME}/{techName}";
                    AllClassDataDto allClassDataDto = FillPersonInfo(personInfoDto, varFileData);
                    foreach (var classInfo in sys_class_info)
                    {
                        DataTable dataTable = dataTables.Where(w => w.TableName == classInfo.CLASS_ID)?.First();
                        if (dataTable != null && dataTable.Rows.Count > 0)
                        {
                            DataRow[] drData = dataTable.Select($"PERSON_ID='{item.PERSON_ID}'");
                            FillExeclData(allClassDataDto, item.PERSON_ID, drData, dataTable, varFileData);
                        }
                    }
                    classDataBag.Add(allClassDataDto);
                    // oaExcelFillDataDto.ALL_CLASS_DATAS.Add(allClassDataDto);
                });

                oaExcelFillDataDto.ALL_CLASS_DATAS.AddRange(classDataBag);

                result = _IBaseDataServices.LoadExcelData(oaExcelFillDataDto);
            }
            catch (Exception ex)
            {
                _logger.LogError("LoadPersonExcelData报错:" + ex);
            }
            return result;
        }

        /// <summary>
        /// 根据时间类型和范围过滤
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="data"></param>
        /// <param name="fieldName"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        public IEnumerable<T> FilterData<T>(IEnumerable<T> data, string fieldName, string startTime, string endTime) where T : class
        {
            DateTime startDateTime = DateTime.Parse(startTime);
            //时间只有月份 查单月的会有问题 故而增加一个月
            DateTime endDateTime = DateTime.Parse(endTime).AddMonths(1);

            // 获取属性信息
            PropertyInfo propertyInfo = typeof(T).GetProperty(fieldName);

            // 检查属性是否存在
            if (propertyInfo == null)
            {
                throw new ArgumentException($"Property {fieldName} not found on type {typeof(T).Name}");
            }

            var filterdData = data.AsQueryable()
                                  .Where(item => Convert.ToDateTime(propertyInfo.GetValue(item)) >= startDateTime &&
                                                 Convert.ToDateTime(propertyInfo.GetValue(item)) < endDateTime)
                                  .ToList();

            return filterdData;
        }
        /// <summary>
        /// 填充人员各个分类数据
        /// </summary>
        /// <param name="allClassDataDto"></param>
        /// <param name="dtData"></param>
        /// <param name="varFileData"></param>
        private void FillExeclData(AllClassDataDto allClassDataDto, string personId, DataRow[] drData, DataTable dtData, List<OA_FIELD_DICT> varFileData)
        {
            var Data = varFileData.FindAll(w => w.CLASSE_CODE == dtData.TableName);
            var classCode = Data.Select(w => w.CLASSE_CODE).Distinct().ToList();
            OaExcelDataDto oaExcelDataDto = new OaExcelDataDto();
            oaExcelDataDto.CLASSE_CODE = dtData.TableName;
            if (drData != null && drData.Length > 0)
            {
                DataRow[] dataRows = new DataRow[] { drData[0] };
                if (dtData.TableName == "PMS_SKILL_CERTIFICATE_LIST")//技能证书传全部合集  其他传最新一项
                {
                    //DataView dv = drData.DefaultView;
                    //dv.Sort = "CERTIFICATE_DATE DESC";
                    //DataTable dt = dv.ToTable();
                    dataRows = drData;
                    oaExcelDataDto.GROUP_BY_KEY = "CERTIFICATE_TYPE";
                    oaExcelDataDto.HAS_DATA_DISPLAY = "√";
                    oaExcelDataDto.NO_HAS_DATA_DISPLAY = "×";
                }
                oaExcelDataDto.ARRAYS = new List<Dictionary<string, string>>();
                oaExcelDataDto.CLASSE_CODE = dtData.TableName;
                foreach (DataRow row in dataRows)
                {
                    Dictionary<string, string> dicRecord = new Dictionary<string, string>();
                    foreach (var filed in Data)
                    {
                        foreach (DataColumn item in dtData.Columns)
                        {
                            if (filed.FIELD_CODE == item.ColumnName)
                            {
                                dicRecord.Add(filed.FIELD_CODE, row[item.ColumnName].ToString());
                            }
                        }
                        if (!dicRecord.ContainsKey("PERSON_ID"))
                            dicRecord.Add("PERSON_ID", personId);
                    }
                    oaExcelDataDto.ARRAYS.Add(dicRecord);
                }
                allClassDataDto.CLASS_DATAS.Add(oaExcelDataDto);
            }
            //else
            //{
            //    oaExcelDataDto.ARRAYS = new List<Dictionary<string, string>>();
            //    Dictionary<string, string> dicRecord = new Dictionary<string, string>();
            //    if (!dicRecord.ContainsKey("PERSON_ID"))
            //        dicRecord.Add("PERSON_ID", personId);
            //    oaExcelDataDto.ARRAYS.Add(dicRecord);
            //    allClassDataDto.CLASS_DATAS.Add(oaExcelDataDto);
            //}
        }
        /// <summary>
        /// 生成execl人员基础数据
        /// </summary>
        /// <param name="personInfoDto"></param>
        /// <param name="varFileData"></param>
        /// <returns></returns>
        private AllClassDataDto FillPersonInfo(PersonInfoDto personInfoDto, List<OA_FIELD_DICT> varFileData)
        {
            AllClassDataDto classDataDto = new AllClassDataDto();
            classDataDto.CLASS_DATAS = new List<OaExcelDataDto>();
            Dictionary<string, string> dicData = new Dictionary<string, string>();
            var fileData = varFileData.FindAll(w => w.CLASSE_CODE == "数据项");
            IDictionary<string, object> personInfoDic = personInfoDto.AsDictionary();
            foreach (var item in fileData)
            {
                foreach (var dic in personInfoDic)
                {
                    if (item.FIELD_CODE == dic.Key)
                    {
                        if (!dicData.ContainsKey(item.FIELD_CODE))
                            dicData.Add(item.FIELD_CODE, dic.Value?.ToString());
                    }
                }
                if (!dicData.ContainsKey(item.FIELD_CODE))
                    dicData.Add(item.FIELD_CODE, "");
                if (!dicData.ContainsKey("PERSON_ID"))
                    dicData.Add("PERSON_ID", personInfoDto.PERSON_ID);
            }
            classDataDto.FIELDS = dicData;
            return classDataDto;
        }



        /// <summary>
        /// 预览模板文件
        /// </summary>
        /// <param name="styleId">模板id</param>
        /// <param name="formId">评价表id</param>
        /// <param name="verId">版本ID</param>
        /// <returns></returns>
        public MemoryStream PreviewPersonTemplate(string styleId, string personId, string hospitalId)
        {
            StyleTemplateFillDataDto styleTemplateFillData = CreateTemplateFillData(styleId, personId, hospitalId);
            byte[] buffer = _IBaseDataServices.PreviewFile(styleTemplateFillData);
            return new MemoryStream(buffer);
        }

        /// <summary>
        /// 预览模板文件
        /// </summary>
        /// <param name="styleId">模板id</param>
        /// <param name="formId">评价表id</param>
        /// <param name="verId">版本ID</param>
        /// <returns></returns>
        public MemoryStream ExportPersonTemplate(string styleId, string personId, string hospitalId)
        {
            StyleTemplateFillDataDto styleTemplateFillData = CreateTemplateFillData(styleId, personId, hospitalId);
            byte[] buffer = _IBaseDataServices.ExportStylePDFFile(styleTemplateFillData);
            return new MemoryStream(buffer);
        }
        /// <summary>
        /// 生成预览导出模板信息
        /// </summary>
        /// <param name="styleId"></param>
        /// <param name="personId"></param>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        private StyleTemplateFillDataDto CreateTemplateFillData(string styleId, string personId, string hospitalId)
        {
            var sys_class_info = _IModuleLabGroupService.GetPmsAddnClassInfo().Select(s => new PMS_ADDN_CLASS_INFO
            {
                CLASS_ID = s.CLASS_ID,
                CLASS_TYPE = s.CLASS_TYPE,
                FORM_SETUP_ID = s.FORM_SETUP_ID,
                TABLE_SETUP_ID = s.TABLE_SETUP_ID,
                CLASS_NAME = s.CLASS_NAME,
                CLASS_SORT = s.CLASS_SORT
            }).ToList();
            var resPerson = _soa.Db.Queryable<PMS_PERSON_INFO>()
             .Where(w => w.PERSON_ID == personId)
             .ToList().FirstOrDefault();
            if (resPerson == null)
                return null;

            List<OA_FIELD_DICT> varFileData = _soa.Db.Queryable<OA_FIELD_DICT>().Where(w => w.MODULE_ID == "H81").ToList();
            StyleTemplateFillDataDto styleTemplateFillData = new StyleTemplateFillDataDto();
            styleTemplateFillData.STYLE_ID = styleId;
            styleTemplateFillData.DATAS = new List<StyleTemplateClassDataDto>();
            var sys6InspectionPgroup = _IBaseDataServices.GetInspectionPgroup();
            List<SYS6_BASE_DATA> lis5_base_data = _IBaseDataServices.GetSys6BaseData();
            PersonInfoDto personInfoDto = CommGetPersonInfo(new List<PMS_PERSON_INFO> { resPerson }, sys6InspectionPgroup, lis5_base_data);
            personInfoDto.USER_TYPE = personInfoDto.USER_TYPE_NAME;
            StyleTemplateClassDataDto styleTemplateClassDataDto = FillPreviewPersonData(personInfoDto, varFileData);
            styleTemplateFillData.DATAS.Add(styleTemplateClassDataDto);
            List<string> listSetUpId = sys_class_info.Select(w => w.TABLE_SETUP_ID).Distinct().ToList();
            List<SYS6_MODULE_FUNC_DICT> listFuncDict = _soa.Db.Queryable<SYS6_MODULE_FUNC_DICT>().Where(p => listSetUpId.Contains(p.SETUP_ID) && p.HOSPITAL_ID == hospitalId)
.Select(s => new SYS6_MODULE_FUNC_DICT()
{
    SETUP_ID = s.SETUP_ID,
    FORM_JSON = s.FORM_JSON,
    FORM_COL_JSON = s.FORM_COL_JSON

}).ToList();
            List<string> listClass = new List<string>();
            string setupJson = _soa.Db.Queryable<OA_OFFICE_STYLE_TEMPLATE>().Where(w => w.STYLE_ID == styleId).Select(w => w.CLASS_COL_JSON).First();
            if (setupJson.IsNotNullOrEmpty())
            {
                JObject obj = JObject.Parse(setupJson);
                foreach (var x in obj)
                {
                    listClass.Add(x.Key);
                }
            }
            if (listClass.Count > 0)
                sys_class_info = sys_class_info.FindAll(w => listClass.Contains(w.CLASS_ID));
            List<PMS_ADDN_RECORD> listRecordList = _soa.Db.Queryable<PMS_ADDN_RECORD>().Where(s => resPerson.PERSON_ID == s.PERSON_ID && s.CHECK_STATE == "2").OrderBy(item => item.RECORD_ID).ToList();
            string archiveTables = string.Join(",", sys_class_info.Select(w => w.CLASS_ID));
            List<PMS_PERSON_FILE> listFile = GetRecordFileByPersonId(archiveTables, resPerson.PERSON_ID);
            Parallel.ForEach(sys_class_info, classInfo =>
            {
                StyleTemplateClassDataDto classData = new StyleTemplateClassDataDto();
                (DataTable dataTable, AutoClassProp AutoClassProp) = CreateClassInfoTableByPersonIds(classInfo, new List<PMS_PERSON_INFO> { resPerson }, listFuncDict, listRecordList, listFile, hospitalId, true);

                classData = FillPreviveClassData(dataTable, classInfo, varFileData);
                styleTemplateFillData.DATAS.Add(classData);
            });
            return styleTemplateFillData;
        }
        /// <summary>
        /// 填充人员预览模板数据
        /// </summary>
        /// <param name="personInfoDto"></param>
        /// <param name="varFileData"></param>
        /// <returns></returns>
        private StyleTemplateClassDataDto FillPreviewPersonData(PersonInfoDto personInfoDto, List<OA_FIELD_DICT> varFileData)
        {
            StyleTemplateClassDataDto styleTemplateClassDataDto = new StyleTemplateClassDataDto();
            styleTemplateClassDataDto.CLASSE_CODE = "数据项";
            Dictionary<string, string> dicData = new Dictionary<string, string>();
            var fileData = varFileData.FindAll(w => w.CLASSE_CODE == "数据项");
            IDictionary<string, object> personInfoDic = personInfoDto.AsDictionary();
            foreach (var item in fileData)
            {
                foreach (var dic in personInfoDic)
                {
                    if (item.FIELD_CODE == dic.Key)
                    {
                        if (!dicData.ContainsKey(item.FIELD_CODE))
                            dicData.Add(item.FIELD_CODE, dic.Value?.ToString());
                    }
                }
                if (!dicData.ContainsKey(item.FIELD_CODE))
                    dicData.Add(item.FIELD_CODE, "");
            }
            styleTemplateClassDataDto.FIELDS = dicData;
            return styleTemplateClassDataDto;
        }
        /// <summary>
        /// 填充人员各个分类数据
        /// </summary>
        /// <param name="allClassDataDto"></param>
        /// <param name="dtData"></param>
        /// <param name="varFileData"></param>
        private StyleTemplateClassDataDto FillPreviveClassData(DataTable dtData, PMS_ADDN_CLASS_INFO classInfo, List<OA_FIELD_DICT> varFileData)
        {
            StyleTemplateClassDataDto styleTemplateClassDataDto = new StyleTemplateClassDataDto();
            styleTemplateClassDataDto.CLASSE_CODE = dtData.TableName;
            var Data = varFileData.FindAll(w => w.CLASSE_CODE == dtData.TableName);
            var classCode = Data.Select(w => w.CLASSE_CODE).Distinct().ToList();
            if (dtData.Rows.Count > 0)
            {
                foreach (DataRow row in dtData.Rows)
                {
                    Dictionary<string, string> dicRecord = new Dictionary<string, string>();
                    foreach (var filed in Data)
                    {
                        foreach (DataColumn item in dtData.Columns)
                        {
                            if (filed.FIELD_CODE == item.ColumnName)
                            {
                                dicRecord.Add(filed.FIELD_CODE, row[item.ColumnName].ToString());
                            }
                        }
                    }
                    styleTemplateClassDataDto.ARRAYS.Add(dicRecord);
                }
            }
            return styleTemplateClassDataDto;
        }


        /// <summary>
        /// 根据人员id和分类id合集获取全部附件
        /// </summary>
        /// <param name="archiveTables">分类id合集</param>
        /// <param name="personId">人员id</param>
        /// <returns></returns>
        public List<PMS_PERSON_FILE> GetRecordFileByPersonId(string archiveTables, string personId)
        {
            List<PMS_PERSON_FILE> pmsPersonFile = _soa.Db.Queryable<PMS_PERSON_FILE>().Where(p => archiveTables.Contains(p.FILE_CLASS) && p.PERSON_ID == personId && p.FILE_STATE == "1").Select<PMS_PERSON_FILE>().ToList();
            List<string> listArchiveTables = archiveTables.Split(',').ToList();
            string smblFlag = _httpContext.GetSmblFlag();
            List<PMS_PERSON_FILE> listReturnFile = new List<PMS_PERSON_FILE>();
            foreach (var table in listArchiveTables)
            {
                switch (table)
                {
                    case "PMS_EDUCATION_LIST":
                        List<PMS_EDUCATION_LIST> pmsEducationList = _soa.Db.Queryable<PMS_EDUCATION_LIST>()
                .Where(s => s.PERSON_ID == personId)
                .WhereIF(smblFlag == "1", s => s.SMBL_FLAG == smblFlag)
                .OrderBy(item => item.EDUCATION_SORT)
                .ToList();
                        foreach (var item in pmsEducationList)
                        {
                            string resume_affix_name = string.Empty;
                            if (item.EDUCATION_AFFIX.IsNotNullOrEmpty())
                            {
                                string[] AFFIXARRY = item.EDUCATION_AFFIX.Split(",");
                                List<PMS_PERSON_FILE> pmsPersonFileReward = pmsPersonFile.Where(p => AFFIXARRY.Contains(p.FILE_ID)).ToList();
                                for (int i = 0; i < AFFIXARRY.Length; i++)
                                {
                                    var PersonFile = pmsPersonFileReward.FirstOrDefault(p => p.FILE_ID == AFFIXARRY[i]);
                                    if (PersonFile != null)
                                    {
                                        listReturnFile.Add(PersonFile);
                                    }
                                }
                            }
                        }
                        break;
                    case "PMS_PROFESSIONAL_LIST":
                        List<PMS_PROFESSIONAL_LIST> pmsProfessionalList = _soa.Db.Queryable<PMS_PROFESSIONAL_LIST>()
                .Where(s => s.PERSON_ID == personId)
                .WhereIF(smblFlag == "1", s => s.SMBL_FLAG == smblFlag)
                .ToList();
                        foreach (var item in pmsProfessionalList)
                        {
                            string resume_affix_name = string.Empty;
                            if (item.PROFESSIONAL_AFFIX.IsNotNullOrEmpty())
                            {
                                string[] AFFIXARRY = item.PROFESSIONAL_AFFIX.Split(",");
                                List<PMS_PERSON_FILE> pmsPersonFileReward = pmsPersonFile.Where(p => AFFIXARRY.Contains(p.FILE_ID)).ToList();
                                for (int i = 0; i < AFFIXARRY.Length; i++)
                                {
                                    var PersonFile = pmsPersonFileReward.FirstOrDefault(p => p.FILE_ID == AFFIXARRY[i]);
                                    if (PersonFile != null)
                                    {
                                        listReturnFile.Add(PersonFile);
                                    }
                                }
                            }
                        }
                        break;
                    case "PMS_SKILL_CERTIFICATE_LIST":
                        List<PMS_SKILL_CERTIFICATE_LIST> pmsSkillCert = _soa.Db.Queryable<PMS_SKILL_CERTIFICATE_LIST>()
                .Where(s => s.PERSON_ID == personId)
                .WhereIF(smblFlag == "1", s => s.SMBL_FLAG == smblFlag)
                .OrderBy(item => item.CERTIFICATE_SORT)
                .ToList();
                        foreach (var item in pmsSkillCert)
                        {
                            string resume_affix_name = string.Empty;
                            if (item.CERTIFICATE_AFFIX.IsNotNullOrEmpty())
                            {
                                string[] AFFIXARRY = item.CERTIFICATE_AFFIX.Split(",");
                                List<PMS_PERSON_FILE> pmsPersonFileReward = pmsPersonFile.Where(p => AFFIXARRY.Contains(p.FILE_ID)).ToList();
                                for (int i = 0; i < AFFIXARRY.Length; i++)
                                {
                                    var PersonFile = pmsPersonFileReward.FirstOrDefault(p => p.FILE_ID == AFFIXARRY[i]);
                                    if (PersonFile != null)
                                        listReturnFile.Add(PersonFile);
                                }
                            }
                        }
                        break;
                    default:
                        break;
                }

            }
            if (listReturnFile.Count == 0 && smblFlag != "1")
                listReturnFile = pmsPersonFile;
            foreach (var item in listReturnFile)
            {
                string previewSuffix = OfficeHelper.CastToPreviewSuffix(item.FILE_SUFFIX);
                string pathfile = (item.FILE_PATH + item.FILE_CNAME + previewSuffix).Replace(@"\", "/");
                item.HTTP_FILE_PATH = FileHttpUrl + pathfile;
            }
            return listReturnFile;
        }
        #region  公共方法
        /// <summary>
        /// 获取人员公共方法
        /// </summary>
        /// <param name="pms_person_info"></param>
        /// <returns></returns>

        public List<PersonInfoDto> CommGetPersonListInfo(List<PMS_PERSON_INFO> pms_person_info)
        {
            var sys6InspectionPgroup = _IBaseDataServices.GetInspectionPgroup();
            List<SYS6_BASE_DATA> techType = _IBaseDataServices.GetSys6BaseData().Where(a => a.CLASS_ID == "职称类型").ToList();

            List<PersonInfoDto> personinfodto = new List<PersonInfoDto>();
            foreach (var item in pms_person_info)
            {
                string pgroup_name = string.Empty;
                var sys6InspectionPgroupList = sys6InspectionPgroup.First(p => p.PGROUP_ID == item.PGROUP_ID);
                if (sys6InspectionPgroupList != null)
                {
                    pgroup_name = sys6InspectionPgroupList.PGROUP_NAME;
                }
                personinfodto.Add(new PersonInfoDto
                {
                    PERSON_ID = item.PERSON_ID,
                    PGROUP_ID = item.PGROUP_ID,
                    PGROUP_NAME = pgroup_name,
                    HOSPITAL_ID = item.HOSPITAL_ID,
                    USER_ID = item.USER_ID,
                    USER_TYPE = item.USER_TYPE,
                    USER_TYPE_NAME = RecordClassBaseName("用户类型", item.USER_TYPE),
                    USER_NAME = item.USER_NAME,
                    USER_ENAME = item.USER_ENAME,
                    SEX_NAME = RecordClassBaseName("性别", item.SEX),
                    AGE = item.AGE,
                    BIRTHDAY = item.BIRTHDAY,
                    NATION = item.NATION,
                    NATION_NAME = RecordClassBaseName("民族", item.NATION),
                    NATIVE_PLACE = item.NATIVE_PLACE,
                    POLITICIAN = item.POLITICIAN,
                    POLITICIAN_NAME = RecordClassBaseName("政治面貌", item.POLITICIAN),
                    PROFESSION = item.PROFESSION,
                    HIGHEST_DEGREE = item.HIGHEST_DEGREE,
                    HIGHEST_DEGREE_NAME = RecordClassBaseName("最高学历", item.HIGHEST_DEGREE),
                    DEGREE_TIME = item.DEGREE_TIME,
                    HIGHEST_DIPLOMA = item.HIGHEST_DIPLOMA,
                    HIGHEST_DIPLOMA_NAME = RecordClassBaseName("最高学位", item.HIGHEST_DIPLOMA),
                    DIPLOMA_TIME = item.DIPLOMA_TIME,
                    WORK_TIME = item.WORK_TIME,
                    LENGTH_SERVICE = item.LENGTH_SERVICE.ToString(),
                    IN_HOSPITAL_DATE = item.IN_HOSPITAL_DATE == null ? "" : ((DateTime)item.IN_HOSPITAL_DATE).ToString("yyyy-MM-dd"),
                    LENGTH_HOSPITAL = item.LENGTH_HOSPITAL.ToString(),
                    DUTIES = item.DUTIES,
                    DUTIES_NAME = RecordClassBaseName("职务", item.DUTIES),
                    TECH_POST = item.TECH_POST,
                    TECH_POST_NAME = RecordClassBaseName("职称级别", item.TECH_POST),
                    ACADEMIC_POST = item.ACADEMIC_POST,
                    //ACADEMIC_POST_NAME = RecordClassBaseName("职称", trainBDitem.ACADEMIC_POST),
                    ACADEMIC_POST_NAME = item.TECHNOLOGY_TYPE == "1" ? RecordClassBaseName("技师职称", item.ACADEMIC_POST)
                                        : item.TECHNOLOGY_TYPE == "2" ? RecordClassBaseName("医师职称", item.ACADEMIC_POST)
                                        : item.TECHNOLOGY_TYPE == "3" ? RecordClassBaseName("护士职称", item.ACADEMIC_POST)
                                        : item.TECHNOLOGY_TYPE == "4" ? RecordClassBaseName("研究员职称", item.ACADEMIC_POST)
                                        : string.Empty,
                    COMM_ADDR = item.COMM_ADDR,
                    HOME_TEL = item.HOME_TEL,
                    PHONE = item.PHONE,
                    CORNET = item.CORNET,
                    BIRTH_PLACE = item.BIRTH_PLACE,
                    HEIGHT = item.HEIGHT,
                    EYESIGHT = item.EYESIGHT,
                    ENGLISH_RANK = item.ENGLISH_RANK,
                    ENGLISH_RANK_NAME = RecordClassBaseName("英语级别", item.ENGLISH_RANK),
                    ENGLISH_RANK_SCORE = item.ENGLISH_RANK_SCORE,
                    MARITAL_STATUS = item.MARITAL_STATUS,
                    MARITAL_STATUS_NAME = item.MARITAL_STATUS == "1" ? "已婚" : "未婚",
                    CHILDREN_CONDITION = item.CHILDREN_CONDITION,
                    CHILDREN_CONDITION_NAME = item.CHILDREN_CONDITION == "1" ? "有" : "无",
                    CARD_TYPE = item.CARD_TYPE,
                    CARD_TYPE_NAME = RecordClassBaseName("证件类型", item.CARD_TYPE),
                    ID_CARD = item.ID_CARD,
                    DOMICILE_PLACE = item.DOMICILE_PLACE,
                    EMERGENCY_CONTACT = item.EMERGENCY_CONTACT,
                    ECONTACT_RELACTION = item.ECONTACT_RELACTION,
                    ECONTACT_PHONE = item.ECONTACT_PHONE,
                    CURRENT_ADDRESS = item.CURRENT_ADDRESS,
                    HEALTH = item.HEALTH,
                    HEALTH_NAME = item.HEALTH == "1" ? "健康" : "不健康",
                    E_MAIL = item.E_MAIL,
                    OFFICE_PHONE = item.OFFICE_PHONE,
                    EMPLOYMENT_UNIT = item.EMPLOYMENT_UNIT,
                    TECHNOLOGY_TYPE = item.TECHNOLOGY_TYPE,
                    TECHNOLOGY_TYPE_NAME = techType.FirstOrDefault(a => a.DATA_ID == item.TECHNOLOGY_TYPE)?.DATA_CNAME ?? "",
                    TECH_CERTIFICE_TIME = item.TECH_CERTIFICE_TIME == null ? "" : ((DateTime)item.TECH_CERTIFICE_TIME).ToString("yyyy-MM-dd"),
                    TECH_POST_PROFESSION = item.TECH_POST_PROFESSION,
                    EMPLOYMENT_SOURE = item.EMPLOYMENT_SOURE,
                    EMPLOYMENT_SOURE_NAME = RecordClassBaseName("入职方式", item.EMPLOYMENT_SOURE),
                    IN_LAB_TIME = item.IN_LAB_TIME,
                    LENGTH_LAB = item.LENGTH_LAB.ToString(),
                    OUT_LAB_TIME = item.OUT_LAB_TIME,
                    PROFESSION_EXPERTISE = item.PROFESSION_EXPERTISE,
                    EMPLOY_TIME = item.EMPLOY_TIME == null ? "" : ((DateTime)item.EMPLOY_TIME).ToString("yyyy-MM-dd"),
                    RETIRE_TIME = item.RETIRE_TIME == null ? "" : ((DateTime)item.RETIRE_TIME).ToString("yyyy-MM-dd"),
                    IN_HOSPITAL_TIME = item.IN_HOSPITAL_TIME == null ? "" : ((DateTime)item.IN_HOSPITAL_TIME).ToString("yyyy-MM-dd"),
                    OUT_HOSPITAL_TIME = item.OUT_HOSPITAL_TIME == null ? "" : ((DateTime)item.OUT_HOSPITAL_TIME).ToString("yyyy-MM-dd"),
                    REEMPLOY_TIME = item.REEMPLOY_TIME == null ? "" : ((DateTime)item.REEMPLOY_TIME).ToString("yyyy-MM-dd"),
                    REGISTER_MODE = item.REGISTER_MODE,
                    REGISTER_MODE_NAME = item.REGISTER_MODE == "1" ? "PC端" : "移动端",
                    REGISTER_PERSON = item.REGISTER_PERSON,
                    REGISTER_TIME = item.REGISTER_TIME,
                    SUBMIT_PERSON = item.SUBMIT_PERSON,
                    SUBMIT_TIME = item.SUBMIT_TIME,
                    CHECK_PERSON = item.CHECK_PERSON,
                    CHECK_TIME = item.CHECK_TIME,
                    CHECK_COMPUTER = item.CHECK_COMPUTER,
                    DOC_PLACE = item.DOC_PLACE,
                    PERSON_DOC_STATE = item.PERSON_DOC_STATE,
                    PERSON_DOC_STATE_NAME = RecordClassBaseName("", item.PERSON_DOC_STATE),
                    PERSON_PHOTO_PATH = item.PERSON_PHOTO_PATH == null ? "" : FileHttpUrl + item.PERSON_PHOTO_PATH,
                    PERSON_STATE = item.PERSON_STATE,
                    PERSON_STATE_NAME = item.PERSON_STATE == "0" ? "已登记" : "已审核",
                    FIRST_RPERSON = item.FIRST_RPERSON,
                    FIRST_RTIME = item.FIRST_RTIME,
                    LAST_MPERSON = item.LAST_MPERSON,
                    LAST_MTIME = item.LAST_MTIME,
                    REMARK = item.REMARK
                });
            }
            return personinfodto;
        }

        public PersonInfoDto CommGetPersonInfo(List<PMS_PERSON_INFO> pms_person_info, List<SYS6_INSPECTION_PGROUP> sys6InspectionPgroup, List<SYS6_BASE_DATA> lis5_base_data, bool isLoadSmblFile = false)
        {
            PersonInfoDto personinfodto = new PersonInfoDto();
            var personTagDict = _IPmsTagService.GetPersonIdPersonTagDict(pms_person_info.Select(w => w.PERSON_ID).ToList());

            foreach (var item in pms_person_info)
            {
                if (item == null)
                    continue;
                string pgroup_name = string.Empty;
                string labId = string.Empty;
                string areaId = string.Empty;
                var sys6InspectionPgroupList = sys6InspectionPgroup.FirstOrDefault(p => p.PGROUP_ID == item.PGROUP_ID);
                if (sys6InspectionPgroupList != null)
                {
                    pgroup_name = sys6InspectionPgroupList.PGROUP_NAME;
                    labId = sys6InspectionPgroupList.LAB_ID;
                    areaId = sys6InspectionPgroupList.AREA_ID;
                }
                personinfodto.PERSON_ID = item.PERSON_ID;
                personinfodto.PGROUP_ID = item.PGROUP_ID;
                personinfodto.LAB_ID = labId;
                personinfodto.AREA_ID = areaId;
                personinfodto.PGROUP_NAME = pgroup_name;
                personinfodto.HOSPITAL_ID = item.HOSPITAL_ID;
                personinfodto.USER_ID = item.USER_ID;
                personinfodto.USER_TYPE = item.USER_TYPE;
                personinfodto.USER_TYPE_NAME = RecordClassBaseName("用户类型", item.USER_TYPE, lis5_base_data);
                personinfodto.USER_NAME = item.USER_NAME;
                personinfodto.USER_ENAME = item.USER_ENAME;
                personinfodto.SEX = item.SEX;
                personinfodto.SEX_NAME = RecordClassBaseName("性别", item.SEX, lis5_base_data);
                personinfodto.AGE = item.AGE;
                personinfodto.BIRTHDAY = item.BIRTHDAY;
                personinfodto.ID_CARD = item.ID_CARD;
                if (personinfodto.BIRTHDAY.IsNullOrEmpty() && personinfodto.ID_CARD.IsNotNullOrEmpty() && personinfodto.ID_CARD.Length == 18)
                {
                    personinfodto.BIRTHDAY = personinfodto.ID_CARD.Substring(6, 4) + "-" + personinfodto.ID_CARD.Substring(10, 2) + "-" + personinfodto.ID_CARD.Substring(12, 2);
                }
                personinfodto.NATION = item.NATION;
                personinfodto.NATION_NAME = RecordClassBaseName("民族", item.NATION, lis5_base_data);
                personinfodto.NATIVE_PLACE = item.NATIVE_PLACE;
                // personinfodto. //NATIVE_PLACE_NAME = "";
                personinfodto.POLITICIAN = item.POLITICIAN;
                personinfodto.POLITICIAN_NAME = RecordClassBaseName("政治面貌", item.POLITICIAN, lis5_base_data);
                personinfodto.PROFESSION = item.PROFESSION;
                personinfodto.HIGHEST_DEGREE = item.HIGHEST_DEGREE;
                personinfodto.HIGHEST_DEGREE_NAME = RecordClassBaseName("最高学历", item.HIGHEST_DEGREE, lis5_base_data);
                personinfodto.DEGREE_TIME = item.DEGREE_TIME;
                personinfodto.HIGHEST_DIPLOMA = item.HIGHEST_DIPLOMA;
                personinfodto.HIGHEST_DIPLOMA_NAME = RecordClassBaseName("最高学位", item.HIGHEST_DIPLOMA, lis5_base_data);
                personinfodto.DIPLOMA_TIME = item.DIPLOMA_TIME;
                personinfodto.WORK_TIME = item.WORK_TIME;
                personinfodto.LENGTH_SERVICE = item.LENGTH_SERVICE.ToString();
                personinfodto.IN_HOSPITAL_DATE = item.IN_HOSPITAL_DATE == null ? "" : ((DateTime)item.IN_HOSPITAL_DATE).ToString("yyyy-MM-dd");
                personinfodto.LENGTH_HOSPITAL = item.LENGTH_HOSPITAL.ToString();
                personinfodto.DUTIES = item.DUTIES;
                personinfodto.DUTIES_NAME = RecordClassBaseName("职务", item.DUTIES, lis5_base_data);
                personinfodto.TECH_POST = item.TECH_POST;
                personinfodto.TECH_POST_NAME = RecordClassBaseName("职称级别", item.TECH_POST, lis5_base_data);
                personinfodto.LOGID = item.LOGID;
                personinfodto.HIS_ID = item.HIS_ID;
                personinfodto.TECHNOLOGY_TYPE = item.TECHNOLOGY_TYPE;
                personinfodto.TECHNOLOGY_TYPE_NAME = RecordClassBaseName("职称类型", item.TECHNOLOGY_TYPE, lis5_base_data);
                personinfodto.ACADEMIC_POST = item.ACADEMIC_POST;
                //personinfodto.ACADEMIC_POST_NAME = RecordClassBaseName("职称", trainBDitem.ACADEMIC_POST);
                personinfodto.ACADEMIC_POST_NAME = personinfodto.TECHNOLOGY_TYPE == "1" ? RecordClassBaseName("技师职称", item.ACADEMIC_POST, lis5_base_data)
                                                 : personinfodto.TECHNOLOGY_TYPE == "2" ? RecordClassBaseName("医师职称", item.ACADEMIC_POST, lis5_base_data)
                                                 : personinfodto.TECHNOLOGY_TYPE == "3" ? RecordClassBaseName("护士职称", item.ACADEMIC_POST, lis5_base_data)
                                                 : personinfodto.TECHNOLOGY_TYPE == "4" ? RecordClassBaseName("研究员职称", item.ACADEMIC_POST, lis5_base_data)
                                                 : string.Empty;
                personinfodto.COMM_ADDR = item.COMM_ADDR;
                personinfodto.HOME_TEL = item.HOME_TEL;
                personinfodto.PHONE = item.PHONE;
                personinfodto.CORNET = item.CORNET;
                personinfodto.BIRTH_PLACE = item.BIRTH_PLACE;
                personinfodto.HEIGHT = item.HEIGHT;
                personinfodto.EYESIGHT = item.EYESIGHT;
                personinfodto.ENGLISH_RANK = item.ENGLISH_RANK;
                personinfodto.ENGLISH_RANK_NAME = RecordClassBaseName("英语级别", item.ENGLISH_RANK, lis5_base_data);
                personinfodto.ENGLISH_RANK_SCORE = item.ENGLISH_RANK_SCORE;
                personinfodto.MARITAL_STATUS = item.MARITAL_STATUS;
                personinfodto.MARITAL_STATUS_NAME = item.MARITAL_STATUS == "1" ? "已婚" : "未婚";
                personinfodto.CHILDREN_CONDITION = item.CHILDREN_CONDITION;
                personinfodto.CHILDREN_CONDITION_NAME = item.CHILDREN_CONDITION == "1" ? "有" : "无";
                personinfodto.CARD_TYPE = item.CARD_TYPE;
                personinfodto.CARD_TYPE_NAME = RecordClassBaseName("证件类型", item.CARD_TYPE, lis5_base_data);

                personinfodto.DOMICILE_PLACE = item.DOMICILE_PLACE;
                personinfodto.EMERGENCY_CONTACT = item.EMERGENCY_CONTACT;
                personinfodto.ECONTACT_RELACTION = item.ECONTACT_RELACTION;
                personinfodto.ECONTACT_PHONE = item.ECONTACT_PHONE;
                personinfodto.CURRENT_ADDRESS = item.CURRENT_ADDRESS;
                personinfodto.HEALTH = item.HEALTH;
                personinfodto.HEALTH_NAME = RecordClassBaseName("健康状况", item.HEALTH, lis5_base_data);
                personinfodto.E_MAIL = item.E_MAIL;
                personinfodto.OFFICE_PHONE = item.OFFICE_PHONE;
                personinfodto.EMPLOYMENT_UNIT = item.EMPLOYMENT_UNIT;
                personinfodto.TECH_CERTIFICE_TIME = item.TECH_CERTIFICE_TIME == null ? "" : ((DateTime)item.TECH_CERTIFICE_TIME).ToString("yyyy-MM-dd");
                personinfodto.TECH_POST_PROFESSION = item.TECH_POST_PROFESSION;
                personinfodto.IN_LAB_TIME = item.IN_LAB_TIME;
                personinfodto.LENGTH_LAB = item.LENGTH_LAB.ToString();
                personinfodto.OUT_LAB_TIME = item.OUT_LAB_TIME;
                personinfodto.EMPLOYMENT_SOURE = item.EMPLOYMENT_SOURE;
                personinfodto.EMPLOYMENT_SOURE_NAME = RecordClassBaseName("入职方式", item.EMPLOYMENT_SOURE, lis5_base_data);
                personinfodto.PROFESSION_EXPERTISE = item.PROFESSION_EXPERTISE;
                personinfodto.EMPLOY_TIME = item.EMPLOY_TIME == null ? "" : ((DateTime)item.EMPLOY_TIME).ToString("yyyy-MM-dd");
                personinfodto.RETIRE_TIME = item.RETIRE_TIME == null ? "" : ((DateTime)item.RETIRE_TIME).ToString("yyyy-MM-dd");
                personinfodto.IN_HOSPITAL_TIME = item.IN_HOSPITAL_TIME == null ? "" : ((DateTime)item.IN_HOSPITAL_TIME).ToString("yyyy-MM-dd");
                personinfodto.OUT_HOSPITAL_TIME = item.OUT_HOSPITAL_TIME == null ? "" : ((DateTime)item.OUT_HOSPITAL_TIME).ToString("yyyy-MM-dd");
                personinfodto.REEMPLOY_TIME = item.REEMPLOY_TIME == null ? "" : ((DateTime)item.REEMPLOY_TIME).ToString("yyyy-MM-dd");
                personinfodto.REGISTER_MODE = item.REGISTER_MODE;
                personinfodto.REGISTER_MODE_NAME = item.REGISTER_MODE == "1" ? "PC端" : "移动端";
                personinfodto.REGISTER_PERSON = item.REGISTER_PERSON;
                personinfodto.REGISTER_TIME = item.REGISTER_TIME;
                personinfodto.SUBMIT_PERSON = item.SUBMIT_PERSON;
                personinfodto.SUBMIT_TIME = item.SUBMIT_TIME;
                personinfodto.CHECK_PERSON = item.CHECK_PERSON;
                personinfodto.CHECK_TIME = item.CHECK_TIME;
                personinfodto.CHECK_COMPUTER = item.CHECK_COMPUTER;
                personinfodto.DOC_PLACE = item.DOC_PLACE;
                personinfodto.PERSON_DOC_STATE = item.PERSON_DOC_STATE;
                personinfodto.PERSON_DOC_STATE_NAME = RecordClassBaseName("人员状态", item.PERSON_DOC_STATE, lis5_base_data);
                string curPatn = file_preview_address + item.PERSON_PHOTO_PATH;
                if (item.PERSON_PHOTO_PATH == null || item.PERSON_PHOTO_PATH == "")
                {
                    curPatn = "/ExampleFile/default.jpg";
                }
                else
                {
                    curPatn = FileHttpUrl + item.PERSON_PHOTO_PATH;
                }
                string curFacePatn = file_preview_address + item.FACE_PHOTO;
                if (item.FACE_PHOTO == null || item.FACE_PHOTO == "")
                {
                    curFacePatn = "/ExampleFile/default.jpg";
                }
                else
                {
                    curFacePatn = FileHttpUrl + item.FACE_PHOTO;
                }
                string curFaceClipPatn = file_preview_address + item.FACE_CLIP_PHOTO;
                if (item.FACE_CLIP_PHOTO == null || item.FACE_CLIP_PHOTO == "")
                {
                    curFaceClipPatn = "/ExampleFile/default.jpg";
                }
                else
                {
                    curFaceClipPatn = FileHttpUrl + item.FACE_CLIP_PHOTO;
                }
                personinfodto.FACE_PHOTO = curFacePatn;
                personinfodto.FACE_CLIP_PHOTO = curFaceClipPatn;
                personinfodto.PERSON_PHOTO_PATH = curPatn;
                personinfodto.PHOTO_PATH = curPatn;
                personinfodto.PERSON_STATE = item.PERSON_STATE;
                personinfodto.PERSON_STATE_NAME = item.PERSON_STATE == "0" ? "已登记" : "已审核";
                personinfodto.FIRST_RPERSON = item.FIRST_RPERSON;
                personinfodto.FIRST_RTIME = item.FIRST_RTIME;
                personinfodto.LAST_MPERSON = item.LAST_MPERSON;
                personinfodto.LAST_MTIME = item.LAST_MTIME;
                personinfodto.REMARK = item.REMARK;
                personinfodto.IF_EMPLOYMENT = item.IF_EMPLOYMENT;
                personinfodto.IF_EMPLOYMENT_NAME = personinfodto.IF_EMPLOYMENT == "1" ? "是" : "否";
                personinfodto.EYESIGHT_LEFT = item.EYESIGHT_LEFT;
                personinfodto.EYESIGHT_RIGHT = item.EYESIGHT_RIGHT;
                personinfodto.GRADUATE_SCHOOL = item.GRADUATE_SCHOOL;
                personinfodto.GRADUATE_DATE = item.GRADUATE_DATE;
                personinfodto.COLOR_DEFICIENCY = item.COLOR_DEFICIENCY;
                personinfodto.RECORD_DATA = item.RECORD_DATA;
                personinfodto.TAGS = personTagDict[personinfodto.PERSON_ID].Select(a => (object)a).ToList();
                if (_httpContext.GetSmblFlag() == "1")
                {
                    LoadSmblListCache();
                    var person_postIds = _SMBL_POST_PERSON_CACHE
                        .Where(a => a.PERSON_ID == personinfodto.PERSON_ID && a.POST_PSTATE == "1" && a.DEPT_POST_ID != null)
                        .Select(a => a.DEPT_POST_ID)
                        .Distinct()
                        .ToList();

                    var person_posts = _SMBL_DEPT_POST_CACHE
                        .Where(a => person_postIds.Contains(a.DEPT_POST_ID) && a.DEPT_POST_STATE == "1")
                        .Select(a => new { a.DEPT_POST_ID, a.DEPT_POST_NAME, a.SMBL_DEPT_ID })
                        .ToList();

                    var person_smbl_dept_posts = person_posts.Join(_SMBL_DEPT_NODE_CACHE,
                         p => p.SMBL_DEPT_ID,
                         n => n.NODE_ID,
                         (p, n) => new { p.DEPT_POST_ID, p.DEPT_POST_NAME, n.NODE_ID, n.NODE_NAME, n.PARENT_NODE_ID })
                        .ToList();

                    var person_smbl_labs = _SMBL_LAB_CACHE
                        .Where(a => a.SMBL_LAB_STATE == "1" && a.PGROUP_SID != null)
                        .Select(a => new { a.SMBL_LAB_CNAME, a.PGROUP_SID })
                        .ToList();

                    var person_smbl_lab_names = person_smbl_labs.Where(a => a.PGROUP_SID.Split(',').Contains(personinfodto.PGROUP_ID)).Select(a => a.SMBL_LAB_CNAME);

                    personinfodto.SMBL_POST = string.Join('、', person_posts.Select(a => a.DEPT_POST_NAME));
                    personinfodto.SMBL_DEPT_NAME = string.Join('、', person_smbl_dept_posts.Select(a => a.NODE_NAME));
                    personinfodto.SMBL_LAB_NAME = string.Join('、', person_smbl_lab_names);

                    personinfodto.SMBL_POST_LIST = new List<object>();
                    person_smbl_dept_posts.ForEach(a => personinfodto.SMBL_POST_LIST.Add(new { a.DEPT_POST_NAME, DEPT_NAME = a.NODE_NAME, PARENT_DEPT_NAME = _SMBL_DEPT_NODE_CACHE.Find(n => n.NODE_ID == a.PARENT_NODE_ID)?.NODE_NAME ?? "/" }));

                    if (isLoadSmblFile)
                    {
                        //证书类型：生物安全培训合格证的OA_BASE_DATA.DATA_ID
                        //string trainCerBaseDataId = _configuration.GetValue<string>("SmblTrainQualificationDataId") ?? throw new BizException("生物安全培训合格证的基础数据ID未设置！");
                        string trainCerBaseDataId = "XBD00000011";

                        var trainCerRecords = _soa.Db.Queryable<PMS_SKILL_CERTIFICATE_LIST>().Where(a => a.PERSON_ID == personinfodto.PERSON_ID && a.CHECK_STATE == "2" && a.CERTIFICATE_TYPE == trainCerBaseDataId)
                            .Select(a => new { a.CERTIFICATE_NUMBER, a.CERTIFICATE_AFFIX })
                            .ToList();

                        List<string> trainCerFileIds = trainCerRecords.SelectMany(r => r.CERTIFICATE_AFFIX.IsNullOrEmpty() ? Array.Empty<string>() : r.CERTIFICATE_AFFIX.Split(',')).ToList();

                        var trainCerFiles = _soa.Db.Queryable<PMS_PERSON_FILE>().Where(a => trainCerFileIds.Contains(a.FILE_ID) && a.FILE_STATE == "1").ToList();
                        trainCerFiles.ForEach(file => file.HTTP_FILE_PATH = FileHttpUrl + file.FILE_PATH + file.FILE_CNAME + OfficeHelper.CastToPreviewSuffix(file.FILE_SUFFIX));

                        personinfodto.SMBL_TRAIN_CER_FILES = new List<object>();
                        trainCerFiles.ForEach(file => personinfodto.SMBL_TRAIN_CER_FILES.Add(file));
                        personinfodto.SMBL_TRAIN_CER_NO = string.Join('、', trainCerRecords.Select(r => r.CERTIFICATE_NUMBER));

                        personinfodto.SMBL_POST_TYPE = GetFieldFromJsonData(item, "SMBL_POST_TYPE");
                        personinfodto.SMBL_POST_NOW = GetFieldFromJsonData(item, "SMBL_POST_NOW");
                    }
                }
            }
            return personinfodto;
        }

        private string GetFieldFromJsonData(PMS_PERSON_INFO person, string fieldName)
        {
            if (person.RECORD_DATA.IsNullOrEmpty())
                return "";
            Dictionary<string, object> jsonDict = DeserializeObject(person.RECORD_DATA);//解析JSON
            return jsonDict.TryGetValue(fieldName, out object fieldValue) && fieldValue != null ? fieldValue.ToString() : "";
        }

        /// <summary>
        ///获取基础数据名称
        /// </summary>
        /// <param name="class_id"></param>
        /// <param name="data_id"></param>
        /// <returns></returns>
        public string RecordClassBaseName(string class_id, string data_id)
        {
            string className = string.Empty;
            if (data_id != null)
            {
                List<SYS6_BASE_DATA> lis5_base_data = _IBaseDataServices.GetSys6BaseData();
                if (lis5_base_data.Where(x => x.CLASS_ID == class_id && x.DATA_ID == data_id).FirstOrDefault() != null)
                {
                    className = lis5_base_data.Where(x => x.CLASS_ID == class_id && x.DATA_ID == data_id).FirstOrDefault().DATA_CNAME;
                }
            }
            return className;
        }

        /// <summary>
        ///获取基础数据名称
        /// </summary>
        /// <param name="class_id"></param>
        /// <param name="data_id"></param>
        /// <returns></returns>
        public string RecordClassBaseName(string class_id, string data_id, List<SYS6_BASE_DATA> lis5_base_data)
        {
            string className = string.Empty;
            if (data_id != null)
            {
                //  List<SYS6_BASE_DATA> lis5_base_data = _IBaseDataServices.GetSys6BaseData();
                if (lis5_base_data.Where(x => x.CLASS_ID == class_id && x.DATA_ID == data_id).FirstOrDefault() != null)
                {
                    className = lis5_base_data.Where(x => x.CLASS_ID == class_id && x.DATA_ID == data_id).FirstOrDefault().DATA_CNAME;
                }
            }
            return className;
        }
        /// <summary>
        ///获取人事基础数据名称(人事系统自行控制)
        /// </summary>
        /// <param name="class_id"></param>
        /// <param name="data_id"></param>
        /// <returns></returns>
        public string RecordClassOaBaseName(string class_id, string data_id)
        {
            string className = string.Empty;
            if (data_id != null)
            {
                List<OA_BASE_DATA> lis5_base_data = _IBaseDataServices.GetOaBaseData();
                if (lis5_base_data.Where(x => x.DATA_ID == data_id).FirstOrDefault() != null)
                {
                    className = lis5_base_data.Where(x => x.DATA_ID == data_id).FirstOrDefault().DATA_NAME;
                }
            }
            return className;
        }
        ///// <summary>
        ///// 根据出生日期，计算精确的年龄
        ///// </summary>
        ///// <param name="birthDate">生日</param>
        ///// <returns></returns>
        //public static int? CalculateAge(string birthDay)
        //{
        //    if (DateTime.TryParse(birthDay, out DateTime birthDate))
        //    {
        //        DateTime nowDateTime = DateTime.Now;
        //        int age = nowDateTime.Year - birthDate.Year;
        //        //再考虑月、天的因素
        //        if (nowDateTime.Month < birthDate.Month || (nowDateTime.Month == birthDate.Month && nowDateTime.Day < birthDate.Day))
        //        {
        //            age--;
        //        }
        //        return age;
        //    }
        //    else return null;
        //}

        /// <summary>
        /// 下载文件到本地
        /// </summary>
        /// <param name="serviceUrl"></param>
        /// <param name="localUrl"></param>
        private byte[] GetConvertType(string serviceUrl)
        {
            byte[] buffer = null;
            if (FileExists(serviceUrl))
            {
                ServicePointManager.ServerCertificateValidationCallback = delegate
                { return true; };
                WebClient client = new WebClient();
                ServicePointManager.SecurityProtocol = (SecurityProtocolType)192 | (SecurityProtocolType)768 | (SecurityProtocolType)3072;
                buffer = client.DownloadData(serviceUrl);
            }
            else
            {
                string contentRootPath = _hostingEnvironment.ContentRootPath;
                string path = Path.Combine(contentRootPath + "ExampleFile/default.jpg");
                FileStream fs = new FileStream(path, FileMode.Open);
                buffer = new byte[fs.Length];
                fs.Read(buffer, 0, buffer.Length);
                fs.Close();
            }

            return buffer;
        }

        /// <summary>
        /// 下载文件到本地
        /// </summary>
        /// <param name="serviceUrl"></param>
        /// <param name="localUrl"></param>
        private byte[] GetConvertIconType(string serviceUrl)
        {
            byte[] buffer = null;
            if (FileExists(serviceUrl))
            {
                ServicePointManager.ServerCertificateValidationCallback = delegate
                { return true; };
                WebClient client = new WebClient();
                ServicePointManager.SecurityProtocol = (SecurityProtocolType)192 | (SecurityProtocolType)768 | (SecurityProtocolType)3072;
                buffer = client.DownloadData(serviceUrl);
            }
            else
            {
                string contentRootPath = _hostingEnvironment.ContentRootPath;
                string path = Path.Combine(contentRootPath + "ExampleFile/icon.png");
                FileStream fs = new FileStream(path, FileMode.Open);
                buffer = new byte[fs.Length];
                fs.Read(buffer, 0, buffer.Length);
                fs.Close();
            }

            return buffer;
        }

        /// <summary>
        /// 判断远程文件是否存在
        /// </summary>
        /// <param name="url">url地址</param>
        /// <returns></returns>
        public bool FileExists(string url)
        {
            ServicePointManager.ServerCertificateValidationCallback = delegate
            { return true; };
            ServicePointManager.SecurityProtocol = (SecurityProtocolType)192 | (SecurityProtocolType)768 | (SecurityProtocolType)3072;
            //ture为存在，false为不存在
            bool result = false;
            WebResponse response = null;
            try
            {
                WebRequest req = WebRequest.Create(url);
                response = req.GetResponse();
                result = response == null ? false : true;
            }
            catch (Exception ex)
            {
                result = false;
            }
            finally
            {
                if (response != null)
                {
                    response.Close();
                }
            }
            return result;
        }

        #endregion
    }
}
