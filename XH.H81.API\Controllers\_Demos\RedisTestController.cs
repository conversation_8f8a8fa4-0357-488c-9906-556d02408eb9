﻿using H.BASE;
using H.Utility;
using Microsoft.AspNetCore.Mvc;
using StackExchange.Redis;
using StackExchange.Redis.Extensions.Core.Abstractions;
using System.Diagnostics;
using XH.H81.Models.Dtos;


namespace XH.H81.API.Controllers._demos
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [ApiExplorerSettings(GroupName = "DEMO")]
    public class RedisTestController : ControllerBase
    {
        private readonly IRedisClientFactory _clientFactory;
        private readonly IRedisDatabase _db;
        //private readonly IEasyCachingProvider _easycahce;
        private readonly IRedisClient _redisS03;
        private readonly IRedisClient _redisS02;
        string testStreamKey = "XH:BASE:STREAM:LIS_CHARGE_ITEM";
        string testStreamKey2 = "XH:BASE:STREAM:LIS_TEST_ITEM";
        public RedisTestController(IRedisClientFactory clientFactory,IRedisDatabase db/*,IEasyCachingProvider easycahce*/)
        {  
            //easycaching
            //为了兼容老版本缓存读写方式,老用法保留,由于easycaching不支持Stream数据类型,所以新版本更换成了Stackexchange
            //_easycahce = easycahce;
            //一下为新版本用法
            //工厂方式
            _clientFactory = clientFactory;
            //直接注入默认redis的默认database 谨慎,需要S01配置默认redis
            _db = db;
            //指定redis 一般用这种方式
            _redisS03=_clientFactory.GetRedisClient("S03");
            _redisS02=_clientFactory.GetRedisClient("S02");

        }
        [HttpGet]
        public IActionResult Write(string key, string val)
        {
            //_redisS03.GetDefaultDatabase().Database.StringSet(key, val, TimeSpan.FromHours(1));
          
            ////[过期仅演示]easycaching用法
            //_easycahce.Set<string>(key+"_easycaching", val+"_easycaching", TimeSpan.FromHours(1));
            return Ok("success");
        }
        [HttpGet]
        public IActionResult Read(string key)
        {
          //  var val= _redisS03.GetDefaultDatabase().Database.StringGet(key);
          //var valEasychcing= _easycahce.Get<string>(key);
          return Ok(/*val.ToString()+valEasychcing*/);
        }

        [HttpGet]
        public IActionResult WriteListBechMark()
        {
            string log = "";
            List<StartTemplateDto> largeList = new List<StartTemplateDto>();

            #region 构造测试list
            for (int i = 0; i < 50000; i++)
            {
                largeList.Add(new StartTemplateDto()
                {
                    Name = "xxxsdfsdfsd手动阀手动阀胜多负少士大夫士大夫时尚大方士大夫撒旦发射点士大夫",
                    Name2 = "34534",
                    Name3 = "威威日",
                    Name4 = "士大夫",
                    Name5 = "sdfsdf",
                    Value = i.ToString(),
                    Id = "ID" + i.ToString()
                });
            }
            #endregion
           
            /////
            Stopwatch sw = new Stopwatch();
            sw.Start();

           _redisS03.GetDefaultDatabase().AddAsync("TEST_LIST", largeList, TimeSpan.FromHours(1))
               .GetAwaiter()
               .GetResult();

           // _cacheRedis1.Set("TestLargeList", largeList, TimeSpan.FromMinutes(5));
            sw.Stop();

            log = "写入耗时:" + sw.ElapsedMilliseconds.ToString() + "ms";
            return Ok(log);
        }
        
        [HttpGet]
        public IActionResult ReadListBechMark()
        {
            string log = "";

            /////
            Stopwatch sw = new Stopwatch();
            sw.Start();
            var x= _redisS03.GetDefaultDatabase().GetAsync<List<StartTemplateDto>>("TEST_LIST")
                .GetAwaiter()
                .GetResult();

            // _cacheRedis1.Set("TestLargeList", largeList, TimeSpan.FromMinutes(5));
            sw.Stop();

            log = $"读取耗时:{sw.ElapsedMilliseconds.ToString()}ms,对象数量:{x.Count}";
            return Ok(log);
        }

        /// <summary>
        /// 往消息队列中添加消息 注:添加消息时如果队列不存在会自动创建队列
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult AddStream()
        {
            //添加新消息,id=*=自动生成 最大数量 10000条
            _redisS02.GetDefaultDatabase().Database.StreamAdd(testStreamKey,
                new NameValueEntry[] { new NameValueEntry("table_name",DateTime.Now.ToComonTimeFormat())}, "*",10000);
            return Ok();
        }
        /// <summary>
        /// 往消息队列2中添加消息 注:添加消息时如果队列不存在会自动创建队列
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult AddStream2()
        {
            //添加新消息,id=*=自动生成 最大数量 10000条
            _redisS02.GetDefaultDatabase().Database.StreamAdd(testStreamKey2,
                new NameValueEntry[] { new NameValueEntry("table_name",DateTime.Now.ToComonTimeFormat())}, "*",10000);
            return Ok();
        }

        [HttpGet]
        public IActionResult RegistryConsumeGroup(string? consumerGroupName)
        {
            consumerGroupName = consumerGroupName??AppSettingsProvider.CurrModuleId;
            //false=消息队列不存在时是否自动创建消息队列 $ =从最新的队列开始读
            _redisS02.GetDefaultDatabase().Database.StreamCreateConsumerGroup(testStreamKey, consumerGroupName, "$",false);
            return Ok();
        }

        [HttpGet]
        public IActionResult ReadStreamGroupMsg(string? consumerGroupName)
        {
            consumerGroupName = consumerGroupName??AppSettingsProvider.CurrModuleId;
            var x=_redisS02.GetDefaultDatabase().Database.StreamReadGroup(testStreamKey, consumerGroupName, "ConsumerName","0",5);
            return Ok(x);
        }

        /// <summary>
        /// 确认消息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult AcknowledgeMsg(string? consumerGroupName,string messageid)
        {
            consumerGroupName = consumerGroupName??AppSettingsProvider.CurrModuleId;
            var x=_redisS02.GetDefaultDatabase().Database.StreamAcknowledge(testStreamKey, consumerGroupName, messageid);
            return Ok(x);
        }
       
    }
}
