﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net6.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
      <Optimize>False</Optimize>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
      <Optimize>False</Optimize>
    </PropertyGroup>

    <ItemGroup>
      <Compile Remove="UploadFileService.cs" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="System.ServiceModel.Duplex" Version="4.8.*" />
        <PackageReference Include="System.ServiceModel.Http" Version="4.10.0" />
        <PackageReference Include="System.ServiceModel.NetTcp" Version="4.8.*" />
        <PackageReference Include="System.ServiceModel.Security" Version="4.8.*" />
        <PackageReference Include="System.Xml.XmlSerializer" Version="4.3.0" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\XH.H81.Base\XH.H81.Base.csproj" />
        <ProjectReference Include="..\XH.H81.IServices\XH.H81.IServices.csproj" />
        <ProjectReference Include="..\XH.H81.Models\XH.H81.Models.csproj" />
    </ItemGroup>

</Project>
