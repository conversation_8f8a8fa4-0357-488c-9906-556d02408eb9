﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using SqlSugar.DbConvert;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Exam
{
    /// <summary>
    /// 考试信息表
    /// </summary>
    [DBOwner("XH_OA")]
    public class EMP_EXAM_INFO
    {
        /// <summary>
        /// 考试ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Key]
        public string? EXAM_ID { get; set; }
        /// <summary>
        /// 医疗机构ID
        /// </summary>
        public string? HOSPITAL_ID { get; set; }
        /// <summary>
        /// 专业组ID
        /// </summary>
        public string? PGROUP_ID { get; set; }
        /// <summary>
        /// 试卷ID
        /// </summary>
        public string? PAPER_ID { get; set; }

        /// <summary>
        /// 信息来源ID 0代表考试系统 其他则是人员评估的评估计划ID
        /// </summary>
        public string? SOURCE_INFO_ID { get; set; }
        /// <summary>
        /// 考试年份(YYYY)
        /// </summary>
        public int? EXAM_YEAR { get; set; }
        /// <summary>
        /// 考试名称
        /// </summary>
        public string? EXAM_NAME { get; set; }
        /// <summary>
        /// 考试编号
        /// </summary>
        public string? EXAM_NO { get; set; }
        /// <summary>
        /// 考试排序号
        /// </summary>
        public string? EXAM_SORT { get; set; }
        /// <summary>
        /// 考试分类
        /// </summary>
        public string? EXAM_CLASS { get; set; }
        /// <summary>
        /// 考试须知
        /// </summary>
        public string? EXAM_NOTES { get; set; }
        /// <summary>
        /// 答题次数  -1 及格后不允许再考  0:不限次数 其他值限制多少次
        /// </summary>
        public int? EXAM_TIMES { get; set; }
        /// <summary>
        /// 答卷模式
        /// </summary>
        public string? ANSWER_MODE { get; set; }
        /// <summary>
        /// 考试积分
        /// </summary>
        public string? EXAM_POINT { get; set; }
        /// <summary>
        /// 考试金额
        /// </summary>
        public decimal? EXAM_MONEY { get; set; }
        /// <summary>
        /// 考试开始时间
        /// </summary>
        public DateTime? EXAM_START_DATE { get; set; }
        /// <summary>
        /// 考试结束时间
        /// </summary>
        public DateTime? EXAM_END_DATE { get; set; }
        /// <summary>
        /// 答卷时长(分钟)
        /// </summary>
        public int? ANSWER_TIMES { get; set; }
        /// <summary>
        /// 提醒时间类型
        /// </summary>
        public string? WARN_TIME_TYPE { get; set; }
        /// <summary>
        /// 提醒时间
        /// </summary>
        public string? WARN_TIME { get; set; }
        /// <summary>
        /// 成绩设置  1交卷后显示成绩  2交卷后不显示成绩
        /// </summary>
        public string? SCORE_SETUP { get; set; }
        /// <summary>
        /// 考试设置(JSON) EXAM_SETUP字段-   1:交卷后允许查看标准答案和解析
        /// </summary>
        public string? EXAM_SETUP { get; set; }
        /// <summary>
        /// 基础防作弊(JSON)
        /// </summary>
        public string? BASE_ANTI_CHEATING { get; set; }
        /// <summary>
        /// 智能防作弊(JSON)
        /// </summary>
        public string? AI_ANTI_CHEATING { get; set; }
        /// <summary>
        /// 结束语设置
        /// </summary>
        public string? TAG_SETUP { get; set; }
        /// <summary>
        /// 审核者
        /// </summary>
        public string? CHECK_PERSON { get; set; }
        /// <summary>
        /// 审核时间
        /// </summary>
        public DateTime? CHECK_TIME { get; set; }
        /// <summary>
        /// 批准者
        /// </summary>
        public string? APPROVAL_PERSON { get; set; }
        /// <summary>
        /// 批准时间
        /// </summary>
        public DateTime? APPROVAL_TIME { get; set; }
        /// <summary>
        /// 评价设置
        /// </summary>
        public string? EVALUATION_SETUP { get; set; }
        /// <summary>
        /// 考试状态[0未提交1已提交2已审核未通过(驳回)3待发布（审核通过）4已发布 10已删除]
        /// </summary>
        public string? EXAM_STATE { get; set; }
        /// <summary>
        /// 首次登记人
        /// </summary>
        public string? FIRST_RPERSON { get; set; }
        /// <summary>
        /// 首次登记时间
        /// </summary>
        public DateTime? FIRST_RTIME { get; set; }
        /// <summary>
        /// 最后修改人员
        /// </summary>
        public string? LAST_MPERSON { get; set; }
        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime? LAST_MTIME { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string? REMARK { get; set; }


        /// <summary>
        /// 评卷人员
        /// </summary>
        public string? MARK_EXAM_PERSON { get; set; }

        /// <summary>
        /// 所属科室id
        /// </summary>
        public string? EMP_LAB_ID { get; set; }


        /// <summary>
        /// 所属类型 0科室 1专业组
        /// </summary>
        public string? LAB_PGROUP_TYPE { get; set; }


        /// <summary>
        /// 归评方案ID
        /// </summary>
        public string? EPLAN_ID { get; set; }
    }
}
