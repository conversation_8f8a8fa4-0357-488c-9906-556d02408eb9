﻿using H.BASE.SqlSugarInfra;
using SqlSugar;
using XH.H81.Models.Entites;
using XH.H81.Models.Entities;
using XH.H81.Models.Entities.Eguard;
using XH.H81.Models.Entities.EvaluatePlan;
using XH.H81.Models.Entities.others;
using XH.H81.Models.Entities.Pms;
using XH.H81.Models.Entities.Tag;
using XH.H81.Models.ExternalEntity;
using XH.LAB.UTILS.Models;
using XH.LAB.UTILS.Models.Entites;

namespace XH.H81.Models.SugarDbContext
{
    public class SugarDbContext_Base : SugarDbContext_Base_Utils
    {
        #region 人事
        public virtual SqlSugarDbSet<PMS_PERSON_INFO> PMS_PERSON_INFO { get; set; }
        /// <summary>
        /// 模块分类
        /// </summary>
        //public virtual SqlSugarDbSet<SYS_CLASS_INFO> SYS_CLASS_INFO { get; set; }
        /// <summary>
        /// 模块信息
        /// </summary>
        /// <summary>
        /// 架构信息
        /// </summary>
        public virtual SqlSugarDbSet<PMS_ARCHITECTURE_INFO> PMS_ARCHITECTURE_INFO { get; set; }

        /// <summary>
        /// 履历记录
        /// </summary>
        public virtual SqlSugarDbSet<PMS_RESUME_LIST> PMS_RESUME_LIST { get; set; }

        /// <summary>
        /// 奖惩记录
        /// </summary>
        public virtual SqlSugarDbSet<PMS_REWARD_LIST> PMS_REWARD_LIST { get; set; }
        /// <summary>
        /// 教学记录
        /// </summary>
        public virtual SqlSugarDbSet<PMS_TEACH_LIST> PMS_TEACH_LIST { get; set; }
        /// <summary>
        /// 进修记录
        /// </summary>
        public virtual SqlSugarDbSet<PMS_STUDY_LIST> PMS_STUDY_LIST { get; set; }
        /// <summary>
        /// 课题记录
        /// </summary>
        public virtual SqlSugarDbSet<PMS_RESEARCH_LIST> PMS_RESEARCH_LIST { get; set; }
        /// <summary>
        /// 论文记录
        /// </summary>
        public virtual SqlSugarDbSet<PMS_THESIS_LIST> PMS_THESIS_LIST { get; set; }
        /// <summary>
        /// 教育记录
        /// </summary>
        public virtual SqlSugarDbSet<PMS_EDUCATION_LIST> PMS_EDUCATION_LIST { get; set; }
        /// <summary>
        /// 技能证书记录
        /// </summary>
        public virtual SqlSugarDbSet<PMS_SKILL_CERTIFICATE_LIST> PMS_SKILL_CERTIFICATE_LIST { get; set; }
        /// <summary>
        /// 培训记录
        /// </summary>
        public virtual SqlSugarDbSet<PMS_TRAIN_LIST> PMS_TRAIN_LIST { get; set; }
        /// <summary>
        /// 社会任职记录
        /// </summary>
        public virtual SqlSugarDbSet<PMS_SOCIAL_OFFICE_LIST> PMS_SOCIAL_OFFICE_LIST { get; set; }
        /// <summary>
        /// 知识产权记录
        /// </summary>
        public virtual SqlSugarDbSet<PMS_INTELLECTUAL_LIST> PMS_INTELLECTUAL_LIST { get; set; }
        /// <summary>
        /// 请假信息
        /// </summary>
        //public virtual SqlSugarDbSet<PMS_LEAVE_INFO> PMS_LEAVE_INFO { get; set; }
        /// <summary>
        /// 访问交流记录
        /// </summary>
        public virtual SqlSugarDbSet<PMS_EXCHANGE_LIST> PMS_EXCHANGE_LIST { get; set; }

        /// <summary>
        /// 职称记录
        /// </summary>
        public virtual SqlSugarDbSet<PMS_PROFESSIONAL_LIST> PMS_PROFESSIONAL_LIST { get; set; }

        /// <summary>
        /// 外派记录
        /// </summary>
        public virtual SqlSugarDbSet<PMS_EXPATRIATE_LIST> PMS_EXPATRIATE_LIST { get; set; }

        /// <summary>
        /// 人员文件信息
        /// </summary>
        public virtual SqlSugarDbSet<PMS_PERSON_FILE> PMS_PERSON_FILE { get; set; }

        /// <summary>
        /// 人事操作日志
        /// </summary>
        public virtual SqlSugarDbSet<PMS_CHANGE_LOG> PMS_CHANGE_LOG { get; set; }
        public virtual SqlSugarDbSet<PMS_PERSON_TAG_RELATE> PMS_PERSON_TAG_RELATE { get; set; }
        public virtual SqlSugarDbSet<OA_TRAIN_TYPE_DICT> OA_TRAIN_TYPE_DICT { get; set; }
        public virtual SqlSugarDbSet<PMS_PERSON_TAG_DICT> PMS_PERSON_TAG_DICT { get; set; }
        public virtual SqlSugarDbSet<OA_EVAL_STAGE_DICT> OA_EVAL_STAGE_DICT { get; set; }
        public virtual SqlSugarDbSet<PMS_PERSON_TAG> PMS_PERSON_TAG { get; set; }
        public virtual SqlSugarDbSet<SYS6_FUNC_FIELD_DICT> SYS6_FUNC_FIELD_DICT { get; set; }

        #endregion

        #region 外部表
        /// <summary>
        /// 系统菜单信息表
        /// </summary>
        public virtual SqlSugarDbSet<SYS6_MENU> SYS6_MENU { get; set; }
        /// <summary>
        /// 模块功能设置字典表
        /// </summary>
        public virtual SqlSugarDbSet<SYS6_MODULE_FUNC_DICT> SYS6_MODULE_FUNC_DICT { get; set; }


        ///// <summary>
        ///// 用户岗位表
        ///// </summary>
        //public virtual SqlSugarDbSet<SYS6_POST> SYS6_POST { get; set; }
        /// <summary>
        /// 岗位单元表
        /// </summary>
        public virtual SqlSugarDbSet<SYS6_POST_UNIT> SYS6_POST_UNIT { get; set; }
        ///// <summary>
        ///// 检验专业组信息表
        ///// </summary>
        //public virtual SqlSugarDbSet<SYS6_INSPECTION_PGROUP> SYS6_INSPECTION_PGROUP { get; set; }

        ///// <summary>
        ///// 管理专业组信息表
        ///// </summary>
        //public virtual SqlSugarDbSet<SYS6_INSPECTION_MGROUP> SYS6_INSPECTION_MGROUP { get; set; }

        ///// <summary>
        ///// 科室信息表
        ///// </summary>
        //public virtual SqlSugarDbSet<SYS6_INSPECTION_LAB> SYS6_INSPECTION_LAB { get; set; }

        /// <summary>
        /// 表字段字典表
        /// </summary>
        public virtual SqlSugarDbSet<SYS6_FEILD_DICT> SYS6_FEILD_DICT { get; set; }

        /// <summary>
        /// 固定基础数据分类表
        /// </summary>
        public virtual SqlSugarDbSet<SYS6_BASE_DATA> SYS6_BASE_DATA { get; set; }
        ///// <summary>
        ///// 岗位信息表
        ///// </summary>
        //public virtual SqlSugarDbSet<SYS6_POST> SYS6_POST { get; set; }
        /// <summary>
        /// 检测仪器信息表
        /// </summary>
        public virtual SqlSugarDbSet<LIS6_INSTRUMENT_INFO> LIS6_INSTRUMENT_INFO { get; set; }

        /// <summary>
        /// 位置信息表
        /// </summary>
        public virtual SqlSugarDbSet<SYS6_POSITION_DICT> SYS6_POSITION_DICT { get; set; }

        /// <summary>
        /// 分析项目信息表
        /// </summary>
        public virtual SqlSugarDbSet<LIS6_TEST_ITEM> LIS6_TEST_ITEM { get; set; }

        /// <summary>
        /// 诊疗项目信息表
        /// </summary>
        public virtual SqlSugarDbSet<LIS6_CHARGE_ITEM> LIS6_CHARGE_ITEM { get; set; }

        public virtual SqlSugarDbSet<SYS6_USER> SYS6_USER { get; set; }

        /// <summary>
        /// 系统设置
        /// </summary>
        public virtual SqlSugarDbSet<SYS6_SETUP_DICT> SYS6_SETUP_DICT { get; set; }
        ///// <summary>
        ///// 检验分组
        ///// </summary>
        //public virtual SqlSugarDbSet<LIS6_INSPECTION_GROUP> LIS6_INSPECTION_GROUP { get; set; }
        /// <summary>
        /// 检测仪器项目
        /// </summary>

        //public virtual SqlSugarDbSet<LIS6_INSTRUMENT_ITEM> LIS6_INSTRUMENT_ITEM { get; set; }

        //public virtual SqlSugarDbSet<QC6_GROUP_INFO> QC6_GROUP_INFO { get; set; }

        //public virtual SqlSugarDbSet<QC6_TEST_UNIT> QC6_TEST_UNIT { get; set; }



        /// <summary>
        /// (标本接收单元表)
        /// </summary>
        //public virtual SqlSugarDbSet<LIS6_INCEPT_UNIT_INFO> LIS6_INCEPT_UNIT_INFO { get; set; }
        /// <summary>
        ///   (采集单元字典表)
        /// </summary>
        //public virtual SqlSugarDbSet<LIS6_SAMPLEGATHER_UNIT> LIS6_SAMPLEGATHER_UNIT { get; set; }

        /// <summary>
        /// (模块角色信息表)
        /// </summary>
        public virtual SqlSugarDbSet<SYS6_ROLE> SYS6_ROLE { get; set; }
        /// <summary>
        /// (岗位权组表)
        /// </summary>
        public virtual SqlSugarDbSet<SYS6_POST_ROLE_COM> SYS6_POST_ROLE_COM { get; set; }


        /// <summary>
        /// （系统角色菜单表）
        /// </summary>

        public virtual SqlSugarDbSet<SYS6_ROLE_MENU> SYS6_ROLE_MENU { get; set; }
        /// <summary>
        /// (单元组合字典表)
        /// </summary>

        public virtual SqlSugarDbSet<SYS6_UNIT_COM_INFO> SYS6_UNIT_COM_INFO { get; set; }

        /// <summary>
        /// 权限组合字典表
        /// </summary>
        //public virtual SqlSugarDbSet<SYS6_ROLE_COM_INFO> SYS6_ROLE_COM_INFO { get; set; }


        public virtual SqlSugarDbSet<SYS6_ROLE_COM_LIST> SYS6_ROLE_COM_LIST { get; set; }
        public virtual SqlSugarDbSet<SYS6_HOSPITAL_INFO> SYS6_HOSPITAL_INFO { get; set; }


        public virtual SqlSugarDbSet<SMBL_LAB> SMBL_LAB { get; set; }

        /// <summary>
        /// 分类附加映射表
        /// </summary>
        public virtual SqlSugarDbSet<PMS_ADDN_CLASS_INFO> PMS_ADDN_CLASS_INFO { get; set; }

        /// <summary>
        /// 分类附加表
        /// </summary>
        public virtual SqlSugarDbSet<PMS_ADDN_RECORD> PMS_ADDN_RECORD { get; set; }

        /// <summary>
        /// 基础数据表
        /// </summary>
        public virtual SqlSugarDbSet<OA_BASE_DATA> OA_BASE_DATA { get; set; }

        /// <summary>
        /// 基础数据分类表
        /// </summary>
        public virtual SqlSugarDbSet<SYS6_BASE_DATA_CLASS> SYS6_BASE_DATA_CLASS { get; set; }


        /// <summary>
        /// 清单模板表
        /// </summary>
        public virtual SqlSugarDbSet<OA_EXCEL_STYLE_TEMPLATE> OA_EXCEL_STYLE_TEMPLATE { get; set; }

        public virtual SqlSugarDbSet<SMBL_ORG> SMBL_ORG { get; set; }

        public virtual SqlSugarDbSet<SYS6_SOFT_MODULE_INFO> SYS6_SOFT_MODULE_INFO { get; set; }
        //public virtual SqlSugarDbSet<OA_EVALUATE_PLAN_DICT> OA_EVALUATE_PLAN_DICT { get; set; }
        //public virtual SqlSugarDbSet<OA_EVALUATE_PLAN_SETUP> OA_EVALUATE_PLAN_SETUP { get; set; }
        //public virtual SqlSugarDbSet<OA_EVALUATE_PLAN_UNIT> OA_EVALUATE_PLAN_UNIT { get; set; }
        //public virtual SqlSugarDbSet<OA_EVALUATE_PLAN_USER> OA_EVALUATE_PLAN_USER { get; set; }

        public virtual SqlSugarDbSet<SYS6_LIMIT_ROLE_DICT> SYS6_LIMIT_ROLE_DICT { get; set; }
        public virtual SqlSugarDbSet<SYS6_LIMIT_ROLE_MENU> SYS6_LIMIT_ROLE_MENU { get; set; }
        public virtual SqlSugarDbSet<SYS6_USER_POSTROLE_LOG> SYS6_USER_POSTROLE_LOG { get; set; }
        public virtual SqlSugarDbSet<SYS6_USER_POWER_LOG> SYS6_USER_POWER_LOG { get; set; }
        public virtual SqlSugarDbSet<SYS6_SETUP> SYS6_SETUP { get; set; }
        public virtual SqlSugarDbSet<SYS6_USER_HISID> SYS6_USER_HISID { get; set; }
        #endregion

        #region 门禁管理
        public virtual SqlSugarDbSet<PMS_VISIT_REQUEST_LIST> PMS_VISIT_REQUEST_LIST { get; set; }
        public virtual SqlSugarDbSet<PMS_VISIT_OPER_LOG> PMS_VISIT_OPER_LOG { get; set; }
        public virtual SqlSugarDbSet<EMS_EQUIPMENT_INFO> PMS_VISIT_REQEMS_EQUIPMENT_INFOUEST_LIST { get; set; }
        public virtual SqlSugarDbSet<PMS_EGUARD_OUTIN_LIST> PMS_EGUARD_OUTIN_LIST { get; set; }
        public virtual SqlSugarDbSet<PMS_EGUARD_COM_DICT> PMS_EGUARD_COM_DICT { get; set; }
        public virtual SqlSugarDbSet<PMS_EGUARD_PASS_DICT> PMS_EGUARD_PASS_DICT { get; set; }
        public virtual SqlSugarDbSet<PMS_VISITOR_INFO> PMS_VISITOR_INFO { get; set; }
        public virtual SqlSugarDbSet<PMS_EGUARD_AUTH> PMS_EGUARD_AUTH { get; set; }
        public virtual SqlSugarDbSet<PMS_EGUARD_AUTH_COM> PMS_EGUARD_AUTH_COM { get; set; }
        public virtual SqlSugarDbSet<PMS_EGUARD_AUTH_POST> PMS_EGUARD_AUTH_POST { get; set; }
        public virtual SqlSugarDbSet<PMS_EGUARD_COM_LIST> PMS_EGUARD_COM_LIST { get; set; }
        public virtual SqlSugarDbSet<PMS_EGUARD_AUTH_USER> PMS_EGUARD_AUTH_USER { get; set; }
        #endregion
    }



    public class SugarDbContext_Master : SugarDbContext_Base
    {

    }

    public class SugarDbContext_Slave : SugarDbContext_Base
    {

    }

    public class SugarDbContext_Master2 : SugarDbContext_Base
    {

    }


    public class SugarDbContext_Slave2 : SugarDbContext_Base
    {

    }
}
