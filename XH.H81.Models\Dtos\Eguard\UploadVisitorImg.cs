﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Dtos.Eguard
{
    public class UploadVisitorImg
    {
        /// <summary>
        /// 人员ID
        /// </summary>
        public string PERSON_ID { get;set; }
        /// <summary>
        /// 文件
        /// </summary>
        public IFormFile FILE { get;set; }
        /// <summary>
        /// 文件后缀
        /// </summary>
        public string FILE_SUFFIX { get;set; }
    }
}
