﻿using AutoMapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XH.H81.Models.Entities.Pms;
using XH.LAB.UTILS.Models;

namespace XH.H81.Models.Dtos
{
    [AutoMap(typeof(PMS_PERSON_INFO), ReverseMap = true)]
    public class SimplePersonInfo
    {
        public string PERSON_ID { get; set; }
        public string? LAB_ID { get; set; }
        public string PGROUP_ID { get; set; }
        public string PGROUP_NAME { get; set; }
        public string HOSPITAL_ID { get; set; }
        public string AREA_ID { get; set; }
        public string AREA_NAME { get; set; }
        public string USER_ID { get; set; }
        public string USER_TYPE { get; set; }
        public string USER_TYPE_NAME { get; set; }
        public string USER_NAME { get; set; }
        public string USER_ENAME { get; set; }
        public string PERSON_DOC_STATE { get; set; }
        public string PERSON_DOC_STATE_NAME { get; set; }

        public string PERSON_STATE { get; set; }
        public string PERSON_STATE_NAME { get; set; }
        public string FIRST_RPERSON { get; set; }
        public string FIRST_RTIME { get; set; }
        public string LAST_MPERSON { get; set; }
        public string LAST_MTIME { get; set; }
        public string REMARK { get; set; }

        public string LAB_NAME { get; set; }

        public string HIS_ID { get; set; }
        /// <summary>
        /// 岗位ID
        /// </summary>
        public string POST_ID { get; set; }
    }
}
