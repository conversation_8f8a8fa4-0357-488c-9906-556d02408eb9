﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text;

namespace XH.H81.Models.Entities.Pms
{
    /// <summary>
    ///课题记录表
    /// </summary>
    [DBOwner("XH_OA")]
    public class PMS_RESEARCH_LIST
    {
        /// <summary>
        ///课题记录ID
        /// </summary>
        [Key]
        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string RESEARCH_ID { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? RECORD_ID { get; set; }
        /// <summary>
        ///医疗机构ID
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? HOSPITAL_ID { get; set; }
        /// <summary>
        ///人员ID
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? PERSON_ID { get; set; }
        /// <summary>
        ///人员名称
        /// </summary>
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? PERSON_NAME { get; set; }
        /// <summary>
        ///排序号
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? RESEARCH_SORT { get; set; }
        /// <summary>
        ///课题分类
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? RESEARCH_CLASS { get; set; }
        /// <summary>
        ///课题分类名称
        /// </summary>
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? RESEARCH_CLASS_NAME { get; set; }
        /// <summary>
        ///课题级别
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? RESEARCH_LEVEL { get; set; }
        /// <summary>
        ///课题级别名称
        /// </summary>
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? RESEARCH_LEVEL_NAME { get; set; }
        /// <summary>
        ///课题年份时间段
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? RESEARCH_YEAR { get; set; }
        /// <summary>
        ///课题名称
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? RESEARCH_NAME { get; set; }
        /// <summary>
        ///课题项目名称
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? RESEARCH_ITEM_NAME { get; set; }
        /// <summary>
        ///课题经费
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public int? RESEARCH_EXPENDITURE { get; set; } 
        /// <summary>
        ///附件
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? RESEARCH_AFFIX { get; set; }
        /// <summary>
        ///附件名称
        /// </summary>
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? AFFIX_NAME { get; set; }
        /// <summary>
        ///指定审核人员
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? SELECT_CHECK_PERSON { get; set; }
        /// <summary>
        ///指定审核人员名称
        /// </summary>
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? SELECT_CHECK_PERSON_NAME { get; set; }
        /// <summary>
        ///审核时间
        /// </summary>
        public DateTime? CHECK_TIME { get; set; }
        /// <summary>
        ///审核人员
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CHECK_PERSON { get; set; }
        /// <summary>
        ///审核人员名称
        /// </summary>
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CHECK_PERSON_NAME { get; set; }
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CHECK_STATE { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CHECK_STATE_NAME { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? NEW_STATE { get; set; }

        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? NEW_STATE_NAME { get; set; }
        /// <summary>
        ///状态
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? RESEARCH_STATE { get; set; }

        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? RESEARCH_STATE_NAME { get; set; }
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FIRST_RPERSON { get; set; }
        /// <summary>
        ///首次登记时间
        /// </summary>
        public DateTime? FIRST_RTIME { get; set; }
        /// <summary>
        ///最后修改人员
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? LAST_MPERSON { get; set; }
        /// <summary>
        ///最后修改时间
        /// </summary>
        public DateTime? LAST_MTIME { get; set; }
        /// <summary>
        ///备注
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? REMARK { get; set; }

        /// <summary>
        /// 字段和所属值
        /// </summary>
        public string RECORD_DATA { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public List<PMS_PERSON_FILE> PMS_PERSON_FILE { get; set; }
        /// <summary>
        ///排序
        /// </summary>
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? SORT_NUM { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? SUBMIT_STATE { get; set; }

        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? RESEARCH_TYPE { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? RESEARCH_TYPE_NAME { get; set; }
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? COMPETENT_DEPT { get; set; }
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? PART_TYPE { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? PART_TYPE_NAME { get; set; }
        public string? RESEARCH_MEMBER { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? REJECT_REASON { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? REJECT_DATE { get; set; }

        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? REJECT_PERSON { get; set; }


        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? PGROUP_NAME { get; set; }

        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? HIS_ID { get; set; }
        /// <summary>
        /// 生安标志
        /// </summary>
        public string SMBL_FLAG { get; set; } = "0";
    }
}
