﻿------------------------------------------------------------------------------------------------------------------------------------------------
----------------------------------以下为DDL语句（表结构修改语句）--------------------------------------------------------------------------------------------------------------
------------------------------------------------------------------------------------------------------------------------------------------------


/*  2025/6/3 #6.25.225 PMS_ASSESS_FORM_ITEM表增加字ITEM_ADD_JSON字段  wgn */
ALTER TABLE XH_OA.PMS_ASSESS_FORM_ITEM ADD ITEM_ADD_JSON CLOB ;
COMMENT ON COLUMN XH_OA.PMS_ASSESS_FORM_ITEM.ITEM_ADD_JSON IS '项目附加属性';



------------------------------------------------------------------------------------------------------------------------------------------------
---------------------------------以下为DML语句（数据处理语句）---------------------------------------------------------------------------------------------------------------
------------------------------------------------------------------------------------------------------------------------------------------------
/*  #6.25.225 增加现从事岗位、岗位类别分类 */
INSERT INTO XH_SYS.SYS6_BASE_DATA_CLASS (DATA_CLASS_ID, HOSPITAL_ID, CLASS_ID, CLASS_NAME, CLASS_SORT, IF_EDIT, CLASS_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK, SYSTEM_ID, ONE_CLASS, DATA_TABLE, DATA_FIELD, IF_COLOR_CLASS) VALUES('现从事岗位', 'H0000', '现从事岗位', '现从事岗位', '1', '0', '1', '初始化', TIMESTAMP '2024-05-09 14:45:24.000000', 'fr_李影', TIMESTAMP '2025-04-09 23:46:16.000000', '', 'OA', '实验室管理相关', 'OA_BASE_DATA', 'DATA_NAME', NULL);
INSERT INTO XH_SYS.SYS6_BASE_DATA_CLASS (DATA_CLASS_ID, HOSPITAL_ID, CLASS_ID, CLASS_NAME, CLASS_SORT, IF_EDIT, CLASS_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK, SYSTEM_ID, ONE_CLASS, DATA_TABLE, DATA_FIELD, IF_COLOR_CLASS) VALUES('岗位类别', 'H0000', '岗位类别', '岗位类别', '1', '0', '1', '初始化', TIMESTAMP '2024-05-09 14:45:24.000000', 'fr_李影', TIMESTAMP '2025-04-09 23:46:16.000000', '', 'OA', '实验室管理相关', 'OA_BASE_DATA', 'DATA_NAME', NULL);

/*  #6.25.225 增加颜色障碍数据 */
INSERT INTO XH_OA.OA_BASE_DATA (DATA_ID, HOSPITAL_ID, MODULE_ID, FATHER_ID, CLASS_ID, DATA_SORT, DATA_NAME, DATA_SNAME, DATA_ENAME, STANDART_ID, CUSTOM_CODE, SPELL_CODE, STATE_FLAG, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK, ADDN_CONFIG_JSON) VALUES('0', 'H0000', 'H81', NULL, 'H810000-COLOR_DEFICIENCY', '0', '正常', NULL, NULL, NULL, NULL, NULL, '1', '初始化', TIMESTAMP '2023-04-04 17:30:01.000000', NULL, NULL, NULL, NULL);
INSERT INTO XH_OA.OA_BASE_DATA (DATA_ID, HOSPITAL_ID, MODULE_ID, FATHER_ID, CLASS_ID, DATA_SORT, DATA_NAME, DATA_SNAME, DATA_ENAME, STANDART_ID, CUSTOM_CODE, SPELL_CODE, STATE_FLAG, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK, ADDN_CONFIG_JSON) VALUES('1', 'H0000', 'H81', NULL, 'H810000-COLOR_DEFICIENCY', '1', '色弱', NULL, NULL, NULL, NULL, NULL, '1', '初始化', TIMESTAMP '2023-04-04 17:30:01.000000', NULL, NULL, NULL, NULL);
INSERT INTO XH_OA.OA_BASE_DATA (DATA_ID, HOSPITAL_ID, MODULE_ID, FATHER_ID, CLASS_ID, DATA_SORT, DATA_NAME, DATA_SNAME, DATA_ENAME, STANDART_ID, CUSTOM_CODE, SPELL_CODE, STATE_FLAG, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK, ADDN_CONFIG_JSON) VALUES('2', 'H0000', 'H81', NULL, 'H810000-COLOR_DEFICIENCY', '2', '色盲', NULL, NULL, NULL, NULL, NULL, '1', '初始化', TIMESTAMP '2023-04-04 17:30:01.000000', NULL, NULL, NULL, NULL);

/*  #6.25.225 增加初始化标签数据 */
INSERT INTO XH_OA.PMS_PERSON_TAG_DICT (PERSON_TAG_ID, HOSPITAL_ID, TAG_CLASS, SOURCE_TYPE, SOURCE_ID, TAG_NAME, TAG_SNAME, TAG_COLOR, TAG_ICON, TAG_JSON, TAG_STATE, PARENT_ID, TAG_SORT, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('TG000001X', '33A001', 'XBD00001201', '1', 'XBD00001201', '实验室管理(ISO15189)', 'ISO', '#1677FF-#ffffff', NULL, NULL, '1', NULL, NULL, NULL, TIMESTAMP '2025-05-05 14:59:29.000000', '李影', TIMESTAMP '2025-06-06 15:00:36.000000', NULL);
INSERT INTO XH_OA.PMS_PERSON_TAG_DICT (PERSON_TAG_ID, HOSPITAL_ID, TAG_CLASS, SOURCE_TYPE, SOURCE_ID, TAG_NAME, TAG_SNAME, TAG_COLOR, TAG_ICON, TAG_JSON, TAG_STATE, PARENT_ID, TAG_SORT, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('TG000002X', '33A001', 'XBD00001202', '1', 'XBD00001202', 'POCT', 'P', '#0FC6C2-#ffffff', NULL, NULL, '1', NULL, '003', NULL, TIMESTAMP '2025-05-05 14:59:29.000000', '李影', TIMESTAMP '2025-05-28 09:00:21.000000', NULL);
INSERT INTO XH_OA.PMS_PERSON_TAG_DICT (PERSON_TAG_ID, HOSPITAL_ID, TAG_CLASS, SOURCE_TYPE, SOURCE_ID, TAG_NAME, TAG_SNAME, TAG_COLOR, TAG_ICON, TAG_JSON, TAG_STATE, PARENT_ID, TAG_SORT, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('TG000003X', '33A001', 'XBD00001203', '1', 'XBD00001203', '生安', NULL, NULL, 'iconshengan', NULL, '1', NULL, NULL, NULL, TIMESTAMP '2025-05-05 14:59:29.000000', NULL, TIMESTAMP '2025-05-05 14:59:29.000000', NULL);
INSERT INTO XH_OA.PMS_PERSON_TAG_DICT (PERSON_TAG_ID, HOSPITAL_ID, TAG_CLASS, SOURCE_TYPE, SOURCE_ID, TAG_NAME, TAG_SNAME, TAG_COLOR, TAG_ICON, TAG_JSON, TAG_STATE, PARENT_ID, TAG_SORT, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('TG000004X', '33A001', 'XBD00001204', '1', 'XBD00001204', '高等级', '高', '#000000-#F1F62E', NULL, NULL, '1', NULL, NULL, NULL, TIMESTAMP '2025-05-05 14:59:29.000000', '李影', TIMESTAMP '2025-05-29 14:02:30.000000', NULL);

/*  #6.25.300 增加采血外援入口及标签 */
INSERT INTO XH_OA.OA_BASE_DATA (DATA_ID, HOSPITAL_ID, MODULE_ID, FATHER_ID, CLASS_ID, DATA_SORT, DATA_NAME, DATA_SNAME, DATA_ENAME, STANDART_ID, CUSTOM_CODE, SPELL_CODE, STATE_FLAG, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK, ADDN_CONFIG_JSON) VALUES('XBD00001205', 'H0000', 'H81', NULL, '系统入口类型', 'XBD00001205', '采血外援', NULL, NULL, '5', NULL, NULL, '1', 'H81初始化', TIMESTAMP '2025-05-10 14:23:08.000000', 'H81初始化', TIMESTAMP '2025-05-10 14:23:08.000000', NULL, NULL);
INSERT INTO XH_OA.PMS_PERSON_TAG_DICT (PERSON_TAG_ID, HOSPITAL_ID, TAG_CLASS, SOURCE_TYPE, SOURCE_ID, TAG_NAME, TAG_SNAME, TAG_COLOR, TAG_ICON, TAG_JSON, TAG_STATE, PARENT_ID, TAG_SORT, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK, IF_COMMON) VALUES('TG000005X', '33A001', 'XBD00001205', '1', 'XBD00001205', '采血外援', '采', '#4B7902-#ffffff', NULL, NULL, '1', NULL, NULL, 'H81初始化', TIMESTAMP '2025-06-23 10:36:27.000000', 'H81初始化', TIMESTAMP '2025-06-23 10:36:27.000000', NULL, NULL);