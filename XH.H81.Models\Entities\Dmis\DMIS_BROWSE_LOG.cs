using System;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using SqlSugar;
using H.Utility.SqlSugarInfra;

namespace XH.H81.Models.Entities.Dmis
{
    [DBOwner("XH_OA")]
    public class DMIS_BROWSE_LOG
    {
        /// <summary>
        /// 日志ID
        /// </summary>
        [Key]
        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        [Column("LOG_ID")]
        [Required(ErrorMessage = "日志ID不允许为空")]

        [StringLength(20, ErrorMessage = "日志ID长度不能超出20字符")]
        [Unicode(false)]
        public string LOG_ID { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [Column("CREATE_TIME")]
        [Unicode(false)]
        public DateTime? CREATE_TIME { get; set; }

        /// <summary>
        /// 任务ID 0无任务
        /// </summary>
        [Column("JOB_ID")]
        [Required(ErrorMessage = "任务ID 0无任务不允许为空")]

        [StringLength(20, ErrorMessage = "任务ID 0无任务长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string JOB_ID { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        [Column("CREATE_PERSON")]
        [StringLength(50, ErrorMessage = "创建人长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CREATE_PERSON { get; set; }

        /// <summary>
        /// 浏览类型  1学习2浏览
        /// </summary>
        [Column("BROWSE_TYPE")]
        [Required(ErrorMessage = "浏览类型  1学习2浏览不允许为空")]

        [StringLength(20, ErrorMessage = "浏览类型  1学习2浏览长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string BROWSE_TYPE { get; set; }

        /// <summary>
        /// 浏览开始时间
        /// </summary>
        [Column("BROWSE_START_TIME")]
        [Unicode(false)]
        public DateTime? BROWSE_START_TIME { get; set; }

        /// <summary>
        /// 文档ID
        /// </summary>
        [Column("DOC_ID")]
        [Required(ErrorMessage = "文档ID不允许为空")]

        [StringLength(50, ErrorMessage = "文档ID长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string DOC_ID { get; set; }

        /// <summary>
        /// 浏览结束时间
        /// </summary>
        [Column("BROWSE_END_TIME")]
        [Unicode(false)]
        public DateTime? BROWSE_END_TIME { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        [Column("USER_ID")]
        [Required(ErrorMessage = "用户ID不允许为空")]

        [StringLength(20, ErrorMessage = "用户ID长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string USER_ID { get; set; }

        /// <summary>
        /// 浏览时长
        /// </summary>
        [Column("BROWSE_DURATION")]
        [Unicode(false)]
        public decimal? BROWSE_DURATION { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Column("REMARK")]
        [StringLength(200, ErrorMessage = "备注长度不能超出200字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? REMARK { get; set; }

        /// <summary>
        /// 浏览电脑
        /// </summary>
        [Column("BROWSE_COMPUTER")]
        [StringLength(50, ErrorMessage = "浏览电脑长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? BROWSE_COMPUTER { get; set; }

        /// <summary>
        /// 浏览进度
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? BROWSE_PROCESS { get; set; }

        /// <summary>
        /// 医疗机构ID
        /// </summary>
        [Column("HOSPITAL_ID")]
        [Required(ErrorMessage = "医疗机构ID不允许为空")]

        [StringLength(20, ErrorMessage = "医疗机构ID长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string HOSPITAL_ID { get; set; }
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string MENU_CLASS { get; set; }
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string CLASS_ID { get; set; }

        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]

        public string? USER_NAME { get; set; }

        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? PGROUP_ID { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? PGROUP_NAME { get; set; }
    }
}
