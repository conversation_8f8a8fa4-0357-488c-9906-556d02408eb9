﻿using AutoMapper;
using H.BASE;
using H.BASE.SqlSugarInfra.Uow;
using H.Utility.Helper;
using Serilog;
using XH.H81.IServices;
using XH.H81.Models;
using XH.H81.Models.Dtos.EvaluatePlan;
using XH.H81.Models.SugarDbContext;
using XH.LAB.UTILS.Helpers;
using XH.LAB.UTILS.Models.Entites;

namespace XH.H81.API.ScheduleJobs
{
    /// <summary>
    /// 定时同步用户信息到档案人员表
    /// </summary>
    public class HandleEplanSchedule : IHostedService, IDisposable
    {
        private readonly IConfiguration _configuration;
        private readonly ISqlSugarUow<SugarDbContext_Master> _soa;
        private readonly IMapper _mapper;
        private readonly IEvaluatePlanService _evaluatePlanServices;
        private Timer _timer;
        private List<string> _hospitalIds;
        static object _lockObject = new object();
        const string lockName = "XH:LOCK:PMS_HandleEplanSchedule";
        public HandleEplanSchedule(IConfiguration configuration, IEvaluatePlanService IEvaluatePlanService, ISqlSugarUow<SugarDbContext_Master> soa, IMapper mapper)
        {
            _configuration = configuration;
            _evaluatePlanServices = IEvaluatePlanService;
            _soa = soa;
            _mapper = mapper;
            ExecutingChangeSqlHelper.ExecutingChangeSql(_soa, configuration, true);
            CoverSqlLog();
        }
        public void Dispose()
        {
            _timer?.Dispose();
        }
        //屏蔽过多的同步日志并加锁
        private void CoverSqlLog()
        {
            //_soa.Db.Aop.OnDiffLogEvent = a => { };
            //_soa.Db.Aop.OnLogExecuting = (a, b) => { };
            _soa.Db.Aop.OnLogExecuted = (a, b) =>
            {
            };
        }

        public Task StartAsync(CancellationToken cancellationToken)
        {
            if (_configuration.GetValue<string>("IsOpenEplanLimitRoleTask") == "1"
                && AppSettingsProvider.SynergySign == "1") //仅连接的数据库为主协同库时才执行
            {
                if (_timer != null)
                {
                    _timer.Dispose();
                    _timer = null;
                }
                if (_timer == null)
                {
                    int asynicTime = 2;
                    Log.Information($"==>[处理规评变动事件定时任务]已开启,{asynicTime}分钟同步一次.");
                    
                    _timer = new Timer(DoWork, null, TimeSpan.Zero, TimeSpan.FromMinutes(asynicTime));
                }
            }
            return Task.CompletedTask;
        }

        public Task StopAsync(CancellationToken cancellationToken)
        {
            if (_timer != null)
            {
                _timer.Dispose();
                _timer = null;
            }
            return Task.CompletedTask;
        }

        private async void DoWork(object param)
        {
            lock (EntityHelper.LockObject)
            {
                try
                {
                    //_soa?.Db?.Open();
                    if (GetTheLock())
                    {
                        //创建每日事件
                        CreateDateChanged();
                        //处理变动事件
                        HandleChangedEvents();
                    }
                    else
                    {
                        GetTheLockFalse();
                    }
                }
                catch (Exception e)
                {
                    var redis = XhRedisHelper.UseS03();
                    var isDeleted = redis.KeyDelete(lockName);
                    if (isDeleted)
                    {
                        Console.WriteLine($"已有停止正在执行的定时任务，服务名：AutoSyncPersonData");
                    }
                    else
                    {
                        var serviceName = redis.StringGet(lockName);
                        var ttl = redis.KeyExpireTime(lockName);
                        Console.WriteLine($"已有服务正在执行，服务名：{serviceName},过期时间为{ttl?.ToString("yyyy-MM-dd HH:mm:ss")}。");
                    }
                    Console.WriteLine($"处理规评变动事件定时任务执行失败：{e.Message}");
                }
                finally
                {
                    //_soa?.Db?.Open();
                }
            }
        }

        private void CreateDateChanged()
        {
            DateTime today = DateTime.Now.Date;
            var newEvents = new List<OaEvaluatePlanEventParm>();
            if (!_soa.Db.Queryable<OA_EVALUATE_PLAN_EVENT>().Any(a => a.EVENT_TYPE == "3" && a.CHANGE_TIME >= today))
            {
                if (_hospitalIds == null)
                {
                    _hospitalIds = _soa.Db.Queryable<OA_EVALUATE_PLAN_SETUP>().Where(a => a.EPLAN_SSTATE == "1" && a.EPLAN_SHELF_LIFE != null && a.SHELF_LIFE_UTYPE != null && (a.LIMIT_PROLE_TYPE == "1" && a.PROLE_COM_SID != null || a.LIMIT_PROLE_TYPE == "2"))
                        .Select(a => a.HOSPITAL_ID).Distinct().ToList();
                }
                foreach (var hospitalId in _hospitalIds)
                {
                    var DateChangeEvent = new OaEvaluatePlanEventParm
                    {
                        HOSPITAL_ID = hospitalId,
                        EVENT_TYPE = "3",
                        CHANGE_TIME = today,
                        CHANGE_PERSON = "H81人员规评定时作业",
                        EVENT_INFO = "定时作业：按规评效期更新限权信息（每天一次）",
                    };
                    newEvents.Add(DateChangeEvent);
                }
            }
            _evaluatePlanServices.CreateOaEvaluatePlanEvent(newEvents);
        }

        private void HandleChangedEvents()
        {
            _evaluatePlanServices.HandleOaEvaluatePlanEvent();
        }

        private bool GetTheLock()
        {
            bool result = false;
            var time = TimeSpan.FromSeconds(20);

            try
            {
                var redis = XhRedisHelper.UseS03();

                result = redis.LockTake(lockName, $"{_configuration.GetSection("Kestrel:Endpoints:Https:Url").Value}", time);
            }
            catch
            {
                Log.Error("==>[处理规评变动事件定时任务]GetTheLock获取Redis服务失败！");
            }

            return result;
        }

        private void GetTheLockFalse()
        {
            try
            {
                var redis = XhRedisHelper.UseS03();
                var serviceName = redis.StringGet(lockName);
                var ttl = redis.KeyExpireTime(lockName);
                Console.WriteLine($"已有服务正在执行，服务名：{serviceName},过期时间为{ttl?.ToString("yyyy-MM-dd HH:mm:ss")}。");
            }
            catch { }
        }
    }
}
