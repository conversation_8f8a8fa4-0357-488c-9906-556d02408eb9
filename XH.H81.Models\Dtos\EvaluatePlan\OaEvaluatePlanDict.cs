﻿//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Text;
//using System.Threading.Tasks;

//namespace XH.H81.Models.Dtos.EvaluatePlan
//{
//    public class OaEvaluatePlanDict
//    {
//        public string EPLAN_ID { get; set; }

//        /// <summary>
//        /// 医疗机构ID
//        /// </summary>
//        public string HOSPITAL_ID { get; set; }

//        /// <summary>
//        /// 科室ID
//        /// </summary>
//        public string LAB_ID { get; set; }

//        /// <summary>
//        /// 专业组ID
//        /// </summary>
//        public string PGROUP_ID { get; set; }

//        /// <summary>
//        /// 科室/专业组ID
//        /// </summary>
//        public string LAB_PGROUP_ID { get; set; }

//        /// <summary>
//        /// 科室/专业组名称
//        /// </summary>
//        public string LAB_PGROUP_NAME { get; set; }

//        /// <summary>
//        /// 规评方案名称
//        /// </summary>
//        public string EPLAN_NAME { get; set; }

//        /// <summary>
//        /// 规评方案类型 (1-学习, 2-培训, 3-评估, 4-考试, 5-资质证书)
//        /// </summary>
//        public string EPLAN_TYPE { get; set; }

//        /// <summary>
//        /// 状态 (0-禁用, 1-在用, 2-删除)
//        /// </summary>
//        public string EPLAN_STATE { get; set; }

//        /// <summary>
//        /// 首次登记人
//        /// </summary>
//        public string FIRST_RPERSON { get; set; }

//        /// <summary>
//        /// 首次登记时间
//        /// </summary>
//        public DateTime? FIRST_RTIME { get; set; }

//        /// <summary>
//        /// 最后修改人员
//        /// </summary>
//        public string LAST_MPERSON { get; set; }
//        /// <summary>
//        /// 最后修改人员
//        /// </summary>
//        public string LAST_MPERSON_NAME { get; set; }

//        /// <summary>
//        /// 最后修改时间
//        /// </summary>
//        public DateTime? LAST_MTIME { get; set; }

//        /// <summary>
//        /// 备注
//        /// </summary>
//        public string REMARK { get; set; }
//    }
//}
