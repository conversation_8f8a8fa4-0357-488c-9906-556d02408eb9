﻿using Microsoft.AspNetCore.Http;

namespace XH.H81.Models.Dtos.External
{
    public class BloodCollectPersonInput
    {
        /// <summary>
        /// 机构ID
        /// </summary>
        public string HOSPITAL_ID { get; set; }

        /// <summary>
        /// 人员姓名
        /// </summary>
        public string USER_NAME { get; set; }

        /// <summary>
        /// 人员工号
        /// </summary>
        public string HIS_ID { get; set; }
        /// <summary>
        /// 科室ID
        /// </summary>
        public string? LAB_ID { get; set; }
        /// <summary>
        /// 院区ID
        /// </summary>
        public string? AREA_ID { get; set; }
        /// <summary>
        /// 民族
        /// </summary>
        public string? NATION { get; set; }
        /// <summary>
        /// 政治面貌
        /// </summary>
        public string? POLITICIAN { get; set; }
        /// <summary>
        /// 最高学历
        /// </summary>
        public string? HIGHEST_DEGREE { get; set; }
        /// <summary>
        /// 证件类型
        /// </summary>
        public string? CARD_TYPE { get; set; }
        /// <summary>
        /// 证件号码
        /// </summary>
        public string? ID_CARD { get; set; }
        /// <summary>
        /// 联系电话
        /// </summary>
        public string? PHONE { get; set; }
        /// <summary>
        /// 办公电话
        /// </summary>
        public string? OFFICE_PHONE { get; set; }
        /// <summary>
        /// 现住址
        /// </summary>
        public string? CURRENT_ADDRESS { get; set; }
        /// <summary>
        /// 参加工作日期?
        /// </summary>
        public DateTime? WORK_TIME { get; set; }

        /// <summary>
        ///病区????????????????????????????????????
        /// </summary>

        ///// <summary>
        ///// 采血外援人员状态：1-学习未完成  2-学习完成 3-线上考试通过 4-线下评估通过
        ///// </summary>
        //public string SUPPORTER_STATE { get; set; }
        public List<BloodCollectUploadFileOutput>? FILES { get; set; }
    }

    public class BloodCollectPersonOutput : BloodCollectPersonInput
    {
        /// <summary>
        /// 人员主键
        /// </summary>
        public string PERSON_ID { get; set; }
        /// <summary>
        /// 检验科室名称
        /// </summary>
        public string? LAB_NAME { get; set; }
        /// <summary>
        /// 院区名称
        /// </summary>
        public string? AREA_NAME { get; set; }
        /// <summary>
        /// 民族
        /// </summary>
        public string? NATION_NAME { get; set; }
        /// <summary>
        /// 政治面貌
        /// </summary>
        public string? POLITICIAN_NAME { get; set; }
        /// <summary>
        /// 最高学历
        /// </summary>
        public string? HIGHEST_DEGREE_NAME { get; set; }
        /// <summary>
        /// 证件类型
        /// </summary>
        public string? CARD_TYPE_NAME { get; set; }

        public List<BloodCollectUploadFileOutput>? FILES { get; set; }
        ///// <summary>
        ///// 采血外援人员状态：1-学习未完成  2-学习完成 3-线上考试通过 4-线下评估通过
        ///// </summary>
        //public string SUPPORTER_STATE { get; set; }
    }

    public class BloodCollectUploadFileInput
    {
        /// <summary>
        /// 机构ID
        /// </summary>
        public string HOSPITAL_ID { get; set; }
        /// <summary>
        /// 工号
        /// </summary>
        public string HIS_ID { get; set; }
        /// <summary>
        /// 姓名
        /// </summary>
        public string USER_NAME { get; set; }
        /// <summary>
        /// 1-执业资格证 2-职称证书 3-培训授权证
        /// </summary>
        public string CERTIFICATE_KIND { get; set; }
        /// <summary>
        /// 文件
        /// </summary>
        public IFormFile? FILE { get; set; }
    }

    public class BloodCollectUploadFileOutput
    {
        /// <summary>
        /// 文件ID
        /// </summary>
        public string FILE_ID { get; set; }
        /// <summary>
        /// 1-执业资格证 2-职称证书 3-培训授权证
        /// </summary>
        public string CERTIFICATE_KIND { get; set; }
        /// <summary>
        /// 1-执业资格证 2-职称证书 3-培训授权证
        /// </summary>
        public string? CERTIFICATE_KIND_NAME { get; set; }
        /// <summary>
        /// 文件名
        /// </summary>
        public string? FILE_NAME { get; set; }
        /// <summary>
        /// 文件前端处理类型
        /// </summary>
        public string? FILE_TYPE { get; set; }
        /// <summary>
        /// 上传路径
        /// </summary>
        public string? UPLOAD_PATH { get; set; }
        /// <summary>
        /// 是否已勾选
        /// </summary>
        public bool IS_CHECKED { get; set; }
    }


}
