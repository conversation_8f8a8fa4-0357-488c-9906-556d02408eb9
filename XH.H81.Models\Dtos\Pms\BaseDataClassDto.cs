﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Dtos.Pms
{
    public class BaseDataClassDto
    {
        /// <summary>
        /// 基础数据分类ID
        /// </summary>
        public string? DATA_CLASS_ID { get; set; }
        public string HOSPITAL_ID { get; set; }
        public string? CLASS_ID { get; set; }
        public string CLASS_NAME { get; set; }
        public string? CLASS_SORT { get; set; }
        public string? DATA_TABLE { get; set; }
        public string? CLASS_STATE { get; set; }
        public string? FIRST_RPERSON { get; set; }
        public DateTime? FIRST_RTIME { get; set; }
        public string? LAST_MPERSON { get; set; }
        public DateTime? LAST_MTIME { get; set; }
        public string? REMARK { get; set; }
        public bool? IS_EDITABLE { get; set; }

    }
}
