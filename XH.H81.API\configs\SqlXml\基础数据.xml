﻿<?xml version="1.0" encoding="utf-8" ?>
<root>
	<根据专业组获取分析项目>
		<OperType>Select</OperType>
		<MSSQL>
			<SQL>
				SELECT C.TEST_ITEM_ID,C.TEST_ITEM_CODE,C.TEST_ITEM_SORT,C.CHINESE_NAME,C.CHINESE_SHORT_NAME,
				C.ENGLISH_NAME,C.ENGLISH_SHORT_NAME,C.TEST_ITEM_UNIT,C.IF_VERIFY,D.DATA_CNAME
				FROM LIS6_TEST_ITEM C  LEFT JOIN LIS6_TEST_ITEM_LAB A ON C.TEST_ITEM_ID=A.TEST_ITEM_ID
				LEFT JOIN SYS6_INSPECTION_PGROUP B ON A.LAB_ID= B.LAB_ID
				LEFT JOIN SYS6_BASE_DATA D ON D.DATA_ID = C.IF_VERIFY AND D.CLASS_ID='结果验证规则'
				WHERE B.PGROUP_ID = @PGROUP_ID AND C.STATE_FLAG='1'
			</SQL>
		</MSSQL>
	</根据专业组获取分析项目>

	<检测数据库表信息>
		<OperType>Select</OperType>
		<MSSQL>
			<SQL>
				SELECT NAME TABLE_NAME FROM SYSOBJECTS WHERE XTYPE='U'
			</SQL>
		</MSSQL>
		<Oracle>
			<SQL>
				SELECT TABLE_NAME FROM USER_TABLES
			</SQL>
		</Oracle>
	</检测数据库表信息>

	<查询数据库表字段>
		<OperType>Select</OperType>
		<MSSQL>
			<SQL>
				SELECT T.TABLE_NAME,
				T.COLUMN_NAME,
				T.DATA_TYPE,
				T.DATA_LENGTH,
				T.NULLABLE,
				T1.COMMENTS,
				T.COLUMN_NAME CSHARPNAME,
				T2.CONSTRAINT_TYPE
				FROM USER_TAB_COLUMNS T
				JOIN USER_COL_COMMENTS T1
				ON T.TABLE_NAME =:TABLE_NAME
				AND T1.TABLE_NAME = :TABLE_NAME
				AND T.COLUMN_NAME = T1.COLUMN_NAME
				LEFT JOIN(SELECT COL.COLUMN_NAME, CON.CONSTRAINT_TYPE
				FROM USER_CONSTRAINTS CON
				JOIN USER_CONS_COLUMNS COL
				ON CON.CONSTRAINT_NAME = COL.CONSTRAINT_NAME
				AND CON.CONSTRAINT_TYPE = 'P'
				AND COL.TABLE_NAME =  :TABLE_NAME
				AND CON.TABLE_NAME = :TABLE_NAME) t2
				ON T.COLUMN_NAME = T2.COLUMN_NAME   ORDER BY T.COLUMN_ID
			</SQL>
		</MSSQL>
	</查询数据库表字段>

	<通过规则信息>
		<OperType>Select</OperType>
		<MSSQL>
			<SQL>
				SELECT A.RULE_ID, A.HOSPITAL_ID, A.PGROUP_ID, A.ITEM_ID, A.RULE_SORT, A.RULE_JSON, A.RULE_JS, A.RULE_SOURCE, A.CHECK_PERSON,
				A.CHECK_TIME, A.CHECK_STATE, A.RULE_STATE, A.FIRST_RPERSON, A.FIRST_RTIME, A.LAST_MPERSON, A.LAST_MTIME, A.REMARK,
				B.PGROUP_NAME,C.CHINESE_NAME,C.TEST_ITEM_CODE FROM LIS6_PASS_RULE_INFO A
				LEFT JOIN SYS6_INSPECTION_PGROUP B ON A.PGROUP_ID=B.PGROUP_ID
				LEFT JOIN LIS6_TEST_ITEM C ON C.TEST_ITEM_ID = A.ITEM_ID WHERE A.PGROUP_ID = @PGROUP_ID
			</SQL>
		</MSSQL>
	</通过规则信息>

	<根据规则ID获取通过规则信息>
		<OperType>Select</OperType>
		<MSSQL>
			<SQL>
				SELECT A.RULE_ID, A.HOSPITAL_ID, A.PGROUP_ID, A.ITEM_ID, A.RULE_SORT, A.RULE_JSON, A.RULE_JS, A.RULE_SOURCE, A.CHECK_PERSON,
				A.CHECK_TIME, A.CHECK_STATE, A.RULE_STATE, A.FIRST_RPERSON, A.FIRST_RTIME, A.LAST_MPERSON, A.LAST_MTIME, A.REMARK,
				B.PGROUP_NAME,C.CHINESE_NAME,C.TEST_ITEM_CODE FROM LIS6_PASS_RULE_INFO A
				LEFT JOIN SYS6_INSPECTION_PGROUP B ON A.PGROUP_ID=B.PGROUP_ID
				LEFT JOIN LIS6_TEST_ITEM C ON C.TEST_ITEM_ID = A.ITEM_ID WHERE A.RULE_ID = @RULE_ID
			</SQL>
		</MSSQL>
	</根据规则ID获取通过规则信息>

	<用户专业组>
		<OperType>Select</OperType>
		<MSSQL>
			<SQL>
				SELECT USER_NO,A.PGROUP_ID,C.PGROUP_NAME,A.LAB_ID,B.LAB_NAME FROM SYS6_USER_PGROUP  A LEFT JOIN SYS6_INSPECTION_LAB B
				ON A.LAB_ID = B.LAB_ID LEFT JOIN SYS6_INSPECTION_PGROUP C ON A.PGROUP_ID = C.PGROUP_ID
				WHERE USER_NO = @USER_NO
			</SQL>
		</MSSQL>
	</用户专业组>

	<用户角色菜单>
		<OperType>SQL</OperType>
		<SQL>
			SELECT A.RULE_ID, A.HOSPITAL_ID, A.PGROUP_ID, A.ITEM_ID, A.RULE_SORT, A.RULE_JSON, A.RULE_JS, A.RULE_SOURCE, A.CHECK_PERSON,
			A.CHECK_TIME, A.CHECK_STATE, A.RULE_STATE, A.FIRST_RPERSON, A.FIRST_RTIME, A.LAST_MPERSON, A.LAST_MTIME, A.REMARK,
			B.PGROUP_NAME,C.CHINESE_NAME,C.TEST_ITEM_CODE FROM LIS6_PASS_RULE_INFO A
			LEFT JOIN SYS6_INSPECTION_PGROUP B ON A.PGROUP_ID=B.PGROUP_ID
			LEFT JOIN LIS6_TEST_ITEM C ON C.TEST_ITEM_ID = A.ITEM_ID WHERE A.RULE_ID = @RULE_ID
		</SQL>
	</用户角色菜单>

	<获取表对应字段>
		<OperType>SelectWithCondation</OperType>
		<MSSQL>
			<SQL>
				SELECT CU.COLUMN_NAME,AU.CONSTRAINT_TYPE,CU.TABLE_NAME
				FROM ALL_CONS_COLUMNS CU, ALL_CONSTRAINTS AU
				WHERE CU.CONSTRAINT_NAME = AU.CONSTRAINT_NAME
				AND AU.CONSTRAINT_TYPE = 'P'
				AND CU.TABLE_NAME = @TABLE_NAME
				GROUP BY  CU.COLUMN_NAME,AU.CONSTRAINT_TYPE,CU.TABLE_NAME
			</SQL>
		</MSSQL>
	</获取表对应字段>

</root>