﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Dtos.IoTDevices.Platform
{
    public class XhXmlDelAccessPersonGrantDto : XhIotRoot
    {
        /// <summary>
        /// 人员编码
        /// </summary>
        public string code { get; set; }
        /// <summary>
        /// 删除类型：1全部删除，所有房间授权全部收回，2指定删除
        /// </summary>
        public int type { get; set; }
        public List<int> roomIds { get; set; }
    }
}
