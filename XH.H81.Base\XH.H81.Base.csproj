﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net6.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <Compile Remove="bin\**" />
        <Compile Remove="obj\**" />
        <EmbeddedResource Remove="bin\**" />
        <EmbeddedResource Remove="obj\**" />
        <None Remove="bin\**" />
        <None Remove="obj\**" />
    </ItemGroup>

    <ItemGroup>
        <Compile Remove="Helper\OfficeHelper.cs" />
        <Compile Remove="NonRoleAuthorizeAttribute.cs" />
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="Microsoft.Office.Interop.Excel" Version="15.0.4795.1001" />
      <PackageReference Include="Serilog.Sinks.FastConsole" Version="2.4.0" />
      <PackageReference Include="SkiaSharp.NativeAssets.Linux" Version="2.88.3" />
      <PackageReference Include="Swashbuckle.AspNetCore.Filters" Version="7.0.8" />
      <PackageReference Include="Swashbuckle.AspNetCore.Filters.Abstractions" Version="7.0.8" />
      <PackageReference Include="XH.LAB.UTILS" Version="6.25.301.23" />
    </ItemGroup>

</Project>
