﻿using MathNet.Numerics.LinearAlgebra.Factorization;
using Microsoft.AspNetCore.Components.Routing;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Primitives;
using Org.BouncyCastle.Asn1.Ocsp;
using Polly;
using RestSharp;
using Serilog;
using System.Net.Security;
using System.Security.Cryptography.X509Certificates;

namespace XH.H81.API.Middleware
{
    public class CustomFileResourceMiddleware
    {
        private readonly RequestDelegate _next;

        private readonly string _router;

        public CustomFileResourceMiddleware(RequestDelegate next, string router)
        {
            _next = next;
            _router = router;
        }


        public async Task InvokeAsync(HttpContext context, IConfiguration configuration, IHttpContextAccessor httpContextAccessor)
        {
            var targetUrl = configuration.GetSection("S54").Value;
            var token = httpContextAccessor.HttpContext.Request.Headers.Authorization.ToString();
            Log.Error(context.Request.Path);
            if (context.Request.Path.StartsWithSegments(_router))
            {

                await GetResources(_router, context, targetUrl, token);
                return;
            }
            await _next(context);
        }

        private async Task GetResources(string router, HttpContext context, string targetUrl, string token)
        {

            var url = context.Request.Path.Value.Split(router);

            if (url[1].Contains("//"))
            {
                url[1] = url[1].Replace("//", "/");
            }
            Log.Error($"{targetUrl}{router}{url[1]}");
            // 创建新的 RestRequest



            using RestClient client = new RestClient(new RestClientOptions
            {
                RemoteCertificateValidationCallback = (object sender, X509Certificate? certificate, X509Chain? chain, SslPolicyErrors sslPolicyErrors) => true,
                BaseUrl = new Uri(targetUrl),
                ThrowOnAnyError = true
            });
            RestRequest request = new RestRequest(url[1], Method.Get);
            request.AddHeader("Authorization", token);
            var restResponse = client.Get(request);

            context.Response.StatusCode = (int)restResponse.StatusCode;
            context.Response.ContentType = restResponse.ContentType;
            await context.Response.Body.WriteAsync(restResponse.RawBytes, 0, restResponse.RawBytes.Length);
        }


        private Method ConvertMethod(string method)
        {
            return method.ToUpper() switch
            {
                "GET" => Method.Get,
                "POST" => Method.Post,
                "PUT" => Method.Put,
                "DELETE" => Method.Delete,
                "PATCH" => Method.Patch,
                _ => throw new NotImplementedException($"HTTP method {method} is not supported.")
            };
        }

    }

}
