﻿using H.Utility;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Security;
using System.Security.Cryptography.X509Certificates;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using Serilog;
using XH.H81.Models.Dtos.IoTDevices;

namespace XH.H81.Services.IotDevice
{
    public class IoTDevicesClient
    {
        const string key = "a27ee8d252b59eeb";
        const string content = "sysczy001:sysczy!";
        //省人内网
        // const string key = "AECR824990001";
        // private const string content = "xhkj:Xhkj@123";
        private string address { get; set; } = "";
        private string token { get; set; } = "";
        private IHttpContextAccessor _httpContext;
        public IoTDevicesClient(string ip, IHttpContextAccessor httpContext)
        {
            Log.Information($"第三方url地址为：{ip}");
            if (ip.IsNullOrEmpty())
            {
                throw new ArgumentNullException("第三方url地址 为空");
            }
            address = ip;
            _httpContext = httpContext;
        }

        public T1 ClientGet<T, T1>(string url, T requestBody = default(T), bool isNeedToken = true)
        {

            using RestClient client = new RestClient(
                new RestClientOptions
                {
                    RemoteCertificateValidationCallback = (object sender, X509Certificate? certificate, X509Chain? chain, SslPolicyErrors sslPolicyErrors) => true,
                    BaseUrl = new Uri(address),
                    ThrowOnAnyError = true,
                });
            Log.Information($"开始请求第三方接口:{address}{url}");
            RestRequest request = new RestRequest(url);
            if (requestBody != null)
            {
                request.AddBody(requestBody);
            }
            if (isNeedToken)
            {
                request.AddHeader("Authorization", token);
            }
            try
            {
                Log.Information($"请求第三方接口为:{address}{url}");
                RestResponse<T1> restResponse = client.ExecuteGet<T1>(request);
                if (restResponse.IsSuccessful)
                {
                    Log.Information($"请求第三方接口{address}{url}成功:{JsonConvert.SerializeObject(restResponse.Data, Formatting.Indented)}");
                    return restResponse.Data;
                }
                Log.Error($"调用第三方接口[{address}{url}]失败:{restResponse.ErrorException}");
                throw new BizException($"调用第三方接口[{address}{url}]失败:{restResponse.ErrorException}");
            }
            catch (Exception ex)
            {
                Log.Error($"调用第三方接口[{address}{url}]错误:{ex.InnerException}");
                throw new BizException(ex.Message);
            }
        }

        public T1 ClientPost<T, T1>(string url, T requestBody = default(T), bool isNeedToken = true)
        {

            using RestClient client = new RestClient(new RestClientOptions
            {
                RemoteCertificateValidationCallback = (object sender, X509Certificate? certificate, X509Chain? chain, SslPolicyErrors sslPolicyErrors) => true,
                BaseUrl = new Uri(address),
                ThrowOnAnyError = true
            });
            Log.Information($"开始请求第三方接口:{address}{url}");
            RestRequest request = new RestRequest(url);
            if (requestBody != null)
            {
                Log.Information($"开始请求第三方入参:{JsonConvert.SerializeObject(requestBody, Formatting.Indented)}");
                request.AddBody(requestBody);
            }
            if (isNeedToken)
            {
                request.AddHeader("Authorization", token);
            }
            try
            {
                RestResponse<T1> restResponse = client.ExecutePost<T1>(request);
                if (restResponse.IsSuccessful)
                {
                    Log.Information($"请求第三方接口{address}{url}成功:{JsonConvert.SerializeObject(restResponse.Data, Formatting.Indented)}");
                    return restResponse.Data;
                }
                Log.Error($"调用第三方接口[{address}{url}]失败:{restResponse.ErrorException}");
                throw new BizException($"调用第三方接口[{address}{url}]失败:{restResponse.ErrorException}");
            }
            catch (Exception ex)
            {
                Log.Error($"调用第三方接口[{address}{url}]错误:{ex.InnerException}");
                throw new BizException(ex.Message);
            }
        }

        /// <summary>
        /// 加密
        /// </summary>
        /// <param name="sn">设备sn</param>
        /// <returns></returns>
        private void GetToken(string sn = "")
        {
            var aesKey = GetAesKey();
            var rsp = ClientPost<TokenInput, TokenResult>("/ocean-api/getAccessToken", new TokenInput(aesKey), false);
            token = rsp.token;
            Log.Information($"第三方接口token为:{token}");
        }

        /// <summary>
        /// AES加密
        /// </summary>
        /// <param name="plainText"></param>
        /// <param name="password"></param>
        /// <returns></returns>
        public static string Encrypt(string plainText, string password)
        {
            Log.Information($"加密前：{plainText}");

            var newKey = GetKey(password);
            try
            {
                // 将密码转换为字节数组
                // 使用密码生成AES密钥
                using (Aes aesAlg = Aes.Create())
                {
                    aesAlg.Key = newKey;
                    aesAlg.Mode = CipherMode.ECB;
                    aesAlg.Padding = PaddingMode.PKCS7;

                    ICryptoTransform encryptor = aesAlg.CreateEncryptor(aesAlg.Key, aesAlg.IV);

                    byte[] byteContent = Encoding.UTF8.GetBytes(content);

                    byte[] encryptedBytes = encryptor.TransformFinalBlock(byteContent, 0, byteContent.Length);

                    string finalKey = Convert.ToBase64String(encryptedBytes);
                    return finalKey;
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
                return null;
            }


        }

        /// <summary>
        /// 解密
        /// </summary>
        /// <param name="content">待解密内容</param>
        /// <param name="password">解密密钥</param>
        /// <returns>解密后的字符串</returns>
        public static string Decrypt(string content, string password)
        {
            try
            {
                byte[] secretKey = GetKey(password);
                using (Aes aesAlg = Aes.Create())
                {
                    aesAlg.Key = secretKey;
                    aesAlg.Mode = CipherMode.ECB;
                    aesAlg.Padding = PaddingMode.PKCS7;

                    ICryptoTransform decryptor = aesAlg.CreateDecryptor(aesAlg.Key, aesAlg.IV);

                    byte[] encryptedBytes = Convert.FromBase64String(content);

                    byte[] decryptedBytes = decryptor.TransformFinalBlock(encryptedBytes, 0, encryptedBytes.Length);

                    return Encoding.UTF8.GetString(decryptedBytes);
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
                return null;
            }
        }

        /// <summary>
        /// 获取最终key
        /// </summary>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public static string GetAesKey()
        {
            try
            {
                byte[] secretKey = GetKey(key);
                using (Aes aesAlg = Aes.Create())
                {
                    aesAlg.Key = secretKey;
                    aesAlg.Mode = CipherMode.ECB;
                    aesAlg.Padding = PaddingMode.PKCS7;

                    ICryptoTransform encryptor = aesAlg.CreateEncryptor(aesAlg.Key, aesAlg.IV);

                    byte[] byteContent = Encoding.UTF8.GetBytes(content);

                    byte[] encryptedBytes = encryptor.TransformFinalBlock(byteContent, 0, byteContent.Length);

                    string finalKey = Convert.ToBase64String(encryptedBytes);
                    return finalKey;
                }
            }
            catch (Exception e)
            {
                throw new Exception("获取最终密钥出现异常", e);
            }

        }
        /// <summary>
        /// 生成密钥
        /// </summary>
        /// <param name="strKey">密钥字符串</param>
        /// <returns>生成的密钥</returns>
        public static byte[] GetKey(string strKey)
        {
            try
            {
                using (var st = new SHA1CryptoServiceProvider())
                {
                    //return sha1.ComputeHash(seed);
                    using (var nd = new SHA1CryptoServiceProvider())
                    {
                        var rd = nd.ComputeHash(st.ComputeHash(Encoding.UTF8.GetBytes(strKey)));
                        byte[] keyArray = rd.Take(16).ToArray();
                        return keyArray;
                    }
                }
            }
            catch (Exception e)
            {
                throw new Exception("初始化密钥出现异常", e);
            }
        }
        /// <summary>
        /// 实时获取开关物联设备功率
        /// </summary>
        /// <param name="sn">设备sn码</param>
        /// <param name="model">设备型号</param>
        /// <returns></returns>
        public IoTDevicesResult GetIoTDeviceInfo(string? id, string? model)
        {
            var url = $"/ocean-api/api/bio/device/switch/statistic?model={model}";
            if (id.IsNotNullOrEmpty())
            {
                url = $"/ocean-api/api/bio/device/switch/statistic?id={id}";
            }
            GetToken();
            var result = ClientGet<object, IoTDevicesResult>(url, null, true);

            if (result.Code == 500)
            {
                Log.Information($"调用第三方接口[{address}{url}]成功，但是业务错误：{result.Data}");
                throw new BizException($"调用第三方接口[{address}{url}]成功，但是业务错误：{result.Data}");
            }
            return result;

            // return new IoTDevicesResult()
            // {
            //     Code = 0, Msg = "操作成功",
            //     Data = new IoTDevicesDto()
            //     {
            //         Id = "405953804392022016",
            //         Sn = "8CCE4E4E3C4F",
            //         Name = "生物安全柜_1",
            //         Model = "1379323011108",
            //         Type = 701,
            //         Ip = "**************",
            //         OpenStatus = 1,
            //         RoomId = 2000000,
            //         RoomName = "房间001",
            //         CheckpointId = null,
            //         CheckpointName = null,
            //         LabId = "1000001",
            //         LabName = null,
            //         IsOnline = 0,
            //         SwitchStatus = 0,
            //         Remark = null,
            //         CreateTime = DateTime.Parse("2025-01-04 14:40:03"),
            //         Voltage = 237.113,
            //         Current = 0.0,
            //         Power = 0.027,
            //         Energy = 2.986,
            //         PowerSyncTime = DateTime.Parse("2025-01-08 12:25:02")
            //
            //     }
            // };
        }


        /// <summary>
        /// 获取环境一体机的实时信息
        /// </summary>
        /// <param name="sn"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        /// <exception cref="BizException"></exception>
        public IoTDevicesResult<List<EnvironmentDevicesDto>> GetEnvironmentDevicesInfo(string? sn, string? model)
        {
            var url = $"/ocean-api/api/bio/env/list?model={model}";
            if (sn.IsNotNullOrEmpty())
            {
                url = $"/ocean-api/api/bio/env/list?sn={sn}";
            }
            GetToken();
            var result = ClientGet<object, IoTDevicesResult<List<EnvironmentDevicesDto>>>(url, null, true);
            if (result.Code == 500)
            {
                Log.Information($"调用第三方接口[{address}{url}]成功，但是业务错误：{result.Data}");
                throw new BizException($"调用第三方接口[{address}{url}]成功，但是业务错误：{result.Data}");
            }
            return result;
        }
    }
}
