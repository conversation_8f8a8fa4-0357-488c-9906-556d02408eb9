//using System;
//using Microsoft.EntityFrameworkCore;
//using System.ComponentModel.DataAnnotations;
//using System.ComponentModel.DataAnnotations.Schema;
//using SqlSugar;
//using H.Utility.SqlSugarInfra;

//namespace XH.H81.Models.ExternalEntity
//{
//    [Table("SYS6_MODULE_FUNC_DICT")]
//    [DBOwner("XH_SYS")]
//    public class SYS6_MODULE_FUNC_DICT
//	{
//		/// <summary>
//		/// PK
//		/// </summary>
//		[Key]
//        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
//        //[Column("SETUP_ID")]
//		[Required(ErrorMessage = "PK不允许为空")]

//		[StringLength(40, ErrorMessage = "PK长度不能超出40字符")]
//		//[Unicode(false)]
//        public string SETUP_ID { get; set; }

//		/// <summary>
//		/// PK
//		/// </summary>
//		[Key]
//        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
//        //[Column("FUNC_ID")]
//		[Required(ErrorMessage = "PK不允许为空")]

//		[StringLength(40, ErrorMessage = "PK长度不能超出40字符")]
//		//[Unicode(false)]
//        public string FUNC_ID { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		[Key]
//        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
//        //[Column("HOSPITAL_ID")]
//		[Required(ErrorMessage = "不允许为空")]

//		[StringLength(20, ErrorMessage = "HOSPITAL_ID长度不能超出20字符")]
//		//[Unicode(false)]
//        public string HOSPITAL_ID { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("FIRST_RPERSON")]
//		[StringLength(50, ErrorMessage = "FIRST_RPERSON长度不能超出50字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? FIRST_RPERSON { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("SKIN_ID")]
//		[StringLength(50, ErrorMessage = "SKIN_ID长度不能超出50字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? SKIN_ID { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("LAST_MTIME")]
//		//[Unicode(false)]
//		public DateTime? LAST_MTIME { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("IF_DOUBLE_CLICK")]
//		[StringLength(20, ErrorMessage = "IF_DOUBLE_CLICK长度不能超出20字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? IF_DOUBLE_CLICK { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("FORM_QUERY_JSON", TypeName = "CLOB")]
//		[StringLength(4000, ErrorMessage = "FORM_QUERY_JSON长度不能超出4000字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? FORM_QUERY_JSON { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("SETUP_CNAME")]
//		[StringLength(100, ErrorMessage = "SETUP_CNAME长度不能超出100字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? SETUP_CNAME { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("SETUP_SORT")]
//		[StringLength(20, ErrorMessage = "SETUP_SORT长度不能超出20字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? SETUP_SORT { get; set; }

//		/// <summary>
//		/// 0禁用1在用
//		/// </summary>
//		//[Column("SETUP_STATE")]
//		[StringLength(20, ErrorMessage = "0禁用1在用长度不能超出20字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? SETUP_STATE { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("SETUP_DESC")]
//		[StringLength(500, ErrorMessage = "SETUP_DESC长度不能超出500字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? SETUP_DESC { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("FORM_COL_JSON", TypeName = "CLOB")]
//		[StringLength(4000, ErrorMessage = "FORM_COL_JSON长度不能超出4000字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? FORM_COL_JSON { get; set; }

//		/// <summary>
//		/// PK
//		/// </summary>
//		//[Column("MODULE_ID")]
//		[Required(ErrorMessage = "PK不允许为空")]

//		[StringLength(20, ErrorMessage = "PK长度不能超出20字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string MODULE_ID { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("REMARK")]
//		[StringLength(200, ErrorMessage = "REMARK长度不能超出200字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? REMARK { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("FIRST_RTIME")]
//		//[Unicode(false)]
//		public DateTime? FIRST_RTIME { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("LAST_MPERSON")]
//		[StringLength(50, ErrorMessage = "LAST_MPERSON长度不能超出50字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? LAST_MPERSON { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("FORM_JSON", TypeName = "CLOB")]
//		[StringLength(4000, ErrorMessage = "FORM_JSON长度不能超出4000字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? FORM_JSON { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("IF_QUERY")]
//		[StringLength(10, ErrorMessage = "IF_QUERY长度不能超出10字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? IF_QUERY { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("SETUP_NAME")]
//		[StringLength(100, ErrorMessage = "SETUP_NAME长度不能超出100字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? SETUP_NAME { get; set; }

//		/// <summary>
//		/// 固定基础数据
//		/// </summary>
//		//[Column("SETUP_CLASS")]
//		[StringLength(50, ErrorMessage = "固定基础数据长度不能超出50字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? SETUP_CLASS { get; set; }


//	}
//}
