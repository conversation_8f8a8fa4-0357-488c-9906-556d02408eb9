﻿//using H.Utility.SqlSugarInfra;
//using SqlSugar;
//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Text;
//using System.Threading.Tasks;

//namespace XH.H81.Models.Entities.EvaluatePlan
//{
//    /// <summary>
//    /// 规评方案变动事件表
//    /// </summary>
//    [DBOwner("XH_OA")]
//    public class OA_EVALUATE_PLAN_EVENT
//    {
//        /// <summary>
//        /// 变动事件ID（主键）
//        /// </summary>
//        [SugarColumn(IsPrimaryKey = true)]
//        public string EVENT_ID { get; set; }

//        /// <summary>
//        /// 医疗机构ID
//        /// </summary>
//        public string HOSPITAL_ID { get; set; }

//        /// <summary>
//        /// 事件类型：1-方案修改 2-人员结果变动 3-定时作业 4-岗位角色变动 5-人员的岗位角色变动
//        /// </summary>
//        public string EVENT_TYPE { get; set; }

//        /// <summary>
//        /// 规评方案ID
//        /// </summary>
//        public string? EPLAN_ID { get; set; }

//        /// <summary>
//        /// 用户ID串
//        /// </summary>
//        public string? USER_SID { get; set; }

//        /// <summary>
//        /// 变更来源
//        /// </summary>
//        public string? SOURCE_ID { get; set; }

//        /// <summary>
//        /// 事件信息
//        /// </summary>
//        public string? EVENT_INFO { get; set; }

//        /// <summary>
//        /// 调整人员
//        /// </summary>
//        public string? CHANGE_PERSON { get; set; }

//        /// <summary>
//        /// 调整时间
//        /// </summary>
//        public DateTime? CHANGE_TIME { get; set; }

//        /// <summary>
//        /// 事件处理批号
//        /// </summary>
//        public string? EVENT_HANDLE_UID { get; set; }

//        /// <summary>
//        /// 事件处理时间
//        /// </summary>
//        public DateTime? EVENT_HANDLE_TIME { get; set; }

//        /// <summary>
//        /// 首次登记人
//        /// </summary>
//        public string? FIRST_RPERSON { get; set; }

//        /// <summary>
//        /// 首次登记时间
//        /// </summary>
//        public DateTime? FIRST_RTIME { get; set; }

//        /// <summary>
//        /// 最后修改人员
//        /// </summary>
//        public string? LAST_MPERSON { get; set; }

//        /// <summary>
//        /// 最后修改时间
//        /// </summary>
//        public DateTime? LAST_MTIME { get; set; }

//        /// <summary>
//        /// 备注
//        /// </summary>
//        public string? REMARK { get; set; }
//    }

//}
