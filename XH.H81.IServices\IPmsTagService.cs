﻿using H.Utility;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XH.H81.Models.Dtos;
using XH.H81.Models.Dtos.Pms;
using XH.H81.Models.Dtos.Tag;
using XH.H81.Models.Entities;
using XH.H81.Models.Entities.Pms;
using XH.LAB.UTILS.Models;

namespace XH.H81.IServices
{
    public interface IPmsTagService
    {



        /// <summary>
        /// 保存标签字典
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="userName"></param>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        ResultDto SaveTagDict(PmsPersonTagDictDto dto, string userName, string hospitalId);

        /// <summary>
        /// 查询标签字典
        /// </summary>
        /// <param name="tagClass"></param>
        /// <param name="tagName"></param>
        /// <param name="state"></param>
        /// <returns></returns>
        ResultDto GetTagDict(string tagClass, string tagName, string state,string hospitalId);

        /// <summary>
        /// 更新标签字典状态
        /// </summary>
        /// <param name="tagId"></param>
        /// <param name="state"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        ResultDto UpdateTagDictState(string tagId, string state, string userName);

        /// <summary>
        /// 获取待选标签人员信息
        /// </summary>
        /// <param name="tagId"></param>
        /// <param name="labPgroupId"></param>
        /// <param name="personName"></param>
        /// <returns></returns>
        ResultDto GetUnSelectPersonTag(string tagId, string labPgroupId, string personName,string hospitalId);

        /// <summary>
        /// 获取已选标签人员信息
        /// </summary>
        /// <param name="tagId"></param>
        /// <param name="labPgroupId"></param>
        /// <param name="personName"></param>
        /// <returns></returns>
        ResultDto GetSelectPersonTag(string tagId, string labPgroupId, string personName,string hospitalId);


        ResultDto GetPersonTag(string labPgroupId, string personName, string hospitalId);

        /// <summary>
        /// 按人员和标签批量操作标签
        /// </summary>
        /// <param name="tagIds"></param>
        /// <param name="personIds"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        ResultDto SavePersonTag(List<string> listTag, List<string> listPerson, string userName);

        /// <summary>
        /// 增加人员标签
        /// </summary>
        /// <param name="tagId">标签id</param>
        /// <param name="personIds">人员id</param>
        /// <param name="userName">人员名称</param>
        /// <returns></returns>
        ResultDto AddPersonTag(string tagIds, string personIds, string userName);

        /// <summary>
        /// 删除人员标签
        /// </summary>
        /// <param name="tagId">标签id</param>
        /// <param name="personIds">人员id</param>
        /// <returns></returns>
        ResultDto DeletePersonTag(string tagIds, string personIds, string userName);

        /// <summary>
        /// 获取人员所有标签的通用方法
        /// </summary>
        /// <param name="personIds"></param>
        /// <returns></returns>
        public Dictionary<string, List<PmsPersonTagDictDto>> GetPersonIdPersonTagDict(List<string> personIds, string systemType = "1", bool isReturnIsoTags = true);

        /// <summary>
        /// 获取标签分类
        /// </summary>
        /// <returns></returns>

        List<TagClassCommboxInfo> GetTagClass();

        /// <summary>
        /// 获取标签分类子集
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        List<CommboxInfo> GetTagClassChild(string id);

        ResultDto GetISOTagNameTree(OrgUserParams orgParm);

        ResultDto GetTagTree(bool? filterIso, string hospitalId);

        /// <summary>
        /// 获取当前“人员标签”已选中的“档案类型”列表
        /// </summary>
        /// <param name="personTagId"></param>
        /// <param name="keyword"></param>
        /// <returns></returns>
        public List<PmsClassInfoDto> GetPersonTagSelectedClasses(string personTagId, string keyword, string hisName);

        /// <summary>
        /// 获取当前“人员标签”未选中的“档案类型”列表
        /// </summary>
        /// <param name="personTagId"></param>
        /// <param name="keyword"></param>
        /// <returns></returns>
        public List<PmsClassInfoDto> GetPersonTagUnselectedClasses(string personTagId, string keyword, string hisName);

        /// <summary>
        /// 取消“档案类型”与当前“人员标签”的关联关系
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public string RemovePersonTagClassAssociation(string personTagId, List<string> classIdList, string hisName);

        /// <summary>
        /// 建立“档案类型”与当前“人员标签”的关联关系
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public string AddPersonTagClassAssociation(string personTagId, List<string> classIdList, string hisName);

        /// <summary>
        /// 获取维护数据项记录信息树
        /// </summary>
        /// <param name="hospital_id"></param>
        /// <returns></returns>
        public List<SysSetUpInfoDto> GetClassDataInfo(string hospital_id);

        /// <summary>
        /// 获取维护数据项基础数据及标签
        /// </summary>
        /// <param name="classId"></param>
        /// <returns></returns>
        public List<OaBaseDataAndTagDto> GetBaseDataAndTag(string classId, string stateFlag, string tagId, string keyWord);

        /// <summary>
        /// 批量获取维护数据项基础数据及标签
        /// </summary>
        /// <param name="menuName"></param>
        /// <returns></returns>
        public Dictionary<string, List<BaseDataAndTagDto>> BatchGetBaseDataAndTag(string menuName, string hospital_id);

        /// <summary>
        /// 保存维护数据项基础数据及标签
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public string SaveBaseDataAndTag(OaBaseDataAndTagDto dto);

        /// <summary>
        /// 删除维护数据项基础数据及标签
        /// </summary>
        /// <param name="DataId"></param>
        /// <returns></returns>
        public string DeleteBaseDataAndTag(OaBaseDataAndTagDto Data);

        /// <summary>
        /// 启用维护数据项基础数据及标签
        /// </summary>
        /// <param name="Data"></param>
        /// <returns></returns>
        public string EnableBaseDataAndTag(OaBaseDataAndTagDto Data);

        /// <summary>
        /// 禁用维护数据项基础数据及标签
        /// </summary>
        /// <param name="Data"></param>
        /// <returns></returns>
        public string DisableBaseDataAndTag(OaBaseDataAndTagDto Data);

        /// <summary>
        /// 维护数据项基础数据及标签排序
        /// </summary>
        /// <param name="Data"></param>
        /// <returns></returns>
        public string SortBaseDataAndTag(List<OaBaseDataAndTagDto> sortedData);

        /// <summary>
        /// 获取评估阶段字典
        /// </summary>
        /// <param name="stageType">评估阶段类型</param>
        /// <param name="stageClass">字典分类</param>
        /// <param name="tagId"></param>
        /// <param name="eplanFlag"></param>
        /// <param name="keyWord"></param>
        /// <param name="stateFlag"></param>
        /// <returns></returns>
        public List<OaEvalStageDictDto> GetEvalStageDict(string stageType, string stageClass, string tagId, string eplanFlag, string keyWord, string stateFlag);

        /// <summary>
        /// 新增或修改评估阶段配置
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public string SaveEvalStageDict(OaEvalStageDictDto dto);

        /// <summary>
        /// 删除评估阶段
        /// </summary>
        /// <param name="stageId"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public bool DeleteEvalStageDict(string stageId);

        /// <summary>
        /// 启用评估阶段
        /// </summary>
        /// <param name="stageId"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public bool EnableEvalStageDict(string stageId);

        /// <summary>
        /// 禁用评估阶段
        /// </summary>
        /// <param name="stageId"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public bool DisableEvalStageDict(string stageId);

        /// <summary>
        /// 评估阶段排序
        /// </summary>
        /// <param name="stageIds"></param>
        /// <returns></returns>
        public bool SortEvalStageDict(List<string> stageIds);

        /// <summary>
        /// 创建标签表单
        /// </summary>
        /// <param name="tagId"></param>
        /// <param name="userName"></param>
        /// <param name="hospitalId"></param>
        /// <returns></returns>

        ResultDto CreateComplexForms(string tagId, string userName, string hospitalId);
        /// <summary>
        /// 获取复杂表单
        /// </summary>
        /// <param name="setUpId"></param>
        /// <param name="merge"></param>
        /// <param name="quintId"></param>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        ResultDto GetComplexForms(string setUpId, string merge, string quintId, string hospitalId);

        /// <summary>
        /// 公共库字段保存
        /// </summary>
        /// <param name="dict"></param>
        /// <returns></returns>

        ResultDto SaveFieldDict(FieldDictDto dict);

        /// <summary>
        /// 获取公共库字段
        /// </summary>
        /// <param name="hospitalId"></param>
        /// <param name="moduleId"></param>
        /// <param name="filedClass"></param>
        /// <returns></returns>
        ResultDto GetGetFieldDicts(string hospitalId, string moduleId, string filedClass);


        public ResultDto InitBaseInfoFieldDict();
    }
}
