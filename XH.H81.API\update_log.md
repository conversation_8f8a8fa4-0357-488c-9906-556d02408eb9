## 6.25.300
`2025-06-25`
- 【新增】采血外援人员注册接口
- 【新增】旧岗位数据自动匹配单元的辅助功能
- 【修复】人员档案JCR分区展示内容问题
- 【修复】人事档案压缩包批量上传及附件展示兼容大写文件名
- 【优化】数据同步定时作业增加主协同库判断
- 【优化】人事档案其他类型树排除掉信息工程师与生物安全
- 【优化】人事档案教育背景学位为“学士”或“无”时学位类型不必填
- 【优化】人员权限增加用户级缓存（人岗位变更后用户需重新登录才更新，或1小时后自动更新，实验室.net各模块同样）

## 6.25.225
`2025-06-10`
- 【新增】实现人员标签相关需求
- 【新增】实现人员基本信息可配置
- 【新增】非固定字段数据项可配置
- 【新增】培训、评估、考试记录绑定证书
- 【优化】完善人员附件上传功能
- 【优化】全面实现多管理单元人岗权逻辑的改造
- 【新增】实现程序自动导入所有数据（含初始化数据及历史版本数据）

## 6.25.219
`2025-04-29`
- 配合系统数据最新人岗权逻辑的修改
- 人员结构分析增加了学位分布图形
- 优化了限权组合勾选菜单节点的逻辑
- 生安入口若干问题修复及优化
- 生安入口增加机构级及实验室级人员一览看板
- 人事档案支持通过高拍仪添加附件
- 增加人员模块表结构缺失自动检测功能

## 6.25.200
`2025-04-03`
- PATCH 1
- 增加规评管理相关功能
- 实现规评限权功能
- 实现生安相关需求

## 6.25.200
`2025-03-21`
- 全量版本

## 6.25.1
`2025-02-24`
- 增加人员规评登记、规评管理等菜单（执行脚本后，请记得配置模块角色）
- 人事档案提交的审核人员下拉列表仅显示具有“记录审核”权限的人员
- 人事档案预览的记录顺序改为与编辑模式顺序一致
- 时间属性展示格式修改为与编辑时一致
- 人员结构分析未维护信息列表不显示“系统数据未维护”人员
- 履历记录年限自动获取
- 分类设置放开对固定分类的禁用启用操作
- 记录审核增加全选按钮

## 6.24.10
`2025-1-3`
- 修复部分客户环境从杏通跳转白屏问题
- 修复证书记录有效日期录入限制
- 完善人事档案无职称时的显示（职称类型选无后，职称级别职称名称置灰）
- 优化人员基本信息退休日期的处理逻辑（按2025年前女性55岁退休计算，允许手工修改退休日期）
- 人员基本信息民族按固有顺序排序

## 6.24.9
`2024-12-6`
- 修复进修记录预览问题
- 修复学历分布重叠问题
- 增加档案记录详情预览弹窗
- 图片预览支持缩放及旋转保存
- 人员结构分析职称分布优化
- 人员结构分析专业组分布按管理管理员分组及优化
- 优化基础数据缓存机制(每30min自动刷新，分类设置基础数据修改后立即更新)
- 人事档案原生预览效果优化（增加表格线，提供横向及竖向模板）- 

## 6.24.8
`2024-11-21`
- 按新规范显示医院信息及图标
- 解决与系统数据字段一致性问题（原PERSON_TYPE改为USER_TYPE）
- 过滤检验专业组类型
- 增加“IsOpenPersonSync”从系统数据同步人员配置开关（默认开）
- 修复标识符过长问题

## 6.24.7
`2024-11-6`
- 增加特定系统基础数据同步到实验室管理库定时作业
- 个人头像文件上传支持后缀大写
- 修复工号从系统数据同步问题 
- 修复记录审核树人员选择逻辑的问题
- 自动计算基本信息退休日期、离院日期、离科日期
- 基本信息职称级别与职称名称实现级联录入

## 6.24.6
`2024-10-24`
- 人事档案默认预览模板增加列宽读取工具箱配置
- 人事档案word清单和默认预览模板增加附件超链接
- 人事清单模板选择增加下拉
- 人事档案增加下载打印
- 没有维护专业组的用户不同步至人事系统
- 新增基础数据类型和基础数据时按创建时间排序
- 维护基础数据时，支持拖动排序
- 人事档案培训记录学分不填时为空
- 人事档案课题记录的课题经费不填时为空
- 人事档案培训记录/外派记录是否完成不填时留空
- 修复人事档案表格排序与工具箱不一致的问题
- 专业组树加上SOURCE_PATH字段
- 完善人事档案无权限或无数据时展示处理逻辑
- 记录审核增加档案记录操作日志流水图（仅支持新数据）
- 允许修改固定字段的是否必填属性
- 基础数据支持数据源的切换

## 6.24.5
`2024-09-26`
- 人员结构分析页面重构
- 中心组织架构和人员结构分析页面增加鉴权处理
- 人事档案动态分类改为默认可编辑
- 人事档案树顶部增加“全部”节点
- 人事档案树支持按工号检索
- 分类设置增加存在有效数据时不能删除动态分类的限制
- 增加存在有效数据时不能删除动态属性及修改输入方式的限制
- 修复动态分类记录排序问题

## 6.24.4
`2024-09-09`
- 优化人员一览表、预览模板加载速度

## 6.24.3
`2024-08-15`
- 人员新增同步、结构分析、人员档案加载时以HIS_ID为依据去重复人员
- 人员同步机制改为全局每20秒同步一次，取消页面刷新触发同步
- 取消职称从系统数据同步
- 修复特殊系统环境组织架构图片加载失败 
- 修复杏通人事快速入口报错
- 去掉分类Json字段脏数据
- 屏蔽过多的同步日志
- 读取工具箱接口时过滤掉排序号字段
- 记录审核过滤掉非本人审核的记录
- 解决禁用分类不显示问题
- 社会任职、履历记录，进修记录，教育背景，年限改为可空
- 日期格式数据保存时，强制使用 yyyy-mm-dd 的格式
- 根据来院和来科日期计算院龄和科龄
- 技能证书记录去除发证日期和获取日期的限制
- 修复名称相同的头像上传后会覆盖其他人的同名头像问题
- 人事档案增加人员评估系统和考试管理系统记录的接入（可在设置页面隐藏）
- 人员结构分析增加各院区统计页签
- 人员结构分析修改顶部人数统计卡片样式
- 完善对多机构的支持

## 6.24.1
`2024-08-01`
- 基本信息排版调整（增加工号、毕业院校、毕业时间、颜色视觉障碍等）
- 人事档案分类导航增加有驳回（红）、未维护（橙）、过期（紫）的颜色区分
- 人事档案附件上传功能调整
- 档案审核性能优化
- 增加隐藏分类功能
- 增加分类属性输入方式、是否必填、是否计入人员清单列表统计等维护
- 增加基础数据分类及选择项目维护功能

## 1.0.13
`2024-07-11`
- 修复人事档案预览报错问题
- 新增人事档案清单功能(管理专业组、专业组加载execl模板数据，人员预览显示模板数据)
- 自定义分类属性同步到OA_FIELD_DICT
- 人事档案-来科日期改成必填
- 与P3实验室的数据进行隔离
- 增加保护避免缓存问题引起报错
- 跨域Nginx代理改为本域后端代理
<<<<<<< HEAD
=======
- 增加研究员基础数据
>>>>>>> 4cf24ed6d25b4dc1fd1049e4d3069b40553cabe3

## 1.0.12
`2024-06-26`
- 增加多库协调相关接口 
- 兼容高斯数据库的修改
- 增加自定义属性下拉选项时支持OA_BASE_DATA数据
- 不允许上传文件的人事记录分类隐藏上传控件
- 修复人事记录首个非序号自动填充数字问题
- 教育记录增加在校时长自动计算年份
- 登录选择科室时增加权限的判断
- 增加技术证书类型下拉类型（嘉兴妇保需求，有脚本）
- 修复进入人员档案编辑时白屏问题

## 1.0.11
`2024-04-26`
- 人事档案预览模式增加自定义控制显示列
- 调整履历记录等多类记录的结束时间录入逻辑
- 生成预览文档的水印自动设置透明度
- 修复预览模板进修时间没获取到数据问题
- 修复教育背景填写数据和预览数据不一致
- 人事档案基本信息的证件类型更改为必填
- 修复无法修改自定义分类的问题

## 1.0.10
`2024-03-27`
- 兼容SqlServer改造

## 1.0.9
`2024-03-21`
-  人员档案预览模式调整为动态生成，
-  人员档案下拉框调整
-  身份证自动计算年龄生日性别

## 1.0.8
`2024-03-13`
-  技术类型统一修改为职称类型，
-  增加双向修改同步： 当身份证号、电话、邮箱、技术类型(改成职称类型)、职称修改这几个字段在系统数据管理（工作账号）或者人事档案进行修改时，会自动同步到人事或系统数据管理
-  修复人员头像固定成修改当前登录用户头像的bug
-  人事档案预览上传文件修改为FormData方式
-  进修记录和访问交流记录增加结束日期类型
-  技能证书记录证书级别新增可编辑

## 1.0.7
`2024-03-07`
-  职称名称修改为按照职务联动切换取技师、医师或护士的职称字典

## 1.0.6
`2024-03-04`
-  提交增加身份验证，增加审核人写入
-  审核增加权限控制

## 1.0.5
`2024-02-28`
-  增加分类设置页面（进行添加、修改新的人事分类和属性的维护）
-  人事档案增加新分类及新属性的编辑与展示功能
-  完善了人事系统人员从基础数据同步的机制
-  修复人员结构分析人员总人数及专业组人数与基础数据不一致的问题
-  修复人事档案页面专业组下拉数据与人员树不一致的问题
-  其他修改和优化

## 1.0.4
`2024-02-19`
-  增加档案附件上传功能（自动把附件压缩包解压并按人员及档案类型拆分保存到个人的人事档案里）
-  上传附件有关组件封装到Nuget中

## 1.0.3
`2024-01-30`
-  上传文件修改为FormData方式
-  上传文件新增对Word、Excel文档的支持，并自动转成Pdf供预览
-  人事档案未完善的逻辑修改（基本信息的必填项+置灰项+履历记录缺失）
-  其他修改和优化

## 1.0.2
`2024-01-12`
-  修复人事档案和记录审核个别环境的报错问题

## 1.0.1
`2024-01-10`
-  解决在线文档上大部分问题
-  完善人员结构分析的统计逻辑（排除临床专业组、更改在职职工统计逻辑等）
-  修复记录审核页面PDF图标问题
-  修复PDF文件大写后缀上传问题
-  优化人员树及记录审核页面性能
-  实现数据权限控制（人事档案、记录审核页面数据权限控制精确到专业组）

## 1.0.0
`2023-08-08`
-  初始化框架