﻿//using H.Utility.SqlSugarInfra;
//using SqlSugar;
//using System;
//using System.Collections.Generic;
//using System.ComponentModel.DataAnnotations;
//using System.Linq;
//using System.Text;
//using System.Threading.Tasks;

//namespace XH.H81.Models.Entities.EvaluatePlan
//{
//    [DBOwner("XH_OA")]
//    public class OA_EVALUATE_PLAN_UNIT
//    {
//        [Key]
//        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
//        /// <summary>
//        /// 规评方案设置ID (主键)
//        /// </summary>
//        public string EPLAN_SID { get; set; }
//        [Key]
//        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
//        /// <summary>
//        /// 单元类型 (主键)
//        /// </summary>
//        public string UNIT_TYPE { get; set; }
//        [Key]
//        /// <summary>
//        /// 单元ID (主键)
//        /// </summary>
//        public string UNIT_ID { get; set; }

//        /// <summary>
//        /// 状态 (0-禁用, 1-在用, 2-删除)
//        /// </summary>
//        public string EPLAN_USTATE { get; set; }

//        /// <summary>
//        /// 首次登记人
//        /// </summary>
//        public string FIRST_RPERSON { get; set; }

//        /// <summary>
//        /// 首次登记时间
//        /// </summary>
//        public DateTime? FIRST_RTIME { get; set; }

//        /// <summary>
//        /// 最后修改人员
//        /// </summary>
//        public string LAST_MPERSON { get; set; }

//        /// <summary>
//        /// 最后修改时间
//        /// </summary>
//        public DateTime? LAST_MTIME { get; set; }

//        /// <summary>
//        /// 备注
//        /// </summary>
//        public string REMARK { get; set; }
//    }
//}
