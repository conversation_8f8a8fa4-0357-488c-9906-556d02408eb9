﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Entities.EvaluatePlan
{
    /// <summary>
    /// 证书字典表
    /// </summary>
    [DBOwner("XH_OA")]
    public class OA_CERTIFICATE_DICT
    {
        /// <summary>
        /// 证书字典ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string CERTIFICATE_DID { get; set; }

        /// <summary>
        /// 医疗机构ID
        /// </summary>
        public string HOSPITAL_ID { get; set; }

        /// <summary>
        /// 排序号
        /// </summary>
        public string CERTIFICATE_DSORT { get; set; }

        /// <summary>
        /// 证书归类
        /// 固定基础数据
        /// </summary>
        public string CERTIFICATE_DTYPE { get; set; }

        /// <summary>
        /// 证书级别
        /// </summary>
        public string CERTIFICATE_DLEVEL { get; set; }

        /// <summary>
        /// 规评标志
        /// </summary>
        public string EPLAN_FLAG { get; set; }

        /// <summary>
        /// 有效日期数据
        /// </summary>
        public string CER_LIFE_VALUE { get; set; }

        /// <summary>
        /// 有效日期单位：M-月 Q-季度 Y-年
        /// </summary>
        public string CER_LIFE_UNIT { get; set; }

        ///// <summary>
        ///// 失效日期，空值表示永久
        ///// </summary>
        //public DateTime? EXPIRE_DATE { get; set; }

        /// <summary>
        /// 状态，0-禁用, 1-在用, 2-删除
        /// </summary>
        public string CERTIFICATE_DSTATE { get; set; }

        /// <summary>
        /// 证书名称
        /// </summary>
        [SugarColumn(ColumnName = "FIRST_RPERSON")]
        public string CERTIFICATE_DNAME { get; set; }

        ///// <summary>
        ///// 首次登记人 【注意，记得dto也要恢复赋值！！！】搜索"字段未加，暂时不返回"
        ///// </summary>
        //public string? FIRST_RPERSON { get; set; }

        /// <summary>
        /// 首次登记时间
        /// </summary>
        public DateTime? FIRST_RTIME { get; set; }

        /// <summary>
        /// 最后修改人员
        /// </summary>
        public string LAST_MPERSON { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime? LAST_MTIME { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string REMARK { get; set; }
    }


}
