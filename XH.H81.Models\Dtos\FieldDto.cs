﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Dtos
{
    public class FieldDto
    {
        /// <summary>
        /// 模板id
        /// </summary>
        public string StyleId { get; set; }

        /// <summary>
        /// 文件
        /// </summary>
        public IFormFile File { get; set; }


        /// <summary>
        /// 页眉页脚数据项key:value JSON
        /// </summary>
        public string Fields { get; set; }
    }
}
