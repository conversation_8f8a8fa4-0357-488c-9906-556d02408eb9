﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Dtos.Dmis
{
    public class JobLogDocDto
    {
        public string? NO { get; set; }
        public string? LEARNING_STATE { get; set; }

        public string? DOC_TYPE { get; set; }
        public string? DOC_TYPE_NAME { get; set; }

        public string? DOC_PARENT_TYPE_NAME { get; set; }

        public string? DOC_ID { get; set; }
        public string? DOC_NAME { get; set; }

        public int? STUDY_DURATION { get; set; }

        public int? LEARNING_TIME { get; set; }

        public DateTime? BROWSE_START_TIME { get; set; }
        public DateTime? BROWSE_END_TIME { get; set; }

        public string? PGROUP_ID { get; set; }
        public string? PGROUP_NAME { get; set; }

        public string? CLASS_ID { get; set; }

        public string? CLASS_NAME { get; set; }
        public string? LEARNING_STATE_NAME { get; set; }

        public string? DOC_STATE { get; set; }

        /// <summary>
        /// 文件路径
        /// </summary>
        public string? FILE_PATH { get; set; }
    }
}
