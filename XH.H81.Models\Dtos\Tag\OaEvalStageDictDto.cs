﻿using AutoMapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XH.H81.Models.Dtos.EvaluatePlan;
using XH.H81.Models.Entities.Tag;

namespace XH.H81.Models.Dtos
{
    [AutoMap(typeof(OA_EVAL_STAGE_DICT), ReverseMap = true)]
    public class OaEvalStageDictDto
    {
        /// <summary>
        /// 评估阶段ID（主键）
        /// </summary>
        public string? EVAL_STAGE_ID { get; set; }

        /// <summary>
        /// 医疗机构ID
        /// </summary>
        public string HOSPITAL_ID { get; set; }

        /// <summary>
        /// 评估阶段类型(TRAIN-培训 ASSESS-评估 EXAM-考试)
        /// </summary>
        public string EVAL_STAGE_TYPE { get; set; }

        /// <summary>
        /// 评估阶段字典分类(OA_BASE_DATA的DATA_ID)
        /// </summary>
        public string? EVAL_STAGE_CLASS { get; set; }

        /// <summary>
        /// 评估阶段字典分类(OA_BASE_DATA的DATA_ID)
        /// </summary>
        public string? EVAL_STAGE_CLASS_NAME { get; set; }

        /// <summary>
        /// 评估阶段字典分类(OA_BASE_DATA的CLASS_ID)
        /// </summary>
        public string? EVAL_STAGE_CLASS_BASEDATACLASSID { get; set; }

        /// <summary>
        /// 评估阶段名称（页面上的培训类型、考试类型。。。）
        /// </summary>
        public string EVAL_STAGE_NAME { get; set; }

        /// <summary>
        /// 频次数值（允许空值）
        /// </summary>
        public decimal? FREQUENCY_VALUE { get; set; }

        /// <summary>
        /// 频次单位：OA_BASE_DATA.CLASS_ID = “评估阶段频次单位”
        /// </summary>
        public string? FREQUENCY_UNIT { get; set; }

        /// <summary>
        /// 频次单位
        /// </summary>
        public string? FREQUENCY_UNIT_NAME { get; set; }

        /// <summary>
        /// 规评标志
        /// </summary>
        public string? EPLAN_FLAG { get; set; }

        /// <summary>
        /// 证书是否必填（是/否）
        /// </summary>
        public string? CER_REQUIRE_FLAG { get; set; }

        /// <summary>
        /// 证书字典ID
        /// </summary>
        public string? CERTIFICATE_DID { get; set; }

        /// <summary>
        /// 排序号（字符型存储）
        /// </summary>
        public string? EVAL_STAGE_SORT { get; set; }

        /// <summary>
        /// 状态（启用/禁用）
        /// </summary>
        public string? EVAL_STAGE_STATE { get; set; }

        /// <summary>
        /// 首次登记人
        /// </summary>
        public string? FIRST_RPERSON { get; set; }

        /// <summary>
        /// 首次登记时间（允许空值）
        /// </summary>
        public DateTime? FIRST_RTIME { get; set; }

        /// <summary>
        /// 最后修改人员
        /// </summary>
        public string? LAST_MPERSON { get; set; }

        /// <summary>
        /// 最后修改时间（允许空值）
        /// </summary>
        public DateTime? LAST_MTIME { get; set; }

        /// <summary>
        /// 备注信息
        /// </summary>
        public string? REMARK { get; set; }
        /// <summary>
        /// 人员标签ID
        /// </summary>
        public List<string>? PERSON_TAG_IDS { get; set; }
        /// <summary>
        /// 人员标签名称
        /// </summary>
        public string? PERSON_TAG_NAMES { get; set; }
        /// <summary>
        /// 关联证书类型
        /// </summary>
        public OaCertificateDict? CERTIFICATE { get; set; }
    }
}

