﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.ExternalEntity
{
    /// <summary>
    /// 职称合集
    /// </summary>
    public class TechList
    {

        /// <summary>
        ///ID
        /// </summary>
        public string? TECH_ID { get; set; }
        /// <summary>
        ///职称级别
        /// </summary>
        public string? TECH_POST { get; set; }

        /// <summary>
        ///职称名称 对应系统数据管理USER表的TECH_POST字段
        /// </summary>
        public string? ACADEMIC_POST { get; set; }

        /// <summary>
        ///聘任职称评定单位
        /// </summary>
        public string? EMPLOYMENT_UNIT { get; set; }
        /// <summary>
        ///职称类型
        /// </summary>
        public string? TECHNOLOGY_TYPE { get; set; }

        /// <summary>
        ///职称专业
        /// </summary>
        public string? TECH_POST_PROFESSION { get; set; }
        /// <summary>
        ///专业技术资格取得时间
        /// </summary>
        public DateTime? TECH_CERTIFICE_TIME { get; set; }
    }
}
