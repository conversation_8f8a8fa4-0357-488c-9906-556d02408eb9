﻿using AutoMapper;
using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using RestSharp;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XH.H81.IServices;
using XH.H81.Models.Dtos.Dmis;
using XH.H81.Models.Entities.Dmis;
using XH.H81.Models.Entity.Dmis;
using XH.H81.Models.ExternalEntity;
using XH.H81.Models.SugarDbContext;
using XH.LAB.UTILS.Models;

namespace XH.H81.Services
{
    public class DmisService : IDmisService
    {
        private readonly IHttpContextAccessor _httpContext;
        private readonly IConfiguration _configuration;
        private readonly ISqlSugarUow<SugarDbContext_Master> _soa;
        private readonly IMapper _mapper;
        private readonly ILogger<ExamService> _logger;
        private readonly string FileHttpUrl = "/H81pdf/api";
        private readonly IBaseDataServices _IBaseDataServices;
        private readonly RestClient _clientH91;
        public DmisService(IConfiguration configuration, IHttpContextAccessor httpContext, ISqlSugarUow<SugarDbContext_Master> dbContext, IMapper mapper, ILogger<ExamService> logger, IBaseDataServices baseDataServices)
        {
            _configuration = configuration;
            _httpContext = httpContext;
            _soa = dbContext;
            _mapper = mapper;
            _logger = logger;
            _IBaseDataServices = baseDataServices;
            _clientH91 = new RestClient(new RestClientOptions
            {
                RemoteCertificateValidationCallback = (Sender, certificate, chain, SslPolicyErrors) => true,
                BaseUrl = new Uri(_configuration["H91"]),
                ThrowOnAnyError = true
            });
        }
        /// <summary>
        /// 获取已分学习任务文档信息
        /// </summary>
        /// <param name="firstmenukey"></param>
        /// <param name="class_id"></param>
        /// <param name="doc_type"></param>
        /// <param name="user_id"></param>
        /// <param name="start_time"></param>
        /// <param name="end_time"></param>
        /// <param name="doc_name"></param>
        /// <param name="learning_state"></param>
        /// <returns></returns>

        public List<JobLogDocDto> GetJobLogDocInfo(string hospital_id, string lab_id, string firstmenukey, string class_id, string doc_type, string user_id, string start_time, string end_time,
            string doc_name, string learning_state, string default_lab, string smbl_flag)
        {
            List<JobLogDocDto> jobLogDocDto = new List<JobLogDocDto>();
            List<DocJobLogDto> jobLogDocList = new List<DocJobLogDto>();
            var menu = _soa.Db.Queryable<SYS6_LAB_MENU>()
                    .LeftJoin<SYS6_MENU>((a, b) => a.MENU_ID == b.MENU_ID)
                    .Where((a, b) => a.LAB_ID == default_lab && a.HOSPITAL_ID == hospital_id && b.MENU_STATE == "9" && b.MENU_CLASS == "nav_menu")
                    .WhereIF(class_id.IsNotNullOrEmpty(), (a, b) => a.MENU_ID == class_id)
                    .Select((a, b) => b).ToList();
            var menuParent = _soa.Db.Queryable<SYS6_MENU>().Where(w => w.SYS_MENU == "H91" && w.MENU_STATE == "9" && w.MENU_CLASS == "nav_menu" && w.MENU_LEVEL == "1").ToList();
            string[] classArry = menu.Select(w => w.MENU_ID).ToArray();
            string[] browseJob = { };

            browseJob = _soa.Db.Queryable<DMIS_BROWSE_JOB>().Where(p => p.MENU_CLASS == firstmenukey && p.HOSPITAL_ID == hospital_id && p.USER_ID == user_id).Select(s => s.DOC_ID).ToArray();
            jobLogDocList = _soa.Db.Queryable<DMIS_SYS_DOC>()
                 .LeftJoin<SYS6_MENU>((a, b) => a.CLASS_ID == b.MENU_ID)
                 .LeftJoin<DMIS_BASE_DATA>((a, b, c) => a.DOC_TYPE == c.DATA_ID)
                 .LeftJoin<SYS6_INSPECTION_PGROUP>((a, b, c, d) => a.PGROUP_ID == d.PGROUP_ID)
                 .LeftJoin<SYS6_INSPECTION_MGROUP>((a, b, c, d, e) => a.PGROUP_ID == e.MGROUP_ID)
                 .LeftJoin<SYS6_INSPECTION_LAB>((a, b, c, d, e, f) => a.PGROUP_ID == f.LAB_ID)
                 .Where((a, b, c, d, e, f) => a.HOSPITAL_ID == hospital_id && a.LAB_ID == lab_id && classArry.Contains(a.CLASS_ID)
                  && a.STUDY_DURATION > 0 && (a.DOC_PROCCESS_STATE == "4" || a.DOC_PROCCESS_STATE == "6"))
                 .WhereIF(doc_type.IsNotNullOrEmpty(), (a, b, c, d, e, f) => a.DOC_TYPE == doc_type)
                 .WhereIF(doc_name.IsNotNullOrEmpty(), (a, b, c, d, e, f) => a.DOC_NAME.Contains(doc_name))
                 .WhereIF(browseJob.Length > 0, (a, b, c, d, e, f) => browseJob.Contains(a.DOC_ID))
                 .Select((a, b, c, d, e, f) => new DocJobLogDto
                 {
                     CLASS_ID = a.CLASS_ID,
                     PARENT_CODE = b.PARENT_CODE,
                     CLASS_NAME = b.MENU_NAME,
                     DOC_ID = a.DOC_ID,
                     DOC_NAME = a.DOC_NAME,
                     STUDY_DURATION = Convert.ToInt32(a.STUDY_DURATION),
                     DOC_TYPE = a.DOC_TYPE,
                     DOC_TYPE_NAME = c.DATA_CNAME,
                     PGROUP_ID = d.PGROUP_ID,
                     PGROUP_NAME = f.LAB_NAME != null ? f.LAB_NAME : d.PGROUP_NAME != null ? d.PGROUP_NAME : e.MGROUP_NAME != null ? e.MGROUP_NAME : "",
                     DOC_STATE = a.DOC_PROCCESS_STATE == "6" ? "已废止" : "已发布"
                 }).ToList();


            if (jobLogDocList.Count > 0)
            {
                //初始化查询时间
                int firstYear = Convert.ToInt32(start_time.Split('-')[0]);
                int firstMonth = Convert.ToInt32(start_time.Split('-')[1]);
                DateTime startDate = new DateTime(firstYear, firstMonth, 1);

                int lastYear = Convert.ToInt32(end_time.Split('-')[0]);
                int lastMonth = Convert.ToInt32(end_time.Split('-')[1]);
                DateTime endDate = new DateTime(lastYear, lastMonth, DateTime.DaysInMonth(lastYear, lastMonth)).AddDays(1);

                List<DMIS_BROWSE_LOG> dmis_browse_log = _soa.Db.Queryable<DMIS_BROWSE_LOG>().Where(b => b.USER_ID == user_id && b.BROWSE_START_TIME >= startDate
                && b.BROWSE_START_TIME <= endDate && jobLogDocList.Select(s => s.DOC_ID).Contains(b.DOC_ID)).ToList();
                List<DMIS_BROWSE_JOB> dmis_browse_job = _soa.Db.Queryable<DMIS_BROWSE_JOB>().Where(p => p.USER_ID == user_id).ToList();
                jobLogDocList = jobLogDocList.OrderBy(o => o.DOC_NAME).ToList();
                List<string> listDocId = jobLogDocList.Select(w => w.DOC_ID).ToList();
                List<DMIS_SYS_FILE> listFile = _soa.Db.Queryable<DMIS_SYS_FILE>().Where(p => listDocId.Contains(p.DOC_ID) && p.REVISION_STATE == "1" && p.FILE_STATE == "1" && p.FILE_ORIGIN != "2").ToList();
                foreach (var item in jobLogDocList)
                {
                    JobLogDocDto jobLogDoc = new JobLogDocDto();
                    item.PARENT_NAME = menuParent.Find(w => w.MENU_ID == item.PARENT_CODE)?.MENU_NAME;
                    DMIS_SYS_FILE file = listFile.Find(w => w.DOC_ID == item.DOC_ID);
                    if (file != null)
                    {
                        jobLogDoc.FILE_PATH = "/S54" + file.FILE_PATH + ".pdf";
                    }
                    int learning_time = Convert.ToInt32(dmis_browse_log.Where(p => p.DOC_ID == item.DOC_ID && p.USER_ID == user_id).Sum(e => e.BROWSE_DURATION) / 60);
                    DateTime? browse_start_time = null;
                    var browseStartRel = dmis_browse_log.Where(p => p.DOC_ID == item.DOC_ID && p.BROWSE_START_TIME != null && p.USER_ID == user_id).OrderBy(o => o.BROWSE_START_TIME).FirstOrDefault();
                    if (browseStartRel != null)
                    {
                        browse_start_time = browseStartRel.BROWSE_START_TIME;
                    }
                    DateTime? browse_end_time = null;
                    var browseEndRel = dmis_browse_log.Where(p => p.DOC_ID == item.DOC_ID && p.BROWSE_END_TIME != null && p.USER_ID == user_id).OrderByDescending(o => o.BROWSE_END_TIME).FirstOrDefault();
                    if (browseEndRel != null)
                    {
                        browse_end_time = browseEndRel.BROWSE_END_TIME;
                    }
                    string learningState = "0";
                    string learningStateName = "未完成";
                    if (dmis_browse_job.FirstOrDefault(p => p.DOC_ID == item.DOC_ID) == null)
                    {
                        learningState = "2";
                        learningStateName = "/";
                    }
                    else
                    {
                        if (learning_time >= item.STUDY_DURATION)
                        {
                            learningState = "1";
                            learningStateName = "已完成";
                        }
                    }
                    jobLogDoc.DOC_PARENT_TYPE_NAME = item.PARENT_NAME;
                    jobLogDoc.DOC_ID = item.DOC_ID;
                    jobLogDoc.DOC_NAME = item.DOC_NAME;
                    jobLogDoc.CLASS_ID = item.CLASS_ID;
                    jobLogDoc.CLASS_NAME = item.CLASS_NAME;
                    jobLogDoc.STUDY_DURATION = item.STUDY_DURATION;//任务时长
                    jobLogDoc.LEARNING_TIME = learning_time;//学习时长
                    jobLogDoc.BROWSE_END_TIME = browse_end_time;
                    jobLogDoc.DOC_STATE = item.DOC_STATE;
                    jobLogDoc.BROWSE_START_TIME = browse_start_time;
                    jobLogDoc.DOC_TYPE = item.DOC_TYPE;
                    jobLogDoc.DOC_TYPE_NAME = item.DOC_TYPE_NAME;
                    jobLogDoc.PGROUP_ID = item.PGROUP_ID;
                    jobLogDoc.PGROUP_NAME = item.PGROUP_NAME;
                    jobLogDoc.LEARNING_STATE = learningState;
                    jobLogDoc.LEARNING_STATE_NAME = learningStateName;
                    jobLogDocDto.Add(jobLogDoc);
                }
                jobLogDocDto = jobLogDocDto.WhereIF(learning_state.IsNotNullOrEmpty(), p => p.LEARNING_STATE == learning_state).OrderBy(o => Convert.ToInt32(o.LEARNING_STATE)).ToList();
            }
            return jobLogDocDto;
        }

        /// <summary>
        /// 获取文档类型下拉树
        /// </summary>
        /// <param name="labId"></param>
        /// <returns></returns>
        public ResultDto GetClassTypeDropDownInfo(string labId)
        { 
            ResultDto resultDto = new ResultDto();
            var url = $"/api/ModuleLabGroup/GetClassTypeDropDownInfo?lab_id={labId}";
            var accessToken = _httpContext.HttpContext.Request.Headers.Authorization;
            RestRequest request = new RestRequest(url);
            request.AddHeader("Authorization", accessToken);
            var res = _clientH91.ExecuteGet<ResultDto>(request);
            return res.Data;
        }


        /// <summary>
        /// 获取文档学习记录(按文档)
        /// </summary>
        /// <param name="lab_id"></param>
        /// <param name="firstmenukey"></param>
        /// <param name="class_id"></param>
        /// <param name="doc_type"></param>
        /// <param name="doc_name"></param>
        /// <param name="doc_state">0废止1发布</param>
        /// <param name="if_quest">0未勾选1勾选</param>
        /// <returns></returns>
        public ResultDto GetDocJobLogInfo(string lab_id, string firstmenukey, string? class_id, string? doc_type, string? doc_name, string? doc_state, string if_quest)
        {
            ResultDto resultDto = new ResultDto();
            var url = $"/api/Dmis/GetDocJobLogInfo?lab_id={lab_id}&&firstmenukey={firstmenukey}&&class_id={class_id}&&doc_type={doc_type}&&doc_name={doc_name}&&doc_state={doc_state}&&if_quest={if_quest}";
            var accessToken = _httpContext.HttpContext.Request.Headers.Authorization;
            RestRequest request = new RestRequest(url);
            request.AddHeader("Authorization", accessToken);
            var res = _clientH91.ExecuteGet<ResultDto>(request);
            return res.Data;
        }
        /// <summary>
        /// 获取已分学习任务人员信息(按人员)
        /// </summary>
        /// <param name="lab_id">科室id</param>
        /// <param name="pgroup_id">专业组id</param>
        /// <param name="user_name">人员名称</param>
        /// <param name="if_request"></param>
        /// <returns></returns>
        public ResultDto GetJobLogUserInfo(string lab_id, string? pgroup_id, string? user_name, string if_request)
        {
            ResultDto resultDto = new ResultDto();
            var url = $"/api/Dmis/GetJobLogUserInfo?lab_id={lab_id}&&pgroup_id={pgroup_id}&&user_name={user_name}&&if_quest={if_request}";
            var accessToken = _httpContext.HttpContext.Request.Headers.Authorization;
            RestRequest request = new RestRequest(url);
            request.AddHeader("Authorization", accessToken);
            var res = _clientH91.ExecuteGet<ResultDto>(request);
            return res.Data;
        }
        /// <summary>
        /// /获取文档对应人员学习记录
        /// </summary>
        /// <param name="lab_id"></param>
        /// <param name="firstmenukey"></param>
        /// <param name="doc_id"></param>
        /// <param name="start_time"></param>
        /// <param name="end_time"></param>
        /// <param name="pgroup_id"></param>
        /// <param name="user_name"></param>
        /// <param name="learning_state">0未完成1已完成</param>
        /// <param name="if_quest">0未勾选1勾选</param>
        /// <returns></returns>
        public ResultDto GetUserJobLogInfo(string lab_id, string firstmenukey, string? doc_id, string start_time, string end_time, string? pgroup_id, string? user_name, string? learning_state, string? if_quest)
        {
            ResultDto resultDto = new ResultDto();
            var url = $"/api/Dmis/GetUserJobLogInfo?lab_id={lab_id}&&firstmenukey={firstmenukey}&&doc_id={doc_id}&&start_time={start_time}&&end_time={end_time}&&pgroup_id={pgroup_id}&&user_name={user_name}&&learning_state={learning_state}&&if_quest={if_quest}";
            var accessToken = _httpContext.HttpContext.Request.Headers.Authorization;
            RestRequest request = new RestRequest(url);
            request.AddHeader("Authorization", accessToken);
            var res = _clientH91.ExecuteGet<ResultDto>(request);
            return res.Data;
        }
    }
}



