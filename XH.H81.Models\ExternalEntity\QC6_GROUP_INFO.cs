//using System;
//using Microsoft.EntityFrameworkCore;
//using System.ComponentModel.DataAnnotations;
//using System.ComponentModel.DataAnnotations.Schema;
//using SqlSugar;
//using H.Utility.SqlSugarInfra;

//namespace XH.H81.Models.ExternalEntity
//{
//    [Table("QC6_GROUP_INFO")]
//    [DBOwner("XH_SYS")]
//    public class QC6_GROUP_INFO
//    {
//        /// <summary>
//        /// 
//        /// </summary>
//        [Key]
//        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
//        //[Column("UNIT_ID")]
//        [Required(ErrorMessage = "不允许为空")]

//        [StringLength(20, ErrorMessage = "UNIT_ID长度不能超出20字符")]
//        //[Unicode(false)]
//        public string UNIT_ID { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>
//        //[Column("LAB_ID")]
//        [Required(ErrorMessage = "不允许为空")]

//        [StringLength(20, ErrorMessage = "LAB_ID长度不能超出20字符")]
//        //[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string LAB_ID { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>
//        //[Column("AREA_ID")]
//        [StringLength(20, ErrorMessage = "AREA_ID长度不能超出20字符")]
//        //[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? AREA_ID { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>
//        //[Column("UNIT_STATE")]
//        [StringLength(20, ErrorMessage = "UNIT_STATE长度不能超出20字符")]
//        //[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? UNIT_STATE { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>
//        //[Column("FIRST_RPERSON")]
//        [StringLength(50, ErrorMessage = "FIRST_RPERSON长度不能超出50字符")]
//        //[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? FIRST_RPERSON { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>
//        //[Column("LAST_MTIME")]
//        //[Unicode(false)]
//        public DateTime? LAST_MTIME { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>
//        //[Column("UNIT_PERSON")]
//        [StringLength(50, ErrorMessage = "UNIT_PERSON长度不能超出50字符")]
//        //[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? UNIT_PERSON { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>
//        //[Column("UNIT_TIMING")]
//        [StringLength(50, ErrorMessage = "UNIT_TIMING长度不能超出50字符")]
//        //[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? UNIT_TIMING { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>
//        //[Column("UNIT_POSITION")]
//        [StringLength(100, ErrorMessage = "UNIT_POSITION长度不能超出100字符")]
//        //[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? UNIT_POSITION { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>
//        //[Column("UNIT_SORT")]
//        [StringLength(20, ErrorMessage = "UNIT_SORT长度不能超出20字符")]
//        //[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? UNIT_SORT { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>
//        //[Column("UNIT_CLASS")]
//        [StringLength(20, ErrorMessage = "UNIT_CLASS长度不能超出20字符")]
//        //[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? UNIT_CLASS { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>
//        //[Column("DATA_SERVER_ID")]
//        [StringLength(20, ErrorMessage = "DATA_SERVER_ID长度不能超出20字符")]
//        //[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? DATA_SERVER_ID { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>
//        //[Column("REMARK")]
//        [StringLength(200, ErrorMessage = "REMARK长度不能超出200字符")]
//        //[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? REMARK { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>
//        //[Column("UNIT_NAME")]
//        [StringLength(100, ErrorMessage = "UNIT_NAME长度不能超出100字符")]
//        //[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? UNIT_NAME { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>
//        //[Column("UNIT_CODE")]
//        [StringLength(50, ErrorMessage = "UNIT_CODE长度不能超出50字符")]
//        //[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? UNIT_CODE { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>
//        //[Column("FIRST_RTIME")]
//        //[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public DateTime? FIRST_RTIME { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>
//        //[Column("LAST_MPERSON")]
//        [StringLength(50, ErrorMessage = "LAST_MPERSON长度不能超出50字符")]
//        //[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? LAST_MPERSON { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>
//        //[Column("PGROUP_ID")]
//        [Required(ErrorMessage = "不允许为空")]

//        [StringLength(20, ErrorMessage = "PGROUP_ID长度不能超出20字符")]
//        //[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string PGROUP_ID { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>
//        //[Column("HOSPITAL_ID")]
//        [Required(ErrorMessage = "不允许为空")]

//        [StringLength(20, ErrorMessage = "HOSPITAL_ID长度不能超出20字符")]
//        //[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string HOSPITAL_ID { get; set; }


//    }
//}
