﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Dtos.Eguard
{
    public class VisitorLogDto
    {
        public string VISIT_REQ_ID { get; set; }
        /// <summary>
        /// 访客名称
        /// </summary>
        public string? VISITOR_NAME { get; set; }
        /// <summary>
        /// 电话
        /// </summary>
        public string? PHONE_NO { get; set; }
        /// <summary>
        /// 工作单位
        /// </summary>
        public string? WORK_UNIT { get; set; }
        /// <summary>
        /// 科室人员
        /// </summary>
        public string? VISIT_PERSON_NAME { get; set; }
        /// <summary>
        /// 职务
        /// </summary>
        public string? DUTIES { get; set; }
        /// <summary>
        /// 专业组/分类
        /// </summary>
        public string? DEPT_CODE_NAME { get; set; }
        /// <summary>
        /// 备案实验室
        /// </summary>
        public string? SMBL_LAB_NAME { get; set; }
        /// <summary>
        /// 人员类型
        /// </summary>
        public string? PERSON_TYPE { get; set; }
        /// <summary>
        /// 申请类型ID
        /// </summary>
        public string? VISIT_REQ_TYPE_ID { get; set; }
        /// <summary>
        /// 申请类型
        /// </summary>
        public string? VISIT_REQ_TYPE { get; set; }
        /// <summary>
        /// 申请理由
        /// </summary>
        public string? VISIT_REQ_REASON { get; set; }
        /// <summary>
        /// 同行人员
        /// </summary>
        public string? FELLOW_PERSON { get; set; }
        /// <summary>
        /// 同行人数
        /// </summary>
        public int? FELLOW_PERSON_NUM { get; set; }
        /// <summary>
        /// 门禁地点id
        /// </summary>
        public string? VISIT_ROOM_ID { get; set; }
        /// <summary>
        /// 门禁地点
        /// </summary>
        public string? VISIT_ROOM_ADDR { get; set; }
        /// <summary>
        /// 门禁时间
        /// </summary>
        public string? VISIT_TIME_FRAME { get; set; }
    }
    public class VisitorListByVistor
    {
        /// <summary>
        /// 课外人员ID
        /// </summary>
        public string VISITOR_ID { get; set; }
        /// <summary>
        /// 访客姓名
        /// </summary>
        public string? VISITOR_NAME { get; set; }
        /// <summary>
        /// 拜访次数
        /// </summary>
        public int? VISIT_AMOUNT { get; set; }
        /// <summary>
        /// 电话
        /// </summary>
        public string? PHONE_NO { get; set; }
        /// <summary>
        /// 所属单位
        /// </summary>
        public string? WORK_UNIT { get; set; }
        public List<LabPersonListByVistor> LabPersonListByVistor { get; set; }
    }
    public class LabPersonListByVistor
    {
        public string VISIT_REQ_ID { get; set; }
        /// <summary>
        /// 科室人员
        /// </summary>
        public string? VISIT_PERSON_NAME { get; set; }
        
        /// <summary>
        /// 申请类型ID
        /// </summary>
        public string? VISIT_REQ_TYPE_ID { get; set; }
        /// <summary>
        /// 申请类型
        /// </summary>
        public string? VISIT_REQ_TYPE { get; set; }
        /// <summary>
        /// 申请理由
        /// </summary>
        public string? VISIT_REQ_REASON { get; set; }
        /// <summary>
        /// 同行人员
        /// </summary>
        public string? FELLOW_PERSON { get; set; }
        /// <summary>
        /// 同行人数
        /// </summary>
        public int? FELLOW_PERSON_NUM { get; set; }
        /// <summary>
        /// 门禁地点id
        /// </summary>
        public string? VISIT_ROOM_ID { get; set; }
        /// <summary>
        /// 门禁地点
        /// </summary>
        public string? VISIT_ROOM_ADDR { get; set; }
        /// <summary>
        /// 门禁时间
        /// </summary>
        public string? VISIT_TIME_FRAME { get; set; }
    }

    public class LabPersonListByLabPerson
    {
        public string VISIT_PERSON_ID { get; set; }
        public string? VISIT_PERSON_NAME { get; set; }
        public string? DUTIES { get; set; }
        public string? DEPT_CODE_NAME { get; set; }
        public string? DEPT_CODE { get; set; }
        public string? SMBL_LAB_NAME { get; set; }
        public int? VISITED_AMOUNT { get; set; }
        public string? PERSON_TYPE { get; set; }
        public List<VisitorListByLabPerson> VisitorListByLabPerson { get; set; }
    }
    public class VisitorListByLabPerson
    {
        public string VISIT_REQ_ID { get; set; }
        public string? VISITOR_NAME { get; set; }
        public string? PHONE_NO { get; set; }
        public string? WORK_UNIT { get; set; }
        /// <summary>
        /// 申请类型ID
        /// </summary>
        public string? VISIT_REQ_TYPE_ID { get; set; }
        /// <summary>
        /// 申请类型
        /// </summary>
        public string? VISIT_REQ_TYPE { get; set; }
        public string? VISIT_REQ_REASON { get; set; }
        public string? FELLOW_PERSON { get; set; }
        public int? FELLOW_PERSON_NUM { get; set; }
        public string? VISIT_ROOM_ADDR { get; set; }
        public string? VISIT_TIME_FRAME { get; set; }
    }

    public class VisitRoomListByRoom
    {
        public string VISIT_ROOM_ID { get; set; }
        public string? VISIT_ROOM_ADDR { get; set; }
        public int? VISITED_AMOUNT { get; set; }
        public List<VisitorListByRoom> VisitorListByRoom { get; set; }
    }
    public class VisitorListByRoom
    {
        public string VISIT_REQ_ID { get; set; }
        public string? VISITOR_NAME { get; set; }
        public string? PHONE_NO { get; set; }
        public string? WORK_UNIT { get; set; }
        public string? VISIT_PERSON_NAME { get; set; }
        public string? VISIT_REQ_TYPE { get; set; }
        public string? VISIT_REQ_REASON { get; set; }
        public string? FELLOW_PERSON { get; set; }
        public int? FELLOW_PERSON_NUM { get; set; }
        public string? VISIT_ROOM_ADDR { get; set; }
        public string? VISIT_TIME_FRAME { get; set; }
        public string? VISIT_REQ_TYPE_ID { get; set; }
        public string? VISIT_PERSON_ID { get; set; }
    }
}
