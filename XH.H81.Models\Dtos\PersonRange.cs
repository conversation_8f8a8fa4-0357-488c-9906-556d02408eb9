﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XH.H81.Models.Entities.Pms;
using XH.LAB.UTILS.Models;
using XH.LAB.UTILS.Models.Dto;
using XH.LAB.UTILS.Models.Entites;

namespace XH.H81.Models.Dtos
{
    public class PersonRange
    {
        public OrgTree Tree { get; set; }
        public List<PMS_PERSON_INFO> PersonList { get; set; }
        /// <summary>
        /// 人员分类
        /// </summary>
        public List<SYS6_USER_CLASS_DICT> UserClassList { get; set; }
        public List<string> PgroupIds { get; set; }
        //public List<SYS6_USER> UserList { get; set; }
        //public List<SYS6_INSPECTION_PGROUP> PgroupList { get; set; }
    }
}
