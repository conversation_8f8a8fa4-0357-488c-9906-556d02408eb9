﻿//using Microsoft.AspNetCore.Authorization;
//using Microsoft.AspNetCore.Mvc;
//using XH.H81.IServices;
//using XH.H81.Services.Pms;

//namespace XH.H98.API.Controllers
//{
//    [Route("api/[controller]/[action]")]
//    [ApiController]
//    [Authorize]
//    [ApiExplorerSettings(GroupName = "XH.H98.API")]
//    public class InfraController : ControllerBase
//    {
//        private readonly ILogger<PmsService> _logger;
//        private readonly IConfiguration _configuration;
//        private readonly ISystemService _systemService;

//        public InfraController(Microsoft.AspNetCore.Hosting.IHostingEnvironment hostingEnvironment, IConfiguration configuration, ILogger<PmsService> logger, ISystemService systemService)
//        {
//            _logger = logger;
//            _configuration = configuration;
//            _systemService = systemService;
//        }


//        /// <summary>
//        /// 获取机构信息
//        /// </summary>
//        /// <returns></returns>
//        [HttpGet]
//        public IActionResult GetHospitalInfo(string hospitalId)
//        {
//            return Ok(_systemService.GetHospitalInfo(hospitalId));
//        }
//    }
//}
