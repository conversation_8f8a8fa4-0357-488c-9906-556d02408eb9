﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.ExternalEntity
{
    [DBOwner("XH_SYS")]
    public class LIS_REQUISITION_INFO
    {
        [Key]
        [SugarColumn(IsPrimaryKey = true)]
        public string requisition_id { get; set; }
        public string patient_name { get; set; }

    }
}
