﻿//using H.Utility;
//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Text;
//using System.Threading.Tasks;
//using XH.H81.Models.Configs;
//using XH.H81.Models.Dtos;

//namespace XH.H81.IServices
//{
//    public interface IUploadFileService
//    {
//        //public static string FILE_PREVIEW = "/H98file/access";
//        public static string S28_UPLOAD_PATH = "PMS_Record_File";
//        public static string LOCAL_UPLOAD_PATH = "PMS_Record_File_Local";

//        ///// <summary>
//        ///// 采用base64方式上传文件（执行上传，WORD、EXCEL文件会额外生成PDF并上传供预览使用）
//        ///// </summary>
//        ///// <param name="fileName"></param>
//        ///// <param name="moduleId"></param>
//        ///// <param name="base64String"></param>
//        ///// <param name="file_suffix"></param>
//        ///// <returns></returns>
//        //ResultDto UploadFileOperate(string fileName, string guidefile, string base64String, string file_suffix);

//        ///// <summary>
//        ///// 使用FormData方式上传文件（执行上传，WORD、EXCEL文件会额外生成PDF并上传供预览使用）
//        ///// </summary>
//        ///// <param name="fileName"></param>
//        ///// <param name="moduleId"></param>
//        ///// <param name="base64String"></param>
//        ///// <param name="file_suffix"></param>
//        ///// <returns></returns>
//        //ResultDto UploadFormDataFileOperate(UploadFileDto file);

//        ///// <summary>
//        ///// 上传文件处理
//        ///// </summary>
//        ///// <param name="dto"></param>
//        ///// <returns></returns>
//        //ResultDto UploadFormDataFile(UploadFileDto file);

//        /// <summary>
//        /// 上传文件并生成预览文件（file.FILE或file.UPLOAD_BYTES不为空时，优先使用FormData二进制方式上传。否则如果file.FILE_BASE64有值，则以base64方式上传）
//        /// </summary>
//        /// <param name="fileName"></param>
//        /// <param name="moduleId"></param>
//        /// <param name="base64String"></param>
//        /// <param name="file_suffix"></param>
//        /// <returns></returns>
//        ResultDto UploadFileOperate(UploadFileDto file);

//        /// <summary>
//        /// 删除文件，如果有生成预览文件也自动删除
//        /// </summary>
//        /// <param name="pathAndFileName"></param>
//        /// <returns></returns>
//        ResultDto DeleteFileOperate(string pathAndFileName);

//        /// <summary>
//        /// 使用FormData方式上传文件到服务器（此方法有上传S28和服务器本地保存两种途径）
//        /// </summary>
//        /// <param name="dto"></param>
//        /// <returns></returns>
//        ResultDto UploadFileToServer(UploadFileDto file);

//        /// <summary>
//        /// 人事档案附件压缩包自动识别并上传（先注册dispatcher，再调用本方法，注册方式参考下面RegisterDispatcherSample方法）
//        /// </summary>
//        /// <param name="dispatcher">执行逻辑编排类（FileFunctionDispatcher）</param>
//        /// <param name="zipFile">人事档案附件压缩包对象</param>
//        /// <param name="dict">执行处理过程中需要外部传入的参数字典</param>
//        /// <returns></returns>
//        ResultDto AutoDispatchUploadFile(FileFunctionDispatcher dispatcher, UploadZipDto zipFile, Dictionary<string, object> dict);

//        /// <summary>
//        /// 编排上传文档的执行逻辑_示例1（一个路径节点对应一个方法）
//        /// 执行规则：按路径
//        /// </summary>
//        /// <returns></returns>
//        private FileFunctionDispatcher RegisterDispatcherSample_1()
//        {
//            //先按需定义执行方法
//            Func<FileDispatcherContext, FileDispatcherContext> FunctionA = a => a;
//            Func<FileDispatcherContext, FileDispatcherContext> FunctionB = a => a;
//            Func<FileDispatcherContext, FileDispatcherContext> FunctionC = a => a;

//            //然后进行编排
//            var root = FileFunctionDispatcher.GetRoot();
//            root.Then(FunctionA).Then(FunctionB).Then(FunctionC);
//            return root;
//        }

//        /// <summary>
//        /// 编排上传文档的执行逻辑_示例2（一个路径节点不止一个方法，所以有分支的执行逻辑）
//        /// 执行规则：先按顺序依次执行同一级别节点的Func方法，当Success为True时，不再继续执行本级节点，立即进入该节点的子节点（下一层级）再按节点顺序执行Func方法，如此递归。
//        /// 停止执行的两种情况：1、某层级里，依次执行完各节点的执行结果都不成功（Success == False）  2、执行到末端层级，并执行成功
//        /// </summary>
//        /// <param name="dict">用于外部传入必须信息的字典</param>
//        /// <returns></returns>
//        private FileFunctionDispatcher RegisterDispatcherSample_2()
//        {
//            //先按需定义执行方法
//            Func<FileDispatcherContext, FileDispatcherContext> FunctionA1 = a => a;
//            Func<FileDispatcherContext, FileDispatcherContext> FunctionA2 = a => a;
//            Func<FileDispatcherContext, FileDispatcherContext> FunctionB1 = a => a;
//            Func<FileDispatcherContext, FileDispatcherContext> FunctionB2 = a => a;
//            Func<FileDispatcherContext, FileDispatcherContext> FunctionB3 = a => a;
//            Func<FileDispatcherContext, FileDispatcherContext> FunctionC1 = a => a;
//            Func<FileDispatcherContext, FileDispatcherContext> FunctionC2 = a => a;

//            //然后进行编排
//            //根节点
//            var root = FileFunctionDispatcher.GetRoot();
//            root.Then
//            (
//                    //一级节点
//                    root.ToTry(FunctionA1),
//                    root.ToTry(FunctionA2).Then
//                    (
//                            //二级节点
//                            root.ToTry(FunctionB1),
//                            root.ToTry(FunctionB2).Then
//                            (
//                                    //三级节点
//                                    root.ToTry(FunctionC1),
//                                    root.ToTry(FunctionC2)
//                            ),
//                            //二级节点
//                            root.ToTry(FunctionB3)
//                     )
//             );
//            return root;
//        }
//    }
//}
