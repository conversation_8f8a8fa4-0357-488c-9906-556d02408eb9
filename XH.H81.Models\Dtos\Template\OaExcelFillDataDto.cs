﻿namespace XH.H81.Models.Dtos.Template
{
    /// <summary>
    /// excel填充数据
    /// </summary>
    public class OaExcelFillDataDto
    {
        /// <summary>
        /// 操作标识UUID
        /// </summary>
        public string OPER_UUID { get; set; }
        /// <summary>
        /// 模板ID
        /// </summary>
        public string STYLE_ID { get; set; }

        /// <summary>
        /// excel主键key名,如果只有数据项，则对应FIELDS里的某个key  
        /// 如果有记录项，则对应ARRAYS里的某个key 
        /// </summary>
        public string KEY_COLUMN { get; set; }

        /// <summary>
        /// 所有的记录项+数据项，比如多个人，此时，这个一条数据等于excel表格的一行
        /// </summary>
        public List<AllClassDataDto> ALL_CLASS_DATAS { get; set; }
    }

    /// <summary>
    ///  一个人所有的履历记录、技能证书记录等 ；一个文档所有的学习任务； 一个人所有有权限的文档
    /// </summary>
    public class AllClassDataDto
    {
        /// <summary>
        /// 单字段数据，数据项
        /// </summary>
        public Dictionary<string, string>? FIELDS { get; set; }
        /// <summary>
        /// 记录项
        /// </summary>
        public List<OaExcelDataDto> CLASS_DATAS { get; set; }
    }

    public class OaExcelDataDto
    {
        /// <summary>
        /// 类型ID  数据项可以固定传个数据项即可
        /// </summary>
        public string CLASSE_CODE { get; set; }

        /// <summary>
        /// 分组归类
        /// </summary>
        public string GROUP_BY_KEY { get; set; }

        /// <summary>
        /// 归类后有数据显示
        /// </summary>
        public string HAS_DATA_DISPLAY { get; set; }

        /// <summary>
        /// 归类后没有数据显示
        /// </summary>
        public string NO_HAS_DATA_DISPLAY { get; set; }
        /// <summary>
        /// 列表数据  记录项
        /// </summary>
        public List<Dictionary<string, string>> ARRAYS { get; set; }


    }
}