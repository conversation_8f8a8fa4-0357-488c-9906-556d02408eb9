﻿using Elastic.Clients.Elasticsearch.MachineLearning;
using H.Utility;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using XH.H81.IServices;
using XH.H81.Models.Dtos.Eguard;

namespace XH.H81.API.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    //[ApiExplorerSettings(GroupName = "EguardControl")]
    [Authorize]
    public class EguardControlController : ControllerBase
    {
        private readonly IEguardControlService _eguardControlService;
        /// <summary>
        /// 
        /// </summary>
        /// <param name="eguardControlService"></param>
        public EguardControlController(IEguardControlService eguardControlService)
        {
            _eguardControlService = eguardControlService;
        }

        [AllowAnonymous]
        [HttpGet]
        public IActionResult Test()
        {
            var res = _eguardControlService.Test();
            return Ok(res);
        }



        /// <summary>
        /// 获取门禁组合
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> GetEguardComList()
        {
            var user = this.User.ToClaimsDto();
            var result = await _eguardControlService.GetEguardComList(user.HOSPITAL_ID);
            return Ok(result);
        }

        /// <summary>
        /// 门禁组合删除/禁用前校验
        /// </summary>
        /// <param name="eguardComId">门禁组合ID</param>
        /// <returns></returns>
        [HttpGet]
        [Produces(typeof(List<EguardComUpdateVerifyDto>))]
        public async Task<IActionResult> EguardComUpdateVerify(string eguardComId)
        {
            var res = await _eguardControlService.EguardComUpdateVerify(eguardComId);
            return Ok(res);
        }

        /// <summary>
        /// 删除已有门禁组合授权
        /// </summary>
        /// <param name="eguardComId">门禁组合ID</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> DeleteEguardAuthCom(string eguardComId)
        {
            var claims = this.User.ToClaimsDto();
            var userName = claims.LOGID + "_" + claims.USER_NAME;
            var res = await _eguardControlService.DeleteEguardAuthCom(eguardComId, userName);
            return Ok(res);
        }

        /// <summary>
        /// 获取门禁列表树
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> GetEguardTreeList()
        {
            var user = this.User.ToClaimsDto();
            var result = await _eguardControlService.GetEguardTreeList(user.HOSPITAL_ID);
            return Ok(result);
        }

        /// <summary>
        /// 新增/编辑/复制 门禁组合信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> SaveEguardInfo(EguardComDto input)
        {
            var user = this.User.ToClaimsDto();
            var result = await _eguardControlService.SaveEguardInfo(input, user);
            return Ok(result);
        }

        /// <summary>
        /// 启用禁用门禁组合
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> ChangeEguardState(ChangeEguardComStateDto input)
        {
            var user = this.User.ToClaimsDto();
            var result = await _eguardControlService.ChangeEguardState(input, user);
            return Ok(result);
        }

        /// <summary>
        /// 删除门禁组合
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> DeleteEguardInfo(DeleteEguardComDto input)
        {
            var user = this.User.ToClaimsDto();
            var result = await _eguardControlService.DeleteEguardInfo(input, user);
            return Ok(result);
        }


        //门禁组合  = > 按门禁
        /// <summary>
        /// 获取单个门禁
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetEguardTableList()
        {
            var user = this.User.ToClaimsDto();
            var result = await _eguardControlService.GetEguardTableList(user.HOSPITAL_ID);
            return Ok(result);
        }


        /// <summary>
        /// 门禁 已选待选组合
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> UpdateEguardComTable(EditEguardTableDto input)
        {
            var user = this.User.ToClaimsDto();
            var result = await _eguardControlService.UpdateEguardComTable(input,user);
            return Ok(result);
        }


        /// <summary>
        /// 启用禁用单个门禁
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> ChangeEguardTableState(EditEguardTableDto input)
        {
            var user = this.User.ToClaimsDto();
            var result = await _eguardControlService.ChangeEguardTableState(input, user);
            return Ok(result);
        }




        /// <summary>
        /// 保存门禁时间计划
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> SaveEguardPassDict(EguardPassDictDto input)
        {
            var user = this.User.ToClaimsDto();
            var result = await _eguardControlService.SaveEguardPassDict(input, user);
            return Ok(result);
        }

        /// <summary>
        /// 启用禁用门禁时间计划
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> ChangeEguardPassDictState(ChangeEguardPassDictDto input)
        {
            var user = this.User.ToClaimsDto();
            var result = await _eguardControlService.ChangeEguardPassDictState(input, user);
            return Ok(result);
        }

        /// <summary>
        /// 删除门禁时间计划
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> DeleteEguardPassDict(DeleteEguardPassDictDto input)
        {
            var user = this.User.ToClaimsDto();
            var result = await _eguardControlService.DeleteEguardPassDict(input, user);
            return Ok(result);
        }


        /// <summary>
        /// 获取门禁时间计划列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> GetEguardPassDictList()
        {
            var user = this.User.ToClaimsDto();
            var result = await _eguardControlService.GetEguardPassDictList(user);
            return Ok(result);
        }

        #region 门禁授权
        ///// <summary>
        ///// 按岗位获取门禁列表
        ///// </summary>
        ///// <param name="authType">LAB-检验科室 ；PGROUP-检验专业组；MGROUP-管理专业组；POST-岗位；PROLE-岗位角色;User-用户id</param>
        ///// <param name="dataId">树选中的id</param>
        ///// <returns></returns>
        //[HttpGet]
        //public async Task<IActionResult> GetUserEguardList(string authType, string dataId)
        //{
        //    var res = await _eguardControlService.GetUserEguardList(authType, dataId);
        //    return Ok(res);
        //}



        /// <summary>
        /// 获取门禁列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> GetEguardList(EguardInput input)
        {
            var user = this.User.ToClaimsDto();
            var result = await _eguardControlService.GetEguardList(input, user);
            return Ok(result);

        }

        /// <summary>
        /// 修改时获取关联数据
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> GetUpdateEguardAuth(UpdateEguardAuth request)
        {
            var res = await _eguardControlService.GetUpdateEguardAuth(request);
            return Ok(res);
        }

        /// <summary>
        /// 新增修改门禁授权
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> SaveEguard(EguardSaveDto input)
        {
            var user = this.User.ToClaimsDto();
            var result = await _eguardControlService.SaveEguard(input, user);
            return Ok(result);
        }

        /// <summary>
        /// 新增、修改门禁授权（新模式）
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> NewEguardSave(NewEguardSaveDto request)
        {
            var user = this.User.ToClaimsDto();
            var result = await _eguardControlService.NewEguardSave(request, user);
            return Ok(result);
        }

        /// <summary>
        /// 复制门禁授权
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> CopyEguardAuth(CopyEguardAuthRequest request)
        {
            var user = this.User.ToClaimsDto();
            var userName = user.LOGID + "_" + user.USER_NAME;
            var result = await _eguardControlService.CopyEguardAuth(request, userName);
            return Ok(result);
        }

        /// <summary>
        /// 判断门禁组合或门禁是否可选
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> JudgEguardCom(JudgEguardCom input)
        {
            var user = this.User.ToClaimsDto();
            var result = await _eguardControlService.JudgEguardCom(input, user);
            return Ok(result);

        }

        /// <summary>
        /// 删除门禁授权
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> DeleteEguard(EguardSaveDto input)
        {
            var user = this.User.ToClaimsDto();
            var result = await _eguardControlService.DeleteEguard(input, user);
            return Ok(result);
        }

        /// <summary>
        /// 新删除授权设置
        /// </summary>
        /// <param name="eguardAuthIds"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> NewDeleteEguardAuth(List<string> eguardAuthIds)
        {
            var user = this.User.ToClaimsDto();
            var operPerson = user.LOGID + "_" + user.USER_NAME;
            var result = await _eguardControlService.NewDeleteEguardAuth(eguardAuthIds,operPerson);
            return Ok(result);
        }


        /// <summary>
        /// 获取用户信息
        /// </summary>
        /// <param name="postId">岗位ID</param>
        /// <param name="nameOrLogid">名称或者岗位ID检索</param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetPostUserInfo(string postId, string? nameOrLogid)
        {
            var user = this.User.ToClaimsDto();
            var result = await _eguardControlService.GetNewPostUserInfo(postId, nameOrLogid, user.HOSPITAL_ID);
            return Ok(result);
        }

        #endregion
        #region 访客核准

        /// <summary>
        /// 访客申请列表
        /// </summary>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetVistorRequest(DateTime startTime, DateTime endTime)
        {
            var claims = this.User.ToClaimsDto();
            endTime = endTime.AddDays(1).AddMilliseconds(-1);
            var res = await _eguardControlService.GetVistorRequest(startTime, endTime, claims.HOSPITAL_ID);
            return Ok(res);
        }

        /// <summary>
        /// 保存申请记录
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> SaveVistorRequest([FromForm]SaveVistorRequestRequest request)
        {
            var claims = this.User.ToClaimsDto();
            var userName = claims.LOGID + "_" + claims.USER_NAME;
            var res = await _eguardControlService.SaveVistorRequest(request, claims.HOSPITAL_ID, userName);
            return Ok(res);
        }

        /// <summary>
        /// 操作申请记录
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> VisitRequestApproval(VisitApprovalRequest request)
        {
            var claims = this.User.ToClaimsDto();
            var userName = claims.LOGID + "_" + claims.USER_NAME;
            var res = await _eguardControlService.VisitRequestApproval(request, claims.HOSPITAL_ID, userName);
            return Ok(res);
        }

        /// <summary>
        /// 删除申请记录
        /// </summary>
        /// <param name="visitReqId"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> DeleteVisitRequest(string visitReqId)
        {
            var claims = this.User.ToClaimsDto();
            var userName = claims.LOGID + "_" + claims.USER_NAME;
            var res = await _eguardControlService.DeleteVisitRequest(visitReqId, userName, claims.HOSPITAL_ID);
            return Ok(res);
        }

        /// <summary>
        /// 获取科外人员列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetVisitorList()
        {
            var claims = this.User.ToClaimsDto();
            var res = await _eguardControlService.GetVisitorList(claims.HOSPITAL_ID);
            return Ok(res);
        }

        /// <summary>
        /// 上传科外人员图片
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> UploadVisitorImg([FromForm]UploadVisitorImg request)
        {
            var claims = this.User.ToClaimsDto();
            var userName = claims.LOGID + "_" + claims.USER_NAME;
            var res = await _eguardControlService.UploadVisitorImg(request, userName);
            return Ok(res);
        }
        #endregion

        #region 访客日志
        /// <summary>
        /// 获取全部访问日志
        /// </summary>
        /// <param name="startTime">检索开始时间</param>
        /// <param name="endTime">检索结束时间</param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetAllVisitorLog(DateTime startTime, DateTime endTime)
        {
            var claims = this.User.ToClaimsDto();
            endTime = endTime.AddDays(1).AddMilliseconds(-1);
            var res = await _eguardControlService.GetAllVisitorLog(startTime, endTime, claims.HOSPITAL_ID);
            return Ok(res);
        }

        /// <summary>
        /// 获取访客日志（按访客）
        /// </summary>
        /// <param name="startTime">检索开始时间</param>
        /// <param name="endTime">检索结束时间</param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetVisitorListByVisitor(DateTime startTime, DateTime endTime)
        {
            var claims = this.User.ToClaimsDto();
            endTime = endTime.AddDays(1).AddMilliseconds(-1);
            var res = await _eguardControlService.GetVisitorListByVisitor(startTime, endTime, claims.HOSPITAL_ID);
            return Ok(res);
        }

        /// <summary>
        /// 获取访客日志（按科室人员）
        /// </summary>
        /// <param name="startTime">检索开始时间</param>
        /// <param name="endTime">检索结束时间</param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetLabPersonListByLabPerson(DateTime startTime, DateTime endTime)
        {
            var claims = this.User.ToClaimsDto();
            endTime = endTime.AddDays(1).AddMilliseconds(-1);
            var res = await _eguardControlService.GetLabPersonListByLabPerson(startTime, endTime, claims.HOSPITAL_ID);
            return Ok(res);
        }

        /// <summary>
        /// 获取访客日志（按门禁地点）
        /// </summary>
        /// <param name="startTime">检索开始时间</param>
        /// <param name="endTime">检索结束时间</param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetVisitRoomListByRoom(DateTime startTime, DateTime endTime)
        {
            var claims = this.User.ToClaimsDto();
            endTime = endTime.AddDays(1).AddMilliseconds(-1);
            var res = await _eguardControlService.GetVisitRoomListByRoom(startTime, endTime,claims.HOSPITAL_ID);
            return Ok(res);
        }
        #endregion

        #region 门禁日志
        /// <summary>
        /// 获取门禁日志（全部）
        /// </summary>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetAllOutInLogList(DateTime startTime, DateTime endTime)
        {
            var claims = this.User.ToClaimsDto();
            endTime = endTime.AddDays(1).AddMilliseconds(-1);
            var res = await _eguardControlService.GetAllOutInLogList(startTime, endTime, claims.HOSPITAL_ID);
            return Ok(res);
        }

        /// <summary>
        /// 获取门禁日志（按人员）
        /// </summary>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetPersonListByPerson(DateTime startTime, DateTime endTime)
        {
            var claims = this.User.ToClaimsDto();
            endTime = endTime.AddDays(1).AddMilliseconds(-1);
            var res = await _eguardControlService.GetPersonListByPerson(startTime, endTime, claims.HOSPITAL_ID);
            return Ok(res);
        }

        /// <summary>
        /// 获取门禁（按门禁）
        /// </summary>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetEguardListByEguard(DateTime startTime, DateTime endTime)
        {
            var claims = this.User.ToClaimsDto();
            endTime = endTime.AddDays(1).AddMilliseconds(-1);
            var res = await _eguardControlService.GetEguardListByEguard(startTime, endTime, claims.HOSPITAL_ID);
            return Ok(res);
        }
        #endregion
        /// <summary>
        /// 获取门禁下拉基础数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetEguardDropDownDataList()
        {
            var claims = this.User.ToClaimsDto();
            var res = await _eguardControlService.GetEguardDropDownDataList(claims.HOSPITAL_ID,claims.LAB_ID);
            return Ok(res.ToResultDto());
        }

        /// <summary>
        /// 发布按钮是否有new样式
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> PublishButtonIfNew()
        {
            var claims = this.User.ToClaimsDto();
            var res = await _eguardControlService.PublishButtonIfNew(claims.HOSPITAL_ID);
            return Ok(res);
        }

        /// <summary>
        /// 发布授权设置
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> EguardAuthPublish()
        {
            var claims = this.User.ToClaimsDto();
            var res = await _eguardControlService.EguardAuthPublish(claims.HOSPITAL_ID);
            return Ok(res);
        }

        /// <summary>
        /// 删除组合授权
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> NewDeleteEguard(UpdateEguardAuth request)
        {
            var claims = this.User.ToClaimsDto();
            var operPeron = claims.LOGID + "_" + claims.USER_NAME;
            var res = await _eguardControlService.NewDeleteEguard(request, operPeron);
            return Ok(res);
        }
    }
}
