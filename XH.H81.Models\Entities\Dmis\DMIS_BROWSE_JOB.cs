using System;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using SqlSugar;
using H.Utility.SqlSugarInfra;

namespace XH.H81.Models.Entities.Dmis
{
    [DBOwner("XH_OA")]
    public class DMIS_BROWSE_JOB
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        [Key]
        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        [Column("JOB_ID")]
        [Required(ErrorMessage = "任务ID不允许为空")]

        [StringLength(20, ErrorMessage = "任务ID长度不能超出20字符")]
        [Unicode(false)]
        public string JOB_ID { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [Column("CREATE_TIME")]
        [Unicode(false)]
        public DateTime? CREATE_TIME { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        [Column("CREATE_PERSON")]
        [StringLength(50, ErrorMessage = "创建人长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CREATE_PERSON { get; set; }

        /// <summary>
        /// 任务状态 0未完成1已完成10已取消
        /// </summary>
        [Column("JOB_STATE")]
        [StringLength(20, ErrorMessage = "任务状态 0未完成1已完成10已取消长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? JOB_STATE { get; set; }

        /// <summary>
        /// 文档ID
        /// </summary>
        [Column("DOC_ID")]
        [Required(ErrorMessage = "文档ID不允许为空")]

        [StringLength(20, ErrorMessage = "文档ID长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string DOC_ID { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        [Column("END_TIME")]
        [Unicode(false)]
        public DateTime? END_TIME { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        [Column("USER_ID")]
        [Required(ErrorMessage = "用户ID不允许为空")]

        [StringLength(20, ErrorMessage = "用户ID长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string USER_ID { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Column("REMARK")]
        [StringLength(200, ErrorMessage = "备注长度不能超出200字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? REMARK { get; set; }

        /// <summary>
        /// 医疗机构ID
        /// </summary>
        [Column("HOSPITAL_ID")]
        [Required(ErrorMessage = "医疗机构ID不允许为空")]

        [StringLength(20, ErrorMessage = "医疗机构ID长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string HOSPITAL_ID { get; set; }
        //是否任务
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? IF_JOB { get; set; }
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? MENU_CLASS { get; set; }
    }
}
