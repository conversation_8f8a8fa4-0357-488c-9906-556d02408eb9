﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Entities.Pms
{
    /// <summary>
    /// 考核记录
    /// </summary>
    [SugarTable("PMS_EXAMINE_LIST", TableDescription = "考核记录")]
    [DBOwner("XH_OA")]
    public class PMS_EXAMINE_LIST
    {
        /// <summary>
        /// 考核记录ID
        /// </summary>
        [SugarColumn(IsIdentity = true, IsPrimaryKey = true)]
        public string EXAMINE_ID { get; set; }

        /// <summary>
        /// 人员ID
        /// </summary>
        public string PERSON_ID { get; set; }

        /// <summary>
        /// 排序号
        /// </summary>
        public string EXAMINE_SORT { get; set; }

        /// <summary>
        /// 医疗机构ID
        /// </summary>
        public string HOSPITAL_ID { get; set; }

        /// <summary>
        /// 数据来源;1-从系统关联 2-手工单条录入 3-批量录入
        /// </summary>
        public string SOURCE_KIND { get; set; }

        /// <summary>
        /// 分类;1-人员评估，2-考试
        /// </summary>
        public string EXAMINE_CLASS { get; set; }

        /// <summary>
        /// 来源记录ID
        /// </summary>
        public string SOURCE_ID { get; set; }

        /// <summary>
        /// 考核类型;来自基础数据
        /// </summary>
        public string EXAMINE_TYPE { get; set; }

        /// <summary>
        /// 考核计划
        /// </summary>
        public string EXAMINE_PLAN { get; set; }

        /// <summary>
        /// 考核名称
        /// </summary>
        public string EXAMINE_NAME { get; set; }

        /// <summary>
        /// 考核日期
        /// </summary>
        public DateTime EXAMINE_DATE { get; set; }

        /// <summary>
        /// 自评得分
        /// </summary>
        public decimal SEVALUATION_SCORE { get; set; }

        /// <summary>
        /// 考评得分
        /// </summary>
        public decimal EVALUATE_SCORE { get; set; }

        /// <summary>
        /// 考试得分
        /// </summary>
        public decimal EXAM_SCORE { get; set; }

        /// <summary>
        /// 综合得分
        /// </summary>
        public decimal TOTAL_SCORE { get; set; }

        /// <summary>
        /// 考核结果等级;数据来自OA_BASE_DATA，结束评估、评卷完成写入该表
        /// </summary>
        public string SCORE_GRADE { get; set; }

        /// <summary>
        /// 考核结果等级名称;带html标签包含中文名、颜色
        /// </summary>
        public string SCORE_GRADE_NAME { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public string EXAMINE_STATE { get; set; }

        /// <summary>
        /// 附件
        /// </summary>
        public string EXAMINE_AFFIX { get; set; }

        /// <summary>
        /// 指定审核人员
        /// </summary>
        public string SELECT_CHECK_PERSON { get; set; }

        /// <summary>
        /// 审核时间
        /// </summary>
        public DateTime CHECK_TIME { get; set; }

        /// <summary>
        /// 审核人员
        /// </summary>
        public string CHECK_PERSON { get; set; }

        /// <summary>
        /// 审核状态
        /// </summary>
        public string CHECK_STATE { get; set; }

        /// <summary>
        /// 首次记录人
        /// </summary>
        public string FIRST_RPERSON { get; set; }

        /// <summary>
        /// 首次记录时间
        /// </summary>
        public DateTime FIRST_RTIME { get; set; }

        /// <summary>
        /// 最后操作人
        /// </summary>
        public string LAST_MPERSON { get; set; }

        /// <summary>
        /// 最后操作时间
        /// </summary>
        public DateTime LAST_TIME { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string REMARK { get; set; }


    }
}
