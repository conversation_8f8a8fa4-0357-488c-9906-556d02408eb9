﻿using iTextSharp.text.pdf.parser;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Dtos.IoTDevices.Access
{
    /// <summary>
    /// 门禁周计划配置信息
    /// </summary>
    public class AccessWeekPlanDetailDto
    {

        /// <summary>
        /// 英文周名称：Monday=星期一,Tuesday=星期二,Wednesday=星期三,Thursday=星期四,Friday=星期五,Saturday=星期六,Sunday=星期日
        /// </summary>
        [JsonProperty("week")]
        [JsonConverter(typeof(StringEnumConverter))]

        public AccessWeekTypeEnum Week { get; set; }

        ///<summary>
        ///配置信息
        /// </summary>
        [JsonProperty("TimeSegment")]
        public List<TimeSegment> TimeSegment { get; set; }
    }
    /// <summary>
    /// 门禁周计划具体时间段配置
    /// </summary>
    public class TimeSegment
    {
        /// <summary>
        /// 默认一天可以设置8个时段，id从1-8
        /// </summary>
        [JsonProperty("id", NullValueHandling = NullValueHandling.Ignore)]
        public int? Id { get; set; }

        /// <summary>
        /// 是否开启：true生效
        /// </summary>
        [JsonProperty("enable")]
        public bool Enable { get; set; } = true;

        /// <summary>
        /// 开始时间段：HH:mm:ss
        /// </summary>
        [JsonProperty("beginTime")]
        public string BeginTime { get; set; }

        /// <summary>
        /// 结束时间段：HH:mm:ss，不能小于开始时间段
        /// </summary>
        [JsonProperty("endTime")]
        public string EndTime { get; set; }
    }
}
