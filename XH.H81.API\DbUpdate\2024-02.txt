--2024-02-20 cxp新增人员附加记录表
CREATE TABLE XH_OA.PMS_ADDN_RECORD(
    RECORD_ID VARCHAR2(50) NOT NULL,
	HOSPITAL_ID VARCHAR2(20) NOT NULL,
    PERSON_ID VARCHAR2(50) NOT NULL,
    CLASS_ID VARCHAR2(50) NOT NULL,    
    RECORD_AFFIX VARCHAR2(500),
    RECORD_STATE VARCHAR2(10) DEFAULT '1',
    RECORD_DATA CLOB,
	SELECT_CHECK_PERSON VARCHAR2(50),
	CHECK_STATE VARCHAR2(10) DEFAULT '0',
    CHECK_PERSON VARCHAR2(50),
    CHECK_TIME DATE,
	FIRST_RPERSON VARCHAR2(50),
    FIRST_RTIME DATE,
    LAST_MPERSON VARCHAR2(50),
    LAST_TIME DATE,
    REMARK VARCHAR2(200)
);
alter table XH_OA.PMS_ADDN_RECORD
add constraint PK_PMS_ADDN_RECORD primary key (RECORD_ID);

COMMENT ON TABLE XH_OA.PMS_ADDN_RECORD IS '人员附加记录表';
COMMENT ON COLUMN XH_OA.PMS_ADDN_RECORD.RECORD_ID IS '主键id';
COMMENT ON COLUMN XH_OA.PMS_ADDN_RECORD.HOSPITAL_ID IS '院区id';
COMMENT ON COLUMN XH_OA.PMS_ADDN_RECORD.PERSON_ID IS '人员id';
COMMENT ON COLUMN XH_OA.PMS_ADDN_RECORD.CLASS_ID IS '分类id,对应PMS_ADDN_CLASS_INFO的主键ID';
COMMENT ON COLUMN XH_OA.PMS_ADDN_RECORD.RECORD_AFFIX IS '附件ID拼接';
COMMENT ON COLUMN XH_OA.PMS_ADDN_RECORD.RECORD_STATE IS '状态;1启用 0禁用';
COMMENT ON COLUMN XH_OA.PMS_ADDN_RECORD.RECORD_DATA IS '字段和所属值';
COMMENT ON COLUMN XH_OA.PMS_ADDN_RECORD.CHECK_STATE IS '审核状态:0是录入、1是提交、2是通过';
COMMENT ON COLUMN XH_OA.PMS_ADDN_RECORD.CHECK_PERSON IS '审核人';
COMMENT ON COLUMN XH_OA.PMS_ADDN_RECORD.CHECK_TIME IS '审核时间';
COMMENT ON COLUMN XH_OA.PMS_ADDN_RECORD.FIRST_RPERSON IS '首次记录人';
COMMENT ON COLUMN XH_OA.PMS_ADDN_RECORD.FIRST_RTIME IS '首次记录时间';
COMMENT ON COLUMN XH_OA.PMS_ADDN_RECORD.LAST_MPERSON IS '最后操作人';
COMMENT ON COLUMN XH_OA.PMS_ADDN_RECORD.LAST_TIME IS '最后操作时间';
COMMENT ON COLUMN XH_OA.PMS_ADDN_RECORD.REMARK IS '备注';

grant select, insert, update, delete on XH_OA.PMS_ADDN_RECORD to XH_COM;

--2024-02-20 cxp新增人员档案分类信息表
CREATE TABLE XH_OA.PMS_ADDN_CLASS_INFO(
    CLASS_ID VARCHAR2(50) NOT NULL,
	HOSPITAL_ID VARCHAR2(20) NOT NULL,
    CLASS_TYPE VARCHAR2(10) DEFAULT '0',
    CLASS_NAME VARCHAR2(50),    
    CLASS_SORT VARCHAR2(10),
    CLASS_STATE VARCHAR2(10) DEFAULT '1',
    FORM_SETUP_ID VARCHAR2(50),
	TABLE_SETUP_ID VARCHAR2(50),
	CLASS_TABLE_NAME VARCHAR2(100),
    CLASS_ADDN_CONFIG VARCHAR2(500),
	FIRST_RPERSON VARCHAR2(50),
    FIRST_RTIME DATE,
    LAST_MPERSON VARCHAR2(50),
    LAST_TIME DATE,
    REMARK VARCHAR2(200)
);
alter table XH_OA.PMS_ADDN_CLASS_INFO
add constraint PK_PMS_ADDN_CLASS_INFO primary key (CLASS_ID);

COMMENT ON TABLE XH_OA.PMS_ADDN_CLASS_INFO IS '人员档案分类信息表';
COMMENT ON COLUMN XH_OA.PMS_ADDN_CLASS_INFO.CLASS_ID IS '主键id';
COMMENT ON COLUMN XH_OA.PMS_ADDN_CLASS_INFO.HOSPITAL_ID IS '医疗机构ID';
COMMENT ON COLUMN XH_OA.PMS_ADDN_CLASS_INFO.CLASS_TYPE IS '分类类型0:固定 1:扩展 默认0';
COMMENT ON COLUMN XH_OA.PMS_ADDN_CLASS_INFO.CLASS_NAME IS '分类名';
COMMENT ON COLUMN XH_OA.PMS_ADDN_CLASS_INFO.CLASS_SORT IS '分类排序';
COMMENT ON COLUMN XH_OA.PMS_ADDN_CLASS_INFO.CLASS_STATE IS '分类状态0禁用1在用2删除,默认1';
COMMENT ON COLUMN XH_OA.PMS_ADDN_CLASS_INFO.FORM_SETUP_ID IS '表单配置记录id';
COMMENT ON COLUMN XH_OA.PMS_ADDN_CLASS_INFO.TABLE_SETUP_ID IS '表格配置记录id';
COMMENT ON COLUMN XH_OA.PMS_ADDN_CLASS_INFO.CLASS_TABLE_NAME IS '分类数据存储表名';
COMMENT ON COLUMN XH_OA.PMS_ADDN_CLASS_INFO.CLASS_ADDN_CONFIG IS '分类扩展配置内容';
COMMENT ON COLUMN XH_OA.PMS_ADDN_RECORD.FIRST_RPERSON IS '首次记录人';
COMMENT ON COLUMN XH_OA.PMS_ADDN_RECORD.FIRST_RTIME IS '首次记录时间';
COMMENT ON COLUMN XH_OA.PMS_ADDN_RECORD.LAST_MPERSON IS '最后操作人';
COMMENT ON COLUMN XH_OA.PMS_ADDN_RECORD.LAST_TIME IS '最后操作时间';
COMMENT ON COLUMN XH_OA.PMS_ADDN_RECORD.REMARK IS '备注';
grant select, insert, update, delete on XH_OA.PMS_ADDN_CLASS_INFO to XH_COM;



--2024-02-19  PMS_EDUCATION_LIST新增 RECORD_DATA 
ALTER TABLE XH_OA.PMS_EDUCATION_LIST ADD RECORD_DATA   CLOB;
COMMENT ON COLUMN XH_OA.PMS_EDUCATION_LIST.RECORD_DATA IS '扩展字段和所属值';

--2024-02-19  PMS_EXCHANGE_LIST新增 RECORD_DATA 
ALTER TABLE XH_OA.PMS_EXCHANGE_LIST ADD RECORD_DATA   CLOB;
COMMENT ON COLUMN XH_OA.PMS_EXCHANGE_LIST.RECORD_DATA IS '扩展字段和所属值';

--2024-02-19  PMS_EXPATRIATE_LIST新增 RECORD_DATA 
ALTER TABLE XH_OA.PMS_EXPATRIATE_LIST ADD RECORD_DATA   CLOB;
COMMENT ON COLUMN XH_OA.PMS_EXPATRIATE_LIST.RECORD_DATA IS '扩展字段和所属值';

--2024-02-19  PMS_INTELLECTUAL_LIST新增 RECORD_DATA 
ALTER TABLE XH_OA.PMS_INTELLECTUAL_LIST ADD RECORD_DATA   CLOB;
COMMENT ON COLUMN XH_OA.PMS_INTELLECTUAL_LIST.RECORD_DATA IS '扩展字段和所属值';

--2024-02-19  PMS_PROFESSIONAL_LIST新增 RECORD_DATA 
ALTER TABLE XH_OA.PMS_PROFESSIONAL_LIST ADD RECORD_DATA   CLOB;
COMMENT ON COLUMN XH_OA.PMS_PROFESSIONAL_LIST.RECORD_DATA IS '扩展字段和所属值';

--2024-02-19  PMS_RESEARCH_LIST新增 RECORD_DATA 
ALTER TABLE XH_OA.PMS_RESEARCH_LIST ADD RECORD_DATA   CLOB;
COMMENT ON COLUMN XH_OA.PMS_RESEARCH_LIST.RECORD_DATA IS '扩展字段和所属值';

--2024-02-19  PMS_RESUME_LIST新增 RECORD_DATA 
ALTER TABLE XH_OA.PMS_RESUME_LIST ADD RECORD_DATA   CLOB;
COMMENT ON COLUMN XH_OA.PMS_RESUME_LIST.RECORD_DATA IS '扩展字段和所属值';

--2024-02-19  PMS_REWARD_LIST新增 RECORD_DATA 
ALTER TABLE XH_OA.PMS_REWARD_LIST ADD RECORD_DATA   CLOB;
COMMENT ON COLUMN XH_OA.PMS_REWARD_LIST.RECORD_DATA IS '扩展字段和所属值';


--2024-02-19  PMS_SKILL_CERTIFICATE_LIST新增 RECORD_DATA 
ALTER TABLE XH_OA.PMS_SKILL_CERTIFICATE_LIST ADD RECORD_DATA   CLOB;
COMMENT ON COLUMN XH_OA.PMS_SKILL_CERTIFICATE_LIST.RECORD_DATA IS '扩展字段和所属值';

--2024-02-19  PMS_SOCIAL_OFFICE_LIST新增 RECORD_DATA 
ALTER TABLE XH_OA.PMS_SOCIAL_OFFICE_LIST ADD RECORD_DATA   CLOB;
COMMENT ON COLUMN XH_OA.PMS_SOCIAL_OFFICE_LIST.RECORD_DATA IS '扩展字段和所属值';


--2024-02-19  PMS_STUDY_LIST新增 RECORD_DATA 
ALTER TABLE XH_OA.PMS_STUDY_LIST ADD RECORD_DATA   CLOB;
COMMENT ON COLUMN XH_OA.PMS_STUDY_LIST.RECORD_DATA IS '扩展字段和所属值';

--2024-02-19  PMS_TEACH_LIST新增 RECORD_DATA 
ALTER TABLE XH_OA.PMS_TEACH_LIST ADD RECORD_DATA   CLOB;
COMMENT ON COLUMN XH_OA.PMS_TEACH_LIST.RECORD_DATA IS '扩展字段和所属值';

--2024-02-19  PMS_THESIS_LIST新增 RECORD_DATA 
ALTER TABLE XH_OA.PMS_THESIS_LIST ADD RECORD_DATA   CLOB;
COMMENT ON COLUMN XH_OA.PMS_THESIS_LIST.RECORD_DATA IS '扩展字段和所属值';

--2024-02-19  PMS_TRAIN_LIST新增 RECORD_DATA 
ALTER TABLE XH_OA.PMS_TRAIN_LIST ADD RECORD_DATA   CLOB;
COMMENT ON COLUMN XH_OA.PMS_TRAIN_LIST.RECORD_DATA IS '扩展字段和所属值';





-- 2024-02-21 wgn 插入固定分类数据
INSERT INTO XH_OA.PMS_ADDN_CLASS_INFO (CLASS_ID, HOSPITAL_ID, CLASS_TYPE, CLASS_NAME, CLASS_SORT, CLASS_STATE, FORM_SETUP_ID, TABLE_SETUP_ID, CLASS_TABLE_NAME, CLASS_ADDN_CONFIG, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_TIME, REMARK)  VALUES('PMS_RESUME_LIST', '33A001', '0', '履历记录', '00000', '1', 'H810101', 'H810115CT', 'PMS_RESUME_LIST', '', '', '', '', '', '');
INSERT INTO XH_OA.PMS_ADDN_CLASS_INFO (CLASS_ID, HOSPITAL_ID, CLASS_TYPE, CLASS_NAME, CLASS_SORT, CLASS_STATE, FORM_SETUP_ID, TABLE_SETUP_ID, CLASS_TABLE_NAME, CLASS_ADDN_CONFIG, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_TIME, REMARK)  VALUES('PMS_PROFESSIONAL_LIST', '33A001', '0', '职称记录', '00012', '1', 'H810113', 'H810119CT', 'PMS_PROFESSIONAL_LIST', '', '', '', '', '', '');
INSERT INTO XH_OA.PMS_ADDN_CLASS_INFO (CLASS_ID, HOSPITAL_ID, CLASS_TYPE, CLASS_NAME, CLASS_SORT, CLASS_STATE, FORM_SETUP_ID, TABLE_SETUP_ID, CLASS_TABLE_NAME, CLASS_ADDN_CONFIG, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_TIME, REMARK)  VALUES('PMS_REWARD_LIST', '33A001', '0', '奖励记录', '00004', '1', 'H810105', 'H810122CT', 'PMS_REWARD_LIST', '', '', '', '', '', '');
INSERT INTO XH_OA.PMS_ADDN_CLASS_INFO (CLASS_ID, HOSPITAL_ID, CLASS_TYPE, CLASS_NAME, CLASS_SORT, CLASS_STATE, FORM_SETUP_ID, TABLE_SETUP_ID, CLASS_TABLE_NAME, CLASS_ADDN_CONFIG, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_TIME, REMARK)  VALUES('PMS_TEACH_LIST', '33A001', '0', '教学记录', '00005', '1', 'H810106', 'H810123CT', 'PMS_TEACH_LIST', '', '', '', '', '', '');
INSERT INTO XH_OA.PMS_ADDN_CLASS_INFO (CLASS_ID, HOSPITAL_ID, CLASS_TYPE, CLASS_NAME, CLASS_SORT, CLASS_STATE, FORM_SETUP_ID, TABLE_SETUP_ID, CLASS_TABLE_NAME, CLASS_ADDN_CONFIG, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_TIME, REMARK)  VALUES('PMS_STUDY_LIST', '33A001', '0', '进修记录', '00006', '1', 'H810107', 'H810116CT', 'PMS_STUDY_LIST', '', '', '', '', '', '');
INSERT INTO XH_OA.PMS_ADDN_CLASS_INFO (CLASS_ID, HOSPITAL_ID, CLASS_TYPE, CLASS_NAME, CLASS_SORT, CLASS_STATE, FORM_SETUP_ID, TABLE_SETUP_ID, CLASS_TABLE_NAME, CLASS_ADDN_CONFIG, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_TIME, REMARK)  VALUES('PMS_RESEARCH_LIST', '33A001', '0', '课题记录', '00007', '1', 'H810108', 'H810124CT', 'PMS_RESEARCH_LIST', '', '', '', '', '', '');
INSERT INTO XH_OA.PMS_ADDN_CLASS_INFO (CLASS_ID, HOSPITAL_ID, CLASS_TYPE, CLASS_NAME, CLASS_SORT, CLASS_STATE, FORM_SETUP_ID, TABLE_SETUP_ID, CLASS_TABLE_NAME, CLASS_ADDN_CONFIG, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_TIME, REMARK)  VALUES('PMS_THESIS_LIST', '33A001', '0', '论著记录', '00008', '1', 'H810109', 'H810127CT', 'PMS_THESIS_LIST', '', '', '', '', '', '');
INSERT INTO XH_OA.PMS_ADDN_CLASS_INFO (CLASS_ID, HOSPITAL_ID, CLASS_TYPE, CLASS_NAME, CLASS_SORT, CLASS_STATE, FORM_SETUP_ID, TABLE_SETUP_ID, CLASS_TABLE_NAME, CLASS_ADDN_CONFIG, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_TIME, REMARK)  VALUES('PMS_EDUCATION_LIST', '33A001', '0', '教育背景', '00009', '1', 'H810110', 'H810117CT', 'PMS_EDUCATION_LIST', '', '', '', '', '', '');
INSERT INTO XH_OA.PMS_ADDN_CLASS_INFO (CLASS_ID, HOSPITAL_ID, CLASS_TYPE, CLASS_NAME, CLASS_SORT, CLASS_STATE, FORM_SETUP_ID, TABLE_SETUP_ID, CLASS_TABLE_NAME, CLASS_ADDN_CONFIG, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_TIME, REMARK)  VALUES('PMS_SKILL_CERTIFICATE_LIST', '33A001', '0', '技能证书记录', '00010', '1', 'H810111', 'H810126CT', 'PMS_SKILL_CERTIFICATE_LIST', '', '', '', '', '', '');
INSERT INTO XH_OA.PMS_ADDN_CLASS_INFO (CLASS_ID, HOSPITAL_ID, CLASS_TYPE, CLASS_NAME, CLASS_SORT, CLASS_STATE, FORM_SETUP_ID, TABLE_SETUP_ID, CLASS_TABLE_NAME, CLASS_ADDN_CONFIG, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_TIME, REMARK)  VALUES('PMS_TRAIN_LIST', '33A001', '0', '培训记录', '00011', '1', 'H810112', 'H810118CT', 'PMS_TRAIN_LIST', '', '', '', '', '', '');
INSERT INTO XH_OA.PMS_ADDN_CLASS_INFO (CLASS_ID, HOSPITAL_ID, CLASS_TYPE, CLASS_NAME, CLASS_SORT, CLASS_STATE, FORM_SETUP_ID, TABLE_SETUP_ID, CLASS_TABLE_NAME, CLASS_ADDN_CONFIG, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_TIME, REMARK)  VALUES('PMS_SOCIAL_OFFICE_LIST', '33A001', '0', '社会任职', '00001', '1', 'H810102', 'H810121CT', 'PMS_SOCIAL_OFFICE_LIST', '', '', '', '', '', '');
INSERT INTO XH_OA.PMS_ADDN_CLASS_INFO (CLASS_ID, HOSPITAL_ID, CLASS_TYPE, CLASS_NAME, CLASS_SORT, CLASS_STATE, FORM_SETUP_ID, TABLE_SETUP_ID, CLASS_TABLE_NAME, CLASS_ADDN_CONFIG, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_TIME, REMARK)  VALUES('PMS_INTELLECTUAL_LIST', '33A001', '0', '知识产权', '00002', '1', 'H810103', 'H810125CT', 'PMS_INTELLECTUAL_LIST', '', '', '', '', '', '');
INSERT INTO XH_OA.PMS_ADDN_CLASS_INFO (CLASS_ID, HOSPITAL_ID, CLASS_TYPE, CLASS_NAME, CLASS_SORT, CLASS_STATE, FORM_SETUP_ID, TABLE_SETUP_ID, CLASS_TABLE_NAME, CLASS_ADDN_CONFIG, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_TIME, REMARK)  VALUES('PMS_EXCHANGE_LIST', '33A001', '0', '访问交流记录', '00003', '1', 'H810104', 'H810128CT', 'PMS_EXCHANGE_LIST', '', '', '', '', '', '');
INSERT INTO XH_OA.PMS_ADDN_CLASS_INFO (CLASS_ID, HOSPITAL_ID, CLASS_TYPE, CLASS_NAME, CLASS_SORT, CLASS_STATE, FORM_SETUP_ID, TABLE_SETUP_ID, CLASS_TABLE_NAME, CLASS_ADDN_CONFIG, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_TIME, REMARK)  VALUES('PMS_EXPATRIATE_LIST', '33A001', '0', '外派记录', '00013', '1', 'H810114', 'H810120CT', 'PMS_EXPATRIATE_LIST', '', '', '', '', '', '');
