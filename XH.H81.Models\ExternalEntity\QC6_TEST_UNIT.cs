using System;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using SqlSugar;
using H.Utility.SqlSugarInfra;

namespace XH.H81.Models.ExternalEntity
{
    [Table("QC6_TEST_UNIT")]
    [DBOwner("XH_SYS")]
    public class QC6_TEST_UNIT
    {
        /// <summary>
        /// 
        /// </summary>
        [Key]
        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        //[Column("TUNIT_ID")]
        [Required(ErrorMessage = "不允许为空")]

        [StringLength(20, ErrorMessage = "TUNIT_ID长度不能超出20字符")]
        //[Unicode(false)]
        public string TUNIT_ID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        //[Column("LAB_ID")]
        [Required(ErrorMessage = "不允许为空")]

        [StringLength(20, ErrorMessage = "LAB_ID长度不能超出20字符")]
        //[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string LAB_ID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        //[Column("OUTCONTROL_LTIME")]
        //[Unicode(false)]
        public decimal? OUTCONTROL_LTIME { get; set; }

        /// <summary>
        /// 
        /// </summary>
        //[Column("FIRST_RPERSON")]
        [StringLength(50, ErrorMessage = "FIRST_RPERSON长度不能超出50字符")]
        //[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FIRST_RPERSON { get; set; }

        /// <summary>
        /// 
        /// </summary>
        //[Column("LAST_MTIME")]
        //[Unicode(false)]
        public DateTime? LAST_MTIME { get; set; }

        /// <summary>
        /// 
        /// </summary>
        //[Column("EVALUATION_CYCLE")]
        [StringLength(10, ErrorMessage = "EVALUATION_CYCLE长度不能超出10字符")]
        //[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? EVALUATION_CYCLE { get; set; }

        /// <summary>
        /// 
        /// </summary>
        //[Column("INSTRUMENT_ID")]
        [Required(ErrorMessage = "不允许为空")]

        [StringLength(20, ErrorMessage = "INSTRUMENT_ID长度不能超出20字符")]
        //[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string INSTRUMENT_ID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        //[Column("TUNIT_NAME")]
        [StringLength(50, ErrorMessage = "TUNIT_NAME长度不能超出50字符")]
        //[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? TUNIT_NAME { get; set; }

        /// <summary>
        /// 
        /// </summary>
        //[Column("QC_VALID_LTIME")]
        [StringLength(10, ErrorMessage = "QC_VALID_LTIME长度不能超出10字符")]
        //[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? QC_VALID_LTIME { get; set; }

        /// <summary>
        /// 
        /// </summary>
        //[Column("QGROUP_ID")]
        [Required(ErrorMessage = "不允许为空")]

        [StringLength(20, ErrorMessage = "QGROUP_ID长度不能超出20字符")]
        //[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string QGROUP_ID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        //[Column("TUNIT_NUM")]
        [StringLength(50, ErrorMessage = "TUNIT_NUM长度不能超出50字符")]
        //[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? TUNIT_NUM { get; set; }

        /// <summary>
        /// 
        /// </summary>
        //[Column("END_TIME")]
        //[Unicode(false)]
        public DateTime? END_TIME { get; set; }

        /// <summary>
        /// 
        /// </summary>
        //[Column("LEAST_ANALYSIS")]
        [StringLength(10, ErrorMessage = "LEAST_ANALYSIS长度不能超出10字符")]
        //[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? LEAST_ANALYSIS { get; set; }

        /// <summary>
        /// 
        /// </summary>
        //[Column("REMARK")]
        [StringLength(200, ErrorMessage = "REMARK长度不能超出200字符")]
        //[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? REMARK { get; set; }

        /// <summary>
        /// 
        /// </summary>
        //[Column("START_TIME")]
        //[Unicode(false)]
        public DateTime? START_TIME { get; set; }

        /// <summary>
        /// 
        /// </summary>
        //[Column("FIRST_RTIME")]
        //[Unicode(false)]
        public DateTime? FIRST_RTIME { get; set; }

        /// <summary>
        /// 
        /// </summary>
        //[Column("LAST_MPERSON")]
        [StringLength(50, ErrorMessage = "LAST_MPERSON长度不能超出50字符")]
        //[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? LAST_MPERSON { get; set; }

        /// <summary>
        /// 
        /// </summary>
        //[Column("TUNIT_DES")]
        [StringLength(500, ErrorMessage = "TUNIT_DES长度不能超出500字符")]
        //[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? TUNIT_DES { get; set; }

        /// <summary>
        /// 
        /// </summary>
        //[Column("QC_VALID_LTIME_TYPE")]
        [StringLength(10, ErrorMessage = "QC_VALID_LTIME_TYPE长度不能超出10字符")]
        //[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? QC_VALID_LTIME_TYPE { get; set; }

        /// <summary>
        /// 
        /// </summary>
        //[Column("TUNIT_STATE")]
        [StringLength(10, ErrorMessage = "TUNIT_STATE长度不能超出10字符")]
        //[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? TUNIT_STATE { get; set; }

        /// <summary>
        /// 
        /// </summary>
        //[Column("HOSPITAL_ID")]
        [Required(ErrorMessage = "不允许为空")]

        [StringLength(20, ErrorMessage = "HOSPITAL_ID长度不能超出20字符")]
        //[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string HOSPITAL_ID { get; set; }


    }
}
