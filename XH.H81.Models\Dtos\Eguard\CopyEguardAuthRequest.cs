﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Dtos.Eguard
{
    public class CopyEguardAuthRequest
    {
        /// <summary>
        /// 授权id
        /// </summary>
        public List<string> EGUARD_AUTH_ID { get;set; }
        /// <summary>
        /// 授权类型 1岗位 2人员
        /// </summary>
        public string EGUARD_AUTH_TYPE { get;set; }
        /// <summary>
        /// 适用范围 LAB-检验科室 PGROUP-检验专业组 MGROUP-管理专业组 POST-岗位 PROLE-岗位角色
        /// </summary>
        public string EPLAN_APPLY_TYPE { get;set; }
        /// <summary>
        /// 授权数据ID（树结构选中的id）
        /// </summary>
        public List<string> EGUARD_DATA_ID { get;set; }
    }
    public class CopyUnionData
    {
        public string NEW_EGUARD_AUTH_ID { get; set; }
        public string OLD_EGUARD_AUTH_ID { get;set; }
    }
}
