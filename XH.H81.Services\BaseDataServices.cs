﻿using AutoMapper;
using H.BASE;
using H.BASE.SqlSugarInfra.Uow;
using H.IRepository;
using H.Repository;
using H.Utility;
using H.Utility.Dtos.S01;
using H.Utility.Helper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Office.Interop.Excel;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NPOI.Util;
using RestSharp;
using Serilog;
using SqlSugar;
using System.Data;
using System.Diagnostics;
using XH.H81.Base.Helper;
using XH.H81.IServices;
using XH.H81.Models;
using XH.H81.Models.Dtos;
using XH.H81.Models.Dtos.Pms;
using XH.H81.Models.Dtos.Template;

using XH.H81.Models.Entities;
using XH.H81.Models.Entities.Exam;
using XH.H81.Models.Entities.others;
using XH.H81.Models.Entities.Pms;
using XH.H81.Models.ExternalEntity;
using XH.H81.Models.SugarDbContext;
using XH.LAB.UTILS.Models;
using XH.LAB.UTILS.Models.Entites;

namespace XH.H81.Services
{
    /// <summary>
    /// 基础数据服务 余祥琼维护
    /// </summary>
    public class BaseDataServices : IBaseDataServices
    {
        private readonly IConfiguration _configuration;
        private readonly IMapper _mapper;
        private readonly ILogger<BaseDataServices> _logger;
        private readonly IHttpContextAccessor _httpContext;
        private readonly RestClient _clientH07;


        private readonly RestClient _clientS28;
        private readonly RestClient _clientS2803;
        private readonly ISqlSugarUow<SugarDbContext_Master> _soa;
        private readonly IMemoryCache _cache;
        private readonly RestClient _clientH5702;
        private readonly RestClient _clientH04;
        private readonly RestClient _clientH115;
        private readonly string ExecuteLisArchiveUrl = "";
        private readonly string _getMaxNumberRedisPrefix;
        public BaseDataServices(IMapper mapper, IConfiguration configuration, ILogger<BaseDataServices> logger, IHttpContextAccessor httpContext
           , ISqlSugarUow<SugarDbContext_Master> suow, IMemoryCache cache)
        {
            _mapper = mapper;
            _logger = logger;
            _cache = cache ?? throw new ArgumentNullException(nameof(cache)); //new MemoryCache(new MemoryCacheOptions());
            _httpContext = httpContext;
            _soa = suow;
            var addressH07 = configuration["H07"];//系统数据管理
            var addressH5702 = configuration["H57-02"];
            var addressS28 = configuration["S28"];
            // var addressH115 = configuration["H115"];
            // var addressH115 = "https://*************:44397";
            var addressH115 = configuration["H115"];
            var addressH04 = configuration["H04-13"];//检验工具箱
            try
            {
                _getMaxNumberRedisPrefix = configuration["getMaxNumberRedisPrefix"];
                //忽略证书错误
                _clientH07 = new RestClient(new RestClientOptions()
                {
                    RemoteCertificateValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true,
                    BaseUrl = new Uri(addressH07),
                    ThrowOnAnyError = true
                });
                _clientH5702 = new RestClient(new RestClientOptions()
                {
                    RemoteCertificateValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true,
                    BaseUrl = new Uri(addressH5702),
                    ThrowOnAnyError = true
                });
                _clientS28 = new RestClient(new RestClientOptions()
                {
                    RemoteCertificateValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true,
                    BaseUrl = new Uri(addressS28),
                    ThrowOnAnyError = true
                });
                _clientH04 = new RestClient(new RestClientOptions()
                {
                    RemoteCertificateValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true,
                    BaseUrl = new Uri(addressH04),
                    ThrowOnAnyError = true
                });
                _clientH115 = new RestClient(new RestClientOptions()
                {
                    RemoteCertificateValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true,
                    BaseUrl = new Uri(addressH115),
                    ThrowOnAnyError = true
                });
            }
            catch (Exception ex)
            {
                Log.Information(ex.ToString());
            }

        }

        /// <summary>
        /// 固定基础数据
        /// </summary>
        /// <param name="systemId">系统ID</param>
        /// <param name="moduleId">模块ID</param>
        /// <param name="oneClass">一级分类</param>
        /// <param name="classId">分类ID</param>
        /// <param name="getType">1,缓存数据，2,表数据</param>
        /// <returns></returns>
        [UseCacheWhenFailed]
        public List<Lis5BaseDataDto> GetLis5BaseData(string classId, string systemId, string moduleId, string oneClass, EnumBaseDataGetType getType)
        {
            var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();

            Stopwatch sw = new Stopwatch();
            sw.Start();
            string url = $"/api/External/GetLis5BaseData?dataClassId={classId}&systemId={systemId}&moduleId={moduleId}&oneClass={oneClass}&getType={getType.ToString()}";

            RestRequest request = new RestRequest(url);
            request.AddHeader("Authorization", token);
            var response = _clientH07.ExecuteGet<ResultDto>(request);
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用H07模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
            if (response.ErrorException != null)
            {
                Log.Error($"调用H07模块[{url}]发生错误:{response.ErrorException}");
                throw new Exception($"调用H07模块[{url}]发生错误:{response.ErrorException}", response.ErrorException);
            }
            else
            {
                if (response.Data.success)
                {
                    return JsonHelper.FromJson<List<Lis5BaseDataDto>>(response.Data.data.ToString());
                }
                else
                {
                    Log.Error($"调用H07模块[{url}]请求完成,但是返回了错误:" + response.Data.msg);
                    return null;
                }
            }
        }

        /// <summary>
        /// 获取组织机构数据
        /// 组织机构数据包括：系统实例,外部系统,医疗机构,院区、分院/检验科室/位置字典/检验专业组/管理专业组
        /// </summary>
        /// <param name="hospitalId">机构ID</param>
        /// <param name="getType">1,缓存数据，2,表数据</param>
        /// <returns></returns>
        [UseCacheWhenFailed]
        public OrganizeDataDto GetOrganizeData(string hospitalId, EnumBaseDataGetType getType)
        {

            var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();
            Stopwatch sw = new Stopwatch();
            sw.Start();
            string url = $"/api/External/GetOrganize333333Data?hospitalId={hospitalId}&getType={getType.ToString()}";

            RestRequest request = new RestRequest(url);
            request.AddHeader("Authorization", token);
            var response = _clientH07.ExecuteGet<ResultDto>(request);
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用H07模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
            if (response.ErrorException != null)
            {
                Log.Error($"调用H07模块[{url}]发生错误:{response.ErrorException}");
                throw new Exception($"调用H07模块[{url}]发生错误:{response.ErrorException}", response.ErrorException);
            }
            else
            {
                if (response.Data.success)
                {
                    return JsonHelper.FromJson<OrganizeDataDto>(response.Data.data.ToString());
                }
                else
                {
                    Log.Error($"调用H07模块[{url}]请求完成,但是返回了错误:" + response.Data.msg);
                    return null;
                }
            }
        }

        /// <summary>
        /// 基础数据
        /// 病人科别,病人病区,护理单元,医院护工表,医院医生表,医院护士表,公司信息,公司联系人
        /// </summary>
        /// <param name="classId">多个用英文逗号隔开</param>
        /// <param name="hospitalId">医疗机构ID</param>
        /// <param name="labId">科室ID</param>
        /// <param name="getType">1,缓存数据，2,表数据</param>
        /// <returns></returns>
        [UseCacheWhenFailed]
        public LisBaseDataDto GetLisBaseData(string classId, string hospitalId, string labId, EnumBaseDataGetType getType)
        {
            var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();

            Stopwatch sw = new Stopwatch();
            sw.Start();
            string url = $"/api/External/GetLisBaseData?dataClassId={classId}&hospitalId={hospitalId}&labId={labId}&getType={getType.ToString()}";

            RestRequest request = new RestRequest(url);
            request.AddHeader("Authorization", token);
            var response = _clientH07.ExecuteGet<ResultDto>(request);
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用H07模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
            if (response.ErrorException != null)
            {
                Log.Error($"调用H07模块[{url}]发生错误:{response.ErrorException}");
                throw new Exception($"调用H07模块[{url}]发生错误:{response.ErrorException}", response.ErrorException);
            }
            else
            {
                if (response.Data.success)
                {
                    return JsonHelper.FromJson<LisBaseDataDto>(response.Data.data.ToString());
                }
                else
                {
                    Log.Error($"调用H07模块[{url}]请求完成,但是返回了错误:" + response.Data.msg);
                    return null;
                }
            }
        }

        /// <summary>
        /// 菜单按钮API权限
        /// </summary>
        /// <param name="moduleId"></param>
        /// <param name="getType">1,缓存数据，2,表数据</param>
        /// <returns></returns>
        [UseCacheWhenFailed]

        public MenuButtonApiDto GetMenuButtonApi(string moduleId, EnumBaseDataGetType getType)
        {
            var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();

            Stopwatch sw = new Stopwatch();
            sw.Start();
            string url = $"/api/External/GetMenuButtonApi?moduleId={moduleId}&getType={getType.ToString()}";

            RestRequest request = new RestRequest(url);
            request.AddHeader("Authorization", token);
            var response = _clientH07.ExecuteGet<ResultDto>(request);
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用H07模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
            if (response.ErrorException != null)
            {
                Log.Error($"调用H07模块[{url}]发生错误:{response.ErrorException}");
                throw new Exception($"调用H07模块[{url}]发生错误:{response.ErrorException}", response.ErrorException);
            }
            else
            {
                if (response.Data.success)
                {
                    return JsonHelper.FromJson<MenuButtonApiDto>(response.Data.data.ToString());
                }
                else
                {
                    Log.Error($"调用H07模块[{url}]请求完成,但是返回了错误:" + response.Data.msg);
                    return null;
                }
            }
        }

        /// <summary>
        /// 账号信息
        /// </summary>
        /// <param name="hospitalId">医疗机构ID</param>
        /// <param name="getType">1,缓存数据，2,表数据</param>
        /// <returns></returns>
        public AccountInfoDto AccountInfo(string hospitalId, EnumBaseDataGetType getType)
        {
            var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();

            Stopwatch sw = new Stopwatch();
            sw.Start();
            string url = $"/api/External/AccountInfo?hospitalId={hospitalId}&getType={getType.ToString()}";

            RestRequest request = new RestRequest(url);
            request.AddHeader("Authorization", token);
            var response = _clientH07.ExecuteGet<ResultDto>(request);
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用H07模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
            if (response.ErrorException != null)
            {
                Log.Error($"调用H07模块[{url}]发生错误:{response.ErrorException}");
                throw new Exception($"调用H07模块[{url}]发生错误:{response.ErrorException}", response.ErrorException);
            }
            else
            {
                if (response.Data.success)
                {
                    return JsonHelper.FromJson<AccountInfoDto>(response.Data.data.ToString());
                }
                else
                {
                    Log.Error($"调用H07模块[{url}]请求完成,但是返回了错误:" + response.Data.msg);
                    return null;
                }
            }
        }

        /// <summary>
        /// 获取设置值
        /// </summary>
        /// <param name="moduleId">模块ID</param>
        /// <param name="unitId">单元ID</param>
        /// <param name="unitType">单元类型</param>
        /// <param name="setupClass">设置分类</param>
        /// <param name="hospitalId">医疗机构ID</param>
        /// <param name="setupNo">设置ID</param>
        /// <param name="getType">1,缓存数据，2,表数据</param>
        /// <returns></returns>
        [UseCacheWhenFailed]
        public List<Lis5SetupDictCacheDto> GetLis5SetupDicts(string moduleId, string hospitalId, string unitId, string unitType, string setupClass, string setupNo, EnumBaseDataGetType getType)
        {
            var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();

            Stopwatch sw = new Stopwatch();
            sw.Start();
            string url = $"/api/External/GetLis5SetupDicts?moduleId={moduleId}&hospitalId={hospitalId}&unitId={unitId}&unitType={unitType}&setupClass={setupClass}&setupNo={setupNo}&getType={getType.ToString()}";

            RestRequest request = new RestRequest(url);
            request.AddHeader("Authorization", token);
            var response = _clientH07.ExecuteGet<ResultDto>(request);
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用H07模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
            if (response.ErrorException != null)
            {
                Log.Error($"调用H07模块[{url}]发生错误:{response.ErrorException}");
                throw new Exception($"调用H07模块[{url}]发生错误:{response.ErrorException}", response.ErrorException);
            }
            else
            {
                if (response.Data.success)
                {
                    return JsonHelper.FromJson<List<Lis5SetupDictCacheDto>>(response.Data.data.ToString());
                }
                else
                {
                    Log.Error($"调用H07模块[{url}]请求完成,但是返回了错误:" + response.Data.msg);
                    return null;
                }
            }
        }



        /// <summary>
        /// 最大值
        /// </summary>
        /// <param name="tableName"></param>
        /// <param name="addCount"></param>
        /// <param name="ifTableMax">1.默认redis最大值;2、表中最大值</param>
        /// <returns></returns>
        public ResultDto GetTableMaxPK(string tableName, string filedName, int addCount = 1, int ifTableMax = 1)
        {
            var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();

            Stopwatch sw = new Stopwatch();
            sw.Start();
            string url = $"/api/External/GetTableMax?tableName={tableName}&addCount={addCount}&ifTableMax={ifTableMax}&fieldName={filedName}";

            RestRequest request = new RestRequest(url);
            request.AddHeader("Authorization", token);
            var response = _clientH07.ExecuteGet<ResultDto>(request);
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用H07模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
            if (response.ErrorException != null)
            {
                Log.Error($"调用H07模块[{url}]发生错误:{response.ErrorException}");
                throw new Exception($"调用H07模块[{url}]发生错误:{response.ErrorException}", response.ErrorException);
            }
            else
            {
                if (response.Data.success)
                {
                    return response.Data;
                }
                else
                {
                    Log.Error($"调用H07模块[{url}]请求完成,但是返回了错误:" + response.Data.msg);
                    return null;
                }
            }
        }


        /// <summary>
        /// 获取主键最大值（精确到字段）
        /// </summary>
        /// <param name="tableName"></param>
        /// <param name="addCount"></param>
        /// <param name="ifTableMax">1.默认redis最大值;2、表中最大值</param>
        /// <param name="fieldName">主键字段名</param>
        /// <returns></returns>
        public ResultDto GetTableMax(string tableName, string fieldName = "", int addCount = 1, int ifTableMax = 1, string dBOwner = "XH_OA")
        {

            string id = IDGenHelper.CreateGuid();
            ResultDto dto = new ResultDto();
            dto.data = id;
            dto.success = true;
            return dto;
            //var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();

            //Stopwatch sw = new Stopwatch();
            //sw.Start();
            //string url = $"/api/External/GetTableMax?tableName={tableName}&addCount={addCount}&ifTableMax={ifTableMax}&fieldName={fieldName}&dBOwner={dBOwner}";

            //RestRequest request = new RestRequest(url);
            //request.AddHeader("Authorization", token);
            //var response = _clientH07.ExecuteGet<ResultDto>(request);
            //sw.Stop();
            //Log.ForContext("elapsed", sw.ElapsedMilliseconds)
            //    .Information($"调用H07模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
            //if (response.ErrorException != null)
            //{
            //    Log.Error($"调用H07模块[{url}]发生错误:{response.ErrorException}");
            //    throw new Exception($"调用H07模块[{url}]发生错误:{response.ErrorException}", response.ErrorException);
            //}
            //else
            //{
            //    if (response.Data.success)
            //    {
            //        return response.Data;
            //    }
            //    else
            //    {
            //        Log.Error($"调用H07模块[{url}]请求完成,但是返回了错误:" + response.Data.msg);
            //        return null;
            //    }
            //}
        }


        /// <summary>
        /// 获取主键最大值（精确到字段）
        /// </summary>
        /// <param name="tableName"></param>
        /// <param name="addCount"></param>
        /// <param name="ifTableMax">1.默认redis最大值;2、表中最大值</param>
        /// <param name="fieldName">主键字段名</param>
        /// <returns></returns>
        public ResultDto GetTableMaxNumber(string tableName, string fieldName = "", int addCount = 1, int ifTableMax = 1, string dBOwner = "XH_OA")
        {
            var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();

            Stopwatch sw = new Stopwatch();
            sw.Start();
            string url = $"/api/External/GetTableMax?tableName={tableName}&addCount={addCount}&ifTableMax={ifTableMax}&fieldName={fieldName}&dBOwner={dBOwner}";

            RestRequest request = new RestRequest(url);
            request.AddHeader("Authorization", token);
            var response = _clientH07.ExecuteGet<ResultDto>(request);
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用H07模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
            if (response.ErrorException != null)
            {
                Log.Error($"调用H07模块[{url}]发生错误:{response.ErrorException}");
                throw new Exception($"调用H07模块[{url}]发生错误:{response.ErrorException}", response.ErrorException);
            }
            else
            {
                if (response.Data.success)
                {
                    return response.Data;
                }
                else
                {
                    Log.Error($"调用H07模块[{url}]请求完成,但是返回了错误:" + response.Data.msg);
                    return null;
                }
            }
        }




        /// <summary>
        /// 错误后最大值处理（精确到字段）
        /// </summary>
        /// <param name="tableName"></param>
        /// <param name="ex"></param>
        /// <returns></returns>
        public ResultDto GetErrorTableMax(string tableName, Exception ex, string fieldName = "", string dBOwner = "XH_OA")
        {
            var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();

            Stopwatch sw = new Stopwatch();
            sw.Start();
            string url = $"/api/External/GetErrorTableMax?tableName={tableName}&ex={ex}&fieldName={fieldName}&dBOwner={dBOwner}";

            RestRequest request = new RestRequest(url);
            request.AddHeader("Authorization", token);
            var response = _clientH07.ExecuteGet<ResultDto>(request);
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用H07模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
            if (response.ErrorException != null)
            {
                Log.Error($"调用H07模块[{url}]发生错误:{response.ErrorException}");
                throw new Exception($"调用H07模块[{url}]发生错误:{response.ErrorException}", response.ErrorException);
            }
            else
            {
                if (response.Data.success)
                {
                    return null;
                }
                else
                {
                    Log.Error($"调用H07模块[{url}]请求完成,但是返回了错误:" + response.Data.msg);
                    return null;
                }
            }
        }




        /// <summary>
        /// 上传文件
        /// </summary>
        /// <param name="jsonStr"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public ResultDto UploadPathFile(string jsonStr)
        {
            ResultDto res = new ResultDto();
            var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();
            //byte[] fileBytes = System.Text.Encoding.UTF8.GetBytes(jsonStr);
            Stopwatch sw = new Stopwatch();
            sw.Start();
            string url = $"/api/Common/SetUploadPdf";
            RestRequest request = new RestRequest(url);
            request.AddHeader("Authorization", token);
            //S28接口入参样例：
            //{
            //"fileName":"8229e727d8404f01848a11e4459b81f7.pdf",
            //"src":"长长的base64",
            //"folderName":"PMS\\26",
            //"ifCover":true
            //}
            request.AddJsonBody(jsonStr);
            var response = _clientS28.ExecutePost<ResultDto>(request);
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
            .Information($"调用S28模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
            if (response.ErrorException != null)
            {
                Log.Error($"调用S28模块[{url}]发生错误:{response.ErrorException}");
                throw new Exception($"调用S28模块[{url}]发生错误:{response.ErrorException}", response.ErrorException);
            }
            else
            {
                if (response.Data.success)
                {
                    dynamic json = JsonConvert.DeserializeObject(Convert.ToString(response.Content));
                    res.success = json["success"];
                    res.data = json["data"].ToString();
                }
                else
                {
                    Log.Error($"调用S28模块[{url}]请求完成,但是返回了错误:" + response.Data.msg);
                    return null;
                }
            }
            return res;
        }

        /// <summary>
        /// 上传文件
        /// </summary>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public ResultDto UploadPathFormDataFile(string folderName, string FileName, byte[] bytes, bool iFCover = true)
        {
            if (folderName.IsNullOrEmpty() || FileName.IsNullOrEmpty())
            {
                throw new Exception("S28上传文件接口的文件名和路径参数不能为空！");
            }
            if (bytes == null || !bytes.Any())
            {
                throw new Exception("S28上传文件接口的文件名和路径参数不能为空！");
            }
            ResultDto res = new ResultDto();
            var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();

            Stopwatch sw = new Stopwatch();
            sw.Start();
            //S28接口入参样例：
            //{
            //"formFiles": 二进制数组           
            //"fileName":"8229e727d8404f01848a11e4459b81f7.pdf",
            //"folderName":"PMS_Assess_File",
            //"ifCover":true
            //}
            string url = $"/api/Common/SetUploadFile?folderName={folderName}";
            RestRequest restRequest = new RestRequest(url);
            restRequest.AddParameter("ifCover", iFCover, ParameterType.UrlSegment);
            restRequest.RequestFormat = DataFormat.Json;
            restRequest.Method = Method.Post;
            restRequest.AddHeader("Authorization", token);
            restRequest.AddHeader("Content-Type", "multipart/form-data");
            restRequest.AddFile("formFiles", bytes, FileName);//样例：restRequest.AddFile("formFiles", bytes, "8229e727d8404f01848a11e4459b81f7.pdf")
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
            .Information($"调用S28模块前[{url}],耗时:{sw.ElapsedMilliseconds}ms");

            sw = new Stopwatch();
            sw.Start();
            var response = _clientS28.ExecutePost<ResultDto>(restRequest);
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
            .Information($"调用S28模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
            if (response.ErrorException != null)
            {
                Log.Error($"调用S28模块[{url}]发生错误:{response.ErrorException}");
                throw new Exception($"调用S28模块[{url}]发生错误:{response.ErrorException}", response.ErrorException);
            }
            else
            {
                if (response.Data.success)
                {
                    res.success = response.Data.success;
                    JArray jarray = JArray.Parse(response.Data.data.ToString()); // 将JSON字符串转换为JArray对象
                    res.data = jarray[0]["UploadPath"].ToString();
                }
                else
                {
                    Log.Error($"调用S28模块[{url}]请求完成,但是返回了错误:" + response.Data.msg);
                    res.success = false;
                    res.msg = response.Data.msg;
                }
            }
            return res;
        }

        /// <summary>
        /// 删除上传文件
        /// </summary>
        /// <param name="jsonStr">删除的文件路径加文件名的列表，示例："[{\"path\":\"PMS/26/2a3660bbbaf147989519382d41f15e88.pdf\"}]"</param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public ResultDto DeleteUploadFile(string jsonStr)
        {
            var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();
            //byte[] fileBytes = System.Text.Encoding.UTF8.GetBytes(jsonStr);
            Stopwatch sw = new Stopwatch();
            sw.Start();
            string url = $"/api/Common/DeleteFile";
            RestRequest request = new RestRequest(url);
            request.AddHeader("Authorization", token);
            request.AddJsonBody(jsonStr);
            var response = _clientS28.ExecutePost<ResultDto>(request);
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
            .Information($"调用S28模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
            if (response.ErrorException != null)
            {
                Log.Error($"调用S28模块[{url}]发生错误:{response.ErrorException}");
                throw new Exception($"调用S28模块[{url}]发生错误:{response.ErrorException}", response.ErrorException);
            }
            else
            {
                if (response.Data.success)
                {
                    return response.Data;
                }
                else
                {
                    Log.Error($"调用S28模块[{url}]请求完成,但是返回了错误:" + response.Data.msg);
                    return null;
                }
            }
        }


        /// <summary>
        /// 调用115接口装载模板数据
        /// </summary>
        /// <param name="oaExcelFillDataDto"></param>
        /// <returns></returns>
        public ResultDto LoadExcelData(OaExcelFillDataDto oaExcelFillDataDto)
        {
            string logJson = JsonHelper.ToJson(oaExcelFillDataDto);
            //_logger.LogError("加载execl数据:" + logJson);
            ResultDto res = new ResultDto();
            var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();
            //byte[] fileBytes = System.Text.Encoding.UTF8.GetBytes(jsonStr);
            Stopwatch sw = new Stopwatch();
            sw.Start();
            string url = $"api/OO/LoadExcelData";
            RestRequest request = new RestRequest(url);
            request.AddHeader("Authorization", token);
            request.AddJsonBody(oaExcelFillDataDto);
            var response = _clientH115.ExecutePost<ResultDto>(request);
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
            .Information($"调用H115模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
            if (response.ErrorException != null)
            {
                Log.Error($"调用H115模块[{url}]发生错误:{response.ErrorException}");
                throw new Exception($"调用H115模块[{url}]发生错误:{response.ErrorException}", response.ErrorException);
            }
            else
            {
                if (response.Data.success)
                {
                    dynamic json = JsonConvert.DeserializeObject(Convert.ToString(response.Content));
                    res.success = json["success"];
                    res.data = json["data"].ToString();
                    res.data1 = json["data1"].ToString();
                    res.msg = json["msg"].ToString();
                    res.data2 = json["data2"].ToString();
                }
                else
                {
                    Log.Error($"调用H115模块[{url}]请求完成,但是返回了错误:" + response.Data.msg);
                    return null;
                }
            }
            return res;
        }


        /// <summary>
        /// 调用H115获取预览文件
        /// </summary>
        /// <param name="templateFillDataDto"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public byte[] PreviewFile(StyleTemplateFillDataDto templateFillDataDto)
        {
            var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();
            Stopwatch sw = new Stopwatch();
            sw.Start();
            string url = $"api/OO/PreviewFile";
            sw.Start();
            RestRequest request = new RestRequest(url);
            //request.AddHeader("Content-Type", "application/octet-stream");
            request.AddHeader("Authorization", token);
            request.AddBody(templateFillDataDto);
            try
            {
                var response = _clientH115.ExecutePost(request);

                sw.Stop();
                Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用H115模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
                if (response.ErrorException != null)
                {
                    Log.Error($"调用H115模块[{url}]发生错误:{response.ErrorException}");
                    throw new Exception($"调用H115模块[{url}]发生错误:{response.ErrorException}", response.ErrorException);
                }
                else
                {
                    var bytes = response.RawBytes;
                    return bytes;
                }
            }
            catch (Exception ex)
            {
                Log.Error($"调用H115模块[{url}]发生错误:{ex}");
                throw new Exception("预览失败!");
            }


        }

        /// <summary>
        ///  装载excel数据并导出excel文件(填充页眉页脚) 返回xlxs
        /// </summary>
        /// <param name="STYLE_ID">模板ID</param>
        /// <param name="FILE">前端导出的表格文件</param>
        /// <param name="FIELDS">页眉页脚数据项key:value  JSON</param>
        /// <returns></returns>
        public byte[] ExportExcelFile(string STYLE_ID, IFormFile FILE, string FIELDS)
        {
            var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();
            Stopwatch sw = new Stopwatch();
            sw.Start();
            string url = $"api/OO/ExportExcelFile?STYLE_ID={STYLE_ID}&&FIELDS={FIELDS}";
            sw.Start();
            RestRequest request = new RestRequest(url);
            byte[] bytes = null;
            using (var stream = FILE.OpenReadStream())
            {
                bytes = new byte[stream.Length];
                stream.Read(bytes, 0, bytes.Length);
                stream.Seek(0, SeekOrigin.Begin);
            }
            request.AddFile("FILE", bytes, FILE?.FileName);
            request.AddHeader("Authorization", token);
            try
            {
                var response = _clientH115.ExecutePost(request);
                sw.Stop();
                Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用H115模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
                if (response.ErrorException != null)
                {
                    Log.Error($"调用H115模块[{url}]发生错误:{response.ErrorException}");
                    throw new Exception($"调用H115模块[{url}]发生错误:{response.ErrorException}", response.ErrorException);
                }
                else
                {
                    var returnBytes = response.RawBytes;
                    return returnBytes;
                }
            }
            catch (Exception ex)
            {
                Log.Error($"调用H115模块[{url}]发生错误:{ex}");
                throw new Exception("加载失败!");
            }
        }

        /// <summary>
        /// 调用H115获取导出pdf文件
        /// </summary>
        /// <param name="templateFillDataDto"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public byte[] ExportStylePDFFile(StyleTemplateFillDataDto templateFillDataDto)
        {
            var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();
            Stopwatch sw = new Stopwatch();
            sw.Start();
            string url = $"api/OO/ExportStylePDFFile";
            sw.Start();
            RestRequest request = new RestRequest(url);
            //request.AddHeader("Content-Type", "application/octet-stream");
            request.AddHeader("Authorization", token);
            request.AddBody(templateFillDataDto);
            try
            {
                var response = _clientH115.ExecutePost(request);

                sw.Stop();
                Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用H115模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
                if (response.ErrorException != null)
                {
                    Log.Error($"调用H115模块[{url}]发生错误:{response.ErrorException}");
                    throw new Exception($"调用H115模块[{url}]发生错误:{response.ErrorException}", response.ErrorException);
                }
                else
                {
                    var bytes = response.RawBytes;
                    return bytes;
                }

            }
            catch (Exception ex)
            {
                Log.Error($"调用H115模块[{url}]发生错误:{ex}");
                throw new Exception("导出失败!");
            }


        }

        /// <summary>
        /// 导入工具箱设置
        /// </summary>
        /// <returns></returns>
        public ResultDto ProductInit()
        {
            ResultDto resultDto = new ResultDto();
            try
            {
                string fileName = "H81全部页面设置-false.xlsx";
                string key = BuilderKey("H81");
                Stopwatch sw = new Stopwatch();
                string url = $"/api/FileService/ProductInit?key={key}&moduleVer={AppSettingsProvider.CurrVersion}&ip={AppSettingsProvider.CurrClientIP}&mac={AppSettingsProvider.CurrClientMac}&initFlag=1";
                string path = Path.Combine(AppContext.BaseDirectory, "configs", fileName);
                RestRequest request = new RestRequest(url);
                if (File.Exists(path))
                {
                    Log.Information($"=========>>>开始初始化列表等设置信息");
                    request.AddFile("file", path);
                    var response = _clientH04.ExecutePost<ResultDto>(request);
                    sw.Stop();
                    Log.Information($"调用H04模块【{url}】耗时：{sw.ElapsedMilliseconds}ms");
                    if (response.ErrorException != null)
                    {
                        Log.Error($"调用H04模块【{url}】错误：{response.ErrorException}");
                        throw new Exception($"调用H04模块【{url}】错误：{response.ErrorException}", response.ErrorException);
                    }
                    else
                    {
                        if (response.Data.success)
                        {
                            string removeName = fileName.Replace("false", "true");
                            File.Move(path, Path.Combine(AppContext.BaseDirectory, "configs", removeName), true);
                            resultDto = JsonConvert.DeserializeObject<ResultDto>(response.Content.ToString());
                            Log.Information($"=========>>>软件模块【{"H81"}】列表等设置信息始化完成");
                            return resultDto;
                        }
                        else
                        {
                            Log.Error($"调用H04模块【{url}】请求完成，但是返回了错误：{response.Data.msg}");
                            throw new Exception($"调用H04模块【{url}】请求完成,但是返回了错误：{response.Data.msg}");
                        }
                    }
                }
                else
                {
                    string alreadyPath = Path.Combine(AppContext.BaseDirectory, "configs", "初始化excel", fileName.Replace("false", "true"));
                    if (File.Exists(alreadyPath))
                    {
                        Log.Information($"软件模块【{"H81"}】列表等相关设置已经初始化过");
                    }

                    return new ResultDto();
                }
            }
            catch (Exception e)
            {
                Log.Error($"初始化产品配置excel失败{e.Message}");
                return resultDto;
            }
        }

        /// <summary>
        /// 生成加密key
        /// </summary>
        /// <param name="moduleId">产品软件模块</param>
        /// <returns></returns>
        public string BuilderKey(string moduleId)
        {
            var requestDto = new LinkRequestDto();
            requestDto.moduleId = moduleId;
            requestDto.requestTime = DateTime.Now.ToComonTimeFormat();
            var key = SmxUtilsHelper.SM4UtilsEncrypt(AppSettingsProvider.PublicKey
                , JsonConvert.SerializeObject(requestDto));
            return key;
        }
        //////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////---

        /// <summary>
        /// 人员信息
        /// </summary>
        /// <returns></returns>
        public List<PMS_PERSON_INFO> GetPmsPersonInfo()
        {
            List<PMS_PERSON_INFO> pms_person_info = new List<PMS_PERSON_INFO>();
            pms_person_info = _soa.Db.Queryable<PMS_PERSON_INFO>().ToList();
            return pms_person_info;
        }

        /// <summary>
        /// 获取专业组信息
        /// </summary>
        /// <returns></returns>
        public List<SYS6_INSPECTION_PGROUP> GetInspectionPgroup()
        {
            var sys6_inspection_pgroup = _soa.Db.Queryable<SYS6_INSPECTION_PGROUP>().Where(p => p.PGROUP_STATE == "1")
                .Select<SYS6_INSPECTION_PGROUP>().ToList();
            return sys6_inspection_pgroup;
        }


        /// <summary>
        /// 获取科室对应人员下拉
        /// </summary>
        /// <param name="hospital_id"></param>
        /// <param name="lab_id"></param>
        /// <returns></returns>
        public List<DropDowDto> GetUserDropDowByLabId(string hospital_id, string lab_id)
        {
            List<DropDowDto> dropdowdto = new List<DropDowDto>();
            List<SYS6_USER> sys6_user = _soa.Db.Queryable<SYS6_USER>().Where(p => p.HOSPITAL_ID == hospital_id && p.LAB_ID == lab_id)
                .Select(s => new SYS6_USER
                {
                    USER_NO = s.USER_NO,
                    USERNAME = s.USERNAME,
                    LOGID = s.LOGID,
                    HIS_ID = s.HIS_ID
                }).ToList();
            foreach (var item in sys6_user)
            {
                string strValue = string.Empty;
                if (item.HIS_ID == null || item.HIS_ID == "")
                {
                    strValue = item.LOGID + "_" + item.USERNAME;
                }
                else
                {
                    strValue = item.HIS_ID + "_" + item.USERNAME;
                }
                dropdowdto.Add(new DropDowDto
                {
                    key = item.USER_NO,
                    value = strValue
                });
            }
            return dropdowdto;
        }

        /// <summary>
        /// 获取基础数据
        /// </summary>
        /// <returns></returns>
        public List<SYS6_BASE_DATA> GetSys6BaseData()
        {
            var listData = new List<SYS6_BASE_DATA>();
            try
            {
                if (!_cache.TryGetValue("SYS6_BASE_DATA", out listData) || listData == null)
                {
                    listData = _soa.Db.Queryable<SYS6_BASE_DATA>().Where(t => t.DATA_STATE == "1").OrderBy(t => t.DATA_SORT).ToList();
                    _cache.Set("SYS6_BASE_DATA", listData, TimeSpan.FromMinutes(30));
                }
            }
            catch { }
            return listData ?? new List<SYS6_BASE_DATA>();
        }
        public List<OA_BASE_DATA> GetOaBaseData()
        {
            var listData = new List<OA_BASE_DATA>();
            try
            {
                if (!_cache.TryGetValue("OA_BASE_DATA", out listData) || listData == null)
                {
                    listData = _soa.Db.Queryable<OA_BASE_DATA>().Where(t => t.STATE_FLAG == "1").OrderBy(t => t.DATA_SORT).ToList();
                    _cache.Set("OA_BASE_DATA", listData, TimeSpan.FromMinutes(30));
                }
            }
            catch { }
            return listData ?? new List<OA_BASE_DATA>();
        }

        public void ResetSys6BaseDataCache()
        {
            _cache.Remove("SYS6_BASE_DATA");
            GetSys6BaseData();
        }
        public void ResetOaBaseDataCache()
        {
            _cache.Remove("OA_BASE_DATA");
            GetOaBaseData();
        }

        public List<RecordRejectDto> GetRecordRejectInfo(List<string> person_ids)
        {
            List<RecordRejectDto> recordReject = new List<RecordRejectDto>();
            var pms_change_log = _soa.Db.Queryable<PMS_CHANGE_LOG>()
                .Where(p => p.OPERATE_TYPE.Contains("驳回"))
                .WhereIF(person_ids != null && person_ids.Any(), p => person_ids.Contains(p.PERSON_ID)).ToList();
            if (pms_change_log.Count() > 0)
            {
                foreach (var item in pms_change_log)
                {
                    recordReject.Add(new RecordRejectDto
                    {
                        CHANGE_CAUSE = item.CHANGE_CAUSE,
                        CHANGE_DATE = item.CHANGE_DATE.ToString(),
                        CHANGE_PERSON = item.CHANGE_PERSON,
                        CHANGE_TABLE = item.CHANGE_TABLE,
                        RECORD_ID=item.RECORD_ID,
                        REMARK = item.REMARK
                    });
                }
            }
            return recordReject;
        }

        /// <summary>
        /// 获取基础数据类型
        /// </summary>
        public List<BaseDataClassDto> GetBaseDataClassList()
        {
            var result = new List<BaseDataClassDto>();
            result = _soa.Db.Queryable<SYS6_BASE_DATA_CLASS>().Where(a => a.CLASS_STATE == "1" && (a.DATA_TABLE == "SYS6_BASE_DATA" || a.DATA_TABLE == "OA_BASE_DATA"))
                .Select(a => new BaseDataClassDto
                {
                    DATA_CLASS_ID = a.DATA_CLASS_ID,
                    DATA_TABLE = a.DATA_TABLE,
                    CLASS_ID = a.CLASS_ID,
                    CLASS_NAME = a.CLASS_NAME,
                    CLASS_SORT = a.CLASS_SORT,
                    CLASS_STATE = a.CLASS_STATE,
                    FIRST_RPERSON = a.FIRST_RPERSON,
                    FIRST_RTIME = a.FIRST_RTIME,
                    HOSPITAL_ID = a.HOSPITAL_ID,
                    LAST_MPERSON = a.LAST_MPERSON,
                    LAST_MTIME = a.LAST_MTIME,
                    REMARK = a.REMARK,
                })
                .ToList();
            result.ForEach(a => a.IS_EDITABLE = a.DATA_TABLE == "OA_BASE_DATA");//仅OA_BASE_DATA可编辑
            return result.OrderBy(a => a.FIRST_RTIME).ToList();
        }
        /// <summary>
        /// 修改基础数据类型
        /// </summary>
        public string SaveBaseDataClass(BaseDataClassDto dto, string hisName)
        {
            string classId = dto.DATA_CLASS_ID;
            SYS6_BASE_DATA_CLASS classEty = null;
            bool isInsert = false;
            if (classId.IsNullOrEmpty())
            {
                classEty = new SYS6_BASE_DATA_CLASS
                {
                    DATA_CLASS_ID = dto.CLASS_NAME,
                    CLASS_ID = dto.CLASS_NAME,
                    CLASS_STATE = "1",
                    DATA_TABLE = "OA_BASE_DATA",
                    DATA_FIELD = "DATA_NAME",
                    HOSPITAL_ID = dto.HOSPITAL_ID,
                    CLASS_SORT = DateTimeOffset.Now.ToUnixTimeSeconds().ToString(),
                    FIRST_RPERSON = hisName,
                    FIRST_RTIME = DateTime.Now,
                    ONE_CLASS = "实验室管理_用户端",
                };
                isInsert = true;
            }
            else
            {
                classEty = _soa.Db.Queryable<SYS6_BASE_DATA_CLASS>().Where(a => a.DATA_CLASS_ID == classId && a.CLASS_STATE == "1" && (a.DATA_TABLE == "SYS6_BASE_DATA" || a.DATA_TABLE == "OA_BASE_DATA")).First();
            }
            if (classEty != null)
            {
                dto.IS_EDITABLE = classEty.DATA_TABLE == "OA_BASE_DATA";
                if (dto.IS_EDITABLE == false)
                {
                    throw new Exception("该基础数据类型不可编辑，无法保存！");
                }
                classEty.CLASS_NAME = dto.CLASS_NAME;
                classEty.REMARK = dto.REMARK;
                classEty.LAST_MPERSON = hisName;
                classEty.LAST_MTIME = DateTime.Now;
                if (isInsert)
                    _soa.Db.Insertable(classEty).ExecuteCommand();
                else
                    _soa.Db.Updateable(classEty).UpdateColumns(a => new { a.CLASS_NAME, a.REMARK, a.LAST_MPERSON, a.LAST_MTIME }).ExecuteCommand();
                ResetOaBaseDataCache();
                ResetSys6BaseDataCache();
            }
            else
            {
                throw new Exception("找不到该基础数据类型，保存失败！");
            }
            return "保存成功！";
        }
        /// <summary>
        /// 删除基础数据类型
        /// </summary>
        public string DeleteBaseDataClass(string dataClassId, string hisName)
        {
            var classEty = _soa.Db.Queryable<SYS6_BASE_DATA_CLASS>().Where(a => a.DATA_CLASS_ID == dataClassId && (a.DATA_TABLE == "SYS6_BASE_DATA" || a.DATA_TABLE == "OA_BASE_DATA")).First();

            if (classEty != null)
            {
                bool IS_EDITABLE = classEty.DATA_TABLE == "OA_BASE_DATA";
                if (IS_EDITABLE == false)
                {
                    throw new Exception("该基础数据类型不可编辑，无法删除！");
                }
                classEty.CLASS_STATE = "2";
                classEty.LAST_MPERSON = hisName;
                classEty.LAST_MTIME = DateTime.Now;
                _soa.Db.Updateable(classEty).UpdateColumns(a => new
                {
                    a.CLASS_STATE,
                    a.LAST_MPERSON,
                    a.LAST_MTIME
                }).ExecuteCommand();
            }
            else
            {
                throw new Exception("找不到该基础数据类型，删除失败！");
            }
            return "删除成功！";
        }
        /// <summary>
        /// 获取基础数据
        /// </summary>
        /// <param name="classId"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public List<OA_BASE_DATA> GetOaBaseData(string classId, string stateFlag = "1")
        {
            List<OA_BASE_DATA> listData = _soa.GetRepository<OA_BASE_DATA>().DbSet()
                                         .Where(w => w.CLASS_ID == classId && w.MODULE_ID == "H81")
                                         .WhereIF(stateFlag.IsNotNullOrEmpty(), w => w.STATE_FLAG == stateFlag).ToList()
                                         .OrderBy(w => w.DATA_SORT?.Length).ThenBy(w => w.DATA_SORT).ToList();
            return listData;
        }
        /// <summary>
        /// 新增、修改实验管理基础数据
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public string AddOrModifyOaBaseData(OaBaseDataDto data)
        {
            DateTime dt = DateTime.Now;
            OA_BASE_DATA BData = _mapper.Map<OA_BASE_DATA>(data);
            var claim = _httpContext.HttpContext.User.ToClaimsDto();
            try
            {
                if (BData.DATA_ID.IsNotNullOrEmpty())
                {
                    var item = _soa.GetRepository<OA_BASE_DATA>().GetFirstOrDefault(w => w.DATA_ID == BData.DATA_ID);
                    if (item != null)
                    {
                        //Id不为空 => 修改
                        //item.DATA_SORT = BData.DATA_SORT;
                        item.DATA_NAME = BData.DATA_NAME;
                        item.REMARK = BData.REMARK;
                        item.LAST_MPERSON = claim.HIS_NAME;
                        item.LAST_MTIME = dt;
                        _soa.GetRepository<OA_BASE_DATA>().Update(item);
                    }
                    else
                    {
                        throw new Exception("该基础数据结果不存在！");
                    }
                }
                else
                {
                    BData.DATA_ID = "H81" + GetTableMaxNumber("OA_BASE_DATA", "DATA_ID", 1, 1, "XH_OA").data.ToString().PadLeft(8, '0');
                    BData.FIRST_RPERSON = claim.HIS_NAME;
                    BData.FIRST_RTIME = dt;
                    BData.LAST_MPERSON = claim.HIS_NAME;
                    BData.LAST_MTIME = dt;
                    BData.HOSPITAL_ID = claim.HOSPITAL_ID;
                    BData.MODULE_ID = "H81";// _configuration["ModuleId"];
                    BData.CLASS_ID = data.CLASS_ID;
                    BData.STATE_FLAG = "1";
                    BData.DATA_SORT = DateTimeOffset.Now.ToUnixTimeSeconds().ToString();

                    _soa.GetRepository<OA_BASE_DATA>().Insert(BData);
                }

                _soa.SaveChanges();
                //强制刷新缓存
                ResetSys6BaseDataCache();
                ResetOaBaseDataCache();
            }
            catch (Exception ex)
            {
                _logger.LogError("AddOrModifyOaBaseData异常信息: " + ex.Message);
                throw new Exception(message: "保存记录失败！");
            }
            return BData.DATA_ID;
        }


        /// <summary>
        /// 删除实验管理基础数据
        /// </summary>
        /// <param name="DataId"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public bool DelOaBaseData(OaBaseDataDto Data)
        {
            return SaveOaBaseData(Data, state: "2");
        }

        /// <summary>
        /// 启用实验管理基础数据
        /// </summary>
        /// <param name="Data"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public bool EnableOaBaseData(OaBaseDataDto Data)
        {
            return SaveOaBaseData(Data, state: "1");
        }

        /// <summary>
        /// 禁用实验管理基础数据
        /// </summary>
        /// <param name="Data"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public bool DisableOaBaseData(OaBaseDataDto Data)
        {
            return SaveOaBaseData(Data, state: "0");
        }

        private bool SaveOaBaseData(OaBaseDataDto Data, string state)
        {
            OA_BASE_DATA data = _soa.GetRepository<OA_BASE_DATA>().GetFirstOrDefault(o => o.CLASS_ID.Equals(Data.CLASS_ID) && o.DATA_ID.Equals(Data.DATA_ID));
            if (data != null)
            {
                var claim = _httpContext.HttpContext.User.ToClaimsDto();
                data.LAST_MPERSON = claim.HIS_NAME;
                data.LAST_MTIME = DateTime.Now;
                data.STATE_FLAG = state;
                _soa.Db.Updateable(data).ExecuteCommand();
            }
            else
            {
                throw new Exception("该基础数据结果不存在！");
            }
            return true;
        }

        /// <summary>
        /// 基础数据重新排序
        /// </summary>
        /// <param name="sortedData"></param>
        /// <returns></returns>
        public bool SortOaBaseData(List<OaBaseDataDto> sortedData)
        {
            if (!sortedData.Any())
                return true;
            var classId = sortedData.FirstOrDefault()?.CLASS_ID;
            var dataIds = sortedData.Select(a => a.DATA_ID).ToList();
            var BData = _soa.Db.Queryable<OA_BASE_DATA>().Where(w => w.CLASS_ID == classId && dataIds.Contains(w.DATA_ID)).ToList();
            foreach (var d in BData)
            {
                var data = sortedData.Find(a => a.DATA_ID == d.DATA_ID);
                int index = sortedData.IndexOf(data);
                if (index > -1)
                    d.DATA_SORT = index.ToString().PadLeft(10, '0');
            }
            int rows = _soa.Db.Updateable(BData).ExecuteCommand();
            return rows > 0;
        }

        /// <summary>
        /// 修改实验管理基础数据状态
        /// </summary>
        /// <param name="DataId"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public bool ForbidOaBaseData(string state, string DataId)
        {
            if (state != "0" && state != "1")
                throw new BizException("参数state有误！");

            OA_BASE_DATA data = _soa.GetRepository<OA_BASE_DATA>().GetFirstOrDefault(o => o.DATA_ID.Equals(DataId));
            if (data != null)
            {
                var ret = _soa.Db.Updateable<OA_BASE_DATA>()
                    .SetColumns(o => new OA_BASE_DATA
                    {
                        STATE_FLAG = state
                    }).Where(o => o.DATA_ID == DataId)
                .ExecuteCommand();
            }
            else
            {
                throw new Exception("评价结果不存在！");
            }
            return true;
        }

        /// <summary>
        /// 获取系统设置
        /// </summary>
        /// <param name="hospitalId"></param>
        /// <param name="areaId"></param>
        /// <returns></returns>
        public async Task<List<SetupValue>> GetSetupValue(string hospitalId,string areaId)
        {
            List<SetupValue> setupValues = new List<SetupValue>();

            //获取系统设置字典
            var sys6SetupDict = _soa.GetRepository<SYS6_SETUP_DICT>().Find(t => (t.MODULE_NO == AppSettingsProvider.CurrModuleId || t.MODULE_NO == "H00") && t.HOSPITAL_ID == "H0000" && t.SETUP_STATE == "1")
            .Select(t => new SYS6_SETUP_DICT
            {
                SETUP_NO = t.SETUP_NO,
                DEFAULT_VALUE = t.DEFAULT_VALUE,
                CHOICE_VALUE = t.CHOICE_VALUE,
                SETUP_NAME = t.SETUP_NAME,
                SETUP_CONTENT = t.SETUP_CONTENT
            }).ToList();

            //获取系统设置单元
            var sys6Setup = _soa.Db.Queryable<SYS6_SETUP, SYS6_SETUP_DICT>((a, b) => new JoinQueryInfos(
                JoinType.Inner, a.SETUP_NO == b.SETUP_NO
            )).Where((a, b) => b.SETUP_STATE == "1" && (b.MODULE_NO == AppSettingsProvider.CurrModuleId || b.MODULE_NO == "H00"))
            .Select((a, b) => new SYS6_SETUP
            {
                HOSPITAL_ID = a.HOSPITAL_ID,
                UNIT_ID = a.UNIT_ID,
                UNIT_TYPE = a.UNIT_TYPE,
                SETUP_ID = a.SETUP_ID,
                SETUP_VALUE = a.SETUP_VALUE,
                SETUP_NO = a.SETUP_NO,
                REMARK = b.CHOICE_VALUE
            }).ToList();

            //院区级
            var areaSetup = sys6Setup.Where(t => t.UNIT_TYPE == "10" && t.UNIT_ID == areaId);
            //机构级
            var hosSetup = sys6Setup.Where(t => t.UNIT_TYPE == "1" && t.UNIT_ID == hospitalId);

            var tempAllSetup = areaSetup?.Union(hosSetup)?? new List<SYS6_SETUP>();
            var groupedSetup = tempAllSetup.GroupBy(t => t.SETUP_NO);
            //进行排序
            var tempGroupedSetup = groupedSetup.Select(t => t.OrderBy(x => (x.GetUnitTypeSort())));
            //每个设置取优先级最高数据
            var SetupList = tempGroupedSetup.Select(t => t.First());
            setupValues.AddRange(sys6SetupDict.Select(x => new SetupValue
            {
                SETUP_NO = x.SETUP_NO,
                SETUP_VALUE = (SetupList.Where(t => t.SETUP_NO == x.SETUP_NO)?.FirstOrDefault()?.SETUP_VALUE).CastNullEmptyToString(x.DEFAULT_VALUE),
                SETUP_CONTENT = x.SETUP_CONTENT,
                SETUP_NAME = x.SETUP_NAME
            }));

            return setupValues;
        }




        public string GetSkillCertificateName(PMS_SKILL_CERTIFICATE_LIST cert)
        {
            string class_id = "人事技能证书类型";
            string className = string.Empty;
            List<OA_BASE_DATA> oaBaseData =GetOaBaseData();
            if (cert.CERTIFICATE_DID.IsNotNullOrEmpty())
            {
                string cerTypeID = _soa.Db.Queryable<Models.Entities.EvaluatePlan.OA_CERTIFICATE_DICT>().Where(a => a.CERTIFICATE_DID == cert.CERTIFICATE_DID).First()?.CERTIFICATE_DTYPE;
                if (cerTypeID.IsNotNullOrEmpty())
                {
                    className = oaBaseData.Where(x => x.CLASS_ID == class_id && x.DATA_ID == cerTypeID).FirstOrDefault()?.DATA_NAME;
                }
            }
            else if (cert.CERTIFICATE_TYPE.IsNotNullOrEmpty())
            {
                className = oaBaseData.Where(x => x.CLASS_ID == class_id && x.DATA_ID == cert.CERTIFICATE_TYPE).FirstOrDefault()?.DATA_NAME;
            }
            return className;

        }

        /// <summary>
        ///获取人事基础数据名称(人事系统自行控制)
        /// </summary>
        /// <param name="class_id"></param>
        /// <param name="data_id"></param>
        /// <returns></returns>
        public string RecordClassOaBaseName(string class_id, string data_id)
        {
            string className = null;
            if (data_id != null)
            {
                List<OA_BASE_DATA> lis5_base_data =GetOaBaseData(class_id);
                if (lis5_base_data.Where(x => x.DATA_ID == data_id).FirstOrDefault() != null)
                {
                    className = lis5_base_data.Where(x => x.DATA_ID == data_id).FirstOrDefault()?.DATA_NAME;
                }
            }
            return className.IsNotNullOrEmpty() ? className : data_id;
        }

        /// <summary>
        ///获取基础数据名称
        /// </summary>
        /// <param name="class_id"></param>
        /// <param name="data_id"></param>
        /// <returns></returns>
        public string RecordClassBaseName(string class_id, string data_id)
        {
            string className = string.Empty;
            if (data_id != null)
            {
                List<SYS6_BASE_DATA> lis5_base_data = GetSys6BaseData();
                if (lis5_base_data.Where(x => x.CLASS_ID == class_id && x.DATA_ID == data_id).FirstOrDefault() != null)
                {
                    className = lis5_base_data.Where(x => x.CLASS_ID == class_id && x.DATA_ID == data_id).FirstOrDefault().DATA_CNAME;
                }
            }
            return className;
        }


        /// <summary>
        /// 获取考试分类信息
        /// </summary>
        /// <param name="hospital_id">医疗机构id</param>
        /// <returns></returns>
        public List<OA_DATA_CLASS> GetExamClassInfo(string? hospital_id)
        {
            List<OA_DATA_CLASS> listClass = new List<OA_DATA_CLASS>();
            try
            {
                listClass = _soa.Db.Queryable<OA_DATA_CLASS>()
                    .Where(p => p.HOSPITAL_ID == hospital_id && p.DATA_CLASS_STATE == "1" && p.DATA_CLASS_TYPE == "考试分类")
                    .ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError($"GetExamClassInfo:{ex.ToString()}");
            }
            return listClass;
        }
    }
}
