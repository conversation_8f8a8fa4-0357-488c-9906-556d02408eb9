﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace XH.H81.Models.Entities.Pms
{
    [DBOwner("XH_OA")]
    public class SYS_CLASS_INFO
    {
        [Key]
        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string MODULE_CLASSID { get; set; }
        //
        public string? CLASS_TYPE { get; set; }
        //
        public string? HOSPITAL_ID { get; set; }
        //
        public string? MODULE_ID { get; set; }
        //
        public string? MODULE_NAME { get; set; }
        //
        public string? CLASS_NAME { get; set; }
        //
        public string? CLASS_SORT { get; set; }
        //
        public string? IF_BROWSE { get; set; }
        //
        public string? CLASS_DES { get; set; }
        //
        public string? ARCHIVE_TABLE { get; set; }
        //
        public string? CLASS_STATE { get; set; }
        //
        public string? FIRST_RPERSON { get; set; }
        //
        public string? FIRST_RTIME { get; set; }
        //
        public string? LAST_MPERSON { get; set; }
        //
        public string? LAST_MTIME { get; set; }
        //
        public string? REMARK { get; set; }
        /// <summary>
        /// 是否支持附件上传：'1'是支持，'0'是不支持
        /// </summary>
        public string? IF_UPLOAD_FILE { get; set; }


    }
}
