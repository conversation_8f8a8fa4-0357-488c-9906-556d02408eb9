﻿using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace XH.H81.Models.Dtos.IoTDevices.Access;
/// <summary>
/// 门禁授权周计划
/// </summary>
public class PlatformAccessWeekPlanDto
{
    /// <summary>
    /// ID 编号，最好从1自增
    /// </summary>
    [JsonProperty("id")]
    public string Id { get; set; }
    /// <summary>
    /// 模板名称
    /// </summary>
    [JsonProperty("name")]
    public string Name { get; set; }
    /// <summary>
    /// 是否开启：true生效
    /// </summary>
    [JsonProperty("enable")]
    public bool Enable { get; set; }
    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("week")]
    [JsonConverter(typeof(StringEnumConverter))]
    public AccessWeekTypeEnum Week { get; set; }

    [JsonProperty("weekId")]
    public string WeekId { get; set; }

    [JsonProperty("weekEnable")]
    public string WeekEnable { get; set; }


    /// <summary>
    /// 开始时间段：HH:mm:ss
    /// </summary>
    [JsonProperty("beginTime")]
    public string BeginTime { get; set; }

    /// <summary>
    /// 结束时间段：HH:mm:ss，不能小于开始时间段
    /// </summary>
    [JsonProperty("endTime")]
    public string EndTime { get; set; }
}