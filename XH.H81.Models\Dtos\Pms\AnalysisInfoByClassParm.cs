﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Dtos.Pms
{
    public class AnalysisInfoByClassParm
    {
        public string? AREA_ID { get; set; }

        public string? PGROUP_ID { get; set; }

        public string ARCHIVE_TABLE { get; set; }
        public string? SMBL_FLAG { get; set; }

        public string? DATE_TYPE { get; set; }

        public string? START_DATE { get; set; }
        public string? END_DATE { get; set; }

        public string? SEARCH_NAME { get; set; }

        public List<string>? LIST_PERSONID { get; set; }

        public string? ID_NAME { get; set; }


        /// <summary>
        /// 状态
        /// </summary>
        public string? STATUS { get; set; }
        public string? tagId { get; set; }
    }
}
