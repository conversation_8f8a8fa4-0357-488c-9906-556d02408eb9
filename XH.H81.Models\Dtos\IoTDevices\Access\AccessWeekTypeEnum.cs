﻿using System.ComponentModel;

namespace XH.H81.Models.Dtos.IoTDevices.Access;

/// <summary>
/// 周末枚举
/// </summary>
public enum AccessWeekTypeEnum
{
    /// <summary>
    /// 周一
    /// </summary>
    /// 
    [Description("Monday")]
    Monday = 1,
    /// <summary>
    /// 周二
    /// </summary>
    [Description("Tuesday")]
    Tuesday = 2,
    /// <summary>
    /// 周三
    /// </summary>
    [Description("Wednesday")]
    Wednesday = 3,
    /// <summary>
    /// 周四
    /// </summary>
    [Description("Thursday")]
    Thursday = 4,

    /// <summary>
    /// 周五
    /// </summary>
    [Description("Friday")]
    Friday = 5,

    /// <summary>
    /// 周六
    /// </summary>
    [Description("Saturday")]
    Saturday = 6,

    /// <summary>
    /// 周日
    /// </summary>
    [Description("Sunday")]
    Sunday = 7


}