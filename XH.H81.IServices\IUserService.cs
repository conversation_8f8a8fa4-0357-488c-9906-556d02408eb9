﻿using H.Utility;
using System.Linq.Expressions;
using XH.H81.Models.Dtos;
using XH.H81.Models.Dtos.Pms;
using XH.H81.Models.Entities.Pms;
using XH.H81.Models.ExternalEntity;
using XH.LAB.UTILS.Models;
using XH.LAB.UTILS.Models.Dto;

namespace XH.H81.IServices.Pms
{
    public interface IUserService
    {  
        ////统一的人员过滤条件（尽量使用）
        //Expression<Func<PMS_PERSON_INFO, bool>> StandardLabPersonFilter { get; }
        PersonInfoDto GetPersonInfoById(string person_id, string user_no, string hospital_id,string load_mode);

        PersonInfoDto GetPersonInfoByPhoneNum(string phone_num, string hospital_id, string user_name);

        ResultDto GetPersonDropDownInfo(string hospital_id);

        ResultDto GetPersonStructureInfo_old(string hospital_id, string lab_id, string area_id, string user_id);

        ResultDto GetPersonStructureInfo(string hospital_id, string lab_id, string area_id, string smblLabId, string user_id);

        ResultDto GetPersonStructureInfo_SMBL(string hospital_id, string lab_id, string area_id, string smblLabId, string user_id);

        ResultDto GeneratePersonFileInfo(string person_id,string hospital_id);

        List<PMS_PERSON_INFO> GetAllPersonInfo();

        List<PersonInfoDto> GetAllPersonInfoByHospitalId(string hospital_id);

        IEnumerable<InspectionPgroupDto> GetPersonInfoTree(string hospital_id, string lab_id, string pgroup_id, string person_name, string user_no);

       //OrgTree GetGroupPersonTree(OrgUserParams orgParm, string person_name, string permissMenuId, bool isIncludeAll = false);

        OrgTree GetAreaGroupDropDownList(OrgUserParams parm, string permissMenuId);

        List<CommboxInfo> GetOtherTypeDropDwon(string labId,string hospitalId);
        OrgTree GetSMBLPersonTree_Lab(OrgUserParams orgParm, string person_name, string permissMenuId, bool isClearZero = true, bool ifCheckPermission = true);
        OrgTree GetSMBLPersonTree_Post(OrgUserParams orgParm, string person_name, string permissMenuId, string postId);
        OrgTree GetISOPersonTree_Lab(OrgUserParams orgParm, string person_name, string permissMenuId);
        OrgTree GetISOLabPersonTree(OrgUserParams orgParm, string person_name, string permissMenuId, OrgTreeNode topNode, bool ifCheckPremission = true);
        OrgTree GetISOPersonTree_Area(OrgUserParams orgParm, string person_name, string permissMenuId);
        OrgTree GetISOPersonTree_Other(OrgUserParams orgParm, string person_name, string permissMenuId, string personType);
        OrgTree GetISOPersonTree_LabOther(OrgUserParams orgParm, string person_name, string permissMenuId);
        OrgTree GetSmblLabTree(OrgUserParams orgParm, string permissMenuId);
        OrgTree GetSmblPostTree(OrgUserParams orgParm, string permissMenuId);
        OrgTree GetSmblDeptTree(OrgUserParams orgParm, string permissMenuId, string smblDeptNodeId, string deptKeyWord);
        OrgTree GetSmblDeptPostPersonTree(string hospitalId, string smblDeptId, string smblPostId, string postKeyword, string personKeyword);


        /// <summary>
        /// 根据专业组ID获取人员列表
        /// </summary>
        /// <param name="pgroupId"></param>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        List<PersonInfoDto> GetPersonList(string pgroupId, string hospitalId);

        /// <summary>
        /// 加载execl数据
        /// </summary>
        /// <param name="loadOfficeDto">查询dto</param>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        ResultDto LoadPersonExcelData(LoadOfficeDto loadOfficeDto, string hospitalId);

        /// <summary>
        /// 预览人员模板
        /// </summary>
        /// <param name="styleId">模板id</param>
        /// <param name="personId">人员id</param>
        /// <param name="hospitalId">机构id</param>
        /// <returns></returns>
        MemoryStream PreviewPersonTemplate(string styleId, string personId, string hospitalId);

        /// <summary>
        /// 导出模板文件
        /// </summary>
        /// <param name="styleId"></param>
        /// <param name="personId"></param>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        MemoryStream ExportPersonTemplate(string styleId, string personId, string hospitalId);

        /// <summary>
        /// 根据人员id和分类id合集获取全部附件
        /// </summary>
        /// <param name="archiveTables"></param>
        /// <param name="personId"></param>
        /// <returns></returns>
        List<PMS_PERSON_FILE> GetRecordFileByPersonId(string archiveTables, string personId);

        /// <summary>
        /// 获取院区列表
        /// </summary>
        /// <param name="LabId"></param>
        /// <returns></returns>
        List<SYS6_INSPECTION_AREA> GetAreaList(string hospitalId, string labId);

        OrgTree GetHistoryPersonTree_SMBL_1(HistoryPersonTreeParm parm);
    }
}
