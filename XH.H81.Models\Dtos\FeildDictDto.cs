﻿using AutoMapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XH.H81.Models.ExternalEntity;

namespace XH.H81.Models.Dtos
{
    [AutoMap(typeof(SYS6_FEILD_DICT))]
    public class FeildDictDto
    {
        public string FIELD_ID { get; set; }
        public string FIELD_CODE { get; set; }
        public string FIELD_NAME { get; set; }
        public string FIELD_VALUE { get; set; }
        public string TABLE_NAME { get; set; }
        public string FEILD_FORMAT { get; set; }

        public string FIELD_SORT { get; set; }
    }
}
