﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Dtos
{
    public class LoadOfficeDto
    {
        /// <summary>
        /// 科室id
        /// </summary>
        public string? labId { get; set; }

        /// <summary>
        /// 院区id
        /// </summary>
        public string? areaId { get; set; }

        /// <summary>
        /// 专业组id
        /// </summary>
        public string? pgroupId { get; set; }


        /// <summary>
        /// 多个专业组id合集
        /// </summary>
        public string? pgroupIds { get; set; }

        /// <summary>
        /// 管理专业id
        /// </summary>
        public string? mgroupId { get; set; }

        /// <summary>
        /// 人员id
        /// </summary>
        public string? personId { get; set; }


        /// <summary>
        /// 人员姓名
        /// </summary>
        public string? personName { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public string? startDate { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public string? endDate { get; set; }

        /// <summary>
        /// 模板id
        /// </summary>
        public string styleId { get; set; }

        /// <summary>
        /// 时间类型
        /// </summary>
        public string? dateType { get; set; }
    }
}
