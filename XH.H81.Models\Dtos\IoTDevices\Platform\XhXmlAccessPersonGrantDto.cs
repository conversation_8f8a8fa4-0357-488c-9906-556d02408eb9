﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Dtos.IoTDevices.Platform
{
    public class XhXmlAccessPersonGrantDto : XhIotRoot
    { /// <summary>
      /// 生物安全码
      /// </summary>
       [JsonProperty("bioSafeCode", NullValueHandling = NullValueHandling.Ignore)]
        public string BioSafeCode { get; set; }

        /// <summary>
        /// 编码
        /// </summary>
        [JsonProperty("code")]
        public string Code { get; set; }

        /// <summary>
        /// 可下载头像文件地址，由于海康门禁限制，图像不能超过200k，可填base64,对接时约定,data:image/jpeg;base64,不用带入
        /// </summary>
        [JsonProperty("filePath")]
        public string FilePath { get; set; }

        /// <summary>
        /// 身份证
        /// </summary>
        [JsonProperty("idCard", NullValueHandling = NullValueHandling.Ignore)]
        public string? IdCard { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        [JsonProperty("name")]
        public string Name { get; set; }

        /// <summary>
        /// 手机号
        /// </summary>
        [JsonProperty("phone", NullValueHandling = NullValueHandling.Ignore)]
        public string? Phone { get; set; }

        /// <summary>
        /// 授权信息集合
        /// </summary>
        [JsonProperty("roomPlans")]
        public List<RoomPlan> RoomPlans { get; set; }
        [JsonProperty("sns" , NullValueHandling = NullValueHandling.Ignore)]
        public List<string> Sns { get; set; }

        /// <summary>
        /// 性别：0女1男
        /// </summary>
        [JsonProperty("sex", NullValueHandling = NullValueHandling.Ignore)]
        public long? Sex { get; set; }

        /// <summary>
        /// 类型：2访客，3实验室人员，4医院临工
        /// </summary>
       [JsonProperty("type")]
        public long Type { get; set; }

        /// <summary>
        /// 杏和id
        /// </summary>
        [JsonProperty("userId")]
        public string UserId { get; set; }
    }

    public  class RoomPlan
    {
        /// <summary>
        /// 结束时间，不传默认30年, yyyy-MM-dd HH:mm:ss
        /// </summary>
        [JsonProperty("endTime", NullValueHandling = NullValueHandling.Ignore)]
        public string? EndTime { get; set; }

        /// <summary>
        /// 门禁计划id
        /// </summary>
        [JsonProperty("planId")]
        public string PlanId { get; set; }

        /// <summary>
        /// 房间id
        /// </summary>
      [JsonProperty("roomId")]
        public long RoomId { get; set; }

        /// <summary>
        /// 开始时间，不传默认30年, yyyy-MM-dd HH:mm:ss
        /// </summary>
        [JsonProperty("startTime", NullValueHandling = NullValueHandling.Ignore)]
        public string? StartTime { get; set; }
    }
}

