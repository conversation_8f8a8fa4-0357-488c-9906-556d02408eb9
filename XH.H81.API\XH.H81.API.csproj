﻿<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net6.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <UserSecretsId>aa5482e1-df92-433a-b2e8-b0e0610f3989</UserSecretsId>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
        <GenerateDocumentationFile>True</GenerateDocumentationFile>
        <ApplicationIcon>icon.ico</ApplicationIcon>
        <Authors>Copyright 杏和软件®</Authors>
        <Version>6.25.329</Version>
        <FileVersion>6.25.329</FileVersion>
        <GeneratePackageOnBuild>True</GeneratePackageOnBuild>
    </PropertyGroup>

    <ItemGroup>
        <Compile Remove=".template.config\**" />
        <Content Remove=".template.config\**" />
        <EmbeddedResource Remove=".template.config\**" />
        <None Remove=".template.config\**" />
        <None Remove="configs\SqlXml\基础数据.xml" />
        <None Remove="ExampleFile\07041d1e7b184ef1b6622943c7a39933.PDF" />
        <None Remove="ExampleFile\8e86da8481dd423ba9b0348132560037.PDF" />
        <None Remove="ExampleFile\9a1f08bba278409b9e1446d7b4a566f7.PDF" />
        <None Remove="ExampleFile\a1bdb614792846cab4d2fc0202062ff8.PDF" />
        <None Remove="ExampleFile\f76ca9e00105438f810e6c358d6ef320.pdf" />
        <None Remove="NPOI" />
        <None Remove="Npoi.Mapper" />
    </ItemGroup>

    <ItemGroup>
        <Content Include="configs\SqlXml\基础数据.xml">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </Content>
        <Content Include="icon.ico" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Autofac.Extras.DynamicProxy" Version="6.0.1" />
        <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.14.0" />
        <PackageReference Include="Namotion.Reflection" Version="2.1.1" />

        <PackageReference Include="NPOI" Version="2.6.0" />
        <PackageReference Include="Npoi.Mapper" Version="6.0.0" />
        <PackageReference Include="SkiaSharp" Version="2.88.8" />
        <PackageReference Include="SkiaSharp.NativeAssets.Linux" Version="2.88.7" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\XH.H81.Base\XH.H81.Base.csproj" />
        <ProjectReference Include="..\XH.H81.IServices\XH.H81.IServices.csproj" />
        <ProjectReference Include="..\XH.H81.Models\XH.H81.Models.csproj" />
        <ProjectReference Include="..\XH.H81.Services\XH.H81.Services.csproj" />
    </ItemGroup>

    <ItemGroup>
        <EmbeddedResource Include="swagger.html">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </EmbeddedResource>
    </ItemGroup>

    <ItemGroup>
        <None Update="CodeConfig.xml">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="configs\H81全部页面设置-停用.xlsx">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="configs\InitData\InitData__人员管理6.25.225初始化数据.txt">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="configs\InitData\InitData__人员管理6.25.300导入数据.txt">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="configs\InitData\InitData__人员管理6.25.329导入数据.txt">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="configs\InitData\备份InitData__人员管理6.25.225初始化数据.txt">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </None>
        <None Update="configs\InitData\备份InitData__人员管理6.25.300导入数据.txt">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </None>
        <None Update="configs\SqlXml\Test1.xml">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="DbUpdate\2024-02.txt">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="DbUpdate\2024-03.txt">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="DbUpdate\2024-05.txt">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="DbUpdate\2024-06.txt">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="DbUpdate\2024-07.txt">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="DbUpdate\2024-08.txt">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="DbUpdate\2024-09.txt">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="DbUpdate\2024-11.txt">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="DbUpdate\2024-10.txt">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="DbUpdate\DeleteSql.txt">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="configs\H81-全部页面设置-初始化导入.xlsx">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="DbUpdate\人事管理H81-DbInit.txt">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="DbUpdate\多机构初始脚本.txt">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="Docs\人事档案预览分类和字段展示配置.docx">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="ExampleFile\default.jpg">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="ExampleFile\file.png">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="ExampleFile\icon.png">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="ExampleFile\PersonInfo - 横向.docx">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="ExampleFile\PersonInfo - 纵向.docx">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="ExampleFile\PersonInfo.docx">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="ExampleFile\watermark.png">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="update_log.md">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="xhdevcert.pfx">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="复杂表单模板备份\ISO的FORM_COL_JSON.txt">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="复杂表单模板备份\ISO的FORM_JSON.txt">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="复杂表单模板备份\生安的FORM_COL_JSON.txt">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="复杂表单模板备份\生安的FORM_JSON.txt">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="服务注册.bat">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
    </ItemGroup>

    <ItemGroup>
        <Folder Include="Middleware\" />
    </ItemGroup>

    <ProjectExtensions>
        <VisualStudio><UserProperties configs_4appsettings_1json__JsonSchema="" properties_4launchsettings_1json__JsonSchema="" /></VisualStudio>
    </ProjectExtensions>

</Project>
