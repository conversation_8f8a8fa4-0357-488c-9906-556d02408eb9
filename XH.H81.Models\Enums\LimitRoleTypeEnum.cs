﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Enums
{
    /// <summary>
    /// 规评方案适用类型 (1-科室通用, 2-管理专业组通用, 3-检验专业组通用, 4-岗位通用, 5-适用岗位角色)
    /// </summary>
    public enum LimitRoleTypeEnum
    {

        /// <summary>
        /// 无效
        /// </summary>
        [Description("无效")]
        DISABLE = 0,
        /// <summary>
        /// 限权
        /// </summary>
        [Description("限权")]
        LIMIT = 1,
        /// <summary>
        /// 停岗
        /// </summary>
        [Description("停岗")]
        STOP = 2,
        /// <summary>
        /// 取消限权
        /// </summary>
        [Description("取消限权")]
        CANCEL_LIMIT = -1,
        /// <summary>
        /// 取消停岗
        /// </summary>
        [Description("取消停岗")]
        CANCEL_STOP = -2,
    }

}
