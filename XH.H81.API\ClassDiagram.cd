﻿<?xml version="1.0" encoding="utf-8"?>
<ClassDiagram MajorVersion="1" MinorVersion="1">
  <Class Name="XH.H81.API.Controllers.DmisController">
    <Position X="16.25" Y="0.5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAgAAAAAAAAAAAAAIEAAAAAAAAAAAAAAAAgAACBAAAg=</HashCode>
      <FileName>Controllers\DmisController.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="XH.H81.API.Controllers.EguardControlController">
    <Position X="4" Y="0.5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>CAAyEEQAJgACcAJgGAABAQqAIsCAAgCBIAICCIBAQAA=</HashCode>
      <FileName>Controllers\EguardControlController.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="XH.H81.API.Controllers.EvaluatePlanController">
    <Position X="7.5" Y="0.5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>BQAUIIISRAAABIgAAIBRooA4AAMAAQAUAUQhQAAVAUw=</HashCode>
      <FileName>Controllers\EvaluatePlanController.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="XH.H81.API.Controllers.ExamController">
    <Position X="18" Y="0.5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AABAICAAAAAAAAAAAAAAAABIABAACAAAIAAAgAAABAg=</HashCode>
      <FileName>Controllers\ExamController.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="XH.H81.API.Controllers.ModuleLabGroupController">
    <Position X="19.75" Y="0.5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAIAEIQACAAAQiIAQEABAAAAAAAEBAACAAAABI=</HashCode>
      <FileName>Controllers\ModuleLabGroupController.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="XH.H81.API.Controllers.OfficeExcelController">
    <Position X="18" Y="4.25" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAEAAAAAAAAgAAgAAAAEIAAADAAAgAAAAAAAAA=</HashCode>
      <FileName>Controllers\OfficeExcelController.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="XH.H81.API.Controllers.PageSettingController">
    <Position X="16.25" Y="3.5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAACABIAAAACICBAAJAAAAAAAAAAAAAAAACAgAg=</HashCode>
      <FileName>Controllers\PageSettingController.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="XH.H81.API.Controllers.PmsAssessController">
    <Position X="11" Y="3.75" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAQAAQAAAAAAAAAAAAAAgAoAAAEAAAAAAAAEAABAAg=</HashCode>
      <FileName>Controllers\PmsAssessController.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="XH.H81.API.Controllers.PmsController" Collapsed="true">
    <Position X="0.5" Y="5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>CEQcgAFIQ6GJFExUJAABEDBBWiAAGADDEAEAhCgIgUk=</HashCode>
      <FileName>Controllers\PmsController.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="XH.H81.API.Controllers.User">
    <Position X="21.5" Y="5.25" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAEgAAAAAAAAAAAAAEAAAAAAAAAAAAAAA=</HashCode>
      <FileName>Controllers\PmsController.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="XH.H81.API.Controllers.PmsTagController">
    <Position X="5.75" Y="0.5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>IFAEQKAWRBQAAAAUQLAABhACEACiACEAAYCAGBgQhCg=</HashCode>
      <FileName>Controllers\PmsTagController.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="XH.H81.API.Controllers.TestController">
    <Position X="0.75" Y="2.5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>ChIIAAkAAAAABAgAAAAhCAAAAAAIAAAAQAUAABAAIAA=</HashCode>
      <FileName>Controllers\TestController.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="XH.H81.API.Controllers.UserController">
    <Position X="9.25" Y="0.5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>BAABASIIAAAAAAAgAAIAICACIgEgACEAAQAASAwAgCg=</HashCode>
      <FileName>Controllers\UserController.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="XH.H81.API.Controllers._demos.BaseDataDemoController">
    <Position X="11" Y="0.5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>CAQAAAAAAAAAAAAIAAAAQAABAQAABAADAAAAAAAAAAA=</HashCode>
      <FileName>Controllers\_Demos\BaseDataDemoController.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="XH.H81.API.Controllers._demos.CacheTestController">
    <Position X="12.75" Y="0.5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAEAAAAAAAAAAAAAAAEEAAAAIAQAAAACAAAAwEAAAAA=</HashCode>
      <FileName>Controllers\_Demos\CacheTestController.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="XH.H81.API.Controllers._demos.DemoController">
    <Position X="14.5" Y="0.5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>CAhAAAAAAACAAAAAAAAAAAIAAAgAACAAAAAAAAAQAgA=</HashCode>
      <FileName>Controllers\_Demos\DemoController.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="XH.H81.API.Controllers._demos.DemoGetConfigController">
    <Position X="14.5" Y="4.25" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAQAAAAAAAAAAAAAAAAAAAAAACCAAAAAAAAAAAA=</HashCode>
      <FileName>Controllers\_Demos\DemoGetConfigController.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="XH.H81.API.Controllers._demos.RedisTestController">
    <Position X="21.5" Y="0.5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>ACAAgAAAAAAEABAIAAQAAAIgAIAQAAQAIAAAQAEBAAA=</HashCode>
      <FileName>Controllers\_Demos\RedisTestController.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="XH.H81.API.Controllers._demos.TokenTestController">
    <Position X="12.75" Y="4.25" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAEAAAACAAAAACAAAAAAAAAAAACIAAEAAAACAAA=</HashCode>
      <FileName>Controllers\_Demos\TokenTestController.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="XH.H81.API.Controllers._demos.IntefaceDemoDto1">
    <Position X="19.75" Y="5.5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAACAAACAAAAAAAAAAAAAAAAAAAACAAAAAAAAAAAAEA=</HashCode>
      <FileName>Controllers\_Demos\XinghePlatformController.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="XH.H81.API.Controllers._demos.XinghePlatformController">
    <Position X="9.25" Y="7.25" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAYAAAAAAAAAAAAAACAAAAAAAAAAAA=</HashCode>
      <FileName>Controllers\_Demos\XinghePlatformController.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Font Name="Microsoft YaHei UI" Size="9" />
</ClassDiagram>