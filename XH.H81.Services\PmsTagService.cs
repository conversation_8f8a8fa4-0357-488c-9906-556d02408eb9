﻿using AutoMapper;
using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using H.Utility.Helper;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using NPOI.Util;
using Serilog;
using System.Collections.Generic;
using XH.H81.Base.Helper;
using XH.H81.IServices;
using XH.H81.Models.Dtos;
using XH.H81.Models.Dtos.Pms;
using XH.H81.Models.Dtos.Tag;
using XH.H81.Models.Entities;
using XH.H81.Models.Entities.Pms;
using XH.H81.Models.Entities.Tag;
using XH.H81.Models.ExternalEntity;
using XH.H81.Models.SugarDbContext;
using XH.LAB.UTILS.Helpers;
using XH.LAB.UTILS.Interface;
using XH.LAB.UTILS.Models;
using XH.LAB.UTILS.Models.Dto;
using XH.LAB.UTILS.Models.Entites;

namespace XH.H81.Services
{
    public class PmsTagService : IPmsTagService
    {
        private readonly IHttpContextAccessor _httpContext;
        private readonly IConfiguration _configuration;
        private readonly ISqlSugarUow<SugarDbContext_Master> _soa;
        private readonly IMapper _mapper;
        private readonly ISystemService _systemService;
        private readonly ILogger<PmsTagService> _logger;
        private readonly IBaseDataServices _IBaseDataServices;
        private readonly IEvaluatePlanService _IEvaluatePlanService;
        private readonly IModuleLabGroupService _IModuleLabGroupService;
        private readonly IOrganizationTreeService2 _organizationTreeService2;
        public PmsTagService(IConfiguration configuration, IHttpContextAccessor httpContext, ISqlSugarUow<SugarDbContext_Master> dbContext, IMapper mapper, ISystemService systemService, ILogger<PmsTagService> logger, IModuleLabGroupService iModuleLabGroupService, IOrganizationTreeService2 organizationTreeService2, IBaseDataServices BaseDataServices, IEvaluatePlanService EvaluatePlanService)
        {
            _configuration = configuration;
            _httpContext = httpContext;
            _soa = dbContext;
            _mapper = mapper;
            _systemService = systemService;
            _logger = logger;
            _IModuleLabGroupService = iModuleLabGroupService;
            _organizationTreeService2 = organizationTreeService2;
            _IBaseDataServices = BaseDataServices;
            _IEvaluatePlanService = EvaluatePlanService;
        }

        #region 标签字典维护
        public ResultDto GetTagDict(string tagClass, string tagName, string state, string hsopitalId)
        {
            var tag = _soa.Db.Queryable<PMS_PERSON_TAG_DICT>()
                .LeftJoin<OA_BASE_DATA>((a, b) => a.TAG_CLASS == b.DATA_ID && b.CLASS_ID == "系统入口类型")
                .WhereIF(tagClass.IsNotNullOrEmpty(), (a, b) => a.TAG_CLASS == tagClass)
                .WhereIF(tagName.IsNotNullOrEmpty(), (a, b) => a.TAG_NAME.Contains(tagName))
                .WhereIF(state.IsNotNullOrEmpty(), (a, b) => a.TAG_STATE == state)
                .Where((a, b) => a.HOSPITAL_ID == hsopitalId && a.TAG_STATE != "2")
                .Select((a, b) => new { a, b }).ToList();
            List<string> listTag = tag.Select(w => w.a).Select(w => w.PERSON_TAG_ID).Distinct().ToList();
            var relateRecord = _soa.Db.Queryable<PMS_PERSON_TAG_RELATE>()
                .Where(w => listTag.Contains(w.PERSON_TAG_ID) && w.DATA_TYPE == "REC_CLASS").ToList();
            var varClass = _soa.Db.Queryable<PMS_ADDN_CLASS_INFO>().Where(w => w.CLASS_STATE == "1").ToList();
            List<PmsPersonTagDictDto> list = new List<PmsPersonTagDictDto>();
            foreach (var item in tag)
            {
                PmsPersonTagDictDto dto = _mapper.Map<PmsPersonTagDictDto>(item.a);
                dto.TAG_CLASS_NAME = item.b.DATA_NAME;
                //switch (dto.TAG_CLASS)
                //{
                //    case "1":
                //        dto.TAG_CLASS_NAME = "实验室管理(ISO15189)";
                //        break;
                //    case "2":
                //        dto.TAG_CLASS_NAME = "POCT";
                //        break;
                //    case "3":
                //        dto.TAG_CLASS_NAME = "生安";
                //        break;
                //    case "4":
                //        dto.TAG_CLASS_NAME = "高等级";
                //        break;
                //    default:
                //        break;
                //}
                var varDataId = relateRecord.Where(w => w.PERSON_TAG_ID == item.a.PERSON_TAG_ID).Select(w => w.DATA_ID).ToList();
                dto.TAG_RECORD_NAME = String.Join('、', varClass.Where(w => varDataId.Contains(w.CLASS_ID)).ToList().Select(w => w.CLASS_NAME).ToList());
                list.Add(dto);
            }
            list = list.Where(w => w.SOURCE_TYPE != "MANUAL").OrderBy(w => w.TAG_SORT).ToList();
            ResultDto resultDto = new ResultDto();
            resultDto.data = list;
            return resultDto;
        }

        public ResultDto SaveTagDict(PmsPersonTagDictDto dto, string userName, string hospitalId)
        {
            ResultDto resultDto = new ResultDto();
            List<string> listClassId = new List<string>();
            string layoutId = "";
            PMS_PERSON_TAG_DICT tag = null;
            if (dto.PARENT_ID == "8190" || dto.PARENT_ID == "8191")//科室职工和其他类型不是标签 取ISO标签id
                tag = _soa.Db.Queryable<PMS_PERSON_TAG_DICT>().Where(w => w.PERSON_TAG_ID == "TG000001X" && w.TAG_STATE == "1").ToList().FirstOrDefault();
            else
                tag = _soa.Db.Queryable<PMS_PERSON_TAG_DICT>().Where(w => w.SOURCE_ID == dto.PARENT_ID && w.TAG_STATE == "1").ToList().FirstOrDefault();
            //缺省上一级标签内容
            if (dto.ENABLE_PARENT != null && dto.ENABLE_PARENT.Value)
            {
                //string[] arryParentId = dto.PARENT_ID.Split('/');
                //Array.Reverse(arryParentId);
                //foreach (var item in arryParentId)
                //{
                layoutId = "H81_" + tag.PERSON_TAG_ID;
                if (tag != null)
                {
                    var relateRecord = _soa.Db.Queryable<PMS_PERSON_TAG_RELATE>()
                    .Where(w => w.PERSON_TAG_ID == tag.PERSON_TAG_ID && w.DATA_TYPE == "REC_CLASS").ToList();
                    listClassId = relateRecord?.Select(w => w.DATA_ID).ToList();
                }
                //}
            }
            PMS_PERSON_TAG_DICT tagDict = _mapper.Map<PMS_PERSON_TAG_DICT>(dto);
            if (tagDict.TAG_COLOR.IsNullOrEmpty() && tag != null && tag.TAG_COLOR.IsNotNullOrEmpty())
                tagDict.TAG_COLOR = tag.TAG_COLOR;
            if (tagDict.SOURCE_ID.IsNotNullOrEmpty() && tagDict.PERSON_TAG_ID.IsNullOrEmpty())
            {
                var varTag = _soa.Db.Queryable<PMS_PERSON_TAG_DICT>().Where(w => w.SOURCE_ID == tagDict.SOURCE_ID && w.TAG_CLASS == tagDict.TAG_CLASS && w.TAG_STATE == "1").ToList();
                if (varTag != null && varTag.Count > 0)
                {
                    resultDto.msg = $"{tagDict.TAG_NAME}已经新增，请勿重复新增";
                    resultDto.success = false;
                    return resultDto;
                }
            }
            int count;
            if (tagDict.PERSON_TAG_ID.IsNullOrEmpty())
            {
                tagDict.PERSON_TAG_ID = "TG" + _IBaseDataServices.GetTableMaxNumber("PMS_PERSON_TAG_DICT", "PERSON_TAG_ID", 1, 1, "XH_OA").data.ToString().PadLeft(6, '0');
                tagDict.HOSPITAL_ID = hospitalId;
                tagDict.FIRST_RPERSON = userName;
                tagDict.FIRST_RTIME = DateTime.Now;
                tagDict.TAG_STATE = "1";
                count = _soa.Db.Insertable(tagDict).ExecuteCommand();
                CreateComplexForms(tagDict, hospitalId, layoutId);
            }
            else
            {
                tagDict.LAST_MPERSON = userName;
                tagDict.LAST_MTIME = DateTime.Now;
                count = _soa.Db.Updateable(tagDict).IgnoreColumns(w => new { w.HOSPITAL_ID, w.TAG_STATE, w.FIRST_RPERSON, w.FIRST_RTIME, w.SOURCE_TYPE }).ExecuteCommand();
            }
            if (listClassId.Count > 0)
                AddPersonTagClassAssociation(tagDict.PERSON_TAG_ID, listClassId, userName);
            Dictionary<string, object> dicReturn = new Dictionary<string, object>();
            dicReturn.Add("COUNT", count);
            dicReturn.Add("MAXID", tagDict.PERSON_TAG_ID);
            resultDto.success = count > 0;
            resultDto.data = dicReturn;
            return resultDto;
        }

        private ResultDto CreateComplexForms(PMS_PERSON_TAG_DICT tagDict, string hospitalId, string layoutId)
        {       //调用工具箱接口创建模板
            TagTemplateDto tagTemplateDto = new TagTemplateDto();
            tagTemplateDto.SETUP_ID = $"H81_{tagDict.PERSON_TAG_ID}";
            tagTemplateDto.FIELD_CLASS = "基本信息";
            tagTemplateDto.FUNC_ID = "H8107";
            tagTemplateDto.HOSPITAL_ID = hospitalId;
            tagTemplateDto.MODULE_ID = "H81";
            tagTemplateDto.SETUP_NAME = $"{tagDict.TAG_NAME}人员表单";
            tagTemplateDto.SETUP_CNAME = $"{tagDict.TAG_NAME}人员表单";
            tagTemplateDto.SETUP_SORT = tagDict.TAG_SORT;
            tagTemplateDto.SETUP_STATE = "1";
            tagTemplateDto.HOSPITAL_ID = hospitalId;
            if (layoutId.IsNotNullOrEmpty())
                tagTemplateDto.LAYOUT_ID = layoutId;
            else
                tagTemplateDto.LAYOUT_ID = "H81_TG000001X";
            ResultDto resultDto = _systemService.CreateComplexForms(JsonHelper.ToJson(tagTemplateDto));
            //SYS6_MODULE_FUNC_DICT templateDict = _soa.Db.Queryable<SYS6_MODULE_FUNC_DICT>().Where(p => p.SETUP_ID == "H81_TG000001X")?.First();
            //SYS6_MODULE_FUNC_DICT updateDict = _soa.Db.Queryable<SYS6_MODULE_FUNC_DICT>().Where(p => p.SETUP_ID == tagTemplateDto.SETUP_ID)?.First();
            //if (templateDict != null && updateDict != null)
            //{
            //   // updateDict.FORM_COL_JSON = templateDict.FORM_COL_JSON;
            //    updateDict.FORM_JSON = templateDict.FORM_JSON;
            //    _soa.Db.Updateable(updateDict).ExecuteCommand();
            //}
            return resultDto;
        }
        /// <summary>
        /// 更新标签状态
        /// </summary>
        /// <param name="tagId">标签id</param>
        /// <param name="state">状态 0删除 1启用 2禁用</param>
        /// <param name="userName"></param>
        /// <returns></returns>
        public ResultDto UpdateTagDictState(string tagId, string state, string userName)
        {
            ResultDto resultDto = new ResultDto();
            var tagDict = _soa.Db.Queryable<PMS_PERSON_TAG_DICT>().Where(w => w.PERSON_TAG_ID == tagId).ToList().FirstOrDefault();
            if (tagDict == null)
            {
                resultDto.success = false;
                resultDto.msg = "查询失败！";
                return resultDto;
            }
            tagDict.TAG_STATE = state;
            tagDict.LAST_MPERSON = userName;
            tagDict.LAST_MTIME = DateTime.Now;
            resultDto.success = _soa.Db.Updateable(tagDict).ExecuteCommand() > 0;
            if (state == "2")
            {
                //调用工具箱接口创建模板
                TagTemplateDto tagTemplateDto = new TagTemplateDto();
                tagTemplateDto.SETUP_ID = $"H81_{tagId}";
                tagTemplateDto.FIELD_CLASS = "基本信息";
                tagTemplateDto.FUNC_ID = "H8107";
                tagTemplateDto.HOSPITAL_ID = _httpContext.GetHospitalId();
                tagTemplateDto.MODULE_ID = "H81";
                tagTemplateDto.SETUP_NAME = $"{tagDict.TAG_NAME}人员表单";
                tagTemplateDto.SETUP_CNAME = $"{tagDict.TAG_NAME}人员表单";
                tagTemplateDto.SETUP_SORT = tagDict.TAG_SORT;
                tagTemplateDto.SETUP_STATE = "2";
                _systemService.CreateComplexForms(JsonHelper.ToJson(tagTemplateDto));
            }
            return resultDto;
        }
        /// <summary>
        /// 获取标签分类
        /// </summary>
        /// <returns></returns>
        public List<TagClassCommboxInfo> GetTagClass()
        {
            List<TagClassCommboxInfo> listCombox = new List<TagClassCommboxInfo>();
            //  var sysData = _soa.Db.Queryable<SYS6_BASE_DATA>().Where(a => a.CLASS_ID == "人员标签分类" && a.DATA_STATE == "1").ToList();
            var sysData = _soa.Db.Queryable<OA_BASE_DATA>()
                .LeftJoin<PMS_PERSON_TAG_DICT>((a, b) => a.DATA_ID == b.SOURCE_ID)
                .Where((a, b) => a.CLASS_ID == "系统入口类型" && a.STATE_FLAG == "1")
                .Select((a, b) => new TagClassCommboxInfo()
                {
                    ID = a.DATA_ID,
                    NAME = a.DATA_NAME,
                    TYPE = a.STANDART_ID,
                    TAG_ID = b.PERSON_TAG_ID
                }).ToList();
            //if (sysData != null && sysData.Count > 0)
            //{
            //    foreach (var item in sysData)
            //    {
            //        TagClassCommboxInfo commboxInfo = new TagClassCommboxInfo();
            //        commboxInfo.ID = item.DATA_ID;
            //        commboxInfo.NAME = item.DATA_NAME;
            //        commboxInfo.TYPE = item.STANDART_ID;
            //        listCombox.Add(commboxInfo);
            //    }
            //}
            //else
            //{
            //    listCombox.Add(new TagClassCommboxInfo { ID = "1", NAME = "实验室管理(ISO15189)", TYPE = "1" });
            //    listCombox.Add(new TagClassCommboxInfo { ID = "2", NAME = "POCT", TYPE = "2" });
            //    listCombox.Add(new TagClassCommboxInfo { ID = "3", NAME = "生安", TYPE = "3" });
            //    listCombox.Add(new TagClassCommboxInfo { ID = "4", NAME = "高等级", TYPE = "4" });
            //}
            return sysData;
        }

        /// <summary>
        /// 获取标签分类子集
        /// </summary>
        /// <returns></returns>
        public List<CommboxInfo> GetTagClassChild(string id)
        {
            List<CommboxInfo> listCombox = new List<CommboxInfo>();
            var sysData = _soa.Db.Queryable<OA_BASE_DATA>().Where(a => a.FATHER_ID == id && a.STATE_FLAG == "1").ToList();
            if (sysData != null && sysData.Count > 0)
            {
                foreach (var item in sysData)
                {
                    CommboxInfo commboxInfo = new CommboxInfo();
                    commboxInfo.ID = item.DATA_ID;
                    commboxInfo.NAME = item.DATA_NAME;
                    listCombox.Add(commboxInfo);
                }
            }
            return listCombox;
        }
        public ResultDto GetISOTagNameTree(OrgUserParams orgParm)
        {
            ResultDto resultDto = new ResultDto();
            var tree = _organizationTreeService2.GetOrgTreeType_Lab_A(_soa, orgParm, "H81", true);

            var treeAllNodes = tree.GetAllNodes();

            var pgroupdIds = treeAllNodes.Where(a => a.NODE_TYPE == GroupTreeNodeTypeEnum.PGROUP.ToIntStr()).Select(a => a.SOURCE_ID).ToList();

            //人员分类
            var userClassList = _soa.Db.Queryable<SYS6_USER_CLASS_DICT>().Where(a => a.USERCLASS_STATE == "1")
                .WhereIF(orgParm.hospital_id.IsNotNullOrEmpty(), a => a.HOSPITAL_ID == orgParm.hospital_id)
                .WhereIF(orgParm.lab_id.IsNotNullOrEmpty(), a => a.LAB_ID == orgParm.lab_id)
                .ToList()
                .WhereIF(orgParm.area_id.IsNotNullOrEmpty(), (a, _) => a.AREA_SID != null && a.AREA_SID.Split('+').Contains(orgParm.area_id))
                .ToList();

            var varPracticeType = _soa.Db.Queryable<OA_BASE_DATA>().Where(w => w.CLASS_ID == "人员执业分类").ToList();
            var varPracticeLevel = _soa.Db.Queryable<OA_BASE_DATA>().Where(w => w.CLASS_ID == "执业级别").ToList();
            foreach (var userClass in userClassList)
            {
                var labNode = treeAllNodes.Where(a => a.NODE_TYPE == GroupTreeNodeTypeEnum.LAB.ToIntStr() && a.SOURCE_ID == userClass.LAB_ID).FirstOrDefault();
                if (labNode != null)
                {
                    var classNode = new OrgTreeNode
                    {
                        NAME = userClass.USERCLASS_NAME,
                        LAB_ID = userClass.LAB_ID,
                        SOURCE_ID = userClass.USERCLASS_ID,
                        NODE_TYPE = GroupTreeNodeTypeEnum.CLASS.ToIntStr(),
                        NODE_TYPE_NAME = GroupTreeNodeTypeEnum.CLASS.ToDesc(),
                    };
                    AddTypeLevel(classNode, userClass.LAB_ID, varPracticeType, varPracticeLevel);
                    labNode.ChildAdd(classNode);
                }
            }

            //增加“科室职工”节点
            var topNode = new OrgTreeNode
            {
                NAME = "科室职工",
                NODE_TYPE = "8190",
                SOURCE_ID = "8190",
                NODE_TYPE_NAME = "科室职工"
            };
            OrgTree resultTree = new OrgTree();
            resultTree.ChildAdd(topNode);
            topNode.ChildAddRange(tree.CHILDREN.ToList());
            //增加“其他类型”节点
            var otherNode = new OrgTreeNode
            {
                NAME = "其他类型",
                NODE_TYPE = "8191",
                SOURCE_ID = "8191",
                NODE_TYPE_NAME = "其他类型"
            };
            resultTree.ChildAdd(otherNode);
            //从人事其它类型树中，获取科室节点下的所有“其它类型”节点
            var otherTree = GetISOPersonTree_Other(orgParm, "H81", null, varPracticeType, varPracticeLevel);
            var otherNodes = otherTree.GetAllNodes(a => a.NODE_TYPE == GroupTreeNodeTypeEnum.TYPE.ToIntStr());
            if (otherNodes.Any())
                otherNode.ChildAddRange(otherNodes.ToList());
            resultTree.RefreshTree(a => true);
            resultDto.data = resultTree;
            return resultDto;
        }


        private OrgTree GetISOTree(OrgUserParams orgParm)
        {
            var tree = _organizationTreeService2.GetOrgTreeType_Lab_A(_soa, orgParm, "H81", true);

            var treeAllNodes = tree.GetAllNodes();

            var pgroupdIds = treeAllNodes.Where(a => a.NODE_TYPE == GroupTreeNodeTypeEnum.PGROUP.ToIntStr()).Select(a => a.SOURCE_ID).ToList();

            //人员分类
            var userClassList = _soa.Db.Queryable<SYS6_USER_CLASS_DICT>().Where(a => a.USERCLASS_STATE == "1")
                .WhereIF(orgParm.hospital_id.IsNotNullOrEmpty(), a => a.HOSPITAL_ID == orgParm.hospital_id)
                .WhereIF(orgParm.lab_id.IsNotNullOrEmpty(), a => a.LAB_ID == orgParm.lab_id)
                .ToList()
                .WhereIF(orgParm.area_id.IsNotNullOrEmpty(), (a, _) => a.AREA_SID != null && a.AREA_SID.Split('+').Contains(orgParm.area_id))
                .ToList();

            var varPracticeType = _soa.Db.Queryable<OA_BASE_DATA>().Where(w => w.CLASS_ID == "人员执业分类").ToList();
            var varPracticeLevel = _soa.Db.Queryable<OA_BASE_DATA>().Where(w => w.CLASS_ID == "执业级别").ToList();
            foreach (var userClass in userClassList)
            {
                var labNode = treeAllNodes.Where(a => a.NODE_TYPE == GroupTreeNodeTypeEnum.LAB.ToIntStr() && a.SOURCE_ID == userClass.LAB_ID).FirstOrDefault();
                if (labNode != null)
                {
                    var classNode = new OrgTreeNode
                    {
                        NAME = userClass.USERCLASS_NAME,
                        LAB_ID = userClass.LAB_ID,
                        SOURCE_ID = userClass.USERCLASS_ID,
                        NODE_TYPE = GroupTreeNodeTypeEnum.CLASS.ToIntStr(),
                        NODE_TYPE_NAME = GroupTreeNodeTypeEnum.CLASS.ToDesc(),
                    };
                    AddTypeLevel(classNode, userClass.LAB_ID, varPracticeType, varPracticeLevel);
                    labNode.ChildAdd(classNode);
                }
            }

            //增加“科室职工”节点
            var topNode = new OrgTreeNode
            {
                NAME = "科室职工",
                NODE_TYPE = "8190",
                SOURCE_ID = "8190",
                NODE_TYPE_NAME = "科室职工"
            };
            OrgTree resultTree = new OrgTree();
            resultTree.ChildAdd(topNode);
            topNode.ChildAddRange(tree.CHILDREN.ToList());
            //增加“其他类型”节点
            var otherNode = new OrgTreeNode
            {
                NAME = "其他类型",
                NODE_TYPE = "8191",
                SOURCE_ID = "8191",
                NODE_TYPE_NAME = "其他类型"
            };
            resultTree.ChildAdd(otherNode);
            //从人事其它类型树中，获取科室节点下的所有“其它类型”节点
            var otherTree = GetISOPersonTree_Other(orgParm, "H81", null, varPracticeType, varPracticeLevel);
            var otherNodes = otherTree.GetAllNodes(a => a.NODE_TYPE == GroupTreeNodeTypeEnum.TYPE.ToIntStr());
            if (otherNodes.Any())
                otherNode.ChildAddRange(otherNodes.ToList());
            resultTree.RefreshTree(a => true);
            return resultTree;
        }
        public ResultDto GetTagTree(bool? filterIso, string hospitalId)
        {
            var resultDto = new ResultDto();
            var tree = new OrgTree();

            // 获取基础标签字典（SOURCE_TYPE为1的标签）
            var baseTagDict = _soa.Db.Queryable<PMS_PERSON_TAG_DICT>()
                .Where(w => w.HOSPITAL_ID == hospitalId
                         && w.TAG_STATE == "1"
                         && w.SOURCE_TYPE == "1")
                .ToList();

            // 构建标签树
            var allTreeNodes = tree.GetAllNodes();

            // 获取所有有效标签（不包括SOURCE_TYPE为1的标签）
            var tagDict = _soa.Db.Queryable<PMS_PERSON_TAG_DICT>()
                .Where(w => w.HOSPITAL_ID == hospitalId && w.TAG_STATE == "1")
                .OrderBy(w => w.FIRST_RTIME)
                .ToList()
                .FindAll(w => w.SOURCE_TYPE != "1" && w.SOURCE_TYPE != "MANUAL");
            List<string> listTagId = tagDict.Select(w => w.PERSON_TAG_ID).Distinct().ToList();
            listTagId.AddRange(baseTagDict.Select(w => w.PERSON_TAG_ID).Distinct().ToList());
            // 获取所有打上该标签的人员ID
            var personTag = _soa.Db.Queryable<PMS_PERSON_TAG>()
                .Where(pt => listTagId.Contains(pt.PERSON_TAG_ID) && pt.TAG_PSTATE == "1")
                .ToList();
            // 构建基础标签树结构
            foreach (var info in baseTagDict)
            {
                var classNode = new OrgTreeNode
                {
                    NAME = info.TAG_NAME,
                    SOURCE_ID = info.PERSON_TAG_ID,
                    NODE_TYPE = "131",
                    NODE_TYPE_NAME = "标签分类",
                };
                Dictionary<string, object> tag = new Dictionary<string, object>();
                tag.Add("TAG_SNAME", info.TAG_SNAME);
                tag.Add("TAG_COLOR", info.TAG_COLOR);
                tag.Add("TAG_ICON", info.TAG_ICON);
                tag.Add("NODE_ID", info.PERSON_TAG_ID);
                classNode.NUM =  personTag.FindAll(w => w.PERSON_TAG_ID == classNode.SOURCE_ID).ToList().Count();
                classNode.SOURCE = tag;
                // 特殊处理ISO标签（PERSON_TAG_ID为1）
                if (info.PERSON_TAG_ID == "TG000001X")
                {
                    //var labNode = new OrgTreeNode
                    //{
                    //    NAME = "科室职工",
                    //    SOURCE_ID = "8190",
                    //    NODE_TYPE = "8190",
                    //    NODE_TYPE_NAME = "科室职工",
                    //};
                    //Dictionary<string, object> labDic = new Dictionary<string, object>();
                    //labDic.Add("NODE_ID", "8190");
                    //labNode.SOURCE = labDic;
                    //classNode.ChildAdd(labNode);
                    //var otherNode = new OrgTreeNode
                    //{
                    //    NAME = "其他类型",
                    //    SOURCE_ID = "8191",
                    //    NODE_TYPE = "8191",
                    //    NODE_TYPE_NAME = "其他类型",
                    //};
                    //Dictionary<string, object> otherDic = new Dictionary<string, object>();
                    //otherDic.Add("NODE_ID", "8191");
                    //otherNode.SOURCE = otherDic;
                    //classNode.ChildAdd(otherNode);

                    OrgTree orgTreeNodes = GetISOTree(new OrgUserParams() { hospital_id = hospitalId ,lab_id=_httpContext.GetLabId()});
                    List<string> listSourceId = tagDict.Select(w => w.SOURCE_ID).Distinct().ToList();
                    orgTreeNodes.AllNodesRemove(w => !listSourceId.Contains(w.SOURCE_ID) && (w.NODE_TYPE!= GroupTreeNodeTypeEnum.MGROUP.ToIntStr() && w.NODE_TYPE != GroupTreeNodeTypeEnum.LAB.ToIntStr() && w.NODE_TYPE!= "8190" && w.NODE_TYPE != "8191"));
                    foreach (var item in orgTreeNodes.GetAllNodes())
                    {
                        PMS_PERSON_TAG_DICT pmsPersonTagDict = tagDict.Find(w => w.SOURCE_ID == item.SOURCE_ID);
                        if (pmsPersonTagDict != null)
                        {
                            item.SOURCE_ID = pmsPersonTagDict.PERSON_TAG_ID;
                            item.NODE_TYPE = "132";
                            item.NODE_TYPE_NAME = "标签名称";
                            Dictionary<string, object> dictTag = new Dictionary<string, object>();
                            dictTag.Add("TAG_SNAME", pmsPersonTagDict.TAG_SNAME);
                            dictTag.Add("TAG_COLOR", pmsPersonTagDict.TAG_COLOR);
                            dictTag.Add("TAG_ICON", pmsPersonTagDict.TAG_ICON);
                            dictTag.Add("NODE_ID", pmsPersonTagDict.SOURCE_ID);
                            item.NUM = personTag.FindAll(w => w.PERSON_TAG_ID == item.SOURCE_ID).ToList().Count();
                            item.SOURCE = dictTag;
                        }
                   
                    }
                    orgTreeNodes.RefreshTreeUnique(W => W.NODE_TYPE == "132");
                    orgTreeNodes.AllNodesRemove(w => w.NUM == 0 && ((w.NODE_TYPE == GroupTreeNodeTypeEnum.LAB.ToIntStr() || w.NODE_TYPE == GroupTreeNodeTypeEnum.MGROUP.ToIntStr() && w.NODE_TYPE!="132")));
                    classNode.ChildAddRange(orgTreeNodes.CHILDREN.ToList());
                }

                tree.ChildAdd(classNode);
            }
            tagDict = tagDict.FindAll(w => w.TAG_CLASS != "XBD00001201");
            foreach (var dict in tagDict)
            {
                OrgTreeNode parentNode = null;

                // 根据PARENT_ID或TAG_CLASS查找父节点
                if (!string.IsNullOrEmpty(dict.PARENT_ID))
                {
                    var parentTag = tagDict.Find(w => w.SOURCE_ID == dict.PARENT_ID);
                    var tagId = parentTag?.PERSON_TAG_ID ?? dict.PARENT_ID;
                    parentNode = tree.GetAllNodes(a => a.SOURCE_ID == tagId).FirstOrDefault();
                }
                else
                {
                    parentNode = allTreeNodes.FirstOrDefault(a => a.SOURCE_ID == dict.TAG_CLASS);
                }

                if (parentNode != null)
                {
                    parentNode.NUM = personTag.FindAll(w => w.PERSON_TAG_ID == parentNode.SOURCE_ID).ToList().Count();
                    OrgTreeNode orgTreeNode = new OrgTreeNode();
                    orgTreeNode.NAME = dict.TAG_NAME;
                    orgTreeNode.SOURCE_ID = dict.PERSON_TAG_ID;
                    orgTreeNode.NODE_TYPE = "132";
                    orgTreeNode.NODE_TYPE_NAME = "标签名称";
                    Dictionary<string, object> tag = new Dictionary<string, object>();
                    tag.Add("TAG_SNAME", dict.TAG_SNAME);
                    tag.Add("TAG_COLOR", dict.TAG_COLOR);
                    tag.Add("TAG_ICON", dict.TAG_ICON);
                    tag.Add("NODE_ID", dict.SOURCE_ID);
                    orgTreeNode.NUM = personTag.FindAll(w => w.PERSON_TAG_ID == orgTreeNode.SOURCE_ID).ToList().Count();
                    orgTreeNode.SOURCE = tag;
                    parentNode.ChildAdd(orgTreeNode);
                   // parentNode.NUM = parentNode.NUM+ parentNode.CHILDREN.Sum(w=>w.NUM);
                }
            }

            //tree.RefreshTree(a => true);
            tree.RefreshTreeWithoutNum();
            //过滤ISO节点
            if (filterIso == true)
            {
                tree.AllNodesRemove(w => w.SOURCE_ID == "TG000001X");
            }

            resultDto.data = tree;
            return resultDto;
        }

        private OrgTree GetISOPersonTree_Other(OrgUserParams orgParm, string permissMenuId, string personType, List<OA_BASE_DATA> listType, List<OA_BASE_DATA> listLevel)
        {
            var tree = new OrgTree();
            var lab = _soa.Db.Queryable<SYS6_INSPECTION_LAB>()
             .Where(a => a.HOSPITAL_ID == orgParm.hospital_id && a.STATE_FLAG == "1" && a.LAB_ID == orgParm.lab_id)
             .ToList()?.FirstOrDefault();
            if (lab == null)
                return tree;
            var userType = _soa.Db.Queryable<SYS6_USER_TYPE_DICT>()
                .Where(a => a.HOSPITAL_ID == orgParm.hospital_id && a.LAB_ID == lab.LAB_ID && a.USERTYPE_STATE == "1")
                .WhereIF(personType.IsNotNullOrEmpty(), a => a.USERTYPE_ID == personType).ToList();
            var baseData = _soa.Db.Queryable<SYS6_BASE_DATA>().Where(a => a.CLASS_ID == "人员类型" && a.DATA_ID != "1" && a.DATA_ID != "10" && a.DATA_ID != "13" && a.DATA_STATE == "1") //25-6-27省人悦琳/朱刚提出需求，排除【信息维护工程师】、【生物安全】
                                .WhereIF(personType.IsNotNullOrEmpty(), a => a.DATA_ID == personType).OrderBy(a => a.DATA_SORT).ToList();
            var labNode = CreateTreeChildNode(lab.LAB_ID, lab.LAB_NAME, lab.LAB_SORT, lab.LAB_ID, GroupTreeNodeTypeEnum.LAB.ToIntStr(), GroupTreeNodeTypeEnum.LAB.ToDesc());
            tree.ChildAdd(labNode);
            foreach (var item in baseData)
            {
                var typeCode = CreateTreeChildNode(item.DATA_ID, item.DATA_CNAME, item.DATA_SORT, item.LAB_ID, GroupTreeNodeTypeEnum.TYPE.ToIntStr(), GroupTreeNodeTypeEnum.TYPE.ToDesc());
                AddTypeLevel(typeCode, item.LAB_ID, listType, listLevel);
                labNode.ChildAdd(typeCode);
            }
            foreach (var item in userType)
            {
                var typeCode = CreateTreeChildNode(item.USERTYPE_ID, item.USERTYPE_NAME, item.USERTYPE_SORT, item.LAB_ID, GroupTreeNodeTypeEnum.TYPE.ToIntStr(), GroupTreeNodeTypeEnum.TYPE.ToDesc());
                AddTypeLevel(typeCode, item.LAB_ID, listType, listLevel);
                labNode.ChildAdd(typeCode);
            }
            return tree;
        }
        /// <summary>
        /// 增加执业级别节点
        /// </summary>
        /// <param name="node"></param>
        /// <param name="labId"></param>
        /// <param name="listType"></param>
        /// <param name="listLevel"></param>
        private void AddTypeLevel(OrgTreeNode node, string labId, List<OA_BASE_DATA> listType, List<OA_BASE_DATA> listLevel)
        {
            if (node.NAME == "规培生")
            {
                foreach (var type in listType)
                {
                    foreach (var level in listLevel)
                    {
                        var levelNode = new OrgTreeNode
                        {
                            NAME = type.DATA_SNAME + level.DATA_SNAME,
                            LAB_ID = labId,
                            SOURCE_ID = $"{node.SOURCE_ID}|{type.DATA_ID}|{level.DATA_ID}",
                            NODE_TYPE = "1221",
                            NODE_TYPE_NAME = "执业分类级别",
                        };
                        node.ChildAdd(levelNode);
                    }
                }
            }
        }
        /// <summary>
        /// 生成树子节点
        /// </summary>
        /// <param name="sourceId"></param>
        /// <param name="name"></param>
        /// <param name="sort"></param>
        /// <param name="labId"></param>
        /// <param name="nodeType"></param>
        /// <param name="nodeTypeName"></param>
        /// <returns></returns>
        private OrgTreeNode CreateTreeChildNode(string sourceId, string name, string sort, string labId, string nodeType, string nodeTypeName)
        {
            var node = new OrgTreeNode();
            node.SOURCE_ID = sourceId;
            node.NAME = name;
            node.SORT = sort;
            node.LAB_ID = labId;
            node.NODE_TYPE = nodeType;
            node.NODE_TYPE_NAME = nodeTypeName;
            return node;
        }
        #endregion
        #region 人员标签关系
        /// <summary>
        /// 获标签待选人员
        /// </summary>
        /// <param name="tagId">标签id</param>
        /// <param name="labPgroupId">科室或专业组id</param>
        /// <param name="personName">人员名称</param>
        /// <returns></returns>
        public ResultDto GetUnSelectPersonTag(string tagId, string labPgroupId, string personName, string hospitalId)
        {
            ResultDto resultDto = new ResultDto();
            // 获取所有打上该标签的人员ID
            var selectedPersonIds = _soa.Db.Queryable<PMS_PERSON_TAG>()
                .Where(pt => pt.PERSON_TAG_ID == tagId && pt.TAG_PSTATE == "1")
                .Select(pt => pt.PERSON_ID)
                .ToList();
            List<SYS6_BASE_DATA> sys6_base_data = _IBaseDataServices.GetSys6BaseData();
            var varPerson = _soa.Db.Queryable<PMS_PERSON_INFO>()
                .LeftJoin<SYS6_INSPECTION_PGROUP>((a, b) => a.PGROUP_ID == b.PGROUP_ID)
                .LeftJoin<SYS6_INSPECTION_LAB>((a, b, c) => a.LAB_ID == c.LAB_ID)
                .Where((a, b, c) => !selectedPersonIds.Contains(a.PERSON_ID) && a.PERSON_STATE == "1" && a.HOSPITAL_ID == hospitalId)
                .WhereIF(labPgroupId.IsNotNullOrEmpty(), (a, b, c) => a.LAB_ID == labPgroupId || a.PGROUP_ID == labPgroupId)
                .WhereIF(personName.IsNotNullOrEmpty(), (a, b, c) => a.USER_NAME.Contains(personName))
                .Select((a, b, c) => new PmsPersonTagDto()
                {
                    PERSON_ID = a.PERSON_ID,
                    LAB_ID = a.LAB_ID,
                    LAB_NAME = c.LAB_NAME,
                    PGROUP_ID = a.PGROUP_ID,
                    PGROUP_NAME = b.PGROUP_NAME,
                    USER_NAME = a.USER_NAME,
                    USER_ID = a.USER_ID,
                    DUTIES = a.DUTIES,
                    PHONE = a.PHONE
                }).ToList();
            List<string> listPersonId = varPerson.Select(w => w.PERSON_ID).Distinct().ToList();
            var varPersonTag = _soa.Db.Queryable<PMS_PERSON_TAG>()
                .LeftJoin<PMS_PERSON_TAG_DICT>((a, b) => a.PERSON_TAG_ID == b.PERSON_TAG_ID)
        .Where((a, b) => listPersonId.Contains(a.PERSON_ID) && a.TAG_PSTATE == "1")
        .Select((a, b) => new PmsPersonTagDto()
        {
            PERSON_ID = a.PERSON_ID,
            TAG_NAME = b.TAG_NAME,

        })
        .ToList();
            foreach (var item in varPerson)
            {
                //item.DUTIES_NAME = RecordClassBaseName("职务", item.DUTIES, sys6_base_data);
                item.TAG_NAME = String.Join('、', varPersonTag.Where(w => w.PERSON_ID == item.PERSON_ID).Select(w => w.TAG_NAME));
                item.TAG_ID = String.Join(',', varPersonTag.Where(w => w.PERSON_ID == item.PERSON_ID).Select(w => w.TAG_ID));
            }
            resultDto.data = varPerson;
            return resultDto;
        }
        /// <summary>
        /// 获标签已选人员
        /// </summary>
        /// <param name="tagId">标签id</param>
        /// <param name="labPgroupId">科室或专业组id</param>
        /// <param name="personName">人员名称</param>
        public ResultDto GetSelectPersonTag(string tagId, string labPgroupId, string personName, string hospitalId)
        {
            ResultDto resultDto = new ResultDto();
            List<string> listTagId = new List<string>();
            listTagId.Add(tagId);
            var varTagDict = _soa.Db.Queryable<PMS_PERSON_TAG_DICT>().Where(w => w.TAG_CLASS == tagId).ToList().FindAll(w => w.SOURCE_TYPE != "MANUAL");
            if (varTagDict != null && varTagDict.Count > 0)
                listTagId.AddRange(varTagDict.Select(w => w.PERSON_TAG_ID).ToList());

            // 获取所有打上该标签的人员ID
            var selectedPersonIds = _soa.Db.Queryable<PMS_PERSON_TAG>()
                .Where(pt => listTagId.Contains(pt.PERSON_TAG_ID) && pt.TAG_PSTATE == "1")
                .Select(pt => pt.PERSON_ID)
                .ToList();
            List<SYS6_BASE_DATA> sys6_base_data = _IBaseDataServices.GetSys6BaseData();
            var varPerson = _soa.Db.Queryable<PMS_PERSON_INFO>()
                .LeftJoin<SYS6_INSPECTION_PGROUP>((a, b) => a.PGROUP_ID == b.PGROUP_ID)
                .LeftJoin<SYS6_INSPECTION_LAB>((a, b, c) => a.LAB_ID == c.LAB_ID)
                .Where((a, b, c) => selectedPersonIds.Contains(a.PERSON_ID) && a.HOSPITAL_ID == hospitalId)
                .WhereIF(labPgroupId.IsNotNullOrEmpty(), (a, b, c) => a.LAB_ID == labPgroupId || a.PGROUP_ID == labPgroupId)
                .WhereIF(personName.IsNotNullOrEmpty(), (a, b, c) => a.USER_NAME.Contains(personName))
                .Select((a, b, c) => new PmsPersonTagDto()
                {
                    PERSON_ID = a.PERSON_ID,
                    LAB_ID = a.LAB_ID,
                    LAB_NAME = c.LAB_NAME,
                    PGROUP_ID = a.PGROUP_ID,
                    PGROUP_NAME = b.PGROUP_NAME,
                    USER_NAME = a.USER_NAME,
                    USER_ID = a.USER_ID,
                    DUTIES = a.DUTIES,
                    PHONE = a.PHONE,
                }).ToList();
            List<string> listPersonId = varPerson.Select(w => w.PERSON_ID).Distinct().ToList();
            var varPersonTag = _soa.Db.Queryable<PMS_PERSON_TAG>()
                .LeftJoin<PMS_PERSON_TAG_DICT>((a, b) => a.PERSON_TAG_ID == b.PERSON_TAG_ID)
        .Where((a, b) => listPersonId.Contains(a.PERSON_ID) && a.TAG_PSTATE == "1")
        .Select((a, b) => new PmsPersonTagDto()
        {
            PERSON_ID = a.PERSON_ID,
            TAG_NAME = b.TAG_NAME,
            TAG_ID = b.PERSON_TAG_ID
        })
        .ToList();
            foreach (var item in varPerson)
            {
                //item.DUTIES_NAME = RecordClassBaseName("职务", item.DUTIES, sys6_base_data);
                item.TAG_NAME = String.Join('、', varPersonTag.Where(w => w.PERSON_ID == item.PERSON_ID).Select(w => w.TAG_NAME));
                item.TAG_ID = String.Join(',', varPersonTag.Where(w => w.PERSON_ID == item.PERSON_ID).Select(w => w.TAG_ID));
            }
            resultDto.data = varPerson;
            return resultDto;
        }

        /// <summary>
        /// 按人员获取人员标签列表
        /// </summary>
        /// <param name="labPgroupId">科室或专业组id</param>
        /// <param name="personName">人员名称</param>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        public ResultDto GetPersonTag(string labPgroupId, string personName, string hospitalId)
        {
            ResultDto resultDto = new ResultDto();
            List<SYS6_BASE_DATA> sys6_base_data = _IBaseDataServices.GetSys6BaseData();
            var varPerson = _soa.Db.Queryable<PMS_PERSON_INFO>()
                .LeftJoin<SYS6_INSPECTION_PGROUP>((a, b) => a.PGROUP_ID == b.PGROUP_ID)
                .LeftJoin<SYS6_INSPECTION_LAB>((a, b, c) => a.LAB_ID == c.LAB_ID)
                .Where((a, b, c) => a.PERSON_STATE == "1" && a.HOSPITAL_ID == hospitalId)
                .WhereIF(labPgroupId.IsNotNullOrEmpty(), (a, b, c) => a.LAB_ID == labPgroupId || a.PGROUP_ID == labPgroupId)
                .WhereIF(personName.IsNotNullOrEmpty(), (a, b, c) => a.USER_NAME.Contains(personName))
                .Select((a, b, c) => new PmsPersonTagDto()
                {
                    PERSON_ID = a.PERSON_ID,
                    LAB_ID = a.LAB_ID,
                    LAB_NAME = c.LAB_NAME,
                    PGROUP_ID = a.PGROUP_ID,
                    PGROUP_NAME = b.PGROUP_NAME,
                    USER_NAME = a.USER_NAME,
                    USER_ID = a.USER_ID,
                    DUTIES = a.DUTIES,
                    PHONE = a.PHONE
                }).ToList();
            List<string> listPersonId = varPerson.Select(w => w.PERSON_ID).Distinct().ToList();
            var varPersonTag = _soa.Db.Queryable<PMS_PERSON_TAG>()
                .LeftJoin<PMS_PERSON_TAG_DICT>((a, b) => a.PERSON_TAG_ID == b.PERSON_TAG_ID)
        .Where((a, b) => listPersonId.Contains(a.PERSON_ID) && a.TAG_PSTATE == "1")
        .Select((a, b) => new PmsPersonTagDto()
        {
            PERSON_ID = a.PERSON_ID,
            TAG_NAME = b.TAG_NAME,
            TAG_ID = b.PERSON_TAG_ID
        })
        .ToList();
            foreach (var item in varPerson)
            {
                //item.DUTIES_NAME = RecordClassBaseName("职务", item.DUTIES, sys6_base_data);
                item.TAG_NAME = String.Join('、', varPersonTag.Where(w => w.PERSON_ID == item.PERSON_ID).Select(w => w.TAG_NAME));
                item.TAG_ID = String.Join(',', varPersonTag.Where(w => w.PERSON_ID == item.PERSON_ID).Select(w => w.TAG_ID));
            }
            varPerson = varPerson.OrderByDescending(w => w.TAG_NAME).ToList();
            resultDto.data = varPerson;
            return resultDto;
        }

        /// <summary>
        /// 保存人员标签(按人员)
        /// </summary>
        /// <param name="listTag"></param>
        /// <param name="listPerson"></param>
        /// <param name="userName"></param>
        /// <returns></returns>

        public ResultDto SavePersonTag(List<string> listTag, List<string> listPerson, string userName)
        {
            var resultDto = new ResultDto();
            List<PMS_PERSON_TAG> listPersonTag = _soa.Db.Queryable<PMS_PERSON_TAG>().Where(w => listPerson.Contains(w.PERSON_ID) && w.TAG_PSTATE == "1").ToList();
            List<PMS_PERSON_TAG> listAddPersonTag = new List<PMS_PERSON_TAG>();
            foreach (string tagId in listTag)
            {
                foreach (var item in listPerson)
                {
                    PMS_PERSON_TAG tag = new PMS_PERSON_TAG();
                    tag.PERSON_TAG_ID = tagId;
                    tag.PERSON_ID = item;
                    tag.TAG_PSTATE = "1";
                    tag.FIRST_RTIME = DateTime.Now;
                    tag.FIRST_RPERSON = userName;
                    listAddPersonTag.Add(tag);
                }
            }
            _soa.Db.Deleteable(listPersonTag).ExecuteCommand();
            resultDto.success = _soa.Db.Insertable(listAddPersonTag).ExecuteCommand() > 0;
            return resultDto;

        }
        /// <summary>
        /// 增加人员标签
        /// </summary>
        /// <param name="tagIds">标签id合集 多个,隔开</param>
        /// <param name="personIds">人员id合集 多个,隔开</param>
        /// <param name="userName"></param>
        /// <returns></returns>
        public ResultDto AddPersonTag(string tagIds, string personIds, string userName)
        {
            var resultDto = new ResultDto();
            List<string> tagIdList = tagIds.Split(',').ToList();
            List<string> personIdList = personIds.Split(',').ToList();
            List<PMS_PERSON_TAG> listPersonTag = new List<PMS_PERSON_TAG>();
            List<PMS_PERSON_TAG> listDeletePersonTag = new List<PMS_PERSON_TAG>();
            var varTagDict = _soa.Db.Queryable<PMS_PERSON_TAG_DICT>().Where(w => w.TAG_STATE == "1").ToList();
            foreach (string tagId in tagIdList)
            {
                //判断是否有二级标签
                var parentId = varTagDict.Find(w => w.PERSON_TAG_ID == tagId)?.PARENT_ID;
                foreach (var item in personIdList)
                {
                    var varPersonTag = _soa.Db.Queryable<PMS_PERSON_TAG>().Where(w => w.PERSON_ID == item && w.TAG_PSTATE == "1").ToList();
                    if (parentId != null)
                    {
                        //选择二级标签之后 需要移除一级标签
                        PMS_PERSON_TAG firstTag = varPersonTag.Find(w => w.PERSON_TAG_ID == parentId);
                        if (firstTag != null)
                            listDeletePersonTag.Add(firstTag);
                    }
                    PMS_PERSON_TAG tag = new PMS_PERSON_TAG();
                    tag.PERSON_TAG_ID = tagId;
                    tag.PERSON_ID = item;
                    tag.TAG_PSTATE = "1";
                    tag.FIRST_RTIME = DateTime.Now;
                    tag.FIRST_RPERSON = userName;
                    listPersonTag.Add(tag);
                }
            }
            if (listDeletePersonTag.Count > 0)
                _soa.Db.Deleteable(listDeletePersonTag).ExecuteCommand();
            resultDto.success = _soa.Db.Insertable(listPersonTag).ExecuteCommand() > 0;
            return resultDto;
        }
        /// <summary>
        /// 删除人员标签
        /// </summary>
        /// <param name="tagIds">标签id合集 多个,隔开</param>
        /// <param name="personIds">人员id合集 多个,隔开</param>
        /// <param name="userName"></param>
        /// <returns></returns>
        public ResultDto DeletePersonTag(string tagIds, string personIds, string userName)
        {
            var resultDto = new ResultDto();
            List<string> personIdList = personIds.Split(',').ToList();
            List<string> tagIdList = tagIds.Split(',').ToList();
            List<PMS_PERSON_TAG> listPersonTag = _soa.Db.Queryable<PMS_PERSON_TAG>().Where(w => tagIdList.Contains(w.PERSON_TAG_ID) && personIdList.Contains(w.PERSON_ID) && w.TAG_PSTATE == "1").ToList();
            //foreach (var item in listPersonTag)
            //{
            //    item.TAG_PSTATE = "2";
            //    item.LAST_MTIME = DateTime.Now;
            //    item.LAST_MPERSON = userName;
            //}
            resultDto.success = _soa.Db.Deleteable(listPersonTag).ExecuteCommand() > 0;
            return resultDto;
        }

        /// <summary>
        ///获取基础数据名称
        /// </summary>
        /// <param name="class_id"></param>
        /// <param name="data_id"></param>
        /// <returns></returns>
        public string RecordClassBaseName(string class_id, string data_id, List<SYS6_BASE_DATA> sys6_base_data)
        {
            string className = string.Empty;
            if (data_id != null)
            {
                if (sys6_base_data.Where(x => x.CLASS_ID == class_id && x.DATA_ID == data_id).FirstOrDefault() != null)
                {
                    className = sys6_base_data.Where(x => x.CLASS_ID == class_id && x.DATA_ID == data_id).FirstOrDefault().DATA_CNAME;
                }
            }
            return className;
        }

        /// <summary>
        /// 获取人员所有标签的通用方法
        /// </summary>
        /// <param name="personIds"></param>
        /// <returns></returns>
        public Dictionary<string, List<PmsPersonTagDictDto>> GetPersonIdPersonTagDict(List<string> personIds, string systemType = "1", bool isReturnIsoTags = true)
        {
            var resultDict = new Dictionary<string, List<PmsPersonTagDictDto>>();

            var PersonTags = _soa.Db.Queryable<PMS_PERSON_INFO>()
                .LeftJoin<SYS6_INSPECTION_PGROUP>((person, pgroup) => person.PGROUP_ID == pgroup.PGROUP_ID && pgroup.PGROUP_STATE == "1")
                .LeftJoin<PMS_PERSON_TAG>((person, pgroup, pt) => pt.PERSON_ID == person.PERSON_ID)
                .LeftJoin<PMS_PERSON_TAG_DICT>((person, pgroup, pt, tag) => tag.PERSON_TAG_ID == pt.PERSON_TAG_ID  //非ISO标签                                                                        
                                                                          || tag.SOURCE_ID == pgroup.LAB_ID || (tag.SOURCE_ID == person.LAB_ID && pgroup.PGROUP_ID == null)
                                                                          || tag.SOURCE_ID == pgroup.PGROUP_ID)
                .InnerJoin<OA_BASE_DATA>((person, pgroup, pt, tag, data) => tag.TAG_CLASS == data.DATA_ID && data.CLASS_ID == "系统入口类型" && data.STATE_FLAG == "1")
                .Where((person, pgroup, pt, tag, data) => tag.TAG_STATE == "1" && tag.SOURCE_ID != null)
                .WhereIF(systemType != "1", (person, pgroup, pt, tag, data) => data.STANDART_ID == systemType)
                .Select((person, pgroup, pt, tag, data) =>
                    new PmsPersonTagDictDto()
                    {
                        PERSON_ID = person.PERSON_ID,
                        PERSON_TAG_ID = tag.PERSON_TAG_ID,
                        TAG_CLASS = tag.TAG_CLASS,
                        TAG_CLASS_NAME = data.DATA_NAME,
                        TAG_NAME = tag.TAG_NAME,
                        TAG_SNAME = tag.TAG_SNAME,
                        TAG_COLOR = tag.TAG_COLOR,
                        TAG_ICON = tag.TAG_ICON,
                        STANDART_ID = data.STANDART_ID,
                        TAG_SORT = tag.TAG_SORT,
                    })
                .ToList()
                .DistinctBy(a => $"{a.PERSON_ID}_{a.PERSON_TAG_ID}")
                .OrderBy(a => a.TAG_SORT)
                .ToList();

            //ISO节点
            PmsPersonTagDictDto isoTag = null;
            if (systemType == "1" && isReturnIsoTags)
            {
                isoTag = _soa.Db.Queryable<PMS_PERSON_TAG_DICT>()
                      .InnerJoin<OA_BASE_DATA>((a, b) => a.SOURCE_ID == b.DATA_ID && b.CLASS_ID == "系统入口类型" && b.STANDART_ID == "1") //STANDART_ID
                      .Select((a, b) =>
                           new PmsPersonTagDictDto()
                           {
                               PERSON_TAG_ID = a.PERSON_TAG_ID,
                               TAG_CLASS = a.TAG_CLASS,
                               TAG_CLASS_NAME = b.DATA_NAME,
                               TAG_NAME = a.TAG_NAME,
                               TAG_SNAME = a.TAG_SNAME,
                               TAG_COLOR = a.TAG_COLOR,
                               TAG_ICON = a.TAG_ICON
                           })
                      .First();
            }

            foreach (var personId in personIds.Distinct())
            {
                var tags = PersonTags.FindAll(a => a.PERSON_ID == personId);
                if (isoTag != null && systemType == "1" && isReturnIsoTags)
                    tags.Insert(0, isoTag);
                resultDict[personId] = tags;
            }
            return resultDict;
        }

        #endregion

        #region “人员标签”与 “档案类型”的关联关系

        /// <summary>
        /// 获取当前“人员标签”已选中的“档案类型”列表
        /// </summary>
        /// <param name="personTagId"></param>
        /// <param name="keyword"></param>
        /// <returns></returns>
        public List<PmsClassInfoDto> GetPersonTagSelectedClasses(string personTagId, string keyword, string hisName)
        {
            var result = new List<PmsClassInfoDto>();
            var data = _soa.Db.Queryable<PMS_PERSON_TAG_RELATE>()
                                   .InnerJoin<PMS_ADDN_CLASS_INFO>((relate, classInfo) => relate.DATA_ID == classInfo.CLASS_ID)
                                   .Where((relate, classInfo) => relate.PERSON_TAG_ID == personTagId
                                                              && relate.DATA_TYPE == "REC_CLASS"
                                                              && relate.HOSPITAL_ID == classInfo.HOSPITAL_ID)
                                   .Where((relate, classInfo) => classInfo.CLASS_TYPE == "0" || classInfo.CLASS_TYPE == "1" || classInfo.CLASS_TYPE == "99")
                                   .WhereIF(keyword.IsNotNullOrEmpty(), (relate, classInfo) => classInfo.CLASS_NAME.Contains(keyword))
                                   .Select((relate, classInfo) => classInfo)
                                   .ToList();

            foreach (var item in data.Distinct().OrderBy(d => d.CLASS_SORT))
            {
                var dto = _mapper.Map<PmsClassInfoDto>(item);
                dto.CLASS_KIND_NAME = dto.CLASS_KIND == "1" ? "健康档案" : "无";
                var dict = EntityHelper.DeserializeObject(item.CLASS_ADDN_CONFIG);
                foreach (var d in dict)
                {
                    if (d.Key == "IF_UPLOAD_FILE")
                        dto.IF_UPLOAD_FILE = d.Value != null && d.Value.ToString() == "1" ? "1" : "0";
                    if (d.Key == "IF_MUST_UPLOAD_FILE")
                        dto.IF_MUST_UPLOAD_FILE = d.Value != null && d.Value.ToString() == "1" ? "1" : "0";
                }
                result.Add(dto);
            }
            return result;
        }

        /// <summary>
        /// 获取当前“人员标签”未选中的“档案类型”列表
        /// </summary>
        /// <param name="personTagId"></param>
        /// <param name="keyword"></param>
        /// <returns></returns>
        public List<PmsClassInfoDto> GetPersonTagUnselectedClasses(string personTagId, string keyword, string hisName)
        {
            var result = new List<PmsClassInfoDto>();
            var data = _soa.Db.Queryable<PMS_ADDN_CLASS_INFO>()
                                   .LeftJoin<PMS_PERSON_TAG_RELATE>((classInfo, relate) => relate.DATA_ID == classInfo.CLASS_ID
                                                                                      && relate.PERSON_TAG_ID == personTagId
                                                                                      && relate.DATA_TYPE == "REC_CLASS")
                                   .Where((classInfo, relate) => relate.TAG_DATA_RID == null) // 没有关联
                                   .Where((classInfo, relate) => classInfo.CLASS_TYPE == "0" || classInfo.CLASS_TYPE == "1" || classInfo.CLASS_TYPE == "99")
                                   .WhereIF(keyword.IsNotNullOrEmpty(), (classInfo, relate) => classInfo.CLASS_NAME.Contains(keyword))
                                   .Select((classInfo, relate) => classInfo)
                                   .ToList();

            foreach (var item in data.Distinct().OrderBy(d => d.CLASS_SORT))
            {
                var dto = _mapper.Map<PmsClassInfoDto>(item);
                dto.CLASS_KIND_NAME = dto.CLASS_KIND == "1" ? "健康档案" : "无";
                var dict = EntityHelper.DeserializeObject(item.CLASS_ADDN_CONFIG);
                foreach (var d in dict)
                {
                    if (d.Key == "IF_UPLOAD_FILE")
                        dto.IF_UPLOAD_FILE = d.Value != null && d.Value.ToString() == "1" ? "1" : "0";
                    if (d.Key == "IF_MUST_UPLOAD_FILE")
                        dto.IF_MUST_UPLOAD_FILE = d.Value != null && d.Value.ToString() == "1" ? "1" : "0";
                }
                result.Add(dto);
            }
            return result;
        }

        /// <summary>
        /// 取消“档案类型”与当前“人员标签”的关联关系
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public string RemovePersonTagClassAssociation(string personTagId, List<string> classIdList, string hisName)
        {
            var result = _soa.Db.Deleteable<PMS_PERSON_TAG_RELATE>()
                                   .Where(r => r.PERSON_TAG_ID == personTagId
                                              && r.DATA_TYPE == "REC_CLASS"
                                              && classIdList.Contains(r.DATA_ID))
                                   .ExecuteCommand();

            return "移除成功！";
        }


        /// <summary>
        /// 建立“档案类型”与当前“人员标签”的关联关系
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public string AddPersonTagClassAssociation(string personTagId, List<string> classIdList, string hisName)
        {
            var now = DateTime.Now;
            List<PMS_PERSON_TAG_RELATE> entities = new List<PMS_PERSON_TAG_RELATE>();
            foreach (var classId in classIdList.Where(a => a.IsNotNullOrEmpty()).Distinct())
            {
                var newRelation = new PMS_PERSON_TAG_RELATE
                {
                    TAG_DATA_RID = IDGenHelper.CreateGuid(),
                    HOSPITAL_ID = _httpContext.GetHospitalId(),
                    PERSON_TAG_ID = personTagId,
                    DATA_TYPE = "REC_CLASS",
                    DATA_ID = classId,
                    FIRST_RPERSON = hisName,
                    FIRST_RTIME = now,
                    LAST_MPERSON = hisName,
                    LAST_MTIME = now
                };
                entities.Add(newRelation);
            }
            _soa.Db.Insertable(entities).ExecuteCommand();
            return "添加成功！";
        }

        #endregion


        #region 维护数据项

        /// <summary>
        /// 获取维护数据项记录信息树
        /// </summary>
        /// <param name="hospital_id"></param>
        /// <returns></returns>
        public List<SysSetUpInfoDto> GetClassDataInfo(string hospital_id)
        {
            hospital_id = hospital_id ?? _httpContext.GetHospitalId();
            var varMoudle = _soa.Db.Queryable<SYS6_MODULE_FUNC_DICT>().Where(p => p.HOSPITAL_ID == hospital_id && p.MODULE_ID == "H81" && p.SETUP_CLASS == "B05-52|B05-53")
               .Select(s => new
               {
                   SETUP_ID = s.SETUP_ID,
                   SETUP_CNAME = s.SETUP_CNAME,
                   SETUP_NAME = s.SETUP_NAME,
                   SETUP_STATE = s.SETUP_STATE,
                   SETUP_SORT = s.SETUP_SORT,
                   REMARK = s.REMARK,
                   FORM_JSON = s.FORM_JSON
               }).OrderBy(w => w.SETUP_SORT).ToList();

            List<SysSetUpInfoDto> list = new List<SysSetUpInfoDto>();
            List<SysSetUpInfoDto> listChild = new List<SysSetUpInfoDto>();
            List<PMS_ADDN_CLASS_INFO> listAddnClass = _IModuleLabGroupService.GetPmsAddnClassInfo(true);
            SysSetUpInfoDto healthRecord = null;  //健康档案

            //加入基本信息
            SysSetUpInfoDto baseInfo = new SysSetUpInfoDto();
            baseInfo.SETUP_CNAME = "基本信息";
            baseInfo.SETUP_STATE = "1";
            baseInfo.SETUP_ID = "PERSON_BASIC_INFO";
            baseInfo.SETUP_SORT = "00000";
            baseInfo.IS_HIDE = 0;
            list.Add(baseInfo);

            foreach (var moudle in varMoudle)
            {
                SysSetUpInfoDto sysSetUpInfoDto = new SysSetUpInfoDto();
                sysSetUpInfoDto.SETUP_CNAME = moudle.SETUP_CNAME;
                sysSetUpInfoDto.SETUP_STATE = moudle.SETUP_STATE;
                sysSetUpInfoDto.SETUP_ID = moudle.SETUP_ID;
                sysSetUpInfoDto.REMARK = moudle.REMARK;
                sysSetUpInfoDto.SETUP_SORT = moudle.SETUP_SORT;
                int index = listAddnClass.FindIndex(w => w.FORM_SETUP_ID == moudle.SETUP_ID);
                if (index > -1)
                {
                    if (listAddnClass[index].CLASS_ID == "PMS_HEALTH_DOC_LIST")
                        healthRecord = sysSetUpInfoDto;
                    sysSetUpInfoDto.IF_NEW = listAddnClass[index].CLASS_TYPE;
                    sysSetUpInfoDto.CLASS_KIND = listAddnClass[index].CLASS_KIND;
                    sysSetUpInfoDto.SMBL_FLAG = listAddnClass[index].SMBL_FLAG;
                    sysSetUpInfoDto.SMBL_REC_FLAG = listAddnClass[index].SMBL_REC_FLAG;
                    //原分类默认上传附件
                    if (sysSetUpInfoDto.IF_NEW == "0")
                    {
                        sysSetUpInfoDto.IF_UPLOAD_FILE = "1";
                        sysSetUpInfoDto.IS_HIDE = 0;
                        sysSetUpInfoDto.IS_AUDITABLE = 1;
                        sysSetUpInfoDto.IS_PROP_EDITABLE = 1;
                    }
                    else
                    {
                        sysSetUpInfoDto.IS_HIDE = listAddnClass[index].IS_HIDE;
                        sysSetUpInfoDto.IS_AUDITABLE = listAddnClass[index].IS_AUDITABLE;
                        sysSetUpInfoDto.IS_PROP_EDITABLE = listAddnClass[index].IS_PROP_EDITABLE;
                    }
                    if (listAddnClass[index].CLASS_ADDN_CONFIG.IsNotNullOrEmpty())
                    {
                        Dictionary<string, object> dict = JsonConvert.DeserializeObject<Dictionary<string, object>>(listAddnClass[index].CLASS_ADDN_CONFIG);
                        foreach (var item in dict)
                        {
                            if (item.Key == "IF_UPLOAD_FILE")
                                sysSetUpInfoDto.IF_UPLOAD_FILE = item.Value != null && item.Value.ToString() == "1" ? "1" : "0";
                            if (item.Key == "IF_MUST_UPLOAD_FILE")
                                sysSetUpInfoDto.IF_MUST_UPLOAD_FILE = item.Value != null && item.Value.ToString() == "1" ? "1" : "0";
                        }
                    }
                    if (sysSetUpInfoDto.CLASS_KIND == "1")
                        listChild.Add(sysSetUpInfoDto);
                    else if (listAddnClass[index].CLASS_ID != "PMS_HEALTH_DOC_LIST")
                        list.Add(sysSetUpInfoDto);
                    //填充数据项类型
                    FillDataTypeInfo(listAddnClass[index], moudle.FORM_JSON, sysSetUpInfoDto);
                }
            }
            list = list.OrderBy(w => w.SETUP_SORT).ToList();
            if (healthRecord != null)
            {
                healthRecord.CHILD = listChild;
                list.Insert(1, healthRecord);
            }
            return list;
        }

        //填充数据项类型
        private void FillDataTypeInfo(PMS_ADDN_CLASS_INFO classItem, string formJson, SysSetUpInfoDto sysSetUpInfoDto)
        {
            List<string> sysClassIds = _soa.Db.Queryable<SYS6_BASE_DATA_CLASS>()
                                        .Where(a => a.DATA_TABLE == "SYS6_BASE_DATA" && a.CLASS_STATE == "1")
                                        .Select(a => a.CLASS_ID)
                                        .ToList();

            sysSetUpInfoDto.DATA_TYPE_LIST = new List<DATA_TYPE>();
            if (formJson.IsNotNullOrEmpty())
            {
                PageSettingForm json = null;
                try
                {
                    json = JsonConvert.DeserializeObject<PageSettingForm>(formJson);
                }
                catch (Exception ex)
                {
                    Log.Error(ex.ToString());
                }
                if (json != null || json.form != null)
                {
                    foreach (Form form in json.form)
                    {
                        if (form.dataClass.IsNotNullOrEmpty())
                        {
                            var dataType = new DATA_TYPE
                            {
                                DATA_TYPE_NAME = form.formName,
                                DATA_TYPE_TABLE = "OA_BASE_DATA",
                                DATA_TYPE_CLASS = form.dataClass,
                                DATA_TYPE_UID = Guid.NewGuid().ToString(),
                                IS_EDITABLE = !sysClassIds.Contains(form.dataClass)  //固定数据项不允许编辑
                            };
                            sysSetUpInfoDto.DATA_TYPE_LIST.Add(dataType);
                        }
                    }
                }
            }
            ////添加教育背景记录数据类型
            //if (classItem.CLASS_ID == "PMS_EDUCATION_LIST")
            //{
            //    sysSetUpInfoDto.DATA_TYPE_LIST = new List<DATA_TYPE>();
            //    var dataType1 = new DATA_TYPE
            //    {
            //        DATA_TYPE_NAME = "学历",
            //        DATA_TYPE_TABLE = "OA_BASE_DATA",
            //        DATA_TYPE_CLASS = "最高学历",
            //        DATA_TYPE_UID = Guid.NewGuid().ToString(),
            //        IS_EDITABLE = false,
            //    };
            //    sysSetUpInfoDto.DATA_TYPE_LIST.Add(dataType1);
            //    var dataType2 = new DATA_TYPE
            //    {
            //        DATA_TYPE_NAME = "学位",
            //        DATA_TYPE_TABLE = "OA_BASE_DATA",
            //        DATA_TYPE_CLASS = "最高学位",
            //        DATA_TYPE_UID = Guid.NewGuid().ToString(),
            //        IS_EDITABLE = false,
            //    };
            //    sysSetUpInfoDto.DATA_TYPE_LIST.Add(dataType2);
            //}
            //添加证书记录数据类型
            if (classItem.CLASS_ID == "PMS_SKILL_CERTIFICATE_LIST")
            {
                //var dataType1 = new DATA_TYPE
                //{
                //    DATA_TYPE_NAME = "证书分类",
                //    DATA_TYPE_TABLE = "OA_BASE_DATA",
                //    DATA_TYPE_CLASS = "人事技能证书类型",
                //    DATA_TYPE_UID = Guid.NewGuid().ToString(),
                //};
                //sysSetUpInfoDto.DATA_TYPE_LIST.Add(dataType1);
                var dataType2 = new DATA_TYPE
                {
                    DATA_TYPE_NAME = "证书类型",
                    DATA_TYPE_TABLE = "OA_CERTIFICATE_DICT",
                    DATA_TYPE_CLASS = "OA_CERTIFICATE_DICT",
                    DATA_TYPE_UID = Guid.NewGuid().ToString(),
                };
                sysSetUpInfoDto.DATA_TYPE_LIST.Add(dataType2);
            }
            //添加评估记录数据类型
            if (classItem.CLASS_ID == "PMS_ASSESS_LIST")
            {
                var dataType1 = new DATA_TYPE
                {
                    DATA_TYPE_NAME = "评估分类",
                    DATA_TYPE_TABLE = "OA_BASE_DATA",
                    DATA_TYPE_CLASS = "人员评估分类",
                    DATA_TYPE_UID = Guid.NewGuid().ToString(),
                };
                sysSetUpInfoDto.DATA_TYPE_LIST.Add(dataType1);
                var dataType2 = new DATA_TYPE
                {
                    DATA_TYPE_NAME = "评估类型",
                    DATA_TYPE_TABLE = "OA_EVAL_STAGE_DICT",
                    DATA_TYPE_CLASS = "ASSESS",
                    DATA_TYPE_UID = Guid.NewGuid().ToString(),
                };
                sysSetUpInfoDto.DATA_TYPE_LIST.Add(dataType2);
            }
            //添加考试记录数据类型
            if (classItem.CLASS_ID == "PMS_EXAM_LIST")
            {
                var dataType1 = new DATA_TYPE
                {
                    DATA_TYPE_NAME = "考试分类",
                    DATA_TYPE_TABLE = "OA_BASE_DATA",
                    DATA_TYPE_CLASS = "人员考试分类",
                    DATA_TYPE_UID = Guid.NewGuid().ToString(),
                };
                sysSetUpInfoDto.DATA_TYPE_LIST.Add(dataType1);
                var dataType2 = new DATA_TYPE
                {
                    DATA_TYPE_NAME = "考试类型",
                    DATA_TYPE_TABLE = "OA_EVAL_STAGE_DICT",
                    DATA_TYPE_CLASS = "EXAM",
                    DATA_TYPE_UID = Guid.NewGuid().ToString()
                };
                sysSetUpInfoDto.DATA_TYPE_LIST.Add(dataType2);
            }
            //添加培训记录数据类型
            if (classItem.CLASS_ID == "PMS_TRAIN_LIST")
            {
                //var dataType1 = new DATA_TYPE
                //{
                //    DATA_TYPE_NAME = "培训分类",
                //    DATA_TYPE_TABLE = "OA_BASE_DATA",
                //    DATA_TYPE_CLASS = "人员培训分类",
                //    DATA_TYPE_UID = Guid.NewGuid().ToString()
                //};
                //sysSetUpInfoDto.DATA_TYPE_LIST.Add(dataType1);
                var dataType2 = new DATA_TYPE
                {
                    DATA_TYPE_NAME = "培训类型",
                    DATA_TYPE_TABLE = "OA_EVAL_STAGE_DICT",
                    DATA_TYPE_CLASS = "TRAIN",
                    DATA_TYPE_UID = Guid.NewGuid().ToString()
                };
                sysSetUpInfoDto.DATA_TYPE_LIST.Add(dataType2);
            }
        }


        public Dictionary<string, List<BaseDataAndTagDto>> BatchGetBaseDataAndTag(string menuName, string hospital_id)
        {
            var result = new Dictionary<string, List<BaseDataAndTagDto>>();
            var classIds = new List<string>();
            if (menuName.IsNullOrEmpty() || menuName.ToLower() == "baseinfo")
            {
                //var baseInfoFields = _systemService.GetGetFieldDicts(hospital_id, "H81", "基本信息");
                //if (baseInfoFields.success)
                //{
                //    try
                //    {
                //        var fields = JsonConvert.DeserializeObject<List<SYS6_FUNC_FIELD_DICT>>(baseInfoFields.data?.ToString());
                //        foreach (SYS6_FUNC_FIELD_DICT field in fields)
                //        {
                //            if (field.STYLE_JSON.IsNotNullOrEmpty())
                //            {
                //                var styleJson = JsonConvert.DeserializeObject<StyleJson>(field.STYLE_JSON);
                //                if (styleJson != null && styleJson.dataClass.IsNotNullOrEmpty() && !classIds.Contains(styleJson.dataClass))
                //                {
                //                    classIds.Add(styleJson.dataClass);
                //                }
                //            }
                //        }
                //    }
                //    catch (Exception ex)
                //    {
                //        Log.Error($"BatchGetBaseDataAndTag执行发出异常：{ex.ToString()}");
                //    }
                //}

                //接口有缓存，暂时改为查表
                var baseInfoFields = _soa.Db.Queryable<SYS6_FUNC_FIELD_DICT>().Select(a => a.STYLE_JSON).ToList();
                foreach (string field in baseInfoFields)
                {
                    if (field.IsNotNullOrEmpty())
                    {
                        try
                        {
                            var styleJson = JsonConvert.DeserializeObject<StyleJson>(field);
                            if (styleJson != null && styleJson.dataClass.IsNotNullOrEmpty() && !classIds.Contains(styleJson.dataClass))
                            {
                                classIds.Add(styleJson.dataClass);
                            }
                        }
                        catch (Exception ex) { Log.Error($"BatchGetBaseDataAndTag执行发出异常：{ex.ToString()}"); }
                    }
                }
            }

            if (menuName.IsNullOrEmpty() || menuName.ToLower() == "record")
            {
                List<SYS6_MODULE_FUNC_DICT> dicts = _soa.Db.Queryable<SYS6_MODULE_FUNC_DICT>().Where(a => a.SETUP_STATE == "1" && a.MODULE_ID == "H81" && a.SETUP_CLASS == "B05-52|B05-53" && a.HOSPITAL_ID == hospital_id).ToList();
                foreach (var dic in dicts)
                {
                    if (dic.FORM_JSON.IsNotNullOrEmpty())
                    {
                        PageSettingForm json = null;
                        try
                        {
                            json = JsonConvert.DeserializeObject<PageSettingForm>(dic.FORM_JSON);
                        }
                        catch (Exception ex)
                        {
                            Log.Error(ex.ToString());
                        }
                        if (json == null || json.form == null)
                            break;
                        foreach (Form form in json.form)
                        {
                            if (form.dataClass.IsNotNullOrEmpty() && !classIds.Contains(form.dataClass))
                            {
                                classIds.Add(form.dataClass);
                            }
                        }
                    }
                }
                classIds.AddRange(new List<string> { "人员评估分类", "人员考试分类", "人员评估评价结果", "考试评价结果" });//评估记录和考试记录无法在工具箱获取
            }

            var relates = _soa.Db.Queryable<PMS_PERSON_TAG_RELATE>()
                            .InnerJoin<PMS_PERSON_TAG_DICT>((rel, tag) => rel.PERSON_TAG_ID == tag.PERSON_TAG_ID && tag.TAG_STATE == "1")
                            .Where((rel, tag) => rel.DATA_TYPE == "BASE_DATA") //DATA_TYPE把"SYS6_BASE_DATA" 和"OA_BASE_DATA"合并为"BASE_DATA"
                            .Select((rel, tag) => new { rel.DATA_ID, rel.DATA_TYPE, tag.PERSON_TAG_ID, tag.TAG_NAME })
                            .ToList();

            foreach (var classId in classIds)
            {
                var dataList = new List<BaseDataAndTagDto>();
                var baseData = new List<BaseDataDto>();
                var tableName = "OA_BASE_DATA";
                List<OA_BASE_DATA> oaBaseData = _IBaseDataServices.GetOaBaseData()
                    .Where(x => x.CLASS_ID == classId && (x.HOSPITAL_ID == _httpContext.GetHospitalId() || x.HOSPITAL_ID == "H0000"))
                    .OrderBy(x => x.DATA_SORT)
                    .ToList();
                if (oaBaseData.Any())
                {
                    baseData = _mapper.Map<List<BaseDataDto>>(oaBaseData);
                }
                if (!baseData.Any())
                {
                    tableName = "SYS6_BASE_DATA";
                    baseData = _IBaseDataServices.GetSys6BaseData()
                        .Where(a => a.CLASS_ID == classId)
                        .WhereIF(classId == "技师职称" || classId == "护士职称" || classId == "医师职称" || classId == "研究员职称", (x, _) => x.DATA_CNAME != "无职称")
                        .Select(a => new BaseDataDto
                        {
                            CLASS_ID = a.CLASS_ID,
                            DATA_ID = a.DATA_ID,
                            DATA_NAME = a.DATA_CNAME,
                            CUSTOM_CODE = a.CUSTOM_CODE,
                            DATA_SNAME = a.DATA_SNAME,
                            STATE_FLAG = a.DATA_STATE,
                            DATA_SORT = a.DATA_SORT,
                            SPELL_CODE = a.SPELL_CODE,
                            DATA_ENAME = a.DATA_ENAME,
                        }).ToList();
                }

                foreach (var data in baseData.Where(a => a.STATE_FLAG != "2"))
                {
                    var thisRelates = relates.FindAll(a => a.DATA_ID == $"{data.CLASS_ID}|{data.DATA_ID}");
                    var tagIds = thisRelates.Select(a => a.PERSON_TAG_ID).ToList();

                    var dto = new BaseDataAndTagDto
                    {
                        DATA_ID = data.DATA_ID,
                        CLASS_ID = data.CLASS_ID,
                        DATA_NAME = data.DATA_NAME,
                        DATA_SNAME = data.DATA_SNAME,
                        DATA_ENAME = data.DATA_ENAME,
                        STATE_FLAG = data.STATE_FLAG,
                        DATA_SORT = data.DATA_SORT,
                        DATA_TABLE = tableName,
                        PERSON_TAG_IDS = tagIds,
                        PERSON_TAG_NAMES = string.Join('、', thisRelates.Select(a => a.TAG_NAME)),
                    };
                    dataList.Add(dto);
                }
                result.Add(classId, dataList);
            }
            return result;
        }

        /// <summary>
        /// 获取维护数据项基础数据及标签
        /// </summary>
        /// <param name="classId"></param>
        /// <returns></returns>
        public List<OaBaseDataAndTagDto> GetBaseDataAndTag(string classId, string stateFlag, string tagId, string keyWord)
        {
            var result = new List<OaBaseDataAndTagDto>();
            var tableName = "OA_BASE_DATA";
            List<OA_BASE_DATA> baseData = _IBaseDataServices.GetOaBaseData(classId, stateFlag);
            if (!baseData.Any())
            {
                baseData = _IBaseDataServices.GetSys6BaseData()
                    .Where(a => a.CLASS_ID == classId && (stateFlag == null || a.DATA_STATE == stateFlag))
                    .Select(a => new OA_BASE_DATA
                    {
                        CLASS_ID = a.CLASS_ID,
                        DATA_ID = a.DATA_ID,
                        DATA_NAME = a.DATA_CNAME,
                        CUSTOM_CODE = a.CUSTOM_CODE,
                        DATA_SNAME = a.DATA_SNAME,
                        STATE_FLAG = a.DATA_STATE,
                        FIRST_RPERSON = a.FIRST_RPERSON,
                        FIRST_RTIME = a.FIRST_RTIME,
                        LAST_MPERSON = a.LAST_MPERSON,
                        LAST_MTIME = a.LAST_MTIME,
                        DATA_SORT = a.DATA_SORT,
                        SPELL_CODE = a.SPELL_CODE,
                        HOSPITAL_ID = a.HOSPITAL_ID,
                        DATA_ENAME = a.DATA_ENAME,
                    }).ToList();
                tableName = "SYS6_BASE_DATA";
            }
            var dataIds = baseData.Select(a => $"{a.CLASS_ID}|{a.DATA_ID}").ToList();
            var relates = _soa.Db.Queryable<PMS_PERSON_TAG_RELATE>()
                            .InnerJoin<PMS_PERSON_TAG_DICT>((rel, tag) => rel.PERSON_TAG_ID == tag.PERSON_TAG_ID && tag.TAG_STATE == "1")
                            .Where((rel, tag) => rel.DATA_TYPE == "BASE_DATA" && dataIds.Contains(rel.DATA_ID))   //DATA_TYPE == "BASE_DATA"是合并了OA_BASE_DATA和SYS6_BASE_DATA
                            .Select((rel, tag) => new { rel.DATA_ID, tag.PERSON_TAG_ID, tag.TAG_NAME })
                            .ToList();

            foreach (var data in baseData.Where(a => a.STATE_FLAG != "2"))
            {
                var thisRelates = relates.FindAll(a => a.DATA_ID == $"{data.CLASS_ID}|{data.DATA_ID}");
                var tagIds = thisRelates.Select(a => a.PERSON_TAG_ID).ToList();
                if (tagId.IsNullOrEmpty() || tagIds.Contains(tagId))
                {
                    var dto = new OaBaseDataAndTagDto
                    {
                        DATA_ID = data.DATA_ID,
                        CLASS_ID = data.CLASS_ID,
                        DATA_NAME = data.DATA_NAME,
                        DATA_SNAME = data.DATA_SNAME,
                        DATA_ENAME = data.DATA_ENAME,
                        STATE_FLAG = data.STATE_FLAG,
                        REMARK = data.REMARK,
                        FIRST_RPERSON = data.FIRST_RPERSON,
                        FIRST_RTIME = data.FIRST_RTIME,
                        LAST_MPERSON = data.LAST_MPERSON,
                        LAST_MTIME = data.LAST_MTIME,
                        DATA_SORT = data.DATA_SORT,
                        DATA_TABLE = tableName,
                        PERSON_TAG_IDS = tagIds,
                        PERSON_TAG_NAMES = string.Join('、', thisRelates.Select(a => a.TAG_NAME)),
                    };
                    if (keyWord.IsNullOrEmpty() || data.DATA_NAME?.Contains(keyWord) == true)
                        result.Add(dto);
                }
            }
            return result;
        }
        /// <summary>
        /// 保存维护数据项基础数据及标签
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public string SaveBaseDataAndTag(OaBaseDataAndTagDto dto)
        {
            string dataId = dto.DATA_ID;
            string sys6ClassTable = _soa.Db.Queryable<SYS6_BASE_DATA_CLASS>().Where(a => a.CLASS_ID == dto.CLASS_ID && a.CLASS_STATE == "1").Select(a => a.DATA_TABLE).First();

            if (sys6ClassTable == "OA_BASE_DATA"
               || (sys6ClassTable == null && !_soa.Db.Queryable<SYS6_BASE_DATA>().Any(a => a.CLASS_ID == dto.CLASS_ID && a.DATA_STATE == "1")))
            {
                dataId = _IBaseDataServices.AddOrModifyOaBaseData(dto);
            }
            if (dataId == null)
                throw new BizException("该数据项不可修改！");

            var relates = _soa.Db.Queryable<PMS_PERSON_TAG_RELATE>()
                              .Where(rel => rel.DATA_TYPE == "BASE_DATA" && rel.DATA_ID == $"{dto.CLASS_ID}|{dataId}")
                              .ToList(); //DATA_TYPE == "BASE_DATA"是合并了OA_BASE_DATA和SYS6_BASE_DATA

            Func<IEnumerable<string>, IEnumerable<string>, bool> isEqual = (list1, list2) => list1.All(a => list2.Any(b => a == b))
               && list1.Count() == list2.Count();

            //标签已经变化
            if (dto.PERSON_TAG_IDS != null && !isEqual(relates.Select(tag => tag.PERSON_TAG_ID), dto.PERSON_TAG_IDS))
            {
                var newRelates = new List<PMS_PERSON_TAG_RELATE>();
                foreach (var tagId in dto.PERSON_TAG_IDS)
                {
                    //DATA_TYPE == "BASE_DATA"是合并了OA_BASE_DATA和SYS6_BASE_DATA
                    newRelates.Add(CreatePersonTagEntity(dto.HOSPITAL_ID, tagId, "BASE_DATA", $"{dto.CLASS_ID}|{dataId}"));
                }
                _soa.Db.Insertable(newRelates).ExecuteCommand();
                _soa.Db.Deleteable(relates).ExecuteCommand();
            }
            return dataId;
        }
        private PMS_PERSON_TAG_RELATE CreatePersonTagEntity(string hospitalId, string tagId, string dataType, string dataId)
        {
            var now = DateTime.Now;
            return new PMS_PERSON_TAG_RELATE
            {
                TAG_DATA_RID = IDGenHelper.CreateGuid(),
                HOSPITAL_ID = hospitalId ?? _httpContext.GetHospitalId(),
                PERSON_TAG_ID = tagId,
                DATA_TYPE = dataType,
                DATA_ID = dataId,
                FIRST_RPERSON = _httpContext.GetUserHisName(),
                FIRST_RTIME = now,
                LAST_MPERSON = _httpContext.GetUserHisName(),
                LAST_MTIME = now,
            };
        }
        /// <summary>
        /// 删除维护数据项基础数据及标签
        /// </summary>
        /// <param name="Data"></param>
        /// <returns></returns>
        public string DeleteBaseDataAndTag(OaBaseDataAndTagDto Data)
        {
            if (_IBaseDataServices.DelOaBaseData(Data))
                return "删除成功！";
            return "删除失败！";
        }

        /// <summary>
        /// 启用维护数据项基础数据及标签
        /// </summary>
        /// <param name="Data"></param>
        /// <returns></returns>
        public string EnableBaseDataAndTag(OaBaseDataAndTagDto Data)
        {
            if (_IBaseDataServices.EnableOaBaseData(Data))
                return "启用成功！";
            return "启用失败！";
        }

        /// <summary>
        /// 禁用维护数据项基础数据及标签
        /// </summary>
        /// <param name="Data"></param>
        /// <returns></returns>
        public string DisableBaseDataAndTag(OaBaseDataAndTagDto Data)
        {
            if (_IBaseDataServices.DisableOaBaseData(Data))
                return "禁用成功！";
            return "禁用失败！";
        }

        /// <summary>
        /// 维护数据项基础数据及标签排序
        /// </summary>
        /// <param name="Data"></param>
        /// <returns></returns>
        public string SortBaseDataAndTag(List<OaBaseDataAndTagDto> sortedData)
        {
            var sortedBd = new List<OaBaseDataDto>();
            sortedData.ForEach(data => sortedBd.Add(data));
            if (_IBaseDataServices.SortOaBaseData(sortedBd))
                return "禁用成功！";
            return "禁用失败！";
        }


        /// <summary>
        /// 获取评估阶段字典
        /// </summary>
        /// <param name="stageType">评估阶段类型</param>
        /// <param name="stageClass">字典分类</param>
        /// <param name="tagId"></param>
        /// <param name="eplanFlag"></param>
        /// <param name="keyWord"></param>
        /// <param name="stateFlag"></param>
        /// <returns></returns>
        public List<OaEvalStageDictDto> GetEvalStageDict(string stageType, string stageClass, string tagId, string eplanFlag, string keyWord, string stateFlag)
        {
            var result = new List<OaEvalStageDictDto>();
            List<OA_EVAL_STAGE_DICT> stageList = _soa.GetRepository<OA_EVAL_STAGE_DICT>().DbSet()
                .Where(w => w.EVAL_STAGE_TYPE == stageType)
                .WhereIF(stageClass.IsNotNullOrEmpty(), w => w.EVAL_STAGE_CLASS == stageClass)
                .WhereIF(eplanFlag.IsNotNullOrEmpty(), w => w.EPLAN_FLAG == eplanFlag)
                .WhereIF(stateFlag.IsNotNullOrEmpty(), w => w.EVAL_STAGE_STATE == stateFlag)
                .WhereIF(keyWord.IsNotNullOrEmpty(), w => w.EVAL_STAGE_NAME.Contains(keyWord)).ToList()
                .OrderBy(w => w.EVAL_STAGE_SORT?.Length).ThenBy(w => w.EVAL_STAGE_SORT).ToList();

            //查证书
            var cerIds = stageList.Select(a => a.CERTIFICATE_DID).Distinct().ToList();
            var cers = _IEvaluatePlanService.GetCertificateDict(hospitalId: null, cerType: null, state: null, level: null, ePlanFlag: null, searchKey: null, CERTIFICATE_DID_LIST: cerIds);

            //查标签
            var dataIds = stageList.Select(a => a.EVAL_STAGE_ID).ToList();
            var relates = _soa.Db.Queryable<PMS_PERSON_TAG_RELATE>()
                            .InnerJoin<PMS_PERSON_TAG_DICT>((rel, tag) => rel.PERSON_TAG_ID == tag.PERSON_TAG_ID && tag.TAG_STATE == "1")
                            .Where((rel, tag) => rel.DATA_TYPE == "OA_EVAL_STAGE_DICT" && dataIds.Contains(rel.DATA_ID))
                            .Select((rel, tag) => new { rel.DATA_ID, tag.PERSON_TAG_ID, tag.TAG_NAME })
                            .ToList();

            //页面上“培训分类”的区分(OA_BASE_DATA)：TRAIN-培训 ASSESS-评估 EXAM-考试
            var baseDataClassId = stageType switch
            {
                "TRAIN" => "人员培训分类",
                "ASSESS" => "人员评估分类",
                "EXAM" => "人员考试分类"
            };
            var typeBaseDatas = _IBaseDataServices.GetOaBaseData(baseDataClassId);
            var frequecyBaseDatas = _IBaseDataServices.GetOaBaseData("评估阶段频次单位");

            foreach (var entity in stageList)
            {
                var thisRelates = relates.FindAll(a => a.DATA_ID == entity.EVAL_STAGE_ID);
                var tagIds = thisRelates.Select(a => a.PERSON_TAG_ID).ToList();

                if (tagId.IsNullOrEmpty() || tagIds.Contains(tagId))
                {
                    var stage = _mapper.Map<OaEvalStageDictDto>(entity);
                    stage.EVAL_STAGE_CLASS_BASEDATACLASSID = baseDataClassId;
                    stage.EVAL_STAGE_CLASS_NAME = typeBaseDatas.Find(a => a.DATA_ID == entity.EVAL_STAGE_CLASS)?.DATA_NAME;
                    stage.FREQUENCY_UNIT_NAME = frequecyBaseDatas.Find(a => a.DATA_ID == entity.FREQUENCY_UNIT)?.DATA_NAME;
                    stage.PERSON_TAG_IDS = tagIds;
                    stage.PERSON_TAG_NAMES = string.Join('、', thisRelates.Select(a => a.TAG_NAME));
                    stage.CERTIFICATE = cers.Find(a => a.CERTIFICATE_DID == entity.CERTIFICATE_DID);
                    stage.EVAL_STAGE_SORT = EntityHelper.GetSort(); //排序生成方法

                    result.Add(stage);
                }

            }

            return result;
        }


        /// <summary>
        /// 新增或修改评估阶段配置
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public string SaveEvalStageDict(OaEvalStageDictDto dto)
        {
            DateTime dt = DateTime.Now;
            OA_EVAL_STAGE_DICT entity = _mapper.Map<OA_EVAL_STAGE_DICT>(dto);
            var claim = _httpContext.HttpContext.User.ToClaimsDto();
            try
            {
                using (_soa.Begin())
                {
                    if (entity.EVAL_STAGE_ID.IsNotNullOrEmpty())
                    {
                        var item = _soa.GetRepository<OA_EVAL_STAGE_DICT>().GetFirstOrDefault(w => w.EVAL_STAGE_ID == entity.EVAL_STAGE_ID);
                        if (item != null)
                        {
                            // 修改逻辑
                            item.EVAL_STAGE_NAME = entity.EVAL_STAGE_NAME;
                            item.FREQUENCY_VALUE = entity.FREQUENCY_VALUE;
                            item.FREQUENCY_UNIT = entity.FREQUENCY_UNIT;
                            item.EPLAN_FLAG = entity.EPLAN_FLAG;
                            item.CER_REQUIRE_FLAG = entity.CER_REQUIRE_FLAG;
                            item.CERTIFICATE_DID = entity.CERTIFICATE_DID;
                            item.EVAL_STAGE_SORT = entity.EVAL_STAGE_SORT;
                            item.REMARK = entity.REMARK;
                            item.LAST_MPERSON = claim.HIS_NAME;
                            item.LAST_MTIME = dt;
                            _soa.GetRepository<OA_EVAL_STAGE_DICT>().Update(item);
                            entity.EVAL_STAGE_STATE = item.EVAL_STAGE_STATE;
                        }
                        else
                        {
                            throw new Exception("该评估阶段配置不存在！");
                        }
                    }
                    else
                    {
                        // 新增逻辑
                        entity.EVAL_STAGE_ID = "ST" + _IBaseDataServices.GetTableMaxNumber("OA_EVAL_STAGE_DICT", "EVAL_STAGE_ID", 1, 1, "XH_OA").data.ToString().PadLeft(6, '0');
                        entity.HOSPITAL_ID = claim.HOSPITAL_ID;
                        entity.FIRST_RPERSON = claim.HIS_NAME;
                        entity.FIRST_RTIME = dt;
                        entity.LAST_MPERSON = claim.HIS_NAME;
                        entity.LAST_MPERSON = claim.HIS_NAME;
                        entity.LAST_MTIME = dt;
                        entity.EVAL_STAGE_STATE = "1"; // 默认启用状态
                        entity.EVAL_STAGE_SORT = EntityHelper.GetSort();
                        _soa.Db.Insertable(entity).ExecuteCommand();
                    }
                    _soa.SaveChanges();

                    //查询标签
                    var relates = _soa.Db.Queryable<PMS_PERSON_TAG_RELATE>()
                                      .Where(rel => rel.DATA_TYPE == "OA_EVAL_STAGE_DICT" && rel.DATA_ID == entity.EVAL_STAGE_ID)
                                      .ToList();

                    Func<IEnumerable<string>, IEnumerable<string>, bool> isEqual = (list1, list2) => list1.All(a => list2.Any(b => a == b))
                       && list1.Count() == list2.Count();

                    //标签已经变化
                    if (dto.PERSON_TAG_IDS != null && !isEqual(relates.Select(tag => tag.PERSON_TAG_ID), dto.PERSON_TAG_IDS))
                    {
                        var newRelates = new List<PMS_PERSON_TAG_RELATE>();
                        foreach (var tagId in dto.PERSON_TAG_IDS)
                        {
                            newRelates.Add(CreatePersonTagEntity(dto.HOSPITAL_ID, tagId, "OA_EVAL_STAGE_DICT", entity.EVAL_STAGE_ID));
                        }
                        _soa.Db.Insertable(newRelates).ExecuteCommand();
                        _soa.Db.Deleteable(relates).ExecuteCommand();
                    }
                    //同步规评方案
                    {
                        if (!_soa.Db.Queryable<OA_EVALUATE_PLAN_DICT>().Any(a => a.EPLAN_ID == entity.EVAL_STAGE_ID))
                        {
                            var eplan = new OaEvaluatePlanDict
                            {
                                EPLAN_ID = null,
                                EPLAN_NAME = entity.EVAL_STAGE_NAME,
                                HOSPITAL_ID = entity.HOSPITAL_ID,
                                EPLAN_TYPE = entity.EVAL_STAGE_TYPE switch { "TRAIN" => "2", "ASSESS" => "3", "EXAM" => "4", _ => "" },
                                EPLAN_STATE = entity.EVAL_STAGE_STATE,
                                FIRST_RPERSON = entity.FIRST_RPERSON,
                                FIRST_RTIME = entity.FIRST_RTIME,
                                LAST_MPERSON = entity.LAST_MPERSON,
                                LAST_MTIME = entity.LAST_MTIME,
                                REMARK = entity.REMARK,
                            };
                            _IEvaluatePlanService.SaveEvaluatePlanDict(eplan, claim.HIS_NAME, entity.EVAL_STAGE_ID);
                        }
                        if (entity.EPLAN_FLAG == "1" && entity.EVAL_STAGE_STATE == "1")
                        {
                            _IEvaluatePlanService.UnDeleteEvaluatePlanDict(entity.EVAL_STAGE_ID, claim.HIS_NAME);
                        }
                        else
                        {
                            _IEvaluatePlanService.DeleteEvaluatePlanDict(entity.EVAL_STAGE_ID, claim.HIS_NAME);
                        }
                    }
                }
                _soa.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.LogError("AddOrModifyEvalStageDict异常信息: " + ex.Message);
                throw new Exception(message: "保存评估阶段配置失败！");
            }
            return entity.EVAL_STAGE_ID;
        }

        /// <summary>
        /// 删除评估阶段
        /// </summary>
        /// <param name="stageId"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public bool DeleteEvalStageDict(string stageId)
        {
            return SaveEvalStageDictState(stageId, "2");
        }

        /// <summary>
        /// 启用评估阶段
        /// </summary>
        /// <param name="stageId"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public bool EnableEvalStageDict(string stageId)
        {
            return SaveEvalStageDictState(stageId, "1");
        }
        /// <summary>
        /// 禁用评估阶段
        /// </summary>
        /// <param name="stageId"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public bool DisableEvalStageDict(string stageId)
        {
            return SaveEvalStageDictState(stageId, "0");
        }

        private bool SaveEvalStageDictState(string stageId, string state)
        {
            using (_soa.Begin())
            {
                OA_EVAL_STAGE_DICT data = _soa.GetRepository<OA_EVAL_STAGE_DICT>().GetFirstOrDefault(o => o.EVAL_STAGE_ID.Equals(stageId));
                if (data != null)
                {
                    var claim = _httpContext.HttpContext.User.ToClaimsDto();
                    data.LAST_MPERSON = claim.HIS_NAME;
                    data.LAST_MTIME = DateTime.Now;
                    data.EVAL_STAGE_STATE = state;
                    _soa.Db.Updateable(data).ExecuteCommand();

                    //更新规评方案状态
                    if (data.EPLAN_FLAG == "1" && data.EVAL_STAGE_STATE == "1")
                    {
                        _IEvaluatePlanService.UnDeleteEvaluatePlanDict(data.EVAL_STAGE_ID, claim.HIS_NAME);
                    }
                    else
                    {
                        _IEvaluatePlanService.DeleteEvaluatePlanDict(data.EVAL_STAGE_ID, claim.HIS_NAME);
                    }
                }
                else
                {
                    throw new Exception("该评估阶段配置不存在！");
                }
                _soa.SaveChanges();
            }
            return true;
        }


        /// <summary>
        /// 评估阶段排序
        /// </summary>
        /// <param name="stageIds"></param>
        /// <returns></returns>
        public bool SortEvalStageDict(List<string> stageIds)
        {
            // 过滤掉空或无效的 ID
            stageIds = stageIds.FindAll(id => !string.IsNullOrEmpty(id));
            if (!stageIds.Any())
                return true;

            // 查询需要调整排序的记录
            var stages = _soa.Db.Queryable<OA_EVAL_STAGE_DICT>()
                                      .Where(s => stageIds.Contains(s.EVAL_STAGE_ID))
                                      .ToList();

            // 调整排序号
            foreach (var st in stages)
            {
                int index = stageIds.IndexOf(st.EVAL_STAGE_ID);
                if (index > -1)
                {
                    st.EVAL_STAGE_SORT = index.ToString().PadLeft(10, '0'); // 填充排序号
                }
            }

            // 更新数据库
            int rows = _soa.Db.Updateable(stages).ExecuteCommand();

            // 返回是否成功
            return rows > 0;
        }
        #endregion

        #region 工具箱相关接口
        /// <summary>
        /// 创建标签表单
        /// </summary>
        /// <param name="tagId"></param>
        /// <param name="userName"></param>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        public ResultDto CreateComplexForms(string tagId, string userName, string hospitalId)
        {
            ResultDto resultDto = new ResultDto();
            var tagDict = _soa.Db.Queryable<PMS_PERSON_TAG_DICT>().Where(w => w.PERSON_TAG_ID == tagId).ToList().FirstOrDefault();
            if (tagDict != null)
            {
                resultDto = CreateComplexForms(tagDict, hospitalId, "");
            }
            else
            {
                resultDto.success = false;
                resultDto.msg = "获取标签失败";
            }
            return resultDto;
        }
        /// <summary>
        /// 获取复杂表单
        /// </summary>
        /// <param name="setUpId"></param>
        /// <param name="merge"></param>
        /// <param name="quintId"></param>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        public ResultDto GetComplexForms(string setUpId, string merge, string quintId, string hospitalId)
        {
            return _systemService.GetComplexForms(hospitalId, "H81", setUpId, merge, quintId);
        }

        /// <summary>
        /// 公共库字段修改
        /// </summary>
        /// <param name="dict"></param>
        /// <returns></returns>
        public ResultDto SaveFieldDict(FieldDictDto dict)
        {
            Random random = new Random();
            if (dict.FIELD_ID.IsNullOrEmpty())
                dict.FIELD_ID = "H81" + _IBaseDataServices.GetTableMaxNumber("SYS6_FUNC_FIELD_DICT", "FIELD_ID", 1, 1, "XH_SYS").data.ToString().PadLeft(6, '0');
            if (dict.FIELD_CODE.IsNullOrEmpty() && dict.FIELD_NAME.IsNotNullOrEmpty())
            {
                dict.FIELD_CODE = new SpellAndWbCodeTookit().GetSpellCode(dict.FIELD_NAME) + random.Next(10000, 99999);
            }
            return _systemService.SaveFieldDict(JsonConvert.SerializeObject(dict));
        }

        /// <summary>
        /// 获取公共库字段
        /// </summary>
        /// <param name="hospitalId"></param>
        /// <param name="moduleId"></param>
        /// <param name="filedClass"></param>
        /// <returns></returns>
        public ResultDto GetGetFieldDicts(string hospitalId, string moduleId, string filedClass)
        {
            return _systemService.GetGetFieldDicts(hospitalId, moduleId, filedClass);
        }

        /// <summary>
        /// 初始化基本信息公共库字段
        /// </summary>
        /// <returns></returns>
        public ResultDto InitBaseInfoFieldDict()
        {
            ResultDto result = new ResultDto();
            SYS6_MODULE_FUNC_DICT formDict = _soa.Db.Queryable<SYS6_MODULE_FUNC_DICT>()
                .Where(p => p.SETUP_ID == "H8101424")?.First();
            FormJsonDto formJsonDto = JsonHelper.FromJson<FormJsonDto>(formDict.FORM_JSON);
            string strJson = @"[
  {
    group: '布局',
    formId: 'col_1',
    icon: 'iconyilie',
    formName: '一行一列',
    dataType: 'Col1',
  },
  {
    group: '布局',
    formId: 'col_2',
    icon: 'iconlianglie',
    formName: '一行两列',
    dataType: 'Col2',
  },
  {
    group: '布局',
    formId: 'col_3',
    icon: 'iconsanlie',
    formName: '一行三列',
    dataType: 'Col3',
  },
  {
    group: '布局',
    formId: 'col_4',
    icon: 'iconsilie',
    formName: '一行四列',
    dataType: 'Col4',
  },
  {
    group: '基本',
    formId: 'title',
    icon: 'iconbiaotilan',
    formName: '标题',
    isForm: false,
    dataType: 'XTitle',
    wigetProps: {
      button: true,
      groupKey: '',
    },
    propslist: ['button', 'buttonSize', 'groupKey'],
  },
  {
    group: '基本',
    formId: 'fengexian',
    icon: 'iconfengexian1',
    formName: '分隔线',
    isForm: false,
    dataType: 'Divider',
    wigetProps: {
      button: true,
      groupKey: '',
    },
    propslist: ['button', 'buttonSize', 'groupKey'],
  },
  {
    group: '基本',
    formId: 'danhangwenben',
    icon: 'icondanhangwenben',
    formName: '单行文本',
    dataType: 'Input',
    isForm: true,
    rules: [{ required: true }],
    wigetProps: {
      placeholder: '请输入',
      allowClear: true,
      maxLength: 200,
      disabled: false,
    },
    propslist: [
      'dataType',
      'required',
      'allowClear',
      'placeholder',
      'maxLength',
      'prefix',
      'suffix',
      'variant',
      'disabled',
      'showCount',
    ],
  },
  {
    group: '基本',
    formId: 'duohangwenben',
    icon: 'iconduohangwenben',
    formName: '多行文本',
    dataType: 'TextArea',
    isForm: true,
    wigetProps: {},
    propslist: [
      'dataType',
      'required',
      'allowClear',
      'placeholder',
      'maxLength',
      'minRows',
      'maxRows',
      'variant',
      'disabled',
      'showCount',
    ],
  },
  {
    group: '基本',
    formId: 'xialakuang',
    icon: 'iconxialakuang',
    formName: '下拉框',
    isForm: true,
    dataType: 'Select',
    wigetProps: {
      options: [
        { value: '选项1', label: '选项1' },
        { value: '选项2', label: '选项2' },
        { value: '选项3', label: '选项3' },
      ],
    },
    propslist: [
      'dataType',
      'required',
      'allowClear',
      'prefix',
      'suffix',
      'variant',
      'placeholder',
      'disabled',
      'showSearch',
      'mode',
      'queryType',
      'dataClass',
      'defaultValue',
      'isDataTypeToPerson'
    ],
  },
  {
    group: '基本',
    formId: 'shuzhishurukuang',
    icon: 'iconshuzhishurukuang',
    formName: '数值',
    isForm: true,
    dataType: 'InputNumber',
    wigetProps: {
      controls: true,
    },

    propslist: [
      'dataType',
      'required',
      'allowClear',
      'placeholder',
      'maxLength',
      'min',
      'max',
      'prefix',
      'suffix',
      'variant',
      'disabled',
      'controls',
    ],
  },
  {
    group: '基本',
    formId: 'danxuankuang',
    icon: 'icondanxuan',
    formName: '单选',
    isForm: true,
    dataType: 'Radio',
    wigetProps: {
      options: [
        { value: '选项1', label: '选项1' },
        { value: '选项2', label: '选项2' },
        { value: '选项3', label: '选项3' },
      ],
    },
    propslist: [
      'dataType',
      'required',
      'optionType',
      'styleFlex',
      'disabled',
      'queryType',
      'dataClass',
      'defaultValue',
      'isDataTypeToPerson'
    ],
  },
  {
    group: '基本',
    formId: 'fuxuankuang',
    icon: 'iconfuxuan',
    formName: '复选',
    isForm: true,
    dataType: 'Checkbox',
    wigetProps: {
      options: [
        { value: '选项1', label: '选项1' },
        { value: '选项2', label: '选项2' },
        { value: '选项3', label: '选项3' },
      ],
    },
    propslist: ['dataType', 'required', 'styleFlex', 'disabled', 'queryType', 'dataClass', 'defaultValue','isDataTypeToPerson'],
  },
  {
    group: '基本',
    formId: 'pingfen',
    icon: 'iconpingfen',
    formName: '评分',
    isForm: true,
    dataType: 'Rate',
    wigetProps: {
      tooltips: ['差', '较差', '一般', '好', '优秀'],
    },
    propslist: ['dataType', 'required', 'allowClear', 'allowHalf', 'disabled'],
  },
  {
    group: '基本',
    formId: 'kaiguan',
    icon: 'iconkaiguan',
    formName: '开关',
    isForm: true,
    dataType: 'Switch',
    wigetProps: {},
    propslist: ['dataType', 'required', 'checkedChildren', 'unCheckedChildren'],
  },
  {
    group: '基本',
    formId: 'huadongshuru',
    icon: 'iconhuadongshuru',
    formName: '滑动输入条',
    dataType: 'Slider',
    isForm: true,
    wigetProps: {},
    propslist: ['dataType', 'required', 'disabled', 'max', 'min'],
  },
  {
    group: '基本',
    formId: 'yansexuanze',
    icon: 'iconyanse',
    formName: '颜色选择器',
    dataType: 'ColorPicker',
    isForm: true,
    wigetProps: {
      showText: true,
      allowClear: true,
    },
    propslist: ['dataType', 'required', 'disabled', 'showText'],
  },

  {
    group: '基本',
    formId: 'shijian',
    icon: 'iconshijian1',
    formName: '时间',
    dataType: 'TimePicker',
    isForm: true,
    wigetProps: {},
    propslist: ['dataType', 'required', 'disabled', 'allowClear'],
  },
  {
    group: '基本',
    formId: 'shijianfanwei',
    icon: 'iconshijianqujian',
    formName: '时间区间',
    dataType: 'TimeRangePicker',
    isForm: true,
    wigetProps: {},
    propslist: ['dataType', 'required', 'disabled', 'allowClear'],
  },
  {
    group: '基本',
    formId: 'riqi',
    icon: 'iconriqi',
    formName: '日期',
    dataType: 'DatePicker',
    isForm: true,
    wigetProps: {},
    propslist: ['dataType', 'required', 'disabled', 'picker', 'allowClear'],
  },
  {
    group: '基本',
    formId: 'riqifanwei',
    icon: 'iconriqifanwei',
    formName: '日期区间',
    dataType: 'RangePicker',
    isForm: true,
    wigetProps: {},
    propslist: ['dataType', 'required', 'disabled', 'picker', 'allowClear'],
  },
  {
    group: '基本',
    formId: 'tupianfujian',
    icon: 'icontouxiang',
    formName: '图片',
    formNameHide: true,
    dataType: 'ImgUpload',
    isForm: true,
    wigetProps: { width: 200, height: 250, accept: '.png,.jpg,.jpeg' },
    propslist: ['dataType', 'required', 'width', 'height'],
  },
  {
    group: '基本',
    formId: 'fujian',
    icon: 'iconfujian',
    formName: '附件',
    isForm: true,
    dataType: 'Upload',
    wigetProps: {
      listType: 'picture-card',
      multiple: true,
      accept: '.png,.jpg,.jpeg,.pdf,.xls,.xlsx,.doc,.docx',
      showUploadList: true,
    },
    propslist: ['dataType', 'required', 'multiple', 'listType', 'accept'],
  },
]";
            List<PropslistJson> listPropslistJson = JsonHelper.FromJson<List<PropslistJson>>(strJson);
            foreach (var item in formJsonDto.rows)
            {
                foreach (var col in item.cols)
                {
                    foreach (var itm in col.items)
                    {
                        if (itm.formCode.IsNullOrEmpty() || itm.formName.IsNullOrEmpty())
                            continue;
                        FieldDictDto dto = new FieldDictDto();
                        dto.FIELD_CLASS = "基本信息";
                        dto.FIELD_NAME = itm.formName;
                        dto.FIELD_CODE = itm.formCode;
                        dto.HOSPITAL_ID = "33A001";
                        dto.MODULE_ID = "H81";
                        dto.FIELD_STATE = "1";
                        dto.FIELD_DESC = "";
                        dto.FIELD_SORT = "";
                        
                        StyleJson json = new StyleJson();
                        json.dataClass = itm.dataClass;
                        json.dataType = itm.dataType;
                        json.labelHide = itm.labelHide;
                        json.rules = itm.rules;
                        json.wigetProps = itm.wigetProps;
                        
                        PropslistJson propslistJson = listPropslistJson.Find(W => W.dataType == json.dataType);
                        dto.READONLY_JSON = propslistJson?.propslist?.ToString().Replace("{", "").Replace("}", "");
                        dto.STYLE_JSON = JsonHelper.ToJson(json);
                        SaveFieldDict(dto);
                    }
                }
            }
            return result;
        }
        #endregion
    }
}
