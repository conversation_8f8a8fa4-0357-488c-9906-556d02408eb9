﻿using AutoMapper;
using XH.H81.Models.Dtos;
using XH.H81.Models.Dtos.Tag;
using XH.H81.Models.Entities;
using XH.H81.Models.Entities.Tag;
using XH.H81.Models.ExternalEntity;

namespace XH.H81.Models
{
    public class MappingProfile : Profile
    {
        public MappingProfile()
        {
            //模型映射演示
            CreateMap<StartTemplateDto, TEST_START_TEMPLATE>()
                .ForMember(d => d.ID,
                    opt => opt.MapFrom(s => s.Id))
                //允许反向
                .ReverseMap();
            CreateMap<PmsPersonTagDictDto, PMS_PERSON_TAG_DICT>().ReverseMap();
        }
    }
}