﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using SqlSugar.DbConvert;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Exam
{
    /// <summary>
    /// 考试答题记录表
    /// </summary>
    [DBOwner("XH_OA")]
    public class EMP_EXAM_USER_ANSWER
    {

        public EMP_EXAM_USER_ANSWER()
        {
            EXAM_FLAG = 0;
            RECENT_FLAG = 0;
        }
        /// <summary>
        /// 人员考试ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Key]
        public string? EXAM_USERID { get; set; }
        /// <summary>
        /// 考试ID
        /// </summary>
        public string? EXAM_ID { get; set; }
        /// <summary>
        /// 试卷ID
        /// </summary>
        public string? PAPER_ID { get; set; }
        /// <summary>
        /// 人员ID
        /// </summary>
        public string? USER_ID { get; set; }
        /// <summary>
        /// 考试次数  
        /// </summary>
        public string? EXAM_NUM { get; set; }
        /// <summary>
        /// 考试方式(线上、线下)
        /// </summary>
        public string? ANSWER_TYPE { get; set; }
        /// <summary>
        /// 人员分类(指定、报名)
        /// </summary>
        public string? USER_CLASS { get; set; }
        /// <summary>
        /// 报名时间
        /// </summary>
        public DateTime? SIGNUP_TIME { get; set; }
        /// <summary>
        /// 报名原因
        /// </summary>
        public string? SIGNUP_CAUSE { get; set; }
        /// <summary>
        /// 确认人员
        /// </summary>
        public string? CONFIRM_PERSON { get; set; }
        /// <summary>
        /// 确认时间
        /// </summary>
        public DateTime? CONFIRM_TIME { get; set; }
        /// <summary>
        /// 考试终端
        /// </summary>
        public string? EXAM_CLIENT { get; set; }
        /// <summary>
        /// 考试终端类型
        /// </summary>
        public string? EXAM_CLIENT_TYPE { get; set; }
        /// <summary>
        /// 考试开始时间 (考生开考时间,非考试设置的开考时间！！)
        /// </summary>
        public DateTime? EXAM_START_TIME { get; set; }
        /// <summary>
        /// 交卷时间
        /// </summary>
        public DateTime? EXAM_END_TIME { get; set; }
        /// <summary>
        /// 考试用时
        /// </summary>
        public decimal? EXAM_DURATION { get; set; }
        /// <summary>
        /// 考试成绩
        /// </summary>
        public decimal? EXAM_SCORE { get; set; }
        /// <summary>
        /// 考试排名
        /// </summary>
        public int? EXAM_RANKING { get; set; }
        /// <summary>
        /// 综合评价
        /// </summary>
        public string? COMPREHENSIVE_ASSESS { get; set; }
        /// <summary>
        /// 部分分数
        /// </summary>
        public string? PART_SCORE { get; set; }
        /// <summary>
        /// 考试积分
        /// </summary>
        public int? EXAM_POINT { get; set; }
        /// <summary>
        /// 评卷人员
        /// </summary>
        public string? MARK_EP_PERSON { get; set; }

        /// <summary>
        /// 自动评卷
        /// </summary>
        public string? AUTO_MARK{ get; set; }
        /// <summary>
        /// 评卷时间
        /// </summary>
        public DateTime? MARK_EP_TIME { get; set; }
        /// <summary>
        /// 答卷明细(试题+答案)  请注意 加人的时候这个字段为空的  只有等到进入考试才会赋值  因此随机组题啥的  都是进入考试的接口GetExamInfoByExamId才开始处理
        /// </summary>
        [SugarColumn(SqlParameterDbType =typeof(NClobPropertyConvert) )]
        public string? ANSWER_INFO { get; set; }
        /// <summary>
        /// 审核人员
        /// </summary>
        public string? CHECK_PERSON { get; set; }
        /// <summary>
        /// 审核时间
        /// </summary>
        public DateTime? CHECK_TIME { get; set; }
        /// <summary>
        /// 审核意见
        /// </summary>
        public string? CHECK_OPINION { get; set; }
        /// <summary>
        /// 考卷文件
        /// </summary>
        public string? ANSWER_FILE { get; set; }
        /// <summary>
        /// 状态[0已报名1已报名确认2未交卷3已交卷4已评卷5已提交6已审核未通过7已审核 10撤销]
        /// </summary>
        public string? EXAM_USER_STATE { get; set; }
        /// <summary>
        /// 首次登记人
        /// </summary>
        public string? FIRST_RPERSON { get; set; }
        /// <summary>
        /// 首次登记时间
        /// </summary>
        public DateTime? FIRST_RTIME { get; set; }
        /// <summary>
        /// 最后修改人员
        /// </summary>
        public string? LAST_MPERSON { get; set; }
        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime? LAST_MTIME { get; set; }
        /// <summary>
        /// 备注(用于缺考说明)
        /// </summary>
        public string? REMARK { get; set; }


        /// <summary>
        /// 考试设置的开考开始时间
        /// </summary>
        public DateTime? EXAM_START_DATE { get; set; }

        /// <summary>
        /// 考试设置的开考结束时间
        /// </summary>
        public DateTime? EXAM_END_DATE { get; set; }

        /// <summary>
        /// 考试标识:0:正常 1补考
        /// </summary>
        public int? EXAM_FLAG { get; set; }

        /// <summary>
        /// 最新标识:0:最新有效 1历史
        /// </summary>
        public int? RECENT_FLAG { get; set; }

        /// <summary>
        /// 科室id
        /// </summary>

        public string? LAB_ID { get; set; }


        /// <summary>
        /// 专业组ID
        /// </summary>

        public string? PGROUP_ID { get; set; }

        /// <summary>
        /// 数据来源类型
        /// </summary>

        public string? SOURCE_TYPE { get; set; }

        /// <summary>
        /// 规评组合ID
        /// </summary>

        public string? STD_GROUP_ID { get; set; }

        /// <summary>
        /// 规评方案ID
        /// </summary>

        public string? STD_SCHEME_ID { get; set; }

        /// <summary>
        /// 考试名称
        /// </summary>

        public string? EXAM_NAME { get; set; }


        /// <summary>
        /// 培训ID
        /// </summary>

        public string? TRAIN_ID { get; set; }

        /// <summary>
        /// 资质证书iD
        /// </summary>

        public string? CER_ID { get; set; }


        /// <summary>
        /// 附件
        /// </summary>

        public string? EVALUATE_AFFIX { get; set; }

        /// <summary>
        /// 生安标志
        /// </summary>

        public string? SMBL_FLAG { get; set; }
    }
}
