﻿using H.Utility.SqlSugarInfra;
using SqlSugar;

namespace XH.H81.Models.Entities.Eguard;

    /// <summary>
    /// 用户门禁授权记录表
    /// </summary>
    [DBOwner("XH_OA")]
    [SugarTable("PMS_EGUARD_AUTH_USER", TableDescription = "用户门禁授权记录表")]
    public class PMS_EGUARD_AUTH_USER
    {
        /// <summary>
        /// 用户授权ID
        /// </summary>
        [SugarColumn(IsPrimaryKey =true, ColumnName = "AUTH_USER_ID")]
        public string AUTH_USER_ID{ get; set; }
        
        /// <summary>
        /// 机构ID
        /// </summary>
        [SugarColumn(ColumnName = "HOSPITAL_ID")]
        public string HOSPITAL_ID{ get; set; }

        /// <summary>
        /// 授权类型;（1. 访客 2.科内人员）
        /// </summary>
        [SugarColumn(ColumnName = "AUTH_TYPE")]
        public string AUTH_TYPE{ get; set; }
        
        /// <summary>
        /// 用户ID
        /// </summary>
        [SugarColumn(ColumnName = "USER_ID")]
        public string USER_ID{ get; set; }
        
        /// <summary>
        /// 设备SN;设备档案信息表
        /// </summary>
        [SugarColumn(ColumnName = "DEVICE_SN")]
        public string? DEVICE_SN{ get; set; }
        
        /// <summary>
        /// 授权开始时间
        /// </summary>
        [SugarColumn(ColumnName = "AUTH_START_DATE")]
        public DateTime? AUTH_START_DATE{ get; set; }
        
        /// <summary>
        /// 授权结束时间
        /// </summary>
        [SugarColumn(ColumnName = "AUTH_END_DATE")]
        public DateTime? AUTH_END_DATE{ get; set; }
        
        /// <summary>
        /// 授权时间JSON
        /// </summary>
        [SugarColumn(ColumnName = "PLAN_JSON")]
        public string? PLAN_JSON{ get; set; }
        
        /// <summary>
        /// 授权状态;(0未执行、1已执行， 2 设置更新,10 授权失败)
        /// </summary>
        [SugarColumn(ColumnName = "AUTH_USER_STATE")]
        public string? AUTH_USER_STATE{ get; set; }
        
        /// <summary>
        /// 失败原因
        /// </summary>
        [SugarColumn(ColumnName = "FAIL_REASON")]
        public string? FAIL_REASON{ get; set; }
        
        /// <summary>
        /// 创建人
        /// </summary>
        [SugarColumn(ColumnName = "FIRST_RPERSON")]
        public string? FIRST_RPERSON{ get; set; }
        
        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(ColumnName = "FIRST_RTIME")]
        public DateTime? FIRST_RTIME{ get; set; }
        
        /// <summary>
        /// 更新人
        /// </summary>
        [SugarColumn(ColumnName = "LAST_MPERSON")]
        public string? LAST_MPERSON{ get; set; }
        
        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(ColumnName = "LAST_MTIME")]
        public DateTime? LAST_MTIME{ get; set; }
        
        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(ColumnName = "REMARK")]
        public string? REMARK{ get; set; }
        
        
        /// <summary>
        /// 起效时间
        /// </summary>
        [SugarColumn(ColumnName = "ACTIVE_DATE")]
        public DateTime? ACTIVE_DATE{ get; set; } 
        
    }