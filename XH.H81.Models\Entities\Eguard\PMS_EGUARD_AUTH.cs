﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Entities.Eguard
{
    /// <summary>
    /// 门禁授权字典表
    /// </summary>
    [DBOwner("XH_OA")]
    public class PMS_EGUARD_AUTH
    {
        /// <summary>
        /// 门禁授权ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string EGUARD_AUTH_ID { get; set; }
        /// <summary>
        /// 医疗机构ID
        /// </summary>
        public string HOSPITAL_ID { get; set; }
        /// <summary>
        /// 门禁类型 1:按门禁组合 2:按门禁
        /// </summary>
        public string? EGUARD_TYPE { get; set; }
        /// <summary>
        /// 授权期限类型 1:长期 2:固定日期
        /// </summary>
        public string? AUTH_LIMIT_TYPE { get; set; }
        /// <summary>
        /// 授权开始时间
        /// </summary>
        public DateTime? AUTH_START_DATE { get; set; }
        /// <summary>
        /// 授权结束时间
        /// </summary>
        public DateTime? AUTH_END_DATE { get; set; }
        /// <summary>
        /// 门禁通过ID
        /// </summary>
        public string? EGUARD_PASS_ID { get; set; }
        /// <summary>
        /// 无计划JSON
        /// </summary>
        public string? EGUARD_PASS_JSON { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public string? EGUARD_AUTH_STATE { get; set; }
        public string? FIRST_RPERSON { get; set; }
        public DateTime? FIRST_RTIME { get; set; }
        public string? LAST_MPERSON { get; set; }
        public DateTime? LAST_MTIME { get; set; }
        public string? REMARK { get; set; }

        /// <summary>
        /// 发布状态   0未发布  1已发布
        /// </summary>
        public string? PUBLISH_STATE { get; set; } = "0";
        /// <summary>
        /// 授权范围
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string EPLAN_APPLY_TYPE { get; set; }
        /// <summary>
        /// 授权数据ID
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string EGUARD_DATA_ID { get; set; }
        /// <summary>
        /// 适用类型
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string  EGUARD_AUTH_TYPE { get; set; }
    }
}
