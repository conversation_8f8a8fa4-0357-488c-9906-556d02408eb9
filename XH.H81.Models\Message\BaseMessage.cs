﻿using Newtonsoft.Json;

namespace XH.H81.Models.Message
{
    public class BaseMessage
    {
        /// <summary>
        /// 院区id
        /// </summary>
        public string AREA_ID { get; set; }
        /// <summary>
        /// 延迟时间
        /// </summary>
        public long DELAY_TIME { get; set; }
        /// <summary>
        /// 机构id
        /// </summary>
        public string HOSPITAL_ID { get; set; }
        /// <summary>
        /// 科室id
        /// </summary>
        public string LAB_ID { get; set; }

        /// <summary>
        /// 消息日期 【聚集索引yyyy-mm-dd】
        /// </summary>
        public string MAG_DATE { get; set; }
        /// <summary>
        /// 系统模块id
        /// </summary>
        public string MODULE_ID { get; set; }

        /// <summary>
        /// 消息分类
        /// </summary>
        public string MSG_CLASS { get; set; }
        /// <summary>
        /// 消息内容
        /// </summary>
        public string MSG_CONTENT { get; set; }

        /// <summary>
        /// 消息对应id 【第三方消息来源对应id】
        /// </summary>
        public string MSG_CORRID { get; set; }

        /// <summary>
        /// 消息处理类型
        /// </summary>
        public string MSG_DISPOSE_TYPE { get; set; }

        /// <summary>
        /// 消息处理url  字段存储子模块号
        /// </summary>
        public string MSG_DISPOSE_URL { get; set; }

        /// <summary>
        /// 消息处理URL样式
        /// </summary>
        public string MSG_DISPOSE_URL_STYLE { get; set; }

        /// <summary>
        /// 消息级别 【固定基础数据】
        /// </summary>
        public string MSG_LEVEL { get; set; }

        /// <summary>
        /// 消息生成操作
        /// </summary>
        public string MSG_OPERATE { get; set; }

        /// <summary>
        /// 消息超时时限 【分钟 0表示不需要超时提示】
        /// </summary>
        public long MSG_OVERTIME { get; set; }

        /// <summary>
        /// 消息主题
        /// </summary>
        public string MSG_TITLE { get; set; }

        /// <summary>
        /// 消息种类
        /// </summary>
        public string MSG_TYPE { get; set; }

        /// <summary>
        /// 消息有效时限
        /// </summary>
        public string MSG_VALID_TIME { get; set; }

        /// <summary>
        /// 个人就是接收人userNo，科室就是labid，专业组就是pdid
        /// </summary>
        public string RECEIVE_UNIT_ID { get; set; }

        /// <summary>
        /// 个人姓名或者组织名称
        /// </summary>
        public string RECEIVE_UNIT_NAME { get; set; }

        /// <summary>
        /// 个人消息传”个人消息“，
        /// </summary>
        public string RECEIVE_UNIT_TYPE { get; set; }

        /// <summary>
        /// 发送设备MAC地址
        /// </summary>
        public string SEND_COMPUTER { get; set; }

        /// <summary>
        /// 发送人
        /// </summary>
        public string SEND_PERSON { get; set; }

        /// <summary>
        /// 发送时间
        /// </summary>
        public string SEND_TIME { get; set; }

        /// <summary>
        /// 个人发就用userNo  组织发就用组织的id
        /// </summary>
        public string SEND_UNIT_ID { get; set; }


    }


}
