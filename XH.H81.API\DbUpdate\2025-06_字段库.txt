﻿/* #6.25.225 字段库初始化 */
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000171', '33A001', 'H81', '基本信息', 'USER_NAME', '姓名', NULL, NULL, '["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]', '{
  "dataType": "Input",
  "rules": "[\n  {\n    \"required\": true\n  }\n]",
  "wigetProps": "{\n  \"placeholder\": \"请输入\",\n  \"allowClear\": true,\n  \"maxLength\": 200,\n  \"disabled\": false\n}"
}', '{"group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"单行文本","dataType":"Input","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]}', '1', 'fr_李影', TIMESTAMP '2025-06-10 15:24:15.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000172', '33A001', 'H81', '基本信息', 'HIS_ID', '工号', NULL, NULL, '[
  "dataType",
  "required",
  "allowClear",
  "placeholder",
  "maxLength",
  "prefix",
  "suffix",
  "variant",
  "disabled",
  "showCount"
]', '{
  "dataClass": "",
  "dataType": "Input",
  "labelHide": "False",
  "labelStyle": "",
  "rules": "[\n  {\n    \"required\": null\n  }\n]",
  "wigetProps": "{}"
}', NULL, '1', 'fr_李影', TIMESTAMP '2025-05-22 14:47:13.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000175', '33A001', 'H81', '基本信息', 'BIRTHDAY', '出生年月', NULL, NULL, '[
  "dataType",
  "required",
  "disabled",
  "picker",
  "allowClear"
]', '{
  "dataClass": "",
  "dataType": "DatePicker",
  "labelHide": "False",
  "labelStyle": "",
  "rules": "[\n  {\n    \"required\": null\n  }\n]",
  "wigetProps": "{\n  \"defaultValue\": null\n}"
}', NULL, '1', 'fr_李影', TIMESTAMP '2025-05-22 14:47:25.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000176', '33A001', 'H81', '基本信息', 'LOGID', '登录ID', NULL, NULL, '["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]', '{
  "dataType": "Input",
  "rules": "[\n  {\n    \"required\": true\n  }\n]",
  "wigetProps": "{\n  \"placeholder\": \"请输入\",\n  \"allowClear\": true,\n  \"maxLength\": 200,\n  \"disabled\": false\n}"
}', '{"group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"单行文本","dataType":"Input","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]}', '1', 'fr_李影', TIMESTAMP '2025-06-09 10:03:52.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', '登录');
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000178', '33A001', 'H81', '基本信息', 'ID_CARD', '证件号码', NULL, NULL, '[
  "dataType",
  "required",
  "allowClear",
  "placeholder",
  "maxLength",
  "prefix",
  "suffix",
  "variant",
  "disabled",
  "showCount"
]', '{
  "dataClass": null,
  "dataType": "Input",
  "labelHide": false,
  "labelStyle": null,
  "rules": [
    {
      "required": true
    }
  ],
  "wigetProps": {
    "button": false,
    "groupKey": null
  }
}', NULL, '1', 'fr_李影', TIMESTAMP '2025-05-22 14:47:26.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000207', '33A001', 'H81', '基本信息', 'PERSON_DOC_STATE', '人员性质', NULL, NULL, '[
  "dataType",
  "required",
  "allowClear",
  "placeholder",
  "maxLength",
  "prefix",
  "suffix",
  "variant",
  "disabled",
  "showCount"
]', '{
  "dataClass": null,
  "dataType": "Input",
  "labelHide": "False",
  "labelStyle": null,
  "rules": "[\n  {\n    \"required\": null\n  }\n]",
  "wigetProps": "{\n  \"disabled\": false,\n  \"style\": {\n    \"color\": null,\n    \"fontSize\": 13,\n    \"fontWeight\": \"normal\",\n    \"fontStyle\": \"normal\",\n    \"background\": null,\n    \"textAlign\": \"left\",\n    \"justifyContent\": \"left\"\n  }\n}"
}', NULL, '1', 'fr_李影', TIMESTAMP '2025-05-22 14:47:34.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000180', '33A001', 'H81', '基本信息', 'SEX', '性别/年龄', NULL, NULL, '["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"]', '{
  "dataClass": "性别",
  "queryType": "api",
  "dataType": "Select",
  "rules": "[\n  {\n    \"required\": true\n  }\n]",
  "wigetProps": "{}",
  "labelHide": "False"
}', '{"group":"基本","formId":"xialakuang","icon":"iconxialakuang","formName":"下拉框","isForm":true,"dataType":"Select","wigetProps":{"options":[],"defaultValue":"2","allowClear":false},"propslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"dataClass":"性别","queryType":"api","rules":[{"required":false}]}', '1', 'fr_李影', TIMESTAMP '2025-06-06 21:19:06.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000195', '33A001', 'H81', '基本信息', 'HEIGHT', '身高', NULL, NULL, '[
  "dataType",
  "required",
  "allowClear",
  "placeholder",
  "maxLength",
  "prefix",
  "suffix",
  "variant",
  "disabled",
  "showCount"
]', '{
  "dataClass": "",
  "dataType": "Input",
  "labelHide": "False",
  "labelStyle": "",
  "rules": "[\n  {\n    \"required\": null\n  }\n]",
  "wigetProps": "{}"
}', NULL, '1', 'fr_李影', TIMESTAMP '2025-05-22 14:47:31.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000203', '33A001', 'H81', '基本信息', 'EMERGENCY_CONTACT', '紧急联系人', NULL, NULL, '[
  "dataType",
  "required",
  "allowClear",
  "placeholder",
  "maxLength",
  "prefix",
  "suffix",
  "variant",
  "disabled",
  "showCount"
]', '{
  "dataClass": "",
  "dataType": "Input",
  "labelHide": "False",
  "labelStyle": "",
  "rules": "[\n  {\n    \"required\": null\n  }\n]",
  "wigetProps": "{}"
}', NULL, '1', 'fr_李影', TIMESTAMP '2025-05-22 14:47:33.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000204', '33A001', 'H81', '基本信息', 'ECONTACT_RELACTION', '与紧急联系人的关系', NULL, NULL, '["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]', '{"dataType":"Input","rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false}}', '{"group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"单行文本","dataType":"Input","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]}', '1', 'fr_李影', TIMESTAMP '2025-06-11 10:46:13.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:46:17.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000205', '33A001', 'H81', '基本信息', 'ECONTACT_PHONE', '紧急联系人电话', NULL, NULL, '[
  "dataType",
  "required",
  "allowClear",
  "placeholder",
  "maxLength",
  "prefix",
  "suffix",
  "variant",
  "disabled",
  "showCount"
]', '{
  "dataClass": "",
  "dataType": "Input",
  "labelHide": "False",
  "labelStyle": "",
  "rules": "[\n  {\n    \"required\": null\n  }\n]",
  "wigetProps": "{}"
}', NULL, '1', 'fr_李影', TIMESTAMP '2025-05-22 14:47:33.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000206', '33A001', 'H81', '基本信息', 'COMM_ADDR', '详细通讯地址', NULL, NULL, '[
  "dataType",
  "required",
  "allowClear",
  "placeholder",
  "maxLength",
  "prefix",
  "suffix",
  "variant",
  "disabled",
  "showCount"
]', '{
  "dataClass": null,
  "dataType": "Input",
  "labelHide": false,
  "labelStyle": null,
  "rules": [
    {
      "required": true
    }
  ],
  "wigetProps": {
    "button": false,
    "groupKey": null
  }
}', NULL, '1', 'fr_李影', TIMESTAMP '2025-05-22 14:47:33.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000209', '33A001', 'H81', '基本信息', 'EMPLOYMENT_SOURE', '入职方式', NULL, NULL, '["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"]', '{
  "dataClass": "入职方式",
  "queryType": "api",
  "dataType": "Select",
  "wigetProps": {
    "options": [],
    "defaultValue": "3"
  }
}', '{"group":"基本","formId":"xialakuang","icon":"iconxialakuang","formName":"下拉框","isForm":true,"dataType":"Select","wigetProps":{"options":[],"defaultValue":"3"},"propslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"dataClass":"入职方式","queryType":"api"}', '1', 'fr_李影', TIMESTAMP '2025-05-30 10:48:07.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000210', '33A001', 'H81', '基本信息', 'USER_TYPE', '用工类型', NULL, NULL, '["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"]', '{
  "dataClass": "用工类型",
  "queryType": "api",
  "dataType": "Select",
  "wigetProps": {
    "options": [],
    "defaultValue": ""
  }
}', '{"group":"基本","formId":"xialakuang","icon":"iconxialakuang","formName":"下拉框","isForm":true,"dataType":"Select","wigetProps":{"options":[],"defaultValue":""},"propslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"dataClass":"用工类型","queryType":"api"}', '1', 'fr_李影', TIMESTAMP '2025-06-10 15:24:56.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000211', '33A001', 'H81', '基本信息', 'TECHNOLOGY_TYPE', '职称类型', NULL, NULL, '["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"]', '{
  "dataClass": "职称类型",
  "queryType": "api",
  "dataType": "Select",
  "wigetProps": "{}",
  "labelHide": "False",
  "rules": "[\n  {\n    \"required\": null\n  }\n]"
}', '{"group":"基本","formId":"xialakuang","icon":"iconxialakuang","formName":"下拉框","isForm":true,"dataType":"Select","wigetProps":{"options":[],"defaultValue":"1"},"propslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"dataClass":"职称类型","queryType":"api"}', '1', 'fr_李影', TIMESTAMP '2025-05-29 21:19:56.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000212', '33A001', 'H81', '基本信息', 'TECH_POST_PROFESSION', '职称专业', NULL, NULL, '[
  "dataType",
  "required",
  "allowClear",
  "placeholder",
  "maxLength",
  "prefix",
  "suffix",
  "variant",
  "disabled",
  "showCount"
]', '{
  "dataClass": "",
  "dataType": "Input",
  "labelHide": "False",
  "labelStyle": "",
  "rules": "[\n  {\n    \"required\": null\n  }\n]",
  "wigetProps": "{}"
}', NULL, '1', 'fr_李影', TIMESTAMP '2025-05-22 14:47:35.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000214', '33A001', 'H81', '基本信息', 'EMPLOYMENT_UNIT', '聘任职称评定单位', NULL, NULL, '[
  "dataType",
  "required",
  "allowClear",
  "placeholder",
  "maxLength",
  "prefix",
  "suffix",
  "variant",
  "disabled",
  "showCount"
]', '{
  "dataClass": "",
  "dataType": "Input",
  "labelHide": "False",
  "labelStyle": "",
  "rules": "[\n  {\n    \"required\": null\n  }\n]",
  "wigetProps": "{}"
}', NULL, '1', 'fr_李影', TIMESTAMP '2025-05-22 14:47:35.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000216', '33A001', 'H81', '基本信息', 'TECH_CERTIFICE_TIME', '职称评定日期', NULL, NULL, '[
  "dataType",
  "required",
  "disabled",
  "picker",
  "allowClear"
]', '{
  "dataClass": null,
  "dataType": "DatePicker",
  "labelHide": false,
  "labelStyle": null,
  "rules": null,
  "wigetProps": {
    "button": false,
    "groupKey": null
  }
}', NULL, '1', 'fr_李影', TIMESTAMP '2025-05-22 14:47:35.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000218', '33A001', 'H81', '基本信息', 'RETIRE_TIME', '退休日期', NULL, NULL, '[
  "dataType",
  "required",
  "disabled",
  "picker",
  "allowClear"
]', '{
  "dataClass": null,
  "dataType": "DatePicker",
  "labelHide": false,
  "labelStyle": null,
  "rules": [
    {
      "required": true
    }
  ],
  "wigetProps": {
    "button": false,
    "groupKey": null
  }
}', NULL, '1', 'fr_李影', TIMESTAMP '2025-05-22 14:47:36.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000219', '33A001', 'H81', '基本信息', 'LENGTH_SERVICE', '工龄', NULL, NULL, '["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]', '{
  "dataType": "Input",
  "rules": [
    {
      "required": true
    }
  ],
  "wigetProps": {
    "placeholder": "请输入",
    "allowClear": true,
    "maxLength": 200,
    "disabled": false
  }
}', '{"group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"单行文本","dataType":"Input","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]}', '1', 'fr_李影', TIMESTAMP '2025-06-10 15:24:15.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000181', '33A001', 'H81', '基本信息', 'AGE', '年龄', NULL, NULL, '[
  "dataType",
  "required",
  "allowClear",
  "placeholder",
  "maxLength",
  "prefix",
  "suffix",
  "variant",
  "disabled",
  "showCount"
]', '{
  "dataClass": "",
  "dataType": "Input",
  "labelHide": "False",
  "labelStyle": "",
  "rules": "[\n  {\n    \"required\": null\n  }\n]",
  "wigetProps": "{}"
}', NULL, '1', 'fr_李影', TIMESTAMP '2025-05-22 14:47:27.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000182', '33A001', 'H81', '基本信息', 'NATIVE_PLACE', '籍贯', NULL, NULL, '["dataType","required","allowClear","prefix","variant","placeholder","disabled","showSearch"]', '{
  "dataType": "Cascader",
  "wigetProps": {}
}', '{"group":"基本","formId":"jilianxuanze","icon":"iconxialakuang","formName":"级联选择","isForm":true,"dataType":"Cascader","wigetProps":{},"propslist":["dataType","required","allowClear","prefix","variant","placeholder","disabled","showSearch"]}', '1', 'fr_李影', TIMESTAMP '2025-06-06 11:24:20.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000184', '33A001', 'H81', '基本信息', 'CURRENT_ADDRESS', '现居住地', NULL, NULL, '[
  "dataType",
  "required",
  "allowClear",
  "placeholder",
  "maxLength",
  "prefix",
  "suffix",
  "variant",
  "disabled",
  "showCount"
]', '{
  "dataClass": "",
  "dataType": "Input",
  "labelHide": "False",
  "labelStyle": "",
  "rules": "[\n  {\n    \"required\": null\n  }\n]",
  "wigetProps": "{}"
}', NULL, '1', 'fr_李影', TIMESTAMP '2025-05-22 14:47:28.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000185', '33A001', 'H81', '基本信息', 'HIGHEST_DEGREE', '最高学历', NULL, NULL, '["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"]', '{
  "dataClass": "最高学历",
  "queryType": "api",
  "dataType": "Select",
  "wigetProps": "{}",
  "labelHide": "False",
  "rules": "[\n  {\n    \"required\": null\n  }\n]"
}', '{"group":"基本","formId":"xialakuang","icon":"iconxialakuang","formName":"下拉框","isForm":true,"dataType":"Select","wigetProps":{"options":[],"defaultValue":"3"},"propslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"dataClass":"最高学历","queryType":"api","isDataTypeToPerson":true}', '1', 'fr_李影', TIMESTAMP '2025-06-05 09:46:31.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000187', '33A001', 'H81', '基本信息', 'GRADUATE_SCHOOL', '毕业院校', NULL, NULL, '[
  "dataType",
  "required",
  "allowClear",
  "placeholder",
  "maxLength",
  "prefix",
  "suffix",
  "variant",
  "disabled",
  "showCount"
]', '{
  "dataClass": "",
  "dataType": "Input",
  "labelHide": "False",
  "labelStyle": "",
  "rules": "[\n  {\n    \"required\": null\n  }\n]",
  "wigetProps": "{}"
}', NULL, '1', 'fr_李影', TIMESTAMP '2025-05-22 14:47:28.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000188', '33A001', 'H81', '基本信息', 'GRADUATE_DATE', '毕业日期', NULL, NULL, '[
  "dataType",
  "required",
  "disabled",
  "picker",
  "allowClear"
]', '{
  "dataClass": "",
  "dataType": "DatePicker",
  "labelHide": "False",
  "labelStyle": "",
  "rules": "[\n  {\n    \"required\": null\n  }\n]",
  "wigetProps": "{}"
}', NULL, '1', 'fr_李影', TIMESTAMP '2025-05-22 14:47:29.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000189', '33A001', 'H81', '基本信息', 'PROFESSION', '毕业专业', NULL, NULL, '[
  "dataType",
  "required",
  "allowClear",
  "placeholder",
  "maxLength",
  "prefix",
  "suffix",
  "variant",
  "disabled",
  "showCount"
]', '{
  "dataClass": "",
  "dataType": "Input",
  "labelHide": "False",
  "labelStyle": "",
  "rules": "[\n  {\n    \"required\": null\n  }\n]",
  "wigetProps": "{}"
}', NULL, '1', 'fr_李影', TIMESTAMP '2025-05-22 14:47:29.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000192', '33A001', 'H81', '基本信息', 'MARITAL_STATUS', '婚姻状况', NULL, NULL, '["dataType","required","optionType","styleFlex","disabled","queryType","dataClass","defaultValue","isDataTypeToPerson"]', '{
  "dataClass": "婚姻状况",
  "queryType": "api",
  "dataType": "Radio",
  "wigetProps": {
    "options": [],
    "defaultValue": ""
  }
}', '{"group":"基本","formId":"danxuankuang","icon":"icondanxuan","formName":"单选","isForm":true,"dataType":"Radio","wigetProps":{"options":[],"defaultValue":""},"propslist":["dataType","required","optionType","styleFlex","disabled","queryType","dataClass","defaultValue","isDataTypeToPerson"],"dataClass":"婚姻状况","queryType":"api"}', '1', 'fr_李影', TIMESTAMP '2025-05-29 21:51:49.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000196', '33A001', 'H81', '基本信息', 'EYESIGHT_RIGHT', '视力', NULL, NULL, '[
  "dataType",
  "required",
  "allowClear",
  "placeholder",
  "maxLength",
  "prefix",
  "suffix",
  "variant",
  "disabled",
  "showCount"
]', '{
  "dataClass": "",
  "dataType": "Input",
  "labelHide": "False",
  "labelStyle": "",
  "rules": "",
  "wigetProps": ""
}', NULL, '1', 'fr_李影', TIMESTAMP '2025-05-22 14:47:31.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000197', '33A001', 'H81', '基本信息', 'EYESIGHT_LEFT', '视力（左/右）', NULL, NULL, '[
  "dataType",
  "required",
  "allowClear",
  "placeholder",
  "maxLength",
  "prefix",
  "suffix",
  "variant",
  "disabled",
  "showCount"
]', '{
  "dataClass": "",
  "dataType": "Input",
  "labelHide": "False",
  "labelStyle": "",
  "rules": "",
  "wigetProps": ""
}', NULL, '1', 'fr_李影', TIMESTAMP '2025-05-22 14:47:31.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000199', '33A001', 'H81', '基本信息', 'PROFESSION_EXPERTISE', '专业特长', NULL, NULL, '[
  "dataType",
  "required",
  "allowClear",
  "placeholder",
  "maxLength",
  "prefix",
  "suffix",
  "variant",
  "disabled",
  "showCount"
]', '{
  "dataClass": "",
  "dataType": "Input",
  "labelHide": "False",
  "labelStyle": "",
  "rules": "[\n  {\n    \"required\": null\n  }\n]",
  "wigetProps": "{}"
}', NULL, '1', 'fr_李影', TIMESTAMP '2025-05-22 14:47:32.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000200', '33A001', 'H81', '基本信息', 'PHONE', '联系方式', NULL, NULL, '[
  "dataType",
  "required",
  "allowClear",
  "placeholder",
  "maxLength",
  "prefix",
  "suffix",
  "variant",
  "disabled",
  "showCount"
]', '{
  "dataClass": null,
  "dataType": "Input",
  "labelHide": false,
  "labelStyle": null,
  "rules": [
    {
      "required": true
    }
  ],
  "wigetProps": {
    "button": false,
    "groupKey": null
  }
}', NULL, '1', 'fr_李影', TIMESTAMP '2025-05-22 14:47:32.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000201', '33A001', 'H81', '基本信息', 'E_MAIL', '邮箱', NULL, NULL, '[
  "dataType",
  "required",
  "allowClear",
  "placeholder",
  "maxLength",
  "prefix",
  "suffix",
  "variant",
  "disabled",
  "showCount"
]', '{
  "dataClass": "",
  "dataType": "Input",
  "labelHide": "False",
  "labelStyle": "",
  "rules": "[\n  {\n    \"required\": null\n  }\n]",
  "wigetProps": "{}"
}', NULL, '1', 'fr_李影', TIMESTAMP '2025-05-22 14:47:32.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000202', '33A001', 'H81', '基本信息', 'OFFICE_PHONE', '办公电话', NULL, NULL, '[
  "dataType",
  "required",
  "allowClear",
  "placeholder",
  "maxLength",
  "prefix",
  "suffix",
  "variant",
  "disabled",
  "showCount"
]', '{
  "dataClass": "",
  "dataType": "Input",
  "labelHide": "False",
  "labelStyle": "",
  "rules": "[\n  {\n    \"required\": null\n  }\n]",
  "wigetProps": "{}"
}', NULL, '1', 'fr_李影', TIMESTAMP '2025-05-22 14:47:32.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000179', '33A001', 'H81', '基本信息', 'POLITICIAN', '政治面貌', NULL, NULL, '["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"]', '{
  "dataClass": "政治面貌",
  "queryType": "api",
  "dataType": "Select",
  "wigetProps": "{}",
  "labelHide": "False",
  "rules": "[\n  {\n    \"required\": null\n  }\n]"
}', '{"group":"基本","formId":"xialakuang","icon":"iconxialakuang","formName":"下拉框","isForm":true,"dataType":"Select","wigetProps":{"options":[],"defaultValue":""},"propslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"dataClass":"政治面貌","queryType":"api"}', '1', 'fr_李影', TIMESTAMP '2025-05-29 21:19:33.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000186', '33A001', 'H81', '基本信息', 'HIGHEST_DIPLOMA', '最高学位', NULL, NULL, '["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"]', '{
  "dataClass": "最高学位",
  "queryType": "api",
  "dataType": "Select",
  "wigetProps": "{\n  \"options\": [\n    {\n      \"value\": \"选项1\",\n      \"label\": \"选项1\"\n    },\n    {\n      \"value\": \"选项2\",\n      \"label\": \"选项2\"\n    },\n    {\n      \"value\": \"选项3\",\n      \"label\": \"选项3\"\n    }\n  ],\n  \"defaultValue\": \"\"\n}"
}', '{"group":"基本","formId":"xialakuang","icon":"iconxialakuang","formName":"下拉框","isForm":true,"dataType":"Select","queryType":"api","wigetProps":{"options":[{"value":"选项1","label":"选项1"},{"value":"选项2","label":"选项2"},{"value":"选项3","label":"选项3"}],"defaultValue":""},"propslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"dataClass":"最高学位"}', '1', 'fr_李影', TIMESTAMP '2025-06-10 15:24:15.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000241', '33A001', 'H81', '基本信息', 'GWLB79071', '岗位类别', NULL, NULL, '["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"]', '{
  "dataClass": "岗位类别",
  "queryType": "api",
  "dataType": "Select",
  "wigetProps": "{\n  \"options\": [\n    {\n      \"value\": \"选项1\",\n      \"label\": \"选项1\"\n    },\n    {\n      \"value\": \"选项2\",\n      \"label\": \"选项2\"\n    },\n    {\n      \"value\": \"选项3\",\n      \"label\": \"选项3\"\n    }\n  ],\n  \"defaultValue\": \"\"\n}"
}', '{"group":"基本","formId":"xialakuang","icon":"iconxialakuang","formName":"下拉框","isForm":true,"dataType":"Select","queryType":"api","wigetProps":{"options":[{"value":"选项1","label":"选项1"},{"value":"选项2","label":"选项2"},{"value":"选项3","label":"选项3"}],"defaultValue":""},"propslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"dataClass":"岗位类别"}', '1', 'fr_李影', TIMESTAMP '2025-06-10 19:00:01.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000242', '33A001', 'H81', '基本信息', 'CJPXQK82201', '参加培训情况', NULL, NULL, '["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]', '{
  "dataType": "Input",
  "rules": "[\n  {\n    \"required\": true\n  }\n]",
  "wigetProps": "{\n  \"placeholder\": \"请输入\",\n  \"allowClear\": true,\n  \"maxLength\": 200,\n  \"disabled\": false\n}"
}', '{"group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"单行文本","dataType":"Input","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]}', '1', 'fr_李影', TIMESTAMP '2025-05-28 17:21:23.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000198', '33A001', 'H81', '基本信息', 'HEALTH', '健康状况', NULL, NULL, '["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"]', '{
  "dataClass": "健康状况",
  "queryType": "api",
  "dataType": "Select",
  "wigetProps": "{}",
  "labelHide": "False",
  "rules": "[\n  {\n    \"required\": null\n  }\n]"
}', '{"group":"基本","formId":"xialakuang","icon":"iconxialakuang","formName":"下拉框","isForm":true,"dataType":"Select","wigetProps":{"options":[],"defaultValue":""},"propslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"dataClass":"健康状况","queryType":"api"}', '1', 'fr_李影', TIMESTAMP '2025-05-29 21:17:24.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000243', '33A001', 'H81', '基本信息', 'PXHGZH48698', '培训合格证号', NULL, NULL, '["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]', '{
  "dataType": "Input",
  "rules": "[\n  {\n    \"required\": true\n  }\n]",
  "wigetProps": "{\n  \"placeholder\": \"请输入\",\n  \"allowClear\": true,\n  \"maxLength\": 200,\n  \"disabled\": false\n}"
}', '{"group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"单行文本","dataType":"Input","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]}', '1', 'fr_李影', TIMESTAMP '2025-05-28 17:21:52.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000245', '33A001', 'H81', '基本信息', 'XZD45449', '新字段', NULL, NULL, '["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"]', '{
  "dataClass": "参会人身份",
  "dataType": "Select",
  "wigetProps": "{\r\n  \"options\": [],\r\n  \"defaultValue\": \"XBD00000001\"\r\n}",
  "labelHide": "False",
  "labelStyle": "{\r\n  \"color\": \"#000000\",\r\n  \"fontSize\": 13,\r\n  \"fontWeight\": \"normal\",\r\n  \"fontStyle\": \"normal\",\r\n  \"background\": null,\r\n  \"textAlign\": \"right\",\r\n  \"justifyContent\": \"right\"\r\n}",
  "rules": "[\r\n  {\r\n    \"required\": null\r\n  }\r\n]"
}', '{"group":"基本","formId":"xialakuang","icon":"iconxialakuang","formName":"下拉框","isForm":true,"dataType":"Select","wigetProps":{"options":[],"defaultValue":"XBD00000001"},"propslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"queryType":"api","dataClass":"参会人身份","isDataTypeToPerson":true}', '1', 'fr_李影', TIMESTAMP '2025-05-28 20:11:00.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000220', '33A001', 'H81', '基本信息', 'IN_HOSPITAL_TIME', '来院日期', NULL, NULL, '[
  "dataType",
  "required",
  "disabled",
  "picker",
  "allowClear"
]', '{
  "dataClass": null,
  "dataType": "DatePicker",
  "labelHide": false,
  "labelStyle": null,
  "rules": null,
  "wigetProps": {
    "button": false,
    "groupKey": null
  }
}', NULL, '1', 'fr_李影', TIMESTAMP '2025-05-22 14:47:36.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000221', '33A001', 'H81', '基本信息', 'OUT_HOSPITAL_TIME', '离院日期', NULL, NULL, '[
  "dataType",
  "required",
  "disabled",
  "picker",
  "allowClear"
]', '{
  "dataClass": null,
  "dataType": "DatePicker",
  "labelHide": false,
  "labelStyle": null,
  "rules": null,
  "wigetProps": {
    "button": false,
    "groupKey": null
  }
}', NULL, '1', 'fr_李影', TIMESTAMP '2025-05-22 14:47:36.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000222', '33A001', 'H81', '基本信息', 'LENGTH_HOSPITAL', '院龄', NULL, NULL, '[
  "dataType",
  "required",
  "allowClear",
  "placeholder",
  "maxLength",
  "prefix",
  "suffix",
  "variant",
  "disabled",
  "showCount"
]', '{
  "dataClass": null,
  "dataType": "Input",
  "labelHide": false,
  "labelStyle": null,
  "rules": [
    {
      "required": true
    }
  ],
  "wigetProps": {
    "button": false,
    "groupKey": null
  }
}', NULL, '1', 'fr_李影', TIMESTAMP '2025-05-22 14:47:37.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000223', '33A001', 'H81', '基本信息', 'IN_LAB_TIME', '来科日期', NULL, NULL, '[
  "dataType",
  "required",
  "disabled",
  "picker",
  "allowClear"
]', '{
  "dataClass": null,
  "dataType": "DatePicker",
  "labelHide": false,
  "labelStyle": null,
  "rules": null,
  "wigetProps": {
    "button": false,
    "groupKey": null
  }
}', NULL, '1', 'fr_李影', TIMESTAMP '2025-05-22 14:47:37.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000224', '33A001', 'H81', '基本信息', 'OUT_LAB_TIME', '离科日期', NULL, NULL, '[
  "dataType",
  "required",
  "disabled",
  "picker",
  "allowClear"
]', '{
  "dataClass": null,
  "dataType": "DatePicker",
  "labelHide": false,
  "labelStyle": null,
  "rules": null,
  "wigetProps": {
    "button": false,
    "groupKey": null
  }
}', NULL, '1', 'fr_李影', TIMESTAMP '2025-05-22 14:47:37.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000225', '33A001', 'H81', '基本信息', 'LENGTH_LAB', '科龄', NULL, NULL, '[
  "dataType",
  "required",
  "allowClear",
  "placeholder",
  "maxLength",
  "prefix",
  "suffix",
  "variant",
  "disabled",
  "showCount"
]', '{
  "dataClass": null,
  "dataType": "Input",
  "labelHide": false,
  "labelStyle": null,
  "rules": [
    {
      "required": true
    }
  ],
  "wigetProps": {
    "button": false,
    "groupKey": null
  }
}', NULL, '1', 'fr_李影', TIMESTAMP '2025-05-22 14:47:37.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000226', '33A001', 'H81', '基本信息', 'XZD35566', '新字段', NULL, NULL, '["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]', '{"dataType":"Input","rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false}}', '{"group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"单行文本","dataType":"Input","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]}', '2', NULL, NULL, 'fr_李影', TIMESTAMP '2025-05-28 17:12:14.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000240', '33A001', 'H81', '基本信息', 'XCSGW70984', '现从事岗位', NULL, NULL, '["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"]', '{
  "dataClass": "现从事岗位",
  "queryType": "api",
  "dataType": "Select",
  "wigetProps": "{\n  \"options\": [\n    {\n      \"value\": \"选项1\",\n      \"label\": \"选项1\"\n    },\n    {\n      \"value\": \"选项2\",\n      \"label\": \"选项2\"\n    },\n    {\n      \"value\": \"选项3\",\n      \"label\": \"选项3\"\n    }\n  ],\n  \"defaultValue\": \"\"\n}"
}', '{"group":"基本","formId":"xialakuang","icon":"iconxialakuang","formName":"下拉框","isForm":true,"dataType":"Select","queryType":"api","wigetProps":{"options":[{"value":"选项1","label":"选项1"},{"value":"选项2","label":"选项2"},{"value":"选项3","label":"选项3"}],"defaultValue":""},"propslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"dataClass":"现从事岗位"}', '1', 'fr_李影', TIMESTAMP '2025-06-10 19:00:01.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000174', '33A001', 'H81', '基本信息', 'CARD_TYPE', '证件类型', NULL, NULL, '["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"]', '{
  "dataClass": "证件类型",
  "queryType": "api",
  "dataType": "Select",
  "wigetProps": "{\n  \"defaultValue\": \"1\"\n}",
  "labelHide": "False",
  "rules": "[\n  {\n    \"required\": null\n  }\n]"
}', '{"group":"基本","formId":"xialakuang","icon":"iconxialakuang","formName":"下拉框","isForm":true,"dataType":"Select","wigetProps":{"options":[],"defaultValue":"3"},"propslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"dataClass":"证件类型","queryType":"api"}', '1', 'fr_李影', TIMESTAMP '2025-05-29 20:39:32.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000213', '33A001', 'H81', '基本信息', 'TECH_POST', '职称级别', NULL, NULL, '["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"]', '{
  "dataClass": "职称级别",
  "queryType": "api",
  "dataType": "Select",
  "wigetProps": "{}",
  "labelHide": "False",
  "rules": "[\n  {\n    \"required\": null\n  }\n]"
}', '{"group":"基本","formId":"xialakuang","icon":"iconxialakuang","formName":"下拉框","isForm":true,"dataType":"Select","wigetProps":{"options":[],"defaultValue":""},"propslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"dataClass":"职称级别","queryType":"api"}', '1', 'fr_李影', TIMESTAMP '2025-05-29 21:18:52.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000228', '33A001', 'H81', '基本信息', 'ZXZD69033', '最新字段', NULL, NULL, '["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]', '{"dataType":"Input","rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false}}', '{"group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"单行文本","dataType":"Input","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]}', '2', 'fr_李影', TIMESTAMP '2025-05-27 18:43:49.000000', 'fr_李影', TIMESTAMP '2025-05-28 17:11:58.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000229', '33A001', 'H81', '基本信息', 'ZXZD62529', '最新字段', NULL, NULL, '["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]', '{"dataType":"Input","rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false}}', '{"group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"单行文本","dataType":"Input","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]}', '2', 'fr_李影', TIMESTAMP '2025-05-27 18:43:50.000000', 'fr_李影', TIMESTAMP '2025-05-28 17:12:01.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000237', '33A001', 'H81', '基本信息', 'BASYS55209', '备案实验室', NULL, NULL, '["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]', '{
  "dataClass": "DataClass_By_Smbl_Lab",
  "queryType": "api",
  "dataType": "Select",
  "rules": "[\n  {\n    \"required\": true\n  }\n]",
  "wigetProps": "{\n  \"placeholder\": \"请输入\",\n  \"allowClear\": true,\n  \"maxLength\": 200,\n  \"disabled\": false\n}"
}', '{"group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"单行文本","dataType":"Input","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]}', '1', 'fr_李影', TIMESTAMP '2025-06-04 09:49:45.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000208', '33A001', 'H81', '基本信息', 'DUTIES', '职务', NULL, NULL, '["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"]', '{
  "dataClass": "职务",
  "queryType": "api",
  "dataType": "Select",
  "wigetProps": "{}",
  "labelHide": "False",
  "rules": "[\n  {\n    \"required\": null\n  }\n]"
}', '{"group":"基本","formId":"xialakuang","icon":"iconxialakuang","formName":"下拉框","isForm":true,"dataType":"Select","wigetProps":{"options":[],"defaultValue":""},"propslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"dataClass":"职务","queryType":"api"}', '1', 'fr_李影', TIMESTAMP '2025-05-29 21:21:08.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000227', '33A001', 'H81', '基本信息', 'ZXZD59943', '最新字段', NULL, NULL, '["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]', '{"dataType":"Input","rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false}}', '{"group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"单行文本","dataType":"Input","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]}', '2', 'fr_李影', TIMESTAMP '2025-05-27 18:36:03.000000', 'fr_李影', TIMESTAMP '2025-05-28 17:12:04.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000238', '33A001', 'H81', '基本信息', 'SFWDWKYHZRY39890', '是否外单位科研合作人员', NULL, NULL, '["dataType","required","optionType","styleFlex","disabled","queryType","dataClass","defaultValue","isDataTypeToPerson"]', '{
  "dataClass": "是否",
  "dataType": "Radio",
  "wigetProps": "{\n  \"options\": [],\n  \"defaultValue\": \"\"\n}"
}', '{"group":"基本","formId":"danxuankuang","icon":"icondanxuan","formName":"单选","isForm":true,"dataType":"Radio","wigetProps":{"options":[],"defaultValue":""},"propslist":["dataType","required","optionType","styleFlex","disabled","queryType","dataClass","defaultValue","isDataTypeToPerson"],"dataClass":"是否","queryType":"api"}', '1', 'fr_李影', TIMESTAMP '2025-05-28 17:18:19.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000177', '33A001', 'H81', '基本信息', 'PGROUP_ID', '专业组', NULL, NULL, '["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"]', '{
  "dataClass": "DataClass_By_PGROUP_ID",
  "queryType": "api",
  "dataType": "Select",
  "wigetProps": {
    "options": [
      {
        "value": "选项1",
        "label": "选项1"
      },
      {
        "value": "选项2",
        "label": "选项2"
      },
      {
        "value": "选项3",
        "label": "选项3"
      }
    ]
  }
}', '{"group":"基本","formId":"xialakuang","icon":"iconxialakuang","formName":"下拉框","isForm":true,"dataType":"Select","dataClass":"DataClass_By_PGROUP_ID","queryType":"api","wigetType":"fixed","wigetProps":{"options":[{"value":"选项1","label":"选项1"},{"value":"选项2","label":"选项2"},{"value":"选项3","label":"选项3"}]},"propslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"]}', '1', 'fr_李影', TIMESTAMP '2025-05-29 21:24:28.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000217', '33A001', 'H81', '基本信息', 'WORK_TIME', '参加工作日期', NULL, NULL, '[
  "dataType",
  "required",
  "disabled",
  "picker",
  "allowClear"
]', '{
  "dataClass": null,
  "dataType": "DatePicker",
  "labelHide": false,
  "labelStyle": null,
  "rules": [
    {
      "required": true
    }
  ],
  "wigetProps": {
    "button": false,
    "groupKey": null
  }
}', NULL, '1', 'fr_李影', TIMESTAMP '2025-05-22 14:47:36.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000190', '33A001', 'H81', '基本信息', 'ENGLISH_RANK', '英语等级', NULL, NULL, '["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"]', '{
  "queryType": "api",
  "dataType": "Select",
  "wigetProps": "{\n  \"options\": []\n}"
}', '{"group":"基本","formId":"xialakuang","icon":"iconxialakuang","formName":"下拉框","isForm":true,"dataType":"Select","queryType":"api","wigetProps":{"options":[{"value":"选项1","label":"选项1"},{"value":"选项2","label":"选项2"},{"value":"选项3","label":"选项3"}]},"propslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"]}', '1', 'fr_李影', TIMESTAMP '2025-06-09 13:40:25.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000239', '33A001', 'H81', '基本信息', 'BZ63459', '备注', NULL, NULL, '["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]', '{
  "dataType": "Input",
  "rules": "[\n  {\n    \"required\": false\n  }\n]",
  "wigetProps": "{\n  \"placeholder\": \"请输入\",\n  \"allowClear\": true,\n  \"maxLength\": 200,\n  \"disabled\": false\n}"
}', '{"group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"单行文本","dataType":"Input","isForm":true,"rules":[{"required":false}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]}', '1', 'fr_李影', TIMESTAMP '2025-06-10 19:00:01.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('*********', '33A001', 'H81', '基本信息', 'PXHGZS25179', '培训合格证书', NULL, NULL, '["dataType","required","multiple","listType","accept"]', '{
  "dataType": "Upload",
  "wigetProps": "{\n  \"listType\": \"picture-card\",\n  \"multiple\": true,\n  \"accept\": \".png,.jpg,.jpeg,.pdf,.xls,.xlsx,.doc,.docx\",\n  \"showUploadList\": true\n}"
}', '{"group":"基本","formId":"fujian","icon":"iconfujian","formName":"附件","isForm":true,"dataType":"Upload","wigetProps":{"listType":"picture-card","multiple":true,"accept":".png,.jpg,.jpeg,.pdf,.xls,.xlsx,.doc,.docx","showUploadList":true},"propslist":["dataType","required","multiple","listType","accept"]}', '1', 'fr_李影', TIMESTAMP '2025-05-28 17:23:05.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('*********', '33A001', 'H81', '基本信息', 'CHILDREN_CONDITION', '有无子女', NULL, NULL, '["dataType","required","optionType","styleFlex","disabled","queryType","dataClass","defaultValue","isDataTypeToPerson"]', '{
  "dataClass": "有无子女",
  "queryType": "api",
  "dataType": "Radio",
  "wigetProps": {
    "options": [],
    "defaultValue": "2"
  }
}', '{"group":"基本","formId":"danxuankuang","icon":"icondanxuan","formName":"单选","isForm":true,"dataType":"Radio","wigetProps":{"options":[],"defaultValue":"2"},"propslist":["dataType","required","optionType","styleFlex","disabled","queryType","dataClass","defaultValue","isDataTypeToPerson"],"dataClass":"有无子女","queryType":"api"}', '1', 'fr_李影', TIMESTAMP '2025-05-30 10:51:59.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000183', '33A001', 'H81', '基本信息', 'DOMICILE_PLACE', '户籍所在地', NULL, NULL, '["dataType","required","allowClear","prefix","variant","placeholder","disabled","showSearch"]', '{
  "dataType": "Cascader",
  "wigetProps": {}
}', '{"group":"基本","formId":"jilianxuanze","icon":"iconxialakuang","formName":"级联选择","isForm":true,"dataType":"Cascader","wigetProps":{},"propslist":["dataType","required","allowClear","prefix","variant","placeholder","disabled","showSearch"]}', '1', 'fr_李影', TIMESTAMP '2025-06-06 11:24:20.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000258', '33A001', 'H81', '基本信息', 'SZ52717', '数值', NULL, NULL, '["dataType","required","allowClear","placeholder","maxLength","min","max","prefix","suffix","variant","disabled","controls"]', '{
  "dataType": "InputNumber",
  "rules": "[\n  {\n    \"required\": true\n  }\n]",
  "wigetProps": "{\n  \"controls\": true\n}",
  "dataClass": "",
  "labelHide": "False",
  "labelStyle": "{\n  \"color\": \"#000000\",\n  \"fontSize\": 13,\n  \"fontWeight\": \"normal\",\n  \"fontStyle\": \"normal\",\n  \"background\": null,\n  \"textAlign\": \"right\",\n  \"justifyContent\": \"right\"\n}"
}', '{"group":"基本","formId":"shuzhishurukuang","icon":"iconshuzhishurukuang","formName":"数值","isForm":true,"dataType":"InputNumber","wigetProps":{"controls":true},"propslist":["dataType","required","allowClear","placeholder","maxLength","min","max","prefix","suffix","variant","disabled","controls"],"rules":[{"required":true}]}', '1', 'fr_李影', TIMESTAMP '2025-06-05 15:16:13.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000250', '33A001', 'H81', '基本信息', 'JBXXX75570', '基本信息项', NULL, NULL, '["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","defaultValue","isDataTypeToPerson"]', '{
  "group": "基本",
  "formId": "xialakuang",
  "icon": "iconxialakuang",
  "formName": "下拉框",
  "isForm": true,
  "dataType": "Select",
  "wigetProps": "{\n  \"options\": [\n    {\n      \"PERSON_TAG_IDS\": [],\n      \"PERSON_TAG_NAMES\": \"\",\n      \"DATA_TABLE\": \"OA_BASE_DATA\",\n      \"DATA_ID\": \"H818100000121\",\n      \"HOSPITAL_ID\": null,\n      \"MODULE_ID\": null,\n      \"FATHER_ID\": null,\n      \"CLASS_ID\": \"H810000-JBXXX75570-78vemt4u\",\n      \"DATA_SORT\": \"1748913185\",\n      \"DATA_NAME\": \"选项1\",\n      \"DATA_SNAME\": null,\n      \"DATA_ENAME\": null,\n      \"STANDART_ID\": null,\n      \"CUSTOM_CODE\": null,\n      \"SPELL_CODE\": null,\n      \"STATE_FLAG\": \"1\",\n      \"FIRST_RPERSON\": \"110_渠晓娜\",\n      \"FIRST_RTIME\": \"2025-06-03 09:13:05\",\n      \"LAST_MPERSON\": \"110_渠晓娜\",\n      \"LAST_MTIME\": \"2025-06-03 09:13:05\",\n      \"REMARK\": null\n    },\n    {\n      \"PERSON_TAG_IDS\": [],\n      \"PERSON_TAG_NAMES\": \"\",\n      \"DATA_TABLE\": \"OA_BASE_DATA\",\n      \"DATA_ID\": \"H818100000122\",\n      \"HOSPITAL_ID\": null,\n      \"MODULE_ID\": null,\n      \"FATHER_ID\": null,\n      \"CLASS_ID\": \"H810000-JBXXX75570-78vemt4u\",\n      \"DATA_SORT\": \"1748913189\",\n      \"DATA_NAME\": \"选项2\",\n      \"DATA_SNAME\": null,\n      \"DATA_ENAME\": null,\n      \"STANDART_ID\": null,\n      \"CUSTOM_CODE\": null,\n      \"SPELL_CODE\": null,\n      \"STATE_FLAG\": \"1\",\n      \"FIRST_RPERSON\": \"110_渠晓娜\",\n      \"FIRST_RTIME\": \"2025-06-03 09:13:09\",\n      \"LAST_MPERSON\": \"110_渠晓娜\",\n      \"LAST_MTIME\": \"2025-06-03 09:13:09\",\n      \"REMARK\": null\n    },\n    {\n      \"PERSON_TAG_IDS\": [],\n      \"PERSON_TAG_NAMES\": \"\",\n      \"DATA_TABLE\": \"OA_BASE_DATA\",\n      \"DATA_ID\": \"H818100000123\",\n      \"HOSPITAL_ID\": null,\n      \"MODULE_ID\": null,\n      \"FATHER_ID\": null,\n      \"CLASS_ID\": \"H810000-JBXXX75570-78vemt4u\",\n      \"DATA_SORT\": \"1748913193\",\n      \"DATA_NAME\": \"选项3\",\n      \"DATA_SNAME\": null,\n      \"DATA_ENAME\": null,\n      \"STANDART_ID\": null,\n      \"CUSTOM_CODE\": null,\n      \"SPELL_CODE\": null,\n      \"STATE_FLAG\": \"1\",\n      \"FIRST_RPERSON\": \"110_渠晓娜\",\n      \"FIRST_RTIME\": \"2025-06-03 09:13:13\",\n      \"LAST_MPERSON\": \"110_渠晓娜\",\n      \"LAST_MTIME\": \"2025-06-03 09:13:13\",\n      \"REMARK\": null\n    }\n  ]\n}",
  "propslist": [
    "dataType",
    "required",
    "allowClear",
    "prefix",
    "suffix",
    "variant",
    "placeholder",
    "disabled",
    "showSearch",
    "mode",
    "queryType",
    "defaultValue",
    "isDataTypeToPerson"
  ],
  "queryType": "enum",
  "dataClass": "H810000-JBXXX75570-78vemt4u"
}', NULL, '1', '110_渠晓娜', TIMESTAMP '2025-06-03 09:13:18.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000252', '33A001', 'H81', '基本信息', 'GZGLLX49535', '工作管理类型', NULL, NULL, '["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","defaultValue","isDataTypeToPerson"]', '{
  "dataClass": "H810000-HIGHEST_DIPLOMA-tctpgeyc",
  "dataType": "Select",
  "queryType": "enum",
  "wigetProps": "{\n  \"options\": [\n    {\n      \"PERSON_TAG_IDS\": [\n        \"TG000104\",\n        \"TG000003X\",\n        \"TG000101\"\n      ],\n      \"PERSON_TAG_NAMES\": \"P2、生安、P1\",\n      \"DATA_TABLE\": \"OA_BASE_DATA\",\n      \"DATA_ID\": \"H818100000132\",\n      \"HOSPITAL_ID\": null,\n      \"MODULE_ID\": null,\n      \"FATHER_ID\": null,\n      \"CLASS_ID\": \"H810000-HIGHEST_DIPLOMA-tctpgeyc\",\n      \"DATA_SORT\": \"1749001591\",\n      \"DATA_NAME\": \"工作管理类A\",\n      \"DATA_SNAME\": null,\n      \"DATA_ENAME\": null,\n      \"STANDART_ID\": null,\n      \"CUSTOM_CODE\": null,\n      \"SPELL_CODE\": null,\n      \"STATE_FLAG\": \"1\",\n      \"FIRST_RPERSON\": \"fr_李影\",\n      \"FIRST_RTIME\": \"2025-06-04 09:46:31\",\n      \"LAST_MPERSON\": \"fr_李影\",\n      \"LAST_MTIME\": \"2025-06-04 09:47:08\",\n      \"REMARK\": null\n    },\n    {\n      \"PERSON_TAG_IDS\": [],\n      \"PERSON_TAG_NAMES\": \"\",\n      \"DATA_TABLE\": \"OA_BASE_DATA\",\n      \"DATA_ID\": \"H818100000133\",\n      \"HOSPITAL_ID\": null,\n      \"MODULE_ID\": null,\n      \"FATHER_ID\": null,\n      \"CLASS_ID\": \"H810000-HIGHEST_DIPLOMA-tctpgeyc\",\n      \"DATA_SORT\": \"1749001752\",\n      \"DATA_NAME\": \"工作管理类型B\",\n      \"DATA_SNAME\": null,\n      \"DATA_ENAME\": null,\n      \"STANDART_ID\": null,\n      \"CUSTOM_CODE\": null,\n      \"SPELL_CODE\": null,\n      \"STATE_FLAG\": \"1\",\n      \"FIRST_RPERSON\": \"fr_李影\",\n      \"FIRST_RTIME\": \"2025-06-04 09:49:12\",\n      \"LAST_MPERSON\": \"fr_李影\",\n      \"LAST_MTIME\": \"2025-06-04 09:49:12\",\n      \"REMARK\": null\n    }\n  ]\n}"
}', NULL, '1', 'fr_李影', TIMESTAMP '2025-06-05 15:48:42.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000256', '33A001', 'H81', '基本信息', 'JBXX70654', '基本信息', NULL, NULL, '["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]', '{
  "dataType": "Input",
  "rules": "[\n  {\n    \"required\": true\n  }\n]",
  "wigetProps": "{\n  \"placeholder\": \"请输入\",\n  \"allowClear\": false,\n  \"maxLength\": 200,\n  \"disabled\": false,\n  \"variant\": \"outlined\"\n}",
  "dataClass": "",
  "labelHide": "False",
  "labelStyle": "{\n  \"color\": \"#000000\",\n  \"fontSize\": 13,\n  \"fontWeight\": \"normal\",\n  \"fontStyle\": \"normal\",\n  \"background\": null,\n  \"textAlign\": \"right\",\n  \"justifyContent\": \"right\"\n}"
}', '{"group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"单行文本","dataType":"Input","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":false,"maxLength":200,"disabled":false,"variant":"outlined"},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]}', '1', 'fr_李影', TIMESTAMP '2025-06-05 15:15:31.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000215', '33A001', 'H81', '基本信息', 'ACADEMIC_POST', '职称名称', NULL, NULL, '["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","defaultValue","isDataTypeToPerson"]', '{"dataClass":"H810000-ACADEMIC_POST","dataType":"Select","queryType":"enum","wigetProps":{"options":[]}}', '{"group":"基本","formId":"xialakuang","icon":"iconxialakuang","formName":"下拉框","isForm":true,"dataType":"Select","queryType":"enum","wigetProps":{"options":[]},"propslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","defaultValue","isDataTypeToPerson"],"dataClass":"H810000-ACADEMIC_POST"}', '1', 'fr_李影', TIMESTAMP '2025-06-11 11:26:42.000000', 'fr_李影', TIMESTAMP '2025-06-11 11:26:48.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000264', '33A001', 'H81', '基本信息', 'YSXZQ25786', '颜色选择器', NULL, NULL, '["dataType","required","disabled","showText"]', '{
  "dataType": "ColorPicker",
  "wigetProps": "{\n  \"showText\": true,\n  \"allowClear\": true\n}",
  "dataClass": "",
  "labelHide": "False",
  "labelStyle": "{\n  \"color\": \"#000000\",\n  \"fontSize\": 13,\n  \"fontWeight\": \"normal\",\n  \"fontStyle\": \"normal\",\n  \"background\": null,\n  \"textAlign\": \"right\",\n  \"justifyContent\": \"right\"\n}",
  "rules": "[\n  {\n    \"required\": null\n  }\n]"
}', '{"group":"基本","formId":"yansexuanze","icon":"iconyanse","formName":"颜色选择器","dataType":"ColorPicker","isForm":true,"wigetProps":{"showText":true,"allowClear":true},"propslist":["dataType","required","disabled","showText"]}', '1', 'fr_李影', TIMESTAMP '2025-06-05 15:18:37.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000267', '33A001', 'H81', '基本信息', 'GWLB57036', '岗位类别', NULL, NULL, '["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]', '{
  "dataType": "Input",
  "rules": "[\n  {\n    \"required\": true\n  }\n]",
  "wigetProps": "{\n  \"placeholder\": \"请输入\",\n  \"allowClear\": true,\n  \"maxLength\": 200,\n  \"disabled\": false\n}",
  "dataClass": "",
  "labelHide": "False",
  "labelStyle": "{\n  \"color\": \"#000000\",\n  \"fontSize\": 13,\n  \"fontWeight\": \"normal\",\n  \"fontStyle\": \"normal\",\n  \"background\": null,\n  \"textAlign\": \"right\",\n  \"justifyContent\": \"right\"\n}"
}', '{"group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"单行文本","dataType":"Input","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]}', '1', 'fr_李影', TIMESTAMP '2025-06-08 14:50:48.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000194', '33A001', 'H81', '基本信息', 'COLOR_DEFICIENCY', '颜色视觉障碍', NULL, NULL, '["dataType","required","optionType","styleFlex","disabled","queryType","defaultValue","isDataTypeToPerson"]', '{"dataClass":"H810000-COLOR_DEFICIENCY","dataType":"Radio","queryType":"enum","wigetProps":{"options":[{"label":"正常","value":"0"},{"label":"色弱","value":"1"},{"label":"色盲","value":"2"}]}}', '{"group":"基本","formId":"danxuankuang","icon":"icondanxuan","formName":"单选","isForm":true,"dataType":"Radio","queryType":"enum","wigetProps":{"options":[{"label":"正常","value":"0"},{"label":"色弱","value":"1"},{"label":"色盲","value":"2"}]},"propslist":["dataType","required","optionType","styleFlex","disabled","queryType","defaultValue","isDataTypeToPerson"],"dataClass":"H810000-COLOR_DEFICIENCY","readOnlyEnum":"1"}', '1', 'fr_李影', TIMESTAMP '2025-06-11 13:35:51.000000', 'fr_李影', TIMESTAMP '2025-06-11 13:48:24.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000270', '33A001', 'H81', '基本信息', 'YYJB50394', '英语级别/成绩(分)', NULL, NULL, '["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"]', '{
  "dataClass": "英语级别",
  "queryType": "api",
  "dataType": "Select",
  "rules": [
    {
      "required": false
    }
  ],
  "wigetProps": {
    "options": [
      {
        "value": "选项1",
        "label": "选项1"
      },
      {
        "value": "选项2",
        "label": "选项2"
      },
      {
        "value": "选项3",
        "label": "选项3"
      }
    ],
    "defaultValue": ""
  }
}', '{"group":"基本","formId":"xialakuang","icon":"iconxialakuang","formName":"下拉框","isForm":true,"dataType":"Select","queryType":"api","wigetProps":{"options":[{"value":"选项1","label":"选项1"},{"value":"选项2","label":"选项2"},{"value":"选项3","label":"选项3"}],"defaultValue":""},"propslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"dataClass":"英语级别","rules":[{"required":false}]}', '1', 'fr_李影', TIMESTAMP '2025-06-11 09:44:53.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000173', '33A001', 'H81', '基本信息', 'NATION', '民族', NULL, NULL, '["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","dataClass","defaultValue","isDataTypeToPerson"]', '{
  "dataClass": "民族",
  "queryType": "api",
  "dataType": "Select",
  "wigetProps": "{\n  \"defaultValue\": \"1\"\n}",
  "labelHide": "False",
  "rules": "[\n  {\n    \"required\": true\n  }\n]"
}', '{"group":"基本","formId":"xialakuang","icon":"iconxialakuang","formName":"下拉框","isForm":true,"dataType":"Select","queryType":"api","wigetProps":{"options":[{"value":"选项1","label":"选项1"},{"value":"选项2","label":"选项2"},{"value":"选项3","label":"选项3"}],"defaultValue":""},"propslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","dataClass","defaultValue","isDataTypeToPerson"],"dataClass":"民族"}', '1', 'fr_李影', TIMESTAMP '2025-06-06 15:49:39.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000191', '33A001', 'H81', '基本信息', 'ENGLISH_RANK_SCORE', '英语成绩', NULL, NULL, '["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]', '{
  "dataType": "Input",
  "rules": "[\n  {\n    \"required\": true\n  }\n]",
  "wigetProps": "{\n  \"placeholder\": \"请输入\",\n  \"allowClear\": true,\n  \"maxLength\": 200,\n  \"disabled\": false\n}",
  "dataClass": "英语级别",
  "labelHide": "False",
  "labelStyle": "{\n  \"color\": \"#000000\",\n  \"fontSize\": 13,\n  \"fontWeight\": \"normal\",\n  \"fontStyle\": \"normal\",\n  \"background\": null,\n  \"textAlign\": \"right\",\n  \"justifyContent\": \"right\"\n}"
}', '{"group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"单行文本","dataType":"Input","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]}', '1', 'fr_李影', TIMESTAMP '2025-06-10 18:56:59.000000', 'fr_李影', TIMESTAMP '2025-06-11 10:26:30.000000', NULL);