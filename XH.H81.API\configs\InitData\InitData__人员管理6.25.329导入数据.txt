===========================================================================================
=====================================人员管理6.25.329导入数据======================================================
===========================================================================================
XH.H81.Models.Entities.SYS6_BASE_DATA_CLASS, XH.H81.Models##INSERT##基础数据分类：人员评估分类
{"DATA_CLASS_ID":"人员评估分类","HOSPITAL_ID":"H0000","CLASS_ID":"人员评估分类","CLASS_NAME":"人员评估分类","CLASS_SORT":"1","DATA_TABLE":"OA_BASE_DATA","DATA_FIELD":"DATA_NAME","CLASS_STATE":"1","FIRST_RPERSON":"初始化","FIRST_RTIME":"2025-05-13T14:45:24","LAST_MPERSON":"初始化","LAST_MTIME":"2022-05-13T14:45:24","REMARK":null,"ONE_CLASS":"实验室管理相关"}
XH.H81.Models.Entities.SYS6_BASE_DATA_CLASS, XH.H81.Models##INSERT##基础数据分类：人员考试分类
{"DATA_CLASS_ID":"人员考试分类","HOSPITAL_ID":"H0000","CLASS_ID":"人员考试分类","CLASS_NAME":"人员考试分类","CLASS_SORT":"1","DATA_TABLE":"OA_BASE_DATA","DATA_FIELD":"DATA_NAME","CLASS_STATE":"1","FIRST_RPERSON":"初始化","FIRST_RTIME":"2025-05-13T14:45:24","LAST_MPERSON":"初始化","LAST_MTIME":"2022-05-13T14:45:24","REMARK":null,"ONE_CLASS":"实验室管理相关"}
XH.LAB.UTILS.Models.Entites.OA_BASE_DATA, XH.LAB.UTILS##UPDATE:DATA_NAME##OA基础数据-颜色视觉障碍：正常
{"DATA_ID":"0","HOSPITAL_ID":"H0000","MODULE_ID":"H81","FATHER_ID":null,"CLASS_ID":"H810000-COLOR_DEFICIENCY","DATA_SORT":"0","DATA_NAME":"正常","DATA_SNAME":null,"DATA_ENAME":null,"STANDART_ID":null,"CUSTOM_CODE":null,"SPELL_CODE":null,"STATE_FLAG":"1","FIRST_RPERSON":"初始化","FIRST_RTIME":"2023-04-04T17:30:01","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:38:50","REMARK":null}
XH.LAB.UTILS.Models.Entites.OA_BASE_DATA, XH.LAB.UTILS##UPDATE:DATA_NAME##OA基础数据-颜色视觉障碍：色弱
{"DATA_ID":"1","HOSPITAL_ID":"H0000","MODULE_ID":"H81","FATHER_ID":null,"CLASS_ID":"H810000-COLOR_DEFICIENCY","DATA_SORT":"1","DATA_NAME":"色弱","DATA_SNAME":null,"DATA_ENAME":null,"STANDART_ID":null,"CUSTOM_CODE":null,"SPELL_CODE":null,"STATE_FLAG":"1","FIRST_RPERSON":"初始化","FIRST_RTIME":"2023-04-04T17:30:01","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:38:50","REMARK":null}
XH.LAB.UTILS.Models.Entites.OA_BASE_DATA, XH.LAB.UTILS##UPDATE:DATA_NAME##OA基础数据-颜色视觉障碍：色盲
{"DATA_ID":"2","HOSPITAL_ID":"H0000","MODULE_ID":"H81","FATHER_ID":null,"CLASS_ID":"H810000-COLOR_DEFICIENCY","DATA_SORT":"2","DATA_NAME":"色盲","DATA_SNAME":null,"DATA_ENAME":null,"STANDART_ID":null,"CUSTOM_CODE":null,"SPELL_CODE":null,"STATE_FLAG":"1","FIRST_RPERSON":"初始化","FIRST_RTIME":"2023-04-04T17:30:01","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:38:51","REMARK":null}
XH.LAB.UTILS.Models.SYS6_MODULE_FUNC_DICT, XH.LAB.UTILS##UPDATE:FORM_JSON,FORM_COL_JSON##复杂表单模板数据：实验室ISO15189人员表单
{"SETUP_ID":"H81_TG000001X","FUNC_ID":"H8107","HOSPITAL_ID":"33A001","FIRST_RPERSON":"H81初始化","SKIN_ID":null,"LAST_MTIME":"2025-07-09T14:38:51","IF_DOUBLE_CLICK":null,"FORM_QUERY_JSON":null,"SETUP_CNAME":"实验室ISO15189人员表单","SETUP_SORT":"001","SETUP_STATE":"1","SETUP_DESC":null,"FORM_COL_JSON":"{\"form\":[{\"formName\":\"民族\",\"formCode\":\"NATION\",\"formCname\":\"民族\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Select\",\"sort\":null,\"dataClass\":\"民族\",\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"defaultValue\",\"isDataTypeToPerson\"],\"styleJson\":{\"formName\":\"民族\",\"dataClass\":\"民族\",\"dataType\":\"Select\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}},\"queryType\":\"api\"},{\"formName\":\"姓名\",\"formCode\":\"USER_NAME\",\"formCname\":\"姓名\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Input\",\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"],\"styleJson\":{\"formName\":\"姓名\",\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}},{\"formName\":\"工号\",\"formCode\":\"HIS_ID\",\"formCname\":\"工号\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Input\",\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"styleJson\":{\"formName\":\"工号\",\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":{\"color\":\"rgb(0,0,0)\"},\"rules\":[{\"required\":null}],\"widgetProps\":{\"style\":{\"color\":\"rgb(0,0,0)\"}}}},{\"formName\":\"出生年月\",\"formCode\":\"BIRTHDAY\",\"formCname\":\"出生年月\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"DatePicker\",\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"disabled\",\"picker\",\"allowClear\"],\"styleJson\":{\"formName\":\"出生年月\",\"dataType\":\"DatePicker\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}},{\"formName\":\"登录ID\",\"formCode\":\"LOGID\",\"formCname\":\"登录ID\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Input\",\"sort\":null,\"dataClass\":\"人员培训分类\",\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"],\"styleJson\":{\"formName\":\"登录ID\",\"dataClass\":\"人员培训分类\",\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}},{\"formName\":\"专业组\",\"formCode\":\"PGROUP_ID\",\"formCname\":\"专业组\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Select\",\"sort\":null,\"dataClass\":\"DataClass_By_PGROUP_ID\",\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"defaultValue\",\"isDataTypeToPerson\"],\"styleJson\":{\"formName\":\"专业组\",\"dataClass\":\"DataClass_By_PGROUP_ID\",\"dataType\":\"Select\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}},\"queryType\":\"api\"},{\"formName\":\"证件号码\",\"formCode\":\"ID_CARD\",\"formCname\":\"证件号码\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Input\",\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"styleJson\":{\"formName\":\"证件号码\",\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":{\"color\":\"rgb(0,0,0)\"},\"rules\":[{\"required\":true}],\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}},{\"formName\":\"性别\",\"formCode\":\"SEX\",\"formCname\":\"性别\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Select\",\"sort\":null,\"dataClass\":\"性别\",\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"],\"styleJson\":{\"formName\":\"性别\",\"dataClass\":\"性别\",\"dataType\":\"Select\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}},\"queryType\":\"api\"},{\"formName\":\"身高\",\"formCode\":\"HEIGHT\",\"formCname\":\"身高\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Input\",\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"styleJson\":{\"formName\":\"身高\",\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}},{\"formName\":\"紧急联系人\",\"formCode\":\"EMERGENCY_CONTACT\",\"formCname\":\"紧急联系人\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Input\",\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"styleJson\":{\"formName\":\"紧急联系人\",\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":{\"color\":\"rgb(0,0,0)\"},\"rules\":[{\"required\":null}],\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}},{\"formName\":\"与紧急联系人的关系\",\"formCode\":\"ECONTACT_RELACTION\",\"formCname\":\"与紧急联系人的关系\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Input\",\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"styleJson\":{\"formName\":\"与紧急联系人的关系\",\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":{\"color\":\"rgb(0,0,0)\"},\"rules\":[{\"required\":null}],\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}},{\"formName\":\"紧急联系人电话\",\"formCode\":\"ECONTACT_PHONE\",\"formCname\":\"紧急联系人电话\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Input\",\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"styleJson\":{\"formName\":\"紧急联系人电话\",\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":{\"color\":\"rgb(0,0,0)\"},\"rules\":[{\"required\":null}]}},{\"formName\":\"详细通讯地址\",\"formCode\":\"COMM_ADDR\",\"formCname\":\"详细通讯地址\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Input\",\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"styleJson\":{\"formName\":\"详细通讯地址\",\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}},{\"formName\":\"人员性质\",\"formCode\":\"PERSON_DOC_STATE\",\"formCname\":\"人员性质\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Input\",\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"styleJson\":{}},{\"formName\":\"入职方式\",\"formCode\":\"EMPLOYMENT_SOURE\",\"formCname\":\"入职方式\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Select\",\"sort\":null,\"dataClass\":\"入职方式\",\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"],\"styleJson\":{\"formName\":\"入职方式\",\"dataClass\":\"入职方式\",\"dataType\":\"Select\",\"labelHide\":false,\"labelStyle\":{\"color\":\"rgb(0,0,0)\"},\"rules\":[{\"required\":true}]},\"queryType\":\"api\"},{\"formName\":\"用工类型\",\"formCode\":\"USER_TYPE\",\"formCname\":\"用工类型\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Select\",\"sort\":null,\"dataClass\":\"用工类型\",\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"],\"styleJson\":{},\"queryType\":\"api\"},{\"formName\":\"执业类型\",\"formCode\":\"TECHNOLOGY_TYPE\",\"formCname\":\"执业类型\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Select\",\"sort\":null,\"dataClass\":\"职称类型\",\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"],\"styleJson\":{\"formName\":\"执业类型\",\"dataClass\":\"职称类型\",\"dataType\":\"Select\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"},\"defaultValue\":null}},\"queryType\":\"api\"},{\"formName\":\"职称专业\",\"formCode\":\"TECH_POST_PROFESSION\",\"formCname\":\"职称专业\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Input\",\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"styleJson\":{\"formName\":\"职称专业\",\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":{\"color\":\"rgb(0,0,0)\"},\"rules\":[{\"required\":null}]}},{\"formName\":\"聘任职称评定单位\",\"formCode\":\"EMPLOYMENT_UNIT\",\"formCname\":\"聘任职称评定单位\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Input\",\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"styleJson\":{\"formName\":\"聘任职称评定单位\",\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":{\"color\":\"rgb(0,0,0)\"},\"rules\":[{\"required\":null}]}},{\"formName\":\"职称名称\",\"formCode\":\"ACADEMIC_POST\",\"formCname\":\"职称名称\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Select\",\"sort\":null,\"dataClass\":\"DataClass_By_Level_Names\",\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"styleJson\":{\"formName\":\"职称名称\",\"dataClass\":\"DataClass_By_Level_Names\",\"dataType\":\"Select\",\"labelStyle\":{\"color\":\"rgb(0,0,0)\"}},\"queryType\":\"api\"},{\"formName\":\"职称评定日期\",\"formCode\":\"TECH_CERTIFICE_TIME\",\"formCname\":\"职称评定日期\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"DatePicker\",\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"disabled\",\"picker\",\"allowClear\"],\"styleJson\":{\"formName\":\"职称评定日期\",\"dataType\":\"DatePicker\",\"labelHide\":false,\"labelStyle\":{\"color\":\"rgb(0,0,0)\"}}},{\"formName\":\"参加工作日期\",\"formCode\":\"WORK_TIME\",\"formCname\":\"参加工作日期\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"DatePicker\",\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"disabled\",\"picker\",\"allowClear\"],\"styleJson\":{\"formName\":\"参加工作日期\",\"dataType\":\"DatePicker\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}},{\"formName\":\"退休日期\",\"formCode\":\"RETIRE_TIME\",\"formCname\":\"退休日期\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"DatePicker\",\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"disabled\",\"picker\",\"allowClear\"],\"styleJson\":{\"formName\":\"退休日期\",\"dataType\":\"DatePicker\",\"labelStyle\":{\"color\":\"rgb(0,0,0)\"},\"rules\":[{\"required\":true}]}},{\"formName\":\"工龄\",\"formCode\":\"LENGTH_SERVICE\",\"formCname\":\"工龄\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Input\",\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"styleJson\":{\"formName\":\"工龄\",\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}},{\"formName\":\"年龄\",\"formCode\":\"AGE\",\"formCname\":\"年龄\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Input\",\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"styleJson\":{}},{\"formName\":\"籍贯\",\"formCode\":\"NATIVE_PLACE\",\"formCname\":\"籍贯\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Cascader\",\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"styleJson\":{\"formName\":\"籍贯\",\"dataType\":\"Cascader\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}},{\"formName\":\"户籍所在地\",\"formCode\":\"DOMICILE_PLACE\",\"formCname\":\"户籍所在地\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Cascader\",\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"styleJson\":{\"formName\":\"户籍所在地\",\"dataType\":\"Cascader\",\"labelHide\":false,\"labelStyle\":{\"color\":\"rgb(0,0,0)\"},\"rules\":[{\"required\":false}],\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}},{\"formName\":\"现居住地\",\"formCode\":\"CURRENT_ADDRESS\",\"formCname\":\"现居住地\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Input\",\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"styleJson\":{\"formName\":\"现居住地\",\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":{\"color\":\"rgb(0,0,0)\"},\"rules\":[{\"required\":null}],\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}},{\"formName\":\"最高学历\",\"formCode\":\"HIGHEST_DEGREE\",\"formCname\":\"最高学历\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Select\",\"sort\":null,\"dataClass\":\"最高学历\",\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"],\"styleJson\":{\"formName\":\"最高学历\",\"dataClass\":\"最高学历\",\"dataType\":\"Select\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}},\"queryType\":\"api\"},{\"formName\":\"毕业院校\",\"formCode\":\"GRADUATE_SCHOOL\",\"formCname\":\"毕业院校\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Input\",\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"styleJson\":{\"formName\":\"毕业院校\",\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":{\"color\":\"rgb(0,0,0)\"},\"rules\":[{\"required\":null}]}},{\"formName\":\"毕业日期\",\"formCode\":\"GRADUATE_DATE\",\"formCname\":\"毕业日期\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"DatePicker\",\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"disabled\",\"picker\",\"allowClear\"],\"styleJson\":{\"formName\":\"毕业日期\",\"dataType\":\"DatePicker\",\"labelHide\":false,\"labelStyle\":{\"color\":\"rgb(0,0,0)\"},\"rules\":[{\"required\":null}]}},{\"formName\":\"毕业专业\",\"formCode\":\"PROFESSION\",\"formCname\":\"毕业专业\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Input\",\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"styleJson\":{\"formName\":\"毕业专业\",\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":{\"color\":\"rgb(0,0,0)\"},\"rules\":[{\"required\":null}],\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}},{\"formName\":\"英语等级\",\"formCode\":\"ENGLISH_RANK\",\"formCname\":\"英语等级\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Select\",\"sort\":null,\"dataClass\":\"英语级别\",\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"styleJson\":{\"formName\":\"英语等级\",\"dataClass\":\"英语级别\",\"dataType\":\"Select\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}},\"queryType\":\"api\"},{\"formName\":\"婚姻状况\",\"formCode\":\"MARITAL_STATUS\",\"formCname\":\"婚姻状况\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Radio\",\"sort\":null,\"dataClass\":\"婚姻状况\",\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"optionType\",\"styleFlex\",\"disabled\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"],\"styleJson\":{\"formName\":\"婚姻状况\",\"dataClass\":\"婚姻状况\",\"dataType\":\"Radio\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}},\"queryType\":\"api\"},{\"formName\":\"颜色视觉障碍\",\"formCode\":\"COLOR_DEFICIENCY\",\"formCname\":\"颜色视觉障碍\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Select\",\"sort\":null,\"dataClass\":\"H810000-COLOR_DEFICIENCY\",\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"optionType\",\"styleFlex\",\"disabled\",\"queryType\",\"defaultValue\",\"isDataTypeToPerson\"],\"styleJson\":{\"formName\":\"颜色视觉障碍\",\"dataClass\":\"H810000-COLOR_DEFICIENCY\",\"dataType\":\"Select\",\"labelStyle\":{\"color\":\"rgb(0,0,0)\"},\"rules\":[{\"required\":true}],\"widgetProps\":{\"style\":{\"color\":\"rgb(0,0,0)\"}}},\"queryType\":\"api\"},{\"formName\":\"视力\",\"formCode\":\"EYESIGHT_RIGHT\",\"formCname\":\"视力\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Input\",\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"styleJson\":{}},{\"formName\":\"视力（左/右）\",\"formCode\":\"EYESIGHT_LEFT\",\"formCname\":\"视力（左/右）\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Input\",\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"styleJson\":{\"formName\":\"视力（左/右）\",\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":{\"color\":\"rgb(0,0,0)\"},\"rules\":[{\"required\":null}]}},{\"formName\":\"专业特长\",\"formCode\":\"PROFESSION_EXPERTISE\",\"formCname\":\"专业特长\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Input\",\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"styleJson\":{\"formName\":\"专业特长\",\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":{\"color\":\"rgb(0,0,0)\"},\"rules\":[{\"required\":null}]}},{\"formName\":\"联系方式\",\"formCode\":\"PHONE\",\"formCname\":\"联系方式\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Input\",\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"styleJson\":{\"formName\":\"联系方式\",\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}},{\"formName\":\"邮箱\",\"formCode\":\"E_MAIL\",\"formCname\":\"邮箱\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Input\",\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"styleJson\":{\"formName\":\"邮箱\",\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":{\"color\":\"rgb(0,0,0)\"},\"rules\":[{\"required\":null}]}},{\"formName\":\"办公电话\",\"formCode\":\"OFFICE_PHONE\",\"formCname\":\"办公电话\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Input\",\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"styleJson\":{\"formName\":\"办公电话\",\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":{\"color\":\"rgb(0,0,0)\"},\"rules\":[{\"required\":null}],\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}},{\"formName\":\"政治面貌\",\"formCode\":\"POLITICIAN\",\"formCname\":\"政治面貌\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Select\",\"sort\":null,\"dataClass\":\"政治面貌\",\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"],\"styleJson\":{\"formName\":\"政治面貌\",\"dataClass\":\"政治面貌\",\"dataType\":\"Select\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}},\"queryType\":\"api\"},{\"formName\":\"最高学位\",\"formCode\":\"HIGHEST_DIPLOMA\",\"formCname\":\"最高学位\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Select\",\"sort\":null,\"dataClass\":\"最高学位\",\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"],\"styleJson\":{\"formName\":\"最高学位\",\"dataClass\":\"最高学位\",\"dataType\":\"Select\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}},\"queryType\":\"api\"},{\"formName\":\"岗位类别\",\"formCode\":\"GWLB79071\",\"formCname\":\"岗位类别\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Select\",\"sort\":null,\"dataClass\":\"岗位类别\",\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"styleJson\":{},\"queryType\":\"api\"},{\"formName\":\"参加培训情况\",\"formCode\":\"CJPXQK82201\",\"formCname\":\"参加培训情况\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Input\",\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"styleJson\":{}},{\"formName\":\"来院日期\",\"formCode\":\"IN_HOSPITAL_TIME\",\"formCname\":\"来院日期\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"DatePicker\",\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"disabled\",\"picker\",\"allowClear\"],\"styleJson\":{\"formName\":\"来院日期\",\"dataType\":\"DatePicker\",\"labelHide\":false,\"labelStyle\":{\"color\":\"rgb(0,0,0)\"}}},{\"formName\":\"离院日期\",\"formCode\":\"OUT_HOSPITAL_TIME\",\"formCname\":\"离院日期\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"DatePicker\",\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"disabled\",\"picker\",\"allowClear\"],\"styleJson\":{\"formName\":\"离院日期\",\"dataType\":\"DatePicker\",\"labelHide\":false,\"labelStyle\":{\"color\":\"rgb(0,0,0)\"},\"rules\":[{\"required\":false}]}},{\"formName\":\"院龄\",\"formCode\":\"LENGTH_HOSPITAL\",\"formCname\":\"院龄\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Input\",\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"styleJson\":{\"formName\":\"院龄\",\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}},{\"formName\":\"来科日期\",\"formCode\":\"IN_LAB_TIME\",\"formCname\":\"来科日期\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"DatePicker\",\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"disabled\",\"picker\",\"allowClear\"],\"styleJson\":{\"formName\":\"来科日期\",\"dataType\":\"DatePicker\",\"labelHide\":false,\"labelStyle\":{\"color\":\"rgb(0,0,0)\"},\"rules\":[{\"required\":true}]}},{\"formName\":\"离科日期\",\"formCode\":\"OUT_LAB_TIME\",\"formCname\":\"离科日期\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"DatePicker\",\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"disabled\",\"picker\",\"allowClear\"],\"styleJson\":{\"formName\":\"离科日期\",\"dataType\":\"DatePicker\",\"labelHide\":false,\"labelStyle\":{\"color\":\"rgb(0,0,0)\"}}},{\"formName\":\"科龄\",\"formCode\":\"LENGTH_LAB\",\"formCname\":\"科龄\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Input\",\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"styleJson\":{\"formName\":\"科龄\",\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":{\"color\":\"rgb(0,0,0)\"},\"rules\":[{\"required\":true}]}},{\"formName\":\"新字段\",\"formCode\":\"XZD35566\",\"formCname\":\"新字段\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":null,\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"styleJson\":{}},{\"formName\":\"健康状况\",\"formCode\":\"HEALTH\",\"formCname\":\"健康状况\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Select\",\"sort\":null,\"dataClass\":\"健康状况\",\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"],\"styleJson\":{\"formName\":\"健康状况\",\"dataClass\":\"健康状况\",\"dataType\":\"Select\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}]},\"queryType\":\"api\"},{\"formName\":\"第二新字段\",\"formCode\":\"DEXZD91452\",\"formCname\":\"第二新字段\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":null,\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"styleJson\":{}},{\"formName\":\"第三字段\",\"formCode\":\"DSZD27003\",\"formCname\":\"第三字段\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":null,\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"styleJson\":{}},{\"formName\":\"现从事岗位\",\"formCode\":\"XCSGW70984\",\"formCname\":\"现从事岗位\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Select\",\"sort\":null,\"dataClass\":\"现从事岗位\",\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"styleJson\":{},\"queryType\":\"api\"},{\"formName\":\"证件类型\",\"formCode\":\"CARD_TYPE\",\"formCname\":\"证件类型\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Select\",\"sort\":null,\"dataClass\":\"证件类型\",\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"],\"styleJson\":{\"formName\":\"证件类型\",\"dataClass\":\"证件类型\",\"dataType\":\"Select\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}},\"queryType\":\"api\"},{\"formName\":\"职称级别\",\"formCode\":\"TECH_POST\",\"formCname\":\"职称级别\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Select\",\"sort\":null,\"dataClass\":\"职称级别\",\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"],\"styleJson\":{\"formName\":\"职称级别\",\"dataClass\":\"职称级别\",\"dataType\":\"Select\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}},\"queryType\":\"api\"},{\"formName\":\"最新字段\",\"formCode\":\"ZXZD69033\",\"formCname\":\"最新字段\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":null,\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"styleJson\":{}},{\"formName\":\"最新字段\",\"formCode\":\"ZXZD62529\",\"formCname\":\"最新字段\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":null,\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"styleJson\":{}},{\"formName\":\"备案实验室\",\"formCode\":\"BASYS55209\",\"formCname\":\"备案实验室\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Select\",\"sort\":null,\"dataClass\":\"DataClass_By_Smbl_Lab\",\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"styleJson\":{},\"queryType\":\"api\"},{\"formName\":\"职务\",\"formCode\":\"DUTIES\",\"formCname\":\"职务\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Select\",\"sort\":null,\"dataClass\":\"职务\",\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"],\"styleJson\":{\"formName\":\"职务\",\"dataClass\":\"职务\",\"dataType\":\"Select\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}},\"queryType\":\"api\"},{\"formName\":\"最新字段\",\"formCode\":\"ZXZD59943\",\"formCname\":\"最新字段\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":null,\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"styleJson\":{}},{\"formName\":\"是否外单位科研合作人员\",\"formCode\":\"SFWDWKYHZRY39890\",\"formCname\":\"是否外单位科研合作人员\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Radio\",\"sort\":null,\"dataClass\":\"是否\",\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"optionType\",\"styleFlex\",\"disabled\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"],\"styleJson\":{},\"queryType\":\"api\"},{\"formName\":\"成绩（分）\",\"formCode\":\"ENGLISH_RANK_SCORE\",\"formCname\":\"成绩（分）\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"InputNumber\",\"sort\":null,\"dataClass\":\"H810000-ENGLISH_RANK_SCORE\",\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"],\"styleJson\":{},\"queryType\":\"enum\"},{\"formName\":\"测试字段1\",\"formCode\":\"CSZD180389\",\"formCname\":\"测试字段1\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":null,\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"styleJson\":{}},{\"formName\":\"备注\",\"formCode\":\"BZ63459\",\"formCname\":\"备注\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Input\",\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"styleJson\":{}},{\"formName\":\"有无子女\",\"formCode\":\"CHILDREN_CONDITION\",\"formCname\":\"有无子女\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Radio\",\"sort\":null,\"dataClass\":\"有无子女\",\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"],\"styleJson\":{\"formName\":\"有无子女\",\"dataClass\":\"有无子女\",\"dataType\":\"Radio\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}},\"queryType\":\"api\"},{\"formName\":\"新字段\",\"formCode\":\"XZD61188\",\"formCname\":\"新字段\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":null,\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"minRows\",\"maxRows\",\"variant\",\"disabled\",\"showCount\"],\"styleJson\":{}},{\"formName\":\"第二新字段\",\"formCode\":\"DEXZD63195\",\"formCname\":\"第二新字段\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":null,\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"minRows\",\"maxRows\",\"variant\",\"disabled\",\"showCount\"],\"styleJson\":{}},{\"formName\":\"测试字段1\",\"formCode\":\"CSZD144599\",\"formCname\":\"测试字段1\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":null,\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"min\",\"max\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"controls\"],\"styleJson\":{}},{\"formName\":\"测试字段2\",\"formCode\":\"CSZD296678\",\"formCname\":\"测试字段2\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":null,\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"min\",\"max\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"controls\"],\"styleJson\":{}},{\"formName\":\"培训合格证号\",\"formCode\":\"PXHGZH48698\",\"formCname\":\"培训合格证号\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Input\",\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"styleJson\":{}},{\"formName\":\"新字段\",\"formCode\":\"XZD45449\",\"formCname\":\"新字段\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Select\",\"sort\":null,\"dataClass\":\"参会人身份\",\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"],\"styleJson\":{},\"queryType\":\"api\"},{\"formName\":\"培训合格证书\",\"formCode\":\"PXHGZS25179\",\"formCname\":\"培训合格证书\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Upload\",\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"multiple\",\"listType\",\"accept\"],\"styleJson\":{}},{\"formName\":\"数值\",\"formCode\":\"SZ52717\",\"formCname\":\"数值\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"InputNumber\",\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"min\",\"max\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"controls\"],\"styleJson\":{}},{\"formName\":\"基本信息项\",\"formCode\":\"JBXXX75570\",\"formCname\":\"基本信息项\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Input\",\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"styleJson\":{}},{\"formName\":\"工作管理类型\",\"formCode\":\"GZGLLX49535\",\"formCname\":\"工作管理类型\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Input\",\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"styleJson\":{}},{\"formName\":\"基本信息\",\"formCode\":\"JBXX70654\",\"formCname\":\"基本信息\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Input\",\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"styleJson\":{}},{\"formName\":\"颜色选择器\",\"formCode\":\"YSXZQ25786\",\"formCname\":\"颜色选择器\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"ColorPicker\",\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"disabled\",\"showText\"],\"styleJson\":{}},{\"formName\":\"岗位类别\",\"formCode\":\"GWLB57036\",\"formCname\":\"岗位类别\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Select\",\"sort\":null,\"dataClass\":\"岗位类别\",\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"],\"styleJson\":{},\"queryType\":\"api\"},{\"formName\":\"英语级别/成绩(分)\",\"formCode\":\"YYJB50394\",\"formCname\":\"英语级别/成绩(分)\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Select\",\"sort\":null,\"dataClass\":\"英语级别\",\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"],\"styleJson\":{},\"queryType\":\"api\"},{\"formName\":\"添增字段\",\"formCode\":\"TZZD85241\",\"formCname\":\"添增字段\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":null,\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"],\"styleJson\":{}},{\"formName\":\"添增字段\",\"formCode\":\"TZZD19249\",\"formCname\":\"添增字段\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Select\",\"sort\":null,\"dataClass\":\"报告单状态\",\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"],\"styleJson\":{},\"queryType\":\"api\"},{\"formName\":\"全新下拉字段\",\"formCode\":\"QXXLZD58888\",\"formCname\":\"全新下拉字段\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Select\",\"sort\":null,\"dataClass\":\"班次状态\",\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"],\"styleJson\":{},\"queryType\":\"api\"},{\"formName\":\"新增字段0613\",\"formCode\":\"XZZD061343005\",\"formCname\":\"新增字段0613\",\"ifShow\":true,\"titleColor\":\"#000000\",\"titleShow\":true,\"titleSize\":13,\"titleStyle\":\"0\",\"titleBackground\":null,\"titleAlign\":\"3\",\"contentLine\":1,\"contentMaxLine\":2,\"contentHeightClass\":1,\"contentHeightRatio\":null,\"contentAlign\":\"1\",\"contentColor\":\"#000000\",\"contentFontSize\":13,\"contentStyle\":\"0\",\"contentBackground\":null,\"titleAndContentType\":\"1\",\"contentEnlarge\":null,\"ifRequired\":null,\"replaceField\":null,\"onlyRead\":\"1\",\"default\":null,\"resetContent\":null,\"dataType\":\"Input\",\"sort\":null,\"dataClass\":null,\"allowMaintainDropDownData\":false,\"editeState\":false,\"formDesc\":null,\"suffix\":null,\"unitFlag\":false,\"isClinaField\":false,\"assistId\":null,\"assistClass\":null,\"assistClassName\":null,\"disabled\":false,\"formId\":null,\"fieldClass\":\"基本信息\",\"readonlyPropslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"styleJson\":{}}]}","MODULE_ID":"H81","REMARK":null,"FIRST_RTIME":"2025-05-22T17:52:27","LAST_MPERSON":"实验室管理导入数据","FORM_JSON":"{\"layout\":{\"type\":\"fixed\",\"widthPercent\":100,\"labelCol\":3,\"verticalGap\":16,\"size\":\"default\",\"style\":{\"paddingTop\":4,\"paddingBottom\":4},\"showRowNum\":false,\"labelLayout\":\"horizontal\",\"labelWrap\":true},\"rows\":[{\"rowId\":\"row_703132c2\",\"cols\":[{\"colId\":\"col_98054ad7\",\"span\":24,\"items\":[{\"itemId\":\"item_70314472\",\"group\":\"基本\",\"formId\":\"title\",\"icon\":\"iconbiaotilan\",\"formName\":\"基本信息\",\"isForm\":false,\"dataType\":\"XTitle\",\"wigetProps\":{\"button\":false,\"groupKey\":\"\"},\"propslist\":[\"button\",\"buttonSize\",\"groupKey\"],\"labelStyle\":{\"background\":\"rgb(241,247,255)\"}}]}]},{\"rowId\":\"row_4489546d\",\"rowType\":\"fixed\",\"cols\":[{\"colId\":\"col_4489e428\",\"span\":8,\"items\":[{\"itemId\":\"item_2962ce6a\",\"wigetType\":\"fixed\",\"formCname\":\"姓名\",\"formCode\":\"USER_NAME\",\"isForm\":true,\"labelCols\":3,\"widgetType\":\"fixed\"},{\"itemId\":\"item_42883768\",\"wigetType\":\"fixed\",\"formCname\":\"工号\",\"formCode\":\"HIS_ID\",\"isForm\":true,\"labelCols\":3,\"widgetType\":\"fixed\"},{\"itemId\":\"item_6212e4d6\",\"wigetType\":\"fixed\",\"formCname\":\"民族\",\"formCode\":\"NATION\",\"isForm\":true,\"labelCols\":3,\"queryType\":\"api\",\"widgetType\":\"fixed\"},{\"itemId\":\"item_7151dcb2\",\"wigetType\":\"fixed\",\"formCname\":\"证件类型\",\"formCode\":\"CARD_TYPE\",\"isForm\":true,\"labelCols\":3,\"queryType\":\"api\",\"widgetType\":\"fixed\"},{\"itemId\":\"item_32478339\",\"wigetType\":\"fixed\",\"formCname\":\"出生年月\",\"formCode\":\"BIRTHDAY\",\"isForm\":true,\"labelCols\":3,\"widgetType\":\"fixed\"}]},{\"colId\":\"col_44892e0a\",\"span\":8,\"items\":[{\"itemId\":\"item_139462f3\",\"wigetType\":\"fixed\",\"formCname\":\"登录ID\",\"formCode\":\"LOGID\",\"isForm\":true,\"labelCols\":3,\"widgetType\":\"fixed\"},{\"itemId\":\"item_59761567\",\"group\":\"基本\",\"formId\":\"danhangwenben\",\"icon\":\"icondanhangwenben\",\"isForm\":true,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"formCode\":\"PGROUP_ID\",\"queryType\":\"api\",\"wigetType\":\"fixed\",\"widgetType\":\"fixed\"},{\"itemId\":\"item_4129cfb5\",\"group\":\"基本\",\"formId\":\"danhangwenben\",\"icon\":\"icondanhangwenben\",\"isForm\":true,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"formCode\":\"ID_CARD\",\"wigetType\":\"fixed\",\"widgetType\":\"fixed\"},{\"itemId\":\"item_320126b3\",\"wigetType\":\"fixed\",\"formCname\":\"政治面貌\",\"formCode\":\"POLITICIAN\",\"isForm\":true,\"labelCols\":3,\"queryType\":\"api\",\"widgetType\":\"fixed\"},{\"itemId\":\"item_09000125\",\"wigetType\":\"fixed\",\"formCname\":\"性别\",\"formCode\":\"SEX\",\"isForm\":true,\"labelCols\":3,\"groupItems\":{\"itemId\":\"item_8296cde6\",\"connector\":\"/\"},\"queryType\":\"api\",\"widgetType\":\"fixed\",\"groupItem\":{\"itemId\":\"item_8296cde6\",\"wigetType\":\"fixed\",\"formCname\":\"年龄\",\"formCode\":\"AGE\",\"isForm\":true,\"labelCols\":3,\"widgetType\":\"fixed\",\"formName\":\"年龄\",\"dataType\":\"Input\",\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"labelHide\":false,\"rules\":[{\"required\":null}],\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"connector\":\"/\"}}]},{\"colId\":\"col_44899c0b\",\"span\":8,\"items\":[{\"itemId\":\"item_3431c44d\",\"group\":\"基本\",\"formId\":\"tupianfujian\",\"icon\":\"icontouxiang\",\"formName\":\"\",\"formNameHide\":true,\"dataType\":\"ImgUpload\",\"isForm\":true,\"wigetProps\":{\"width\":180,\"height\":220,\"accept\":\".png,.jpg,.jpeg\",\"style\":{\"justifyContent\":\"center\",\"textAlign\":\"center\",\"display\":\"flex\"}},\"propslist\":[\"dataType\",\"required\",\"width\",\"height\"],\"formCode\":\"USER_IMG\",\"labelStyle\":{\"justifyContent\":\"center\",\"textAlign\":\"center\",\"display\":\"flex\"},\"wigetType\":\"\",\"widgetType\":\"\"}]}],\"style\":{\"paddingTop\":0,\"paddingLeft\":0}},{\"rowId\":\"row_8197b441\",\"rowType\":\"fixed\",\"cols\":[{\"colId\":\"col_81970997\",\"span\":8,\"items\":[{\"itemId\":\"item_6155443a\",\"group\":\"基本\",\"formId\":\"jilianxuanze\",\"icon\":\"iconxialakuang\",\"isForm\":true,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\"],\"formCode\":\"NATIVE_PLACE\",\"wigetType\":\"fixed\",\"widgetType\":\"fixed\"}]},{\"colId\":\"col_8197a622\",\"span\":8,\"items\":[{\"itemId\":\"item_0917a793\",\"group\":\"基本\",\"formId\":\"jilianxuanze\",\"icon\":\"iconxialakuang\",\"isForm\":true,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\"],\"formCode\":\"DOMICILE_PLACE\",\"wigetType\":\"fixed\",\"widgetType\":\"fixed\"}]},{\"colId\":\"col_81972abd\",\"span\":8,\"items\":[{\"itemId\":\"item_6403d7e1\",\"wigetType\":\"fixed\",\"formCname\":\"现居住地\",\"formCode\":\"CURRENT_ADDRESS\",\"isForm\":true,\"labelCols\":3,\"widgetType\":\"fixed\"}]}]},{\"rowId\":\"row_5451a683\",\"rowType\":\"fixed\",\"cols\":[{\"colId\":\"col_54514c9e\",\"span\":8,\"items\":[{\"itemId\":\"item_7247fc33\",\"wigetType\":\"fixed\",\"formCname\":\"最高学历\",\"formCode\":\"HIGHEST_DEGREE\",\"isForm\":true,\"labelCols\":3,\"queryType\":\"api\",\"widgetType\":\"fixed\"}]},{\"colId\":\"col_5451fe44\",\"span\":8,\"items\":[{\"itemId\":\"item_0015dd2a\",\"wigetType\":\"fixed\",\"formCname\":\"最高学位\",\"formCode\":\"HIGHEST_DIPLOMA\",\"isForm\":true,\"labelCols\":3,\"queryType\":\"api\",\"widgetType\":\"fixed\"}]},{\"colId\":\"col_545176f0\",\"span\":8,\"items\":[{\"itemId\":\"item_83732b44\",\"wigetType\":\"fixed\",\"formCname\":\"毕业院校\",\"formCode\":\"GRADUATE_SCHOOL\",\"isForm\":true,\"labelCols\":3,\"widgetType\":\"fixed\"}]}]},{\"rowId\":\"row_792943be\",\"rowType\":\"fixed\",\"cols\":[{\"colId\":\"col_7929c9ea\",\"span\":8,\"items\":[{\"itemId\":\"item_56524f1d\",\"wigetType\":\"fixed\",\"formCname\":\"毕业日期\",\"formCode\":\"GRADUATE_DATE\",\"isForm\":true,\"labelCols\":3,\"picker\":\"date\",\"widgetType\":\"fixed\"}]},{\"colId\":\"col_792978e6\",\"span\":8,\"items\":[{\"itemId\":\"item_8938132c\",\"wigetType\":\"fixed\",\"formCname\":\"毕业专业\",\"formCode\":\"PROFESSION\",\"isForm\":true,\"labelCols\":3,\"widgetType\":\"fixed\"}]},{\"colId\":\"col_792902f7\",\"span\":8,\"items\":[{\"itemId\":\"item_0283dc80\",\"wigetType\":\"fixed\",\"formCname\":\"英语等级\",\"formCode\":\"ENGLISH_RANK\",\"isForm\":true,\"labelCols\":3,\"queryType\":\"api\",\"groupItems\":{\"itemId\":\"item_3241dad8\"},\"widgetType\":\"fixed\",\"groupItem\":{\"itemId\":\"item_3241dad8\",\"wigetType\":\"fixed\",\"formCname\":\"英语等级成绩\",\"formCode\":\"ENGLISH_RANK_SCORE\",\"isForm\":true,\"labelCols\":3,\"groupItems\":{\"connector\":\"/\"},\"queryType\":\"api\",\"widgetType\":\"fixed\",\"formName\":\"成绩（分）\",\"dataClass\":\"H810000-ENGLISH_RANK_SCORE\",\"dataType\":\"InputNumber\",\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"labelHide\":false,\"rules\":[{\"required\":null}],\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"connector\":\"/\"}}]}]},{\"rowId\":\"row_9975fc08\",\"rowType\":\"fixed\",\"cols\":[{\"colId\":\"col_9975268c\",\"span\":8,\"items\":[{\"itemId\":\"item_8402065b\",\"group\":\"基本\",\"formId\":\"danxuankuang\",\"icon\":\"icondanxuan\",\"isForm\":true,\"propslist\":[\"dataType\",\"required\",\"optionType\",\"styleFlex\",\"disabled\",\"queryType\",\"dataClass\",\"defaultValue\"],\"formCode\":\"MARITAL_STATUS\",\"queryType\":\"api\",\"wigetType\":\"fixed\",\"widgetType\":\"fixed\"}]},{\"colId\":\"col_9975783b\",\"span\":8,\"items\":[{\"itemId\":\"item_65602032\",\"group\":\"基本\",\"formId\":\"danxuankuang\",\"icon\":\"icondanxuan\",\"isForm\":true,\"propslist\":[\"dataType\",\"required\",\"optionType\",\"styleFlex\",\"disabled\",\"queryType\",\"dataClass\",\"defaultValue\"],\"formCode\":\"CHILDREN_CONDITION\",\"queryType\":\"api\",\"wigetType\":\"fixed\",\"widgetType\":\"fixed\"}]},{\"colId\":\"col_9975bf05\",\"span\":8,\"items\":[{\"itemId\":\"item_6949a68a\",\"group\":\"基本\",\"formId\":\"danxuankuang\",\"icon\":\"icondanxuan\",\"isForm\":true,\"propslist\":[\"dataType\",\"required\",\"optionType\",\"styleFlex\",\"disabled\",\"queryType\",\"dataClass\",\"defaultValue\"],\"formCode\":\"COLOR_DEFICIENCY\",\"wigetType\":\"fixed\",\"widgetType\":\"fixed\"}]}]},{\"rowId\":\"row_1288c597\",\"rowType\":\"fixed\",\"cols\":[{\"colId\":\"col_12882354\",\"span\":8,\"items\":[{\"itemId\":\"item_94162c7e\",\"wigetType\":\"fixed\",\"formCname\":\"身高\",\"formCode\":\"HEIGHT\",\"isForm\":true,\"labelCols\":3,\"widgetType\":\"fixed\"}]},{\"colId\":\"col_128857ea\",\"span\":8,\"items\":[{\"itemId\":\"item_30368955\",\"wigetType\":\"fixed\",\"formCname\":\"视力\",\"formCode\":\"EYESIGHT_LEFT\",\"isForm\":true,\"labelCols\":3,\"groupItems\":{\"itemId\":\"item_30362876\",\"connector\":\"/\"},\"widgetType\":\"fixed\",\"groupItem\":{\"itemId\":\"item_30362876\",\"wigetType\":\"fixed\",\"formCname\":\"视力\",\"formCode\":\"EYESIGHT_RIGHT\",\"isForm\":true,\"labelCols\":3,\"widgetType\":\"fixed\",\"formName\":\"\",\"dataType\":\"Input\",\"labelStyle\":null,\"labelHide\":false,\"rules\":[{\"required\":null}],\"connector\":\"/\"}}]},{\"colId\":\"col_12881c8d\",\"span\":8,\"items\":[{\"itemId\":\"item_57810316\",\"wigetType\":\"fixed\",\"formCname\":\"健康状况\",\"formCode\":\"HEALTH\",\"isForm\":true,\"labelCols\":3,\"queryType\":\"api\",\"widgetType\":\"fixed\"}]}]},{\"rowId\":\"row_6000ae73\",\"rowType\":\"fixed\",\"cols\":[{\"colId\":\"col_5999c0a3\",\"span\":24,\"items\":[{\"itemId\":\"item_25860758\",\"wigetType\":\"fixed\",\"formCname\":\"专业特长\",\"formCode\":\"PROFESSION_EXPERTISE\",\"isForm\":true,\"labelCols\":3,\"widgetType\":\"fixed\"}]}]},{\"rowId\":\"row_872901bb\",\"cols\":[{\"colId\":\"col_87295e8e\",\"span\":24,\"items\":[{\"itemId\":\"item_8729c365\",\"group\":\"基本\",\"formId\":\"title\",\"icon\":\"iconbiaotilan\",\"formName\":\"联系方式\",\"isForm\":false,\"dataType\":\"XTitle\",\"wigetProps\":{\"button\":false,\"groupKey\":\"\"},\"propslist\":[\"button\",\"buttonSize\",\"groupKey\"],\"labelStyle\":{\"background\":\"rgb(241,247,255)\"}}]}]},{\"rowId\":\"row_22044871\",\"rowType\":\"fixed\",\"cols\":[{\"colId\":\"col_22033905\",\"span\":8,\"items\":[{\"itemId\":\"item_0767e0e3\",\"group\":\"基本\",\"formId\":\"danhangwenben\",\"icon\":\"icondanhangwenben\",\"isForm\":true,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"formCode\":\"PHONE\",\"wigetType\":\"fixed\",\"widgetType\":\"fixed\"}]},{\"colId\":\"col_220388bf\",\"span\":8,\"items\":[{\"itemId\":\"item_5105c708\",\"wigetType\":\"fixed\",\"formCname\":\"邮箱\",\"formCode\":\"E_MAIL\",\"isForm\":true,\"labelCols\":3,\"widgetType\":\"fixed\"}]},{\"colId\":\"col_2203da89\",\"span\":8,\"items\":[{\"itemId\":\"item_51265c4b\",\"wigetType\":\"fixed\",\"formCname\":\"办公电话\",\"formCode\":\"OFFICE_PHONE\",\"isForm\":true,\"labelCols\":3,\"widgetType\":\"fixed\"}]}]},{\"rowId\":\"row_44289f89\",\"rowType\":\"fixed\",\"cols\":[{\"colId\":\"col_44282d91\",\"span\":8,\"items\":[{\"itemId\":\"item_7569b6a4\",\"wigetType\":\"fixed\",\"formCname\":\"紧急联系人\",\"formCode\":\"EMERGENCY_CONTACT\",\"isForm\":true,\"labelCols\":3,\"widgetType\":\"fixed\"}]},{\"colId\":\"col_442871d2\",\"span\":8,\"items\":[{\"itemId\":\"item_46767669\",\"wigetType\":\"fixed\",\"formCname\":\"与紧急联系人的关系\",\"formCode\":\"ECONTACT_RELACTION\",\"isForm\":true,\"labelCols\":3,\"widgetType\":\"fixed\"}]},{\"colId\":\"col_44281ab4\",\"span\":8,\"items\":[{\"itemId\":\"item_7830c240\",\"wigetType\":\"fixed\",\"formCname\":\"紧急联系人电话\",\"formCode\":\"ECONTACT_PHONE\",\"isForm\":true,\"labelCols\":3,\"widgetType\":\"fixed\"}]}]},{\"rowId\":\"row_66919741\",\"rowType\":\"fixed\",\"cols\":[{\"colId\":\"col_66917fa5\",\"span\":24,\"items\":[{\"itemId\":\"item_269625ee\",\"group\":\"基本\",\"formId\":\"danhangwenben\",\"icon\":\"icondanhangwenben\",\"isForm\":true,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"formCode\":\"COMM_ADDR\",\"wigetType\":\"fixed\",\"widgetType\":\"fixed\"}]}]},{\"rowId\":\"row_10441e18\",\"cols\":[{\"colId\":\"col_1044101c\",\"span\":24,\"items\":[{\"itemId\":\"item_1044b066\",\"group\":\"基本\",\"formId\":\"title\",\"icon\":\"iconbiaotilan\",\"formName\":\"聘用信息\",\"isForm\":false,\"dataType\":\"XTitle\",\"wigetProps\":{\"button\":false,\"groupKey\":\"\"},\"propslist\":[\"button\",\"buttonSize\",\"groupKey\"],\"labelStyle\":{\"background\":\"rgb(241,247,255)\"}}]}]},{\"rowId\":\"row_9115c670\",\"rowType\":\"fixed\",\"cols\":[{\"colId\":\"col_91159801\",\"span\":8,\"items\":[{\"itemId\":\"item_46903557\",\"wigetType\":\"fixed\",\"formCname\":\"行政职务\",\"formCode\":\"DUTIES\",\"isForm\":true,\"labelCols\":3,\"queryType\":\"api\",\"widgetType\":\"fixed\"}]},{\"colId\":\"col_9115f31e\",\"span\":8,\"items\":[{\"itemId\":\"item_1302d4a1\",\"group\":\"基本\",\"formId\":\"danhangwenben\",\"icon\":\"icondanhangwenben\",\"isForm\":true,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"formCode\":\"EMPLOYMENT_SOURE\",\"queryType\":\"api\",\"wigetType\":\"fixed\",\"widgetType\":\"fixed\"}]},{\"colId\":\"col_9115c5e9\",\"span\":8,\"items\":[]}]},{\"rowId\":\"row_6645162b\",\"cols\":[{\"colId\":\"col_66457692\",\"span\":24,\"items\":[{\"itemId\":\"item_66458985\",\"group\":\"基本\",\"formId\":\"title\",\"icon\":\"iconbiaotilan\",\"formName\":\"职称信息\",\"isForm\":false,\"dataType\":\"XTitle\",\"wigetProps\":{\"button\":true,\"groupKey\":\"row_50147784\"},\"propslist\":[\"button\",\"buttonSize\",\"groupKey\"],\"labelStyle\":{\"background\":\"rgb(241,247,255)\"}}]}]},{\"rowId\":\"row_50147784\",\"rowType\":\"fixed\",\"cols\":[{\"colId\":\"col_50141556\",\"span\":8,\"items\":[{\"itemId\":\"item_8098cda6\",\"wigetType\":\"fixed\",\"formCname\":\"职称类型\",\"formCode\":\"TECHNOLOGY_TYPE\",\"isForm\":true,\"labelCols\":3,\"queryType\":\"api\",\"widgetType\":\"fixed\"},{\"itemId\":\"item_40152a3c\",\"wigetType\":\"fixed\",\"formCname\":\"职称专业\",\"formCode\":\"TECH_POST_PROFESSION\",\"isForm\":true,\"labelCols\":3,\"widgetType\":\"fixed\"}]},{\"colId\":\"col_5014acb7\",\"span\":8,\"items\":[{\"itemId\":\"item_6728683d\",\"wigetType\":\"fixed\",\"formCname\":\"职称级别\",\"formCode\":\"TECH_POST\",\"isForm\":true,\"labelCols\":3,\"queryType\":\"api\",\"widgetType\":\"fixed\"},{\"itemId\":\"item_3260d6ac\",\"wigetType\":\"fixed\",\"formCname\":\"聘任职称评定单位\",\"formCode\":\"EMPLOYMENT_UNIT\",\"isForm\":true,\"labelCols\":3,\"widgetType\":\"fixed\"}]},{\"colId\":\"col_50144ff2\",\"span\":8,\"items\":[{\"itemId\":\"item_7651faaa\",\"wigetType\":\"fixed\",\"formCname\":\"职称名称\",\"formCode\":\"ACADEMIC_POST\",\"isForm\":true,\"labelCols\":3,\"queryType\":\"enum\",\"widgetType\":\"fixed\"},{\"itemId\":\"item_8846125d\",\"group\":\"基本\",\"formId\":\"riqi\",\"icon\":\"iconriqi\",\"isForm\":true,\"propslist\":[\"dataType\",\"required\",\"disabled\",\"picker\",\"allowClear\"],\"formCode\":\"TECH_CERTIFICE_TIME\",\"picker\":\"date\",\"wigetType\":\"fixed\",\"widgetType\":\"fixed\"}]}]},{\"rowId\":\"row_7658e9dc\",\"cols\":[{\"colId\":\"col_7658673d\",\"span\":24,\"items\":[{\"itemId\":\"item_7658162e\",\"group\":\"基本\",\"formId\":\"title\",\"icon\":\"iconbiaotilan\",\"formName\":\"工作时间\",\"isForm\":false,\"dataType\":\"XTitle\",\"wigetProps\":{\"button\":false,\"groupKey\":\"\"},\"propslist\":[\"button\",\"buttonSize\",\"groupKey\"],\"labelStyle\":{\"background\":\"rgb(241,247,255)\"}}]}]},{\"rowId\":\"row_9762ccff\",\"rowType\":\"fixed\",\"cols\":[{\"colId\":\"col_976274b2\",\"span\":8,\"items\":[{\"itemId\":\"item_4481cefc\",\"group\":\"基本\",\"formId\":\"danhangwenben\",\"icon\":\"icondanhangwenben\",\"isForm\":true,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"formCode\":\"WORK_TIME\",\"picker\":\"date\",\"wigetType\":\"fixed\",\"widgetType\":\"fixed\"}]},{\"colId\":\"col_9762e2b0\",\"span\":8,\"items\":[{\"itemId\":\"item_71571e24\",\"group\":\"基本\",\"formId\":\"danhangwenben\",\"icon\":\"icondanhangwenben\",\"isForm\":true,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"formCode\":\"RETIRE_TIME\",\"picker\":\"date\",\"wigetType\":\"fixed\",\"widgetType\":\"fixed\"}]},{\"colId\":\"col_97627c02\",\"span\":8,\"items\":[{\"itemId\":\"item_9508c577\",\"group\":\"基本\",\"formId\":\"danhangwenben\",\"icon\":\"icondanhangwenben\",\"isForm\":true,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"formCode\":\"LENGTH_SERVICE\",\"wigetType\":\"fixed\",\"widgetType\":\"fixed\"}]}]},{\"rowId\":\"row_220912d8\",\"rowType\":\"fixed\",\"cols\":[{\"colId\":\"col_220965fa\",\"span\":8,\"items\":[{\"itemId\":\"item_43173cf5\",\"group\":\"基本\",\"formId\":\"riqi\",\"icon\":\"iconriqi\",\"isForm\":true,\"propslist\":[\"dataType\",\"required\",\"disabled\",\"picker\",\"allowClear\"],\"formCode\":\"IN_HOSPITAL_TIME\",\"picker\":\"date\",\"wigetType\":\"fixed\",\"widgetType\":\"fixed\"}]},{\"colId\":\"col_22096345\",\"span\":8,\"items\":[{\"itemId\":\"item_6068bda8\",\"group\":\"基本\",\"formId\":\"riqi\",\"icon\":\"iconriqi\",\"isForm\":true,\"propslist\":[\"dataType\",\"required\",\"disabled\",\"picker\",\"allowClear\"],\"formCode\":\"OUT_HOSPITAL_TIME\",\"picker\":\"date\",\"wigetType\":\"fixed\",\"widgetType\":\"fixed\"}]},{\"colId\":\"col_220959c2\",\"span\":8,\"items\":[{\"itemId\":\"item_1230e745\",\"group\":\"基本\",\"formId\":\"danhangwenben\",\"icon\":\"icondanhangwenben\",\"isForm\":true,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"formCode\":\"LENGTH_HOSPITAL\",\"wigetType\":\"fixed\",\"widgetType\":\"fixed\"}]}]},{\"rowId\":\"row_4948213c\",\"rowType\":\"fixed\",\"cols\":[{\"colId\":\"col_4948b3be\",\"span\":8,\"items\":[{\"itemId\":\"item_615434a6\",\"group\":\"基本\",\"formId\":\"shijian\",\"icon\":\"iconshijian1\",\"isForm\":true,\"propslist\":[\"dataType\",\"required\",\"disabled\",\"allowClear\"],\"formCode\":\"IN_LAB_TIME\",\"picker\":\"date\",\"wigetType\":\"fixed\",\"widgetType\":\"fixed\"}]},{\"colId\":\"col_4948324b\",\"span\":8,\"items\":[{\"itemId\":\"item_8292530e\",\"group\":\"基本\",\"formId\":\"shijian\",\"icon\":\"iconshijian1\",\"isForm\":true,\"propslist\":[\"dataType\",\"required\",\"disabled\",\"allowClear\"],\"formCode\":\"OUT_LAB_TIME\",\"picker\":\"date\",\"wigetType\":\"fixed\",\"widgetType\":\"fixed\"}]},{\"colId\":\"col_49482927\",\"span\":8,\"items\":[{\"itemId\":\"item_19597cb8\",\"group\":\"基本\",\"formId\":\"danhangwenben\",\"icon\":\"icondanhangwenben\",\"isForm\":true,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"],\"formCode\":\"LENGTH_LAB\",\"wigetType\":\"fixed\",\"widgetType\":\"fixed\"}]}]}]}","IF_QUERY":null,"SETUP_NAME":"实验室ISO15189人员表单","SETUP_CLASS":"B05-66|B05-67"}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：职称名称
{"FIELD_ID":"H81000215","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"ACADEMIC_POST","FIELD_NAME":"职称名称","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataClass\":\"DataClass_By_Level_Names\",\"queryType\":\"api\",\"dataType\":\"Select\",\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}]},\"formName\":\"职称名称\",\"labelStyle\":{\"color\":\"rgb(0,0,0)\"}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"xialakuang\",\"icon\":\"iconxialakuang\",\"formName\":\"下拉框\",\"isForm\":true,\"dataType\":\"Select\",\"dataClass\":\"DataClass_By_Level_Names\",\"queryType\":\"api\",\"wigetType\":\"fixed\",\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}]},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-19T11:45:07","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:38:52","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：基本信息
{"FIELD_ID":"H81000256","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"JBXX70654","FIELD_NAME":"基本信息","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataType\":\"Input\",\"rules\":[{\"required\":true}],\"wigetProps\":{\"placeholder\":\"请输入\",\"allowClear\":false,\"maxLength\":200,\"disabled\":false,\"variant\":\"outlined\"},\"formName\":\"基本信息\"}","ADDN_JSON":"{\"group\":\"基本\",\"formId\":\"danhangwenben\",\"icon\":\"icondanhangwenben\",\"formName\":\"单行文本\",\"dataType\":\"Input\",\"isForm\":true,\"rules\":[{\"required\":true}],\"wigetProps\":{\"placeholder\":\"请输入\",\"allowClear\":false,\"maxLength\":200,\"disabled\":false,\"variant\":\"outlined\"},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T19:55:32","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:38:53","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：执业类型
{"FIELD_ID":"H81000211","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"TECHNOLOGY_TYPE","FIELD_NAME":"执业类型","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataClass\":\"职称类型\",\"queryType\":\"api\",\"dataType\":\"Select\",\"rules\":[{\"required\":null}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}],\"defaultValue\":\"\"},\"formName\":\"执业类型\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"colSpan\":8,\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"},\"defaultValue\":null}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"xialakuang\",\"icon\":\"iconxialakuang\",\"formName\":\"下拉框\",\"isForm\":true,\"dataType\":\"Select\",\"wigetProps\":{\"options\":[],\"defaultValue\":\"1\"},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"],\"dataClass\":\"职称类型\",\"queryType\":\"api\",\"rules\":[{\"required\":false}]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T19:55:42","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:38:53","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：现居住地
{"FIELD_ID":"H81000184","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"CURRENT_ADDRESS","FIELD_NAME":"现居住地","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":null,\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":{\"color\":\"rgb(0,0,0)\"},\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"现居住地\",\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"dataType\":\"Input\",\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"现居住地\",\"dataClass\":null,\"labelHide\":false,\"labelStyle\":null,\"colSpan\":8,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-14T00:04:01","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:38:53","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：工号
{"FIELD_ID":"H81000172","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"HIS_ID","FIELD_NAME":"工号","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":null,\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":{\"color\":\"rgb(0,0,0)\"},\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"工号\"}","ADDN_JSON":"{\"colSpan\":8,\"dataClass\":null,\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":{\"color\":\"rgb(0,0,0)\"},\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"工号\",\"widgetProps\":{\"style\":{\"color\":\"rgb(0,0,0)\"}},\"readOnlyEnum\":\"1\",\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"实验室管理导入数据","FIRST_RTIME":"2025-07-09T14:38:54","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-07-09T15:17:38","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：人员性质
{"FIELD_ID":"H81000207","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"PERSON_DOC_STATE","FIELD_NAME":"人员性质","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataType\":\"Input\",\"rules\":[{\"required\":true}],\"wigetProps\":{\"placeholder\":\"请输入\",\"allowClear\":true,\"maxLength\":200,\"disabled\":false},\"formName\":\"人员性质\",\"dataClass\":null,\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"dataType\":\"Input\",\"rules\":[{\"required\":true}],\"wigetProps\":{\"placeholder\":\"请输入\",\"allowClear\":true,\"maxLength\":200,\"disabled\":false},\"formName\":\"人员性质\",\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-13T22:51:04","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:38:54","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：证件号码
{"FIELD_ID":"H81000178","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"ID_CARD","FIELD_NAME":"证件号码","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":\"8\",\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":{\"color\":\"rgb(0,0,0)\"},\"rules\":[{\"required\":true}],\"wigetProps\":{\"button\":false,\"groupKey\":null},\"formName\":\"证件号码\",\"dataClass\":null,\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"colSpan\":8,\"dataType\":\"Input\",\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"},\"defaultValue\":null},\"formName\":\"证件号码1234\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-13T20:46:13","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:38:55","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：性别
{"FIELD_ID":"H81000180","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"SEX","FIELD_NAME":"性别","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":\"性别\",\"queryType\":\"api\",\"dataType\":\"Select\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}],\"defaultValue\":\"\"},\"formName\":\"性别\",\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"colSpan\":8,\"dataClass\":\"性别\",\"queryType\":\"api\",\"dataType\":\"Select\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":true}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}],\"defaultValue\":\"\"},\"formName\":\"性别/年龄\",\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-14T00:04:01","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:38:55","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：详细通讯地址
{"FIELD_ID":"H81000206","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"COMM_ADDR","FIELD_NAME":"详细通讯地址","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataType\":\"Input\",\"rules\":[{\"required\":null}],\"wigetProps\":{\"button\":false,\"groupKey\":null},\"formName\":\"详细通讯地址\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"colSpan\":8,\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"danhangwenben\",\"icon\":\"icondanhangwenben\",\"formName\":\"单行文本\",\"dataType\":\"Input\",\"isForm\":true,\"rules\":[{\"required\":true}],\"wigetProps\":{\"placeholder\":\"请输入\",\"allowClear\":true,\"maxLength\":200,\"disabled\":false},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T18:38:50","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:38:56","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：入职方式
{"FIELD_ID":"H81000209","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"EMPLOYMENT_SOURE","FIELD_NAME":"入职方式","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataClass\":\"入职方式\",\"queryType\":\"api\",\"dataType\":\"Select\",\"wigetProps\":{\"button\":false,\"groupKey\":null},\"formName\":\"入职方式\",\"rules\":[{\"required\":true}],\"labelHide\":false,\"labelStyle\":{\"color\":\"rgb(0,0,0)\"}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"xialakuang\",\"icon\":\"iconxialakuang\",\"formName\":\"下拉框\",\"isForm\":true,\"dataType\":\"Select\",\"wigetProps\":{\"options\":[],\"defaultValue\":\"3\"},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"],\"dataClass\":\"入职方式\",\"queryType\":\"api\"}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T18:38:52","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:38:56","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：用工类型
{"FIELD_ID":"H81000210","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"USER_TYPE","FIELD_NAME":"用工类型","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataClass\":\"用工类型\",\"queryType\":\"api\",\"dataType\":\"Select\",\"wigetProps\":{\"button\":false,\"groupKey\":null},\"formName\":\"用工类型\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"colSpan\":8,\"rules\":[{\"required\":null}],\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"xialakuang\",\"icon\":\"iconxialakuang\",\"formName\":\"下拉框\",\"isForm\":true,\"dataType\":\"Select\",\"wigetProps\":{\"options\":[],\"defaultValue\":\"\"},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"],\"dataClass\":\"用工类型\",\"queryType\":\"api\"}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T18:38:53","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:38:57","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：聘任职称评定单位
{"FIELD_ID":"H81000214","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"EMPLOYMENT_UNIT","FIELD_NAME":"聘任职称评定单位","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":null,\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":{\"color\":\"rgb(0,0,0)\"},\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"聘任职称评定单位\"}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"dataType\":\"Input\",\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"聘任职称评定单位\",\"dataClass\":null,\"labelHide\":false,\"labelStyle\":null,\"colSpan\":8,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-14T00:04:01","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:38:57","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：职称评定日期
{"FIELD_ID":"H81000216","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"TECH_CERTIFICE_TIME","FIELD_NAME":"职称评定日期","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataType\":\"DatePicker\",\"wigetProps\":{\"button\":false,\"groupKey\":null},\"formName\":\"职称评定日期\",\"labelHide\":false,\"labelStyle\":{\"color\":\"rgb(0,0,0)\"}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"riqi\",\"icon\":\"iconriqi\",\"formName\":\"日期\",\"dataType\":\"DatePicker\",\"isForm\":true,\"wigetProps\":{},\"propslist\":[\"dataType\",\"required\",\"disabled\",\"picker\",\"allowClear\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T20:54:43","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:38:57","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：工龄
{"FIELD_ID":"H81000219","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"LENGTH_SERVICE","FIELD_NAME":"工龄","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataType\":\"Input\",\"rules\":[{\"required\":null}],\"wigetProps\":{\"button\":false,\"groupKey\":null},\"formName\":\"工龄\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"danhangwenben\",\"icon\":\"icondanhangwenben\",\"formName\":\"单行文本\",\"dataType\":\"Input\",\"isForm\":true,\"rules\":[{\"required\":true}],\"wigetProps\":{\"placeholder\":\"请输入\",\"allowClear\":true,\"maxLength\":200,\"disabled\":false},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T18:38:58","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:38:58","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：年龄
{"FIELD_ID":"H81000181","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"AGE","FIELD_NAME":"年龄","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":null,\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":true,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"年龄\",\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"dataType\":\"Input\",\"rules\":[{\"required\":true}],\"wigetProps\":{\"disabled\":true,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"年龄\",\"dataClass\":null,\"labelHide\":false,\"labelStyle\":null,\"colSpan\":8,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-07-09T13:57:55","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:38:58","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：籍贯
{"FIELD_ID":"H81000182","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"NATIVE_PLACE","FIELD_NAME":"籍贯","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataType\":\"Cascader\",\"wigetProps\":{\"button\":false,\"groupKey\":null},\"formName\":\"籍贯\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"colSpan\":8,\"dataClass\":null,\"rules\":[{\"required\":null}],\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"jilianxuanze\",\"icon\":\"iconxialakuang\",\"formName\":\"级联选择\",\"isForm\":true,\"dataType\":\"Cascader\",\"wigetProps\":{},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T18:39:01","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:38:59","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：最高学历
{"FIELD_ID":"H81000185","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"HIGHEST_DEGREE","FIELD_NAME":"最高学历","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":\"最高学历\",\"queryType\":\"api\",\"dataType\":\"Select\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}]},\"formName\":\"最高学历\",\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}","ADDN_JSON":"{\"colSpan\":8,\"dataClass\":\"最高学历\",\"queryType\":\"api\",\"dataType\":\"Select\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":true}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}]},\"formName\":\"最高学历\",\"readOnlyEnum\":\"1\",\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-19T16:34:31","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:38:59","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：退休日期
{"FIELD_ID":"H81000218","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"RETIRE_TIME","FIELD_NAME":"退休日期","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataType\":\"DatePicker\",\"wigetProps\":{},\"formName\":\"退休日期\",\"rules\":[{\"required\":true}],\"labelStyle\":{\"color\":\"rgb(0,0,0)\"}}","ADDN_JSON":"{\"group\":\"基本\",\"formId\":\"riqi\",\"icon\":\"iconriqi\",\"formName\":\"日期\",\"dataType\":\"DatePicker\",\"isForm\":true,\"wigetProps\":{},\"propslist\":[\"dataType\",\"required\",\"disabled\",\"picker\",\"allowClear\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-19T16:34:31","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:00","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：婚姻状况
{"FIELD_ID":"H81000192","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"MARITAL_STATUS","FIELD_NAME":"婚姻状况","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataClass\":\"婚姻状况\",\"queryType\":\"api\",\"dataType\":\"Radio\",\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}],\"defaultValue\":\"\"},\"formName\":\"婚姻状况\",\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"colSpan\":8,\"labelHide\":false,\"rules\":[{\"required\":null}],\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"danxuankuang\",\"icon\":\"icondanxuan\",\"formName\":\"单选\",\"isForm\":true,\"dataType\":\"Radio\",\"wigetProps\":{\"options\":[],\"defaultValue\":\"\"},\"propslist\":[\"dataType\",\"required\",\"optionType\",\"styleFlex\",\"disabled\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"],\"dataClass\":\"婚姻状况\",\"queryType\":\"api\"}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T18:39:04","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:00","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：联系方式
{"FIELD_ID":"H81000200","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"PHONE","FIELD_NAME":"联系方式","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataType\":\"Input\",\"rules\":[{\"required\":null}],\"wigetProps\":{\"button\":false,\"groupKey\":null},\"formName\":\"联系方式\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"colSpan\":8,\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"danhangwenben\",\"icon\":\"icondanhangwenben\",\"formName\":\"单行文本\",\"dataType\":\"Input\",\"isForm\":true,\"rules\":[{\"required\":true}],\"wigetProps\":{\"placeholder\":\"请输入\",\"allowClear\":true,\"maxLength\":200,\"disabled\":false},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T18:39:07","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:00","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：视力
{"FIELD_ID":"H81000196","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"EYESIGHT_RIGHT","FIELD_NAME":"视力","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":\"4\",\"dataClass\":null,\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":null,\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"视力\"}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"dataType\":\"Input\",\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"视力\",\"dataClass\":null,\"labelHide\":false,\"labelStyle\":null,\"colSpan\":8,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-14T00:04:01","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:02","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：职称专业
{"FIELD_ID":"H81000212","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"TECH_POST_PROFESSION","FIELD_NAME":"职称专业","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":null,\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":{\"color\":\"rgb(0,0,0)\"},\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"职称专业\"}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"dataType\":\"Input\",\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"职称专业\",\"dataClass\":null,\"labelHide\":false,\"labelStyle\":null,\"colSpan\":8,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-14T00:04:01","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:02","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：离院日期
{"FIELD_ID":"H81000221","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"OUT_HOSPITAL_TIME","FIELD_NAME":"离院日期","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataType\":\"DatePicker\",\"labelHide\":false,\"labelStyle\":{\"color\":\"rgb(0,0,0)\"},\"rules\":[{\"required\":false}],\"wigetProps\":{\"button\":false,\"groupKey\":null,\"allowClear\":true},\"formName\":\"离院日期\"}","ADDN_JSON":"{\"dataType\":\"DatePicker\",\"wigetProps\":{\"button\":false,\"groupKey\":null,\"allowClear\":true},\"formName\":\"离院日期\",\"labelHide\":false,\"labelStyle\":null,\"readOnlyEnum\":\"1\",\"propslist\":[\"dataType\",\"required\",\"disabled\",\"picker\",\"allowClear\"],\"rules\":[{\"required\":false}]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-07-01T15:05:13","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:03","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：离科日期
{"FIELD_ID":"H81000224","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"OUT_LAB_TIME","FIELD_NAME":"离科日期","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataType\":\"DatePicker\",\"wigetProps\":{\"button\":false,\"groupKey\":null},\"formName\":\"离科日期\",\"labelHide\":false,\"labelStyle\":{\"color\":\"rgb(0,0,0)\"}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"riqi\",\"icon\":\"iconriqi\",\"formName\":\"日期\",\"dataType\":\"DatePicker\",\"isForm\":true,\"wigetProps\":{},\"propslist\":[\"dataType\",\"required\",\"disabled\",\"picker\",\"allowClear\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T20:54:43","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:03","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：科龄
{"FIELD_ID":"H81000225","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"LENGTH_LAB","FIELD_NAME":"科龄","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataClass\":null,\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":{\"color\":\"rgb(0,0,0)\"},\"rules\":[{\"required\":true}],\"wigetProps\":{\"button\":false,\"groupKey\":null},\"formName\":\"科龄\"}","ADDN_JSON":null,"FIELD_STATE":"1","FIRST_RPERSON":"zwt_周伟涛","FIRST_RTIME":"2025-06-12T17:33:14","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:04","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：最高学位
{"FIELD_ID":"H81000186","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"HIGHEST_DIPLOMA","FIELD_NAME":"最高学位","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":\"最高学位\",\"queryType\":\"api\",\"dataType\":\"Select\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}]},\"formName\":\"最高学位\",\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}","ADDN_JSON":"{\"colSpan\":8,\"dataClass\":\"最高学位\",\"queryType\":\"api\",\"dataType\":\"Select\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}]},\"formName\":\"最高学位\",\"readOnlyEnum\":\"1\",\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-19T16:34:31","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:04","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：毕业日期
{"FIELD_ID":"H81000188","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"GRADUATE_DATE","FIELD_NAME":"毕业日期","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":null,\"dataType\":\"DatePicker\",\"labelHide\":false,\"labelStyle\":{\"color\":\"rgb(0,0,0)\"},\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"毕业日期\"}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"dataType\":\"DatePicker\",\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"毕业日期\",\"dataClass\":null,\"labelHide\":false,\"labelStyle\":null,\"rules\":[{\"required\":null}],\"colSpan\":8,\"propslist\":[\"dataType\",\"required\",\"disabled\",\"picker\",\"allowClear\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-14T00:04:01","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:04","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：与紧急联系人的关系
{"FIELD_ID":"H81000204","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"ECONTACT_RELACTION","FIELD_NAME":"与紧急联系人的关系","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataType\":\"Input\",\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"与紧急联系人的关系\",\"dataClass\":null,\"labelHide\":false,\"labelStyle\":{\"color\":\"rgb(0,0,0)\"},\"colSpan\":8,\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"danhangwenben\",\"icon\":\"icondanhangwenben\",\"formName\":\"单行文本\",\"dataType\":\"Input\",\"isForm\":true,\"rules\":[{\"required\":true}],\"wigetProps\":{\"placeholder\":\"请输入\",\"allowClear\":true,\"maxLength\":200,\"disabled\":false},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T18:39:26","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:05","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：证件类型
{"FIELD_ID":"H81000174","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"CARD_TYPE","FIELD_NAME":"证件类型","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":\"证件类型\",\"queryType\":\"api\",\"dataType\":\"Select\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}]},\"formName\":\"证件类型\",\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}","ADDN_JSON":"{\"colSpan\":8,\"dataClass\":\"证件类型\",\"queryType\":\"api\",\"dataType\":\"Select\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":true}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}]},\"formName\":\"证件类型\",\"readOnlyEnum\":\"1\",\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-27T14:27:32","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:05","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：职称级别
{"FIELD_ID":"H81000213","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"TECH_POST","FIELD_NAME":"职称级别","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":\"职称级别\",\"queryType\":\"api\",\"dataType\":\"Select\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}],\"defaultValue\":\"\"},\"formName\":\"职称级别\",\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"dataClass\":\"职称级别\",\"queryType\":\"api\",\"dataType\":\"Select\",\"rules\":[{\"required\":null}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}],\"defaultValue\":\"\"},\"formName\":\"职称级别\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"colSpan\":8,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-14T00:04:01","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:06","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：专业特长
{"FIELD_ID":"H81000199","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"PROFESSION_EXPERTISE","FIELD_NAME":"专业特长","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":null,\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":{\"color\":\"rgb(0,0,0)\"},\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"专业特长\"}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"dataType\":\"Input\",\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"专业特长\",\"dataClass\":null,\"labelHide\":false,\"labelStyle\":null,\"colSpan\":8,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-14T00:04:01","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:06","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：来科日期
{"FIELD_ID":"H81000223","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"IN_LAB_TIME","FIELD_NAME":"来科日期","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataType\":\"DatePicker\",\"wigetProps\":{\"button\":false,\"groupKey\":null},\"formName\":\"来科日期\",\"rules\":[{\"required\":true}],\"labelHide\":false,\"labelStyle\":{\"color\":\"rgb(0,0,0)\"}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"riqi\",\"icon\":\"iconriqi\",\"formName\":\"日期\",\"dataType\":\"DatePicker\",\"isForm\":true,\"wigetProps\":{},\"propslist\":[\"dataType\",\"required\",\"disabled\",\"picker\",\"allowClear\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T21:10:48","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:07","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：是否外单位科研合作人员
{"FIELD_ID":"H81000238","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"SFWDWKYHZRY39890","FIELD_NAME":"是否外单位科研合作人员","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataClass\":\"是否\",\"queryType\":\"api\",\"dataType\":\"Radio\",\"wigetProps\":{\"options\":[],\"defaultValue\":\"\"},\"formName\":\"是否外单位科研合作人员\"}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"danxuankuang\",\"icon\":\"icondanxuan\",\"formName\":\"单选\",\"isForm\":true,\"dataType\":\"Radio\",\"wigetProps\":{\"options\":[],\"defaultValue\":\"\"},\"propslist\":[\"dataType\",\"required\",\"optionType\",\"styleFlex\",\"disabled\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"],\"dataClass\":\"是否\",\"queryType\":\"api\"}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T18:39:34","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:07","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：专业组
{"FIELD_ID":"H81000177","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"PGROUP_ID","FIELD_NAME":"专业组","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":\"DataClass_By_PGROUP_ID\",\"queryType\":\"api\",\"dataType\":\"Select\",\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}]},\"formName\":\"专业组\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"xialakuang\",\"icon\":\"iconxialakuang\",\"formName\":\"下拉框\",\"isForm\":true,\"dataType\":\"Select\",\"dataClass\":\"DataClass_By_PGROUP_ID\",\"queryType\":\"api\",\"wigetType\":\"fixed\",\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}]},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-19T15:20:34","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:07","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：参加工作日期
{"FIELD_ID":"H81000217","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"WORK_TIME","FIELD_NAME":"参加工作日期","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataType\":\"DatePicker\",\"wigetProps\":{\"button\":false,\"groupKey\":null},\"formName\":\"参加工作日期\",\"rules\":[{\"required\":null}],\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"colSpan\":8,\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"riqi\",\"icon\":\"iconriqi\",\"formName\":\"日期\",\"dataType\":\"DatePicker\",\"isForm\":true,\"wigetProps\":{},\"propslist\":[\"dataType\",\"required\",\"disabled\",\"picker\",\"allowClear\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T21:10:54","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:08","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：英语等级
{"FIELD_ID":"H81000190","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"ENGLISH_RANK","FIELD_NAME":"英语等级","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":\"英语级别\",\"queryType\":\"api\",\"dataType\":\"Select\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"英语等级\",\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"dataClass\":\"英语级别\",\"queryType\":\"api\",\"dataType\":\"Select\",\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"英语等级\",\"labelHide\":false,\"labelStyle\":null,\"colSpan\":8,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-14T00:04:01","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:08","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：登录ID
{"FIELD_ID":"H81000176","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"LOGID","FIELD_NAME":"登录ID","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":\"人员培训分类\",\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}],\"defaultValue\":\"H818100000052\"},\"formName\":\"登录ID\",\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"dataType\":\"Input\",\"rules\":[{\"required\":null}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}],\"defaultValue\":\"H818100000052\"},\"formName\":\"登录ID\",\"dataClass\":\"人员培训分类\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"colSpan\":8,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-14T00:04:01","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:09","REMARK":"登录"}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：备注
{"FIELD_ID":"H81000239","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"BZ63459","FIELD_NAME":"备注","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataType\":\"Input\",\"rules\":[{\"required\":false}],\"wigetProps\":{\"placeholder\":\"请输入\",\"allowClear\":true,\"maxLength\":200,\"disabled\":false},\"formName\":\"备注\",\"colSpan\":8,\"dataClass\":null,\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"danhangwenben\",\"icon\":\"icondanhangwenben\",\"formName\":\"单行文本\",\"dataType\":\"Input\",\"isForm\":true,\"rules\":[{\"required\":false}],\"wigetProps\":{\"placeholder\":\"请输入\",\"allowClear\":true,\"maxLength\":200,\"disabled\":false},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T19:54:32","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:09","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：培训合格证书
{"FIELD_ID":"*********","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"PXHGZS25179","FIELD_NAME":"培训合格证书","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataType\":\"Upload\",\"wigetProps\":{\"listType\":\"picture-card\",\"multiple\":true,\"accept\":\".png,.jpg,.jpeg,.pdf,.xls,.xlsx,.doc,.docx\",\"showUploadList\":true},\"formName\":\"培训合格证书\"}","ADDN_JSON":"{\"group\":\"基本\",\"formId\":\"fujian\",\"icon\":\"iconfujian\",\"formName\":\"附件\",\"isForm\":true,\"dataType\":\"Upload\",\"wigetProps\":{\"listType\":\"picture-card\",\"multiple\":true,\"accept\":\".png,.jpg,.jpeg,.pdf,.xls,.xlsx,.doc,.docx\",\"showUploadList\":true},\"propslist\":[\"dataType\",\"required\",\"multiple\",\"listType\",\"accept\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T18:39:41","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:10","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：有无子女
{"FIELD_ID":"*********","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"CHILDREN_CONDITION","FIELD_NAME":"有无子女","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataClass\":\"有无子女\",\"queryType\":\"api\",\"dataType\":\"Radio\",\"rules\":[{\"required\":null}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}],\"defaultValue\":\"\"},\"formName\":\"有无子女\",\"colSpan\":8,\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"danxuankuang\",\"icon\":\"icondanxuan\",\"formName\":\"单选\",\"isForm\":true,\"dataType\":\"Radio\",\"wigetProps\":{\"options\":[],\"defaultValue\":\"2\"},\"propslist\":[\"dataType\",\"required\",\"optionType\",\"styleFlex\",\"disabled\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"],\"dataClass\":\"有无子女\",\"queryType\":\"api\",\"rules\":[{\"required\":false}]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T19:54:38","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:10","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：出生年月
{"FIELD_ID":"H81000175","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"BIRTHDAY","FIELD_NAME":"出生年月","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":null,\"dataType\":\"DatePicker\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"},\"picker\":\"date\"},\"formName\":\"出生年月\",\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"dataType\":\"DatePicker\",\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"},\"picker\":\"date\"},\"formName\":\"出生年月\",\"dataClass\":null,\"labelHide\":false,\"labelStyle\":null,\"rules\":[{\"required\":null}],\"colSpan\":8,\"propslist\":[\"dataType\",\"required\",\"disabled\",\"picker\",\"allowClear\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-14T00:04:01","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:11","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：身高
{"FIELD_ID":"H81000195","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"HEIGHT","FIELD_NAME":"身高","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":null,\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"身高\",\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"colSpan\":8,\"dataClass\":null,\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":null,\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"身高\",\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-14T00:04:01","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:11","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：紧急联系人电话
{"FIELD_ID":"H81000205","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"ECONTACT_PHONE","FIELD_NAME":"紧急联系人电话","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":null,\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":{\"color\":\"rgb(0,0,0)\"},\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"紧急联系人电话\"}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"dataType\":\"Input\",\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"紧急联系人电话\",\"dataClass\":null,\"labelHide\":false,\"labelStyle\":null,\"colSpan\":8,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-14T00:04:01","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:11","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：数值
{"FIELD_ID":"H81000258","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"SZ52717","FIELD_NAME":"数值","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataType\":\"InputNumber\",\"rules\":[{\"required\":true}],\"wigetProps\":{\"controls\":true},\"formName\":\"数值\"}","ADDN_JSON":"{\"group\":\"基本\",\"formId\":\"shuzhishurukuang\",\"icon\":\"iconshuzhishurukuang\",\"formName\":\"数值\",\"isForm\":true,\"dataType\":\"InputNumber\",\"wigetProps\":{\"controls\":true},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"min\",\"max\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"controls\"],\"rules\":[{\"required\":true}]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T19:55:18","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:12","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：基本信息项
{"FIELD_ID":"H81000250","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"JBXXX75570","FIELD_NAME":"基本信息项","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataType\":\"Input\",\"rules\":[{\"required\":null}],\"wigetProps\":{\"placeholder\":\"请输入\",\"allowClear\":true,\"maxLength\":200,\"disabled\":false},\"formName\":\"基本信息项\",\"dataClass\":null,\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}","ADDN_JSON":"{\"group\":\"基本\",\"formId\":\"danhangwenben\",\"icon\":\"icondanhangwenben\",\"formName\":\"单行文本\",\"dataType\":\"Input\",\"isForm\":true,\"rules\":[{\"required\":true}],\"wigetProps\":{\"placeholder\":\"请输入\",\"allowClear\":true,\"maxLength\":200,\"disabled\":false},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T19:55:23","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:12","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：岗位类别
{"FIELD_ID":"H81000241","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"GWLB79071","FIELD_NAME":"岗位类别","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":\"8\",\"dataClass\":\"岗位类别\",\"queryType\":\"api\",\"dataType\":\"Select\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}],\"defaultValue\":\"\"},\"formName\":\"岗位类别\"}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"dataClass\":\"岗位类别\",\"queryType\":\"api\",\"dataType\":\"Select\",\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}],\"defaultValue\":\"\"},\"formName\":\"岗位类别\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"colSpan\":8,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-14T00:04:01","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:13","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：工作管理类型
{"FIELD_ID":"H81000252","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"GZGLLX49535","FIELD_NAME":"工作管理类型","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataType\":\"Input\",\"rules\":[{\"required\":null}],\"wigetProps\":{\"placeholder\":\"请输入\",\"allowClear\":true,\"maxLength\":200,\"disabled\":false},\"formName\":\"工作管理类型\",\"dataClass\":null,\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}","ADDN_JSON":"{\"group\":\"基本\",\"formId\":\"danhangwenben\",\"icon\":\"icondanhangwenben\",\"formName\":\"单行文本\",\"dataType\":\"Input\",\"isForm\":true,\"rules\":[{\"required\":true}],\"wigetProps\":{\"placeholder\":\"请输入\",\"allowClear\":true,\"maxLength\":200,\"disabled\":false},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T19:55:27","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:13","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：户籍所在地
{"FIELD_ID":"H81000183","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"DOMICILE_PLACE","FIELD_NAME":"户籍所在地","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataType\":\"Cascader\",\"rules\":[{\"required\":false}],\"wigetProps\":{\"button\":false,\"groupKey\":null},\"formName\":\"户籍所在地\",\"labelHide\":false,\"labelStyle\":{\"color\":\"rgb(0,0,0)\"},\"colSpan\":8,\"dataClass\":null,\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"jilianxuanze\",\"icon\":\"iconxialakuang\",\"formName\":\"级联选择\",\"isForm\":true,\"dataType\":\"Cascader\",\"wigetProps\":{},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\"],\"rules\":[{\"required\":false}]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T19:55:12","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:14","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：参加培训情况
{"FIELD_ID":"H81000242","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"CJPXQK82201","FIELD_NAME":"参加培训情况","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataType\":\"Input\",\"rules\":[{\"required\":true}],\"wigetProps\":{\"placeholder\":\"请输入\",\"allowClear\":true,\"maxLength\":200,\"disabled\":false},\"formName\":\"参加培训情况\"}","ADDN_JSON":"{\"group\":\"基本\",\"formId\":\"danhangwenben\",\"icon\":\"icondanhangwenben\",\"formName\":\"单行文本\",\"dataType\":\"Input\",\"isForm\":true,\"rules\":[{\"required\":true}],\"wigetProps\":{\"placeholder\":\"请输入\",\"allowClear\":true,\"maxLength\":200,\"disabled\":false},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T18:39:12","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:14","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：培训合格证号
{"FIELD_ID":"H81000243","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"PXHGZH48698","FIELD_NAME":"培训合格证号","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataType\":\"Input\",\"rules\":[{\"required\":true}],\"wigetProps\":{\"placeholder\":\"请输入\",\"allowClear\":true,\"maxLength\":200,\"disabled\":false},\"formName\":\"培训合格证号\",\"colSpan\":8,\"dataClass\":null,\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"}}","ADDN_JSON":"{\"group\":\"基本\",\"formId\":\"danhangwenben\",\"icon\":\"icondanhangwenben\",\"formName\":\"单行文本\",\"dataType\":\"Input\",\"isForm\":true,\"rules\":[{\"required\":true}],\"wigetProps\":{\"placeholder\":\"请输入\",\"allowClear\":true,\"maxLength\":200,\"disabled\":false},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T19:23:00","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:14","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：新字段
{"FIELD_ID":"H81000245","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"XZD45449","FIELD_NAME":"新字段","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataClass\":\"参会人身份\",\"queryType\":\"api\",\"dataType\":\"Select\",\"wigetProps\":{\"options\":[],\"defaultValue\":\"XBD00000001\"},\"formName\":\"新字段\"}","ADDN_JSON":"{\"group\":\"基本\",\"formId\":\"xialakuang\",\"icon\":\"iconxialakuang\",\"formName\":\"下拉框\",\"isForm\":true,\"dataType\":\"Select\",\"wigetProps\":{\"options\":[],\"defaultValue\":\"XBD00000001\"},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"],\"queryType\":\"api\",\"dataClass\":\"参会人身份\",\"isDataTypeToPerson\":true}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T19:30:33","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:15","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：现从事岗位
{"FIELD_ID":"H81000240","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"XCSGW70984","FIELD_NAME":"现从事岗位","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataClass\":\"现从事岗位\",\"queryType\":\"api\",\"dataType\":\"Select\",\"rules\":[{\"required\":null}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}],\"defaultValue\":\"\"},\"formName\":\"现从事岗位\",\"colSpan\":8,\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"xialakuang\",\"icon\":\"iconxialakuang\",\"formName\":\"下拉框\",\"isForm\":true,\"dataType\":\"Select\",\"queryType\":\"api\",\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}],\"defaultValue\":\"\"},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"],\"dataClass\":\"现从事岗位\",\"rules\":[{\"required\":false}]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T19:24:19","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:15","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：办公电话
{"FIELD_ID":"H81000202","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"OFFICE_PHONE","FIELD_NAME":"办公电话","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":null,\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":{\"color\":\"rgb(0,0,0)\"},\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"办公电话\",\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"dataType\":\"Input\",\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"办公电话\",\"dataClass\":null,\"labelHide\":false,\"labelStyle\":null,\"colSpan\":8,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-14T00:04:01","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:16","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：毕业专业
{"FIELD_ID":"H81000189","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"PROFESSION","FIELD_NAME":"毕业专业","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":null,\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":{\"color\":\"rgb(0,0,0)\"},\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"毕业专业\",\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"dataType\":\"Input\",\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"毕业专业\",\"dataClass\":null,\"labelHide\":false,\"labelStyle\":null,\"colSpan\":8,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-14T00:04:01","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:16","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：视力（左/右）
{"FIELD_ID":"H81000197","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"EYESIGHT_LEFT","FIELD_NAME":"视力（左/右）","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":\"8\",\"dataClass\":null,\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":{\"color\":\"rgb(0,0,0)\"},\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"视力（左/右）\"}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"dataType\":\"Input\",\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"视力（左/右）\",\"dataClass\":null,\"labelHide\":false,\"labelStyle\":null,\"colSpan\":8,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-14T00:04:01","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:17","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：颜色选择器
{"FIELD_ID":"H81000264","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"YSXZQ25786","FIELD_NAME":"颜色选择器","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataType\":\"ColorPicker\",\"rules\":[{\"required\":false}],\"wigetProps\":{\"showText\":true,\"allowClear\":true},\"formName\":\"颜色选择器\"}","ADDN_JSON":"{\"group\":\"基本\",\"formId\":\"yansexuanze\",\"icon\":\"iconyanse\",\"formName\":\"颜色选择器\",\"dataType\":\"ColorPicker\",\"isForm\":true,\"wigetProps\":{\"showText\":true,\"allowClear\":true},\"propslist\":[\"dataType\",\"required\",\"disabled\",\"showText\"],\"rules\":[{\"required\":false}]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T19:56:21","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:17","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：毕业院校
{"FIELD_ID":"H81000187","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"GRADUATE_SCHOOL","FIELD_NAME":"毕业院校","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataType\":\"Input\",\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"毕业院校\",\"dataClass\":null,\"labelHide\":false,\"labelStyle\":{\"color\":\"rgb(0,0,0)\"},\"colSpan\":8}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"danhangwenben\",\"icon\":\"icondanhangwenben\",\"formName\":\"单行文本\",\"dataType\":\"Input\",\"isForm\":true,\"rules\":[{\"required\":false}],\"wigetProps\":{\"placeholder\":\"请输入\",\"allowClear\":true,\"maxLength\":200,\"disabled\":false},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T19:56:06","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:17","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：政治面貌
{"FIELD_ID":"H81000179","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"POLITICIAN","FIELD_NAME":"政治面貌","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":\"政治面貌\",\"queryType\":\"api\",\"dataType\":\"Select\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}],\"defaultValue\":\"\"},\"formName\":\"政治面貌\",\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"dataClass\":\"政治面貌\",\"queryType\":\"api\",\"dataType\":\"Select\",\"rules\":[{\"required\":null}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}],\"defaultValue\":\"\"},\"formName\":\"政治面貌\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"colSpan\":8,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-14T00:04:01","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:18","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：职务
{"FIELD_ID":"H81000208","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"DUTIES","FIELD_NAME":"职务","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":\"职务\",\"queryType\":\"api\",\"dataType\":\"Select\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}],\"defaultValue\":\"\"},\"formName\":\"职务\",\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"dataClass\":\"职务\",\"queryType\":\"api\",\"dataType\":\"Select\",\"rules\":[{\"required\":null}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}],\"defaultValue\":\"\"},\"formName\":\"职务\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"colSpan\":8,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-14T00:04:01","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:18","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：岗位类别
{"FIELD_ID":"H81000267","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"GWLB57036","FIELD_NAME":"岗位类别","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataClass\":\"岗位类别\",\"queryType\":\"api\",\"dataType\":\"Select\",\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}],\"defaultValue\":\"\"},\"formName\":\"岗位类别\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"xialakuang\",\"icon\":\"iconxialakuang\",\"formName\":\"下拉框\",\"isForm\":true,\"dataType\":\"Select\",\"queryType\":\"api\",\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}],\"defaultValue\":\"\"},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"],\"dataClass\":\"岗位类别\"}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T19:57:02","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:19","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：成绩（分）
{"FIELD_ID":"H81000191","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"ENGLISH_RANK_SCORE","FIELD_NAME":"成绩（分）","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataType\":\"InputNumber\",\"wigetProps\":{\"controls\":true},\"formName\":\"成绩（分）\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"dataClass\":\"H810000-ENGLISH_RANK_SCORE\"}","ADDN_JSON":"{\"group\":\"基本\",\"formId\":\"shuzhishurukuang\",\"icon\":\"iconshuzhishurukuang\",\"formName\":\"数值\",\"isForm\":true,\"dataType\":\"InputNumber\",\"wigetProps\":{\"controls\":true},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"min\",\"max\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"controls\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-07-08T11:24:06","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:19","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：英语级别/成绩(分)
{"FIELD_ID":"H81000270","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"YYJB50394","FIELD_NAME":"英语级别/成绩(分)","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":\"8\",\"dataClass\":\"英语级别\",\"queryType\":\"api\",\"dataType\":\"Select\",\"rules\":[{\"required\":false}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}],\"defaultValue\":\"\"},\"formName\":\"英语级别/成绩(分)\"}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"dataClass\":\"英语级别\",\"queryType\":\"api\",\"dataType\":\"Select\",\"rules\":[{\"required\":false}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}],\"defaultValue\":\"\"},\"formName\":\"英语级别/成绩(分)\",\"colSpan\":8,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-14T00:04:01","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:20","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：民族
{"FIELD_ID":"H81000173","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"NATION","FIELD_NAME":"民族","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":\"民族\",\"queryType\":\"api\",\"dataType\":\"Select\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"wigetProps\":{\"options\":[]},\"formName\":\"民族\",\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"dataClass\":\"民族\",\"queryType\":\"api\",\"dataType\":\"Select\",\"rules\":[{\"required\":true}],\"wigetProps\":{\"options\":[]},\"formName\":\"下拉框\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"colSpan\":8,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-13T22:51:04","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:20","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：邮箱
{"FIELD_ID":"H81000201","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"E_MAIL","FIELD_NAME":"邮箱","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":null,\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":{\"color\":\"rgb(0,0,0)\"},\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"邮箱\"}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"dataType\":\"Input\",\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"邮箱\",\"dataClass\":null,\"labelHide\":false,\"labelStyle\":null,\"colSpan\":8,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-14T00:04:01","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:20","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：备案实验室
{"FIELD_ID":"H81000237","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"BASYS55209","FIELD_NAME":"备案实验室","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataClass\":\"DataClass_By_Smbl_Lab\",\"queryType\":\"api\",\"dataType\":\"Select\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}]},\"formName\":\"备案实验室\",\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}","ADDN_JSON":"{\"dataClass\":\"DataClass_By_Smbl_Lab\",\"queryType\":\"api\",\"dataType\":\"Select\",\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}]},\"formName\":\"备案实验室\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"readOnlyEnum\":\"1\",\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-07-01T15:05:13","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:21","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：紧急联系人
{"FIELD_ID":"H81000203","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"EMERGENCY_CONTACT","FIELD_NAME":"紧急联系人","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":null,\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":{\"color\":\"rgb(0,0,0)\"},\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"紧急联系人\",\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"colSpan\":8,\"dataClass\":null,\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":null,\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"紧急联系人\",\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-14T00:04:01","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:21","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：颜色视觉障碍
{"FIELD_ID":"H81000194","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"COLOR_DEFICIENCY","FIELD_NAME":"颜色视觉障碍","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataClass\":\"H810000-COLOR_DEFICIENCY\",\"queryType\":\"api\",\"dataType\":\"Select\",\"rules\":[{\"required\":true}],\"wigetProps\":{\"options\":[{\"label\":\"正常\",\"value\":\"0\"},{\"label\":\"色弱\",\"value\":\"1\"},{\"label\":\"色盲\",\"value\":\"2\"}]},\"formName\":\"颜色视觉障碍\",\"labelStyle\":{\"color\":\"rgb(0,0,0)\"},\"widgetProps\":{\"style\":{\"color\":\"rgb(0,0,0)\"}}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"dataClass\":\"H810000-COLOR_DEFICIENCY\",\"queryType\":\"api\",\"dataType\":\"Select\",\"rules\":[{\"required\":true}],\"wigetProps\":{\"options\":[{\"label\":\"正常\",\"value\":\"0\"},{\"label\":\"色弱\",\"value\":\"1\"},{\"label\":\"色盲\",\"value\":\"2\"}]},\"formName\":\"颜色视觉障碍\",\"readOnlyEnum\":\"1\",\"propslist\":[\"dataType\",\"required\",\"optionType\",\"styleFlex\",\"disabled\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-07-08T11:08:16","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:22","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：姓名
{"FIELD_ID":"H81000171","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"USER_NAME","FIELD_NAME":"姓名","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":null,\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}]},\"formName\":\"姓名\",\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"colSpan\":8,\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}]},\"formName\":\"姓名AAA\",\"dataClass\":null,\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"ldd_李丹丹","FIRST_RTIME":"2025-06-18T19:13:00","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:22","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：健康状况
{"FIELD_ID":"H81000198","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"HEALTH","FIELD_NAME":"健康状况","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":\"健康状况\",\"queryType\":\"api\",\"dataType\":\"Select\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}],\"defaultValue\":\"\"},\"formName\":\"健康状况\"}","ADDN_JSON":"{\"dataClass\":\"健康状况\",\"queryType\":\"api\",\"dataType\":\"Select\",\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}],\"defaultValue\":\"\"},\"formName\":\"健康状况\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"colSpan\":8,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-14T00:04:01","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:23","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：来院日期
{"FIELD_ID":"H81000220","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"IN_HOSPITAL_TIME","FIELD_NAME":"来院日期","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataType\":\"DatePicker\",\"wigetProps\":{\"button\":false,\"groupKey\":null},\"formName\":\"来院日期\",\"labelHide\":false,\"labelStyle\":{\"color\":\"rgb(0,0,0)\"}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"riqi\",\"icon\":\"iconriqi\",\"formName\":\"日期\",\"dataType\":\"DatePicker\",\"isForm\":true,\"wigetProps\":{},\"propslist\":[\"dataType\",\"required\",\"disabled\",\"picker\",\"allowClear\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T20:54:43","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:23","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##UPDATE:STYLE_JSON,ADDN_JSON##公共字段库：院龄
{"FIELD_ID":"H81000222","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"LENGTH_HOSPITAL","FIELD_NAME":"院龄","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataType\":\"Input\",\"rules\":[{\"required\":null}],\"wigetProps\":{\"button\":false,\"groupKey\":null},\"formName\":\"院龄\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"colSpan\":8,\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"danhangwenben\",\"icon\":\"icondanhangwenben\",\"formName\":\"单行文本\",\"dataType\":\"Input\",\"isForm\":true,\"rules\":[{\"required\":true}],\"wigetProps\":{\"placeholder\":\"请输入\",\"allowClear\":true,\"maxLength\":200,\"disabled\":false},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T18:39:19","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-09T14:39:24","REMARK":null}
XH.LAB.UTILS.Models.Entites.OA_BASE_DATA, XH.LAB.UTILS##UPDATE:DATA_NAME##OA基础数据-人事已实现的属性输入方式：文本
{"DATA_ID":"1","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FATHER_ID":null,"CLASS_ID":"人事已实现的属性输入方式","DATA_SORT":"1","DATA_NAME":"文本","DATA_SNAME":null,"DATA_ENAME":null,"STANDART_ID":null,"CUSTOM_CODE":null,"SPELL_CODE":null,"STATE_FLAG":"1","FIRST_RPERSON":"H81初始化","FIRST_RTIME":"2024-06-27T18:23:08","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-11T17:25:59","REMARK":"11111"}
XH.LAB.UTILS.Models.Entites.OA_BASE_DATA, XH.LAB.UTILS##UPDATE:DATA_NAME##OA基础数据-人事已实现的属性输入方式：数字
{"DATA_ID":"2","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FATHER_ID":null,"CLASS_ID":"人事已实现的属性输入方式","DATA_SORT":"2","DATA_NAME":"数字","DATA_SNAME":null,"DATA_ENAME":null,"STANDART_ID":null,"CUSTOM_CODE":null,"SPELL_CODE":null,"STATE_FLAG":"1","FIRST_RPERSON":"H81初始化","FIRST_RTIME":"2024-06-27T18:23:08","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-11T17:25:59","REMARK":"11111"}
XH.LAB.UTILS.Models.Entites.OA_BASE_DATA, XH.LAB.UTILS##UPDATE:DATA_NAME##OA基础数据-人事已实现的属性输入方式：日期
{"DATA_ID":"3","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FATHER_ID":null,"CLASS_ID":"人事已实现的属性输入方式","DATA_SORT":"3","DATA_NAME":"日期","DATA_SNAME":null,"DATA_ENAME":null,"STANDART_ID":null,"CUSTOM_CODE":null,"SPELL_CODE":null,"STATE_FLAG":"1","FIRST_RPERSON":"H81初始化","FIRST_RTIME":"2024-06-27T18:23:08","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-11T17:26:00","REMARK":"11111"}
XH.LAB.UTILS.Models.Entites.OA_BASE_DATA, XH.LAB.UTILS##UPDATE:DATA_NAME##OA基础数据-人事已实现的属性输入方式：时间
{"DATA_ID":"4","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FATHER_ID":null,"CLASS_ID":"人事已实现的属性输入方式","DATA_SORT":"4","DATA_NAME":"时间","DATA_SNAME":null,"DATA_ENAME":null,"STANDART_ID":null,"CUSTOM_CODE":null,"SPELL_CODE":null,"STATE_FLAG":"1","FIRST_RPERSON":"H81初始化","FIRST_RTIME":"2024-06-27T18:23:08","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-11T17:26:00","REMARK":"11111"}
XH.LAB.UTILS.Models.Entites.OA_BASE_DATA, XH.LAB.UTILS##UPDATE:DATA_NAME##OA基础数据-人事已实现的属性输入方式：下拉单选
{"DATA_ID":"9","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FATHER_ID":null,"CLASS_ID":"人事已实现的属性输入方式","DATA_SORT":"6","DATA_NAME":"下拉单选","DATA_SNAME":null,"DATA_ENAME":null,"STANDART_ID":null,"CUSTOM_CODE":null,"SPELL_CODE":null,"STATE_FLAG":"1","FIRST_RPERSON":"H81初始化","FIRST_RTIME":"2024-06-27T18:23:08","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-11T17:26:00","REMARK":"11111"}
XH.LAB.UTILS.Models.Entites.OA_BASE_DATA, XH.LAB.UTILS##UPDATE:DATA_NAME##OA基础数据-人事已实现的属性输入方式：大文本
{"DATA_ID":"12","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FATHER_ID":null,"CLASS_ID":"人事已实现的属性输入方式","DATA_SORT":"7","DATA_NAME":"大文本","DATA_SNAME":null,"DATA_ENAME":null,"STANDART_ID":null,"CUSTOM_CODE":null,"SPELL_CODE":null,"STATE_FLAG":"1","FIRST_RPERSON":"H81初始化","FIRST_RTIME":"2024-06-27T18:23:08","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-11T17:26:01","REMARK":"11111"}
XH.LAB.UTILS.Models.Entites.OA_BASE_DATA, XH.LAB.UTILS##UPDATE:DATA_NAME##OA基础数据-人事已实现的属性输入方式：单选按钮
{"DATA_ID":"13","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FATHER_ID":null,"CLASS_ID":"人事已实现的属性输入方式","DATA_SORT":"8","DATA_NAME":"单选按钮","DATA_SNAME":null,"DATA_ENAME":null,"STANDART_ID":null,"CUSTOM_CODE":null,"SPELL_CODE":null,"STATE_FLAG":"1","FIRST_RPERSON":"H81初始化","FIRST_RTIME":"2024-06-27T18:23:08","LAST_MPERSON":"实验室管理导入数据","LAST_MTIME":"2025-07-11T17:26:01","REMARK":"11111"}
