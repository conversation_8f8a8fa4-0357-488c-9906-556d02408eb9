﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Dtos.Tag
{
    public class PmsPersonTagDto
    {
        /// <summary>
        /// 人员id
        /// </summary>
        public string PERSON_ID { get; set; }

        /// <summary>
        /// 科室id
        /// </summary>
        public string? LAB_ID { get; set; }

        /// <summary>
        /// 科室名称
        /// </summary>
        public string? LAB_NAME { get; set; }

        /// <summary>
        /// 专业组id
        /// </summary>
        public string? PGROUP_ID { get; set; }

        /// <summary>
        /// 专业组名称
        /// </summary>
        public string? PGROUP_NAME { get; set; }

        /// <summary>
        /// 账号id
        /// </summary>
        public string? USER_ID { get; set; }


        /// <summary>
        ///姓名
        /// </summary>
        public string? USER_NAME { get; set; }

        /// <summary>
        ///行政职务
        /// </summary>
        public string? DUTIES { get; set; }

        /// <summary>
        /// 行政职务名称
        /// </summary>
        public string? DUTIES_NAME { get; set; }

        /// <summary>
        ///手机
        /// </summary>
        public string? PHONE { get; set; }

        /// <summary>
        /// 所属实验室
        /// </summary>
        public string SMBL_LAB { get; set; }

        /// <summary>
        /// 标签名称
        /// </summary>
        public string TAG_NAME { get; set; }

        /// <summary>
        /// 标签id
        /// </summary>
        public string TAG_ID { get; set; }
    }
}
