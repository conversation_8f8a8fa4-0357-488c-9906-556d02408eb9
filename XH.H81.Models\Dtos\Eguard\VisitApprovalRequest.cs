﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Dtos.Eguard
{
    public class VisitApprovalRequest
    {
        /// <summary>
        /// 操作类型 1通过 2驳回
        /// </summary>
        public string OPER_TYPE { get;set; }
        /// <summary>
        /// 操作电脑 杏通壳子获取
        /// </summary>
        public string OPER_COMPUTER { get;set; }
        /// <summary>
        /// 操作原因
        /// </summary>
        public string OPER_CAUSE { get;set; }
        /// <summary>
        /// 勾选的数据id
        /// </summary>
        public List<string> VISIT_REQ_IDS { get; set; }
    }
}
