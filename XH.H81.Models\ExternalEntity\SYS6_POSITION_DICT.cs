using System;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using SqlSugar;
using H.Utility.SqlSugarInfra;

namespace XH.H81.Models.ExternalEntity
{
    [Table("SYS6_POSITION_DICT")]
    [DBOwner("XH_SYS")]
    public class SYS6_POSITION_DICT
	{
		/// <summary>
		/// 
		/// </summary>
		[Key]
        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        //[Column("POSITION_ID")]
		[Required(ErrorMessage = "不允许为空")]

		[StringLength(50, ErrorMessage = "POSITION_ID长度不能超出50字符")]
		//[Unicode(false)]
		public string POSITION_ID { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("LAB_ID")]
		[Required(ErrorMessage = "不允许为空")]

		[StringLength(20, ErrorMessage = "LAB_ID长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string LAB_ID { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("AREA_ID")]
		[Required(ErrorMessage = "不允许为空")]

		[StringLength(20, ErrorMessage = "AREA_ID长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string AREA_ID { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("FIRST_RPERSON")]
		[StringLength(50, ErrorMessage = "FIRST_RPERSON长度不能超出50字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FIRST_RPERSON { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("LAST_MTIME")]
		//[Unicode(false)]
		public DateTime? LAST_MTIME { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("TOWER_NO")]
		[StringLength(20, ErrorMessage = "TOWER_NO长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? TOWER_NO { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("FLOOR_NO")]
		[StringLength(50, ErrorMessage = "FLOOR_NO长度不能超出50字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FLOOR_NO { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("POSITION_SORT")]
		[StringLength(50, ErrorMessage = "POSITION_SORT长度不能超出50字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? POSITION_SORT { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("REMARK")]
		[StringLength(200, ErrorMessage = "REMARK长度不能超出200字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? REMARK { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("POSITION_NAME")]
		[StringLength(50, ErrorMessage = "POSITION_NAME长度不能超出50字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? POSITION_NAME { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("FIRST_RTIME")]
		//[Unicode(false)]
        public DateTime? FIRST_RTIME { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("LAST_MPERSON")]
		[StringLength(50, ErrorMessage = "LAST_MPERSON长度不能超出50字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? LAST_MPERSON { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("ROOM_NO")]
		[StringLength(20, ErrorMessage = "ROOM_NO长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? ROOM_NO { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("POSITION_STATE")]
		[StringLength(20, ErrorMessage = "POSITION_STATE长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? POSITION_STATE { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("HOSPITAL_ID")]
		[Required(ErrorMessage = "不允许为空")]

		[StringLength(20, ErrorMessage = "HOSPITAL_ID长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string HOSPITAL_ID { get; set; }


	}
}
