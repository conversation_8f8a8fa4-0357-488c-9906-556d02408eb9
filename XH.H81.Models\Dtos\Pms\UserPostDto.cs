﻿using System;
using System.Collections.Generic;
using System.Text;

namespace XH.H81.Models.Dtos.Pms
{
  public class UserPostDto
    {
        public string POST_ID { get; set; }
        public string POST_NAME { get; set; }
        public string POST_DESC { get; set; }
        public string POST_CLASS { get; set; }
        public string POST_CLASS_NAME { get; set; }
        public string POST_STATE { get; set; }
        public string REMARK { get; set; }
        public string POST_SORT { get; set; }
        public string PGROUP_ID { get; set; }

        public string LAB_ID { get; set; }

        public string PGROUP_NAME { get; set; }
        public string POST_JOB { get; set; }
        public string POST_TYPE { get; set; }

        public string POST_TYPE_NAME { get; set; }
        public string INFO_TYPE { get; set; }
        public string PERSON_NUM { get; set; }
        public string POST_SUPERIOR { get; set; }//上级岗
        public string POST_SUPERIOR_NAME { get; set; }

        public string POST_LEVEL { get; set; }//级别
        public string POST_LEVEL_NAME { get; set; }//级别

        public string USER_NO { get; set; }


        public DateTime? FIRST_RTIME { get; set; }

        public List<UserPostOption> OPTIONS { get; set; }

        public List<PostRoleDto> USERROLE { get; set; }
    }
}
