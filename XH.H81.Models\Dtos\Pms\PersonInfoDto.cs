﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text;

namespace XH.H81.Models.Dtos.Pms
{
    public class PersonInfoDto
    {
        public string PERSON_ID { get; set; }
        public string? LAB_ID { get; set; }
        public string PGROUP_ID { get; set; }
        public string PGROUP_NAME { get; set; }
        public string HOSPITAL_ID { get; set; }
        public string AREA_ID { get; set; }
        public string AREA_NAME { get; set; }
        public string USER_ID { get; set; }
        public string USER_TYPE { get; set; }
        public string USER_TYPE_NAME { get; set; }
        public string USER_NAME { get; set; }
        public string USER_ENAME { get; set; }
        public string SEX { get; set; }
        public string SEX_NAME { get; set; }
        public string AGE { get; set; }
        public string BIRTHDAY { get; set; }
        public string NATION { get; set; }
        public string NATION_NAME { get; set; }
        public string NATIVE_PLACE { get; set; }
        //public string     NATIVE_PLACE_NAME  { get; set; }
        public string POLITICIAN { get; set; }
        public string POLITICIAN_NAME { get; set; }
        public string PROFESSION { get; set; }
        public string HIGHEST_DEGREE { get; set; }
        public string HIGHEST_DEGREE_NAME { get; set; }
        public string DEGREE_TIME { get; set; }
        public string HIGHEST_DIPLOMA { get; set; }
        public string HIGHEST_DIPLOMA_NAME { get; set; }
        public string DIPLOMA_TIME { get; set; }
        public string WORK_TIME { get; set; }
        public string LENGTH_SERVICE { get; set; }
        public string IN_HOSPITAL_DATE { get; set; }
        public string LENGTH_HOSPITAL { get; set; }

        public string DUTIES { get; set; }
        public string DUTIES_NAME { get; set; }
        public string TECH_POST { get; set; }
        public string TECH_POST_NAME { get; set; }
        public string ACADEMIC_POST { get; set; }
        public string ACADEMIC_POST_NAME { get; set; }
        public string COMM_ADDR { get; set; }
        public string HOME_TEL { get; set; }
        public string PHONE { get; set; }
        public string CORNET { get; set; }
        public string BIRTH_PLACE { get; set; }
        public string HEIGHT { get; set; }
        public string EYESIGHT { get; set; }
        public string ENGLISH_RANK { get; set; }
        public string ENGLISH_RANK_NAME { get; set; }
        public string ENGLISH_RANK_SCORE { get; set; }
        public string MARITAL_STATUS { get; set; }
        public string MARITAL_STATUS_NAME { get; set; }
        public string CHILDREN_CONDITION { get; set; }
        public string CHILDREN_CONDITION_NAME { get; set; }
        public string CARD_TYPE { get; set; }
        public string CARD_TYPE_NAME { get; set; }
        public string ID_CARD { get; set; }
        public string DOMICILE_PLACE { get; set; }
        public string EMERGENCY_CONTACT { get; set; }
        public string ECONTACT_RELACTION { get; set; }
        public string ECONTACT_PHONE { get; set; }
        public string CURRENT_ADDRESS { get; set; }
        public string HEALTH { get; set; }
        public string HEALTH_NAME { get; set; }
        public string E_MAIL { get; set; }
        public string OFFICE_PHONE { get; set; }
        public string EMPLOYMENT_UNIT { get; set; }
        public string TECHNOLOGY_TYPE { get; set; }
        public string TECHNOLOGY_TYPE_NAME { get; set; }
        public string TECH_CERTIFICE_TIME { get; set; }
        public string TECH_POST_PROFESSION { get; set; }
        public string EMPLOYMENT_SOURE { get; set; }
        public string EMPLOYMENT_SOURE_NAME { get; set; }
        public string PROFESSION_EXPERTISE { get; set; }
        public string EMPLOY_TIME { get; set; }
        public string RETIRE_TIME { get; set; }
        public string IN_HOSPITAL_TIME { get; set; }
        public string OUT_HOSPITAL_TIME { get; set; }
        public string REEMPLOY_TIME { get; set; }
        public string REGISTER_MODE { get; set; }
        public string REGISTER_MODE_NAME { get; set; }
        public string REGISTER_PERSON { get; set; }
        public string REGISTER_TIME { get; set; }
        public string SUBMIT_PERSON { get; set; }
        public string SUBMIT_TIME { get; set; }
        public string CHECK_PERSON { get; set; }
        public string CHECK_TIME { get; set; }
        public string CHECK_COMPUTER { get; set; }
        public string DOC_PLACE { get; set; }
        public string PERSON_DOC_STATE { get; set; }
        public string PERSON_DOC_STATE_NAME { get; set; }
        public string PERSON_PHOTO_PATH { get; set; }

        /// <summary>
        /// 人员人脸照片
        /// </summary>
        public string FACE_PHOTO { get; set; }

        /// <summary>
        /// 人员人脸裁剪照片
        /// </summary>
        public string FACE_CLIP_PHOTO { get; set; }
        public string PHOTO_PATH { get; set; }

        public string IN_LAB_TIME { get; set; }//来科日期
        public string LENGTH_LAB { get; set; }//科龄
        public string OUT_LAB_TIME { get; set; }//离科日期


        public string PERSON_STATE { get; set; }
        public string PERSON_STATE_NAME { get; set; }
        public string FIRST_RPERSON { get; set; }
        public string FIRST_RTIME { get; set; }
        public string LAST_MPERSON { get; set; }
        public string LAST_MTIME { get; set; }
        public string REMARK { get; set; }
        public string LOGID { get; set; }

        public string Token { get; set; }

        public string LAB_NAME { get; set; }
        
        public string HIS_ID { get; set; }
        public string? IF_EMPLOYMENT { get; set; }
        public string? IF_EMPLOYMENT_NAME { get; set; }
        public string? RECORD_DATA { get; set; }

        /// <summary>
        ///左视力
        /// </summary>
        public string? EYESIGHT_LEFT { get; set; }

        /// <summary>
        ///右视力
        /// </summary>
        public string? EYESIGHT_RIGHT { get; set; }

        /// <summary>
        ///毕业院校
        /// </summary>
        public string? GRADUATE_SCHOOL { get; set; }
        /// <summary>
        ///毕业日期
        /// </summary>
        public DateTime? GRADUATE_DATE { get; set; }

        /// <summary>
        ///颜色视觉障碍（0-正常  1-色弱 2-色盲）
        /// </summary>
        public string? COLOR_DEFICIENCY { get; set; }

        /// <summary>
        ///部门/岗位
        /// </summary>
        public string? DEPARTORPOST { get; set; }

        /// <summary>
        ///生安岗位
        /// </summary>
        public string? SMBL_POST { get; set; }

        /// <summary>
        ///生安-岗位信息列表
        /// </summary>
        public List<object>? SMBL_POST_LIST { get; set; }

        /// <summary>
        ///生安-岗位类别
        /// </summary>
        public string? SMBL_POST_TYPE { get; set; }

        /// <summary>
        ///生安-现从事岗位
        /// </summary>
        public string? SMBL_POST_NOW { get; set; }

        /// <summary>
        ///生安部门
        /// </summary>
        public string? SMBL_DEPT_NAME { get; set; }

        /// <summary>
        ///备案实验室
        /// </summary>
        public string? SMBL_LAB_NAME { get; set; }

        /// <summary>
        ///生物安全培训合格证号
        /// </summary>
        public string? SMBL_TRAIN_CER_NO { get; set; }


        /// <summary>
        ///工作年限
        /// </summary>
        public string? WORK_YEAR { get; set; }


        /// <summary>
        ///从事本岗位年限
        /// </summary>
        public string? POST_YRAR { get; set; }

        /// <summary>
        ///工作类型 
        /// </summary>
        public string? WORK_TYPE { get; set; }

        /// <summary>
        ///部门/科室岗位
        /// </summary>
        public string? DEPARTORLABPOST { get; set; }

        /// <summary>
        ///所有岗位（科室）
        /// </summary>
        public string? LABPOST { get; set; }

        /// <summary>
        ///所有岗位（专业组）
        /// </summary>
        public string? PGROUPPOST { get; set; }

        /// <summary>
        /// 学历/学位
        /// </summary>
        public string DEGREE_NAMEOR_DIPLOMA_NAME { get; set; }

        /// <summary>
        /// 职务/职称名称
        /// </summary>
        public string DUTIES_NAMEORACADEMIC_POST_NAME { get; set; }

        /// <summary>
        /// 出生日期(年)
        /// </summary>
        public string BIRTHDAY_YEAR { get; set; }

        /// <summary>
        ///部门/管理岗位
        /// </summary>
        public string? DEPARTORADMINPOST { get; set; }


        /// <summary>
        ///科室岗备注
        /// </summary>
        public string? LABPOSTREMARK { get; set; }

        /// <summary>
        ///生物安全培训合格证文件列表
        /// </summary>
        public List<object>? SMBL_TRAIN_CER_FILES { get; set; }

        public List<object>? TAGS { get; set; }
    }
}
