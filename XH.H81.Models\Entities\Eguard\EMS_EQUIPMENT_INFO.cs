﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace XH.H81.Models.Entities.Eguard
{
    /// <summary>
    /// 设备信息表
    /// </summary>
    [Table("SYS6_POSITION_DICT")]
    [DBOwner("XH_OA")]
    public class EMS_EQUIPMENT_INFO
    {
        /// <summary>
        /// 设备ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Key]
        public string EQUIPMENT_ID { get; set; }
        /// <summary>
        /// 管理单元ID
        /// </summary>
        public string? UNIT_ID { get; set; }
        /// <summary>
        /// 医疗机构ID
        /// </summary>
        public string? HOSPITAL_ID { get; set; }
        /// <summary>
        /// 设备名称
        /// </summary>
        public string? EQUIPMENT_NAME { get; set; }
        /// <summary>
        /// 设备序号
        /// </summary>
        public string? EQUIPMENT_NUM { get; set; }
        /// <summary>
        /// 设备代号
        /// </summary>
        public string? EQUIPMENT_CODE { get; set; }
        /// <summary>
        /// 设备型号
        /// </summary>
        public string? EQUIPMENT_MODEL { get; set; }
        /// <summary>
        /// 设备类型
        /// </summary>
        public string? EQUIPMENT_TYPE { get; set; }
        /// <summary>
        /// 设备分类
        /// </summary>
        public string? EQUIPMENT_CLASS { get; set; }
        /// <summary>
        /// 设备序列号（门禁的sn）
        /// </summary>
        public string? SERIAL_NUMBER { get; set; }
        /// <summary>
        /// 科室位置ID
        /// </summary>
        public string? POSITION_ID { get; set; }
        /// <summary>
        /// 科室ID
        /// </summary>
        public string? LAB_ID { get; set; }
        /// <summary>
        /// 设备状态 0未启用（管理）1在用 2停用 3报废 4删除
        /// </summary>
        public string? EQUIPMENT_STATE { get; set; }
        public string? SMBL_CLASS { get; set; }
    }
}
