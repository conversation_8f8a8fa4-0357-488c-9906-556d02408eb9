﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text;

namespace XH.H81.Models.Entities.Pms
{
    /// <summary>
    ///技能证书记录表
    /// </summary>
    [DBOwner("XH_OA")]
    public class PMS_SKILL_CERTIFICATE_LIST
    {
        /// <summary>
        ///证书记录ID
        /// </summary>
        [Key]
        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CERTIFICATE_ID { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? RECORD_ID { get; set; }
        /// <summary>
        ///医疗机构ID
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? HOSPITAL_ID { get; set; }
        /// <summary>
        ///人员ID
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? PERSON_ID { get; set; }
        /// <summary>
        ///人员名称
        /// </summary>
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? PERSON_NAME { get; set; }
        /// <summary>
        ///排序号
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CERTIFICATE_SORT { get; set; }
        /// <summary>
        ///证书类型
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CERTIFICATE_TYPE { get; set; }
        /// <summary>
        ///证书类型名称
        /// </summary>
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CERTIFICATE_TYPE_NAME { get; set; }
        /// <summary>
        ///证书级别
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CERTIFICATE_LEVEL { get; set; }
        /// <summary>
        ///证书级别名称
        /// </summary>
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CERTIFICATE_LEVEL_NAME { get; set; }
        /// <summary>
        ///证书名称
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CERTIFICATE_NAME { get; set; }
        /// <summary>
        ///发证机构
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CERTIFICATE_UNIT { get; set; }
        /// <summary>
        ///获取时间
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? OBTAIN_TIME { get; set; }
        /// <summary>
        ///发证日期
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CERTIFICATE_DATE { get; set; }
        /// <summary>
        ///证书有效期
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CERTIFICATE_VALIDITY { get; set; }
        /// <summary>
        ///证书编号
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CERTIFICATE_NUMBER { get; set; }
        /// <summary>
        ///附件
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CERTIFICATE_AFFIX { get; set; }
        /// <summary>
        ///附件名称
        /// </summary>
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? AFFIX_NAME { get; set; }
        /// <summary>
        ///指定审核人员
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? SELECT_CHECK_PERSON { get; set; }
        /// <summary>
        ///指定审核人员名称
        /// </summary>
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? SELECT_CHECK_PERSON_NAME { get; set; }
        /// <summary>
        ///审核时间
        /// </summary>
        public DateTime? CHECK_TIME { get; set; }
        /// <summary>
        ///审核人员
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CHECK_PERSON { get; set; }
        /// <summary>
        ///审核人员名称
        /// </summary>
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CHECK_PERSON_NAME { get; set; }
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CHECK_STATE { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CHECK_STATE_NAME { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? NEW_STATE { get; set; }

        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? NEW_STATE_NAME { get; set; }
        /// <summary>
        ///状态
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CERTIFICATE_STATE { get; set; }


        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CERTIFICATE_STATE_NAME { get; set; }
        /// <summary>
        ///首次登记人
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FIRST_RPERSON { get; set; }
        /// <summary>
        ///首次登记时间
        /// </summary>
        public DateTime? FIRST_RTIME { get; set; }
        /// <summary>
        ///最后修改人员
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? LAST_MPERSON { get; set; }
        /// <summary>
        ///最后修改时间
        /// </summary>
        public DateTime? LAST_MTIME { get; set; }

        /// <summary>
        /// 规评组合id
        /// </summary>
        public string? EPLAN_COM_ID { get; set; }
        /// <summary>
        ///备注
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? REMARK { get; set; }

        /// <summary>
        /// 字段和所属值
        /// </summary>
        public string? RECORD_DATA { get; set; }

        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public List<PMS_PERSON_FILE>? PMS_PERSON_FILE { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? SORT_NUM { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? SUBMIT_STATE { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? REJECT_REASON { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? REJECT_DATE { get; set; }

        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? REJECT_PERSON { get; set; }
        /// <summary>
        /// 科室名称
        /// </summary>

        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? LAB_NAME { get; set; }

        /// <summary>
        /// 专业组名称
        /// </summary>
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? PGROUP_NAME { get; set; }

        /// <summary>
        /// 人员id
        /// </summary>

        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? USER_ID { get; set; }
        /// <summary>
        /// 工号
        /// </summary>

        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? HIS_ID { get; set; }
        /// <summary>
        /// 姓名
        /// </summary>

        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? USER_NAME { get; set; }

        /// <summary>
        /// 人员组合名称
        /// </summary>
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? USER_COM_NAME { get; set; }

        /// <summary>
        /// 生安标志
        /// </summary>
        public string? SMBL_FLAG { get; set; }

        /// <summary>
        /// 证书字典ID
        /// </summary>
        public string? CERTIFICATE_DID { get; set; }

        /// <summary>
        /// 证书类型名称
        /// </summary>
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)] 
        public string? CERTIFICATE_DID_NAME { get; set; }

        /// <summary>
        /// 证书效期值
        /// </summary>
        public string? CER_EXPIRY_VALUE { get; set; }

        /// <summary>
        /// 证书效期值类型(M-月 Q-季度 Y-年)
        /// </summary>
        public string? CER_EXPIRY_TYPE { get; set; }

        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? RELATE_CLASS_NAME { get; set; }
    }
}
