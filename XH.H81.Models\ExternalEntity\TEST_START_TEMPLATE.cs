﻿using H.Utility.SqlSugarInfra;
using ProtoBuf;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.ExternalEntity
{
    [DBOwner("XH_SYS")]
    public class TEST_START_TEMPLATE
    {
        [Key]
        [SugarColumn(IsPrimaryKey = true)]
        public string ID { get; set; }
        public string? NAME { get; set; }
        public int? VALUE { get; set; }
        public DateTime? CREATE_TIME { get; set; }    

    }
}
