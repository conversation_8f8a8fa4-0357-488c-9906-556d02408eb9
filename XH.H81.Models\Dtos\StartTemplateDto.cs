﻿using AutoMapper;
using AutoMapper.Configuration.Annotations;
using System.ComponentModel.DataAnnotations;
using XH.H81.Models.ExternalEntity;

namespace XH.H81.Models.Dtos
{

    [AutoMap(typeof(TEST_START_TEMPLATE),ReverseMap = true)]
    public class StartTemplateDto
    {
        /// <summary>
        /// ID
        /// </summary>
        [Required(ErrorMessage ="ID必填")]
        [SourceMember("ID")]
        public string Id { get; set; }
        /// <summary>
        /// 名称
        /// </summary>
        [SourceMember("NAME")]
        public string Name { get; set; }
        public string Name1 { get; set; }
        public string Name2 { get; set; }
        public string Name3 { get; set; }
        public string Name4 { get; set; }
        public string Name5 { get; set; }
        public string Name6 { get; set; }
        public string Name7 { get; set; }
        public string Value { get; set; }

    }
}
