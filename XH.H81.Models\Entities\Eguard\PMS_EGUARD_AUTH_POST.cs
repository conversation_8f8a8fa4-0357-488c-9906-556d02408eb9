﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Entities.Eguard
{
    /// <summary>
    /// 门禁授权岗位/人员关系表
    /// </summary>
    [DBOwner("XH_OA")]
    public class PMS_EGUARD_AUTH_POST
    {
        /// <summary>
        /// 门禁授权岗位ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string EGUARD_AUTH_PID { get; set; }
        /// <summary>
        /// 门禁授权ID
        /// </summary>
        public string EGUARD_AUTH_ID { get; set; }
        /// <summary>
        /// 授权类型
        /// </summary>
        public string EGUARD_AUTH_TYPE { get; set; }
        /// <summary>
        /// 适用范围
        /// </summary>
        public string EPLAN_APPLY_TYPE { get; set; }
        /// <summary>
        /// 授权数据ID
        /// </summary>
        public string EGUARD_DATA_ID { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public string EGUARD_AUTH_PSTATE { get; set; }
        public string? FIRST_RPERSON { get; set; }
        public DateTime? FIRST_RTIME { get; set; }
        public string? LAST_MPERSON { get; set; }
        public DateTime? LAST_MTIME { get; set; }
        public string? REMARK { get; set; }
    }
}
