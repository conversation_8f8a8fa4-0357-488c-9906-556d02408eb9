﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net6.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <GenerateDocumentationFile>True</GenerateDocumentationFile>
    </PropertyGroup>

    <ItemGroup>
      <Compile Remove="Configs\**" />
      <EmbeddedResource Remove="Configs\**" />
      <None Remove="Configs\**" />
    </ItemGroup>

    <ItemGroup>
      <Compile Remove="Dtos\Pms\ProfessionGroupDto.cs" />
      <Compile Remove="Dtos\UploadFileDto.cs" />
      <Compile Remove="Dtos\UploadZipDto.cs" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="AutoMapper" Version="12.0.1" />
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="6.0.16" />
        <PackageReference Include="protobuf-net.Core" Version="3.1.22" />

    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\XH.H81.Base\XH.H81.Base.csproj" />
    </ItemGroup>

</Project>
