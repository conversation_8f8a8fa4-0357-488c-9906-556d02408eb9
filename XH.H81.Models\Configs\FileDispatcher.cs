﻿//using H.Utility;
//using Newtonsoft.Json;
//using Newtonsoft.Json.Linq;
//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Text;
//using System.Threading.Tasks;
//using XH.H81.Models.Configs;

//namespace XH.H81.Models.Configs
//{
//    /// <summary>
//    /// 调度执行的上下文信息，默认resultKind = 方法不匹配；success = false； dataChain = new List<object>()
//    /// </summary>
//    public class FileDispatcherContext
//    {
//        /// <summary>
//        /// 当前附件文件的物理路径+文件名+后缀
//        /// </summary>
//        public string PysicalPath { get; set; }

//        /// <summary>
//        /// 所在的相对路径和文件名以及后缀
//        /// </summary>
//        public string[] PathList { get; set; }

//        /// <summary>
//        /// 当前层级路径的字符串
//        /// </summary>
//        public string CurrentPath
//        {
//            get { return PathList[Level-1]; }
//        }

//        /// <summary>
//        /// 所在的路径层级
//        /// </summary>
//        public int Level { get; internal set; }

//        /// <summary>
//        /// 记录每个路径执行的结果的字典
//        /// </summary>
//        public Dictionary<string,object> DataDict{ get; set; }

//        /// <summary>
//        /// 调度执行是否成功
//        /// </summary>
//        public bool Success { get; set; } = false;

//        /// <summary>
//        /// 附带消息
//        /// </summary>
//        public string? Msg { get; set; } = string.Empty;

//    }


    


//    public class FileFunctionDispatcher
//    {
//        /// <summary>
//        /// 本节点方法
//        /// </summary>
//        Func<FileDispatcherContext, FileDispatcherContext> Func;

//        /// <summary>
//        /// 子节点方法列表
//        /// </summary>
//        private List<FileFunctionDispatcher> ChildrenFunc{ get; set; } = new List<FileFunctionDispatcher>();

//        /// <summary>
//        /// 私有构建方法
//        /// </summary>
//        /// <param name="func"></param>
//        private  FileFunctionDispatcher(Func<FileDispatcherContext, FileDispatcherContext> func)
//        {
//            Func = func;
//        }

//        /// <summary>
//        /// 构建方法编排的入口
//        /// </summary>
//        /// <returns></returns>
//        static public FileFunctionDispatcher GetRoot()
//        {
//            return new FileFunctionDispatcher(a => a);
//        }

//        /// <summary>
//        /// 当前成功后自动执行下一个方法，否则中断
//        /// </summary>
//        /// <param name="children"></param>
//        /// <returns></returns>
//        public FileFunctionDispatcher Then(Func<FileDispatcherContext, FileDispatcherContext> func)
//        {
//            FileFunctionDispatcher childFunc = new FileFunctionDispatcher(func);
//            ChildrenFunc.Add(childFunc);
//            return childFunc;
//        }

//        /// <summary>
//        /// 当前成功后自动执行下一层级方法
//        /// </summary>
//        /// <param name="children"></param>
//        /// <returns></returns>
//        public FileFunctionDispatcher Then(params FileFunctionDispatcher[] children)
//        {
//            ChildrenFunc.AddRange(children);
//            return this;
//        }


//        /// <summary>
//        /// 构建同一目录层级的处理方法
//        /// （同级如果有多个ToTry，则当前失败后才会执行下一个）
//        /// </summary>
//        /// <param name="func"></param>
//        /// <returns></returns>
//        public FileFunctionDispatcher ToTry(Func<FileDispatcherContext, FileDispatcherContext> func)
//        {
//            return new FileFunctionDispatcher(func);
//        }

//        /// <summary>
//        /// 开始调度执行
//        /// </summary>
//        /// <param name="pathFile"></param>
//        /// <param name="physicalPath"></param>
//        /// <param name="dataDict"></param>
//        /// <returns></returns>
//        public FileDispatcherContext Start(string pathFile, string physicalPath, Dictionary<string, object> dataDict)
//        {
//            var context = new FileDispatcherContext();
//            context.Level = 1;
//            context.PysicalPath = pathFile;
//            context.PathList = pathFile.Substring(physicalPath.Length).Replace('\\', '/').TrimStart('/').Split('/');
//            if (dataDict == null) dataDict = new Dictionary<string, object>();
//            context.DataDict = dataDict;
//            return this.Run(context);
//        }

//        private FileDispatcherContext Run(FileDispatcherContext beforeContext)
//        {
//            FileDispatcherContext newContext = beforeContext;
//            foreach (var fun in this.ChildrenFunc)
//            {
//                newContext = fun.Func(beforeContext);
//                if (newContext.Success == true)
//                {
//                    newContext.Level += 1;
//                    newContext = fun.Run(newContext);
//                    break;
//                }
//            }
//            return newContext;
//        }
//    }

//}


