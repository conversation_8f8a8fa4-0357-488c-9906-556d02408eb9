﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Entities.EvaluatePlan
{
    [DBOwner("XH_SYS")]
    public class SYS6_LIMIT_ROLE_DICT
    {
        [Key]
        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        /// <summary>
        /// 限权组合ID
        /// </summary>
        public string? LROLE_NO { get; set; }

        /// <summary>
        /// 医疗机构ID
        /// </summary>
        public string HOSPITAL_ID { get; set; }

        /// <summary>
        /// 组合名称
        /// </summary>
        public string LROLE_NAME { get; set; }

        /// <summary>
        /// 组合描述
        /// </summary>
        public string? LROLE_DESCRIPTION { get; set; }

        /// <summary>
        /// 组合排序号
        /// </summary>
        public string? LROLE_SORT { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public string? LROLE_STATE { get; set; }

        /// <summary>
        /// 首次登记人
        /// </summary>
        public string? FIRST_RPERSON { get; set; }

        /// <summary>
        /// 首次登记时间
        /// </summary>
        public DateTime? FIRST_RTIME { get; set; }

        /// <summary>
        /// 最后修改人员
        /// </summary>
        public string? LAST_MPERSON { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime? LAST_MTIME { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? REMARK { get; set; }
    }
}
