﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Dtos.Eguard
{
    public class EguardTreeDto
    {
        /// <summary>
        /// 房间ID
        /// </summary>
        public string POSITION_ID { get; set; }
        /// <summary>
        /// 房间名
        /// </summary>
        public string POSITION_NAME { get; set; }
        public List<EguardInfoDto> EguardTreeSecondNode { get; set; }
    }


    /// <summary>
    /// 按照门禁勾选门禁组合
    /// </summary>
    public class EguardTableDto
    {
        /// <summary>
        /// 门禁位置ID
        /// </summary>
        public string? EGRUAD_ADDR_ID { get; set; }
        /// <summary>
        /// 门禁位置名称
        /// </summary>
        public string? EGRUAD_ADDR { get; set; }
        /// <summary>
        /// 门禁设备ID
        /// </summary>
        public string? EGUARD_ID { get; set; }
        /// <summary>
        /// 门禁设备名称
        /// </summary>
        public string? EGUARD_NAME { get; set; }
        /// <summary>
        /// 门禁状态
        /// </summary>
        public string? EGUARD_STATE { get; set; }
        /// <summary>
        /// 门禁组合ID
        /// </summary>
        public List<string>? EGUARD_COM_ID { get; set; }
        /// <summary>
        /// 组合数量
        /// </summary>
        public int? EGUARD_COM_AMOUNT { get; set; }
        public string? FIRST_RPERSON { get; set; }
        public DateTime? FIRST_RTIME { get; set; }
        public string? LAST_MPERSON { get; set; }
        public DateTime? LAST_MTIME { get; set; }
        public string? ECOMBINE_STATE { get; set; }
    }


    /// <summary>
    /// 门禁选择组合
    /// </summary>
    public class EditEguardTableDto
    {
        /// <summary>
        /// 门禁组合ID
        /// </summary>
        public List<string>? EGUARD_COM_IDs { get; set; }
        /// <summary>
        /// 门禁设备ID
        /// </summary>
        public string? EQUIPMENT_ID { get; set; }
        /// <summary>
        /// 启用禁用
        /// </summary>
        public string? EQUIPMENT_STATE { get; set; }
        /// <summary>
        /// 操作类型 add  delete
        /// </summary>
        public string? OPERATE_TYPE { get; set; }
    }
}
