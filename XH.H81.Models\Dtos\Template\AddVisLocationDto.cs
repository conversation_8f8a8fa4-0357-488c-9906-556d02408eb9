﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Dtos.Template
{
    /// <summary>
    /// 增加模板记录信息
    /// </summary>
    public class AddVisLocationDto
    {
        /// <summary>
        /// 科室ID
        /// </summary>
        public string LAB_ID { get; set; }

        /// <summary>
        /// 分类CODE码
        /// </summary>
        public string STYLE_CLASS_CODE { get; set; }

        /// <summary>
        /// 客户端标识;0:杏通 1:web
        /// </summary>
        public string FLAG { get; set; }

        /// <summary>
        /// 功能标识:用于前端区分一个页面多外需要定位的情况下 位置信息 如果没有特殊要求则存STYLEID即可;如果界面还有一些比如时间、条件，则自行拼json
        /// </summary>
        public string LOCATION_INFO { get; set; }

        /// <summary>
        /// 扩展信息;扩展存储信息，比如pdfis的缩放，访问位置高度等记录信息
        /// </summary>
        public string EXT_INFO { get; set; }
    }
}
