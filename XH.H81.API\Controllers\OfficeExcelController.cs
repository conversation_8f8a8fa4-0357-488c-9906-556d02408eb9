﻿using H.Utility;
using H.Utility.Helper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using XH.H81.IServices;
using XH.H81.Models.Dtos;
using XH.LAB.UTILS.Interface;
using static XH.LAB.UTILS.Implement.H115OnlyOfficeService;

namespace XH.H81.API.Controllers
{

    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize]
    public class OfficeExcelController : ControllerBase
    {
        private readonly IH115OnlyOfficeService _h115OnlyOfficeService;
        private readonly IBaseDataServices _baseDataServices;
        private readonly IModuleLabGroupService _moduleLabGroupService;
        public OfficeExcelController(IH115OnlyOfficeService h115OnlyOfficeService, IBaseDataServices baseDataServices, IModuleLabGroupService moduleLabGroupService)
        { _h115OnlyOfficeService = h115OnlyOfficeService; _baseDataServices = baseDataServices; _moduleLabGroupService = moduleLabGroupService; }


        /// <summary>
        /// H115 GetLabPGroupList  接口
        /// </summary>
        /// <param name="LAB_ID">科室ID</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetLabPGroupList(string LAB_ID)
        {
            return Ok(_h115OnlyOfficeService.GetLabPGroupList(LAB_ID));
        }


        /// <summary>
        /// H115 GetTemplateList 接口
        /// </summary>
        /// <param name="HOSPITAL_ID">机构ID</param>
        /// <param name="LAB_ID">科室ID</param>
        /// <param name="AREA_ID">院区ID</param>
        /// <param name="PGROUP_ID">检验专业组ID</param>
        /// <param name="LAB_PGROUP_TYPE">专业组科室分类，0科室，1专业组 ，默认1</param>
        /// <param name="STYLE_CLASS_CODE">模板分类code</param>
        /// <param name="DATA_ID">数据id</param>
        /// <param name="STYLE_NAME">模板名称</param>
        /// <param name="CATALOG_NAME">目录名称</param>
        /// <param name="STYLE_IDS">模板styleId集合 “，”分割</param>
        /// <param name="OFFICE_FLAG"> 0:只有excel清单配置 1:只有word模板配置 2:word+excel都有(默认)</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetTemplateList(string HOSPITAL_ID, string LAB_ID, string? AREA_ID, string? PGROUP_ID, string? LAB_PGROUP_TYPE, string STYLE_CLASS_CODE, string? DATA_ID, string? STYLE_NAME, string? CATALOG_NAME, string? STYLE_IDS,string? OFFICE_FLAG)
        {
            return Ok(_h115OnlyOfficeService.GetTemplateList(HOSPITAL_ID, LAB_ID, AREA_ID, PGROUP_ID, LAB_PGROUP_TYPE, STYLE_CLASS_CODE, DATA_ID, STYLE_NAME, CATALOG_NAME, STYLE_IDS, OFFICE_FLAG));
        }

        /// <summary>
        /// 新增当前模块访问记录
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult AddVisLocation(AddVisLocationInput input)
        {
            return Ok(_h115OnlyOfficeService.AddVisLocation(input));
        }

        /// <summary>
        /// 获取当前模块上次访问记录
        /// </summary>
        /// <param name="labId">科室id</param>
        /// <param name="styleClassCode">模板分类code</param>
        /// <param name="flag">前端标识，无需要则不传</param>
        /// <param name="loadMode">客户端标识 0 杏通   1 web</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetLastVisLocation(string labId, string styleClassCode, string? flag, string loadMode = "1")
        {
            return Ok(_h115OnlyOfficeService.GetLastVisLocation(labId,styleClassCode,flag,loadMode));
        }



        /// <summary>
        /// 装载excel数据并导出excel文件(填充页眉页脚) 返回xlxs
        /// </summary>
        /// <param name="STYLE_ID">模板id</param>
        /// <param name="FILE">文件</param>
        /// <param name="FIELDS">页眉页脚数据项key:value JSON</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult ExportExcelFile(string STYLE_ID, IFormFile FILE, string FIELDS)
        {
            var claim = User.ToClaimsDto();
            Dictionary<string,string>Dic=JsonHelper.FromJson<Dictionary<string, string>>(FIELDS);
            int SEASON = 0;
            if (DateTime.Now.Month >= 1 && DateTime.Now.Month <= 3)
            {
                SEASON = 1;
            }
            else if (DateTime.Now.Month >= 4 && DateTime.Now.Month <= 6)
            {
                SEASON = 2;
            }
            else if (DateTime.Now.Month >= 7 && DateTime.Now.Month <= 9)
            {
                SEASON = 3;
            }
            else if (DateTime.Now.Month >= 10 && DateTime.Now.Month <= 12)
            {
                SEASON = 4;
            }
            Dic.Add("YEAR_MOUNTH_DAY", DateTime.Now.ToString("yyyy-MM-dd"));
            Dic.Add("YEAR_MOUNTH", DateTime.Now.ToString("yyyy-MM"));
            Dic.Add("YEAR", DateTime.Now.Year.ToString());
            Dic.Add("MONTH", DateTime.Now.Month.ToString());
            Dic.Add("DAY", DateTime.Now.Day.ToString());
            Dic.Add("HIS_NAME", claim.HIS_NAME);
            Dic.Add("SEASON", SEASON.ToString());
            Dic.Add("CURRENT_TIME", DateTime.Now.ToString());
            string areaId = "";
            if (Dic.Keys.Contains("AREA_ID")&& Dic["AREA_ID"]!=null)
            {
                areaId = Dic["AREA_ID"].ToString();
                Dic.Add("AREA_NAME", _moduleLabGroupService.GetAreaName(areaId));
            }
            string json=JsonHelper.ToJson(Dic);
            var data = _baseDataServices.ExportExcelFile(STYLE_ID, FILE, json);
            string contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            return File(data, contentType);
        }
    }



}
