﻿using Elastic.Clients.Elasticsearch;
using H.Utility;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Org.BouncyCastle.Ocsp;
using XH.H81.IServices;
using XH.H81.Models.Dtos;
using XH.H81.Models.Dtos.Tag;
using XH.H81.Models.Entities;
using XH.H81.Services;
using XH.LAB.UTILS.Models;

namespace XH.H81.API.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize]
    public class PmsTagController : ControllerBase
    {
        private IPmsTagService _pmsTagService;
        private readonly IBaseDataServices _IBaseDataService;
        private readonly IEvaluatePlanService _IEvaluatePlanService;
        public PmsTagController(IPmsTagService pmsTagService, IBaseDataServices ibasedataService, IEvaluatePlanService iEvaluatePlanService)
        {
            _pmsTagService = pmsTagService;
            _IBaseDataService = ibasedataService;
            _IEvaluatePlanService = iEvaluatePlanService;
        }
        /// <summary>
        /// 获取标签字典
        /// </summary>
        /// <param name="tagClass">标签分类</param>
        /// <param name="tagName">标签名称</param>
        /// <param name="state">状态 1在用 2禁用 0删除</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetTagDict(string? tagClass, string? tagName, string? state)
        {
            var claim = this.User.ToClaimsDto();
            var Result = _pmsTagService.GetTagDict(tagClass, tagName, state, claim.HOSPITAL_ID);
            return Ok(Result);
        }


        /// <summary>
        /// 保存标签字典
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult SaveTagDict(PmsPersonTagDictDto dto)
        {
            var claim = this.User.ToClaimsDto();
            var Result = _pmsTagService.SaveTagDict(dto, claim.USER_NAME, claim.HOSPITAL_ID);
            return Ok(Result);
        }

        /// <summary>
        /// 更新标签字典状态
        /// </summary>
        /// <param name="tagId"></param>
        /// <param name="state"></param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult UpdateTagDictState(string tagId, string state)
        {
            var claim = this.User.ToClaimsDto();
            var Result = _pmsTagService.UpdateTagDictState(tagId, state, claim.USER_NAME);
            return Ok(Result);
        }

        /// <summary>
        /// 获取标签待选人员列表
        /// </summary>
        /// <param name="tagId">标签id</param>
        /// <param name="labPgroupId">科室或专业组id</param>
        /// <param name="personName">人员姓名</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetUnSelectPersonTag(string? tagId, string? labPgroupId, string? personName)
        {
            var claim = this.User.ToClaimsDto();
            var Result = _pmsTagService.GetUnSelectPersonTag(tagId, labPgroupId, personName, claim.HOSPITAL_ID);
            return Ok(Result);
        }


        /// <summary>
        /// 获取标签已选人员列表
        /// </summary>
        /// <param name="tagId">标签id</param>
        /// <param name="labPgroupId">科室或专业组id</param>
        /// <param name="personName">人员姓名</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetSelectPersonTag(string tagId, string? labPgroupId, string? personName)
        {
            var claim = this.User.ToClaimsDto();
            var Result = _pmsTagService.GetSelectPersonTag(tagId, labPgroupId, personName, claim.HOSPITAL_ID);
            return Ok(Result);
        }

        /// <summary>
        /// 按人员获取人员标签列表信息
        /// </summary>
        /// <param name="labPgroupId"></param>
        /// <param name="personName"></param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetPersonTag(string? labPgroupId, string? personName)
        {
            var claim = this.User.ToClaimsDto();
            var Result = _pmsTagService.GetPersonTag(labPgroupId, personName, claim.HOSPITAL_ID);
            return Ok(Result);
        }

        /// <summary>
        /// 保存人员标签（按人员）
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult SavePersonTag(PersonTagRelateDto personTagRelateDto)
        {
            var claim = this.User.ToClaimsDto();
            var Result = _pmsTagService.SavePersonTag(personTagRelateDto.ListTag, personTagRelateDto.ListPerson, claim.HIS_NAME);
            return Ok(Result);
        }

        /// <summary>
        /// 标签增加人员
        /// </summary>
        /// <param name="tagIds">标签id 支持多个,隔开</param>
        /// <param name="personIds">人员id 多个,隔开</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult AddPersonTag(string tagIds, string personIds)
        {
            var claim = this.User.ToClaimsDto();
            var Result = _pmsTagService.AddPersonTag(tagIds, personIds, claim.HIS_NAME);
            return Ok(Result);
        }

        /// <summary>
        /// 标签删除人员
        /// </summary>
        /// <param name="tagIds">标签ids 支持多个,隔开</param>
        /// <param name="personIds">人员id 多个,隔开</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult DeletePersonTag(string tagIds, string personIds)
        {
            var claim = this.User.ToClaimsDto();
            var Result = _pmsTagService.DeletePersonTag(tagIds, personIds, claim.HIS_NAME);
            return Ok(Result);
        }

        /// <summary>
        /// 获取标签分类
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetTagClass()
        {
            var claim = this.User.ToClaimsDto();
            var Result = _pmsTagService.GetTagClass();
            return Ok(Result.ToResultDto());
        }


        /// <summary>
        /// 获取标签分类子集
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetTagClassChild(string id)
        {
            var claim = this.User.ToClaimsDto();
            var Result = _pmsTagService.GetTagClassChild(id);
            return Ok(Result.ToResultDto());
        }

        /// <summary>
        /// 获取IOS标签名称树
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult GetISOTagNameTree(PersonTreeParm parm)
        {
            var claim = this.User.ToClaimsDto();
            string hospital_id = parm.HOSPITAL_ID ?? claim.HOSPITAL_ID;
            var orgParm = new OrgUserParams { area_id = parm.AREA_ID, hospital_id = hospital_id, lab_id = parm.LAB_ID, smbl_lab_id = parm.SMBL_LAB_ID, pgroup_id = parm.PGROUP_ID, user_id = claim.USER_NO };
            var Result = _pmsTagService.GetISOTagNameTree(orgParm);
            return Ok(Result);
        }

        /// <summary>
        /// 获取标签树
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetTagTree(bool? filterIso)
        {
            var claim = this.User.ToClaimsDto();
            var Result = _pmsTagService.GetTagTree(filterIso, claim.HOSPITAL_ID);
            return Ok(Result);
        }
        /// <summary>
        /// 获取当前“人员标签”已选中的“档案类型”列表
        /// </summary>
        /// <param name="personTagId"></param>
        /// <param name="keyword"></param>
        /// <param name="hisName"></param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetPersonTagSelectedClasses(string personTagId, string? keyword)
        {
            var claim = this.User.ToClaimsDto();
            var Result = _pmsTagService.GetPersonTagSelectedClasses(personTagId, keyword, claim.HIS_NAME);
            return Ok(Result.ToResultDto());
        }

        /// <summary>
        /// 获取当前“人员标签”未选中的“档案类型”列表
        /// </summary>
        /// <param name="personTagId"></param>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetPersonTagUnselectedClasses(string personTagId, string? keyword)
        {
            var claim = this.User.ToClaimsDto();
            var Result = _pmsTagService.GetPersonTagUnselectedClasses(personTagId, keyword, claim.HIS_NAME);
            return Ok(Result.ToResultDto());
        }

        /// <summary>
        /// 取消“档案类型”与当前“人员标签”的关联关系
        /// </summary>
        /// <param name="personTagId"></param>
        /// <param name="classIdList"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult RemovePersonTagClassAssociation(string personTagId, [FromBody] List<string> classIdList)
        {
            var claim = this.User.ToClaimsDto();
            var Result = _pmsTagService.RemovePersonTagClassAssociation(personTagId, classIdList, claim.HIS_NAME);
            return Ok(Result.ToResultDto());
        }

        /// <summary>
        /// 建立“档案类型”与当前“人员标签”的关联关系
        /// </summary>
        /// <param name="personTagId"></param>
        /// <param name="classIdList"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult AddPersonTagClassAssociation(string personTagId, [FromBody] List<string> classIdList)
        {
            var claim = this.User.ToClaimsDto();
            var Result = _pmsTagService.AddPersonTagClassAssociation(personTagId, classIdList, claim.HIS_NAME);
            return Ok(Result.ToResultDto());
        }
        /// <summary>
        /// 获取维护数据项记录信息树
        /// </summary>
        /// <param name="hospital_id"></param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetClassDataInfo(string? hospital_id)
        {
            var Result = _pmsTagService.GetClassDataInfo(hospital_id);
            return Ok(Result.ToResultDto());
        }


        /// <summary>
        /// 获取维护数据项基础数据及标签
        /// </summary>
        /// <param name="classId"></param>
        /// <param name="tagId"></param>
        /// <param name="keyWord"></param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetBaseDataAndTag(string classId, string? stateFlag, string? tagId, string? keyWord)
        {
            var Result = _pmsTagService.GetBaseDataAndTag(classId, stateFlag, tagId, keyWord);
            return Ok(Result.ToResultDto());
        }

        /// <summary>
        /// 批量获取基础数据及标签
        /// </summary>
        /// <param name="menuName">页面名称：空表示获取所有基础数据；BaseInfo表示只获取基本信息页面下拉数据；Record表示只获取人事记录页面下拉数据</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult BatchGetBaseDataAndTag(string? menuName)
        {
            var Result = _pmsTagService.BatchGetBaseDataAndTag(menuName, User.ToClaimsDto().HOSPITAL_ID);
            return Ok(Result.ToResultDto());
        }



        /// <summary>
        /// 保存维护数据项基础数据及标签
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult SaveBaseDataAndTag(OaBaseDataAndTagDto dto)
        {
            var Result = _pmsTagService.SaveBaseDataAndTag(dto);
            return Ok(Result.ToResultDto());
        }


        /// <summary>
        /// 删除维护数据项基础数据及标签
        /// </summary>
        /// <param name="Data"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult DeleteBaseDataAndTag(OaBaseDataAndTagDto Data)
        {
            var Result = _pmsTagService.DeleteBaseDataAndTag(Data);
            return Ok(Result.ToResultDto());
        }

        /// <summary>
        /// 启用维护数据项基础数据及标签
        /// </summary>
        /// <param name="Data"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult EnableBaseDataAndTag(OaBaseDataAndTagDto Data)
        {
            var Result = _pmsTagService.EnableBaseDataAndTag(Data);
            return Ok(Result.ToResultDto());
        }
        /// <summary>
        /// 禁用维护数据项基础数据及标签
        /// </summary>
        /// <param name="Data"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult DisableBaseDataAndTag(OaBaseDataAndTagDto Data)
        {
            var Result = _pmsTagService.DisableBaseDataAndTag(Data);
            return Ok(Result.ToResultDto());
        }
        /// <summary>
        /// 维护数据项基础数据及标签排序
        /// </summary>
        /// <param name="sortedData"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult SortBaseDataAndTag(List<OaBaseDataAndTagDto> sortedData)
        {
            var Result = _pmsTagService.SortBaseDataAndTag(sortedData);
            return Ok(Result.ToResultDto());
        }


        /// <summary>
        /// 获取维护数据项证书类型
        /// </summary>
        /// <param name="hospitalId"></param>
        /// <param name="cerType"></param>
        /// <param name="state"></param>
        /// <param name="ePlanFlag"></param>
        /// <param name="searchKey"></param>
        /// <param name="tagId"></param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetCertificateDictAndTag(string HOSPITAL_ID, string? CER_TYPE, string? STATE_FLAG, string? EPLAN_FLAG, string? SEARCH_KEY, string? PERSON_TAG_ID)
        {
            var result = _IEvaluatePlanService.GetCertificateDictAndTag(HOSPITAL_ID, CER_TYPE, STATE_FLAG, EPLAN_FLAG, SEARCH_KEY, PERSON_TAG_ID);
            return Ok(result.ToResultDto());
        }


        /// <summary>
        /// 保存维护数据项证书类型
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult SaveCertificateDictAndTag(OaCertificateDict dto)
        {
            var claim = this.User.ToClaimsDto();
            var result = _IEvaluatePlanService.SaveCertificateDictAndTag(dto, claim.HIS_NAME);
            return Ok(result.ToResultDto());
        }

        /// <summary>
        /// 启用证书
        /// </summary>
        /// <param name="CERTIFICATE_DID"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult EnableCertificateDictAndTag(string CERTIFICATE_DID)
        {
            var claim = this.User.ToClaimsDto();
            var result = _IEvaluatePlanService.EnableCertificateDict(CERTIFICATE_DID, isDisable: false, claim.HIS_NAME);
            return Ok(result.ToResultDto());
        }

        /// <summary>
        /// 禁用证书
        /// </summary>
        /// <param name="CERTIFICATE_DID"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult DisableCertificateDictAndTag(string CERTIFICATE_DID)
        {
            var claim = this.User.ToClaimsDto();
            var result = _IEvaluatePlanService.EnableCertificateDict(CERTIFICATE_DID, isDisable: true, claim.HIS_NAME);
            return Ok(result.ToResultDto());
        }

        /// <summary>
        /// 删除证书
        /// </summary>
        /// <param name="CERTIFICATE_DID"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult DeleteCertificateDictAndTag(string CERTIFICATE_DID)
        {
            var claim = this.User.ToClaimsDto();
            var result = _IEvaluatePlanService.DeleteCertificateDict(CERTIFICATE_DID, claim.HIS_NAME);
            return Ok(result.ToResultDto());
        }
        /// <summary>
        /// 证书排序
        /// </summary>
        /// <param name="sortedDataIds"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult SortCertificateDictAndTag(List<string> sortedDataIds)
        {
            var result = _IEvaluatePlanService.SortCertificateDict(sortedDataIds);
            return Ok(result.ToResultDto());
        }

        /// <summary>
        /// 获取评估阶段字典
        /// </summary>
        /// <param name="stageType">评估阶段类型</param>
        /// <param name="stageClass">字典分类</param>
        /// <param name="tagId"></param>
        /// <param name="eplanFlag"></param>
        /// <param name="keyWord"></param>
        /// <param name="stateFlag"></param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetEvalStageDict(string stageType, string? stageClass, string? tagId, string? eplanFlag, string? keyWord, string? stateFlag)
        {
            var result = _pmsTagService.GetEvalStageDict(stageType, stageClass, tagId, eplanFlag, keyWord, stateFlag);
            return Ok(result.ToResultDto());
        }

        /// <summary>
        /// 新增或修改评估阶段配置
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [HttpPost]
        public IActionResult SaveEvalStageDict(OaEvalStageDictDto dto)
        {
            var result = _pmsTagService.SaveEvalStageDict(dto);
            return Ok(result.ToResultDto());
        }


        /// <summary>
        /// 删除评估阶段
        /// </summary>
        /// <param name="stageId"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [HttpPost]
        public IActionResult DeleteEvalStageDict(string stageId)
        {
            var result = _pmsTagService.DeleteEvalStageDict(stageId);
            return Ok(result.ToResultDto());
        }

        /// <summary>
        /// 启用评估阶段
        /// </summary>
        /// <param name="stageId"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [HttpPost]
        public IActionResult EnableEvalStageDict(string stageId)
        {
            var result = _pmsTagService.EnableEvalStageDict(stageId);
            return Ok(result.ToResultDto());
        }

        /// <summary>
        /// 禁用评估阶段
        /// </summary>
        /// <param name="stageId"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [HttpPost]
        public IActionResult DisableEvalStageDict(string stageId)
        {
            var result = _pmsTagService.DisableEvalStageDict(stageId);
            return Ok(result.ToResultDto());
        }

        /// <summary>
        /// 评估阶段排序
        /// </summary>
        /// <param name="stageIds"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult SortEvalStageDict(List<string> stageIds)
        {
            var result = _pmsTagService.SortEvalStageDict(stageIds);
            return Ok(result.ToResultDto());
        }

        /// <summary>
        /// 创建标签表单
        /// </summary>
        /// <param name="tagId">标签id</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult CreateComplexForms(string tagId)
        {
            var claim = this.User.ToClaimsDto();
            var result = _pmsTagService.CreateComplexForms(tagId, claim.HIS_NAME, claim.HOSPITAL_ID);
            return Ok(result);
        }
        /// <summary>
        /// 获取复杂表单信息
        /// </summary>
        /// <param name="setUpId">标签id</param>
        /// <param name="merge">是否合并字典json</param>
        /// <param name="qunitId">单元 预留</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetComplexForms(string setUpId, string merge, string?qunitId)
        {
            var claim = this.User.ToClaimsDto();
            var result = _pmsTagService.GetComplexForms(setUpId, merge, qunitId, claim.HOSPITAL_ID);
            return Ok(result);
        }

        /// <summary>
        /// 公共库字段保存
        /// </summary>
        /// <param name="fieldDict"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult SaveFieldDict(FieldDictDto fieldDict)
        {
            var claim = this.User.ToClaimsDto();
            fieldDict.HOSPITAL_ID = claim.HOSPITAL_ID;
            fieldDict.MODULE_ID = claim.MODULE_ID;
            fieldDict.FIRST_RTIME = DateTime.Now;
            fieldDict.FIRST_RPERSON = claim.HIS_NAME;
           // fieldDict.FIELD_STATE = "1";
            var result = _pmsTagService.SaveFieldDict(fieldDict);
            return Ok(result);
        }

        /// <summary>
        /// 获取公共库字段
        /// </summary>
        /// <param name="filedClass">分类</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetGetFieldDicts(string filedClass)
        {
            var claim = this.User.ToClaimsDto();
            var result = _pmsTagService.GetGetFieldDicts(claim.HOSPITAL_ID, claim.MODULE_ID, filedClass);
            return Ok(result);
        }

        [HttpPost]
        public IActionResult InitBaseInfoFieldDict()
        {
            var claim = this.User.ToClaimsDto();
            var result = _pmsTagService.InitBaseInfoFieldDict();
            return Ok(result);
        }

    }
}
