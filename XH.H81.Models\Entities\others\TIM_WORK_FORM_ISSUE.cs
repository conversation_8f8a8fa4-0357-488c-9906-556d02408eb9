﻿using H.Utility.SqlSugarInfra;
using Microsoft.EntityFrameworkCore;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Entities.others
{
    [DBOwner("XH_OA")]
    public class TIM_WORK_FORM_ISSUE
    {
        /// <summary>
        /// 归档ID
        /// </summary>
        [Key]
        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        //[Column("ISSUE_ID")]
        [Required(ErrorMessage = "归档ID不允许为空")]

        [StringLength(50, ErrorMessage = "归档ID长度不能超出50字符")]
        //[Unicode(false)]
        public string ISSUE_ID { get; set; }

        /// <summary>
        /// 科室ID
        /// </summary>
        //[Column("LAB_ID")]
        [Required(ErrorMessage = "科室ID不允许为空")]

        [StringLength(20, ErrorMessage = "科室ID长度不能超出20字符")]
        //[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string LAB_ID { get; set; }

        /// <summary>
        /// 归档电脑
        /// </summary>
        //[Column("ISSUED_COMPUTER")]
        [StringLength(50, ErrorMessage = "归档电脑长度不能超出50字符")]
        //[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? ISSUED_COMPUTER { get; set; }

        /// <summary>
        /// 归档状态1、已归档0、已撤销
        /// </summary>
        //[Column("ISSUE_STATE")]
        [StringLength(10, ErrorMessage = "归档状态1、已归档0、已撤销长度不能超出10字符")]
        //[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? ISSUE_STATE { get; set; }

        /// <summary>
        /// 首次登记人
        /// </summary>
        //[Column("FIRST_RPERSON")]
        [StringLength(50, ErrorMessage = "首次登记人长度不能超出50字符")]
        //[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FIRST_RPERSON { get; set; }

        /// <summary>
        /// 归档年份
        /// </summary>
        //[Column("ISSUE_YEAR")]
        //[Unicode(false)]
        public decimal? ISSUE_YEAR { get; set; }

        /// <summary>
        /// 归档月份
        /// </summary>
        //[Column("ISSUE_MONTH")]
        //[Unicode(false)]
        public decimal? ISSUE_MONTH { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        //[Column("LAST_MTIME")]
        //[Unicode(false)]
        public DateTime? LAST_MTIME { get; set; }

        /// <summary>
        /// 管理单元ID
        /// </summary>
        //[Column("UNIT_ID")]
        [Required(ErrorMessage = "管理单元ID不允许为空")]

        [StringLength(20, ErrorMessage = "管理单元ID长度不能超出20字符")]
        //[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string UNIT_ID { get; set; }

        /// <summary>
        /// 记录单版本主体ID
        /// </summary>
        //[Column("FORM_VER_MAIN_ID")]
        [Required(ErrorMessage = "记录单版本主体ID不允许为空")]

        [StringLength(50, ErrorMessage = "记录单版本主体ID长度不能超出50字符")]
        //[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string FORM_VER_MAIN_ID { get; set; }

        /// <summary>
        /// 归档名称
        /// </summary>
        //[Column("ISSUE_NAME")]
        [StringLength(200, ErrorMessage = "归档名称长度不能超出200字符")]
        //[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? ISSUE_NAME { get; set; }

        /// <summary>
        /// 记录单ID
        /// </summary>
        //[Column("FORM_ID")]
        [Required(ErrorMessage = "记录单ID不允许为空")]

        [StringLength(50, ErrorMessage = "记录单ID长度不能超出50字符")]
        //[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string FORM_ID { get; set; }

        /// <summary>
        /// 归档编号
        /// </summary>
        //[Column("ISSUE_NUM")]
        [StringLength(20, ErrorMessage = "归档编号长度不能超出20字符")]
        //[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? ISSUE_NUM { get; set; }

        /// <summary>
        /// 记录数
        /// </summary>
        //[Column("RECORD_NUMS")]
        //[Unicode(false)]
        public decimal? RECORD_NUMS { get; set; }

        /// <summary>
        /// 归档时间
        /// </summary>
        //[Column("ISSUED_TIME")]
        //[Unicode(false)]
        public DateTime? ISSUED_TIME { get; set; }

        /// <summary>
        /// 管理模块ID
        /// </summary>
        //[Column("MODULE_ID")]
        [Required(ErrorMessage = "管理模块ID不允许为空")]

        [StringLength(20, ErrorMessage = "管理模块ID长度不能超出20字符")]
        //[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string MODULE_ID { get; set; }

        /// <summary>
        /// 归档人员
        /// </summary>
        //[Column("ISSUED_PERSON")]
        [StringLength(50, ErrorMessage = "归档人员长度不能超出50字符")]
        //[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? ISSUED_PERSON { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        //[Column("REMARK")]
        [StringLength(200, ErrorMessage = "备注长度不能超出200字符")]
        //[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? REMARK { get; set; }

        /// <summary>
        /// 表单编号
        /// </summary>
        //[Column("RECORD_NUM")]
        [StringLength(20, ErrorMessage = "表单编号长度不能超出20字符")]
        //[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? RECORD_NUM { get; set; }

        /// <summary>
        /// 首次登记时间
        /// </summary>
        //[Column("FIRST_RTIME")]
        //[Unicode(false)]
        public DateTime? FIRST_RTIME { get; set; }

        /// <summary>
        /// 最后修改人
        /// </summary>
        //[Column("LAST_MPERSON")]
        [StringLength(50, ErrorMessage = "最后修改人长度不能超出50字符")]
        //[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? LAST_MPERSON { get; set; }

        /// <summary>
        /// 体系编号
        /// </summary>
        //[Column("RECORDSYS_NUM")]
        [StringLength(50, ErrorMessage = "体系编号长度不能超出50字符")]
        //[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? RECORDSYS_NUM { get; set; }

        /// <summary>
        /// 归档文件
        /// </summary>
        //[Column("ISSUE_FILE")]
        [StringLength(200, ErrorMessage = "归档文件长度不能超出200字符")]
        //[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? ISSUE_FILE { get; set; }

        /// <summary>
        /// 检验专业组ID
        /// </summary>
        //[Column("PGROUP_ID")]
        [Required(ErrorMessage = "检验专业组ID不允许为空")]

        [StringLength(20, ErrorMessage = "检验专业组ID长度不能超出20字符")]
        //[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string PGROUP_ID { get; set; }

        /// <summary>
        /// 归档日期范围
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? ISSUE_DATE { get; set; }

        /// <summary>
        /// 医疗机构ID
        /// </summary>
        //[Column("HOSPITAL_ID")]
        [Required(ErrorMessage = "医疗机构ID不允许为空")]

        [StringLength(20, ErrorMessage = "医疗机构ID长度不能超出20字符")]
        //[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string HOSPITAL_ID { get; set; }


    }
}
