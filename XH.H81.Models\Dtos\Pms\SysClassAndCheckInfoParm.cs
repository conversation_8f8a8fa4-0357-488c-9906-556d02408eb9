﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Dtos.Pms
{
    public class SysClassAndCheckInfoParm
    {
        public string HOSPITAL_ID { get; set; }
        public string? LAB_ID { get; set; }
        public string? PERSON_ID { get; set; }
        public string? STATUS { get; set; }
        public string? ARCHIVE_TABLE { get; set; }
        public string? SMBL_FLAG { get; set; }
        /// <summary>
        /// 时间类型
        /// </summary>
        public string? DATE_TYPE { get; set; }
        /// <summary>
        /// 开始时间
        /// </summary>
        public string? START_DATE { get; set; }
        /// <summary>
        /// 结束时间
        /// </summary>
        public string? END_DATE { get; set; }

        /// <summary>
        /// 工号/姓名
        /// </summary>
        public string? ID_NAME{ get; set; }

        /// <summary>
        /// 内容检索
        /// </summary>
        public string? SEARCH_NAME { get; set; }
    }
}
