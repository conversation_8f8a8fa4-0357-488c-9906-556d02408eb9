//using System;
//using Microsoft.EntityFrameworkCore;
//using System.ComponentModel.DataAnnotations;
//using System.ComponentModel.DataAnnotations.Schema;
//using SqlSugar;
//using H.Utility.SqlSugarInfra;

//namespace XH.H81.Models.ExternalEntity
//{
//    [Table("LIS6_SAMPLEGATHER_UNIT")]
//    [DBOwner("XH_SYS")]
//    public class LIS6_SAMPLEGATHER_UNIT
//	{
//		/// <summary>
//		/// 
//		/// </summary>
//		[Key]
//        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
//        //[Column("UNIT_ID")]
//		[Required(ErrorMessage = "不允许为空")]

//		[StringLength(50, ErrorMessage = "UNIT_ID长度不能超出50字符")]
//		//[Unicode(false)]
//		public string UNIT_ID { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("UNIT_STATE")]
//		[StringLength(20, ErrorMessage = "UNIT_STATE长度不能超出20字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? UNIT_STATE { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("FIRST_RPERSON")]
//		[StringLength(50, ErrorMessage = "FIRST_RPERSON长度不能超出50字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? FIRST_RPERSON { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("UNIT_ADD")]
//		[StringLength(100, ErrorMessage = "UNIT_ADD长度不能超出100字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? UNIT_ADD { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("LAST_MTIME")]
//		//[Unicode(false)]
//		public DateTime? LAST_MTIME { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("MQ_CHANNEL")]
//		[StringLength(100, ErrorMessage = "MQ_CHANNEL长度不能超出100字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? MQ_CHANNEL { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("IF_QUEUE")]
//		[StringLength(20, ErrorMessage = "IF_QUEUE长度不能超出20字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? IF_QUEUE { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("UNIT_SORT")]
//		[StringLength(50, ErrorMessage = "UNIT_SORT长度不能超出50字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? UNIT_SORT { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("UNIT_DEPT")]
//		[StringLength(50, ErrorMessage = "UNIT_DEPT长度不能超出50字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? UNIT_DEPT { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("REMARK")]
//		[StringLength(255, ErrorMessage = "REMARK长度不能超出255字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? REMARK { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("UNIT_NAME")]
//		[StringLength(100, ErrorMessage = "UNIT_NAME长度不能超出100字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? UNIT_NAME { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("FIRST_RTIME")]
//		//[Unicode(false)]
//		public DateTime? FIRST_RTIME { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("LAST_MPERSON")]
//		[StringLength(50, ErrorMessage = "LAST_MPERSON长度不能超出50字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? LAST_MPERSON { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("UNIT_AREA")]
//		[StringLength(50, ErrorMessage = "UNIT_AREA长度不能超出50字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? UNIT_AREA { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("HOSPITAL_ID")]
//		[StringLength(20, ErrorMessage = "HOSPITAL_ID长度不能超出20字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? HOSPITAL_ID { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("PATIENT_TYPE")]
//		[StringLength(20, ErrorMessage = "PATIENT_TYPE长度不能超出20字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? PATIENT_TYPE { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("UNIT_LEVEL")]
//		[StringLength(20, ErrorMessage = "UNIT_LEVEL长度不能超出20字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? UNIT_LEVEL { get; set; }


//	}
//}
