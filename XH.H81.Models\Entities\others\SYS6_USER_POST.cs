﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Entities
{
    /// <summary>
    /// 用户岗位关联表
    /// </summary>
    [Table("SYS6_USER_POST")]
    [DBOwner("XH_SYS")]
    public class SYS6_USER_POST
    {
        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]

        public string UPOST_ID { get; set; }

        /// <summary>
        /// 岗位ID(老模式）; 0-新模式，岗位-岗位角色关联ID 【PR前缀】填在POSTROLE_ID字段
        /// </summary>
        public string POST_ID { get; set; }

        /// <summary>
        /// 新模式独有，岗位-岗位角色关联ID 【PR前缀】
        /// </summary>
        public string POSTROLE_ID { get; set; }

        public string? FIRST_RPERSON { get; set; }


        public DateTime? LAST_MTIME { get; set; }


        public string? UPOST_SORT { get; set; }



        public string? REMARK { get; set; }

        public DateTime? FIRST_RTIME { get; set; }


        public string? LAST_MPERSON { get; set; }



        public string USER_NO { get; set; }


        public string HOSPITAL_ID { get; set; }


        /// <summary>
        /// 状态:0停岗、1在用、2结束
        /// </summary>
        public string? UPOST_STATE { get; set; }

        /// <summary>
        /// 起始日期
        /// </summary>
        public DateTime? UPOST_USE_SDATE { get; set; }
        /// <summary>
        /// 有效日期
        /// </summary>
        public DateTime? UPOST_USE_EDATE { get; set; }
        /// <summary>
        /// 结束日期
        /// </summary>
        public DateTime? UPOST_END_DATE { get; set; }

    }

}
