﻿using System;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using AutoMapper;
using XH.LAB.UTILS.Models.Entites;
using XH.H81.Models.Dtos.Pms;


/*
 * <AUTHOR> XingHe
 * @date : 2023-8-26
 * @desc : 实验室管理基础数据表
 */
namespace XH.H81.Models.Dtos
{
    /// <summary>
    /// 实验室管理基础数据表
    /// </summary>
    [AutoMap(typeof(OA_BASE_DATA), ReverseMap = true)]
    public class OaBaseDataDto
    {
        /// <summary>
        /// 基础数据ID
        /// </summary>
        public string? DATA_ID { get; set; }

        /// <summary>
        /// 医疗机构ID
        /// </summary>
        public string? HOSPITAL_ID { get; set; }

        /// <summary>
        /// 模块ID
        /// </summary>
        public string? MODULE_ID { get; set; }

        /// <summary>
        /// 父级ID
        /// </summary>
        public string? FATHER_ID { get; set; }

        /// <summary>
        /// 分类ID
        /// </summary>
        public string? CLASS_ID { get; set; }

        /// <summary>
        /// 排序号
        /// </summary>
        public string? DATA_SORT { get; set; }

        /// <summary>
        /// 评价结果
        /// </summary>
        [Required(ErrorMessage = "评价结果不允许为空")]
        public string DATA_NAME { get; set; }

        /// <summary>
        /// 简称
        /// </summary>
        public string? DATA_SNAME { get; set; }

        /// <summary>
        /// 英文名
        /// </summary>
        public string? DATA_ENAME { get; set; }

        /// <summary>
        /// 标准代码
        /// </summary>
        public string? STANDART_ID { get; set; }

        /// <summary>
        /// 自定义码
        /// </summary>
        public string? CUSTOM_CODE { get; set; }

        /// <summary>
        /// 拼音码
        /// </summary>
        public string? SPELL_CODE { get; set; }


        /// <summary>
        /// 状态
        /// </summary>
        public string? STATE_FLAG { get; set; }

        /// <summary>
        /// 首次登记人
        /// </summary>
        public string? FIRST_RPERSON { get; set; }

        /// <summary>
        /// 首次登记时间
        /// </summary>
        public DateTime? FIRST_RTIME { get; set; }

        /// <summary>
        /// 最后修改人员
        /// </summary>
        public string? LAST_MPERSON { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime? LAST_MTIME { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? REMARK { get; set; }

    }


    public class OaBaseDataAndTagDto : OaBaseDataDto
    {
        /// <summary>
        /// 人员标签ID
        /// </summary>
        public List<string>? PERSON_TAG_IDS { get; set; }
        /// <summary>
        /// 人员标签名称
        /// </summary>
        public string? PERSON_TAG_NAMES { get; set; }
        /// <summary>
        /// 数据表
        /// </summary>
        public string? DATA_TABLE { get; set; }
    }
}