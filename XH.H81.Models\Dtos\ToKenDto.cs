﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Dtos
{
    public class ToKenDto
    {
        public string DISPLAY_NAME { get; set; }
        public string EXTRA_INFO { get; set; }
        public string H<PERSON><PERSON>TAL_CNAME { get; set; }
        public string H<PERSON><PERSON>TAL_ID { get; set; }
        public string INSTANCE_ID { get; set; }
        public string LAB_ID { get; set; }
        public string LOGID { get; set; }
        public string MANAGE_CLASS { get; set; }
        public string MGROUP_ID { get; set; }
        public string MODULE_ID { get; set; }
        public string PHONE_NO { get; set; }
        public string POWER { get; set; }
        public string ROLE_LIST { get; set; }
        public string TECH_POST { get; set; }
        public string TOKENGUID { get; set; }
        public string USER_NAME { get; set; }
        public string USER_NO { get; set; }
        public string PERSON_ID { get; set; }
        public string OPERATE_PERSON { get; set; }
    }
}
