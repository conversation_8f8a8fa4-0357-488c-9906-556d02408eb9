﻿using H.Utility.Helper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using System.Xml.Serialization;

namespace XH.H81.Models.Dtos.IoTDevices.Platform
{

    [XmlRoot("Root")]
    public class XhXmlRoot
    {
        public string MethodCode { get; set; } = "IotSwitch";
        public string ProductCode { get; set; } = "SMBL";
        public string INTERFACE_ID { get; set; } = "SMBL";
        public string DataFormat { get; set; } = "JSON";

        public string Sign { get; set; } = "E291A2AFF63C4D07B663FC7014ACD62B@xhlis";

        public static XhXmlRoot CreatRoot(string methodCode = "IotSwitch")
        {
            return new XhXmlRoot()
            {
                MethodCode = methodCode,
                ProductCode = "SMBL",
                INTERFACE_ID = "SMBL",
                DataFormat = "JSON",
                Sign= "E291A2AFF63C4D07B663FC7014ACD62B@xhlis"
            };
        }
    }
    [XmlRoot("Root")]
    public class XhIotRoot
    {

    }
    //  [XmlRoot("Root")]
    public class XhIotBody : XhIotRoot
    {

    }


    public class XhPlatformRequest
    {
        public string headXml { get; set; }
        public string bodyXml { get; set; }
        public string Sign { get; set; }


        public static XhPlatformRequest Create< TBody>(XhXmlRoot head, TBody body)
        {
           string sign= head.Sign += JsonHelper.ToJson(body);
            var sm3Sign = SmxUtilsHelper.SM3Utils(sign);
            head.Sign= sm3Sign;
            var xmlHead = XmlHelper.SerializeObjectToXml(head);
            if (body != null)
            {
                //  var xmlBody = XmlHelper.SerializeObjectToXml(body);
              
                return new XhPlatformRequest()
                {
                    headXml = Convert.ToBase64String(Encoding.UTF8.GetBytes(xmlHead)),
                    bodyXml = Convert.ToBase64String(Encoding.UTF8.GetBytes(JsonHelper.ToJson(body))),
                    //Sign = sign
                };
            }
            else
            {
               // var sign = SmxUtilsHelper.SM3Utils("E291A2AFF63C4D07B663FC7014ACD62B@xhlis");
                return new XhPlatformRequest()
                {
                    headXml = Convert.ToBase64String(Encoding.UTF8.GetBytes(xmlHead)),
                    bodyXml = Convert.ToBase64String(Encoding.UTF8.GetBytes("")),
                    //Sign = sign
                };
            }
        }
    }

    public class XhPlatformResponse<T>
    {
        public string ResultCode { get; set; }
        public string ResultMsg { get; set; }
        public List<T> Items { get; set; }
    }
    public class XhPlatformResponse1
    {
        public string ResultCode { get; set; }
        public string ResultMsg { get; set; }
        public object Items { get; set; }
    }
}
public static class XmlHelper
{
    public static string SerializeObjectToXml<T>(T obj)
    {
        // 获取泛型对象的类型
        Type type = typeof(T);
        // 创建根节点
        XElement rootElement = new XElement("Root");

        // 遍历对象的所有属性
        foreach (PropertyInfo property in type.GetProperties())
        {
            // 获取属性值
            object value = property.GetValue(obj);
            // 如果值不为空，添加到XML中
            if (value != null)
            {
                XElement element = new XElement(property.Name, value.ToString());
                rootElement.Add(element);
            }
        }
        // 返回XML字符串
        return rootElement.ToString();
    }
}