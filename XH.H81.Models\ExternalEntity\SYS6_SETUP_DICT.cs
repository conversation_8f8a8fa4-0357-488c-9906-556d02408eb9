//using System;
//using Microsoft.EntityFrameworkCore;
//using System.ComponentModel.DataAnnotations;
//using System.ComponentModel.DataAnnotations.Schema;
//using SqlSugar;
//using H.Utility.SqlSugarInfra;

//namespace XH.H81.Models.ExternalEntity
//{
//    [Table("SYS6_SETUP_DICT")]
//    [DBOwner("XH_SYS")]
//    public class SYS6_SETUP_DICT
//	{
//		/// <summary>
//		/// 
//		/// </summary>
//		[Key]
//        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
//        //[Column("SETUP_NO")]
//		[Required(ErrorMessage = "不允许为空")]

//		[StringLength(20, ErrorMessage = "SETUP_NO长度不能超出20字符")]
//		//[Unicode(false)]
//        public string SETUP_NO { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		[Key]
//        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
//        //[Column("HOSPITAL_ID")]
//		[Required(ErrorMessage = "不允许为空")]

//		[StringLength(20, ErrorMessage = "HOSPITAL_ID长度不能超出20字符")]
//		//[Unicode(false)]
//        public string HOSPITAL_ID { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("FIRST_RPERSON")]
//		[StringLength(50, ErrorMessage = "FIRST_RPERSON长度不能超出50字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? FIRST_RPERSON { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("LAST_MTIME")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public DateTime? LAST_MTIME { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("UNIT_ID")]
//		[Required(ErrorMessage = "不允许为空")]

//		[StringLength(20, ErrorMessage = "UNIT_ID长度不能超出20字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string UNIT_ID { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("MODULE_NO")]
//		[StringLength(50, ErrorMessage = "MODULE_NO长度不能超出50字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? MODULE_NO { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("CHOICE_VALUE")]
//		[StringLength(4000, ErrorMessage = "CHOICE_VALUE长度不能超出4000字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? CHOICE_VALUE { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("SETUP_SNAME")]
//		[StringLength(100, ErrorMessage = "SETUP_SNAME长度不能超出100字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? SETUP_SNAME { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("SETUP_SORT")]
//		[StringLength(20, ErrorMessage = "SETUP_SORT长度不能超出20字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? SETUP_SORT { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("SETUP_STATE")]
//		[StringLength(10, ErrorMessage = "SETUP_STATE长度不能超出10字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? SETUP_STATE { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("SETUP_DESC")]
//		[StringLength(500, ErrorMessage = "SETUP_DESC长度不能超出500字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? SETUP_DESC { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("UNIT_TYPE")]
//		[Required(ErrorMessage = "不允许为空")]

//		[StringLength(50, ErrorMessage = "UNIT_TYPE长度不能超出50字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string UNIT_TYPE { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("DATA_CLASS_ID")]
//		[StringLength(200, ErrorMessage = "DATA_CLASS_ID长度不能超出200字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? DATA_CLASS_ID { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("SETUP_ONECLASS")]
//		[StringLength(50, ErrorMessage = "SETUP_ONECLASS长度不能超出50字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? SETUP_ONECLASS { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("REMARK")]
//		[StringLength(200, ErrorMessage = "REMARK长度不能超出200字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? REMARK { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("SETUP_CONTENT")]
//		[StringLength(2000, ErrorMessage = "SETUP_CONTENT长度不能超出2000字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? SETUP_CONTENT { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("FIRST_RTIME")]
//		//[Unicode(false)]
//		public DateTime? FIRST_RTIME { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("LAST_MPERSON")]
//		[StringLength(50, ErrorMessage = "LAST_MPERSON长度不能超出50字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? LAST_MPERSON { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("DEFAULT_VALUE")]
//		[StringLength(500, ErrorMessage = "DEFAULT_VALUE长度不能超出500字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? DEFAULT_VALUE { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("SETUP_NAME")]
//		[StringLength(300, ErrorMessage = "SETUP_NAME长度不能超出300字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? SETUP_NAME { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("SETUP_CLASS")]
//		[StringLength(200, ErrorMessage = "SETUP_CLASS长度不能超出200字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? SETUP_CLASS { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("SETUP_TYPE")]
//		[StringLength(20, ErrorMessage = "SETUP_TYPE长度不能超出20字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? SETUP_TYPE { get; set; }


//	}
//}
