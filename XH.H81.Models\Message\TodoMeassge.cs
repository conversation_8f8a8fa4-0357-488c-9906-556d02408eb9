﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XH.H81.Base.Helper;

namespace XH.H81.Models.Message
{
    public class TodoMeassge : BaseMessage
    {

        public TodoMeassge(double validityPeriodDay = 0)
        {
            MAG_DATE = DateTime.Now.ToString("yyyy-MM-dd");
            MSG_CLASS = "M13";
            MSG_CORRID = DateTime.UtcNow.Ticks.ToString();
            MSG_DISPOSE_TYPE = "1";
            MSG_DISPOSE_URL = "H84";
            MSG_DISPOSE_URL_STYLE = "";
            MSG_LEVEL = "1";
            MSG_OPERATE = "1";
            MSG_VALID_TIME = DateTime.Now.AddDays(validityPeriodDay).ToString("yyyy-MM-dd HH:mm:ss");
            RECEIVE_UNIT_TYPE = "个人消息";
            SEND_TIME = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            SEND_COMPUTER = GetMacLoacltion.GetMacAddress();
        }


        public static TodoMeassge CreatTodoMeassge(string sendUserNo, string sendUserName, string hospitalId, string labId, string areaid, string modeuleId, string title, string content, string msgType, string receiveUserNo, string receiveName, double validityPeriodDays = 0, long delay = 0, long overTime = 0)
        {
            var todoMesagger = new TodoMeassge(validityPeriodDays);
            todoMesagger.HOSPITAL_ID = hospitalId;
            todoMesagger.AREA_ID = areaid;
            todoMesagger.LAB_ID = labId;
            todoMesagger.MODULE_ID = modeuleId;
            todoMesagger.MSG_TITLE = title;
            todoMesagger.MSG_CONTENT = content;
            todoMesagger.MSG_TYPE = msgType;
            todoMesagger.RECEIVE_UNIT_ID = receiveUserNo;
            todoMesagger.RECEIVE_UNIT_NAME = receiveName;
            todoMesagger.SEND_UNIT_ID = sendUserNo;
            todoMesagger.SEND_PERSON = sendUserName;
            todoMesagger.DELAY_TIME = delay;
            todoMesagger.MSG_OVERTIME = overTime;
            return todoMesagger;
        }

    }
}
