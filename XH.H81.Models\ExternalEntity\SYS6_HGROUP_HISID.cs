﻿using System.ComponentModel.DataAnnotations.Schema;
using H.Utility.SqlSugarInfra;

namespace XH.H81.Models.ExternalEntity;

[Table("SYS6_HGROUP_HISID")]
[DBOwner("XH_SYS")]
public class SYS6_HGROUP_HISID
{
    public string HISID_ID { get; set; }
    public string HOS<PERSON>TAL_ID { get; set; }
    public string HGROUP_CLASS { get; set; }
    public string DATA_ID { get; set; }
    public string? HIS_ID { get; set; }
    public string? HIS_NAME { get; set; }
    public string? INTERFACE_ID { get; set; }
    public string? HISID_STATE { get; set; }
    public string? FIRST_RPERSON { get; set; }
    public DateTime? FIRST_RTIME { get; set; }
    public DateTime? LAST_MPERSON { get; set; }
    public string? LAST_MTIME { get; set; }
    public string? REMARK { get; set; }
    public string? AREA_SID { get; set; }
}