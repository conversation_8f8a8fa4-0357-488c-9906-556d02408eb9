﻿using Newtonsoft.Json;

namespace XH.H81.Models.Dtos.IoTDevices.Access;
/// <summary>
/// 门禁授权计划
/// </summary>
public class AccessPlanDto
{
    /// <summary>
    /// ID 编号，最好从1自增
    /// </summary>
    [JsonProperty("id")]
    public string Id { get; set; }
    /// <summary>
    /// 模板名称
    /// </summary>
    [JsonProperty("templateName")]
    public string TemplateName { get; set; }
    /// <summary>
    /// 是否开启：true生效
    /// </summary>
    [JsonProperty("enable")]
    public bool Enable { get; set; }
    /// <summary>
    /// 周计划id
    /// </summary>
    [JsonProperty("weekPlanNo")]
    public int weekPlanNo { get; set; }

    /// <summary>
    /// 节假日组计划，多个英文逗号分隔“1,2”
    /// </summary>
    [JsonProperty("holidayGroupNo")]
    public string holidayGroupNo { get; set; }
}