﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Enums
{
    /// <summary>
    /// 规评设置状态枚举
    /// </summary>
    public enum EvaluatePlanSetupStateEnum
    {
        /// <summary>
        /// 禁用
        /// </summary>
        [Description("禁用")]
        DISABLE = 0,
        /// <summary>
        /// 启用
        /// </summary>
        [Description("启用")]
        ENABLE = 1,
        /// <summary>
        /// 删除
        /// </summary>
        [Description("删除")]
        DELETE = 2,
        /// <summary>
        /// 未启用
        /// </summary>
        [Description("未启用")]
        UNABLE = 3,
    }

}
