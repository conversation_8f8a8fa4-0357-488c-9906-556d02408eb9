﻿using AutoMapper;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XH.LAB.UTILS.Models.Entites;

namespace XH.H81.Models.Dtos
{
    public class DropDown
    {
        public string key { get; set; }
        public string value { get; set; }

    }
    public class LevelDropDown: DropDown
    {
        public string? level { get; set; }

    }

    public class BaseDataAndTagDto : BaseDataDto
    {
        /// <summary>
        /// 人员标签ID
        /// </summary>
        public List<string>? PERSON_TAG_IDS { get; set; }
        /// <summary>
        /// 人员标签名称
        /// </summary>
        public string? PERSON_TAG_NAMES { get; set; }
    }

    /// <summary>
    /// 实验室管理基础数据表
    /// </summary>
    [AutoMap(typeof(OA_BASE_DATA), ReverseMap = true)]
    public class BaseDataDto
    {
        /// <summary>
        /// 基础数据ID
        /// </summary>
        public string? DATA_ID { get; set; }

        /// <summary>
        /// 父级ID
        /// </summary>
        public string? FATHER_ID { get; set; }

        /// <summary>
        /// 分类ID
        /// </summary>
        public string? CLASS_ID { get; set; }

        /// <summary>
        /// 排序号
        /// </summary>
        public string? DATA_SORT { get; set; }

        /// <summary>
        /// 数据名称
        /// </summary>
        public string DATA_NAME { get; set; }

        /// <summary>
        /// 简称
        /// </summary>
        public string? DATA_SNAME { get; set; }

        /// <summary>
        /// 英文名
        /// </summary>
        public string? DATA_ENAME { get; set; }

        /// <summary>
        /// 标准代码
        /// </summary>
        public string? STANDART_ID { get; set; }

        /// <summary>
        /// 自定义码
        /// </summary>
        public string? CUSTOM_CODE { get; set; }

        /// <summary>
        /// 拼音码
        /// </summary>
        public string? SPELL_CODE { get; set; }


        /// <summary>
        /// 状态
        /// </summary>
        public string? STATE_FLAG { get; set; }

        /// <summary>
        /// 数据表
        /// </summary>
        public string? DATA_TABLE { get; set; }

    }

}
