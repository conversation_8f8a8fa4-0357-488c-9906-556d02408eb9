﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text;

namespace XH.H81.Models.Entities.Pms
{
    /// <summary>
    ///论文记录表
    /// </summary>
    [DBOwner("XH_OA")]
    public class PMS_THESIS_LIST
    {
        /// <summary>
        ///论文记录ID
        /// </summary>
        [Key]
        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string THESIS_ID { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? RECORD_ID { get; set; }
        /// <summary>
        ///医疗机构ID
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? HOSPITAL_ID { get; set; }
        /// <summary>
        ///人员ID
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? PERSON_ID { get; set; }
        /// <summary>
        ///人员名称
        /// </summary>
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? PERSON_NAME { get; set; }
        /// <summary>
        ///排序号
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? THESIS_SORT { get; set; }
        /// <summary>
        ///论文类型
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? THESIS_TYPE { get; set; }
        /// <summary>
        ///论文类型名称
        /// </summary>
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? THESIS_TYPE_NAME { get; set; }
        /// <summary>
        ///论文级别
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? THESIS_LEVEL { get; set; }
        /// <summary>
        ///论文级别名称
        /// </summary>
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? THESIS_LEVEL_NAME { get; set; }
        /// <summary>
        ///刊物/出版社
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? THESIS_PUBLISH { get; set; }
        /// <summary>
        ///论文名称
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? THESIS_NAME { get; set; }
        /// <summary>
        ///发布时间
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? ISSUED_TIME { get; set; }
        /// <summary>
        ///论文项目名称
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? THESIS_ITEM_NAME { get; set; }
        /// <summary>
        ///是否SCI记录
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? IF_SCI { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? IF_SCI_NAME { get; set; }
        /// <summary>
        ///JCR分区
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? JCR_RANGE { get; set; }

        /// <summary>
        /// 字段和所属值
        /// </summary>
        public string RECORD_DATA { get; set; }
        /// <summary>
        ///JCR分区名称
        /// </summary>
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? JCR_RANGE_NAME { get; set; }
        /// <summary>
        ///IF值
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? IF_VALUE { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? IF_VALUE_NAME { get; set; }
        /// <summary>
        ///论文作者排序
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? THESIS_PERSON_SORT { get; set; }
        /// <summary>
        ///论文作者排序名称
        /// </summary>
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? THESIS_PERSON_SORT_NAME { get; set; }
        /// <summary>
        ///附件
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? THESIS_AFFIX { get; set; }
        /// <summary>
        ///附件名称
        /// </summary>
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? AFFIX_NAME { get; set; }
        /// <summary>
        ///指定审核人员
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? SELECT_CHECK_PERSON { get; set; }
        /// <summary>
        ///指定审核人员名称
        /// </summary>
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? SELECT_CHECK_PERSON_NAME { get; set; }
        /// <summary>
        ///审核时间
        /// </summary>
        public DateTime? CHECK_TIME { get; set; }
        /// <summary>
        ///审核人员
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CHECK_PERSON { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CHECK_PERSON_NAME { get; set; }
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CHECK_STATE { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CHECK_STATE_NAME { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? NEW_STATE { get; set; }

        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? NEW_STATE_NAME { get; set; }
        /// <summary>
        ///状态
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? THESIS_STATE { get; set; }


        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? THESIS_STATE_NAME { get; set; }
        /// <summary>
        ///首次登记人
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FIRST_RPERSON { get; set; }
        /// <summary>
        ///首次登记时间
        /// </summary>
        public DateTime? FIRST_RTIME { get; set; }
        /// <summary>
        ///最后修改人员
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? LAST_MPERSON { get; set; }
        /// <summary>
        ///最后修改时间
        /// </summary>
        public DateTime? LAST_MTIME { get; set; }
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? REMARK { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public List<PMS_PERSON_FILE> PMS_PERSON_FILE { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? SORT_NUM { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? SUBMIT_STATE { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? REJECT_REASON { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? REJECT_DATE { get; set; }

        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? REJECT_PERSON { get; set; }

        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? PGROUP_NAME { get; set; }

        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? HIS_ID { get; set; }
        /// <summary>
        /// 生安标志
        /// </summary>
        public string SMBL_FLAG { get; set; } = "0";



    }
}
