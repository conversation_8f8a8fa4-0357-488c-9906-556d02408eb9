﻿using H.Utility;
using H.Utility.SqlSugarInfra;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Entities.Pms
{
    [DBOwner("XH_OA")]
    public class PMS_PERSON_INFO
    {
        /// <summary>
        ///人员ID
        /// </summary>
        [Key]
        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "人员ID")]
        public string PERSON_ID { get; set; }
        /// <summary>
        /// 科室ID
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "科室ID")]
        public string? LAB_ID { get; set; }
        /// <summary>
        /// 院区ID
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString,ColumnDescription = "院区ID")]
        public string? AREA_ID { get; set; }
        /// <summary>
        ///管理单元ID
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "管理单元ID")]
        public string? PGROUP_ID { get; set; }
        /// <summary>
        ///管理单元名称
        /// </summary>
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "管理单元名称")]
        public string? PGROUP_NAME { get; set; }
        /// <summary>
        ///医疗机构ID
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "医疗机构ID")]
        public string? HOSPITAL_ID { get; set; }
        /// <summary>
        ///账号ID
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "账号ID")]
        public string? USER_ID { get; set; }

        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "登录ID")]
        public string? LOGID { get; set; }
        /// <summary>
        ///人员类型
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "人员类型")]
        //1-正式员工2-合同职工3-返聘职工4-实习人数5-进修人数6-规培人数7-临聘人数
        public string? USER_TYPE { get; set; }
        /// <summary>
        ///姓名
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "姓名")]
        public string? USER_NAME { get; set; }
        /// <summary>
        /// 英文名
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "英文名")]
        public string? USER_ENAME { get; set; }
        /// <summary>
        ///性别
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "性别")]
        public string? SEX { get; set; }
        /// <summary>
        ///年龄
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "年龄")]
        public string? AGE { get; set; }
        /// <summary>
        ///出生年月
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "出生年月")]
        public string? BIRTHDAY { get; set; }
        /// <summary>
        ///民族
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "民族")]
        public string? NATION { get; set; }
        /// <summary>
        ///籍贯
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "籍贯")]
        public string? NATIVE_PLACE { get; set; }
        /// <summary>
        ///政治面貌
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "政治面貌")]
        public string? POLITICIAN { get; set; }
        /// <summary>
        ///毕业专业
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "毕业专业")]
        public string? PROFESSION { get; set; }
        /// <summary>
        ///最高学历
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "最高学历")]
        public string? HIGHEST_DEGREE { get; set; }
        /// <summary>
        ///学历取得时间
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "学历取得时间")]
        public string? DEGREE_TIME { get; set; }
        /// <summary>
        ///最高学位
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "最高学位")]
        public string? HIGHEST_DIPLOMA { get; set; }
        /// <summary>
        ///学位取得时间
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "学位取得时间")]
        public string? DIPLOMA_TIME { get; set; }
        /// <summary>
        ///参加工作时间
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "参加工作时间")]
        public string? WORK_TIME { get; set; }
        /// <summary>
        ///连续工龄
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.Int32, ColumnDescription = "连续工龄")]
        public int? LENGTH_SERVICE { get; set; } = 0;
        /// <summary>
        ///进院日期
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.Date, ColumnDescription = "进院日期")]
        public DateTime? IN_HOSPITAL_DATE { get; set; }
        /// <summary>
        ///院龄
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.Int32, ColumnDescription = "院龄")]
        public int? LENGTH_HOSPITAL { get; set; } = 0;
        /// <summary>
        ///行政职务
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "行政职务")]
        public string? DUTIES { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "行政职务名称")]
        public string? DUTIES_NAME { get; set; }
        /// <summary>
        ///职称级别
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "职称级别")]
        public string? TECH_POST { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "职称级别名称")]
        public string? TECH_POST_NAME { get; set; }
        /// <summary>
        ///职称名称 对应系统数据管理USER表的TECH_POST字段
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "职称名称ID")]
        public string? ACADEMIC_POST { get; set; }

        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "职称名称")]
        public string? ACADEMIC_POST_NAME { get; set; }
        /// <summary>
        ///通信地址
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "通信地址")]
        public string? COMM_ADDR { get; set; }
        /// <summary>
        ///家庭电话
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "家庭电话")]
        public string? HOME_TEL { get; set; }
        /// <summary>
        ///手机
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "手机")]
        public string? PHONE { get; set; }
        /// <summary>
        ///短号
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "短号")]
        public string? CORNET { get; set; }
        /// <summary>
        ///出生地
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "出生地")]
        public string? BIRTH_PLACE { get; set; }
        /// <summary>
        ///身高
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "身高")]
        public string? HEIGHT { get; set; }
        /// <summary>
        ///视力
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "视力")]
        public string? EYESIGHT { get; set; }
        /// <summary>
        ///左视力
        /// </summary>
        [SugarColumn(IsIgnore = true)] 
        public string? EYESIGHT_LEFT
        { 
            get { return EYESIGHT.IsNullOrEmpty() ? "" : EYESIGHT.Split('/')[0]; }
            set { EYESIGHT = $"{value}/{EYESIGHT_RIGHT}"; }
        }
        /// <summary>
        ///右视力
        /// </summary>
        [SugarColumn(IsIgnore = true)] 
        public string? EYESIGHT_RIGHT
        {
            get { return EYESIGHT.IsNullOrEmpty() || !EYESIGHT.Contains('/') ? "" : EYESIGHT.Split('/')[1]; }
            set { EYESIGHT = $"{EYESIGHT_LEFT}/{value}"; }
        }
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "英语等级")]
        public string? ENGLISH_RANK { get; set; }
        /// <summary>
        ///英语等级成绩
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "英语等级成绩")]
        public string? ENGLISH_RANK_SCORE { get; set; }
        /// <summary>
        ///婚姻状况
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "婚姻状况")]
        public string? MARITAL_STATUS { get; set; }
        /// <summary>
        ///有无子女
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "有无子女")]
        public string? CHILDREN_CONDITION { get; set; }
        /// <summary>
        ///证件类型
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "证件类型")]
        public string? CARD_TYPE { get; set; }
        /// <summary>
        ///身份证
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "身份证")]
        public string? ID_CARD { get; set; }
        /// <summary>
        ///户籍所在地
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "户籍所在地")]
        public string? DOMICILE_PLACE { get; set; }
        /// <summary>
        ///紧急联系人
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "紧急联系人")]
        public string? EMERGENCY_CONTACT { get; set; }
        /// <summary>
        ///与紧急联系人的关系
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "与紧急联系人的关系")]
        public string? ECONTACT_RELACTION { get; set; }
        /// <summary>
        ///紧急联系人电话
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "紧急联系人电话")]
        public string? ECONTACT_PHONE { get; set; }
        /// <summary>
        ///现居住地
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "现居住地")]
        public string? CURRENT_ADDRESS { get; set; }
        /// <summary>
        ///健康状况
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "健康状况")]
        public string? HEALTH { get; set; }
        /// <summary>
        ///邮箱
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "邮箱")]
        public string? E_MAIL { get; set; }
        /// <summary>
        ///办公电话
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "办公电话")]
        public string? OFFICE_PHONE { get; set; }
        /// <summary>
        ///聘任职称评定单位
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "聘任职称评定单位")]
        public string? EMPLOYMENT_UNIT { get; set; }
        /// <summary>
        ///职称类型
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "职称类型")]
        public string? TECHNOLOGY_TYPE { get; set; }
        /// <summary>
        ///专业技术资格取得时间
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.Date, ColumnDescription = "专业技术资格取得时间")]
        public DateTime? TECH_CERTIFICE_TIME { get; set; }
        /// <summary>
        ///职称专业
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "职称专业")]
        public string? TECH_POST_PROFESSION { get; set; }
        /// <summary>
        ///招聘来源
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "招聘来源")]
        public string? EMPLOYMENT_SOURE { get; set; }
        /// <summary>
        ///专业特长
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "专业特长")]
        public string? PROFESSION_EXPERTISE { get; set; }
        /// <summary>
        ///聘用时间
        /// </summary>
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.Date, ColumnDescription = "聘用时间")]
        public DateTime? EMPLOY_TIME { get; set; }
        /// <summary>
        ///退休时间
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.Date, ColumnDescription = "退休时间")]
        public DateTime? RETIRE_TIME { get; set; }
        /// <summary>
        ///来院时间
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.Date, ColumnDescription = "来院时间")]
        public DateTime? IN_HOSPITAL_TIME { get; set; }

        [SugarColumn(SqlParameterDbType = System.Data.DbType.Date, ColumnDescription = "离院时间")]
        /// <summary>
        ///离院时间
        /// </summary>
        public DateTime? OUT_HOSPITAL_TIME { get; set; }

        [SugarColumn(SqlParameterDbType = System.Data.DbType.Date, ColumnDescription = "返聘时间")]
        /// <summary>
        ///返聘时间
        /// </summary>
        public DateTime? REEMPLOY_TIME { get; set; }
        /// <summary>
        ///登记模式
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "登记模式")]
        public string? REGISTER_MODE { get; set; }
        /// <summary>
        ///登记人员
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "登记人员")]
        public string? REGISTER_PERSON { get; set; }
        /// <summary>
        ///登记时间
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "登记时间")]
        public string? REGISTER_TIME { get; set; }
        /// <summary>
        ///提交人员
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "提交人员")]
        public string? SUBMIT_PERSON { get; set; }
        /// <summary>
        ///提交时间
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "提交时间")]
        public string? SUBMIT_TIME { get; set; }
        /// <summary>
        ///审核人员
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "审核人员")]
        public string? CHECK_PERSON { get; set; }
        /// <summary>
        ///审核时间
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "审核时间")]
        public string? CHECK_TIME { get; set; }
        /// <summary>
        ///审核电脑
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "审核电脑")]
        public string? CHECK_COMPUTER { get; set; }
        /// <summary>
        ///放置场所
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "放置场所")]
        public string? DOC_PLACE { get; set; }
        /// <summary>
        ///人员性质
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "人员性质")]
        public string? PERSON_DOC_STATE { get; set; }
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "人员头像")]
        public string? PERSON_PHOTO_PATH { get; set; }
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "来科日期")]
        public string? IN_LAB_TIME { get; set; }//来科日期
        [SugarColumn(SqlParameterDbType = System.Data.DbType.Int32, ColumnDescription = "来科日期")]
        public int LENGTH_LAB { get; set; } = 0;//科龄
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "离科日期")]
        public string? OUT_LAB_TIME { get; set; }//离科日期

        /// <summary>
        ///状态 
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "状态")]
        public string? PERSON_STATE { get; set; }
        /// <summary>
        ///首次登记人
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "首次登记人")]
        public string? FIRST_RPERSON { get; set; }
        /// <summary>
        ///首次登记时间
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "首次登记时间")]
        public string? FIRST_RTIME { get; set; }
        /// <summary>
        ///最后修改人员
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "最后修改人员")]
        public string? LAST_MPERSON { get; set; }
        /// <summary>
        ///最后修改时间
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "最后修改时间")]
        public string? LAST_MTIME { get; set; }
        /// <summary>
        ///备注
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "备注")]
        public string? REMARK { get; set; }
        /// <summary>
        /// 工号
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "工号")]
        public string? HIS_ID { get; set; }

        //[SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        //public string? HIS_NAME { get => $"{HIS_ID}_{USER_NAME}"; }

        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "是否聘用")]
        public string? IF_EMPLOYMENT { get; set; }
        /// <summary>
        ///毕业院校
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "毕业院校")]
        public string? GRADUATE_SCHOOL { get; set; }
        /// <summary>
        ///毕业日期
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.Date, ColumnDescription = "毕业日期")]
        public DateTime? GRADUATE_DATE { get; set; }

        /// <summary>
        ///颜色视觉障碍（0-正常  1-色弱 2-色盲）
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "颜色视觉障碍")]
        public string? COLOR_DEFICIENCY { get; set; }

        /// <summary>
        /// 扩展字段
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "扩展字段")]
        public string? RECORD_DATA { get; set; }

        /// <summary>
        /// 人员分类
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "人员分类")]
        public string? PERSON_TYPE { get; set; }

        /// <summary>
        ///管理单元名称
        /// </summary>
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "管理单元")]
        public string? SMBL_POST_TYPE { get; set; }

        /// <summary>
        ///管理单元名称
        /// </summary>
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString, ColumnDescription = "管理单元名称")]
        public string? SMBL_POST_NOW { get; set; }

        /// <summary>
        /// 人员人脸照片
        /// </summary>
        public string FACE_PHOTO { get; set; }

        /// <summary>
        /// 人员人脸裁剪照片
        /// </summary>
        public string FACE_CLIP_PHOTO { get; set; }
    }
}
