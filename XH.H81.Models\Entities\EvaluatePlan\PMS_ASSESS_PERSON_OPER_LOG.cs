﻿using System;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using SqlSugar;
using H.Utility.SqlSugarInfra;

/*
 * <AUTHOR> XingHe
 * @date : 2023-9-11
 * @desc : 评估人员操作日志表
 */
namespace XH.H81.Models.Entities.EvaluatePlan
{
    /// <summary>
    /// 评估人员操作日志表
    /// </summary>
    [SugarTable("PMS_ASSESS_PERSON_OPER_LOG")]
    [DBOwner("XH_OA")]
    public class PMS_ASSESS_PERSON_OPER_LOG
    {
        /// <summary>
        /// 日志ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        //[Column("LOG_ID")]
        [Required(ErrorMessage = "日志ID不允许为空")]
        [StringLength(50, ErrorMessage = "日志ID长度不能超出50字符")]
        //[Unicode(false)]
        public string LOG_ID { get; set; }

        /// <summary>
        /// 计划人员评估ID
        /// </summary>
        //[Column("PLAN_PERSON_ID")]
        [Required(ErrorMessage = "计划人员评估ID不允许为空")]
        [StringLength(20, ErrorMessage = "计划人员评估ID长度不能超出20字符")]
        //[Unicode(false)]
        public string PLAN_PERSON_ID { get; set; }

        /// <summary>
        /// 医疗机构ID
        /// </summary>
        //[Column("HOSPITAL_ID")]
        [Required(ErrorMessage = "医疗机构ID不允许为空")]
        [StringLength(20, ErrorMessage = "医疗机构ID长度不能超出20字符")]
        //[Unicode(false)]
        public string HOSPITAL_ID { get; set; }

        /// <summary>
        /// 操作ID
        /// </summary>
        //[Column("OPER_ID")]
        [Required(ErrorMessage = "操作ID不允许为空")]
        [StringLength(50, ErrorMessage = "操作ID长度不能超出50字符")]
        //[Unicode(false)]
        public string OPER_ID { get; set; }

        /// <summary>
        /// 操作时间
        /// </summary>
        //[Column("OPER_TIME")]

        //[Unicode(false)]
        public DateTime? OPER_TIME { get; set; }

        /// <summary>
        /// 操作人
        /// </summary>
        //[Column("OPER_PERSON")]
        [StringLength(50, ErrorMessage = "操作人长度不能超出50字符")]
        //[Unicode(false)]
        public string? OPER_PERSON { get; set; }

        /// <summary>
        /// 操作电脑
        /// </summary>
        //[Column("OPER_COMPUTER")]
        [StringLength(50, ErrorMessage = "操作电脑长度不能超出50字符")]
        //[Unicode(false)]
        public string? OPER_COMPUTER { get; set; }

        /// <summary>
        /// 原因分类;多个原因分类用逗号分开，不能修改原来分类
        /// </summary>
        //[Column("CAUSE_TYPE")]
        [StringLength(100, ErrorMessage = "原因分类长度不能超出100字符")]
        //[Unicode(false)]
        public string? CAUSE_TYPE { get; set; }

        /// <summary>
        /// 操作原因
        /// </summary>
        //[Column("OPER_CAUSE")]
        [StringLength(500, ErrorMessage = "操作原因长度不能超出500字符")]
        //[Unicode(false)]
        public string? OPER_CAUSE { get; set; }

        /// <summary>
        /// 当前状态;0未参加 1已参加 2已自评 3已考评（专家全部考评完成）4废止
        /// </summary>
        //[Column("CURRENT_STATE")]
        [StringLength(50, ErrorMessage = "当前状态长度不能超出50字符")]
        //[Unicode(false)]
        public string? CURRENT_STATE { get; set; }

        /// <summary>
        /// 下一个操作人
        /// </summary>
        //[Column("OPER_PERSON_NEXT")]
        [StringLength(50, ErrorMessage = "下一个操作人长度不能超出50字符")]
        //[Unicode(false)]
        public string? OPER_PERSON_NEXT { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        //[Column("OPER_STATE")]
        [StringLength(10, ErrorMessage = "状态长度不能超出10字符")]
        //[Unicode(false)]
        public string? OPER_STATE { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        //[Column("REMARK")]
        [StringLength(100, ErrorMessage = "备注长度不能超出100字符")]
        //[Unicode(false)]
        public string? REMARK { get; set; }

    }
}