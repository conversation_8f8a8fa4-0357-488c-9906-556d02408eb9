﻿/* #6.25.225 复杂表单模板数据 */
INSERT INTO XH_SYS.SYS6_MODULE_FUNC_DICT (SETUP_ID, FUNC_ID, MODULE_ID, SETUP_NAME, SETUP_CNAME, SETUP_DESC, SETUP_CLASS, SETUP_SORT, SKIN_ID, SETUP_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK, IF_DOUBLE_CLICK, FORM_JSON, FORM_COL_JSON, FORM_QUERY_JSON, IF_QUERY, HOSPITAL_ID) VALUES('H81_TG000001X', 'H8107', 'H81', '实验室ISO15189人员表单', '实验室ISO15189人员表单', NULL, 'B05-66|B05-67', '001', NULL, '1', 'H81初始化', TIMESTAMP '2025-05-22 17:52:27.000000', 'H81初始化', TIMESTAMP '2025-06-11 09:41:59.000000', NULL, NULL, '{"layout":{"type":"fixed","widthPercent":100,"labelCol":3,"verticalGap":16,"size":"default","style":{"paddingTop":4,"paddingBottom":4},"showRowNum":false,"labelLayout":"horizontal","labelWrap":true},"rows":[{"rowId":"row_703132c2","cols":[{"colId":"col_98054ad7","span":24,"items":[{"itemId":"item_70314472","group":"基本","formId":"title","icon":"iconbiaotilan","formName":"基本信息","isForm":false,"dataType":"XTitle","wigetProps":{"button":false,"groupKey":""},"propslist":["button","buttonSize","groupKey"],"labelStyle":{"background":"rgb(241,247,255)"}}]}]},{"rowId":"row_4489546d","rowType":"fixed","cols":[{"colId":"col_4489e428","span":8,"items":[{"itemId":"item_2962ce6a","colSpan":8,"wigetType":"fixed","formName":"姓名","formCname":"姓名","formCode":"USER_NAME","isForm":true,"dataType":"Input","labelCols":3},{"itemId":"item_42883768","colSpan":8,"wigetType":"fixed","formName":"工号","formCname":"工号","formCode":"HIS_ID","isForm":true,"dataType":"Input","labelCols":3},{"itemId":"item_6212e4d6","colSpan":8,"wigetType":"fixed","formName":"下拉框","formCname":"民族","formCode":"NATION","isForm":true,"dataType":"Select","labelCols":3,"queryType":"enum","group":"基本","formId":"xialakuang","icon":"iconxialakuang","propslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","defaultValue","isDataTypeToPerson"]},{"itemId":"item_7151dcb2","colSpan":8,"wigetType":"fixed","formName":"证件类型","formCname":"证件类型","formCode":"CARD_TYPE","isForm":true,"dataType":"Select","labelCols":3,"queryType":"api"},{"itemId":"item_32478339","colSpan":8,"wigetType":"fixed","formName":"出生年月","formCname":"出生年月","formCode":"BIRTHDAY","isForm":true,"dataType":"DatePicker","labelCols":3}]},{"colId":"col_44892e0a","span":8,"items":[{"itemId":"item_139462f3","colSpan":8,"wigetType":"fixed","formName":"登录ID","formCname":"登录ID","formCode":"LOGID","isForm":true,"dataType":"Input","labelCols":3},{"itemId":"item_59761567","group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"专业组","dataType":"Select","dataClass":"PGROUP_ID","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"formCode":"PGROUP_ID","queryType":"api"},{"itemId":"item_4129cfb5","group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"证件号码","dataType":"Input","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"formCode":"ID_CARD"},{"itemId":"item_320126b3","colSpan":8,"wigetType":"fixed","formName":"政治面貌","formCname":"政治面貌","formCode":"POLITICIAN","isForm":true,"dataType":"Select","labelCols":3,"queryType":"api"},{"itemId":"item_09000125","colSpan":8,"wigetType":"fixed","formName":"性别/年龄","formCname":"性别","formCode":"SEX","isForm":true,"dataType":"Select","labelCols":3,"groupItems":{"itemId":"item_8296cde6","connector":"/"},"queryType":"api"},{"itemId":"item_8296cde6","colSpan":8,"wigetType":"fixed","formName":"年龄","formCname":"年龄","formCode":"AGE","isForm":true,"dataType":"Input","labelCols":3}]},{"colId":"col_44899c0b","span":8,"items":[{"itemId":"item_3431c44d","group":"基本","formId":"tupianfujian","icon":"icontouxiang","formName":"","formNameHide":true,"dataType":"ImgUpload","isForm":true,"wigetProps":{"width":180,"height":220,"accept":".png,.jpg,.jpeg","style":{"justifyContent":"center","textAlign":"center","display":"flex"}},"propslist":["dataType","required","width","height"],"formCode":"USER_IMG","labelStyle":{"justifyContent":"center","textAlign":"center","display":"flex"}}]}],"style":{"paddingTop":0,"paddingLeft":0}},{"rowId":"row_8197b441","rowType":"fixed","cols":[{"colId":"col_81970997","span":8,"items":[{"itemId":"item_6155443a","group":"基本","formId":"jilianxuanze","icon":"iconxialakuang","formName":"籍贯","isForm":true,"dataType":"Cascader","wigetProps":{},"propslist":["dataType","required","allowClear","prefix","variant","placeholder","disabled","showSearch"],"formCode":"NATIVE_PLACE"}]},{"colId":"col_8197a622","span":8,"items":[{"itemId":"item_0917a793","group":"基本","formId":"jilianxuanze","icon":"iconxialakuang","formName":"户籍所在地","isForm":true,"dataType":"Cascader","wigetProps":{},"propslist":["dataType","required","allowClear","prefix","variant","placeholder","disabled","showSearch"],"formCode":"DOMICILE_PLACE"}]},{"colId":"col_81972abd","span":8,"items":[{"itemId":"item_6403d7e1","colSpan":8,"wigetType":"fixed","formName":"现居住地","formCname":"现居住地","formCode":"CURRENT_ADDRESS","isForm":true,"dataType":"Input","labelCols":3}]}]},{"rowId":"row_5451a683","rowType":"fixed","cols":[{"colId":"col_54514c9e","span":8,"items":[{"itemId":"item_7247fc33","colSpan":8,"wigetType":"fixed","formName":"最高学历","formCname":"最高学历","formCode":"HIGHEST_DEGREE","isForm":true,"dataType":"Select","labelCols":3,"queryType":"api"}]},{"colId":"col_5451fe44","span":8,"items":[{"itemId":"item_0015dd2a","colSpan":8,"wigetType":"fixed","formName":"最高学位","formCname":"最高学位","formCode":"HIGHEST_DIPLOMA","isForm":true,"dataType":"Select","labelCols":3,"queryType":"api"}]},{"colId":"col_545176f0","span":8,"items":[{"itemId":"item_83732b44","colSpan":8,"wigetType":"fixed","formName":"毕业院校","formCname":"毕业院校","formCode":"GRADUATE_SCHOOL","isForm":true,"dataType":"Input","labelCols":3}]}]},{"rowId":"row_792943be","rowType":"fixed","cols":[{"colId":"col_7929c9ea","span":8,"items":[{"itemId":"item_56524f1d","colSpan":8,"wigetType":"fixed","formName":"毕业日期","formCname":"毕业日期","formCode":"GRADUATE_DATE","isForm":true,"dataType":"DatePicker","labelCols":3,"picker":"date"}]},{"colId":"col_792978e6","span":8,"items":[{"itemId":"item_8938132c","colSpan":8,"wigetType":"fixed","formName":"毕业专业","formCname":"毕业专业","formCode":"PROFESSION","isForm":true,"dataType":"Input","labelCols":3}]},{"colId":"col_792902f7","span":8,"items":[{"itemId":"item_57575b56","colSpan":8,"wigetType":"fixed","formName":"英语级别/成绩（分）","formCname":"英语级别/成绩（分）","formCode":"ENGLISH_RANK_SCORE","isForm":true,"dataType":"Input","labelCols":3,"groupItems":{}},{"itemId":"item_06023261","colSpan":8,"wigetType":"fixed","formName":"英语级别/成绩(分)","formCname":"英语级别/成绩(分)","formCode":"YYJB50394","isForm":true,"dataType":"Select","labelCols":3,"groupItems":{"itemId":"item_57575b56"}}]}]},{"rowId":"row_9975fc08","rowType":"fixed","cols":[{"colId":"col_9975268c","span":8,"items":[{"itemId":"item_8402065b","group":"基本","formId":"danxuankuang","icon":"icondanxuan","formName":"婚姻状况","isForm":true,"dataType":"Radio","dataClass":"婚姻状况","wigetProps":{},"propslist":["dataType","required","optionType","styleFlex","disabled","queryType","dataClass","defaultValue"],"formCode":"MARITAL_STATUS","labelStyle":{"justifyContent":"flex-end","textAlign":"right","display":"flex"},"queryType":"api"}]},{"colId":"col_9975783b","span":8,"items":[{"itemId":"item_65602032","group":"基本","formId":"danxuankuang","icon":"icondanxuan","formName":"有无子女","dataClass":"有无子女","isForm":true,"dataType":"Radio","wigetProps":{},"propslist":["dataType","required","optionType","styleFlex","disabled","queryType","dataClass","defaultValue"],"formCode":"CHILDREN_CONDITION","queryType":"api"}]},{"colId":"col_9975bf05","span":8,"items":[{"itemId":"item_6949a68a","group":"基本","formId":"danxuankuang","icon":"icondanxuan","formName":"颜色视觉障碍","isForm":true,"dataType":"Radio","wigetProps":{"options":[{"value":"0","label":"正常"},{"value":"1","label":"色弱"},{"value":"2","label":"色盲"}],"style":{"justifyContent":"flex-start","textAlign":"left","display":"flex"}},"propslist":["dataType","required","optionType","styleFlex","disabled","queryType","dataClass","defaultValue"],"formCode":"COLOR_DEFICIENCY","labelStyle":{"justifyContent":"flex-end","textAlign":"right","display":"flex"}}]}]},{"rowId":"row_1288c597","rowType":"fixed","cols":[{"colId":"col_12882354","span":8,"items":[{"itemId":"item_94162c7e","colSpan":8,"wigetType":"fixed","formName":"身高","formCname":"身高","formCode":"HEIGHT","isForm":true,"dataType":"Input","labelCols":3}]},{"colId":"col_128857ea","span":8,"items":[{"itemId":"item_30362876","colSpan":8,"wigetType":"fixed","formName":"视力","formCname":"视力","formCode":"EYESIGHT_RIGHT","isForm":true,"dataType":"Input","labelCols":3},{"itemId":"item_30368955","colSpan":8,"wigetType":"fixed","formName":"视力（左/右）","formCname":"视力","formCode":"EYESIGHT_LEFT","isForm":true,"dataType":"Input","labelCols":3,"groupItems":{"itemId":"item_30362876","connector":"/"}}]},{"colId":"col_12881c8d","span":8,"items":[{"itemId":"item_57810316","colSpan":8,"wigetType":"fixed","formName":"健康状况","formCname":"健康状况","formCode":"HEALTH","isForm":true,"dataType":"Select","labelCols":3,"queryType":"api"}]}]},{"rowId":"row_6000ae73","rowType":"fixed","cols":[{"colId":"col_5999c0a3","span":24,"items":[{"itemId":"item_25860758","colSpan":8,"wigetType":"fixed","formName":"专业特长","formCname":"专业特长","formCode":"PROFESSION_EXPERTISE","isForm":true,"dataType":"Input","labelCols":3}]}]},{"rowId":"row_872901bb","cols":[{"colId":"col_87295e8e","span":24,"items":[{"itemId":"item_8729c365","group":"基本","formId":"title","icon":"iconbiaotilan","formName":"联系方式","isForm":false,"dataType":"XTitle","wigetProps":{"button":false,"groupKey":""},"propslist":["button","buttonSize","groupKey"],"labelStyle":{"background":"rgb(241,247,255)"}}]}]},{"rowId":"row_22044871","rowType":"fixed","cols":[{"colId":"col_22033905","span":8,"items":[{"itemId":"item_0767e0e3","group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"联系方式","dataType":"Input","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"formCode":"PHONE"}]},{"colId":"col_220388bf","span":8,"items":[{"itemId":"item_5105c708","colSpan":8,"wigetType":"fixed","formName":"邮箱","formCname":"邮箱","formCode":"E_MAIL","isForm":true,"dataType":"Input","labelCols":3}]},{"colId":"col_2203da89","span":8,"items":[{"itemId":"item_51265c4b","colSpan":8,"wigetType":"fixed","formName":"办公电话","formCname":"办公电话","formCode":"OFFICE_PHONE","isForm":true,"dataType":"Input","labelCols":3}]}]},{"rowId":"row_44289f89","rowType":"fixed","cols":[{"colId":"col_44282d91","span":8,"items":[{"itemId":"item_7569b6a4","colSpan":8,"wigetType":"fixed","formName":"紧急联系人","formCname":"紧急联系人","formCode":"EMERGENCY_CONTACT","isForm":true,"dataType":"Input","labelCols":3}]},{"colId":"col_442871d2","span":8,"items":[{"itemId":"item_46767669","colSpan":8,"wigetType":"fixed","formName":"与紧急联系人的关系","formCname":"与紧急联系人的关系","formCode":"ECONTACT_RELACTION","isForm":true,"dataType":"Input","labelCols":3}]},{"colId":"col_44281ab4","span":8,"items":[{"itemId":"item_7830c240","colSpan":8,"wigetType":"fixed","formName":"紧急联系人电话","formCname":"紧急联系人电话","formCode":"ECONTACT_PHONE","isForm":true,"dataType":"Input","labelCols":3}]}]},{"rowId":"row_66919741","rowType":"fixed","cols":[{"colId":"col_66917fa5","span":24,"items":[{"itemId":"item_269625ee","group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"详细通讯地址","dataType":"Input","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"formCode":"COMM_ADDR"}]}]},{"rowId":"row_10441e18","cols":[{"colId":"col_1044101c","span":24,"items":[{"itemId":"item_1044b066","group":"基本","formId":"title","icon":"iconbiaotilan","formName":"聘用信息","isForm":false,"dataType":"XTitle","wigetProps":{"button":false,"groupKey":""},"propslist":["button","buttonSize","groupKey"],"labelStyle":{"background":"rgb(241,247,255)"}}]}]},{"rowId":"row_9115c670","rowType":"fixed","cols":[{"colId":"col_91159801","span":8,"items":[{"itemId":"item_46903557","colSpan":8,"wigetType":"fixed","formName":"职务","formCname":"行政职务","formCode":"DUTIES","isForm":true,"dataType":"Select","labelCols":3,"queryType":"api"}]},{"colId":"col_9115f31e","span":8,"items":[{"itemId":"item_1302d4a1","group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"入职方式","dataType":"Select","dataClass":"入职方式","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"formCode":"EMPLOYMENT_SOURE","queryType":"api"}]},{"colId":"col_9115c5e9","span":8,"items":[{"itemId":"item_1581f1d3","group":"基本","formId":"xialakuang","icon":"iconxialakuang","formName":"用工类型","dataClass":"用工类型","isForm":true,"dataType":"Select","wigetProps":{},"propslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue"],"formCode":"USER_TYPE","queryType":"api"}]}]},{"rowId":"row_6645162b","cols":[{"colId":"col_66457692","span":24,"items":[{"itemId":"item_66458985","group":"基本","formId":"title","icon":"iconbiaotilan","formName":"职称信息","isForm":false,"dataType":"XTitle","wigetProps":{"button":true,"groupKey":"row_50147784"},"propslist":["button","buttonSize","groupKey"],"labelStyle":{"background":"rgb(241,247,255)"}}]}]},{"rowId":"row_50147784","rowType":"fixed","cols":[{"colId":"col_50141556","span":8,"items":[{"itemId":"item_8098cda6","colSpan":8,"wigetType":"fixed","formName":"职称类型","formCname":"职称类型","formCode":"TECHNOLOGY_TYPE","isForm":true,"dataType":"Select","labelCols":3,"queryType":"api"},{"itemId":"item_40152a3c","colSpan":8,"wigetType":"fixed","formName":"职称专业","formCname":"职称专业","formCode":"TECH_POST_PROFESSION","isForm":true,"dataType":"Input","labelCols":3}]},{"colId":"col_5014acb7","span":8,"items":[{"itemId":"item_6728683d","colSpan":8,"wigetType":"fixed","formName":"职称级别","formCname":"职称级别","formCode":"TECH_POST","isForm":true,"dataType":"Select","labelCols":3,"queryType":"api"},{"itemId":"item_3260d6ac","colSpan":8,"wigetType":"fixed","formName":"聘任职称评定单位","formCname":"聘任职称评定单位","formCode":"EMPLOYMENT_UNIT","isForm":true,"dataType":"Input","labelCols":3}]},{"colId":"col_50144ff2","span":8,"items":[{"itemId":"item_7651faaa","colSpan":8,"wigetType":"fixed","formName":"职称名称","formCname":"职称名称","formCode":"ACADEMIC_POST","isForm":true,"dataType":"Select","labelCols":3,"queryType":"api"},{"itemId":"item_8846125d","group":"基本","formId":"riqi","icon":"iconriqi","formName":"职称评定日期","dataType":"DatePicker","isForm":true,"wigetProps":{},"propslist":["dataType","required","disabled","picker","allowClear"],"formCode":"TECH_CERTIFICE_TIME","picker":"date"}]}]},{"rowId":"row_7658e9dc","cols":[{"colId":"col_7658673d","span":24,"items":[{"itemId":"item_7658162e","group":"基本","formId":"title","icon":"iconbiaotilan","formName":"工作时间","isForm":false,"dataType":"XTitle","wigetProps":{"button":false,"groupKey":""},"propslist":["button","buttonSize","groupKey"],"labelStyle":{"background":"rgb(241,247,255)"}}]}]},{"rowId":"row_9762ccff","rowType":"fixed","cols":[{"colId":"col_976274b2","span":8,"items":[{"itemId":"item_4481cefc","group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"参加工作日期","dataType":"DatePicker","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false,"defaultValue":null},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"formCode":"WORK_TIME","picker":"date"}]},{"colId":"col_9762e2b0","span":8,"items":[{"itemId":"item_71571e24","group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"退休日期","dataType":"DatePicker","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false,"defaultValue":null},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"formCode":"RETIRE_TIME","picker":"date"}]},{"colId":"col_97627c02","span":8,"items":[{"itemId":"item_9508c577","group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"工龄","dataType":"Input","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"formCode":"LENGTH_SERVICE"}]}]},{"rowId":"row_220912d8","rowType":"fixed","cols":[{"colId":"col_220965fa","span":8,"items":[{"itemId":"item_43173cf5","group":"基本","formId":"riqi","icon":"iconriqi","formName":"来院日期","dataType":"DatePicker","isForm":true,"wigetProps":{},"propslist":["dataType","required","disabled","picker","allowClear"],"formCode":"IN_HOSPITAL_TIME","picker":"date"}]},{"colId":"col_22096345","span":8,"items":[{"itemId":"item_6068bda8","group":"基本","formId":"riqi","icon":"iconriqi","formName":"离院日期","dataType":"DatePicker","isForm":true,"wigetProps":{},"propslist":["dataType","required","disabled","picker","allowClear"],"formCode":"OUT_HOSPITAL_TIME","picker":"date"}]},{"colId":"col_220959c2","span":8,"items":[{"itemId":"item_1230e745","group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"院龄","dataType":"Input","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"formCode":"LENGTH_HOSPITAL"}]}]},{"rowId":"row_4948213c","rowType":"fixed","cols":[{"colId":"col_4948b3be","span":8,"items":[{"itemId":"item_615434a6","group":"基本","formId":"shijian","icon":"iconshijian1","formName":"来科日期","dataType":"DatePicker","isForm":true,"rules":[{"required":true}],"wigetProps":{"defaultValue":null},"propslist":["dataType","required","disabled","allowClear"],"formCode":"IN_LAB_TIME","picker":"date"}]},{"colId":"col_4948324b","span":8,"items":[{"itemId":"item_8292530e","group":"基本","formId":"shijian","icon":"iconshijian1","formName":"离科日期","dataType":"DatePicker","isForm":true,"wigetProps":{"defaultValue":null},"propslist":["dataType","required","disabled","allowClear"],"formCode":"OUT_LAB_TIME","picker":"date"}]},{"colId":"col_49482927","span":8,"items":[{"itemId":"item_19597cb8","group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"科龄","dataType":"Input","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"formCode":"LENGTH_LAB"}]}]}]}', '{"form":[{"formName":"登录ID","formCode":"LOGID","formCname":"登录ID","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":"人员培训分类","allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","styleJson":{"dataType":"Input","rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false}}},{"formName":"姓名","formCode":"USER_NAME","formCname":"姓名","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":"","allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","styleJson":{"dataType":"Input","rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false}}},{"formName":"性别","formCode":"SEX","formCname":"性别","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Select","sort":null,"dataClass":"性别","allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","styleJson":{"dataClass":"性别","dataType":"Select","labelHide":false,"rules":[{"required":true}],"wigetProps":{}},"queryType":"api"},{"formName":"年龄","formCode":"SEX","formCname":"年龄","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"1","sort":null,"dataClass":"","allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","styleJson":{"dataClass":"性别","dataType":"Select","labelHide":false,"rules":[{"required":true}],"wigetProps":{}}},{"formName":"出生年月","formCode":"BIRTHDAY","formCname":"出生年月","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"DatePicker","sort":null,"dataClass":"","allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","styleJson":{"dataType":"DatePicker","rules":[{"required":null}],"wigetProps":{"defaultValue":null}}},{"formName":"民族","formCode":"NATION","formCname":"民族","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Select","sort":null,"dataClass":"民族","allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","defaultValue","isDataTypeToPerson"],"styleJson":{"dataClass":"民族","dataType":"Select","rules":[{"required":true}],"wigetProps":{"defaultValue":"1"}},"queryType":"api"},{"formName":"工号","formCode":"HIS_ID","formCname":"工号","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{"dataType":"Input","rules":[{"required":null}],"wigetProps":{}}},{"formName":"专业组","formCode":"PGROUP_ID","formCname":"专业组","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Select","sort":null,"dataClass":"DataClass_By_PGROUP_ID","allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","defaultValue","isDataTypeToPerson"],"styleJson":{},"queryType":"api"},{"formName":"证件号码","formCode":"ID_CARD","formCname":"证件号码","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{}},{"formName":"身高","formCode":"HEIGHT","formCname":"身高","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{"dataType":"Input","rules":[{"required":null}],"wigetProps":{}}},{"formName":"紧急联系人","formCode":"EMERGENCY_CONTACT","formCname":"紧急联系人","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{"dataType":"Input","rules":[{"required":null}],"wigetProps":{}}},{"formName":"与紧急联系人的关系","formCode":"ECONTACT_RELACTION","formCname":"与紧急联系人的关系","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{"dataType":"Input","rules":[{"required":null}],"wigetProps":{}}},{"formName":"紧急联系人电话","formCode":"ECONTACT_PHONE","formCname":"紧急联系人电话","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{"dataType":"Input","rules":[{"required":null}],"wigetProps":{}}},{"formName":"详细通讯地址","formCode":"COMM_ADDR","formCname":"详细通讯地址","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{}},{"formName":"人员性质","formCode":"PERSON_DOC_STATE","formCname":"人员性质","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{}},{"formName":"入职方式","formCode":"EMPLOYMENT_SOURE","formCname":"入职方式","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Select","sort":null,"dataClass":"入职方式","allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"styleJson":{},"queryType":"api"},{"formName":"用工类型","formCode":"USER_TYPE","formCname":"用工类型","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Select","sort":null,"dataClass":"用工类型","allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"styleJson":{},"queryType":"api"},{"formName":"职称类型","formCode":"TECHNOLOGY_TYPE","formCname":"职称类型","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Select","sort":null,"dataClass":"职称类型","allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"styleJson":{"dataClass":"职称类型","dataType":"Select","rules":[{"required":null}],"wigetProps":{}},"queryType":"api"},{"formName":"职称专业","formCode":"TECH_POST_PROFESSION","formCname":"职称专业","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{"dataType":"Input","rules":[{"required":null}],"wigetProps":{}}},{"formName":"聘任职称评定单位","formCode":"EMPLOYMENT_UNIT","formCname":"聘任职称评定单位","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{"dataType":"Input","rules":[{"required":null}],"wigetProps":{}}},{"formName":"职称名称","formCode":"ACADEMIC_POST","formCname":"职称名称","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Select","sort":null,"dataClass":"医师职称","allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{"dataClass":"医师职称","dataType":"Select","wigetProps":{"options":[{"value":"选项1","label":"选项1"},{"value":"选项2","label":"选项2"},{"value":"选项3","label":"选项3"}],"defaultValue":""}},"queryType":"api"},{"formName":"职称评定日期","formCode":"TECH_CERTIFICE_TIME","formCname":"职称评定日期","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"DatePicker","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","disabled","picker","allowClear"],"styleJson":{}},{"formName":"参加工作日期","formCode":"WORK_TIME","formCname":"参加工作日期","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"DatePicker","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","disabled","picker","allowClear"],"styleJson":{}},{"formName":"退休日期","formCode":"RETIRE_TIME","formCname":"退休日期","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"DatePicker","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","disabled","picker","allowClear"],"styleJson":{}},{"formName":"工龄","formCode":"LENGTH_SERVICE","formCname":"工龄","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{}},{"formName":"年龄","formCode":"AGE","formCname":"年龄","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{"dataType":"Input","rules":[{"required":null}],"wigetProps":{}}},{"formName":"籍贯","formCode":"NATIVE_PLACE","formCname":"籍贯","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Cascader","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","styleJson":{}},{"formName":"户籍所在地","formCode":"DOMICILE_PLACE","formCname":"户籍所在地","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Cascader","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","styleJson":{}},{"formName":"现居住地","formCode":"CURRENT_ADDRESS","formCname":"现居住地","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{"dataType":"Input","rules":[{"required":null}],"wigetProps":{}}},{"formName":"最高学历","formCode":"HIGHEST_DEGREE","formCname":"最高学历","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Select","sort":null,"dataClass":"最高学历","allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"styleJson":{"dataClass":"最高学历","dataType":"Select","rules":[{"required":null}],"wigetProps":{}},"queryType":"api"},{"formName":"毕业院校","formCode":"GRADUATE_SCHOOL","formCname":"毕业院校","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{"dataType":"Input","rules":[{"required":null}],"wigetProps":{}}},{"formName":"毕业日期","formCode":"GRADUATE_DATE","formCname":"毕业日期","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"DatePicker","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","disabled","picker","allowClear"],"styleJson":{"dataType":"DatePicker","rules":[{"required":null}],"wigetProps":{}}},{"formName":"毕业专业","formCode":"PROFESSION","formCname":"毕业专业","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{"dataType":"Input","rules":[{"required":null}],"wigetProps":{}}},{"formName":"英语等级","formCode":"ENGLISH_RANK","formCname":"英语等级","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Select","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{"dataType":"Select","wigetProps":{"options":[]}},"queryType":"api"},{"formName":"婚姻状况","formCode":"MARITAL_STATUS","formCname":"婚姻状况","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Radio","sort":null,"dataClass":"婚姻状况","allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","optionType","styleFlex","disabled","queryType","dataClass","defaultValue","isDataTypeToPerson"],"styleJson":{},"queryType":"api"},{"formName":"颜色视觉障碍","formCode":"COLOR_DEFICIENCY","formCname":"颜色视觉障碍","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Radio","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","optionType","styleFlex","disabled","queryType","defaultValue","isDataTypeToPerson"],"styleJson":{},"queryType":"enum"},{"formName":"视力","formCode":"EYESIGHT_RIGHT","formCname":"视力","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{"dataType":"Input"}},{"formName":"视力（左/右）","formCode":"EYESIGHT_LEFT","formCname":"视力（左/右）","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{"dataType":"Input"}},{"formName":"专业特长","formCode":"PROFESSION_EXPERTISE","formCname":"专业特长","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{"dataType":"Input","rules":[{"required":null}],"wigetProps":{}}},{"formName":"联系方式","formCode":"PHONE","formCname":"联系方式","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{}},{"formName":"邮箱","formCode":"E_MAIL","formCname":"邮箱","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{"dataType":"Input","rules":[{"required":null}],"wigetProps":{}}},{"formName":"办公电话","formCode":"OFFICE_PHONE","formCname":"办公电话","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{"dataType":"Input","rules":[{"required":null}],"wigetProps":{}}},{"formName":"政治面貌","formCode":"POLITICIAN","formCname":"政治面貌","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Select","sort":null,"dataClass":"政治面貌","allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"styleJson":{"dataClass":"政治面貌","dataType":"Select","rules":[{"required":null}],"wigetProps":{}},"queryType":"api"},{"formName":"最高学位","formCode":"HIGHEST_DIPLOMA","formCname":"最高学位","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Select","sort":null,"dataClass":"最高学位","allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"styleJson":{"dataClass":"最高学位","dataType":"Select","wigetProps":{"options":[{"value":"选项1","label":"选项1"},{"value":"选项2","label":"选项2"},{"value":"选项3","label":"选项3"}],"defaultValue":""}},"queryType":"api","isDataTypeToPerson":"True"},{"formName":"岗位类别","formCode":"GWLB79071","formCname":"岗位类别","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Select","sort":null,"dataClass":"岗位类别","allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{},"queryType":"api"},{"formName":"参加培训情况","formCode":"CJPXQK82201","formCname":"参加培训情况","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{}},{"formName":"培训合格证号","formCode":"PXHGZH48698","formCname":"培训合格证号","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{}},{"formName":"来院日期","formCode":"IN_HOSPITAL_TIME","formCname":"来院日期","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"DatePicker","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","disabled","picker","allowClear"],"styleJson":{}},{"formName":"离院日期","formCode":"OUT_HOSPITAL_TIME","formCname":"离院日期","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"DatePicker","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","disabled","picker","allowClear"],"styleJson":{}},{"formName":"院龄","formCode":"LENGTH_HOSPITAL","formCname":"院龄","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{}},{"formName":"来科日期","formCode":"IN_LAB_TIME","formCname":"来科日期","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"DatePicker","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","disabled","picker","allowClear"],"styleJson":{}},{"formName":"离科日期","formCode":"OUT_LAB_TIME","formCname":"离科日期","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"DatePicker","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","disabled","picker","allowClear"],"styleJson":{}},{"formName":"科龄","formCode":"LENGTH_LAB","formCname":"科龄","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{}},{"formName":"健康状况","formCode":"HEALTH","formCname":"健康状况","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Select","sort":null,"dataClass":"健康状况","allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"styleJson":{"dataClass":"健康状况","dataType":"Select","rules":[{"required":null}],"wigetProps":{}},"queryType":"api"},{"formName":"现从事岗位","formCode":"XCSGW70984","formCname":"现从事岗位","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Select","sort":null,"dataClass":"现从事岗位","allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{},"queryType":"api"},{"formName":"证件类型","formCode":"CARD_TYPE","formCname":"证件类型","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Select","sort":null,"dataClass":"证件类型","allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"styleJson":{"dataClass":"证件类型","dataType":"Select","rules":[{"required":null}],"wigetProps":{"defaultValue":"1"}},"queryType":"api"},{"formName":"职称级别","formCode":"TECH_POST","formCname":"职称级别","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Select","sort":null,"dataClass":"职称级别","allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"styleJson":{"dataClass":"职称级别","dataType":"Select","rules":[{"required":null}],"wigetProps":{}},"queryType":"api"},{"formName":"备案实验室","formCode":"BASYS55209","formCname":"备案实验室","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{}},{"formName":"职务","formCode":"DUTIES","formCname":"职务","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Select","sort":null,"dataClass":"职务","allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"styleJson":{"dataClass":"职务","dataType":"Select","rules":[{"required":null}],"wigetProps":{}},"queryType":"api"},{"formName":"是否外单位科研合作人员","formCode":"SFWDWKYHZRY39890","formCname":"是否外单位科研合作人员","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Radio","sort":null,"dataClass":"是否","allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","optionType","styleFlex","disabled","queryType","dataClass","defaultValue","isDataTypeToPerson"],"styleJson":{}},{"formName":"英语级别/成绩（分）","formCode":"ENGLISH_RANK_SCORE","formCname":"英语级别/成绩（分）","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":"英语级别","allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"styleJson":{"dataClass":"英语级别","dataType":"Input","rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false}},"queryType":"enum"},{"formName":"备注","formCode":"BZ63459","formCname":"备注","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{},"queryType":"enum"},{"formName":"培训合格证书","formCode":"PXHGZS25179","formCname":"培训合格证书","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Upload","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","multiple","listType","accept"],"styleJson":{}},{"formName":"有无子女","formCode":"CHILDREN_CONDITION","formCname":"有无子女","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Radio","sort":null,"dataClass":"有无子女","allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"styleJson":{},"queryType":"api"},{"formName":"新字段","formCode":"XZD45449","formCname":"新字段","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Select","sort":null,"dataClass":"参会人身份","allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"styleJson":{}},{"formName":"新增-文本","formCode":"XZWB49684","formCname":"新增-文本","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Select","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","defaultValue","isDataTypeToPerson"],"styleJson":{},"queryType":"enum","isDataTypeToPerson":"True"},{"formName":"删除测试","formCode":"SCCS85056","formCname":"删除测试","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"InputNumber","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","min","max","prefix","suffix","variant","disabled","controls"],"styleJson":{}},{"formName":"新增-下拉-","formCode":"XZXL86444","formCname":"新增-下拉-","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Select","sort":null,"dataClass":"参会人身份","allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"styleJson":{"dataClass":"H810000-XZXL86444-9rvbco2g","dataType":"Select","wigetProps":{"options":[{"PERSON_TAG_IDS":["TG000098","TG000103","TG000102","TG000108","TG000109","TG000001"],"PERSON_TAG_NAMES":"检验科、内科、临床生化实验室、生化 1、生化室-检验、实验室管理(ISO15189)","DATA_TABLE":"OA_BASE_DATA","DATA_ID":"H818100000185","HOSPITAL_ID":null,"MODULE_ID":null,"FATHER_ID":null,"CLASS_ID":"H810000-XZXL86444-9rvbco2g","DATA_SORT":"1749521116","DATA_NAME":"ISO选项","DATA_SNAME":null,"DATA_ENAME":null,"STANDART_ID":null,"CUSTOM_CODE":null,"SPELL_CODE":null,"STATE_FLAG":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-10 10:05:16","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-10 10:05:16","REMARK":null},{"PERSON_TAG_IDS":["TG000106","TG000095","TG000002","TG000097"],"PERSON_TAG_NAMES":"操作员、监督员、POCT、管理人员","DATA_TABLE":"OA_BASE_DATA","DATA_ID":"H818100000186","HOSPITAL_ID":null,"MODULE_ID":null,"FATHER_ID":null,"CLASS_ID":"H810000-XZXL86444-9rvbco2g","DATA_SORT":"1749521131","DATA_NAME":"POCT选项","DATA_SNAME":null,"DATA_ENAME":null,"STANDART_ID":null,"CUSTOM_CODE":null,"SPELL_CODE":null,"STATE_FLAG":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-10 10:05:31","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-10 10:05:31","REMARK":null},{"PERSON_TAG_IDS":["TG000104","TG000003","TG000101"],"PERSON_TAG_NAMES":"P2、生安、P1","DATA_TABLE":"OA_BASE_DATA","DATA_ID":"H818100000187","HOSPITAL_ID":null,"MODULE_ID":null,"FATHER_ID":null,"CLASS_ID":"H810000-XZXL86444-9rvbco2g","DATA_SORT":"1749521144","DATA_NAME":"生安选项","DATA_SNAME":null,"DATA_ENAME":null,"STANDART_ID":null,"CUSTOM_CODE":null,"SPELL_CODE":null,"STATE_FLAG":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-10 10:05:44","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-10 10:05:44","REMARK":null}]}},"queryType":"enum"},{"formName":"新增-下拉-自定义","formCode":"XZXLZDY94278","formCname":"新增-下拉-自定义","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"TextArea","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","defaultValue","isDataTypeToPerson"],"styleJson":{"dataType":"TextArea","rules":[{"required":null}],"wigetProps":{}},"queryType":"enum","isDataTypeToPerson":"False"},{"formName":"基本信息项","formCode":"JBXXX75570","formCname":"基本信息项","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Select","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","defaultValue","isDataTypeToPerson"],"styleJson":{"dataClass":"H810000-JBXXX75570-78vemt4u","dataType":"Select","wigetProps":{"options":[{"PERSON_TAG_IDS":[],"PERSON_TAG_NAMES":"","DATA_TABLE":"OA_BASE_DATA","DATA_ID":"H818100000121","HOSPITAL_ID":null,"MODULE_ID":null,"FATHER_ID":null,"CLASS_ID":"H810000-JBXXX75570-78vemt4u","DATA_SORT":"1748913185","DATA_NAME":"选项1","DATA_SNAME":null,"DATA_ENAME":null,"STANDART_ID":null,"CUSTOM_CODE":null,"SPELL_CODE":null,"STATE_FLAG":"1","FIRST_RPERSON":"110_渠晓娜","FIRST_RTIME":"2025-06-03 09:13:05","LAST_MPERSON":"110_渠晓娜","LAST_MTIME":"2025-06-03 09:13:05","REMARK":null},{"PERSON_TAG_IDS":[],"PERSON_TAG_NAMES":"","DATA_TABLE":"OA_BASE_DATA","DATA_ID":"H818100000122","HOSPITAL_ID":null,"MODULE_ID":null,"FATHER_ID":null,"CLASS_ID":"H810000-JBXXX75570-78vemt4u","DATA_SORT":"1748913189","DATA_NAME":"选项2","DATA_SNAME":null,"DATA_ENAME":null,"STANDART_ID":null,"CUSTOM_CODE":null,"SPELL_CODE":null,"STATE_FLAG":"1","FIRST_RPERSON":"110_渠晓娜","FIRST_RTIME":"2025-06-03 09:13:09","LAST_MPERSON":"110_渠晓娜","LAST_MTIME":"2025-06-03 09:13:09","REMARK":null},{"PERSON_TAG_IDS":[],"PERSON_TAG_NAMES":"","DATA_TABLE":"OA_BASE_DATA","DATA_ID":"H818100000123","HOSPITAL_ID":null,"MODULE_ID":null,"FATHER_ID":null,"CLASS_ID":"H810000-JBXXX75570-78vemt4u","DATA_SORT":"1748913193","DATA_NAME":"选项3","DATA_SNAME":null,"DATA_ENAME":null,"STANDART_ID":null,"CUSTOM_CODE":null,"SPELL_CODE":null,"STATE_FLAG":"1","FIRST_RPERSON":"110_渠晓娜","FIRST_RTIME":"2025-06-03 09:13:13","LAST_MPERSON":"110_渠晓娜","LAST_MTIME":"2025-06-03 09:13:13","REMARK":null}]}},"queryType":"enum"},{"formName":"基本信息111","formCode":"JBXX11110767","formCname":"基本信息111","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Select","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","defaultValue","isDataTypeToPerson"],"styleJson":{},"queryType":"enum"},{"formName":"生化基本信息","formCode":"SHJBXX95580","formCname":"生化基本信息","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Select","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","defaultValue","isDataTypeToPerson"],"styleJson":{},"queryType":"enum","isDataTypeToPerson":"True"},{"formName":"多行基本信息","formCode":"DHJBXX59257","formCname":"多行基本信息","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"TextArea","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","minRows","maxRows","variant","disabled","showCount"],"styleJson":{}},{"formName":"数值","formCode":"SZ52717","formCname":"数值","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"InputNumber","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","min","max","prefix","suffix","variant","disabled","controls"],"styleJson":{}},{"formName":"工作管理类型","formCode":"GZGLLX49535","formCname":"工作管理类型","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Select","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","defaultValue","isDataTypeToPerson"],"styleJson":{},"queryType":"enum"},{"formName":"基本信息增加了","formCode":"JBXXZJL58931","formCname":"基本信息增加了","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"TextArea","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{}},{"formName":"0605","formCode":"060524863","formCname":"0605","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Select","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","defaultValue","isDataTypeToPerson"],"styleJson":{},"queryType":"enum","isDataTypeToPerson":"True"},{"formName":"基本信息","formCode":"JBXX70654","formCname":"基本信息","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{}},{"formName":"颜色选择器","formCode":"YSXZQ25786","formCname":"颜色选择器","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"ColorPicker","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","disabled","showText"],"styleJson":{}},{"formName":"基本信息0606","formCode":"JBXX060622841","formCname":"基本信息0606","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Select","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","optionType","styleFlex","disabled","queryType","defaultValue","isDataTypeToPerson"],"styleJson":{},"queryType":"enum"},{"formName":"单选","formCode":"DX88389","formCname":"单选","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Radio","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","optionType","styleFlex","disabled","queryType","defaultValue","isDataTypeToPerson"],"styleJson":{},"queryType":"enum"},{"formName":"滑动输入条","formCode":"HDSRT92974","formCname":"滑动输入条","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Slider","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","disabled","max","min"],"styleJson":{}},{"formName":"评分","formCode":"PF43843","formCname":"评分","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Rate","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","allowHalf","disabled"],"styleJson":{}},{"formName":"060611","formCode":"06061133563","formCname":"060611","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Checkbox","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","styleFlex","disabled","queryType","defaultValue","isDataTypeToPerson"],"styleJson":{},"queryType":"enum"},{"formName":"复选","formCode":"FX42243","formCname":"复选","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Checkbox","sort":null,"dataClass":"参会人身份","allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","styleFlex","disabled","queryType","dataClass","defaultValue","isDataTypeToPerson"],"styleJson":{},"queryType":"api"},{"formName":"开关","formCode":"KG13514","formCname":"开关","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Switch","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","checkedChildren","unCheckedChildren"],"styleJson":{}},{"formName":"岗位类别","formCode":"GWLB57036","formCname":"岗位类别","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{}},{"formName":"下拉数据","formCode":"XLSJ15945","formCname":"下拉数据","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Select","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","defaultValue","isDataTypeToPerson"],"styleJson":{},"queryType":"enum"},{"formName":"下拉数据","formCode":"XLSJ30139","formCname":"下拉数据","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Select","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","defaultValue","isDataTypeToPerson"],"styleJson":{},"queryType":"enum"},{"formName":"英语级别/成绩(分)","formCode":"YYJB50394","formCname":"英语级别/成绩(分)","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":null,"sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"styleJson":{}}]}', NULL, NULL, '33A001');
INSERT INTO XH_SYS.SYS6_MODULE_FUNC_DICT (SETUP_ID, FUNC_ID, MODULE_ID, SETUP_NAME, SETUP_CNAME, SETUP_DESC, SETUP_CLASS, SETUP_SORT, SKIN_ID, SETUP_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK, IF_DOUBLE_CLICK, FORM_JSON, FORM_COL_JSON, FORM_QUERY_JSON, IF_QUERY, HOSPITAL_ID) VALUES('H81_TG000003X', 'H8107', 'H81', '生安人员表单', '生安人员表单', NULL, 'B05-66|B05-67', NULL, NULL, '1', 'H81初始化', TIMESTAMP '2025-05-28 16:42:49.000000', 'H81初始化', TIMESTAMP '2025-06-11 10:26:30.000000', NULL, NULL, '{"layout":{"type":"fixed","widthPercent":100,"labelCol":4,"verticalGap":16,"size":"default","style":{"paddingTop":4,"paddingBottom":4},"showRowNum":false,"labelLayout":"horizontal","labelWrap":true},"rows":[{"rowId":"row_703132c2","cols":[{"colId":"col_98054ad7","span":24,"items":[{"itemId":"item_70314472","group":"基本","formId":"title","icon":"iconbiaotilan","formName":"基本信息","isForm":false,"dataType":"XTitle","wigetProps":{"button":false,"groupKey":""},"propslist":["button","buttonSize","groupKey"],"labelStyle":{"background":"rgb(241,247,255)"}}]}]},{"rowId":"row_4489546d","rowType":"fixed","cols":[{"colId":"col_4489e428","span":8,"items":[{"itemId":"item_2962ce6a","colSpan":8,"wigetType":"fixed","formName":"姓名/工号","formCname":"姓名","formCode":"USER_NAME","isForm":true,"dataType":"Input","labelCols":3,"groupItems":{"itemId":"item_42883768","connector":"/"}},{"itemId":"item_42883768","colSpan":8,"wigetType":"fixed","formName":"工号","formCname":"工号","formCode":"HIS_ID","isForm":true,"dataType":"Input","labelCols":3},{"itemId":"item_59761567","group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"专业组/分类","dataType":"Select","dataClass":"PGROUP_ID","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"formCode":"PGROUP_ID","queryType":"api"},{"itemId":"item_320126b3","colSpan":8,"wigetType":"fixed","formName":"政治面貌","formCname":"政治面貌","formCode":"POLITICIAN","isForm":true,"dataType":"Select","labelCols":3,"queryType":"api"},{"itemId":"item_4129cfb5","group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"证件号码","dataType":"Input","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"formCode":"ID_CARD"},{"itemId":"item_09000125","colSpan":8,"wigetType":"fixed","formName":"性别/年龄","formCname":"性别","formCode":"SEX","isForm":true,"dataType":"Select","labelCols":3,"groupItems":{"itemId":"item_8296cde6","connector":"/"},"queryType":"api"},{"itemId":"item_0015dd2a","colSpan":8,"wigetType":"fixed","formName":"最高学位","formCname":"最高学位","formCode":"HIGHEST_DIPLOMA","isForm":true,"dataType":"Select","labelCols":3,"queryType":"api"},{"itemId":"item_8938132c","colSpan":8,"wigetType":"fixed","formName":"毕业专业","formCname":"毕业专业","formCode":"PROFESSION","isForm":true,"dataType":"Input","labelCols":3},{"itemId":"item_4481cefc","group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"参加工作日期","dataType":"DatePicker","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false,"defaultValue":null},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"formCode":"WORK_TIME","picker":"date"}]},{"colId":"col_44892e0a","span":8,"items":[{"itemId":"item_139462f3","colSpan":8,"wigetType":"fixed","formName":"登录ID","formCname":"登录ID","formCode":"LOGID","isForm":true,"dataType":"Input","labelCols":3},{"itemId":"item_8296cde6","colSpan":8,"wigetType":"fixed","formName":"年龄","formCname":"年龄","formCode":"AGE","isForm":true,"dataType":"Input","labelCols":3},{"itemId":"item_5492c71d","colSpan":8,"wigetType":"fixed","formName":"备案实验室","formCname":"备案实验室","formCode":"BASYS55209","isForm":true,"dataType":"Input","labelCols":3},{"itemId":"item_615434a6","group":"基本","formId":"shijian","icon":"iconshijian1","formName":"来科日期","dataType":"DatePicker","isForm":true,"rules":[{"required":true}],"wigetProps":{"defaultValue":null},"propslist":["dataType","required","disabled","allowClear"],"formCode":"IN_LAB_TIME","picker":"date"},{"itemId":"item_56524f1d","colSpan":8,"wigetType":"fixed","formName":"毕业日期","formCname":"毕业日期","formCode":"GRADUATE_DATE","isForm":true,"dataType":"DatePicker","labelCols":3,"picker":"date"},{"itemId":"item_83732b44","colSpan":8,"wigetType":"fixed","formName":"毕业院校","formCname":"毕业院校","formCode":"GRADUATE_SCHOOL","isForm":true,"dataType":"Input","labelCols":3},{"itemId":"item_7247fc33","colSpan":8,"wigetType":"fixed","formName":"最高学历","formCname":"最高学历","formCode":"HIGHEST_DEGREE","isForm":true,"dataType":"Select","labelCols":3,"queryType":"api"},{"itemId":"item_32478339","colSpan":8,"wigetType":"fixed","formName":"出生日期","formCname":"出生年月","formCode":"BIRTHDAY","isForm":true,"dataType":"DatePicker","labelCols":3},{"itemId":"item_7151dcb2","colSpan":8,"wigetType":"fixed","formName":"证件类型","formCname":"证件类型","formCode":"CARD_TYPE","isForm":true,"dataType":"Select","labelCols":3,"queryType":"api"}]},{"colId":"col_44899c0b","span":8,"items":[{"itemId":"item_3431c44d","group":"基本","formId":"tupianfujian","icon":"icontouxiang","formName":"","formNameHide":true,"dataType":"ImgUpload","isForm":true,"wigetProps":{"width":180,"height":220,"accept":".png,.jpg,.jpeg","style":{"justifyContent":"center","textAlign":"center","display":"flex"}},"propslist":["dataType","required","width","height"],"formCode":"USER_IMG","labelStyle":{"justifyContent":"center","textAlign":"center","display":"flex"}}]}],"style":{"paddingTop":0,"paddingLeft":0}},{"rowId":"row_5451a683","rowType":"fixed","cols":[{"colId":"col_54514c9e","span":8,"items":[{"itemId":"item_0767e0e3","group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"联系方式","dataType":"Input","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"formCode":"PHONE"}]},{"colId":"col_5451fe44","span":8,"items":[{"itemId":"item_51265c4b","colSpan":8,"wigetType":"fixed","formName":"办公电话","formCname":"办公电话","formCode":"OFFICE_PHONE","isForm":true,"dataType":"Input","labelCols":3}]},{"colId":"col_545176f0","span":8,"items":[{"itemId":"item_184214d0","colSpan":8,"wigetType":"fixed","formName":"是否外单位科研合作人员","formCname":"是否外单位科研合作人员","formCode":"SFWDWKYHZRY39890","isForm":true,"dataType":"Radio","labelCols":3}]}]},{"rowId":"row_792943be","rowType":"fixed","cols":[{"colId":"col_7929c9ea","span":8,"items":[{"itemId":"item_46903557","colSpan":8,"wigetType":"fixed","formName":"职务","formCname":"行政职务","formCode":"DUTIES","isForm":true,"dataType":"Select","labelCols":3,"queryType":"api"}]},{"colId":"col_792978e6","span":8,"items":[{"itemId":"item_6728683d","colSpan":8,"wigetType":"fixed","formName":"职称级别","formCname":"职称级别","formCode":"TECH_POST","isForm":true,"dataType":"Select","labelCols":3,"queryType":"api"}]},{"colId":"col_792902f7","span":8,"items":[{"itemId":"item_7651faaa","colSpan":8,"wigetType":"fixed","formName":"职称名称","formCname":"职称名称","formCode":"ACADEMIC_POST","isForm":true,"dataType":"Select","labelCols":3,"queryType":"api"}]}]},{"rowId":"row_6000ae73","rowType":"fixed","cols":[{"colId":"col_5999c0a3","span":24,"items":[{"itemId":"item_02983c63","colSpan":8,"wigetType":"fixed","formName":"备注","formCname":"备注","formCode":"BZ63459","isForm":true,"dataType":"Input","labelCols":3}]}]},{"rowId":"row_872901bb","cols":[{"colId":"col_87295e8e","span":24,"items":[{"itemId":"item_8729c365","group":"基本","formId":"title","icon":"iconbiaotilan","formName":"岗位信息","isForm":false,"dataType":"XTitle","wigetProps":{"button":false,"groupKey":""},"propslist":["button","buttonSize","groupKey"],"labelStyle":{"background":"rgb(241,247,255)"}}]}]},{"rowId":"row_44289f89","rowType":"fixed","cols":[{"colId":"col_44282d91","span":8,"items":[{"itemId":"item_8652e515","colSpan":8,"wigetType":"fixed","formName":"现从事岗位","formCname":"现从事岗位","formCode":"XCSGW70984","isForm":true,"dataType":"Select","labelCols":3}]},{"colId":"col_442871d2","span":8,"items":[{"itemId":"item_0336a8d4","colSpan":8,"wigetType":"fixed","formName":"岗位类别","formCname":"岗位类别","formCode":"GWLB79071","isForm":true,"dataType":"Select","labelCols":3}]},{"colId":"col_44281ab4","span":8,"items":[]}]},{"rowId":"row_6645162b","cols":[{"colId":"col_66457692","span":24,"items":[{"itemId":"item_66458985","group":"基本","formId":"title","icon":"iconbiaotilan","formName":"生物安全培训情况","isForm":false,"dataType":"XTitle","wigetProps":{"button":false,"groupKey":"row_50147784"},"propslist":["button","buttonSize","groupKey"],"labelStyle":{"background":"rgb(241,247,255)"}}]}]},{"rowId":"row_50147784","rowType":"fixed","cols":[{"colId":"col_50141556","span":8,"items":[{"itemId":"item_9050973c","colSpan":8,"wigetType":"fixed","formName":"参加培训情况","formCname":"参加培训情况","formCode":"CJPXQK82201","isForm":true,"dataType":"Input","labelCols":3}]},{"colId":"col_5014acb7","span":8,"items":[{"itemId":"item_1026fb5c","colSpan":8,"wigetType":"fixed","formName":"培训合格证号","formCname":"培训合格证号","formCode":"PXHGZH48698","isForm":true,"dataType":"Input","labelCols":3}]},{"colId":"col_50144ff2","span":8,"items":[]}]},{"rowId":"row_3398cb84","cols":[{"colId":"col_33987472","span":8,"items":[{"itemId":"item_339843b6","colSpan":8,"wigetType":"fixed","formName":"培训合格证书","formCname":"培训合格证书","formCode":"PXHGZS25179","isForm":true,"dataType":"Upload","labelCols":3}]}]}]}', '{"form":[{"formName":"民族","formCode":"NATION","formCname":"民族","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Select","sort":null,"dataClass":"民族","allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"styleJson":{},"queryType":"api"},{"formName":"姓名","formCode":"USER_NAME","formCname":"姓名","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"styleJson":{"dataType":"Input","rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false}}},{"formName":"工号","formCode":"HIS_ID","formCname":"工号","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{"dataType":"Input","rules":[{"required":null}],"wigetProps":{}}},{"formName":"出生年月","formCode":"BIRTHDAY","formCname":"出生年月","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"DatePicker","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","disabled","picker","allowClear"],"styleJson":{"dataType":"DatePicker","rules":[{"required":null}],"wigetProps":{"defaultValue":null}}},{"formName":"登录ID","formCode":"LOGID","formCname":"登录ID","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"styleJson":{"dataType":"Input","rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false}}},{"formName":"专业组","formCode":"PGROUP_ID","formCname":"专业组","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Select","sort":null,"dataClass":"DataClass_By_PGROUP_ID","allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","defaultValue","isDataTypeToPerson"],"styleJson":{},"queryType":"api"},{"formName":"证件号码","formCode":"ID_CARD","formCname":"证件号码","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{}},{"formName":"性别/年龄","formCode":"SEX","formCname":"性别/年龄","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Select","sort":null,"dataClass":"性别","allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"styleJson":{"dataClass":"性别","dataType":"Select","rules":[{"required":true}],"wigetProps":{}},"queryType":"api"},{"formName":"身高","formCode":"HEIGHT","formCname":"身高","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{}},{"formName":"紧急联系人","formCode":"EMERGENCY_CONTACT","formCname":"紧急联系人","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{}},{"formName":"与紧急联系人的关系","formCode":"ECONTACT_RELACTION","formCname":"与紧急联系人的关系","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{}},{"formName":"紧急联系人电话","formCode":"ECONTACT_PHONE","formCname":"紧急联系人电话","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{}},{"formName":"详细通讯地址","formCode":"COMM_ADDR","formCname":"详细通讯地址","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{}},{"formName":"人员性质","formCode":"PERSON_DOC_STATE","formCname":"人员性质","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{}},{"formName":"入职方式","formCode":"EMPLOYMENT_SOURE","formCname":"入职方式","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Select","sort":null,"dataClass":"入职方式","allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"styleJson":{},"queryType":"api"},{"formName":"用工类型","formCode":"USER_TYPE","formCname":"用工类型","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Select","sort":null,"dataClass":"用工类型","allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"styleJson":{},"queryType":"api"},{"formName":"职称类型","formCode":"TECHNOLOGY_TYPE","formCname":"职称类型","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Select","sort":null,"dataClass":"职称类型","allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"styleJson":{},"queryType":"api"},{"formName":"职称专业","formCode":"TECH_POST_PROFESSION","formCname":"职称专业","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{}},{"formName":"聘任职称评定单位","formCode":"EMPLOYMENT_UNIT","formCname":"聘任职称评定单位","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{}},{"formName":"职称名称","formCode":"ACADEMIC_POST","formCname":"职称名称","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Select","sort":null,"dataClass":"医师职称","allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{"dataClass":"医师职称","dataType":"Select","wigetProps":{"options":[{"value":"选项1","label":"选项1"},{"value":"选项2","label":"选项2"},{"value":"选项3","label":"选项3"}],"defaultValue":""}},"queryType":"api"},{"formName":"职称评定日期","formCode":"TECH_CERTIFICE_TIME","formCname":"职称评定日期","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"DatePicker","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","disabled","picker","allowClear"],"styleJson":{}},{"formName":"参加工作日期","formCode":"WORK_TIME","formCname":"参加工作日期","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"DatePicker","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","disabled","picker","allowClear"],"styleJson":{}},{"formName":"退休日期","formCode":"RETIRE_TIME","formCname":"退休日期","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"DatePicker","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","disabled","picker","allowClear"],"styleJson":{}},{"formName":"工龄","formCode":"LENGTH_SERVICE","formCname":"工龄","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{}},{"formName":"年龄","formCode":"AGE","formCname":"年龄","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{"dataType":"Input","rules":[{"required":null}],"wigetProps":{}}},{"formName":"籍贯","formCode":"NATIVE_PLACE","formCname":"籍贯","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Cascader","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","styleJson":{}},{"formName":"户籍所在地","formCode":"DOMICILE_PLACE","formCname":"户籍所在地","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Cascader","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","styleJson":{}},{"formName":"现居住地","formCode":"CURRENT_ADDRESS","formCname":"现居住地","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{}},{"formName":"最高学历","formCode":"HIGHEST_DEGREE","formCname":"最高学历","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Select","sort":null,"dataClass":"最高学历","allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"styleJson":{"dataClass":"最高学历","dataType":"Select","rules":[{"required":null}],"wigetProps":{}},"queryType":"api"},{"formName":"毕业院校","formCode":"GRADUATE_SCHOOL","formCname":"毕业院校","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{"dataType":"Input","rules":[{"required":null}],"wigetProps":{}}},{"formName":"毕业日期","formCode":"GRADUATE_DATE","formCname":"毕业日期","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"DatePicker","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","disabled","picker","allowClear"],"styleJson":{"dataType":"DatePicker","rules":[{"required":null}],"wigetProps":{}}},{"formName":"毕业专业","formCode":"PROFESSION","formCname":"毕业专业","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{"dataType":"Input","rules":[{"required":null}],"wigetProps":{}}},{"formName":"英语等级","formCode":"ENGLISH_RANK","formCname":"英语等级","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Select","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{},"queryType":"api"},{"formName":"婚姻状况","formCode":"MARITAL_STATUS","formCname":"婚姻状况","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Radio","sort":null,"dataClass":"婚姻状况","allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","optionType","styleFlex","disabled","queryType","dataClass","defaultValue","isDataTypeToPerson"],"styleJson":{},"queryType":"api"},{"formName":"颜色视觉障碍","formCode":"COLOR_DEFICIENCY","formCname":"颜色视觉障碍","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Radio","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","optionType","styleFlex","disabled","queryType","defaultValue","isDataTypeToPerson"],"styleJson":{},"queryType":"enum"},{"formName":"视力","formCode":"EYESIGHT_RIGHT","formCname":"视力","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{}},{"formName":"视力（左/右）","formCode":"EYESIGHT_LEFT","formCname":"视力（左/右）","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{}},{"formName":"专业特长","formCode":"PROFESSION_EXPERTISE","formCname":"专业特长","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{}},{"formName":"联系方式","formCode":"PHONE","formCname":"联系方式","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{}},{"formName":"邮箱","formCode":"E_MAIL","formCname":"邮箱","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{}},{"formName":"办公电话","formCode":"OFFICE_PHONE","formCname":"办公电话","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{"dataType":"Input","rules":[{"required":null}],"wigetProps":{}}},{"formName":"政治面貌","formCode":"POLITICIAN","formCname":"政治面貌","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Select","sort":null,"dataClass":"政治面貌","allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"styleJson":{"dataClass":"政治面貌","dataType":"Select","rules":[{"required":null}],"wigetProps":{}},"queryType":"api"},{"formName":"最高学位","formCode":"HIGHEST_DIPLOMA","formCname":"最高学位","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Select","sort":null,"dataClass":"最高学位","allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"styleJson":{"dataClass":"最高学位","dataType":"Select","wigetProps":{"options":[{"value":"选项1","label":"选项1"},{"value":"选项2","label":"选项2"},{"value":"选项3","label":"选项3"}],"defaultValue":""}},"queryType":"api"},{"formName":"来院日期","formCode":"IN_HOSPITAL_TIME","formCname":"来院日期","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"DatePicker","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","disabled","picker","allowClear"],"styleJson":{}},{"formName":"离院日期","formCode":"OUT_HOSPITAL_TIME","formCname":"离院日期","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"DatePicker","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","disabled","picker","allowClear"],"styleJson":{}},{"formName":"院龄","formCode":"LENGTH_HOSPITAL","formCname":"院龄","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{}},{"formName":"来科日期","formCode":"IN_LAB_TIME","formCname":"来科日期","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"DatePicker","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","disabled","picker","allowClear"],"styleJson":{}},{"formName":"离科日期","formCode":"OUT_LAB_TIME","formCname":"离科日期","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"DatePicker","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","disabled","picker","allowClear"],"styleJson":{}},{"formName":"科龄","formCode":"LENGTH_LAB","formCname":"科龄","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{}},{"formName":"新字段","formCode":"XZD35566","formCname":"新字段","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":null,"sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{}},{"formName":"健康状况","formCode":"HEALTH","formCname":"健康状况","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Select","sort":null,"dataClass":"健康状况","allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"styleJson":{},"queryType":"api"},{"formName":"第二新字段","formCode":"DEXZD91452","formCname":"第二新字段","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":null,"sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","defaultValue","isDataTypeToPerson"],"styleJson":{}},{"formName":"第三字段","formCode":"DSZD27003","formCname":"第三字段","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":null,"sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","defaultValue","isDataTypeToPerson"],"styleJson":{}},{"formName":"证件类型","formCode":"CARD_TYPE","formCname":"证件类型","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Select","sort":null,"dataClass":"证件类型","allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"styleJson":{"dataClass":"证件类型","dataType":"Select","rules":[{"required":null}],"wigetProps":{"defaultValue":"1"}},"queryType":"api"},{"formName":"职称级别","formCode":"TECH_POST","formCname":"职称级别","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Select","sort":null,"dataClass":"职称级别","allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"styleJson":{"dataClass":"职称级别","dataType":"Select","rules":[{"required":null}],"wigetProps":{}},"queryType":"api"},{"formName":"最新字段","formCode":"ZXZD69033","formCname":"最新字段","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":null,"sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","defaultValue","isDataTypeToPerson"],"styleJson":{}},{"formName":"最新字段","formCode":"ZXZD62529","formCname":"最新字段","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":null,"sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","defaultValue","isDataTypeToPerson"],"styleJson":{}},{"formName":"职务","formCode":"DUTIES","formCname":"职务","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Select","sort":null,"dataClass":"职务","allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"styleJson":{"dataClass":"职务","dataType":"Select","rules":[{"required":null}],"wigetProps":{}},"queryType":"api"},{"formName":"最新字段","formCode":"ZXZD59943","formCname":"最新字段","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":null,"sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","defaultValue","isDataTypeToPerson"],"styleJson":{}},{"formName":"英语级别/成绩（分）","formCode":"ENGLISH_RANK_SCORE","formCname":"英语级别/成绩（分）","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"styleJson":{}},{"formName":"测试字段1","formCode":"CSZD180389","formCname":"测试字段1","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":null,"sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{}},{"formName":"有无子女","formCode":"CHILDREN_CONDITION","formCname":"有无子女","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Radio","sort":null,"dataClass":"有无子女","allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"styleJson":{},"queryType":"api"},{"formName":"新字段","formCode":"XZD61188","formCname":"新字段","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":null,"sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","minRows","maxRows","variant","disabled","showCount"],"styleJson":{}},{"formName":"第二新字段","formCode":"DEXZD63195","formCname":"第二新字段","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":null,"sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","minRows","maxRows","variant","disabled","showCount"],"styleJson":{}},{"formName":"测试字段1","formCode":"CSZD144599","formCname":"测试字段1","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":null,"sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","min","max","prefix","suffix","variant","disabled","controls"],"styleJson":{}},{"formName":"测试字段2","formCode":"CSZD296678","formCname":"测试字段2","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":null,"sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","min","max","prefix","suffix","variant","disabled","controls"],"styleJson":{}},{"formName":"备案实验室","formCode":"BASYS55209","formCname":"备案实验室","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{"dataType":"Input","rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false}}},{"formName":"是否外单位科研合作人员","formCode":"SFWDWKYHZRY39890","formCname":"是否外单位科研合作人员","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Radio","sort":null,"dataClass":"是否","allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","optionType","styleFlex","disabled","queryType","dataClass","defaultValue","isDataTypeToPerson"],"styleJson":{"dataClass":"是否","dataType":"Radio","wigetProps":{"options":[],"defaultValue":""}}},{"formName":"备注","formCode":"BZ63459","formCname":"备注","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{"dataType":"Input","rules":[{"required":false}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false}}},{"formName":"岗位类别","formCode":"GWLB79071","formCname":"岗位类别","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Select","sort":null,"dataClass":"岗位类别","allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{"dataClass":"岗位类别","dataType":"Select","wigetProps":{"options":[{"value":"选项1","label":"选项1"},{"value":"选项2","label":"选项2"},{"value":"选项3","label":"选项3"}],"defaultValue":""}},"queryType":"api"},{"formName":"现从事岗位","formCode":"XCSGW70984","formCname":"现从事岗位","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Select","sort":null,"dataClass":"现从事岗位","allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{"dataClass":"现从事岗位","dataType":"Select","wigetProps":{"options":[{"value":"选项1","label":"选项1"},{"value":"选项2","label":"选项2"},{"value":"选项3","label":"选项3"}],"defaultValue":""}},"queryType":"api"},{"formName":"参加培训情况","formCode":"CJPXQK82201","formCname":"参加培训情况","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{"dataType":"Input","rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false}}},{"formName":"培训合格证号","formCode":"PXHGZH48698","formCname":"培训合格证号","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Input","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{"dataType":"Input","rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false}}},{"formName":"培训合格证书","formCode":"PXHGZS25179","formCname":"培训合格证书","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":"Upload","sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","multiple","listType","accept"],"styleJson":{"dataType":"Upload","wigetProps":{"listType":"picture-card","multiple":true,"accept":".png,.jpg,.jpeg,.pdf,.xls,.xlsx,.doc,.docx","showUploadList":true}}},{"formName":"新字段","formCode":"XZD45449","formCname":"新字段","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":null,"sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"styleJson":{}},{"formName":"新增-文本","formCode":"XZWB49684","formCname":"新增-文本","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":null,"sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","defaultValue","isDataTypeToPerson"],"styleJson":{}},{"formName":"生化基本信息","formCode":"SHJBXX95580","formCname":"生化基本信息","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":null,"sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","defaultValue","isDataTypeToPerson"],"styleJson":{}},{"formName":"多行基本信息","formCode":"DHJBXX59257","formCname":"多行基本信息","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":null,"sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","minRows","maxRows","variant","disabled","showCount"],"styleJson":{}},{"formName":"数值","formCode":"SZ52717","formCname":"数值","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":null,"sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","min","max","prefix","suffix","variant","disabled","controls"],"styleJson":{}},{"formName":"删除测试","formCode":"SCCS85056","formCname":"删除测试","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":null,"sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","min","max","prefix","suffix","variant","disabled","controls"],"styleJson":{}},{"formName":"基本信息项","formCode":"JBXXX75570","formCname":"基本信息项","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":null,"sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","defaultValue","isDataTypeToPerson"],"styleJson":{}},{"formName":"工作管理类型","formCode":"GZGLLX49535","formCname":"工作管理类型","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":null,"sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","defaultValue","isDataTypeToPerson"],"styleJson":{}},{"formName":"基本信息增加了","formCode":"JBXXZJL58931","formCname":"基本信息增加了","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":null,"sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","minRows","maxRows","variant","disabled","showCount"],"styleJson":{}},{"formName":"0605","formCode":"060524863","formCname":"0605","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":null,"sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","defaultValue","isDataTypeToPerson"],"styleJson":{}},{"formName":"基本信息","formCode":"JBXX70654","formCname":"基本信息","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":null,"sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{}},{"formName":"下拉过滤测试","formCode":"XZXL86444","formCname":"下拉过滤测试","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":null,"sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","defaultValue","isDataTypeToPerson"],"styleJson":{}},{"formName":"新增-下拉-自定义","formCode":"XZXLZDY94278","formCname":"新增-下拉-自定义","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":null,"sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","minRows","maxRows","variant","disabled","showCount"],"styleJson":{}},{"formName":"颜色选择器","formCode":"YSXZQ25786","formCname":"颜色选择器","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":null,"sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","disabled","showText"],"styleJson":{}},{"formName":"基本信息0606","formCode":"JBXX060622841","formCname":"基本信息0606","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":null,"sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","defaultValue","isDataTypeToPerson"],"styleJson":{}},{"formName":"岗位类别","formCode":"GWLB57036","formCname":"岗位类别","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":null,"sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"],"styleJson":{}},{"formName":"滑动输入条","formCode":"HDSRT92974","formCname":"滑动输入条","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":null,"sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","disabled","max","min"],"styleJson":{}},{"formName":"基本信息111","formCode":"JBXX11110767","formCname":"基本信息111","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":null,"sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","defaultValue","isDataTypeToPerson"],"styleJson":{}},{"formName":"评分","formCode":"PF43843","formCname":"评分","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":null,"sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","allowHalf","disabled"],"styleJson":{}},{"formName":"060611","formCode":"06061133563","formCname":"060611","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":null,"sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","styleFlex","disabled","queryType","defaultValue","isDataTypeToPerson"],"styleJson":{}},{"formName":"复选","formCode":"FX42243","formCname":"复选","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":null,"sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","styleFlex","disabled","queryType","dataClass","defaultValue","isDataTypeToPerson"],"styleJson":{}},{"formName":"开关","formCode":"KG13514","formCname":"开关","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":null,"sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","checkedChildren","unCheckedChildren"],"styleJson":{}},{"formName":"英语级别/成绩(分)","formCode":"YYJB50394","formCname":"英语级别/成绩(分)","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":null,"sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"styleJson":{}},{"formName":"下拉数据","formCode":"XLSJ15945","formCname":"下拉数据","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":null,"sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","defaultValue","isDataTypeToPerson"],"styleJson":{}},{"formName":"下拉数据","formCode":"XLSJ30139","formCname":"下拉数据","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":null,"sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","defaultValue","isDataTypeToPerson"],"styleJson":{}},{"formName":"单选","formCode":"DX88389","formCname":"单选","ifShow":true,"titleColor":"#000000","titleShow":true,"titleSize":13,"titleStyle":"0","titleBackground":null,"titleAlign":"3","contentLine":1,"contentMaxLine":2,"contentHeightClass":1,"contentHeightRatio":null,"contentAlign":"1","contentColor":"#000000","contentFontSize":13,"contentStyle":"0","contentBackground":null,"titleAndContentType":"1","contentEnlarge":null,"ifRequired":null,"replaceField":null,"onlyRead":"1","default":null,"resetContent":null,"dataType":null,"sort":null,"dataClass":null,"allowMaintainDropDownData":false,"editeState":false,"formDesc":null,"suffix":null,"unitFlag":false,"isClinaField":false,"assistId":null,"assistClass":null,"assistClassName":null,"disabled":false,"formId":null,"fieldClass":"基本信息","readonlyPropslist":["dataType","required","optionType","styleFlex","disabled","queryType","defaultValue","isDataTypeToPerson"],"styleJson":{}}]}', NULL, NULL, '33A001');