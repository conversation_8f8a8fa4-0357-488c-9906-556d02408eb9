using System;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using SqlSugar;
using H.Utility.SqlSugarInfra;

namespace XH.H81.Models.ExternalEntity
{
    /// <summary>
    /// 检验单元信息表
    /// </summary>
    [Table("LIS6_INSPECTION_GROUP")]
    [DBOwner("XH_SYS")]
    public class LIS6_INSPECTION_GROUP
	{
		/// <summary>
		/// 
		/// </summary>
		[Key]
        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        //[Column("GROUP_ID")]
		[Required(ErrorMessage = "不允许为空")]

		[StringLength(10, ErrorMessage = "GROUP_ID长度不能超出10字符")]
		//[Unicode(false)]
		public string GROUP_ID { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("LAB_ID")]
		[Required(ErrorMessage = "不允许为空")]

		[StringLength(20, ErrorMessage = "LAB_ID长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string LAB_ID { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("FIRST_RPERSON")]
		[StringLength(50, ErrorMessage = "FIRST_RPERSON长度不能超出50字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FIRST_RPERSON { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("AREA_ID")]
		[StringLength(20, ErrorMessage = "AREA_ID长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? AREA_ID { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("START_NO")]
		[StringLength(2, ErrorMessage = "START_NO长度不能超出2字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? START_NO { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("LAST_MTIME")]
		//[Unicode(false)]
		public DateTime? LAST_MTIME { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("START_FOCUS")]
		[StringLength(20, ErrorMessage = "START_FOCUS长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? START_FOCUS { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("REVIEW_TYPE")]
		[StringLength(10, ErrorMessage = "REVIEW_TYPE长度不能超出10字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? REVIEW_TYPE { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("NUM_PREFIX")]
		[StringLength(10, ErrorMessage = "NUM_PREFIX长度不能超出10字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? NUM_PREFIX { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("CUNIT_ID")]
		[StringLength(20, ErrorMessage = "CUNIT_ID长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CUNIT_ID { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("OUT_UNIT_ID")]
		[StringLength(20, ErrorMessage = "OUT_UNIT_ID长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? OUT_UNIT_ID { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("GROUP_CLASS")]
		[StringLength(50, ErrorMessage = "GROUP_CLASS长度不能超出50字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? GROUP_CLASS { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("NUM_IF_CHAR")]
		[StringLength(10, ErrorMessage = "NUM_IF_CHAR长度不能超出10字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? NUM_IF_CHAR { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("REPORT_MODE")]
		[StringLength(20, ErrorMessage = "REPORT_MODE长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? REPORT_MODE { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("GROUP_CODE")]
		[StringLength(20, ErrorMessage = "GROUP_CODE长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? GROUP_CODE { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("QC_UNIT_ID")]
		[StringLength(20, ErrorMessage = "QC_UNIT_ID长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? QC_UNIT_ID { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("STATE_FLAG")]
		[StringLength(10, ErrorMessage = "STATE_FLAG长度不能超出10字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? STATE_FLAG { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("REMARK")]
		[StringLength(200, ErrorMessage = "REMARK长度不能超出200字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? REMARK { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("FIRST_RTIME")]
		//[Unicode(false)]
		public DateTime? FIRST_RTIME { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("LAST_MPERSON")]
		[StringLength(50, ErrorMessage = "LAST_MPERSON长度不能超出50字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? LAST_MPERSON { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("NUM_SUFFIX_LEN")]
		//[Unicode(false)]
		public decimal? NUM_SUFFIX_LEN { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("GROUP_SORT")]
		[StringLength(20, ErrorMessage = "GROUP_SORT长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? GROUP_SORT { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("GROUP_NAME")]
		[StringLength(50, ErrorMessage = "GROUP_NAME长度不能超出50字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? GROUP_NAME { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("PGROUP_ID")]
		[StringLength(20, ErrorMessage = "PGROUP_ID长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? PGROUP_ID { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("HOSPITAL_ID")]
		[Required(ErrorMessage = "不允许为空")]

		[StringLength(20, ErrorMessage = "HOSPITAL_ID长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string HOSPITAL_ID { get; set; }


	}
}
