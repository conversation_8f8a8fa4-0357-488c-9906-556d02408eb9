﻿using H.BASE.SqlSugarInfra.Uow;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using Spire.Presentation.Collections;
using System.Diagnostics;
using System.Drawing.Text;
using XH.H81.IServices;
using XH.H81.Models.Dtos.Template;
using XH.H81.Models.Entities;
using XH.H81.Models.Entities.Pms;
using XH.H81.Models.Entities.Tag;
using XH.H81.Models.SugarDbContext;
using XH.LAB.UTILS.Helpers;
using XH.LAB.UTILS.Models;
using XH.LAB.UTILS.Models.Entites;

namespace XH.H81.Services
{
    public class TableStructDetectAndInitDataService : IHostedService, IDisposable
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<BaseDataServices> _logger;
        private readonly IHttpContextAccessor _httpContext;
        private readonly ISqlSugarUow<SugarDbContext_Master> _soa;
        private readonly IBaseDataServices _IBaseDataServices;
        public TableStructDetectAndInitDataService(IConfiguration configuration, ILogger<BaseDataServices> logger, IHttpContextAccessor httpContext
           , ISqlSugarUow<SugarDbContext_Master> suow, IBaseDataServices iBaseDataServices)
        {
            _configuration = configuration;
            _logger = logger;
            _httpContext = httpContext;
            _soa = suow;
            _IBaseDataServices = iBaseDataServices;
        }


        public Task StartAsync(CancellationToken cancellationToken)
        {
            lock (EntityHelper.LockObject)
            {
                ////创建数据文件，文件生成后请注释掉
                CreateInitData();

                //表结构检测
                EntityHelper.DetectTableStruct(_configuration, _soa);

                //插入初始化数据
                EntityHelper.ExecuteInitData(_soa, _configuration);

                //自动匹配旧数据管理单元
                AutoMatchModuleOldPostUnit();
            }
            return Task.CompletedTask;
        }

        /// <summary>
        /// 生成初始化数据文件
        /// </summary>
        private void CreateInitData()
        {
            bool predict = false;
            if (predict)
            {
                CreateInitData_Base();
                CreateInitData_6_25_225();
                CreateInitData_6_25_300();
                CreateInitData_6_25_329();
            }
        }

        private void CreateInitData_Base()
        {
            #region 初始化数据模板
            // 数据行列表
            List<InitDataLine> dataLines = new List<InitDataLine>();
            // 创建数据对象 -- 人员管理菜单
            var data = _soa.Db.Queryable<SYS6_MENU>().Where(a => a.MENU_ID.Contains("H81") && a.MENU_STATE == "1").ToList();
            dataLines.AddRange(data.Select(item => new InitDataLine
            {
                DataObject = item,
                Remark = $"人员管理菜单：{item.MENU_NAME}" //可自由备注，重在方便辨别数据内容
            }).ToList());
            //更新人事档案类型属性
            var update1 = _soa.Db.Queryable<PMS_ADDN_CLASS_INFO>().Where(a => a.CLASS_ID == "PMS_LEARNING_LIST" || a.CLASS_ID == "PMS_ASSESS_LIST" || a.CLASS_ID == "PMS_EXAM_LIST" || a.CLASS_ID == "PMS_MEETING_LIST" || a.CLASS_ID == "PMS_POST_LIST").ToList();
            dataLines.AddRange(update1.Select(item => new InitDataLine
            {
                DataObject = item,
                Remark = $"更新人事档案类型属性",
                PerformType = PerformType.UPDATE,
                UpdateColumns = new[] { "CLASS_TYPE", "IS_PROP_EDITABLE", "IS_AUDITABLE" } //【注意必须填对】
            }).ToList());
            //创建数据文件
            EntityHelper.CreateInitDataFile("人员管理6.25.217初始化数据", "人员管理6.25.217初始化数据", dataLines);

            #endregion
        }

        private void CreateInitData_6_25_225()
        {
            #region 人员初始化数据6.25.225
            // 数据行列表
            List<InitDataLine> dataLines = new List<InitDataLine>();

            // 创建数据对象 -- 人员管理菜单
            var data = _soa.Db.Queryable<SYS6_MENU>().Where(a => a.MENU_ID.Contains("H81") && a.MENU_STATE == "1").ToList();
            dataLines.AddRange(data.Select(item => new InitDataLine
            {
                DataObject = item,
                Remark = $"人员管理菜单：{item.MENU_NAME}" //可自由备注，重在方便辨别数据内容
            }).ToList());

            // 创建数据对象 -- 系统固定数据
            var bdClassIds = new string[] {
                "性别","最高学历","最高学位","民族","政治面貌","职务","用工类型","证件类型","入职方式","职称","婚姻状况","有无子女","健康状况","人员状态","职称级别","职称类型","英语级别","奖惩级别","教学分类","教学级别","课题分类","课题级别"
                ,"论著类型","论著级别","论著作者排序","JCR分区","是否SCI记录","学位类型","学历性质","学习形式","学分类型","证书级别","任职机构等级","任职职务","当前是否在职","知识产权分类","交流级别","外派性质","进修分类","人事技能证书类型"
                ,"考试评价结果","人员评估评价结果","岗位类型","岗位分类","岗位类别","现从事岗位","人员培训分类", "课题类型",    "是否完成", "人员状态", "职称名称",  "教学分类", "教学级别", "进修分类", "课题分类", "课题类型", "参与类型", "入职方式", "课题级别"};
            var sysData = _soa.Db.Queryable<SYS6_BASE_DATA>().Where(a => bdClassIds.Contains(a.CLASS_ID) && a.DATA_STATE == "1").ToList();
            dataLines.AddRange(sysData.Select(item => new InitDataLine
            {
                DataObject = item,
                Remark = $"系统固定数据-{item.CLASS_ID}：{item.DATA_CNAME}" //可自由备注，重在方便辨别数据内容
            }).ToList());

            // 创建数据对象 -- OA基础数据-评估阶段频次单位
            var data2 = _soa.Db.Queryable<OA_BASE_DATA>().Where(a => a.CLASS_ID == "评估阶段频次单位" && a.STATE_FLAG == "1").ToList();
            dataLines.AddRange(data2.Select(item => new InitDataLine
            {
                DataObject = item,
                Remark = $"OA基础数据-评估阶段频次单位：{item.DATA_NAME}"
            }).ToList());

            // 创建数据对象 -- OA基础数据
            var data3 = _soa.Db.Queryable<OA_BASE_DATA>().Where(a => a.DATA_ID.Contains("XBD00")).ToList();
            dataLines.AddRange(data3.Select(item => new InitDataLine
            {
                DataObject = item,
                Remark = $"OA基础数据-{item.CLASS_ID}：{item.DATA_NAME}"
            }).ToList());

            // 创建数据对象 -- 人事已实现的属性输入方式的基础数据
            var data4 = _soa.Db.Queryable<OA_BASE_DATA>().Where(a => a.CLASS_ID == "人事已实现的属性输入方式" && a.STATE_FLAG == "1").ToList();
            dataLines.AddRange(data4.Select(item => new InitDataLine
            {
                DataObject = item,
                Remark = $"OA基础数据-人事已实现的属性输入方式：{item.DATA_NAME}"
            }).ToList());

            // 创建数据对象 -- 增加规培生相关的基础数据
            var data5 = _soa.Db.Queryable<OA_BASE_DATA>().Where(a => a.CLASS_ID == "人员执业分类" && a.STATE_FLAG == "1").ToList();
            dataLines.AddRange(data5.Select(item => new InitDataLine
            {
                DataObject = item,
                Remark = $"OA基础数据-人员执业分类：{item.DATA_NAME}"
            }).ToList());

            // 创建数据对象 -- 增加规培生相关的基础数据
            var data6 = _soa.Db.Queryable<OA_BASE_DATA>().Where(a => a.CLASS_ID == "执业级别" && a.STATE_FLAG == "1").ToList();
            dataLines.AddRange(data6.Select(item => new InitDataLine
            {
                DataObject = item,
                Remark = $"OA基础数据-执业级别：{item.DATA_NAME}"
            }).ToList());

            // 创建数据对象 -- OA基础数据-人员培训分类
            var data8 = _soa.Db.Queryable<OA_BASE_DATA>().Where(a => a.CLASS_ID == "人员培训分类" && a.STATE_FLAG == "1").ToList();
            dataLines.AddRange(data8.Select(item => new InitDataLine
            {
                DataObject = item,
                Remark = $"OA基础数据-人员培训分类：{item.DATA_NAME}"
            }).ToList());

            // 创建数据对象 -- OA基础数据-系统入口类型
            var data9 = _soa.Db.Queryable<OA_BASE_DATA>().Where(a => a.CLASS_ID == "系统入口类型" && a.STATE_FLAG == "1").ToList();
            dataLines.AddRange(data9.Select(item => new InitDataLine
            {
                DataObject = item,
                Remark = $"OA基础数据-系统入口类型：{item.DATA_NAME}"
            }).ToList());

            // 创建数据对象 -- OA基础数据-系统入口类型
            var data10 = _soa.Db.Queryable<OA_BASE_DATA>().Where(a => a.CLASS_ID == "人员标签" && a.STATE_FLAG == "1").ToList();
            dataLines.AddRange(data10.Select(item => new InitDataLine
            {
                DataObject = item,
                Remark = $"OA基础数据-人员标签：{item.DATA_NAME}"
            }).ToList());

            // 创建数据对象 -- 增加颜色障碍数据
            var data11 = _soa.Db.Queryable<OA_BASE_DATA>().Where(a => a.CLASS_ID == "H810000-COLOR_DEFICIENCY" && a.STATE_FLAG == "1").ToList();
            dataLines.AddRange(data11.Select(item => new InitDataLine
            {
                DataObject = item,
                Remark = $"OA基础数据-颜色障碍数据：{item.DATA_NAME}"
            }).ToList());

            // 创建数据对象 -- 固定数据分类增加“人员培训分类”、现从事岗位、岗位类别
            var data7 = _soa.Db.Queryable<SYS6_BASE_DATA_CLASS>().Where(a => bdClassIds.Contains(a.CLASS_ID) || a.CLASS_ID == "人员培训分类" || a.CLASS_ID == "现从事岗位" || a.CLASS_ID == "岗位类别").ToList();
            dataLines.AddRange(data7.Select(item => new InitDataLine
            {
                DataObject = item,
                Remark = $"固定数据分类-{item.CLASS_NAME}"
            }).ToList());

            // 创建数据对象 -- 复杂表单模板数据
            var data12 = _soa.Db.Queryable<SYS6_MODULE_FUNC_DICT>().Where(a => a.SETUP_ID == "H81_TG000001X" || a.SETUP_ID == "H81_TG000003X").ToList();
            dataLines.AddRange(data12.Select(item => new InitDataLine
            {
                DataObject = item,
                Remark = $"复杂表单模板数据：{item.SETUP_NAME}"
            }).ToList());

            // 创建数据对象 -- 初始化标签数据
            var data14 = _soa.Db.Queryable<PMS_PERSON_TAG_DICT>().Where(a => a.PERSON_TAG_ID == "TG000001X" || a.PERSON_TAG_ID == "TG000002X" || a.PERSON_TAG_ID == "TG000003X" || a.PERSON_TAG_ID == "TG000004X").ToList();
            dataLines.AddRange(data14.Select(item => new InitDataLine
            {
                DataObject = item,
                Remark = $"初始化标签数据：{item.TAG_NAME}"
            }).ToList());

            // 创建数据对象 -- 清单字段
            //var data16 = _soa.Db.Queryable<OA_FIELD_DICT>().Where(a => a.FIELD_STATE == "1" && a.MODULE_ID == "H81").ToList();
            //dataLines.AddRange(data16.Select(item => new InitDataLine
            //{
            //    DataObject = item,
            //    Remark = $"初始化清单字段：{item.FIELD_NAME}"
            //}).ToList());


            // 创建数据对象 -- 评估、考试记录的工具箱
            var data17 = _soa.Db.Queryable<SYS6_MODULE_FUNC_DICT>().Where(a => a.SETUP_ID == "H810154" || a.SETUP_ID == "H810154CT" || a.SETUP_ID == "H810153" || a.SETUP_ID == "H810153CT"
                            || a.SETUP_ID == "H810164" || a.SETUP_ID == "H810164CT" || a.SETUP_ID == "H810165" || a.SETUP_ID == "H810165CT" || a.SETUP_ID == "H810166" || a.SETUP_ID == "H810166CT").ToList();
            dataLines.AddRange(data17.Select(item => new InitDataLine
            {
                DataObject = item,
                Remark = $"初始化工具箱配置：{item.SETUP_NAME}"
            }).ToList());


            //人事档案类型
            var recordClass = new string[] { "PMS_MEETING_LIST", "PMS_RESUME_LIST", "PMS_PROFESSIONAL_LIST", "PMS_REWARD_LIST", "PMS_TEACH_LIST", "PMS_STUDY_LIST", "PMS_RESEARCH_LIST", "PMS_THESIS_LIST"
                , "PMS_EDUCATION_LIST", "PMS_SKILL_CERTIFICATE_LIST", "PMS_TRAIN_LIST", "PMS_SOCIAL_OFFICE_LIST", "PMS_INTELLECTUAL_LIST", "PMS_EXCHANGE_LIST", "PMS_EXPATRIATE_LIST", "PMS_ASSESS_LIST"
                , "PMS_EXAM_LIST", "PMS_HEALTH_DOC_LIST", "PMS_RISK_LIST", "PMS_LEARNING_LIST", "PMS_BODYCHECK_LIST", "PMS_POST_LIST"};
            var data15 = _soa.Db.Queryable<PMS_ADDN_CLASS_INFO>().Where(a => recordClass.Contains(a.CLASS_ID)).ToList();
            dataLines.AddRange(data15.Select(item => new InitDataLine
            {
                DataObject = item,
                Remark = $"人事档案类型：{item.CLASS_NAME}",
            }).ToList());

            //人员标签与人事档案类型关系
            var data14_1 = _soa.Db.Queryable<PMS_PERSON_TAG_RELATE>().Where(a => recordClass.Contains(a.DATA_ID) && (a.PERSON_TAG_ID == "TG000001X" || a.PERSON_TAG_ID == "TG000002X" || a.PERSON_TAG_ID == "TG000003X" || a.PERSON_TAG_ID == "TG000004X")).ToList();
            dataLines.AddRange(data14_1.Select(item => new InitDataLine
            {
                DataObject = item,
                Remark = $"初始化人员标签与人事档案类型关系"
            }).ToList());


            //更新人事档案类型属性
            var update1 = _soa.Db.Queryable<PMS_ADDN_CLASS_INFO>().Where(a => a.CLASS_ID == "PMS_LEARNING_LIST" || a.CLASS_ID == "PMS_ASSESS_LIST" || a.CLASS_ID == "PMS_EXAM_LIST" || a.CLASS_ID == "PMS_MEETING_LIST" || a.CLASS_ID == "PMS_POST_LIST").ToList();
            dataLines.AddRange(update1.Select(item => new InitDataLine
            {
                DataObject = item,
                Remark = $"更新人事档案类型属性",
                PerformType = PerformType.UPDATE,
                UpdateColumns = new[] { "CLASS_TYPE", "IS_PROP_EDITABLE", "IS_AUDITABLE" } //【注意必须填对】
            }).ToList());


            // 创建数据对象 -- 更新人事已实现的属性输入方式的基础数据
            var update3 = _soa.Db.Queryable<OA_BASE_DATA>().Where(a => a.CLASS_ID == "人事已实现的属性输入方式" && a.STATE_FLAG == "1").ToList();
            dataLines.AddRange(update3.Select(item => new InitDataLine
            {
                DataObject = item,
                Remark = $"OA基础数据-人事已实现的属性输入方式：{item.DATA_NAME}",
                PerformType = PerformType.UPDATE,
                UpdateColumns = new[] { "DATA_NAME" }
            }).ToList());


            // 创建数据对象 -- 更新职称对应的级别
            var update4 = _soa.Db.Queryable<SYS6_BASE_DATA>().Where(a => (a.CLASS_ID == "技师职称" || a.CLASS_ID == "医师职称" || a.CLASS_ID == "护士职称" || a.CLASS_ID == "研究员职称") && a.DATA_STATE == "1").ToList();
            dataLines.AddRange(update4.Select(item => new InitDataLine
            {
                DataObject = item,
                Remark = $"更新职称对应的级别：{item.DATA_CNAME}"
            }).ToList());

            //创建数据文件
            EntityHelper.CreateInitDataFile("人员管理6.25.225初始化数据", "人员管理6.25.225初始化数据", dataLines);

            #endregion
        }

        private void CreateInitData_6_25_300()
        {
            #region 人员导入数据6.25.300

            // 数据行列表
            List<InitDataLine> dataLines = new List<InitDataLine>();
            // 创建数据对象 -- 基础数据分类
            var data = _soa.Db.Queryable<SYS6_BASE_DATA_CLASS>().Where(a => a.ONE_CLASS == "实验室管理相关" && a.CLASS_STATE == "1").ToList();
            dataLines.AddRange(data.Select(item => new InitDataLine
            {
                DataObject = item,
                Remark = $"基础数据分类：{item.CLASS_NAME}" //可自由备注，重在方便辨别数据内容
            }).ToList());

            // 创建数据对象 -- 共公字段数据
            var data13 = _soa.Db.Queryable<SYS6_FUNC_FIELD_DICT>().Where(a => a.MODULE_ID == "H81" && a.FIELD_CLASS == "基本信息" && a.FIELD_STATE == "1").ToList();
            dataLines.AddRange(data13.Select(item => new InitDataLine
            {
                DataObject = item,
                Remark = $"共公字段数据：{item.FIELD_NAME}"
            }).ToList());

            //更新菜单排序
            var update2 = _soa.Db.Queryable<SYS6_MENU>().Where(a => a.MENU_ID.Contains("H81") && a.MENU_STATE == "1").ToList();
            dataLines.AddRange(update2.Select(item => new InitDataLine
            {
                DataObject = item,
                Remark = $"更新菜单排序：{item.MENU_NAME}",
                PerformType = PerformType.UPDATE,
                UpdateColumns = new[] { "MENU_SORT", "MENU_NAME", "MENU_ICON", "MENU_URL" }
            }).ToList());
            //创建数据文件
            EntityHelper.CreateInitDataFile("人员管理6.25.300导入数据", "人员管理6.25.300导入数据", dataLines);

            #endregion
        }

        private void CreateInitData_6_25_329()
        {
            #region 人员导入数据6.25.329

            // 数据行列表
            List<InitDataLine> dataLines = new List<InitDataLine>();
            // 创建数据对象 -- 基础数据分类(人员评估分类\人员评估分类)
            var data = _soa.Db.Queryable<SYS6_BASE_DATA_CLASS>().Where(a => a.ONE_CLASS == "实验室管理相关" && a.CLASS_STATE == "1" && (a.CLASS_ID == "人员评估分类" || (a.CLASS_ID == "人员考试分类"))).ToList();
            dataLines.AddRange(data.Select(item => new InitDataLine
            {
                DataObject = item,
                Remark = $"基础数据分类：{item.CLASS_NAME}" //可自由备注，重在方便辨别数据内容
            }).ToList());

            // 创建数据对象 -- 更新颜色视觉障碍的基础数据
            var update1 = _soa.Db.Queryable<OA_BASE_DATA>().Where(a => a.CLASS_ID == "H810000-COLOR_DEFICIENCY" && a.STATE_FLAG == "1").ToList();
            dataLines.AddRange(update1.Select(item => new InitDataLine
            {
                DataObject = item,
                Remark = $"OA基础数据-颜色视觉障碍：{item.DATA_NAME}",
                PerformType = PerformType.UPDATE,
                UpdateColumns = new[] { "DATA_NAME" }
            }).ToList());

            // 创建数据对象 -- 更新ISO基础信息模板
            var update2 = _soa.Db.Queryable<SYS6_MODULE_FUNC_DICT>().Where(a => a.SETUP_ID == "H81_TG000001X").ToList();
            dataLines.AddRange(update2.Select(item => new InitDataLine
            {
                DataObject = item,
                Remark = $"复杂表单模板数据：{item.SETUP_NAME}",
                PerformType = PerformType.UPDATE,
                UpdateColumns = new[] { "FORM_JSON", "FORM_COL_JSON" }
            }).ToList());

            // 创建数据对象 -- 更新公共字段库
            var update3 = _soa.Db.Queryable<SYS6_FUNC_FIELD_DICT>().Where(a => a.MODULE_ID == "H81" && a.FIELD_CLASS == "基本信息").ToList();
            dataLines.AddRange(update3.Select(item => new InitDataLine
            {
                DataObject = item,
                Remark = $"公共字段库：{item.FIELD_NAME}",
                PerformType = PerformType.UPDATE,
                UpdateColumns = new[] { "STYLE_JSON", "ADDN_JSON" }
            }).ToList());

            // 创建数据对象 -- 更新人事已实现的属性输入方式的基础数据
            var update4 = _soa.Db.Queryable<OA_BASE_DATA>().Where(a => a.CLASS_ID == "人事已实现的属性输入方式" && a.STATE_FLAG == "1").ToList();
            dataLines.AddRange(update4.Select(item => new InitDataLine
            {
                DataObject = item,
                Remark = $"OA基础数据-人事已实现的属性输入方式：{item.DATA_NAME}",
                PerformType = PerformType.UPDATE,
                UpdateColumns = new[] { "DATA_NAME" }
            }).ToList());

            //创建数据文件
            EntityHelper.CreateInitDataFile("人员管理6.25.329导入数据", "人员管理6.25.329导入数据", dataLines);

            #endregion
        }


        private void CreateInitData_POCT()
        {
            #region 人员导入数据——POCT大版本

            // 数据行列表
            List<InitDataLine> dataLines = new List<InitDataLine>();

            // 创建数据对象 -- 基础数据:采血外援
            var sysData = _soa.Db.Queryable<OA_BASE_DATA>().Where(a => a.DATA_ID == "XBD00000029" && a.STATE_FLAG == "1").ToList();
            dataLines.AddRange(sysData.Select(item => new InitDataLine
            {
                DataObject = item,
                Remark = $"基础数据-{item.CLASS_ID}：{item.DATA_NAME}" //可自由备注，重在方便辨别数据内容
            }).ToList());

            //人事档案类型 -- 规评岗位记录
            var recordClass = new string[] { "PMS_EPLAN_POST_LIST" };
            var data1 = _soa.Db.Queryable<PMS_ADDN_CLASS_INFO>().Where(a => recordClass.Contains(a.CLASS_ID)).ToList();
            dataLines.AddRange(data1.Select(item => new InitDataLine
            {
                DataObject = item,
                Remark = $"人事档案类型：{item.CLASS_NAME}",
            }).ToList());

            // 创建数据对象 -- 工具箱规评岗位记录
            var data2 = _soa.Db.Queryable<SYS6_MODULE_FUNC_DICT>().Where(a => a.SETUP_ID == "H810140802" || a.SETUP_ID == "H810140802CT").ToList();
            dataLines.AddRange(data2.Select(item => new InitDataLine
            {
                DataObject = item,
                Remark = $"工具箱规评岗位记录：{item.SETUP_NAME}"
            }).ToList());

            //创建数据文件
            EntityHelper.CreateInitDataFile("人员管理_POCT版本导入数据", "人员管理_POCT版本导入数据", dataLines);

            #endregion
        }


        private void AutoMatchModuleOldPostUnit()
        {
            string configName = "AutoMatchModuleOldPostUnit";
            List<string> postModuleIds = null;
            try
            {
                postModuleIds = _configuration[configName]?.Split(',').Select(a => a.Trim().ToUpper()).ToList();
            }
            catch { }
            //没有填入AutoMatchModuleOldPostUnit参数，则不执行
            if (postModuleIds == null || !postModuleIds.Any())
                return;

            Log.Information("==>[岗位旧数据自动匹配管理单元]正在进行中，过程可能持续几分钟时间，请稍等...");

            var oldPostInfo = _soa.Db.Queryable<Models.Entities.SYS6_POST>()
                .InnerJoin<SYS6_POST_ROLE_COM>((post, poCom) => post.POST_ID == poCom.POST_ID && post.POST_STATE == "1" && post.POST_ULEVEL == null)
                .InnerJoin<SYS6_ROLE_COM_LIST>((post, poCom, comList) => poCom.ROLECOM_ID == comList.ROLECOM_ID && comList.ROLE_STATE == "1")
                .Where((post, poCom, comList) => postModuleIds.Contains(comList.MODULE_ID))
                .Select((post, poCom, comList) => new { post.POST_ID, post.HOSPITAL_ID, post.LAB_ID, post.PGROUP_ID, comList.MODULE_ID })
                .ToList();

            var hospitals = _soa.Db.Queryable<SYS6_HOSPITAL_INFO>()
                .Where(a => oldPostInfo.Select(a => a.HOSPITAL_ID).Distinct().ToList().Contains(a.HOSPITAL_ID) && a.HOSPITAL_STATE == "1")
                .Select(a => new { a.HOSPITAL_ID, a.HOSPITAL_CNAME })
                .ToList();

            var labs = _soa.Db.Queryable<SYS6_INSPECTION_LAB>()
                .Where(a => oldPostInfo.Select(a => a.LAB_ID).Distinct().ToList().Contains(a.LAB_ID) && a.STATE_FLAG == "1")
                .Select(a => new { a.LAB_ID, a.LAB_NAME })
                .ToList();

            var mgroups = _soa.Db.Queryable<SYS6_INSPECTION_MGROUP>()
                .Where(a => oldPostInfo.Select(a => a.LAB_ID).Distinct().ToList().Contains(a.LAB_ID) && a.MGROUP_STATE == "1")
                .Select(a => new { a.LAB_ID, a.MGROUP_ID, a.MGROUP_NAME })
                .ToList();

            var pgroups = _soa.Db.Queryable<SYS6_INSPECTION_PGROUP>()
                .Where(a => oldPostInfo.Select(a => a.LAB_ID).Distinct().ToList().Contains(a.LAB_ID) && a.PGROUP_STATE == "1")
                .Select(a => new { a.LAB_ID, a.PGROUP_ID, a.PGROUP_NAME })
                .ToList();

            var smblLabs = _soa.Db.Queryable<SMBL_LAB>()
                .Where(a => oldPostInfo.Select(a => a.LAB_ID).Distinct().ToList().Contains(a.LAB_ID) && a.SMBL_LAB_STATE == "1")
                .Select(a => new { a.LAB_ID, a.SMBL_LAB_ID, a.PGROUP_SID, a.SMBL_LAB_CNAME })
            .ToList();

            var upostIndexList = _soa.Db.Queryable<SYS6_POST_UNIT>().Where(a => a.UPOST_ID.StartsWith("x")).Select(a => a.UPOST_ID).ToList();
            int upostIndex = !upostIndexList.Any() ? 0 : upostIndexList.Max(a => a == null ? 0 : int.Parse(a.Trim('x')));
            List <SYS6_POST_UNIT> postUnitList = new List<SYS6_POST_UNIT>();
            foreach (var postGroup in oldPostInfo.GroupBy(a => new { a.POST_ID, a.MODULE_ID }))
            {
                ////机构
                //var hospital = hospitals.Find(a => a.HOSPITAL_ID == postGroup.First().HOSPITAL_ID);
                //var hospitalUnit = CreatePostUnit(postGroup.First().POST_ID,"1",hospital.HOSPITAL_ID,hospital.HOSPITAL_CNAME, postGroup.First().MODULE_ID);
                //postUnitList.Add(hospitalUnit);

                //科室
                var thisLabs = labs.FindAll(a => postGroup.Any(g => g.LAB_ID == a.LAB_ID && g.PGROUP_ID == "PG000"));
                var labUnits = thisLabs.Select(a => CreatePostUnit(postGroup.First().POST_ID, "11", a.LAB_ID, a.LAB_NAME, postGroup.First().MODULE_ID)).ToList();
                postUnitList.AddRange(labUnits);

                //管理专业组
                var thisMgroups = mgroups.FindAll(a => postGroup.Any(g => g.LAB_ID == a.LAB_ID && g.PGROUP_ID == "PG000"));
                var mgroupUnits = thisMgroups.Select(a => CreatePostUnit(postGroup.First().POST_ID, "19", a.MGROUP_ID, a.MGROUP_NAME, postGroup.First().MODULE_ID)).ToList();
                postUnitList.AddRange(mgroupUnits);

                //检验专业组
                var thisPgroups = pgroups.FindAll(a => postGroup.Any(g => g.LAB_ID == a.LAB_ID && g.PGROUP_ID == "PG000") || postGroup.Any(g => g.PGROUP_ID == a.PGROUP_ID));
                var pgroupUnits = thisPgroups.Select(a => CreatePostUnit(postGroup.First().POST_ID, "2", a.PGROUP_ID, a.PGROUP_NAME, postGroup.First().MODULE_ID)).ToList();
                postUnitList.AddRange(pgroupUnits);

                //备案实验室
                var thisSmblLabs = smblLabs.FindAll(a => postGroup.Any(g => g.LAB_ID == a.LAB_ID && g.PGROUP_ID == "PG000") || postGroup.Any(g => (a.PGROUP_SID + ",").Contains(g.PGROUP_ID)));
                var smblLabUnits = thisSmblLabs.Select(a => CreatePostUnit(postGroup.First().POST_ID, "28", a.SMBL_LAB_ID, a.SMBL_LAB_CNAME, postGroup.First().MODULE_ID)).ToList();
                postUnitList.AddRange(smblLabUnits);

            }
            var existPostUnits = _soa.Db.Queryable<SYS6_POST_UNIT>().Where(a => oldPostInfo.Select(o => o.POST_ID).Distinct().ToList().Contains(a.POST_ID)).ToList();
            List<SYS6_POST_UNIT> postUnitInsertList = new List<SYS6_POST_UNIT>();
            foreach (var unit in postUnitList)
            {
                if (!existPostUnits.Any(e => e.POST_ID == unit.POST_ID && e.UNIT_ID == unit.UNIT_ID && e.MODULE_ID == unit.MODULE_ID && e.UNIT_CLASS == unit.UNIT_CLASS))
                    postUnitInsertList.Add(unit);
            }

            _soa.Db.Insertable(postUnitInsertList).ExecuteCommand();
            EntityHelper.DisableAppSettingConfigItem(configName);

            Log.Information("==>[岗位旧数据自动匹配管理单元]已完成。");

            SYS6_POST_UNIT CreatePostUnit(string postId, string unitClass, string unitId, string unitName, string moduleId)
            {
                var pUnit = new SYS6_POST_UNIT();
                pUnit.UPOST_ID = $"x{(++upostIndex).ToString().PadLeft(5, '0')}";
                pUnit.POST_ID = postId;
                pUnit.MODULE_ID = moduleId;
                pUnit.UNIT_CLASS = unitClass;  
                pUnit.UNIT_ID = unitId;
                pUnit.UNIT_NAME = unitName;
                pUnit.FIRST_RPERSON = "实验室初始化";
                pUnit.FIRST_RTIME = DateTime.Now;
                pUnit.LAST_MPERSON = "实验室初始化";
                pUnit.LAST_MTIME = DateTime.Now;
                return pUnit;
            }
        }
        private void test()
        {
            //插入数据（自动替换HOSPITAL_ID，每条数据独立执行，失败不互相影响）【按需启用】
            //EntityHelper.InsertInitiateData(_soa, List_01121EB66D824E05A857ADEC73575265);

            ////全字段更新数据（自动替换HOSPITAL_ID，每条数据独立执行，失败不互相影响）【按需启用】【严禁更新业务数据及字段！】
            //EntityHelper.UpdateInitiateData(_soa, List_01121EB66D824E05A857ADEC73575265);

            ////指定字段更新数据（自动替换HOSPITAL_ID，每条数据独立执行，失败不互相影响）【按需启用】【严禁更新业务数据及字段！】
            //EntityHelper.UpdateInitiateData(_soa, List_01121EB66D824E05A857ADEC73575265, a => new { a.NAME, a.STATE });
        }
        public Task StopAsync(CancellationToken cancellationToken)
        {
            return Task.CompletedTask;
        }
        public void Dispose()
        {

        }
    }
}
