﻿using Newtonsoft.Json;

namespace XH.H81.Models.Dtos.IoTDevices;

public  class IoTDevicesDto
{
    /// <summary>
    /// id
    /// </summary>
    [JsonProperty("id", NullValueHandling = NullValueHandling.Ignore)]
    public string? Id { get; set; }
        
    /// <summary>
    /// 设备sn
    /// </summary>
    [JsonProperty("sn", NullValueHandling = NullValueHandling.Ignore)]
    public string? Sn { get; set; }
        
    /// <summary>
    /// 名称
    /// </summary>
    [JsonProperty("name",NullValueHandling = NullValueHandling.Ignore)]
    public string? Name { get; set; }
        
    /// <summary>
    /// 设备型号
    /// </summary>
    [JsonProperty("model", NullValueHandling = NullValueHandling.Ignore)]
    public string Model { get; set; }
        
    /// <summary>
    /// 701智能开关，702生物安全柜，703紫外灯，704高压灭菌柜
    /// </summary>
    [JsonProperty("type",NullValueHandling = NullValueHandling.Ignore)]
    public double Type { get; set; }
        
    /// <summary>
    /// ip
    /// </summary>
    [JsonProperty("ip", NullValueHandling = NullValueHandling.Ignore)]
    public string? Ip { get; set; }


    /// <summary>
    /// 通电状态：0未通电 1通电
    /// </summary>
    [JsonProperty("openStatus", NullValueHandling = NullValueHandling.Ignore)]
    public int OpenStatus { get; set; } = 0;
        
    /// <summary>
    /// 房间id
    /// </summary>
    [JsonProperty("roomId", NullValueHandling = NullValueHandling.Ignore)]
    public long? RoomId { get; set; }
        
        
    /// <summary>
    /// 房间名称
    /// </summary>
    [JsonProperty("roomName", NullValueHandling = NullValueHandling.Ignore)]
    public string? RoomName { get; set; }
        
    /// <summary>
    /// 测点id
    /// </summary>
    [JsonProperty("checkpointId")]
    public string? CheckpointId { get; set; }

    /// <summary>
    /// 测点名称
    /// </summary>
    [JsonProperty("checkpointName")]
    public string? CheckpointName { get; set; }

    /// <summary>
    /// 实验室id
    /// </summary>
    [JsonProperty("labId",NullValueHandling = NullValueHandling.Ignore)]
    public long? LabId { get; set; }

    /// <summary>
    /// 实验室名
    /// </summary>
    [JsonProperty("labName")]
    public string LabName { get; set; }

        
    /// <summary>
    /// 是否在线：0不在线1在线
    /// </summary>
    [JsonProperty("isOnline",NullValueHandling = NullValueHandling.Ignore)]
    public int IsOnline { get; set; }

    /// <summary>
    /// 设备开关状态：0关机，1待机 ,2运行中, 3过载   201消毒 
    /// </summary>
    [JsonProperty("switchStatus",NullValueHandling = NullValueHandling.Ignore)]
    public int SwitchStatus { get; set; }
        
    /// <summary>
    /// 备注
    /// </summary>
    [JsonProperty("remark")]
    public string? Remark { get; set; }
        
    /// <summary>
    /// 创建时间
    /// </summary>
    [JsonProperty("createTime")]
    public string? CreateTime { get; set; }
        
    /// <summary>
    /// 当前电压，单位V，保留三位小数
    /// </summary>
    [JsonProperty("voltage",NullValueHandling = NullValueHandling.Ignore)]
    public double Voltage { get; set; }
        
        
    /// <summary>
    /// 当前电流，单位A，保留三位小数
    /// </summary>
    [JsonProperty("current",NullValueHandling = NullValueHandling.Ignore)]
    public double Current { get; set; }

    /// <summary>
    /// 当前功率，单位W，保留三位小数
    /// </summary>
    [JsonProperty("power",NullValueHandling = NullValueHandling.Ignore)]
    public double Power { get; set; }
        
    /// <summary>
    /// 累计用电量，单位KW*H，保留三位小数，
    /// 断电后不会归零，重置后会归零
    /// </summary>
    [JsonProperty("energy",NullValueHandling = NullValueHandling.Ignore)]
    public double Energy { get; set; }


    /// <summary>
    /// 功率同步时间
    /// </summary>
    [JsonProperty("powerSyncTime",NullValueHandling = NullValueHandling.Ignore)]
    public string? PowerSyncTime { get; set; }
}