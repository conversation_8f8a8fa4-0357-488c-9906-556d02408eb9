﻿using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Processing;

namespace XH.H81.Base.Helper;

public class PicBase64Helper
{
    /// <summary>
    /// base64循环压缩
    /// </summary>
    /// <param name="base64String">图片的base64</param>
    /// <param name="maxFileSize">最大值 KB</param>
    /// <param name="ratio">原始比例</param>
    /// <param name="minRatio">每次缩小的比例 ratio *= minRatio</param>
    /// <param name="quality">图片保存的质量 90 </param>
    /// <returns></returns>
    public static string CompressBase64Image(string base64String, int maxFileSize, float ratio= 1.0f, float minRatio = 0.9f , int quality = 90)
    {
        // 去掉Base64字符串中的"data:image/png;base64,"前缀
        if (base64String.StartsWith("data:image"))
        {
            base64String = base64String.Substring(base64String.IndexOf(',') + 1);
        }

        byte[] imageBytes = Convert.FromBase64String(base64String);

        using (Image image = Image.Load(imageBytes))
        {
            // 如果文件大小小于等于200KB，直接返回原始Base64字符串
            if (imageBytes.Length <= maxFileSize*1024)
            {
                return base64String;
            }
            // 计算压缩比例
            for (int i = 1; i <= 10; i++)
            {
                ratio *= minRatio;
                // 创建一个内存流来保存压缩后的图片
                using (var ms = new MemoryStream())
                {
                    // 压缩图片
                    image.Mutate(x => x.Resize((int)(image.Width * ratio), (int)(image.Height * ratio)));
                    image.SaveAsJpeg(ms, new SixLabors.ImageSharp.Formats.Jpeg.JpegEncoder() { Quality = quality });
                    // 检查文件大小是否小于等于200KB
                    if (ms.Length <= maxFileSize*1024)
                    {
                        // 将压缩后的图片转换为Base64字符串
                        byte[] compressedBytes = ms.ToArray();
                        string compressedBase64 = Convert.ToBase64String(compressedBytes);
                        return compressedBase64;
                    }
                }
            }
        }
        throw new Exception("压缩失败,请重新上传图片");
    }
    
    /// <summary>
    /// 文件转base64 字符串
    /// </summary>
    /// <param name="imagePath"></param>
    /// <returns></returns>
    public static string CompressImageToBase64String( byte[] bytes)
    {
        // 将字节数据转换为Base64字符串
        string base64String = Convert.ToBase64String(bytes);
        return base64String;
    }
}