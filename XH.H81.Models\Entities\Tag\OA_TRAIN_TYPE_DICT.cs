﻿using System;
using System.Collections.Generic;
using H.Utility.SqlSugarInfra;
using SqlSugar;

/*
 * <AUTHOR>
 * @date : 2025-3-31
 * @desc : 培训类型字典表
 */
namespace XH.H81.Models.Entities.Tag
{
    /// <summary>
    /// 培训类型字典表
    /// </summary>
    [DBOwner("XH_OA")]
    public class OA_TRAIN_TYPE_DICT
    {
        /// <summary>
        /// 培训类型ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string TRAIN_TYPE_ID { get; set; }

        /// <summary>
        /// 医疗机构ID
        /// </summary>
        public string HOSPITAL_ID { get; set; }

        /// <summary>
        /// 培训类型字典分类;取值来源OA_BASE_DATA表CLASS_ID=‘培训分类’
        /// </summary>
        public string TRAIN_TYPE_CLASS { get; set; }

        /// <summary>
        /// 培训类型名称
        /// </summary>
        public string TRAIN_TYPE_NAME { get; set; }

        /// <summary>
        /// 规评标志
        /// </summary>
        public string EPLAN_FLAG { get; set; }

        /// <summary>
        /// 排序号
        /// </summary>
        public string TRAIN_TYPE_SORT { get; set; }

        /// <summary>
        /// 状态;0禁用 1在用（默认） 2删除
        /// </summary>
        public string TRAIN_TYPE_STATE { get; set; }

        /// <summary>
        /// 首次登记人
        /// </summary>
        public string FIRST_RPERSON { get; set; }

        /// <summary>
        /// 首次登记时间
        /// </summary>
        public DateTime FIRST_RTIME { get; set; }

        /// <summary>
        /// 最后修改人员
        /// </summary>
        public string LAST_MPERSON { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime LAST_MTIME { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string REMARK { get; set; }

    }
}