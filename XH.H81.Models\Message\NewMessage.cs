﻿using H.Utility;
using NPOI.OpenXmlFormats.Spreadsheet;
using NPOI.SS.Formula.PTG;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XH.H81.Base.Helper;

namespace XH.H81.Models.Message
{
    public class NewMessage : BaseMessage
    {
        public NewMessage()
        {
            MAG_DATE = DateTime.Now.ToString("yyyy-MM-dd");
            MSG_CLASS = "M13";
            //考试id
            MSG_CORRID = DateTime.UtcNow.Ticks.ToString();
            MSG_DISPOSE_TYPE = "1";
            MSG_DISPOSE_URL = "";
            MSG_DISPOSE_URL_STYLE = "";
            MSG_LEVEL = "1";
            MSG_OPERATE = "1";
            MSG_VALID_TIME = DateTime.Now.AddDays(2).ToString("yyyy-MM-dd HH:mm:ss");
            RECEIVE_UNIT_TYPE = "个人消息";
            SEND_TIME = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            SEND_COMPUTER = GetMacLoacltion.GetMacAddress();
        }

        public static NewMessage CreatNewMessage(string crrrId, string sendUserNo, string sendUserName, string hospitalId, string labId, string areaid, string modeuleId, string title, string content, string msgType, string receiveUserNo, string receiveName, long delay = 0, long overTime = 0)
        {

            var newMessage = new NewMessage();
            newMessage.MSG_CORRID = crrrId;
            newMessage.HOSPITAL_ID = hospitalId;
            newMessage.AREA_ID = areaid;
            newMessage.LAB_ID = labId;
            newMessage.MODULE_ID = modeuleId;
            newMessage.MSG_TITLE = title;
            newMessage.MSG_CONTENT = content;
            newMessage.MSG_TYPE = msgType;
            newMessage.RECEIVE_UNIT_ID = receiveUserNo;
            newMessage.RECEIVE_UNIT_NAME = receiveName;
            newMessage.SEND_UNIT_ID = sendUserNo;
            newMessage.SEND_PERSON = sendUserName;
            newMessage.DELAY_TIME = delay;
            newMessage.MSG_OVERTIME = overTime;
            return newMessage;
        }

    }
}
