﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Entities.Eguard
{
    /// <summary>
    /// 门禁授权设备/组合关系表
    /// </summary>
    [DBOwner("XH_OA")]
    public class PMS_EGUARD_AUTH_COM
    {
        /// <summary>
        /// 门禁授权设备ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string EGUARD_AUTH_CID { get; set; }
        /// <summary>
        /// 门禁授权ID
        /// </summary>
        public string EGUARD_AUTH_ID { get; set; }
        /// <summary>
        /// 门禁组合/门禁ID
        /// </summary>
        public string EGUARD_DATA_ID { get; set; }
        /// <summary>
        /// 门禁类型 1 按门禁组合 2 按门禁
        /// </summary>
        public string EGUARD_DATA_TYPE { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public string? EGUARD_AUTH_CSTATE { get; set; }
        public string? FIRST_RPERSON { get; set; }
        public DateTime? FIRST_RTIME { get; set; }
        public string? LAST_MPERSON { get; set; }
        public DateTime? LAST_MTIME { get; set; }
        public string? REMARK { get; set; }
    }
}
