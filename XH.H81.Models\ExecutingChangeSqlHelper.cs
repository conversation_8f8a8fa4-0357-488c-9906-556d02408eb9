﻿using H.BASE.SqlSugarInfra.Uow;
using H.BASE.SqlSugarInfra.Uow;
using Microsoft.Extensions.Configuration;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using XH.H81.Models.SugarDbContext;

namespace XH.H81.Models
{
    /// <summary>
    /// 表名转换类
    /// </summary>
    public static class ExecutingChangeSqlHelper
    {
        //.Select((answer) => new
        //         {
        //             answer,//正确写法 
        //             a = answer//错误写法 会导致a没值}
        //Join、select会产生别名，直接where、tolist则不会
        public static void ExecutingChangeSql(ISqlSugarUow<SugarDbContext_Master> suow, IConfiguration configuration, bool isNeed=false)
        {
            suow.Db.Aop.OnExecutingChangeSql = (sql, pars) =>
            {
                if (isNeed)
                {
                    try
                    {
                        if (sql != null && sql.Contains("SELECT"))
                        {
                            string sqlPar = sql.Replace(" \"XH_OA\".", " ");
                            sqlPar = sqlPar.Replace(" \"XH_SYS\".", " ");
                            Regex regex = new Regex(@"(?:FROM|JOIN)\s+(""[^""\n]+""\s*(?:""[^""\n]+""\s*)?)");
                            MatchCollection matchCollection = regex.Matches(sqlPar);
                            if (matchCollection.Count > 0)
                            {
                                foreach (System.Text.RegularExpressions.Match item in matchCollection)
                                {
                                    if (item.Groups.Count > 1)
                                    {
                                        string[] value = item.Groups[1].Value?.Split(" ");
                                        if (value != null && value.Length > 1)
                                        {
                                            //原表名
                                            string tableName = value[0].Replace("\"", "") + ".";
                                            //缩写
                                            string abbreviationName = value[1].Replace("\"", "") + ".";
                                            if (abbreviationName != ".")
                                            {
                                                sql = sql.Replace(abbreviationName + tableName, abbreviationName);
                                                sql = sql.Replace("\"" + tableName, "\"" + abbreviationName);
                                            }
                                            // sql = sql.Replace(tableName, abbreviationName);
                                        }
                                    }
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Serilog.Log.Error("表名转换OnExecutingChangeSql: " + ex);
                    }
                }
                if (configuration.GetValue<string>("CheckSqlFieldTooLong") == "1")
                    CheckIdentifierTooLong(sql);
                return new KeyValuePair<string, SugarParameter[]>(sql, pars);
            };
        }

        /// <summary>
        /// 对标识符长度限制在30字符的校验（避免在Oracle 11g的环境下报错）
        /// </summary>
        /// <param name="sql"></param>
        /// <exception cref="Exception"></exception>
        private static void CheckIdentifierTooLong(string sql)
        {
            bool identifierStart = false;
            int strength = 0;
            for (int i = 0; i < sql.Length; i++)
            {
                //定位到“ AS ”
                if (!identifierStart && sql[i] == 'A' && i > 0 && (i + 5) < sql.Length)
                {
                    if (sql[i - 1] == ' ' && sql[i + 1] == 'S' && sql[i + 2] == ' ' && (sql[i + 3] == '\"'))
                    {
                        identifierStart = true;
                        i = i + 3;
                        continue;
                    }
                }
                if (identifierStart && sql[i] != '\"')
                {
                    strength++;
                }
                else if (identifierStart && sql[i] == '\"')
                {
                    if (strength > 30)
                    {
                        throw new Exception($"标识符{sql.Substring(i - strength, strength)}的长度大于30个字符！");
                    }
                    identifierStart = false;
                    strength = 0;
                }
            }
        }
    }
}
