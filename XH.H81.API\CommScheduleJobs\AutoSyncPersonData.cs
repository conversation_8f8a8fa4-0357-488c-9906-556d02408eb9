﻿//using System;
//using System.Collections.Generic;
//using System.Diagnostics;
//using System.IO;
//using System.Linq;
//using System.Threading;
//using System.Threading.Tasks;
//using AutoMapper;
//using EasyCaching.Core;
//using H.BASE.SqlSugarInfra.Uow;
//using H.Utility;
//using H.Utility.Helper;
//using Microsoft.Extensions.Configuration;
//using Microsoft.Extensions.Hosting;
//using Serilog;
//using XH.H81.API.Controllers;
//using XH.H81.Base.Helper;
//using XH.H81.IServices;
//using XH.H81.Models;
//using XH.H81.Models.Dtos;
//using XH.H81.Models.Entities.Pms;
//using XH.H81.Models.ExternalEntity;
//using XH.H81.Models.SugarDbContext;
//using XH.LAB.UTILS.Models;

//namespace XH.H81.API.ScheduleJobs
//{
//    /// <summary>
//    /// 定时同步用户信息到档案人员表
//    /// </summary>
//    public class AutoSyncPersonData : IHostedService, IDisposable
//    {
//        private readonly IConfiguration _configuration;
//        private readonly ISqlSugarUow<SugarDbContext_Master> _soa;
//        private readonly IMapper _mapper;
//        private readonly IBaseDataServices _IBaseDataServices;
//        private Timer _timer;
//        static object _lockObject = new object();
//        private readonly IEasyCachingProviderFactory _cachingProvider;
//        const string lockName = "XH:LOCK:PMS_SyncPersonFromH07";
//        public AutoSyncPersonData(IConfiguration configuration, IBaseDataServices iBaseDataServices, ISqlSugarUow<SugarDbContext_Master> soa, IMapper mapper, IEasyCachingProviderFactory cachingProvider)
//        {
//            _configuration = configuration;
//            _IBaseDataServices = iBaseDataServices;
//            _soa = soa;
//            _mapper = mapper;
//            //_lockObject = _lockObject ?? new object();
//            _cachingProvider = cachingProvider;
//            ExecutingChangeSqlHelper.ExecutingChangeSql(_soa, configuration, true);
//            CoverSqlLog();
//        }
//        public void Dispose()
//        {
//            _timer?.Dispose();
//        }
//        //屏蔽过多的同步日志
//        private void CoverSqlLog()
//        {
//            //_soa.Db.Aop.OnDiffLogEvent = a => { };
//            //_soa.Db.Aop.OnLogExecuting = (a, b) => { };
//            _soa.Db.Aop.OnLogExecuted = (a, b) =>
//            {
//                Console.WriteLine(a);
//                Console.WriteLine(b);
//            };
//        }

//        public Task StartAsync(CancellationToken cancellationToken)
//        {
//            if (_configuration.GetValue<string>("IsOpenPersonSync") != "0")
//            {
//                if (_timer != null)
//                {
//                    _timer.Dispose();
//                    _timer = null;
//                }
//                if (_timer == null)
//                {
//                    Log.Information("==>[定时同步用户数据到人员档案表任务]已开启,35秒同步一次.");
//                    _timer = new Timer(DoWork, null, TimeSpan.Zero,
//                        TimeSpan.FromSeconds(35));
//                }
//            }
//            return Task.CompletedTask;
//        }

//        public Task StopAsync(CancellationToken cancellationToken)
//        {
//            if (_timer != null)
//            {
//                _timer.Dispose();
//                _timer = null;
//            }
//            return Task.CompletedTask;
//        }

//        private async void DoWork(object param)
//        {
//            try
//            {
//                //_soa?.Db?.Open();
//                if (GetTheLock())
//                {

//                    //DoSyncPerson();
//                }
//                else
//                {
//                    GetTheLockFalse();
//                }
//            }
//            catch (Exception e)
//            {
//                var redis = _cachingProvider.GetRedisProvider("S03");
//                var isDeleted = redis.KeyDel(lockName);
//                if (isDeleted)
//                {
//                    Console.WriteLine($"已有停止正在执行的定时任务，服务名：AutoSyncPersonData");
//                }
//                else
//                {
//                    var serviceName = redis.StringGet(lockName);
//                    var ttl = redis.TTL(lockName);
//                    Console.WriteLine($"已有停止正在执行的定时任务，服务名：AutoSyncPersonData,缓存键删除失败，剩余时长为:{ttl}秒");
//                }
//                Console.WriteLine($"AutoSyncPersonData同步人员数据失败：{e.Message}");
//            }
//        }

//        private void DoSyncPerson()
//        {
//            lock (_lockObject)
//            {
//                Stopwatch stopwatch = Stopwatch.StartNew();
//                try
//                {
//                    ////改为手动关闭数据库连接
//                    //_soa.Db.CurrentConnectionConfig.IsAutoCloseConnection = false;

//                    //新增人员数据
//                    AddPersonInfo();

//                    //更新人员数据

//                    //有效的 核心字段改变的
//                    var changeMainFieldData = _soa.Db.Queryable<SYS6_USER>().InnerJoin<PMS_PERSON_INFO>
//                        ((user, person) => user.USER_NO == person.USER_ID)
//                          .Where((user, person) => user.HOSPITAL_ID != person.HOSPITAL_ID
//                          || user.STATE_FLAG != person.PERSON_STATE
//                          || user.DEPT_CODE != person.PGROUP_ID || (user.DEPT_CODE != null && person.PGROUP_ID == null)
//                          || user.POWER != person.DUTIES || (user.POWER != null && person.DUTIES == null)
//                          || user.USER_TYPE != person.USER_TYPE || (user.USER_TYPE != null && person.USER_TYPE == null)
//                          // || user.TECH_POST != person.ACADEMIC_POST 职称不从系统数据同步
//                          || user.HIS_ID != person.HIS_ID || (user.HIS_ID != null && person.HIS_ID == null)
//                          || user.JOB_STATE != person.PERSON_DOC_STATE || (user.JOB_STATE != null && person.PERSON_DOC_STATE == null)
//                          || user.LOGID != person.LOGID
//                          || user.ID_CARD != person.ID_CARD || (user.ID_CARD != null && person.ID_CARD == null)
//                          || person.PERSON_STATE != user.STATE_FLAG
//                          || person.USER_NAME != user.USERNAME)
//                         .Select((user, person) => new { user, person }).ToList();
//                    List<PMS_PERSON_INFO> persons = new List<PMS_PERSON_INFO>();
//                    //var flags1 = changeMainFieldData.Select(p => new { flag = $"[{p.person.USER_ID}{p.person.USER_NAME}]" }).ToList();

//                    foreach (var item in changeMainFieldData)
//                    {
//                        Stopwatch stopwatch1 = Stopwatch.StartNew();
//                        PMS_PERSON_INFO personItemOjb = item.person;
//                        SYS6_USER user = item.user;
//                        WritePersonInfo(user, personItemOjb);
//                        persons.Add(personItemOjb);
//                        int updateCount = 0;
//                        string flagsStr = "";
//                        if (persons.Count > 0)
//                        {
//                            updateCount = _soa.Db.Updateable(persons).ExecuteCommand();
//                            var flags = persons.Select(p => new { flag = $"[{p.USER_ID}{p.USER_NAME}]" }).ToList();
//                            flagsStr = string.Join(",", flags);
//                        }
//                        stopwatch1.Stop();
//                        if (updateCount > 0)
//                        {
//                            Log.Information($"==>[定时同步用户数据到人员档案表任务]执行结束.共维护[{changeMainFieldData.Count}]条核心字段变更数据,更新成功条数:[{updateCount}],数据为:[{flagsStr}],耗时:[{stopwatch1.ElapsedMilliseconds}]ms");
//                        }
//                    }
//                }
//                catch (Exception ex)
//                {
//                    Log.Error($"==>[定时同步用户数据到人员档案表任务]执行结束:{ex}");
//                }
//                finally
//                {
//                    //_soa.Db.Ado.Close();
//                    //_soa.Db.Ado.Dispose();
//                    stopwatch.Stop();
//                    Log.Information($"==>[定时同步用户数据到人员档案表任务]执行结束.一次定时任务总耗时:[{stopwatch.ElapsedMilliseconds}]ms");
//                }

//            }
//        }

//        private void WritePersonInfo(SYS6_USER user, PMS_PERSON_INFO personItemOjb)
//        {
//            personItemOjb.HOSPITAL_ID = user.HOSPITAL_ID ?? "H000";
//            personItemOjb.PGROUP_ID = user.DEPT_CODE ?? "PG000";
//            personItemOjb.DUTIES = user.POWER;
//            personItemOjb.ACADEMIC_POST = user.TECH_POST;
//            personItemOjb.USER_TYPE = user.USER_TYPE;
//            personItemOjb.LOGID = user.LOGID;
//            personItemOjb.HIS_ID = user.HIS_ID;
//            personItemOjb.PERSON_STATE = user.STATE_FLAG;
//            personItemOjb.USER_NAME = user.USERNAME;
//            personItemOjb.PERSON_DOC_STATE = user.JOB_STATE;
//            personItemOjb.FIRST_RTIME = user.FIRST_RTIME?.ToString();//同步时以更早一条记录为准
//            personItemOjb.LAST_MTIME = DateTime.Now.ToString("yyyy/MM/dd hh:mm:ss");
//            if (user.ID_CARD != null && personItemOjb.ID_CARD != user.ID_CARD && user.ID_CARD.Length == 18)
//            {
//                personItemOjb.ID_CARD = user.ID_CARD;
//                personItemOjb.CARD_TYPE = "1";
//                personItemOjb.BIRTHDAY = user.ID_CARD.Substring(6, 4) + "-" + user.ID_CARD.Substring(10, 2) + "-" + user.ID_CARD.Substring(12, 2);
//                if (int.TryParse(user.ID_CARD.Substring(14, 3), out int sex))
//                {
//                    if (sex % 2 == 0)
//                    {
//                        personItemOjb.SEX = "2";
//                    }
//                    else
//                    {
//                        personItemOjb.SEX = "1";
//                    }
//                }
//                personItemOjb.AGE = CommonHelper.CalculateAge(personItemOjb.BIRTHDAY).ToString();
//            }
//            personItemOjb.ID_CARD = user.ID_CARD == null ? "" : user.ID_CARD;
//        }


//        private void AddPersonInfo()
//        {
//            Stopwatch stopwatch = Stopwatch.StartNew();
//            List<SYS6_USER> insertUsers = null;
//            try
//            {
//                insertUsers = _soa.Db.Queryable<SYS6_USER>().LeftJoin<PMS_PERSON_INFO>((user, person) => user.USER_NO == person.USER_ID)
//                .Where((user, person) => person.USER_ID == null //人员表不存在
//                  && user.LAB_ID != null && user.HIS_ID != null  //科室、工号都不为空
//                  && user.HIS_ID != "0000" && user.HIS_ID != "00000"   //排除0000工号
//                  && user.MANAGE_CLASS != null && user.MANAGE_CLASS == "4" //非机构级、分组级管理账号)
//                  && user.DEPT_CODE != null) //没有维护专业组，插入PMS_PERSON_INFO表将报错
//                .ToList();
//            }
//            catch { }
//            if (insertUsers != null && insertUsers.Any())
//            {
//                var insertPersons = insertUsers.Select(user =>
//                {
//                    var person = new PMS_PERSON_INFO();
//                    person.PERSON_ID = IDGenHelper.CreateGuid();
//                    person.USER_ID = user.USER_NO;
//                    WritePersonInfo(user, person);
//                    return person;
//                }).ToList();

//                var insertCount = _soa.Db.Insertable(insertPersons).ExecuteCommand();
//                stopwatch.Stop();
//                if (insertCount > 0)
//                {
//                    var flags = string.Join('、', insertPersons.Select(p => new { flag = $"[{p.USER_ID}_{p.USER_NAME}]" }));
//                    Log.Information($"==>[定时同步用户数据到人员档案表任务]执行结束.共新增成功条数:[{insertCount}],数据为:[{flags}],耗时:[{stopwatch.ElapsedMilliseconds}]ms");
//                }
//            }
//        }


//        private bool GetTheLock()
//        {
//            bool result = false;
//            var time = TimeSpan.FromSeconds(20);
//            IRedisCachingProvider redis = null;

//            try
//            {
//                redis = _cachingProvider.GetRedisProvider("S03");

//                result = redis.StringSet(lockName, $"{_configuration.GetSection("Kestrel:Endpoints:Https:Url").Value}", time, "nx");
//            }
//            catch
//            {
//                Log.Error("==>[定时同步用户数据到人员档案表任务]GetTheLock获取Redis服务失败！");
//            }

//            if (redis == null)
//                Log.Error("==>[定时同步用户数据到人员档案表任务]GetTheLock获取Redis服务为空！");

//            return result;
//        }

//        private void GetTheLockFalse()
//        {
//            try
//            {
//                var redis = _cachingProvider.GetRedisProvider("S03");
//                var serviceName = redis.StringGetAsync(lockName);
//                var ttl = redis.TTL(lockName);
//                if (ttl / 3600 >= 1)
//                {
//                    Console.WriteLine($"已有服务正在执行，服务名：{serviceName},剩余时长未{ttl / 3600}小时");
//                }
//                if (ttl / 3600 < 1)
//                {
//                    Console.WriteLine($"已有服务正在执行，服务名：{serviceName},剩余时长未{ttl}秒");
//                }
//            }
//            catch { }
//        }
//    }
//}
