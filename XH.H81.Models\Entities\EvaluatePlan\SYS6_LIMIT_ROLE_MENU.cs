﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Entities.EvaluatePlan
{
    [DBOwner("XH_SYS")]
    public class SYS6_LIMIT_ROLE_MENU
    {
        /// <summary>
        /// 编号
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string SERIAL_NO { get; set; }

        /// <summary>
        /// 限权组合ID
        /// </summary>
        public string LROLE_NO { get; set; }

        /// <summary>
        /// 限权模块
        /// </summary>
        public string MODULE_ID { get; set; }

        /// <summary>
        /// 菜单/功能编号
        /// </summary>
        public string MENU_NO { get; set; }

        /// <summary>
        /// 首次登记人
        /// </summary>
        public string FIRST_RPERSON { get; set; }

        /// <summary>
        /// 首次登记时间
        /// </summary>
        public DateTime? FIRST_RTIME { get; set; }

        /// <summary>
        /// 最后修改人员
        /// </summary>
        public string LAST_MPERSON { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime? LAST_MTIME { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string REMARK { get; set; }
    }
}
