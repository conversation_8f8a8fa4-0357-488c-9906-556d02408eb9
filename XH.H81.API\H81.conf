server {
      #端口号
      listen 18081 ssl;
      # gzip config
      gzip on;
      gzip_min_length 1k;
      gzip_comp_level 9;
      gzip_types text/plain text/css text/javascript application/json application/javascript application/x-javascript application/xml;
      gzip_vary on;
      gzip_disable "MSIE [1-6]\.";


      location / {
            #网站文件地址
            root  /xh22sc/H81/web;
            index  index.html index.htm;
            try_files  $uri $uri/ /index.html;
      }
        location /api {
        #后台API地址
        proxy_pass https://localhost:18481;
    }
        location /ExampleFile/ {
        #后台图片地址
        proxy_pass https://localhost:18481;
     }
        #share文件地址 
        location /H81pdf/api/ {
		proxy_pass https://localhost:18481; 
         }

     location /S54/ {
		proxy_pass https://localhost:18481; 
         }

}

