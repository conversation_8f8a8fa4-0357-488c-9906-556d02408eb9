﻿using H.Utility.SqlSugarInfra;
using Microsoft.EntityFrameworkCore;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.ExternalEntity
{
    [DBOwner("XH_SYS")]
    public class SYS6_LAB_MENU
    {
        [Key]
        [StringLength(50)]
        [Unicode(false)]
        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string MENU_ID { get; set; }
        [StringLength(20)]
        [Unicode(false)]
        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string HOSPITAL_ID { get; set; }

        [StringLength(20)]
        [Unicode(false)]
        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string LAB_ID { get; set; }
        [StringLength(10)]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string MENU_LSTATE { get; set; }
        [StringLength(50)]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string FIRST_RPERSON { get; set; }
        [Column(TypeName = "DATE")]
        public DateTime? FIRST_RTIME { get; set; }
        [StringLength(50)]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string LAST_MPERSON { get; set; }
        [Column(TypeName = "DATE")]
        public DateTime? LAST_MTIME { get; set; }
        [StringLength(200)]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string REMARK { get; set; }

        /// <summary>
        /// 菜单属性
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string MENU_LJSON { get; set; }

        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? IF_BIOSAFETY { get; set; }
    }
}
