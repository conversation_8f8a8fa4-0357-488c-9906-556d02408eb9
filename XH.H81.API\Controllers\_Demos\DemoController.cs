﻿using H.Utility;
using Microsoft.AspNetCore.Mvc;
using System.Diagnostics;
using XH.H81.IServices;
using XH.H81.Models.Dtos;
using XH.H81.Models.Entities;
using XH.H81.Models.ExternalEntity;

namespace XH.H81.API.Controllers._demos
{
    /// <summary>
    /// 使用样例
    /// </summary>
    [Route("api/[controller]/[action]")]
    [ApiController]
    [ApiExplorerSettings(GroupName = "DEMO")]

    public class DemoController : ControllerBase
    {
        private readonly IDemoServices _sampleService;

        public DemoController(IDemoServices sampleService,IHostEnvironment env)
        {
           
            _sampleService = sampleService;
        }
        /// <summary>
        /// 默认数据库EF 样例
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [NonRoleAuthorizeAttribute]//不验证角色
        public IActionResult EFDemoFromDefaultDb()
        {
            var res = _sampleService.GetDemoData();
            return Ok(res.ToResultDto());
        }
        /// <summary>
        /// 数据库2访问样例
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        
        public IActionResult EFDemoFromDb2()
        {
            var res = _sampleService.TestDB2EF();
            return Ok(res.ToResultDto());
        }

        /// <summary>
        /// 批量插入demo
        /// </summary>
        /// <returns></returns>
        [HttpPut]
        public IActionResult BulkInsertDemo()
        {
            List<TEST_START_TEMPLATE> bulkModel=new List<TEST_START_TEMPLATE>();

            for (int i = 0; i < 100; i++)
            {
                bulkModel.Add(new TEST_START_TEMPLATE()
                {
                    ID = IDGenHelper.CreateGuid().ToString(),
                    NAME = "批量生成"+i.ToString(),
                    VALUE = i,
                    CREATE_TIME = DateTime.Now
                });
            }
            Stopwatch sw=new Stopwatch();
            sw.Start();
            _sampleService.BulkInsert(bulkModel);
            sw.Stop();
            return Ok($"批量插入100条耗时:${sw.ElapsedMilliseconds}ms".ToResultDto());
        }

        /// <summary>
        /// 批量删除
        /// </summary>
        /// <returns></returns>
        [HttpDelete]
        public IActionResult BulkDeleteDemo()
        {
            var deleteData = _sampleService.GetDemoData().Take(100);
            Stopwatch sw = new Stopwatch();
            sw.Start();
            _sampleService.BulkDelete(deleteData.ToList());
            sw.Stop();
            return Ok($"批量删除top100耗时:${sw.ElapsedMilliseconds}ms".ToResultDto());
        }

        /// <summary>
        /// 按条件删除数
        /// </summary>
        /// <returns></returns>
        [HttpDelete]
        public IActionResult DeleteByPredicate()
        {
           var i= _sampleService.DeleteByPredicate();
           return Ok(i.ToResultDto());
        }
        /// <summary>
        /// 按条件将Value小于50的数据改成5000
        /// </summary>
        /// <returns></returns>

        [HttpPut]
        public IActionResult UpdateByPredicate()
        {
            _sampleService.UpdateByPredicate();
            return Ok();
        }

        /// <summary>
        /// 使用原生sql语句查询 
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult QueryBySql()
        {
            var res=_sampleService.QueryBySQL();
            return Ok(res.ToResultDto());
        }

        /// <summary>
        /// 测试automapper
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult AutoMapperTest()
        {
            var res = _sampleService.AutoMapTest();
            return Ok(res.ToResultDto());
        }
        /// <summary>
        /// 自动模型验证测试
        /// </summary>
        /// <returns></returns>

        [HttpPost]
        public IActionResult ModelValidateTest(StartTemplateDto model)
        {
        
            return Ok(new ResultDto()
            {
                data="模型验证测试通过"
            });
        }
    }
}
