﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Entities.Eguard
{
    /// <summary>
    /// 实验室访问申请表
    /// </summary>
    [DBOwner("XH_OA")]
    public class PMS_VISIT_REQUEST_LIST
    {
        /// <summary>
        /// 访问申请ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string VISIT_REQ_ID { get; set; }
        /// <summary>
        /// 人员ID
        /// </summary>
        public string PERSON_ID { get; set; }
        /// <summary>
        /// 医疗机构ID
        /// </summary>
        public string HOSPITAL_ID { get; set; }
        /// <summary>
        /// 批次ID
        /// </summary>
        public string BATCH_ID { get; set; }
        /// <summary>
        /// 申请类型
        /// </summary>
        public string VISIT_REQ_TYPE { get; set; }
        /// <summary>
        /// 科室人员
        /// </summary>
        public string? VISIT_PERSON_ID { get; set; }
        /// <summary>
        /// 访问地点
        /// </summary>
        public string? VISIT_ROOM_ID { get; set; }
        /// <summary>
        /// 同行人员
        /// </summary>
        public string? FELLOW_PERSON { get; set; }
        /// <summary>
        /// 同行人数
        /// </summary>
        public int? FELLOW_PERSON_NUM { get; set; }
        /// <summary>
        /// 访问理由
        /// </summary>
        public string? VISIT_REQ_REASON { get; set; }
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? VISIT_START_TIME { get; set; }
        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? VISIT_END_TIME { get; set; }
        /// <summary>
        /// 状态  0未提交 1待审核 2待审批 3通过 4使用中 5驳回 6拒绝 7取消
        /// </summary>
        public string? VISIT_REQ_STATE { get; set; }
        /// <summary>
        /// 申请来源 1i检验 （默认）2PC端录入
        /// </summary>
        public string? VISIT_ORIGIN { get; set; }
        public string? FIRST_RPERSON { get; set; }
        public DateTime? FIRST_RTIME { get; set; }
        public string? LAST_MPERSON { get; set; }
        public DateTime? LAST_MTIME { get; set; }
        public string? REMARK { get; set; }

        /// <summary>
        /// 门禁核准PC端可操作的状态
        /// <remarks>待审核、待审批、通过、驳回</remarks>
        /// </summary>
        public static readonly List<string> EguardApproval_PC = new()
        {
            "1","2","3","5"
        };
        /// <summary>
        /// 门禁核准PC端显示的数据状态
        /// </summary>
        public static readonly List<string> EguardState_PC = new()
        {
            "1","2","3","4","5","6","7","8","9","10","11"
        };
    }
}
