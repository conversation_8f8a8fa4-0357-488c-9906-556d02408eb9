﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Dtos.Eguard
{
    /// <summary>
    /// 保存门禁组合
    /// </summary>
    public class SaveEguardComRequest
    {
        /// <summary>
        /// 门禁组合ID
        /// </summary>
        public string? EGUARD_COM_ID { get; set; }
        /// <summary>
        /// 组合名称
        /// </summary>
        public string? EGUARD_COM_NAME { get; set; }
        /// <summary>
        /// 组合描述
        /// </summary>
        public string? EGUARD_COM_DESC { get; set; }
        /// <summary>
        /// 关联的门禁ID
        /// </summary>
        public List<string>? EGUARD_IDS { get; set; }
    }
}
