﻿INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000171', '33A001', 'H81', '基本信息', 'USER_NAME', '姓名', NULL, NULL, '["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]', '{"dataType":"Input","rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"formName":"姓名"}', '{"group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"单行文本","dataType":"Input","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 21:26:42.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000172', '33A001', 'H81', '基本信息', 'HIS_ID', '工号', NULL, NULL, '["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]', '{"dataType":"Input","rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"formName":"工号","dataClass":null,"labelHide":false,"labelStyle":{"color":"rgb(208,2,27)","fontSize":13,"fontWeight":"normal","fontStyle":"normal","background":null,"textAlign":"right","justifyContent":"right"}}', '{"group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"单行文本","dataType":"Input","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 18:52:10.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000178', '33A001', 'H81', '基本信息', 'ID_CARD', '证件号码', NULL, NULL, '["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]', '{"dataType":"Input","rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"formName":"证件号码"}', '{"group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"单行文本","dataType":"Input","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 21:00:21.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000207', '33A001', 'H81', '基本信息', 'PERSON_DOC_STATE', '人员性质', NULL, NULL, '["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]', '{"dataType":"Input","rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"formName":"人员性质"}', '{"group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"单行文本","dataType":"Input","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 18:38:47.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000180', '33A001', 'H81', '基本信息', 'SEX', '性别/年龄', NULL, NULL, '["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"]', '{"dataClass":"性别","queryType":"api","dataType":"Select","rules":[{"required":true}],"wigetProps":{},"formName":"性别/年龄"}', '{"group":"基本","formId":"xialakuang","icon":"iconxialakuang","formName":"下拉框","isForm":true,"dataType":"Select","wigetProps":{"options":[],"defaultValue":"2","allowClear":false},"propslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"dataClass":"性别","queryType":"api","rules":[{"required":false}]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 18:38:48.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000203', '33A001', 'H81', '基本信息', 'EMERGENCY_CONTACT', '紧急联系人', NULL, NULL, '["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]', '{"dataType":"Input","rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"formName":"紧急联系人"}', '{"group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"单行文本","dataType":"Input","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 18:38:49.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000206', '33A001', 'H81', '基本信息', 'COMM_ADDR', '详细通讯地址', NULL, NULL, '["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]', '{"dataType":"Input","rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"formName":"详细通讯地址"}', '{"group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"单行文本","dataType":"Input","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 18:38:50.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000209', '33A001', 'H81', '基本信息', 'EMPLOYMENT_SOURE', '入职方式', NULL, NULL, '["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"]', '{"dataClass":"入职方式","queryType":"api","dataType":"Select","wigetProps":{"options":[],"defaultValue":"3"},"formName":"入职方式"}', '{"group":"基本","formId":"xialakuang","icon":"iconxialakuang","formName":"下拉框","isForm":true,"dataType":"Select","wigetProps":{"options":[],"defaultValue":"3"},"propslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"dataClass":"入职方式","queryType":"api"}', '1', 'fr_李影', TIMESTAMP '2025-06-12 18:38:52.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000210', '33A001', 'H81', '基本信息', 'USER_TYPE', '用工类型', NULL, NULL, '["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"]', '{"dataClass":"用工类型","queryType":"api","dataType":"Select","wigetProps":{"options":[],"defaultValue":""},"formName":"用工类型"}', '{"group":"基本","formId":"xialakuang","icon":"iconxialakuang","formName":"下拉框","isForm":true,"dataType":"Select","wigetProps":{"options":[],"defaultValue":""},"propslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"dataClass":"用工类型","queryType":"api"}', '1', 'fr_李影', TIMESTAMP '2025-06-12 18:38:53.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000214', '33A001', 'H81', '基本信息', 'EMPLOYMENT_UNIT', '聘任职称评定单位', NULL, NULL, '["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]', '{"dataType":"Input","rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"formName":"聘任职称评定单位"}', '{"group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"单行文本","dataType":"Input","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 18:38:55.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000216', '33A001', 'H81', '基本信息', 'TECH_CERTIFICE_TIME', '职称评定日期', NULL, NULL, '["dataType","required","disabled","picker","allowClear"]', '{"dataType":"DatePicker","wigetProps":{},"formName":"职称评定日期"}', '{"group":"基本","formId":"riqi","icon":"iconriqi","formName":"日期","dataType":"DatePicker","isForm":true,"wigetProps":{},"propslist":["dataType","required","disabled","picker","allowClear"]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 20:54:43.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000219', '33A001', 'H81', '基本信息', 'LENGTH_SERVICE', '工龄', NULL, NULL, '["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]', '{"dataType":"Input","rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"formName":"工龄"}', '{"group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"单行文本","dataType":"Input","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 18:38:58.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000181', '33A001', 'H81', '基本信息', 'AGE', '年龄', NULL, NULL, '["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]', '{"dataType":"Input","rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"formName":"年龄"}', '{"group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"单行文本","dataType":"Input","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 18:38:59.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000182', '33A001', 'H81', '基本信息', 'NATIVE_PLACE', '籍贯', NULL, NULL, '["dataType","required","allowClear","prefix","variant","placeholder","disabled","showSearch"]', '{"dataType":"Cascader","wigetProps":{},"formName":"籍贯"}', '{"group":"基本","formId":"jilianxuanze","icon":"iconxialakuang","formName":"级联选择","isForm":true,"dataType":"Cascader","wigetProps":{},"propslist":["dataType","required","allowClear","prefix","variant","placeholder","disabled","showSearch"]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 18:39:01.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000185', '33A001', 'H81', '基本信息', 'HIGHEST_DEGREE', '最高学历', NULL, NULL, '["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"]', '{"dataClass":"最高学历","queryType":"api","dataType":"Select","wigetProps":{"options":[],"defaultValue":"3"},"formName":"最高学历"}', '{"group":"基本","formId":"xialakuang","icon":"iconxialakuang","formName":"下拉框","isForm":true,"dataType":"Select","wigetProps":{"options":[],"defaultValue":"3"},"propslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"dataClass":"最高学历","queryType":"api","isDataTypeToPerson":true}', '1', 'zwt_周伟涛', TIMESTAMP '2025-06-12 19:57:53.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000218', '33A001', 'H81', '基本信息', 'RETIRE_TIME', '退休日期', NULL, NULL, '["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]', '{"dataType":"Input","rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"formName":"退休日期"}', '{"group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"单行文本","dataType":"Input","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 19:47:41.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000192', '33A001', 'H81', '基本信息', 'MARITAL_STATUS', '婚姻状况', NULL, NULL, '["dataType","required","optionType","styleFlex","disabled","queryType","dataClass","defaultValue","isDataTypeToPerson"]', '{"dataClass":"婚姻状况","queryType":"api","dataType":"Radio","wigetProps":{"options":[],"defaultValue":""},"formName":"婚姻状况"}', '{"group":"基本","formId":"danxuankuang","icon":"icondanxuan","formName":"单选","isForm":true,"dataType":"Radio","wigetProps":{"options":[],"defaultValue":""},"propslist":["dataType","required","optionType","styleFlex","disabled","queryType","dataClass","defaultValue","isDataTypeToPerson"],"dataClass":"婚姻状况","queryType":"api"}', '1', 'fr_李影', TIMESTAMP '2025-06-12 18:39:04.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000196', '33A001', 'H81', '基本信息', 'EYESIGHT_RIGHT', '视力', NULL, NULL, '["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]', '{"dataType":"Input","rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"formName":"视力"}', '{"group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"单行文本","dataType":"Input","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 18:39:05.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000200', '33A001', 'H81', '基本信息', 'PHONE', '联系方式', NULL, NULL, '["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]', '{"dataType":"Input","rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"formName":"联系方式"}', '{"group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"单行文本","dataType":"Input","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 18:39:07.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000202', '33A001', 'H81', '基本信息', 'OFFICE_PHONE', '办公电话', NULL, NULL, '["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]', '{"dataType":"Input","rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"formName":"办公电话"}', '{"group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"单行文本","dataType":"Input","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 18:39:08.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000274', '33A001', 'H81', '基本信息', 'QXXLZD58888', '全新下拉字段', NULL, NULL, '["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"]', '{"dataClass":"班次状态","dataType":"Select","queryType":"api","wigetProps":{"options":[{"value":"选项1","label":"选项1"},{"value":"选项2","label":"选项2"},{"value":"选项3","label":"选项3"}],"defaultValue":""},"formName":"全新下拉字段","labelHide":false,"labelStyle":{"color":"#000000","fontSize":13,"fontWeight":"normal","fontStyle":"normal","background":null,"textAlign":"right","justifyContent":"right"},"rules":[{"required":null}]}', '{"group":"基本","formId":"xialakuang","icon":"iconxialakuang","formName":"下拉框","isForm":true,"dataType":"Select","queryType":"api","wigetProps":{"options":[{"value":"选项1","label":"选项1"},{"value":"选项2","label":"选项2"},{"value":"选项3","label":"选项3"}],"defaultValue":""},"propslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"dataClass":"班次状态"}', '1', 'fr_李影', TIMESTAMP '2025-06-12 20:38:00.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000186', '33A001', 'H81', '基本信息', 'HIGHEST_DIPLOMA', '最高学位', NULL, NULL, '["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"]', '{"dataClass":"最高学位","queryType":"api","dataType":"Select","wigetProps":{"options":[{"label":"工作管理类A","value":"H818100000132"}]},"formName":"最高学位"}', '{"group":"基本","formId":"xialakuang","icon":"iconxialakuang","formName":"下拉框","isForm":true,"dataType":"Select","queryType":"api","wigetProps":{"options":[{"value":"选项1","label":"选项1"},{"value":"选项2","label":"选项2"},{"value":"选项3","label":"选项3"}],"defaultValue":""},"propslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"dataClass":"最高学位"}', '1', 'fr_李影', TIMESTAMP '2025-06-12 18:39:10.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000241', '33A001', 'H81', '基本信息', 'GWLB79071', '岗位类别', NULL, NULL, '["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"]', '{"dataClass":"岗位类别","queryType":"api","dataType":"Select","wigetProps":{"options":[{"value":"选项1","label":"选项1"},{"value":"选项2","label":"选项2"},{"value":"选项3","label":"选项3"}],"defaultValue":""},"formName":"岗位类别"}', '{"group":"基本","formId":"xialakuang","icon":"iconxialakuang","formName":"下拉框","isForm":true,"dataType":"Select","queryType":"api","wigetProps":{"options":[{"value":"选项1","label":"选项1"},{"value":"选项2","label":"选项2"},{"value":"选项3","label":"选项3"}],"defaultValue":""},"propslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"dataClass":"岗位类别"}', '1', 'fr_李影', TIMESTAMP '2025-06-12 18:39:11.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000242', '33A001', 'H81', '基本信息', 'CJPXQK82201', '参加培训情况', NULL, NULL, '["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]', '{"dataType":"Input","rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"formName":"参加培训情况"}', '{"group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"单行文本","dataType":"Input","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 18:39:12.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000198', '33A001', 'H81', '基本信息', 'HEALTH', '健康状况', NULL, NULL, '["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"]', '{"dataClass":"健康状况","queryType":"api","dataType":"Select","wigetProps":{"options":[],"defaultValue":""},"formName":"健康状况"}', '{"group":"基本","formId":"xialakuang","icon":"iconxialakuang","formName":"下拉框","isForm":true,"dataType":"Select","wigetProps":{"options":[],"defaultValue":""},"propslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"dataClass":"健康状况","queryType":"api"}', '1', 'fr_李影', TIMESTAMP '2025-06-12 18:39:13.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000243', '33A001', 'H81', '基本信息', 'PXHGZH48698', '培训合格证号', NULL, NULL, '["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]', '{"dataType":"Input","rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"formName":"培训合格证号"}', '{"group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"单行文本","dataType":"Input","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 19:23:00.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000245', '33A001', 'H81', '基本信息', 'XZD45449', '新字段', NULL, NULL, '["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"]', '{"dataClass":"参会人身份","queryType":"api","dataType":"Select","wigetProps":{"options":[],"defaultValue":"XBD00000001"},"formName":"新字段"}', '{"group":"基本","formId":"xialakuang","icon":"iconxialakuang","formName":"下拉框","isForm":true,"dataType":"Select","wigetProps":{"options":[],"defaultValue":"XBD00000001"},"propslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"queryType":"api","dataClass":"参会人身份","isDataTypeToPerson":true}', '1', 'fr_李影', TIMESTAMP '2025-06-12 19:30:33.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000212', '33A001', 'H81', '基本信息', 'TECH_POST_PROFESSION', '职称专业', NULL, NULL, '["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]', '{"dataType":"Input","rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"formName":"职称专业"}', '{"group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"单行文本","dataType":"Input","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 18:38:54.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000220', '33A001', 'H81', '基本信息', 'IN_HOSPITAL_TIME', '来院日期', NULL, NULL, '["dataType","required","disabled","picker","allowClear"]', '{"dataType":"DatePicker","wigetProps":{},"formName":"来院日期"}', '{"group":"基本","formId":"riqi","icon":"iconriqi","formName":"日期","dataType":"DatePicker","isForm":true,"wigetProps":{},"propslist":["dataType","required","disabled","picker","allowClear"]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 20:54:43.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000221', '33A001', 'H81', '基本信息', 'OUT_HOSPITAL_TIME', '离院日期', NULL, NULL, '["dataType","required","disabled","picker","allowClear"]', '{"dataType":"DatePicker","wigetProps":{},"formName":"离院日期"}', '{"group":"基本","formId":"riqi","icon":"iconriqi","formName":"日期","dataType":"DatePicker","isForm":true,"wigetProps":{},"propslist":["dataType","required","disabled","picker","allowClear"]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 20:54:43.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000222', '33A001', 'H81', '基本信息', 'LENGTH_HOSPITAL', '院龄', NULL, NULL, '["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]', '{"dataType":"Input","rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"formName":"院龄"}', '{"group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"单行文本","dataType":"Input","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 18:39:19.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000224', '33A001', 'H81', '基本信息', 'OUT_LAB_TIME', '离科日期', NULL, NULL, '["dataType","required","disabled","picker","allowClear"]', '{"dataType":"DatePicker","wigetProps":{},"formName":"离科日期"}', '{"group":"基本","formId":"riqi","icon":"iconriqi","formName":"日期","dataType":"DatePicker","isForm":true,"wigetProps":{},"propslist":["dataType","required","disabled","picker","allowClear"]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 20:54:43.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000225', '33A001', 'H81', '基本信息', 'LENGTH_LAB', '科龄', NULL, NULL, '[
  "dataType",
  "required",
  "allowClear",
  "placeholder",
  "maxLength",
  "prefix",
  "suffix",
  "variant",
  "disabled",
  "showCount"
]', '{"dataClass":null,"dataType":"Input","labelHide":false,"labelStyle":null,"rules":[{"required":true}],"wigetProps":{"button":false,"groupKey":null},"formName":"科龄"}', NULL, '1', 'zwt_周伟涛', TIMESTAMP '2025-06-12 17:33:14.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000188', '33A001', 'H81', '基本信息', 'GRADUATE_DATE', '毕业日期', NULL, NULL, '["dataType","required","disabled","picker","allowClear"]', '{"dataType":"DatePicker","wigetProps":{},"formName":"毕业日期"}', '{"group":"基本","formId":"riqi","icon":"iconriqi","formName":"日期","dataType":"DatePicker","isForm":true,"wigetProps":{},"propslist":["dataType","required","disabled","picker","allowClear"]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 20:54:43.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000240', '33A001', 'H81', '基本信息', 'XCSGW70984', '现从事岗位', NULL, NULL, '["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"]', '{"dataClass":"现从事岗位","queryType":"api","dataType":"Select","rules":[{"required":false}],"wigetProps":{"options":[{"value":"选项1","label":"选项1"},{"value":"选项2","label":"选项2"},{"value":"选项3","label":"选项3"}],"defaultValue":""},"formName":"现从事岗位"}', '{"group":"基本","formId":"xialakuang","icon":"iconxialakuang","formName":"下拉框","isForm":true,"dataType":"Select","queryType":"api","wigetProps":{"options":[{"value":"选项1","label":"选项1"},{"value":"选项2","label":"选项2"},{"value":"选项3","label":"选项3"}],"defaultValue":""},"propslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"dataClass":"现从事岗位","rules":[{"required":false}]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 19:24:19.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000204', '33A001', 'H81', '基本信息', 'ECONTACT_RELACTION', '关系', NULL, NULL, '["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]', '{"dataType":"Input","rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"formName":"与紧急联系人的关系"}', '{"group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"单行文本","dataType":"Input","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 18:39:26.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000174', '33A001', 'H81', '基本信息', 'CARD_TYPE', '证件类型', NULL, NULL, '["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"]', '{"dataClass":"证件类型","queryType":"api","dataType":"Select","rules":[{"required":false}],"wigetProps":{"options":[],"defaultValue":"3"},"formName":"证件类型"}', '{"group":"基本","formId":"xialakuang","icon":"iconxialakuang","formName":"下拉框","isForm":true,"dataType":"Select","wigetProps":{"options":[],"defaultValue":"3"},"propslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"dataClass":"证件类型","queryType":"api","rules":[{"required":false}]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 19:46:34.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000213', '33A001', 'H81', '基本信息', 'TECH_POST', '职称级别', NULL, NULL, '["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"]', '{"dataClass":"职称级别","queryType":"api","dataType":"Select","rules":[{"required":false}],"wigetProps":{"options":[],"defaultValue":""},"formName":"职称级别"}', '{"group":"基本","formId":"xialakuang","icon":"iconxialakuang","formName":"下拉框","isForm":true,"dataType":"Select","wigetProps":{"options":[],"defaultValue":""},"propslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"dataClass":"职称级别","queryType":"api","rules":[{"required":false}]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 19:46:43.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000199', '33A001', 'H81', '基本信息', 'PROFESSION_EXPERTISE', '专业特长', NULL, NULL, '["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]', '{"dataType":"Input","rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"formName":"专业特长"}', '{"group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"单行文本","dataType":"Input","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 18:39:06.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000237', '33A001', 'H81', '基本信息', 'BASYS55209', '备案实验室', NULL, NULL, '["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"]', '{"dataClass":"DataClass_By_Smbl_Lab","queryType":"api","dataType":"Select","wigetProps":{"options":[{"value":"选项1","label":"选项1"},{"value":"选项2","label":"选项2"},{"value":"选项3","label":"选项3"}]},"formName":"备案实验室"}', '{"group":"基本","formId":"xialakuang","icon":"iconxialakuang","formName":"下拉框","isForm":true,"dataType":"Select","dataClass":"DataClass_By_Smbl_Lab","queryType":"api","wigetType":"fixed","wigetProps":{"options":[{"value":"选项1","label":"选项1"},{"value":"选项2","label":"选项2"},{"value":"选项3","label":"选项3"}]},"propslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 18:39:32.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000223', '33A001', 'H81', '基本信息', 'IN_LAB_TIME', '来科日期', NULL, NULL, '["dataType","required","disabled","picker","allowClear"]', '{"dataType":"DatePicker","wigetProps":{},"formName":"来科日期"}', '{"group":"基本","formId":"riqi","icon":"iconriqi","formName":"日期","dataType":"DatePicker","isForm":true,"wigetProps":{},"propslist":["dataType","required","disabled","picker","allowClear"]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 21:10:48.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000238', '33A001', 'H81', '基本信息', 'SFWDWKYHZRY39890', '是否外单位科研合作人员', NULL, NULL, '["dataType","required","optionType","styleFlex","disabled","queryType","dataClass","defaultValue","isDataTypeToPerson"]', '{"dataClass":"是否","queryType":"api","dataType":"Radio","wigetProps":{"options":[],"defaultValue":""},"formName":"是否外单位科研合作人员"}', '{"group":"基本","formId":"danxuankuang","icon":"icondanxuan","formName":"单选","isForm":true,"dataType":"Radio","wigetProps":{"options":[],"defaultValue":""},"propslist":["dataType","required","optionType","styleFlex","disabled","queryType","dataClass","defaultValue","isDataTypeToPerson"],"dataClass":"是否","queryType":"api"}', '1', 'fr_李影', TIMESTAMP '2025-06-12 18:39:34.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000177', '33A001', 'H81', '基本信息', 'PGROUP_ID', '专业组', NULL, NULL, '["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"]', '{"dataClass":"DataClass_By_PGROUP_ID","queryType":"api","dataType":"Select","wigetProps":{"options":[{"value":"选项1","label":"选项1"},{"value":"选项2","label":"选项2"},{"value":"选项3","label":"选项3"}]},"formName":"专业组"}', '{"group":"基本","formId":"xialakuang","icon":"iconxialakuang","formName":"下拉框","isForm":true,"dataType":"Select","dataClass":"DataClass_By_PGROUP_ID","queryType":"api","wigetType":"fixed","wigetProps":{"options":[{"value":"选项1","label":"选项1"},{"value":"选项2","label":"选项2"},{"value":"选项3","label":"选项3"}]},"propslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 18:39:35.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000217', '33A001', 'H81', '基本信息', 'WORK_TIME', '参加工作日期', NULL, NULL, '["dataType","required","disabled","picker","allowClear"]', '{"dataType":"DatePicker","wigetProps":{},"formName":"参加工作日期"}', '{"group":"基本","formId":"riqi","icon":"iconriqi","formName":"日期","dataType":"DatePicker","isForm":true,"wigetProps":{},"propslist":["dataType","required","disabled","picker","allowClear"]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 21:10:54.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000190', '33A001', 'H81', '基本信息', 'ENGLISH_RANK', '英语等级', NULL, NULL, '["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"]', '{"dataClass":"英语级别","queryType":"api","dataType":"Select","rules":[{"required":false}],"wigetProps":{"options":[{"value":"选项1","label":"选项1"},{"value":"选项2","label":"选项2"},{"value":"选项3","label":"选项3"}],"defaultValue":""},"formName":"英语等级"}', '{"group":"基本","formId":"xialakuang","icon":"iconxialakuang","formName":"下拉框","isForm":true,"dataType":"Select","queryType":"api","wigetProps":{"options":[{"value":"选项1","label":"选项1"},{"value":"选项2","label":"选项2"},{"value":"选项3","label":"选项3"}],"defaultValue":""},"propslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"rules":[{"required":false}],"dataClass":"英语级别"}', '1', 'fr_李影', TIMESTAMP '2025-06-12 19:52:17.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000176', '33A001', 'H81', '基本信息', 'LOGID', '登录ID', NULL, NULL, '["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]', '{"dataType":"Input","rules":[{"required":true}],"wigetProps":{"options":[{"value":"选项1","label":"选项1"},{"value":"选项2","label":"选项2"},{"value":"选项3","label":"选项3"}],"defaultValue":"H818100000052"},"formName":"登录ID","dataClass":"人员培训分类"}', '{"group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"单行文本","dataType":"Input","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 19:52:36.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', '登录');
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000239', '33A001', 'H81', '基本信息', 'BZ63459', '备注', NULL, NULL, '["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]', '{"dataType":"Input","rules":[{"required":false}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"formName":"备注"}', '{"group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"单行文本","dataType":"Input","isForm":true,"rules":[{"required":false}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 19:54:32.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('*********', '33A001', 'H81', '基本信息', 'PXHGZS25179', '培训合格证书', NULL, NULL, '["dataType","required","multiple","listType","accept"]', '{"dataType":"Upload","wigetProps":{"listType":"picture-card","multiple":true,"accept":".png,.jpg,.jpeg,.pdf,.xls,.xlsx,.doc,.docx","showUploadList":true},"formName":"培训合格证书"}', '{"group":"基本","formId":"fujian","icon":"iconfujian","formName":"附件","isForm":true,"dataType":"Upload","wigetProps":{"listType":"picture-card","multiple":true,"accept":".png,.jpg,.jpeg,.pdf,.xls,.xlsx,.doc,.docx","showUploadList":true},"propslist":["dataType","required","multiple","listType","accept"]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 18:39:41.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('*********', '33A001', 'H81', '基本信息', 'CHILDREN_CONDITION', '有无子女', NULL, NULL, '["dataType","required","optionType","styleFlex","disabled","queryType","dataClass","defaultValue","isDataTypeToPerson"]', '{"dataClass":"有无子女","queryType":"api","dataType":"Radio","rules":[{"required":false}],"wigetProps":{"options":[],"defaultValue":"2"},"formName":"有无子女"}', '{"group":"基本","formId":"danxuankuang","icon":"icondanxuan","formName":"单选","isForm":true,"dataType":"Radio","wigetProps":{"options":[],"defaultValue":"2"},"propslist":["dataType","required","optionType","styleFlex","disabled","queryType","dataClass","defaultValue","isDataTypeToPerson"],"dataClass":"有无子女","queryType":"api","rules":[{"required":false}]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 19:54:38.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000175', '33A001', 'H81', '基本信息', 'BIRTHDAY', '出生年月', NULL, NULL, '["dataType","required","disabled","picker","allowClear"]', '{"dataType":"DatePicker","wigetProps":{},"formName":"出生年月"}', '{"group":"基本","formId":"riqi","icon":"iconriqi","formName":"日期","dataType":"DatePicker","isForm":true,"wigetProps":{},"propslist":["dataType","required","disabled","picker","allowClear"]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 20:54:43.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000195', '33A001', 'H81', '基本信息', 'HEIGHT', '身高', NULL, NULL, '["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]', '{"dataType":"Input","rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"formName":"身高"}', '{"group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"单行文本","dataType":"Input","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 19:55:02.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000205', '33A001', 'H81', '基本信息', 'ECONTACT_PHONE', '紧急联系人电话', NULL, NULL, '["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]', '{"dataType":"Input","rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"formName":"紧急联系人电话"}', '{"group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"单行文本","dataType":"Input","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 19:55:07.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000183', '33A001', 'H81', '基本信息', 'DOMICILE_PLACE', '户籍所在地', NULL, NULL, '["dataType","required","allowClear","prefix","variant","placeholder","disabled","showSearch"]', '{"dataType":"Cascader","rules":[{"required":false}],"wigetProps":{},"formName":"户籍所在地"}', '{"group":"基本","formId":"jilianxuanze","icon":"iconxialakuang","formName":"级联选择","isForm":true,"dataType":"Cascader","wigetProps":{},"propslist":["dataType","required","allowClear","prefix","variant","placeholder","disabled","showSearch"],"rules":[{"required":false}]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 19:55:12.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000258', '33A001', 'H81', '基本信息', 'SZ52717', '数值', NULL, NULL, '["dataType","required","allowClear","placeholder","maxLength","min","max","prefix","suffix","variant","disabled","controls"]', '{"dataType":"InputNumber","rules":[{"required":true}],"wigetProps":{"controls":true},"formName":"数值"}', '{"group":"基本","formId":"shuzhishurukuang","icon":"iconshuzhishurukuang","formName":"数值","isForm":true,"dataType":"InputNumber","wigetProps":{"controls":true},"propslist":["dataType","required","allowClear","placeholder","maxLength","min","max","prefix","suffix","variant","disabled","controls"],"rules":[{"required":true}]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 19:55:18.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000250', '33A001', 'H81', '基本信息', 'JBXXX75570', '基本信息项', NULL, NULL, '["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]', '{"dataType":"Input","rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"formName":"基本信息项"}', '{"group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"单行文本","dataType":"Input","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 19:55:23.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000252', '33A001', 'H81', '基本信息', 'GZGLLX49535', '工作管理类型', NULL, NULL, '["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]', '{"dataType":"Input","rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"formName":"工作管理类型"}', '{"group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"单行文本","dataType":"Input","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 19:55:27.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000256', '33A001', 'H81', '基本信息', 'JBXX70654', '基本信息', NULL, NULL, '["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]', '{"dataType":"Input","rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":false,"maxLength":200,"disabled":false,"variant":"outlined"},"formName":"基本信息"}', '{"group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"单行文本","dataType":"Input","isForm":true,"rules":[{"required":true}],"wigetProps":{"placeholder":"请输入","allowClear":false,"maxLength":200,"disabled":false,"variant":"outlined"},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 19:55:32.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000211', '33A001', 'H81', '基本信息', 'TECHNOLOGY_TYPE', '职称类型', NULL, NULL, '["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"]', '{"dataClass":"职称类型","queryType":"api","dataType":"Select","rules":[{"required":false}],"wigetProps":{"options":[],"defaultValue":"1"},"formName":"职称类型"}', '{"group":"基本","formId":"xialakuang","icon":"iconxialakuang","formName":"下拉框","isForm":true,"dataType":"Select","wigetProps":{"options":[],"defaultValue":"1"},"propslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"dataClass":"职称类型","queryType":"api","rules":[{"required":false}]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 19:55:42.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000215', '33A001', 'H81', '基本信息', 'ACADEMIC_POST', '职称名称', NULL, NULL, '["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"]', '{"dataClass":"医师职称","queryType":"api","dataType":"Select","rules":[{"required":true}],"wigetProps":{"options":[],"defaultValue":""},"formName":"职称名称"}', '{"group":"基本","formId":"xialakuang","icon":"iconxialakuang","formName":"下拉框","isForm":true,"dataType":"Select","queryType":"api","wigetProps":{"options":[],"defaultValue":""},"propslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"dataClass":"医师职称","rules":[{"required":true}]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 21:06:35.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000184', '33A001', 'H81', '基本信息', 'CURRENT_ADDRESS', '现居住地', NULL, NULL, '["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]', '{"dataType":"Input","rules":[{"required":false}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"formName":"现居住地"}', '{"group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"单行文本","dataType":"Input","isForm":true,"rules":[{"required":false}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 19:55:53.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000187', '33A001', 'H81', '基本信息', 'GRADUATE_SCHOOL', '毕业院校', NULL, NULL, '["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]', '{"dataType":"Input","rules":[{"required":false}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"formName":"毕业院校"}', '{"group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"单行文本","dataType":"Input","isForm":true,"rules":[{"required":false}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 19:56:06.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000189', '33A001', 'H81', '基本信息', 'PROFESSION', '毕业专业', NULL, NULL, '["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]', '{"dataType":"Input","rules":[{"required":false}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"formName":"毕业专业"}', '{"group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"单行文本","dataType":"Input","isForm":true,"rules":[{"required":false}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 19:49:21.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000197', '33A001', 'H81', '基本信息', 'EYESIGHT_LEFT', '视力（左/右）', NULL, NULL, '["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]', '{"dataType":"Input","rules":[{"required":false}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"formName":"视力（左/右）"}', '{"group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"单行文本","dataType":"Input","isForm":true,"rules":[{"required":false}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 19:49:24.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000264', '33A001', 'H81', '基本信息', 'YSXZQ25786', '颜色选择器', NULL, NULL, '["dataType","required","disabled","showText"]', '{"dataType":"ColorPicker","rules":[{"required":false}],"wigetProps":{"showText":true,"allowClear":true},"formName":"颜色选择器"}', '{"group":"基本","formId":"yansexuanze","icon":"iconyanse","formName":"颜色选择器","dataType":"ColorPicker","isForm":true,"wigetProps":{"showText":true,"allowClear":true},"propslist":["dataType","required","disabled","showText"],"rules":[{"required":false}]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 19:56:21.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000179', '33A001', 'H81', '基本信息', 'POLITICIAN', '政治面貌', NULL, NULL, '["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"]', '{"dataClass":"政治面貌","queryType":"api","dataType":"Select","rules":[{"required":false}],"wigetProps":{"options":[],"defaultValue":""},"formName":"政治面貌"}', '{"group":"基本","formId":"xialakuang","icon":"iconxialakuang","formName":"下拉框","isForm":true,"dataType":"Select","wigetProps":{"options":[],"defaultValue":""},"propslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"dataClass":"政治面貌","queryType":"api","rules":[{"required":false}]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 19:56:34.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000208', '33A001', 'H81', '基本信息', 'DUTIES', '职务', NULL, NULL, '["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"]', '{"dataClass":"职务","queryType":"api","dataType":"Select","rules":[{"required":false}],"wigetProps":{"options":[],"defaultValue":""},"formName":"职务"}', '{"group":"基本","formId":"xialakuang","icon":"iconxialakuang","formName":"下拉框","isForm":true,"dataType":"Select","wigetProps":{"options":[],"defaultValue":""},"propslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"dataClass":"职务","queryType":"api","rules":[{"required":false}]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 19:56:42.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000267', '33A001', 'H81', '基本信息', 'GWLB57036', '岗位类别', NULL, NULL, '["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"]', '{"dataClass":"岗位类别","queryType":"api","dataType":"Select","wigetProps":{"options":[{"value":"选项1","label":"选项1"},{"value":"选项2","label":"选项2"},{"value":"选项3","label":"选项3"}],"defaultValue":""},"formName":"岗位类别"}', '{"group":"基本","formId":"xialakuang","icon":"iconxialakuang","formName":"下拉框","isForm":true,"dataType":"Select","queryType":"api","wigetProps":{"options":[{"value":"选项1","label":"选项1"},{"value":"选项2","label":"选项2"},{"value":"选项3","label":"选项3"}],"defaultValue":""},"propslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"dataClass":"岗位类别"}', '1', 'fr_李影', TIMESTAMP '2025-06-12 19:57:02.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000194', '33A001', 'H81', '基本信息', 'COLOR_DEFICIENCY', '颜色视觉障碍', NULL, NULL, '["dataType","required","optionType","styleFlex","disabled","queryType","defaultValue","isDataTypeToPerson"]', '{"dataClass":"H810000-COLOR_DEFICIENCY","queryType":"enum","dataType":"Radio","rules":[{"required":false}],"wigetProps":{"options":[{"label":"正常","value":"0"},{"label":"色弱","value":"1"},{"label":"色盲","value":"2"}]},"formName":"颜色视觉障碍"}', '{"group":"基本","formId":"danxuankuang","icon":"icondanxuan","formName":"单选","isForm":true,"dataType":"Radio","queryType":"enum","wigetProps":{"options":[{"label":"正常","value":"0"},{"label":"色弱","value":"1"},{"label":"色盲","value":"2"}]},"propslist":["dataType","required","optionType","styleFlex","disabled","queryType","defaultValue","isDataTypeToPerson"],"dataClass":"H810000-COLOR_DEFICIENCY","readOnlyEnum":"1","rules":[{"required":false}]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 19:57:15.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000270', '33A001', 'H81', '基本信息', 'YYJB50394', '英语级别/成绩(分)', NULL, NULL, '["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"]', '{"dataClass":"英语级别","queryType":"api","dataType":"Select","rules":[{"required":false}],"wigetProps":{"options":[{"value":"选项1","label":"选项1"},{"value":"选项2","label":"选项2"},{"value":"选项3","label":"选项3"}],"defaultValue":""},"formName":"英语级别/成绩(分)"}', '{"group":"基本","formId":"xialakuang","icon":"iconxialakuang","formName":"下拉框","isForm":true,"dataType":"Select","queryType":"api","wigetProps":{"options":[{"value":"选项1","label":"选项1"},{"value":"选项2","label":"选项2"},{"value":"选项3","label":"选项3"}],"defaultValue":""},"propslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"dataClass":"英语级别","rules":[{"required":false}]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 20:02:01.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000173', '33A001', 'H81', '基本信息', 'NATION', '民族', NULL, NULL, '["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","dataClass","defaultValue","isDataTypeToPerson"]', '{"dataClass":"民族","queryType":"api","dataType":"Select","rules":[{"required":false}],"wigetProps":{"options":[{"value":"选项1","label":"选项1"},{"value":"选项2","label":"选项2"},{"value":"选项3","label":"选项3"}],"defaultValue":""},"formName":"民族"}', '{"group":"基本","formId":"xialakuang","icon":"iconxialakuang","formName":"下拉框","isForm":true,"dataType":"Select","queryType":"api","wigetProps":{"options":[{"value":"选项1","label":"选项1"},{"value":"选项2","label":"选项2"},{"value":"选项3","label":"选项3"}],"defaultValue":""},"propslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","dataClass","defaultValue","isDataTypeToPerson"],"dataClass":"民族","rules":[{"required":false}]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 20:54:43.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000191', '33A001', 'H81', '基本信息', 'ENGLISH_RANK_SCORE', '英语成绩', NULL, NULL, '["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]', '{"dataType":"Input","rules":[{"required":false}],"wigetProps":{"options":[],"defaultValue":""},"formName":"英语级别/成绩（分）","dataClass":"英语级别"}', '{"group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"单行文本","dataType":"Input","isForm":true,"rules":[{"required":false}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 19:57:45.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000201', '33A001', 'H81', '基本信息', 'E_MAIL', '邮箱', NULL, NULL, '["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]', '{"dataType":"Input","rules":[{"required":false}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"formName":"邮箱"}', '{"group":"基本","formId":"danhangwenben","icon":"icondanhangwenben","formName":"单行文本","dataType":"Input","isForm":true,"rules":[{"required":false}],"wigetProps":{"placeholder":"请输入","allowClear":true,"maxLength":200,"disabled":false},"propslist":["dataType","required","allowClear","placeholder","maxLength","prefix","suffix","variant","disabled","showCount"]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 19:55:59.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:43:10.000000', NULL);
INSERT INTO XH_SYS.SYS6_FUNC_FIELD_DICT (FIELD_ID, HOSPITAL_ID, MODULE_ID, FIELD_CLASS, FIELD_CODE, FIELD_NAME, FIELD_DESC, FIELD_SORT, READONLY_JSON, STYLE_JSON, ADDN_JSON, FIELD_STATE, FIRST_RPERSON, FIRST_RTIME, LAST_MPERSON, LAST_MTIME, REMARK) VALUES('H81000272', '33A001', 'H81', '基本信息', 'TZZD19249', '新添字段', NULL, NULL, '["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"]', '{"dataClass":"报告单状态","queryType":"api","dataType":"Select","rules":[{"required":false}],"wigetProps":{"options":[],"defaultValue":""},"formName":"新添字段"}', '{"group":"基本","formId":"xialakuang","icon":"iconxialakuang","formName":"下拉框","isForm":true,"dataType":"Select","queryType":"api","wigetProps":{"options":[],"defaultValue":""},"propslist":["dataType","required","allowClear","prefix","suffix","variant","placeholder","disabled","showSearch","mode","queryType","dataClass","defaultValue","isDataTypeToPerson"],"dataClass":"报告单状态","rules":[{"required":false}]}', '1', 'fr_李影', TIMESTAMP '2025-06-12 21:43:34.000000', 'fr_李影', TIMESTAMP '2025-06-12 21:44:24.000000', NULL);