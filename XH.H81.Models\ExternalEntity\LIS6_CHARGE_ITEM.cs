using System;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using SqlSugar;
using H.Utility.SqlSugarInfra;

namespace XH.H81.Models.ExternalEntity
{
    [Table("LIS6_CHARGE_ITEM")]
    [DBOwner("XH_SYS")]
    public class LIS6_CHARGE_ITEM
	{
		/// <summary>
		/// 
		/// </summary>
		[Key]
        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        //[Column("CHARGE_ITEM_ID")]
		[Required(ErrorMessage = "不允许为空")]

		[StringLength(20, ErrorMessage = "CHARGE_ITEM_ID长度不能超出20字符")]
		//[Unicode(false)]
        public string CHARGE_ITEM_ID { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[Key]
        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        //[Column("HOSPITAL_ID")]
		[Required(ErrorMessage = "不允许为空")]

		[StringLength(20, ErrorMessage = "HOSPITAL_ID长度不能超出20字符")]
		//[Unicode(false)]
        public string HOSPITAL_ID { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("CHARGE_ITEM_CLASS")]
		[StringLength(20, ErrorMessage = "CHARGE_ITEM_CLASS长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CHARGE_ITEM_CLASS { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("FIRST_RPERSON")]
		[StringLength(50, ErrorMessage = "FIRST_RPERSON长度不能超出50字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FIRST_RPERSON { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("INSPECT_GRADE")]
		[StringLength(20, ErrorMessage = "INSPECT_GRADE长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? INSPECT_GRADE { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("CHINESE_NAME_SHORT")]
		[StringLength(50, ErrorMessage = "CHINESE_NAME_SHORT长度不能超出50字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CHINESE_NAME_SHORT { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("CHARGE_ITEM_DCID")]
		[StringLength(20, ErrorMessage = "CHARGE_ITEM_DCID长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CHARGE_ITEM_DCID { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("LAST_MTIME")]
		//[Unicode(false)]
		public DateTime? LAST_MTIME { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("SNOWFLAKES_ID")]
		[StringLength(50, ErrorMessage = "SNOWFLAKES_ID长度不能超出50字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? SNOWFLAKES_ID { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("IF_SERVICE")]
		[StringLength(20, ErrorMessage = "IF_SERVICE长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? IF_SERVICE { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("AREA_SID")]
		[StringLength(200, ErrorMessage = "AREA_SID长度不能超出200字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? AREA_SID { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("COMBO_FLAG")]
		[StringLength(20, ErrorMessage = "COMBO_FLAG长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? COMBO_FLAG { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("CUSTOM_CODE")]
		[StringLength(20, ErrorMessage = "CUSTOM_CODE长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CUSTOM_CODE { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("STANDART_ID")]
		[StringLength(20, ErrorMessage = "STANDART_ID长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? STANDART_ID { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("WORKLOAD")]
		//[Unicode(false)]
        public decimal? WORKLOAD { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("OUT_SERVICE")]
		[StringLength(20, ErrorMessage = "OUT_SERVICE长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? OUT_SERVICE { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("CHARGE")]
		//[Unicode(false)]
		public decimal? CHARGE { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("PATIENT_STYPE")]
		[StringLength(200, ErrorMessage = "PATIENT_STYPE长度不能超出200字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? PATIENT_STYPE { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("CHARGE_ITEM_SORT")]
		[StringLength(20, ErrorMessage = "CHARGE_ITEM_SORT长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CHARGE_ITEM_SORT { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("PATIENT_SDEPT")]
		[StringLength(200, ErrorMessage = "PATIENT_SDEPT长度不能超出200字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? PATIENT_SDEPT { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("CHINESE_NAME")]
		[StringLength(50, ErrorMessage = "CHINESE_NAME长度不能超出50字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CHINESE_NAME { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("STATE_FLAG")]
		[StringLength(10, ErrorMessage = "STATE_FLAG长度不能超出10字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? STATE_FLAG { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("REMARK")]
		[StringLength(200, ErrorMessage = "REMARK长度不能超出200字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? REMARK { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("FIRST_RTIME")]
		//[Unicode(false)]
		public DateTime? FIRST_RTIME { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("LAST_MPERSON")]
		[StringLength(50, ErrorMessage = "LAST_MPERSON长度不能超出50字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? LAST_MPERSON { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("ENGLISH_NAME")]
		[StringLength(50, ErrorMessage = "ENGLISH_NAME长度不能超出50字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? ENGLISH_NAME { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("SPELL_CODE")]
		[StringLength(20, ErrorMessage = "SPELL_CODE长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? SPELL_CODE { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("INSPECT_STYPE")]
		[StringLength(200, ErrorMessage = "INSPECT_STYPE长度不能超出200字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? INSPECT_STYPE { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("HOSPITAL_SERVICE")]
		[StringLength(20, ErrorMessage = "HOSPITAL_SERVICE长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? HOSPITAL_SERVICE { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("ENGLISH_ENAME")]
		[StringLength(50, ErrorMessage = "ENGLISH_ENAME长度不能超出50字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? ENGLISH_ENAME { get; set; }


	}
}
