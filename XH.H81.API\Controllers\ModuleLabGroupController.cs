﻿using H.Utility;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using XH.H81.IServices;
using XH.H81.Models.Dtos;
using XH.LAB.UTILS.Models.Dto;
using XH.LAB.UTILS.Models;

namespace XH.H81.API.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [ApiExplorerSettings(GroupName = "组织架构相关")]
    [Authorize]
    public class ModuleLabGroupController : ControllerBase
    {
        private IModuleLabGroupService _IModuleLabGroupService;
        private readonly IBaseDataServices _IBasedataService;
        public ModuleLabGroupController(IModuleLabGroupService iModuleLabGroupService, IConfiguration configuration,
            IBaseDataServices iBasedataService)
        {
            _IModuleLabGroupService = iModuleLabGroupService;
            _IBasedataService = iBasedataService;
        }

        /// <summary>
        /// 获取ToKen信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetToKenInfo()
        {
            var claim = this.User.ToClaimsDto();
            ToKenDto tokendto = new ToKenDto();
            tokendto.USER_NAME = claim.USER_NAME;
            tokendto.USER_NO = claim.USER_NO;
            tokendto.LAB_ID = claim.LAB_ID;
            tokendto.HOSPITAL_ID = claim.HOSPITAL_ID;
            tokendto.MODULE_ID = claim.MODULE_ID;
            tokendto.OPERATE_PERSON = claim.HIS_NAME;
            tokendto.HOSPITAL_CNAME = claim.HOSPITAL_CNAME;
            tokendto.PERSON_ID = _IModuleLabGroupService.GetPersonId(claim.HOSPITAL_ID, claim.USER_NO);
            return Ok(tokendto.ToResultDto());
        }


        /// <summary>
        /// 根据软件模块获取对应菜单
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult GetMenuInfo()
        {
            var claim = this.User.ToClaimsDto();
            var result = _IModuleLabGroupService.GetMenuInfo(claim.MODULE_ID, claim.HOSPITAL_ID, claim.LAB_ID, claim.USER_NO);
            return Ok(result.ToResultDto());
        }


        /// <summary>
        /// 获取人员对应科室信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetLisInspectionLabInfo()
        {
            var claim = this.User.ToClaimsDto();
            var result = _IModuleLabGroupService.GetLisInspectionLabInfo(claim.USER_NO, claim.HOSPITAL_ID);
            return Ok(result.ToResultDto());
        }

        /// <summary>
        /// 返回生安登录选择机构、科室、备案实验室列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetLogonHospitalLabList()
        {
            var claim = this.User.ToClaimsDto();
            var result = _IModuleLabGroupService.GetLogonHospitalLabList(claim.USER_NO, claim.HOSPITAL_ID);
            return Ok(result.ToResultDto());
        }

        /// <summary>
        /// 获取人员信息树
        /// </summary>
        /// <param name="obj">{"LAB_ID":"","PGROUP_ID":"","PERSON_NAME":""}</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult GetPersonInfoTree(dynamic obj)
        {
            var claim = this.User.ToClaimsDto();
            dynamic json = JsonConvert.DeserializeObject(Convert.ToString(obj));
            string hospital_id = claim.HOSPITAL_ID;
            string lab_id = json["LAB_ID"].ToString();
            string pgroup_id = json["PGROUP_ID"].ToString();
            string person_name = json["PERSON_NAME"].ToString();
            string user_no = claim.USER_NO;
            var data = _IModuleLabGroupService.GetPersonInfoTree(hospital_id, lab_id, pgroup_id, person_name, user_no);
            return Ok(new ResultDto()
            {
                data = data
            });
        }

        ///// <summary>
        ///// 根据科室和人员获取对应分组信息
        ///// </summary>
        ///// <param name="obj">{"LAB_ID":""}</param>
        ///// <returns></returns>
        //[HttpPost]
        //public IActionResult GetPgroupInfoByLabIdUser(dynamic obj)
        //{
        //    var claim = this.User.ToClaimsDto();
        //    JObject json = JsonConvert.DeserializeObject(Convert.ToString(obj));
        //    string lab_id = json["LAB_ID"].ToString();
        //    string pgroup_id = json["PGROUP_ID"].ToString();
        //    string person_name = json.ContainsKey("PERSON_NAME") ? json["PERSON_NAME"].ToString() : "";
        //    string page_type = json.ContainsKey("PAGE_TYPE") ? json["PAGE_TYPE"].ToString() : "";
        //    string hospital_id = claim.HOSPITAL_ID;
        //    var data = _IModuleLabGroupService.GetPgroupInfoByLabIdUser(hospital_id, lab_id, pgroup_id, person_name, claim.USER_NO, page_type);
        //    return Ok(new ResultDto()
        //    {
        //        data = data
        //    });
        //}


        /// <summary>
        /// 获取检验分组下拉信息
        /// </summary>
        /// <param name="pgroup_id"></param>
        /// <param name="lab_id"></param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetGroupDropDownInfo(string pgroup_id, string lab_id)
        {
            var claim = this.User.ToClaimsDto();
            var result = _IModuleLabGroupService.GetGroupDropDownInfo(claim.HOSPITAL_ID, pgroup_id, lab_id);
            return Ok(result.ToResultDto());
        }
        ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
        /// <summary>
        /// 获取操作人
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetOperatePersonInfo()
        {
            var claim = this.User.ToClaimsDto();
            var OperatePerson = claim.USER_NAME;
            return Ok(OperatePerson.ToResultDto());
        }

        /// <summary>
        /// 获取科室对应人员下拉
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetUserDropDowByLabId()
        {
            var claim = this.User.ToClaimsDto();
            var result = _IBasedataService.GetUserDropDowByLabId(claim.HOSPITAL_ID, claim.LAB_ID);
            return Ok(result.ToResultDto());
        }

        /// <summary>
        /// 获取人员下拉信息
        /// </summary>
        /// <param name="obj">{"LAB_ID":"L001"}</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult GetUserDropDownInfo(Dictionary<string,string> obj)
        {
            var claim = this.User.ToClaimsDto();
            string lab_id = obj.ContainsKey("LAB_ID") ? obj["LAB_ID"] : claim.LAB_ID;
            string hospital_id = obj.ContainsKey("HOSPITAL_ID") ? obj["HOSPITAL_ID"] : claim.HOSPITAL_ID;
            var result = _IModuleLabGroupService.GetUserDropDownInfo(hospital_id, lab_id);
            return Ok(result.ToResultDto());
        }


        /// <summary>
        /// 获取固定基础数据下拉
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult GetBaseDataInfo()
        {
            var claim = this.User.ToClaimsDto();
            string hospital_id = claim.HOSPITAL_ID;
            var data = _IModuleLabGroupService.GetBaseDataInfo(hospital_id);
            return Ok(new ResultDto()
            {
                data = data
            });
        }

        /// <summary>
        /// 获取职称下拉数据下拉
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult GetLevelTitle()
        {
            var claim = this.User.ToClaimsDto();
            var data = _IModuleLabGroupService.GetLevelTitle();
            return Ok(new ResultDto()
            {
                data = data
            });
        }
        [HttpGet]
        public IActionResult GetTestOrganizationTree(string? hospital_id)
        {
            var claim = this.User.ToClaimsDto();
            var data = _IModuleLabGroupService.GetTestOrganizationTree(hospital_id);
            return Ok(new ResultDto()
            {
                data = data
            });
        }

        /// <summary>
        /// 获取生安组织ID
        /// </summary>
        /// <param name="hospital_id"></param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetSmblOrg(string? hospital_id)
        {
            var claim = this.User.ToClaimsDto();
            var data = _IModuleLabGroupService.GetSmblOrg(hospital_id);
            return Ok(data.ToResultDto());
        }

        /// <summary>
        /// 获取科室/管理专业组/检验专业组下拉
        /// </summary>
        /// <param name="hospital_id"></param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetLabMgroupPgroupDropDown(string hospital_id, string? lab_id, string? area_id, string? pgroup_id)
        {
            var claim = this.User.ToClaimsDto();
            var orgParm = new OrgUserParams { user_id = claim.USER_NO, hospital_id = hospital_id, lab_id = lab_id, area_id = area_id, pgroup_id = pgroup_id };
            var data = _IModuleLabGroupService.GetLabMgroupPgroupTree(orgParm, "H81", false);
            return Ok(data.ToResultDto());
        }
        /// <summary>
        /// 获取人员组合信息
        /// </summary>
        /// <param name="comName">组合名称</param>
        /// <param name="userName">人员名称</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetUserComInfo(string? comName,string? userName)
        {
            var claim = this.User.ToClaimsDto();
            var data = _IModuleLabGroupService.GetUserComInfo(comName, userName);
            return Ok(data.ToResultDto());
        }

        /// <summary>
        /// 获取专业组列表
        /// </summary>
        /// <param name="hospitalId"></param>
        /// <param name="labId"></param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetPgroupList(string hospitalId, string? labId)
        {
            var data = _IModuleLabGroupService.GetPgroupList(hospitalId, labId);
            return Ok(data.ToResultDto());
        }

        /// <summary>
        /// 获取备案实验室列表
        /// </summary>
        /// <param name="hospitalId"></param>
        /// <param name="labId"></param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetSmblLabList(string hospitalId, string? labId)
        {
            var data = _IModuleLabGroupService.GetSmblLabList(hospitalId, labId);
            return Ok(data.ToResultDto());
        }
    }
}
