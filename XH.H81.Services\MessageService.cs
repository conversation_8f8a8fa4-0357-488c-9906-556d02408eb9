﻿using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using RestSharp;
using Serilog;
using System.Diagnostics;
using System.Security.Claims;
using XH.H81.IServices.Message;
using XH.H81.Models.Message;
using XH.H81.Models.SugarDbContext;
using XH.LAB.UTILS.Models;

namespace XH.H81.Services
{
    public class MessageService : IMessageService
    {

        /// <summary>
        /// 通用消息发送接口
        /// </summary>
        const string sendMessageApi = "/api/CommunalAlarm/InsertMsgInfo";

        private readonly IHttpContextAccessor _httpContext;
        private readonly IConfiguration _configuration;

        private readonly ISqlSugarUow<SugarDbContext_Master> _dbContext;
        public RestClient _h05Client { get; set; }
        public MessageService(IConfiguration configuration, IHttpContextAccessor httpContext, ISqlSugarUow<SugarDbContext_Master> dbContext)
        {
            _configuration = configuration;

            var H05Address = _configuration["H05"];
            if (H05Address.IsNotNullOrEmpty())
            {
                H05Address = H05Address.Replace("/clinical-alarm-board?hospital_id={HOSPITAL_ID}&client_mac={CLIENT_MAC}", "");

                _h05Client = new RestClient(new RestClientOptions
                {
                    RemoteCertificateValidationCallback = (Sender, certificate, chain, SslPolicyErrors) => true,
                    BaseUrl = new Uri(H05Address),
                    ThrowOnAnyError = true
                });
            }
            _httpContext = httpContext;
            _dbContext = dbContext;
        }

        public void SendReminderMessage(string dataId, ClaimsDto sender, List<ReceiverInfo> receivers)
        {

            var requestBody = new List<BaseMessage>();

            foreach (var receiver in receivers)
            {
                requestBody.Add(NewMessage.CreatNewMessage(dataId, sender.USER_NO, $"{sender.LOGID}_{sender.USER_NAME}", receiver.HospitalId, receiver.LabId, receiver.AreaId, receiver.ModeuleId, receiver.Title, receiver.Content, receiver.MsgType, receiver.receiveUserNo, receiver.receiveName, receiver.Delay, receiver.OverTime));
            }
            var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();
            try
            {
                SendMessageClient(token, requestBody);
            }
            catch (Exception e)
            {
                throw e;
            }

        }

        public void SendTodoListMessage(ClaimsDto sender, List<ReceiverInfo> receivers, double validityPeriodDays)
        {
            var requestBody = new List<BaseMessage>();

            foreach (var receiver in receivers)
            {
                requestBody.Add(TodoMeassge.CreatTodoMeassge(sender.USER_NO, $"{sender.LOGID}_{sender.USER_NAME}", receiver.HospitalId, receiver.LabId, receiver.AreaId, receiver.ModeuleId, receiver.Title, receiver.Content, receiver.MsgType, receiver.receiveUserNo, receiver.receiveName, validityPeriodDays, receiver.Delay, receiver.OverTime));
            }
            var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();
            try
            {
                SendMessageClient(token, requestBody);
            }
            catch (Exception e)
            {
                throw e;
            }
        }

        /// <summary>
        /// 发送短信客户端
        /// </summary>
        /// <param name="token"></param>
        /// <param name="requestBody"></param>
        /// <exception cref="Exception"></exception>
        private void SendMessageClient(string token, List<BaseMessage> requestBody)
        {
            Log.Information($"接口：{sendMessageApi} \n 参数：{JsonConvert.SerializeObject(requestBody)}");
            Stopwatch sw = new Stopwatch();
            sw.Start();
            RestRequest request = new RestRequest(sendMessageApi);
            request.AddHeader("Authorization", token);
            request.AddJsonBody(requestBody);
            try
            {
                var response = _h05Client.ExecutePost<ResultDto>(request);
                sw.Stop();
                Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用H05模块[{sendMessageApi}],耗时:{sw.ElapsedMilliseconds}ms");
                if (response.IsSuccessful && response.Data.success)
                {
                    return;
                }
                else
                {
                    Log.Error($"调用H05模块[{sendMessageApi}]发生错误:{response.ErrorException}");
                    throw new Exception($"调用H05模块[{sendMessageApi}]发生错误:{response.ErrorException}", response.ErrorException);
                }
            }
            catch (Exception ex)
            {
                Log.Error($"调用H05模块[{sendMessageApi}]发生错误:{ex}");
                throw new Exception("发送消息失败!");
            }
        }

        /// <summary>
        /// 获取已发送消息
        /// </summary>
        /// <param name="token"></param>
        /// <param name="classId"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        private ResultDto GetCommunalInfoByParam(string token,string classId)
        {

            Stopwatch sw = new Stopwatch();
            sw.Start();
            string url = $"/api/CommunalAlarm/GetCommunalInfoByParam?msgClassId={classId}";

            RestRequest request = new RestRequest(url);
            request.AddHeader("Authorization", token);
            var response = _h05Client.ExecuteGet<ResultDto>(request);
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用H05模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
            if (response.ErrorException != null)
            {
                Log.Error($"调用H05模块[{url}]发生错误:{response.ErrorException}");
                throw new Exception($"调用H07模块[{url}]发生错误:{response.ErrorException}", response.ErrorException);
            }
            else
            {
                if (response.Data.success)
                {
                    return response.Data;
                }
                else
                {
                    Log.Error($"调用H07模块[{url}]请求完成,但是返回了错误:" + response.Data.msg);
                    throw new Exception("撤销发送消息失败!");
                }
            }
        }

        /// <summary>
        /// 撤销消息
        /// </summary>
        /// <param name="token"></param>
        /// <param name="msgId"></param>
        private void CancelMsgInfo(string token,string msgId)
        {
            Stopwatch sw = new Stopwatch();
            sw.Start();
            string url = $"/api/CommunalAlarm/CancelMsgInfo?MSG_ID={msgId}";

            RestRequest request = new RestRequest(url);
            request.AddHeader("Authorization", token);
            var response = _h05Client.ExecutePost<ResultDto>(request);
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用H05模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
            if (response.ErrorException != null)
            {
                Log.Error($"调用H05模块[{url}]发生错误:{response.ErrorException}");
                throw new Exception($"调用H07模块[{url}]发生错误:{response.ErrorException}", response.ErrorException);
            }
            else
            {
                if (response.Data.success)
                {
                    return;
                }
                else
                {
                    Log.Error($"调用H07模块[{url}]请求完成,但是返回了错误:" + response.Data.msg);
                    throw new Exception("撤销发送消息失败!");
                }
            }

        }
        /// <summary>
        /// 发送提交记录消息
        /// </summary>
        /// <param name="sender">提交记录</param>
        /// <param name="recordId">记录id</param>
        /// <param name="checkUser">审核人id</param>
        /// <param name="className">分类名称</param>
        /// <param name="recordCount">记录条数</param>
        public void SendSubmitRecordReminderMessage(ClaimsDto sender,string recordId, string checkUser,string className,string recordCount)
        {
            const string title = "人事记录审核通知";
            const string msgType = "MO804";

            var userInfo = _dbContext.Db.Queryable<SYS6_USER>().Where(x => checkUser==x.USER_NO).First();
            if (userInfo is null)
            {
                return;
            }
            var dept = userInfo.DEPT_CODE;
            var organizationaslInfo = _dbContext.Db.Queryable<SYS6_INSPECTION_PGROUP>().Where(x => dept==x.PGROUP_ID).ToList();
            var receives = new List<ReceiverInfo>();
  
                if (userInfo.DEPT_CODE.IsNullOrEmpty())
                {
                    receives.Add(new ReceiverInfo
                    {
                        HospitalId = userInfo.HOSPITAL_ID,
                        AreaId = "A000",
                        LabId = $"{userInfo.LAB_ID}",
                        ModeuleId = "H81",
                        receiveName = $"{userInfo.LOGID}_{userInfo.USERNAME}",
                        receiveUserNo = $"{userInfo.USER_NO}",
                        Title = title,
                        Content = $"{userInfo.LOGID}_{userInfo.USERNAME}，您有{recordCount}条{className}需要审核",
                        MsgType = msgType,
                        Delay = 0 ,
                        OverTime = 0,
                    });
                }
                else
                {
                    var organizationalInfo = organizationaslInfo.Where(x => x.PGROUP_ID == userInfo.DEPT_CODE).First();
                    receives.Add(new ReceiverInfo
                    {
                        HospitalId = $"{userInfo.HOSPITAL_ID}",
                        AreaId = organizationalInfo.AREA_ID,
                        LabId = organizationalInfo.LAB_ID,
                        ModeuleId = "H81",
                        receiveName = $"{userInfo.LOGID}_{userInfo.USERNAME}",
                        receiveUserNo = $"{userInfo.USER_NO}",
                        Title = title,
                        Content = $"{userInfo.LOGID}_{userInfo.USERNAME}，您有{recordCount}条{className}需要审核",
                        MsgType = msgType,
                        Delay = 0 ,
                        OverTime = 0,
                    });
                }

            
            SendReminderMessage(recordId, sender, receives);
        }

        public void SendCancelRecordReminderMessage(ClaimsDto sender, string recordId, string checkUser, string className, string recordCount)
        {

            const string title = "撤销人事记录提醒通知";
            const string msgType = "MO804";

            var userInfo = _dbContext.Db.Queryable<SYS6_USER>().Where(x => checkUser == x.USER_NO).First();
            if (userInfo is null)
            {
                return;
            }
            var dept = userInfo.DEPT_CODE;
            var organizationaslInfo = _dbContext.Db.Queryable<SYS6_INSPECTION_PGROUP>().Where(x => dept == x.PGROUP_ID).ToList();
            var receives = new List<ReceiverInfo>();

            if (userInfo.DEPT_CODE.IsNullOrEmpty())
            {
                receives.Add(new ReceiverInfo
                {
                    HospitalId = userInfo.HOSPITAL_ID,
                    AreaId = "A000",
                    LabId = $"{userInfo.LAB_ID}",
                    ModeuleId = "H81",
                    receiveName = $"{userInfo.LOGID}_{userInfo.USERNAME}",
                    receiveUserNo = $"{userInfo.USER_NO}",
                    Title = title,
                    Content = $"{userInfo.LOGID}_{userInfo.USERNAME}，{recordCount}条{className}已撤销",
                    MsgType = msgType,
                    Delay = 0,
                    OverTime = 0,
                });
            }
            else
            {
                var organizationalInfo = organizationaslInfo.Where(x => x.PGROUP_ID == userInfo.DEPT_CODE).First();
                receives.Add(new ReceiverInfo
                {
                    HospitalId = $"{userInfo.HOSPITAL_ID}",
                    AreaId = organizationalInfo.AREA_ID,
                    LabId = organizationalInfo.LAB_ID,
                    ModeuleId = "H81",
                    receiveName = $"{userInfo.LOGID}_{userInfo.USERNAME}",
                    receiveUserNo = $"{userInfo.USER_NO}",
                    Title = title,
                    Content = $"{userInfo.LOGID}_{userInfo.USERNAME}，{recordCount}条{className}已撤销",
                    MsgType = msgType,
                    Delay = 0,
                    OverTime = 0,
                });
            }

            SendReminderMessage(recordId, sender, receives);
            //查询当前人发送考试消息 进行撤销
            var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();
            ResultDto senddMsgDto = GetCommunalInfoByParam(token,"M13");
            List<SendMessage> listMsg = JsonConvert.DeserializeObject<List<SendMessage>>(senddMsgDto.data.ToString());
            List<string> listMsgId = listMsg.Where(w => w.MSG_STATE == "0" && w.MSG_CORRID == recordId).Select(w => w.MSG_ID).ToList();
            foreach (var item in listMsgId)
            {
                CancelMsgInfo(token, item);
            }
        }

    }
}
