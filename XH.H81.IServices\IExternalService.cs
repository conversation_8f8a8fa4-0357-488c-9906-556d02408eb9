﻿using H.Utility;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XH.H81.Models.Dtos.External;

namespace XH.H81.IServices
{
    public interface IExternalService
    {
        BloodCollectPersonOutput RegisterBloodCollectSupportPerson(string hospitalId, string userName, string hisId);

        /// <summary>
        /// 新增或保存采血外援人员
        /// </summary>
        /// <param name="parm"></param>
        /// <returns></returns>
        public BloodCollectPersonOutput SaveBloodCollectPerson(Dictionary<string, object> parm);

        /// <summary>
        /// 获取采血外援人员列表
        /// </summary>
        /// <param name="searchKey"></param>
        /// <param name="personId"></param>
        /// <returns></returns>
        public List<BloodCollectPersonOutput> GetBloodCollectPersonList(string searchKey, string personId);

        /// <summary>
        /// 删除采血外援人员证件文件
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        public ResultDto DeleteBloodCollectPersonFile(BloodCollectUploadFileOutput file);

        /// <summary>
        /// 上传采血外援人员证件文件
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        public BloodCollectUploadFileOutput UploadBloodCollectPersonFile([FromForm] BloodCollectUploadFileInput file);
    }
}
