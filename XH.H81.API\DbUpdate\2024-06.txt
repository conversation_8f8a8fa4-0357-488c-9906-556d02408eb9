﻿--2024-06-07  PMS_PERSON_INFO新增 RECORD_DATA 
ALTER TABLE XH_OA.PMS_PERSON_INFO ADD RECORD_DATA   CLOB;
COMMENT ON COLUMN XH_OA.PMS_PERSON_INFO.RECORD_DATA IS '扩展字段';

-- 2024-06-13 插入技术证书类型
INSERT INTO XH_OA.OA_BASE_DATA (DATA_ID,HOSPITAL_ID,MODULE_ID,FATHER_ID,CLASS_ID,DATA_SORT,DATA_NAME,DATA_SNAME,DATA_ENAME,STANDART_ID,CUSTOM_CODE,SPELL_CODE,STATE_FLAG,FIRST_RPERSON,FIRST_RTIME,LAST_MPERSON,LAST_MTIME,REMARK,ADDN_CONFIG_JSON) VALUES ('XBD00000011','33A001','H81',NULL,'人事技能证书类型','1','生物安全证',NULL,NULL,NULL,NULL,NULL,'1','xh_管理员',TIMESTAMP '2024-06-13 15:57:48.000000','xh_管理员',TIMESTAMP '2024-06-13 15:57:48.000000',NULL,NULL);
INSERT INTO XH_OA.OA_BASE_DATA (DATA_ID,HOSPITAL_ID,MODULE_ID,FATHER_ID,CLASS_ID,DATA_SORT,DATA_NAME,DATA_SNAME,DATA_ENAME,STANDART_ID,CUSTOM_CODE,SPELL_CODE,STATE_FLAG,FIRST_RPERSON,FIRST_RTIME,LAST_MPERSON,LAST_MTIME,REMARK,ADDN_CONFIG_JSON) VALUES ('XBD00000012','33A001','H81',NULL,'人事技能证书类型','2','艾滋病诊断',NULL,NULL,NULL,NULL,NULL,'1','xh_管理员',TIMESTAMP '2024-06-13 15:57:48.000000','xh_管理员',TIMESTAMP '2024-06-13 15:57:48.000000',NULL,NULL);
INSERT INTO XH_OA.OA_BASE_DATA (DATA_ID,HOSPITAL_ID,MODULE_ID,FATHER_ID,CLASS_ID,DATA_SORT,DATA_NAME,DATA_SNAME,DATA_ENAME,STANDART_ID,CUSTOM_CODE,SPELL_CODE,STATE_FLAG,FIRST_RPERSON,FIRST_RTIME,LAST_MPERSON,LAST_MTIME,REMARK,ADDN_CONFIG_JSON) VALUES ('XBD00000013','33A001','H81',NULL,'人事技能证书类型','3','PCR证',NULL,NULL,NULL,NULL,NULL,'1','xh_管理员',TIMESTAMP '2024-06-13 15:57:48.000000','xh_管理员',TIMESTAMP '2024-06-13 15:57:48.000000',NULL,NULL);
INSERT INTO XH_OA.OA_BASE_DATA (DATA_ID,HOSPITAL_ID,MODULE_ID,FATHER_ID,CLASS_ID,DATA_SORT,DATA_NAME,DATA_SNAME,DATA_ENAME,STANDART_ID,CUSTOM_CODE,SPELL_CODE,STATE_FLAG,FIRST_RPERSON,FIRST_RTIME,LAST_MPERSON,LAST_MTIME,REMARK,ADDN_CONFIG_JSON) VALUES ('XBD00000014','33A001','H81',NULL,'人事技能证书类型','4','高压锅',NULL,NULL,NULL,NULL,NULL,'1','xh_管理员',TIMESTAMP '2024-06-13 15:57:48.000000','xh_管理员',TIMESTAMP '2024-06-13 15:57:48.000000',NULL,NULL);
INSERT INTO XH_OA.OA_BASE_DATA (DATA_ID,HOSPITAL_ID,MODULE_ID,FATHER_ID,CLASS_ID,DATA_SORT,DATA_NAME,DATA_SNAME,DATA_ENAME,STANDART_ID,CUSTOM_CODE,SPELL_CODE,STATE_FLAG,FIRST_RPERSON,FIRST_RTIME,LAST_MPERSON,LAST_MTIME,REMARK,ADDN_CONFIG_JSON) VALUES ('XBD00000015','33A001','H81',NULL,'人事技能证书类型','5','内审员证书',NULL,NULL,NULL,NULL,NULL,'1','xh_管理员',TIMESTAMP '2024-06-13 15:57:48.000000','xh_管理员',TIMESTAMP '2024-06-13 15:57:48.000000',NULL,NULL);
INSERT INTO XH_OA.OA_BASE_DATA (DATA_ID,HOSPITAL_ID,MODULE_ID,FATHER_ID,CLASS_ID,DATA_SORT,DATA_NAME,DATA_SNAME,DATA_ENAME,STANDART_ID,CUSTOM_CODE,SPELL_CODE,STATE_FLAG,FIRST_RPERSON,FIRST_RTIME,LAST_MPERSON,LAST_MTIME,REMARK,ADDN_CONFIG_JSON) VALUES ('XBD00000016','33A001','H81',NULL,'人事技能证书类型','6','执业证书',NULL,NULL,NULL,NULL,NULL,'1','xh_管理员',TIMESTAMP '2024-06-13 15:57:48.000000','xh_管理员',TIMESTAMP '2024-06-13 15:57:48.000000',NULL,NULL);
INSERT INTO XH_OA.OA_BASE_DATA (DATA_ID,HOSPITAL_ID,MODULE_ID,FATHER_ID,CLASS_ID,DATA_SORT,DATA_NAME,DATA_SNAME,DATA_ENAME,STANDART_ID,CUSTOM_CODE,SPELL_CODE,STATE_FLAG,FIRST_RPERSON,FIRST_RTIME,LAST_MPERSON,LAST_MTIME,REMARK,ADDN_CONFIG_JSON) VALUES ('XBD00000017','33A001','H81',NULL,'人事技能证书类型','7','结核病防治',NULL,NULL,NULL,NULL,NULL,'1','xh_管理员',TIMESTAMP '2024-06-13 15:57:48.000000','xh_管理员',TIMESTAMP '2024-06-13 15:57:48.000000',NULL,NULL);
INSERT INTO XH_OA.OA_BASE_DATA (DATA_ID,HOSPITAL_ID,MODULE_ID,FATHER_ID,CLASS_ID,DATA_SORT,DATA_NAME,DATA_SNAME,DATA_ENAME,STANDART_ID,CUSTOM_CODE,SPELL_CODE,STATE_FLAG,FIRST_RPERSON,FIRST_RTIME,LAST_MPERSON,LAST_MTIME,REMARK,ADDN_CONFIG_JSON) VALUES ('XBD00000018','33A001','H81',NULL,'人事技能证书类型','8','性病上岗证',NULL,NULL,NULL,NULL,NULL,'1','xh_管理员',TIMESTAMP '2024-06-13 15:57:48.000000','xh_管理员',TIMESTAMP '2024-06-13 15:57:48.000000',NULL,NULL);


--2024-06-27 增加PMS_THESIS_LIST表THESIS_AFFIX字段长度
ALTER TABLE XH_OA.PMS_THESIS_LIST MODIFY THESIS_AFFIX VARCHAR2(4000);

--2024-06-27 增加PMS_EDUCATION_LIST表EDUCATION_AFFIX字段长度
ALTER TABLE XH_OA.PMS_EDUCATION_LIST MODIFY EDUCATION_AFFIX VARCHAR2(4000);

--2024-06-27 增加PMS_EXCHANGE_LIST表EXCHANGE_AFFIX字段长度
ALTER TABLE XH_OA.PMS_EXCHANGE_LIST MODIFY EXCHANGE_AFFIX VARCHAR2(4000);

--2024-06-27 增加PMS_EXPATRIATE_LIST表EXPATRIATE_AFFIX字段长度
ALTER TABLE XH_OA.PMS_EXPATRIATE_LIST MODIFY EXPATRIATE_AFFIX VARCHAR2(4000);

--2024-06-27 增加PMS_INTELLECTUAL_LIST表INTELLECTUAL_AFFIX字段长度
ALTER TABLE XH_OA.PMS_INTELLECTUAL_LIST MODIFY INTELLECTUAL_AFFIX VARCHAR2(4000);

--2024-06-27 增加PMS_PROFESSIONAL_LIST表PROFESSIONAL_AFFIX字段长度
ALTER TABLE XH_OA.PMS_PROFESSIONAL_LIST MODIFY PROFESSIONAL_AFFIX VARCHAR2(4000);

--2024-06-27 增加PMS_RESEARCH_LIST表RESEARCH_AFFIX字段长度
ALTER TABLE XH_OA.PMS_RESEARCH_LIST MODIFY RESEARCH_AFFIX VARCHAR2(4000);

--2024-06-27 增加PMS_RESUME_LIST表RESUME_AFFIX字段长度
ALTER TABLE XH_OA.PMS_RESUME_LIST MODIFY RESUME_AFFIX VARCHAR2(4000);

--2024-06-27 增加PMS_REWARD_LIST表REWARD_AFFIX字段长度
ALTER TABLE XH_OA.PMS_REWARD_LIST MODIFY REWARD_AFFIX VARCHAR2(4000);

--2024-06-27 增加PMS_SKILL_CERTIFICATE_LIST表CERTIFICATE_AFFIX字段长度
ALTER TABLE XH_OA.PMS_SKILL_CERTIFICATE_LIST MODIFY CERTIFICATE_AFFIX VARCHAR2(4000);

--2024-06-27 增加PMS_SOCIAL_OFFICE_LIST表SOFFICE_AFFIX字段长度
ALTER TABLE XH_OA.PMS_SOCIAL_OFFICE_LIST MODIFY SOFFICE_AFFIX VARCHAR2(4000);

--2024-06-27 增加PMS_STUDY_LIST表STUDY_AFFIX字段长度
ALTER TABLE XH_OA.PMS_STUDY_LIST MODIFY STUDY_AFFIX VARCHAR2(4000);

--2024-06-27 增加PMS_TEACH_LIST表TEACH_AFFIX字段长度
ALTER TABLE XH_OA.PMS_TEACH_LIST MODIFY TEACH_AFFIX VARCHAR2(4000);

--2024-06-27 增加PMS_TRAIN_LIST表TRAIN_AFFIX字段长度
ALTER TABLE XH_OA.PMS_TRAIN_LIST MODIFY TRAIN_AFFIX VARCHAR2(4000);

--2024-06-27 增加PMS_ADDN_RECORD表RECORD_AFFIX字段长度
ALTER TABLE XH_OA.PMS_ADDN_RECORD MODIFY RECORD_AFFIX VARCHAR2(4000);