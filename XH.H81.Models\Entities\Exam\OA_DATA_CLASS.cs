﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Entities.Exam
{
    /// <summary>
    /// 管理分类字典表
    /// </summary>
    [DBOwner("XH_OA")]
    public class OA_DATA_CLASS
    {
        /// <summary>
        /// 分类ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Key]
        public string? DATA_CLASS_ID { get; set; }
        /// <summary>
        /// 医疗机构ID
        /// </summary>
        public string? HOSPITAL_ID { get; set; }
        /// <summary>
        /// 类型
        /// </summary>
        public string? DATA_CLASS_TYPE { get; set; }
        /// <summary>
        /// 中文名
        /// </summary>
        public string? DATA_CLASS_CNAME { get; set; }
        /// <summary>
        /// 英文名
        /// </summary>
        public string? DATA_CLASS_ENAME { get; set; }
        /// <summary>
        /// 排序号
        /// </summary>
        public string? DATA_CLASS_SORT { get; set; }
        /// <summary>
        /// 状态[0禁用1在用2删除]
        /// </summary>
        public string? DATA_CLASS_STATE { get; set; }
        /// <summary>
        /// 首次登记人
        /// </summary>
        public string? FIRST_RPERSON { get; set; }
        /// <summary>
        /// 首次登记时间
        /// </summary>
        public DateTime? FIRST_RTIME { get; set; }
        /// <summary>
        /// 最后修改人员
        /// </summary>
        public string? LAST_MPERSON { get; set; }
        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime? LAST_MTIME { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string? REMARK { get; set; }
    }
}
