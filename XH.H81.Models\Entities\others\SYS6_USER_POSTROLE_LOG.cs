﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Entities.others
{
    [DBOwner("XH_SYS")]
    public class SYS6_USER_POSTROLE_LOG
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string UPOST_LOG_ID { get; set; }

        public string HOSPITAL_ID { get; set; }

        public string USER_NO { get; set; }

        public string POSTROLE_ID { get; set; }

        public DateTime? UPOST_START_DATE { get; set; }

        public DateTime? UPOST_END_DATE { get; set; }

        public string CHANGE_PERSON { get; set; }

        public DateTime? CHANGE_TIME { get; set; }

        public string OPERATE_TYPE { get; set; }

        public string FIRST_RPERSON { get; set; }

        public DateTime? FIRST_RTIME { get; set; }

        public string LAST_MPERSON { get; set; }

        public DateTime? LAST_MTIME { get; set; }

        public string REMARK { get; set; }
    }

}
