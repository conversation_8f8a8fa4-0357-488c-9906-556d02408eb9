//using System;
//using Microsoft.EntityFrameworkCore;
//using System.ComponentModel.DataAnnotations;
//using System.ComponentModel.DataAnnotations.Schema;
//using SqlSugar;
//using H.Utility.SqlSugarInfra;

//namespace XH.H81.Models.ExternalEntity
//{
//    /// <summary>
//    /// 用户表 （通过DEPT_CODE限定所属专业组）
//    /// </summary>
//    [DBOwner("XH_SYS")]
//    public class SYS6_USER
//    {
//        /// <summary>
//        /// 
//        /// </summary>
//        [Key]
//        [SugarColumn(IsPrimaryKey = true)]
//        public string USER_NO { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>
//        public string? LAB_ID { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>
//        //[Column("FIRST_RPERSON")]

//        public string? FIRST_RPERSON { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>
//        //[Column("QQ")]

//        public string? QQ { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>

//        public string? IF_BONDING_KEY { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>
//        //[Column("USERNAME")]

//        public string USERNAME { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>

//        public DateTime? LAST_MTIME { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>

//        public string? USER_KEY { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>

//        public string? E_MAIL { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>

//        public string? UPLOAD_STATE { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>

//        public string? DEPT_CODE { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>

//        public string? CHECK_PWDD { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>

//        public string? CA_VERIFY { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>
//        //[Column("CA_KEY")]
//        [StringLength(100, ErrorMessage = "CA_KEY长度不能超出100字符")]
//        //[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? CA_KEY { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>
//        //[Column("IF_SIGN_VERIFY")]
//        [StringLength(20, ErrorMessage = "IF_SIGN_VERIFY长度不能超出20字符")]
//        //[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? IF_SIGN_VERIFY { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>
//        //[Column("USER_FILE")]
//        [StringLength(200, ErrorMessage = "USER_FILE长度不能超出200字符")]
//        //[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? USER_FILE { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>
//        //[Column("PWD_EDIT_DATE")]
//        //[Unicode(false)]
//        public DateTime? PWD_EDIT_DATE { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>
//        //[Column("PWD_BS")]
//        [StringLength(100, ErrorMessage = "PWD_BS长度不能超出100字符")]
//        //[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? PWD_BS { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>
//        //[Column("PWD_ERROR_N")]
//        //[Unicode(false)]
//        public decimal? PWD_ERROR_N { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>
//        //[Column("USER_CLASS")]
//        [StringLength(20, ErrorMessage = "USER_CLASS长度不能超出20字符")]
//        //[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? USER_CLASS { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>
//        //[Column("LOCK_STATE")]
//        [StringLength(10, ErrorMessage = "LOCK_STATE长度不能超出10字符")]
//        //[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? LOCK_STATE { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>

//        public string? USER_TYPE { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>
//        //[Column("JOB_STATE")]
//        [StringLength(10, ErrorMessage = "JOB_STATE长度不能超出10字符")]
//        //[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? JOB_STATE { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>
//        //[Column("ACCOUNT_TYPE")]
//        [StringLength(20, ErrorMessage = "ACCOUNT_TYPE长度不能超出20字符")]
//        //[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? ACCOUNT_TYPE { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>
//        //[Column("SIGNATURE_PIC")]
//        [StringLength(200, ErrorMessage = "SIGNATURE_PIC长度不能超出200字符")]
//        //[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? SIGNATURE_PIC { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>
//        //[Column("PHONE_NO")]
//        [StringLength(50, ErrorMessage = "PHONE_NO长度不能超出50字符")]
//        //[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? PHONE_NO { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>
//        //[Column("LOCK_TIME")]
//        //[Unicode(false)]
//        public DateTime? LOCK_TIME { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>
//        //[Column("LOGID")]
//        [Required(ErrorMessage = "不允许为空")]

//        [StringLength(20, ErrorMessage = "LOGID长度不能超出20字符")]
//        //[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string LOGID { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>

//        public string? STATE_FLAG { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>
//        //[Column("REMARK")]
//        [StringLength(100, ErrorMessage = "REMARK长度不能超出100字符")]
//        //[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? REMARK { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>
//        //[Column("HIS_ID")]
//        [StringLength(50, ErrorMessage = "HIS_ID长度不能超出50字符")]
//        //[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? HIS_ID { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>
//        //[Column("FIRST_RTIME")]
//        //[Unicode(false)]
//        public DateTime? FIRST_RTIME { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>
//        //[Column("LAST_MPERSON")]
//        [StringLength(50, ErrorMessage = "LAST_MPERSON长度不能超出50字符")]
//        //[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? LAST_MPERSON { get; set; }




//        /// <summary>
//        /// 
//        /// </summary>
//        //[Column("MANAGE_CLASS")]
//        [StringLength(20, ErrorMessage = "MANAGE_CLASS长度不能超出20字符")]
//        //[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? MANAGE_CLASS { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>
//        //[Column("POWER")]
//        [StringLength(20, ErrorMessage = "POWER长度不能超出20字符")]
//        //[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? POWER { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>
//        //[Column("HOSPITAL_ID")]
//        [Required(ErrorMessage = "不允许为空")]

//        [StringLength(20, ErrorMessage = "HOSPITAL_ID长度不能超出20字符")]
//        //[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string HOSPITAL_ID { get; set; }

//        /// <summary>
//        /// 
//        /// </summary>
//        //[Column("ID_CARD")]
//        [StringLength(50, ErrorMessage = "ID_CARD长度不能超出50字符")]
//        //[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? ID_CARD { get; set; }

//        /// <summary>
//        /// 职称类型对应人事系统的字段TECHNOLOGY_TYPE
//        /// </summary>
//        public string? TECH_TYPE { get; set; }
//        /// <summary>
//        /// 职称对应人事系统的职称名称字段ACADEMIC_POST
//        /// </summary>
//        public string? TECH_POST { get; set; }


//    }
//}
