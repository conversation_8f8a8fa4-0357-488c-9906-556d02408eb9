===========================================================================================
=====================================人员管理6.25.300导入数据======================================================
===========================================================================================
XH.H81.Models.Entities.SYS6_BASE_DATA_CLASS, XH.H81.Models##INSERT##基础数据分类：参会人身份
{"DATA_CLASS_ID":"参会人身份","HOSPITAL_ID":"H0000","CLASS_ID":"参会人身份","CLASS_NAME":"参会人身份","CLASS_SORT":"1","DATA_TABLE":"OA_BASE_DATA","DATA_FIELD":"DATA_NAME","CLASS_STATE":"1","FIRST_RPERSON":"初始化","FIRST_RTIME":"2024-05-09T14:45:24","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-04-09T23:46:16","REMARK":"【可编辑！】","ONE_CLASS":"实验室管理相关"}
XH.H81.Models.Entities.SYS6_BASE_DATA_CLASS, XH.H81.Models##INSERT##基础数据分类：学位类型
{"DATA_CLASS_ID":"学位类型","HOSPITAL_ID":"H0000","CLASS_ID":"学位类型","CLASS_NAME":"学位类型","CLASS_SORT":"1","DATA_TABLE":"SYS6_BASE_DATA","DATA_FIELD":"DATA_CNAME","CLASS_STATE":"1","FIRST_RPERSON":"初始化","FIRST_RTIME":"2024-07-03T14:45:24","LAST_MPERSON":"初始化","LAST_MTIME":"2024-05-09T14:45:24","REMARK":null,"ONE_CLASS":"实验室管理相关"}
XH.H81.Models.Entities.SYS6_BASE_DATA_CLASS, XH.H81.Models##INSERT##基础数据分类：学历性质
{"DATA_CLASS_ID":"学历性质","HOSPITAL_ID":"H0000","CLASS_ID":"学历性质","CLASS_NAME":"学历性质","CLASS_SORT":"1","DATA_TABLE":"SYS6_BASE_DATA","DATA_FIELD":"DATA_CNAME","CLASS_STATE":"1","FIRST_RPERSON":"初始化","FIRST_RTIME":"2024-07-03T14:45:24","LAST_MPERSON":"初始化","LAST_MTIME":"2024-05-09T14:45:24","REMARK":null,"ONE_CLASS":"实验室管理相关"}
XH.H81.Models.Entities.SYS6_BASE_DATA_CLASS, XH.H81.Models##INSERT##基础数据分类：学习形式
{"DATA_CLASS_ID":"学习形式","HOSPITAL_ID":"H0000","CLASS_ID":"学习形式","CLASS_NAME":"学习形式","CLASS_SORT":"1","DATA_TABLE":"SYS6_BASE_DATA","DATA_FIELD":"DATA_CNAME","CLASS_STATE":"1","FIRST_RPERSON":"初始化","FIRST_RTIME":"2024-07-03T14:45:24","LAST_MPERSON":"初始化","LAST_MTIME":"2024-05-09T14:45:24","REMARK":null,"ONE_CLASS":"实验室管理相关"}
XH.H81.Models.Entities.SYS6_BASE_DATA_CLASS, XH.H81.Models##INSERT##基础数据分类：课题级别
{"DATA_CLASS_ID":"课题级别","HOSPITAL_ID":"H0000","CLASS_ID":"课题级别","CLASS_NAME":"课题级别","CLASS_SORT":"1","DATA_TABLE":"OA_BASE_DATA","DATA_FIELD":"DATA_CNAME","CLASS_STATE":"1","FIRST_RPERSON":"初始化","FIRST_RTIME":"2024-07-03T14:45:24","LAST_MPERSON":"初始化","LAST_MTIME":"2024-05-09T14:45:24","REMARK":null,"ONE_CLASS":"实验室管理相关"}
XH.H81.Models.Entities.SYS6_BASE_DATA_CLASS, XH.H81.Models##INSERT##基础数据分类：人事技能证书类型
{"DATA_CLASS_ID":"人事技能证书类型","HOSPITAL_ID":"H0000","CLASS_ID":"人事技能证书类型","CLASS_NAME":"人事技能证书类型","CLASS_SORT":"1","DATA_TABLE":"OA_BASE_DATA","DATA_FIELD":"DATA_NAME","CLASS_STATE":"1","FIRST_RPERSON":"初始化","FIRST_RTIME":"2024-05-09T14:45:24","LAST_MPERSON":"初始化","LAST_MTIME":"2024-05-09T14:45:24","REMARK":null,"ONE_CLASS":"实验室管理相关"}
XH.H81.Models.Entities.SYS6_BASE_DATA_CLASS, XH.H81.Models##INSERT##基础数据分类：有无
{"DATA_CLASS_ID":"有无","HOSPITAL_ID":"H0000","CLASS_ID":"有无","CLASS_NAME":"有无","CLASS_SORT":"1","DATA_TABLE":"OA_BASE_DATA","DATA_FIELD":"DATA_NAME","CLASS_STATE":"1","FIRST_RPERSON":"初始化","FIRST_RTIME":"2024-05-09T14:45:24","LAST_MPERSON":"初始化","LAST_MTIME":"2024-05-09T14:45:24","REMARK":null,"ONE_CLASS":"实验室管理相关"}
XH.H81.Models.Entities.SYS6_BASE_DATA_CLASS, XH.H81.Models##INSERT##基础数据分类：对勾或交叉
{"DATA_CLASS_ID":"对勾或交叉","HOSPITAL_ID":"H0000","CLASS_ID":"对勾或交叉","CLASS_NAME":"对勾或交叉","CLASS_SORT":"1","DATA_TABLE":"OA_BASE_DATA","DATA_FIELD":"DATA_NAME","CLASS_STATE":"1","FIRST_RPERSON":"初始化","FIRST_RTIME":"2024-05-09T14:45:24","LAST_MPERSON":"初始化","LAST_MTIME":"2024-05-09T14:45:24","REMARK":null,"ONE_CLASS":"实验室管理相关"}
XH.H81.Models.Entities.SYS6_BASE_DATA_CLASS, XH.H81.Models##INSERT##基础数据分类：人员状态
{"DATA_CLASS_ID":"人员状态","HOSPITAL_ID":"H0000","CLASS_ID":"人员状态","CLASS_NAME":"人员状态","CLASS_SORT":"1","DATA_TABLE":"SYS6_BASE_DATA","DATA_FIELD":"DATA_CNAME","CLASS_STATE":"1","FIRST_RPERSON":"初始化","FIRST_RTIME":"2024-07-03T14:45:24","LAST_MPERSON":"初始化","LAST_MTIME":"2024-05-09T14:45:24","REMARK":null,"ONE_CLASS":"实验室管理相关"}
XH.H81.Models.Entities.SYS6_BASE_DATA_CLASS, XH.H81.Models##INSERT##基础数据分类：奖惩级别
{"DATA_CLASS_ID":"奖惩级别","HOSPITAL_ID":"H0000","CLASS_ID":"奖惩级别","CLASS_NAME":"奖惩级别","CLASS_SORT":"1","DATA_TABLE":"SYS6_BASE_DATA","DATA_FIELD":"DATA_CNAME","CLASS_STATE":"1","FIRST_RPERSON":"初始化","FIRST_RTIME":"2024-07-03T14:45:24","LAST_MPERSON":"初始化","LAST_MTIME":"2024-05-09T14:45:24","REMARK":null,"ONE_CLASS":"实验室管理相关"}
XH.H81.Models.Entities.SYS6_BASE_DATA_CLASS, XH.H81.Models##INSERT##基础数据分类：教学分类
{"DATA_CLASS_ID":"教学分类","HOSPITAL_ID":"H0000","CLASS_ID":"教学分类","CLASS_NAME":"教学分类","CLASS_SORT":"1","DATA_TABLE":"SYS6_BASE_DATA","DATA_FIELD":"DATA_CNAME","CLASS_STATE":"1","FIRST_RPERSON":"初始化","FIRST_RTIME":"2024-07-03T14:45:24","LAST_MPERSON":"初始化","LAST_MTIME":"2024-05-09T14:45:24","REMARK":null,"ONE_CLASS":"实验室管理相关"}
XH.H81.Models.Entities.SYS6_BASE_DATA_CLASS, XH.H81.Models##INSERT##基础数据分类：教学级别
{"DATA_CLASS_ID":"教学级别","HOSPITAL_ID":"H0000","CLASS_ID":"教学级别","CLASS_NAME":"教学级别","CLASS_SORT":"1","DATA_TABLE":"SYS6_BASE_DATA","DATA_FIELD":"DATA_CNAME","CLASS_STATE":"1","FIRST_RPERSON":"初始化","FIRST_RTIME":"2024-07-03T14:45:24","LAST_MPERSON":"初始化","LAST_MTIME":"2024-05-09T14:45:24","REMARK":null,"ONE_CLASS":"实验室管理相关"}
XH.H81.Models.Entities.SYS6_BASE_DATA_CLASS, XH.H81.Models##INSERT##基础数据分类：课题分类
{"DATA_CLASS_ID":"课题分类","HOSPITAL_ID":"H0000","CLASS_ID":"课题分类","CLASS_NAME":"课题分类","CLASS_SORT":"1","DATA_TABLE":"SYS6_BASE_DATA","DATA_FIELD":"DATA_CNAME","CLASS_STATE":"1","FIRST_RPERSON":"初始化","FIRST_RTIME":"2024-07-03T14:45:24","LAST_MPERSON":"初始化","LAST_MTIME":"2024-05-09T14:45:24","REMARK":null,"ONE_CLASS":"实验室管理相关"}
XH.H81.Models.Entities.SYS6_BASE_DATA_CLASS, XH.H81.Models##INSERT##基础数据分类：论著类型
{"DATA_CLASS_ID":"论著类型","HOSPITAL_ID":"H0000","CLASS_ID":"论著类型","CLASS_NAME":"论著类型","CLASS_SORT":"1","DATA_TABLE":"SYS6_BASE_DATA","DATA_FIELD":"DATA_CNAME","CLASS_STATE":"1","FIRST_RPERSON":"初始化","FIRST_RTIME":"2024-07-03T14:45:24","LAST_MPERSON":"初始化","LAST_MTIME":"2024-05-09T14:45:24","REMARK":null,"ONE_CLASS":"实验室管理相关"}
XH.H81.Models.Entities.SYS6_BASE_DATA_CLASS, XH.H81.Models##INSERT##基础数据分类：论著级别
{"DATA_CLASS_ID":"论著级别","HOSPITAL_ID":"H0000","CLASS_ID":"论著级别","CLASS_NAME":"论著级别","CLASS_SORT":"1","DATA_TABLE":"SYS6_BASE_DATA","DATA_FIELD":"DATA_CNAME","CLASS_STATE":"1","FIRST_RPERSON":"初始化","FIRST_RTIME":"2024-07-03T14:45:24","LAST_MPERSON":"初始化","LAST_MTIME":"2024-05-09T14:45:24","REMARK":null,"ONE_CLASS":"实验室管理相关"}
XH.H81.Models.Entities.SYS6_BASE_DATA_CLASS, XH.H81.Models##INSERT##基础数据分类：论著作者排序
{"DATA_CLASS_ID":"论著作者排序","HOSPITAL_ID":"H0000","CLASS_ID":"论著作者排序","CLASS_NAME":"论著作者排序","CLASS_SORT":"1","DATA_TABLE":"SYS6_BASE_DATA","DATA_FIELD":"DATA_CNAME","CLASS_STATE":"1","FIRST_RPERSON":"初始化","FIRST_RTIME":"2024-07-03T14:45:24","LAST_MPERSON":"初始化","LAST_MTIME":"2024-05-09T14:45:24","REMARK":null,"ONE_CLASS":"实验室管理相关"}
XH.H81.Models.Entities.SYS6_BASE_DATA_CLASS, XH.H81.Models##INSERT##基础数据分类：JCR分区
{"DATA_CLASS_ID":"JCR分区","HOSPITAL_ID":"H0000","CLASS_ID":"JCR分区","CLASS_NAME":"JCR分区","CLASS_SORT":"1","DATA_TABLE":"SYS6_BASE_DATA","DATA_FIELD":"DATA_CNAME","CLASS_STATE":"1","FIRST_RPERSON":"初始化","FIRST_RTIME":"2024-07-03T14:45:24","LAST_MPERSON":"初始化","LAST_MTIME":"2024-05-09T14:45:24","REMARK":null,"ONE_CLASS":"实验室管理相关"}
XH.H81.Models.Entities.SYS6_BASE_DATA_CLASS, XH.H81.Models##INSERT##基础数据分类：是否SCI记录
{"DATA_CLASS_ID":"是否SCI记录","HOSPITAL_ID":"H0000","CLASS_ID":"是否SCI记录","CLASS_NAME":"是否SCI记录","CLASS_SORT":"1","DATA_TABLE":"SYS6_BASE_DATA","DATA_FIELD":"DATA_CNAME","CLASS_STATE":"1","FIRST_RPERSON":"初始化","FIRST_RTIME":"2024-07-03T14:45:24","LAST_MPERSON":"初始化","LAST_MTIME":"2024-05-09T14:45:24","REMARK":null,"ONE_CLASS":"实验室管理相关"}
XH.H81.Models.Entities.SYS6_BASE_DATA_CLASS, XH.H81.Models##INSERT##基础数据分类：学分类型
{"DATA_CLASS_ID":"学分类型","HOSPITAL_ID":"H0000","CLASS_ID":"学分类型","CLASS_NAME":"学分类型","CLASS_SORT":"1","DATA_TABLE":"SYS6_BASE_DATA","DATA_FIELD":"DATA_CNAME","CLASS_STATE":"1","FIRST_RPERSON":"初始化","FIRST_RTIME":"2024-07-03T14:45:24","LAST_MPERSON":"初始化","LAST_MTIME":"2024-05-09T14:45:24","REMARK":null,"ONE_CLASS":"实验室管理相关"}
XH.H81.Models.Entities.SYS6_BASE_DATA_CLASS, XH.H81.Models##INSERT##基础数据分类：证书级别
{"DATA_CLASS_ID":"证书级别","HOSPITAL_ID":"H0000","CLASS_ID":"证书级别","CLASS_NAME":"证书级别","CLASS_SORT":"1","DATA_TABLE":"SYS6_BASE_DATA","DATA_FIELD":"DATA_CNAME","CLASS_STATE":"1","FIRST_RPERSON":"初始化","FIRST_RTIME":"2024-07-03T14:45:24","LAST_MPERSON":"初始化","LAST_MTIME":"2024-05-09T14:45:24","REMARK":null,"ONE_CLASS":"实验室管理相关"}
XH.H81.Models.Entities.SYS6_BASE_DATA_CLASS, XH.H81.Models##INSERT##基础数据分类：任职机构等级
{"DATA_CLASS_ID":"任职机构等级","HOSPITAL_ID":"H0000","CLASS_ID":"任职机构等级","CLASS_NAME":"任职机构等级","CLASS_SORT":"1","DATA_TABLE":"SYS6_BASE_DATA","DATA_FIELD":"DATA_CNAME","CLASS_STATE":"1","FIRST_RPERSON":"初始化","FIRST_RTIME":"2024-07-03T14:45:24","LAST_MPERSON":"初始化","LAST_MTIME":"2024-05-09T14:45:24","REMARK":null,"ONE_CLASS":"实验室管理相关"}
XH.H81.Models.Entities.SYS6_BASE_DATA_CLASS, XH.H81.Models##INSERT##基础数据分类：人员培训分类
{"DATA_CLASS_ID":"人员培训分类","HOSPITAL_ID":"H0000","CLASS_ID":"人员培训分类","CLASS_NAME":"人员培训分类","CLASS_SORT":"1","DATA_TABLE":"OA_BASE_DATA","DATA_FIELD":"DATA_NAME","CLASS_STATE":"1","FIRST_RPERSON":"初始化","FIRST_RTIME":"2025-05-13T14:45:24","LAST_MPERSON":"初始化","LAST_MTIME":"2022-05-13T14:45:24","REMARK":null,"ONE_CLASS":"实验室管理相关"}
XH.H81.Models.Entities.SYS6_BASE_DATA_CLASS, XH.H81.Models##INSERT##基础数据分类：任职职务
{"DATA_CLASS_ID":"任职职务","HOSPITAL_ID":"H0000","CLASS_ID":"任职职务","CLASS_NAME":"任职职务","CLASS_SORT":"1","DATA_TABLE":"SYS6_BASE_DATA","DATA_FIELD":"DATA_CNAME","CLASS_STATE":"1","FIRST_RPERSON":"初始化","FIRST_RTIME":"2024-07-03T14:45:24","LAST_MPERSON":"初始化","LAST_MTIME":"2024-05-09T14:45:24","REMARK":null,"ONE_CLASS":"实验室管理相关"}
XH.H81.Models.Entities.SYS6_BASE_DATA_CLASS, XH.H81.Models##INSERT##基础数据分类：当前是否在职
{"DATA_CLASS_ID":"当前是否在职","HOSPITAL_ID":"H0000","CLASS_ID":"当前是否在职","CLASS_NAME":"当前是否在职","CLASS_SORT":"1","DATA_TABLE":"SYS6_BASE_DATA","DATA_FIELD":"DATA_CNAME","CLASS_STATE":"1","FIRST_RPERSON":"初始化","FIRST_RTIME":"2024-07-03T14:45:24","LAST_MPERSON":"初始化","LAST_MTIME":"2024-05-09T14:45:24","REMARK":null,"ONE_CLASS":"实验室管理相关"}
XH.H81.Models.Entities.SYS6_BASE_DATA_CLASS, XH.H81.Models##INSERT##基础数据分类：知识产权分类
{"DATA_CLASS_ID":"知识产权分类","HOSPITAL_ID":"H0000","CLASS_ID":"知识产权分类","CLASS_NAME":"知识产权分类","CLASS_SORT":"1","DATA_TABLE":"SYS6_BASE_DATA","DATA_FIELD":"DATA_CNAME","CLASS_STATE":"1","FIRST_RPERSON":"初始化","FIRST_RTIME":"2024-07-03T14:45:24","LAST_MPERSON":"初始化","LAST_MTIME":"2024-05-09T14:45:24","REMARK":null,"ONE_CLASS":"实验室管理相关"}
XH.H81.Models.Entities.SYS6_BASE_DATA_CLASS, XH.H81.Models##INSERT##基础数据分类：入职方式
{"DATA_CLASS_ID":"入职方式","HOSPITAL_ID":"H0000","CLASS_ID":"入职方式","CLASS_NAME":"入职方式","CLASS_SORT":"1","DATA_TABLE":"SYS6_BASE_DATA","DATA_FIELD":"DATA_CNAME","CLASS_STATE":"1","FIRST_RPERSON":"初始化","FIRST_RTIME":"2024-07-03T14:45:24","LAST_MPERSON":"初始化","LAST_MTIME":"2024-05-09T14:45:24","REMARK":null,"ONE_CLASS":"实验室管理相关"}
XH.H81.Models.Entities.SYS6_BASE_DATA_CLASS, XH.H81.Models##INSERT##基础数据分类：交流级别
{"DATA_CLASS_ID":"交流级别","HOSPITAL_ID":"H0000","CLASS_ID":"交流级别","CLASS_NAME":"交流级别","CLASS_SORT":"1","DATA_TABLE":"SYS6_BASE_DATA","DATA_FIELD":"DATA_CNAME","CLASS_STATE":"1","FIRST_RPERSON":"初始化","FIRST_RTIME":"2024-07-03T14:45:24","LAST_MPERSON":"初始化","LAST_MTIME":"2024-05-09T14:45:24","REMARK":null,"ONE_CLASS":"实验室管理相关"}
XH.H81.Models.Entities.SYS6_BASE_DATA_CLASS, XH.H81.Models##INSERT##基础数据分类：外派性质
{"DATA_CLASS_ID":"外派性质","HOSPITAL_ID":"H0000","CLASS_ID":"外派性质","CLASS_NAME":"外派性质","CLASS_SORT":"1","DATA_TABLE":"SYS6_BASE_DATA","DATA_FIELD":"DATA_CNAME","CLASS_STATE":"1","FIRST_RPERSON":"初始化","FIRST_RTIME":"2024-07-03T14:45:24","LAST_MPERSON":"初始化","LAST_MTIME":"2024-05-09T14:45:24","REMARK":null,"ONE_CLASS":"实验室管理相关"}
XH.H81.Models.Entities.SYS6_BASE_DATA_CLASS, XH.H81.Models##INSERT##基础数据分类：进修分类
{"DATA_CLASS_ID":"进修分类","HOSPITAL_ID":"H0000","CLASS_ID":"进修分类","CLASS_NAME":"进修分类","CLASS_SORT":"1","DATA_TABLE":"SYS6_BASE_DATA","DATA_FIELD":"DATA_CNAME","CLASS_STATE":"1","FIRST_RPERSON":"初始化","FIRST_RTIME":"2024-07-03T14:45:24","LAST_MPERSON":"初始化","LAST_MTIME":"2024-05-09T14:45:24","REMARK":null,"ONE_CLASS":"实验室管理相关"}
XH.H81.Models.Entities.SYS6_BASE_DATA_CLASS, XH.H81.Models##INSERT##基础数据分类：课题类型
{"DATA_CLASS_ID":"课题类型","HOSPITAL_ID":"H0000","CLASS_ID":"课题类型","CLASS_NAME":"课题类型","CLASS_SORT":"1","DATA_TABLE":"SYS6_BASE_DATA","DATA_FIELD":"DATA_CNAME","CLASS_STATE":"1","FIRST_RPERSON":"初始化","FIRST_RTIME":"2024-07-03T14:45:24","LAST_MPERSON":"初始化","LAST_MTIME":"2024-05-09T14:45:24","REMARK":null,"ONE_CLASS":"实验室管理相关"}
XH.H81.Models.Entities.SYS6_BASE_DATA_CLASS, XH.H81.Models##INSERT##基础数据分类：是否完成
{"DATA_CLASS_ID":"是否完成","HOSPITAL_ID":"H0000","CLASS_ID":"是否完成","CLASS_NAME":"是否完成","CLASS_SORT":"1","DATA_TABLE":"SYS6_BASE_DATA","DATA_FIELD":"DATA_CNAME","CLASS_STATE":"1","FIRST_RPERSON":"初始化","FIRST_RTIME":"2024-07-03T14:45:24","LAST_MPERSON":"初始化","LAST_MTIME":"2024-05-09T14:45:24","REMARK":null,"ONE_CLASS":"实验室管理相关"}
XH.H81.Models.Entities.SYS6_BASE_DATA_CLASS, XH.H81.Models##INSERT##基础数据分类：参与类型
{"DATA_CLASS_ID":"参与类型","HOSPITAL_ID":"H0000","CLASS_ID":"参与类型","CLASS_NAME":"参与类型","CLASS_SORT":"1","DATA_TABLE":"SYS6_BASE_DATA","DATA_FIELD":"DATA_CNAME","CLASS_STATE":"1","FIRST_RPERSON":"初始化","FIRST_RTIME":"2024-07-03T14:45:24","LAST_MPERSON":"初始化","LAST_MTIME":"2024-05-09T14:45:24","REMARK":null,"ONE_CLASS":"实验室管理相关"}
XH.H81.Models.Entities.SYS6_BASE_DATA_CLASS, XH.H81.Models##INSERT##基础数据分类：考试评价结果
{"DATA_CLASS_ID":"考试评价结果","HOSPITAL_ID":"H0000","CLASS_ID":"考试评价结果","CLASS_NAME":"考试评价结果","CLASS_SORT":"1","DATA_TABLE":"OA_BASE_DATA","DATA_FIELD":"DATA_NAME","CLASS_STATE":"1","FIRST_RPERSON":"初始化","FIRST_RTIME":"2024-05-09T14:45:24","LAST_MPERSON":"初始化","LAST_MTIME":"2025-04-09T23:46:16","REMARK":null,"ONE_CLASS":"实验室管理相关"}
XH.H81.Models.Entities.SYS6_BASE_DATA_CLASS, XH.H81.Models##INSERT##基础数据分类：人员评估评价结果
{"DATA_CLASS_ID":"人员评估评价结果","HOSPITAL_ID":"H0000","CLASS_ID":"人员评估评价结果","CLASS_NAME":"人员评估评价结果","CLASS_SORT":"1","DATA_TABLE":"OA_BASE_DATA","DATA_FIELD":"DATA_NAME","CLASS_STATE":"1","FIRST_RPERSON":"初始化","FIRST_RTIME":"2024-05-09T14:45:24","LAST_MPERSON":"初始化","LAST_MTIME":"2025-04-09T23:46:16","REMARK":null,"ONE_CLASS":"实验室管理相关"}
XH.H81.Models.Entities.SYS6_BASE_DATA_CLASS, XH.H81.Models##INSERT##基础数据分类：技术类型
{"DATA_CLASS_ID":"技术类型","HOSPITAL_ID":"H0000","CLASS_ID":"技术类型","CLASS_NAME":"技术类型","CLASS_SORT":"1","DATA_TABLE":"SYS6_BASE_DATA","DATA_FIELD":"DATA_CNAME","CLASS_STATE":"1","FIRST_RPERSON":"初始化","FIRST_RTIME":"2024-07-03T14:45:24","LAST_MPERSON":"初始化","LAST_MTIME":"2024-05-09T14:45:24","REMARK":null,"ONE_CLASS":"实验室管理相关"}
XH.H81.Models.Entities.SYS6_BASE_DATA_CLASS, XH.H81.Models##INSERT##基础数据分类：现从事岗位
{"DATA_CLASS_ID":"现从事岗位","HOSPITAL_ID":"H0000","CLASS_ID":"现从事岗位","CLASS_NAME":"现从事岗位","CLASS_SORT":"1","DATA_TABLE":"OA_BASE_DATA","DATA_FIELD":"DATA_NAME","CLASS_STATE":"1","FIRST_RPERSON":"初始化","FIRST_RTIME":"2024-05-09T14:45:24","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-04-09T23:46:16","REMARK":null,"ONE_CLASS":"实验室管理相关"}
XH.H81.Models.Entities.SYS6_BASE_DATA_CLASS, XH.H81.Models##INSERT##基础数据分类：岗位类别
{"DATA_CLASS_ID":"岗位类别","HOSPITAL_ID":"H0000","CLASS_ID":"岗位类别","CLASS_NAME":"岗位类别","CLASS_SORT":"1","DATA_TABLE":"OA_BASE_DATA","DATA_FIELD":"DATA_NAME","CLASS_STATE":"1","FIRST_RPERSON":"初始化","FIRST_RTIME":"2024-05-09T14:45:24","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-04-09T23:46:16","REMARK":null,"ONE_CLASS":"实验室管理相关"}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：职称名称
{"FIELD_ID":"H81000215","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"ACADEMIC_POST","FIELD_NAME":"职称名称","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataClass\":\"DataClass_By_Level_Names\",\"queryType\":\"api\",\"dataType\":\"Select\",\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}]},\"formName\":\"职称名称\"}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"xialakuang\",\"icon\":\"iconxialakuang\",\"formName\":\"下拉框\",\"isForm\":true,\"dataType\":\"Select\",\"dataClass\":\"DataClass_By_Level_Names\",\"queryType\":\"api\",\"wigetType\":\"fixed\",\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}]},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-19T11:45:07","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：基本信息
{"FIELD_ID":"H81000256","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"JBXX70654","FIELD_NAME":"基本信息","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataType\":\"Input\",\"rules\":[{\"required\":true}],\"wigetProps\":{\"placeholder\":\"请输入\",\"allowClear\":false,\"maxLength\":200,\"disabled\":false,\"variant\":\"outlined\"},\"formName\":\"基本信息\"}","ADDN_JSON":"{\"group\":\"基本\",\"formId\":\"danhangwenben\",\"icon\":\"icondanhangwenben\",\"formName\":\"单行文本\",\"dataType\":\"Input\",\"isForm\":true,\"rules\":[{\"required\":true}],\"wigetProps\":{\"placeholder\":\"请输入\",\"allowClear\":false,\"maxLength\":200,\"disabled\":false,\"variant\":\"outlined\"},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T19:55:32","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：职称类型
{"FIELD_ID":"H81000211","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"TECHNOLOGY_TYPE","FIELD_NAME":"职称类型","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataClass\":\"职称类型\",\"queryType\":\"api\",\"dataType\":\"Select\",\"rules\":[{\"required\":null}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}],\"defaultValue\":\"\"},\"formName\":\"职称类型\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"colSpan\":8}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"xialakuang\",\"icon\":\"iconxialakuang\",\"formName\":\"下拉框\",\"isForm\":true,\"dataType\":\"Select\",\"wigetProps\":{\"options\":[],\"defaultValue\":\"1\"},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"],\"dataClass\":\"职称类型\",\"queryType\":\"api\",\"rules\":[{\"required\":false}]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T19:55:42","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：现居住地
{"FIELD_ID":"H81000184","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"CURRENT_ADDRESS","FIELD_NAME":"现居住地","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":null,\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":null,\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"现居住地\"}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"dataType\":\"Input\",\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"现居住地\",\"dataClass\":null,\"labelHide\":false,\"labelStyle\":null,\"colSpan\":8,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-14T00:04:01","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：工号
{"FIELD_ID":"H81000172","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"HIS_ID","FIELD_NAME":"工号","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":null,\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":null,\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"工号\"}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"colSpan\":\"8\",\"dataClass\":null,\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":null,\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"工号\",\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-14T10:27:57","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：人员性质
{"FIELD_ID":"H81000207","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"PERSON_DOC_STATE","FIELD_NAME":"人员性质","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataType\":\"Input\",\"rules\":[{\"required\":true}],\"wigetProps\":{\"placeholder\":\"请输入\",\"allowClear\":true,\"maxLength\":200,\"disabled\":false},\"formName\":\"人员性质\",\"dataClass\":null,\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"dataType\":\"Input\",\"rules\":[{\"required\":true}],\"wigetProps\":{\"placeholder\":\"请输入\",\"allowClear\":true,\"maxLength\":200,\"disabled\":false},\"formName\":\"人员性质\",\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-13T22:51:04","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：证件号码
{"FIELD_ID":"H81000178","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"ID_CARD","FIELD_NAME":"证件号码","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":\"8\",\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":null,\"rules\":[{\"required\":true}],\"wigetProps\":{\"button\":false,\"groupKey\":null},\"formName\":\"证件号码\"}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"colSpan\":8,\"dataType\":\"Input\",\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"},\"defaultValue\":null},\"formName\":\"证件号码1234\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-13T20:46:13","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：性别/年龄
{"FIELD_ID":"H81000180","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"SEX","FIELD_NAME":"性别/年龄","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":\"性别\",\"queryType\":\"api\",\"dataType\":\"Select\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}],\"defaultValue\":\"\"},\"formName\":\"性别/年龄\",\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"colSpan\":8,\"dataClass\":\"性别\",\"queryType\":\"api\",\"dataType\":\"Select\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":true}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}],\"defaultValue\":\"\"},\"formName\":\"性别/年龄\",\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-14T00:04:01","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：紧急联系人
{"FIELD_ID":"H81000203","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"EMERGENCY_CONTACT","FIELD_NAME":"紧急联系人","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":null,\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":null,\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"紧急联系人\"}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"colSpan\":8,\"dataClass\":null,\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":null,\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"紧急联系人\",\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-14T00:04:01","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：详细通讯地址
{"FIELD_ID":"H81000206","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"COMM_ADDR","FIELD_NAME":"详细通讯地址","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataType\":\"Input\",\"rules\":[{\"required\":true}],\"wigetProps\":{\"button\":false,\"groupKey\":null},\"formName\":\"详细通讯地址\",\"labelHide\":false,\"labelStyle\":null}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"danhangwenben\",\"icon\":\"icondanhangwenben\",\"formName\":\"单行文本\",\"dataType\":\"Input\",\"isForm\":true,\"rules\":[{\"required\":true}],\"wigetProps\":{\"placeholder\":\"请输入\",\"allowClear\":true,\"maxLength\":200,\"disabled\":false},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T18:38:50","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：入职方式
{"FIELD_ID":"H81000209","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"EMPLOYMENT_SOURE","FIELD_NAME":"入职方式","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataClass\":\"入职方式\",\"queryType\":\"api\",\"dataType\":\"Select\",\"wigetProps\":{\"button\":false,\"groupKey\":null},\"formName\":\"入职方式\",\"rules\":[{\"required\":true}],\"labelHide\":false,\"labelStyle\":null}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"xialakuang\",\"icon\":\"iconxialakuang\",\"formName\":\"下拉框\",\"isForm\":true,\"dataType\":\"Select\",\"wigetProps\":{\"options\":[],\"defaultValue\":\"3\"},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"],\"dataClass\":\"入职方式\",\"queryType\":\"api\"}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T18:38:52","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：用工类型
{"FIELD_ID":"H81000210","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"USER_TYPE","FIELD_NAME":"用工类型","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataClass\":\"用工类型\",\"queryType\":\"api\",\"dataType\":\"Select\",\"wigetProps\":{\"button\":false,\"groupKey\":null},\"formName\":\"用工类型\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"colSpan\":8,\"rules\":[{\"required\":null}],\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"xialakuang\",\"icon\":\"iconxialakuang\",\"formName\":\"下拉框\",\"isForm\":true,\"dataType\":\"Select\",\"wigetProps\":{\"options\":[],\"defaultValue\":\"\"},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"],\"dataClass\":\"用工类型\",\"queryType\":\"api\"}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T18:38:53","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：聘任职称评定单位
{"FIELD_ID":"H81000214","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"EMPLOYMENT_UNIT","FIELD_NAME":"聘任职称评定单位","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":null,\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":null,\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"聘任职称评定单位\"}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"dataType\":\"Input\",\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"聘任职称评定单位\",\"dataClass\":null,\"labelHide\":false,\"labelStyle\":null,\"colSpan\":8,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-14T00:04:01","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：职称评定日期
{"FIELD_ID":"H81000216","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"TECH_CERTIFICE_TIME","FIELD_NAME":"职称评定日期","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataType\":\"DatePicker\",\"wigetProps\":{\"button\":false,\"groupKey\":null},\"formName\":\"职称评定日期\",\"labelHide\":false,\"labelStyle\":null}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"riqi\",\"icon\":\"iconriqi\",\"formName\":\"日期\",\"dataType\":\"DatePicker\",\"isForm\":true,\"wigetProps\":{},\"propslist\":[\"dataType\",\"required\",\"disabled\",\"picker\",\"allowClear\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T20:54:43","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：工龄
{"FIELD_ID":"H81000219","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"LENGTH_SERVICE","FIELD_NAME":"工龄","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataType\":\"Input\",\"rules\":[{\"required\":true}],\"wigetProps\":{\"button\":false,\"groupKey\":null},\"formName\":\"工龄\",\"labelHide\":false,\"labelStyle\":null}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"danhangwenben\",\"icon\":\"icondanhangwenben\",\"formName\":\"单行文本\",\"dataType\":\"Input\",\"isForm\":true,\"rules\":[{\"required\":true}],\"wigetProps\":{\"placeholder\":\"请输入\",\"allowClear\":true,\"maxLength\":200,\"disabled\":false},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T18:38:58","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：年龄
{"FIELD_ID":"H81000181","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"AGE","FIELD_NAME":"年龄","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":null,\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":null,\"rules\":[{\"required\":true}],\"wigetProps\":{\"disabled\":true,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"年龄\"}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"dataType\":\"Input\",\"rules\":[{\"required\":true}],\"wigetProps\":{\"disabled\":true,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"年龄\",\"dataClass\":null,\"labelHide\":false,\"labelStyle\":null,\"colSpan\":8,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-14T00:04:01","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：籍贯
{"FIELD_ID":"H81000182","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"NATIVE_PLACE","FIELD_NAME":"籍贯","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataType\":\"Cascader\",\"wigetProps\":{\"button\":false,\"groupKey\":null},\"formName\":\"籍贯\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"colSpan\":8,\"dataClass\":null,\"rules\":[{\"required\":null}],\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"jilianxuanze\",\"icon\":\"iconxialakuang\",\"formName\":\"级联选择\",\"isForm\":true,\"dataType\":\"Cascader\",\"wigetProps\":{},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T18:39:01","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：最高学历
{"FIELD_ID":"H81000185","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"HIGHEST_DEGREE","FIELD_NAME":"最高学历","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":\"最高学历\",\"queryType\":\"api\",\"dataType\":\"Select\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":true}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}]},\"formName\":\"最高学历\"}","ADDN_JSON":"{\"colSpan\":8,\"dataClass\":\"最高学历\",\"queryType\":\"api\",\"dataType\":\"Select\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":true}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}]},\"formName\":\"最高学历\",\"readOnlyEnum\":\"1\",\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-19T16:34:31","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-27T14:28:39","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：退休日期
{"FIELD_ID":"H81000218","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"RETIRE_TIME","FIELD_NAME":"退休日期","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataType\":\"DatePicker\",\"wigetProps\":{},\"formName\":\"退休日期\"}","ADDN_JSON":"{\"group\":\"基本\",\"formId\":\"riqi\",\"icon\":\"iconriqi\",\"formName\":\"日期\",\"dataType\":\"DatePicker\",\"isForm\":true,\"wigetProps\":{},\"propslist\":[\"dataType\",\"required\",\"disabled\",\"picker\",\"allowClear\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-19T16:34:31","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-27T14:25:24","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：婚姻状况
{"FIELD_ID":"H81000192","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"MARITAL_STATUS","FIELD_NAME":"婚姻状况","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataClass\":\"婚姻状况\",\"queryType\":\"api\",\"dataType\":\"Radio\",\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}],\"defaultValue\":\"\"},\"formName\":\"婚姻状况\",\"labelStyle\":{\"justifyContent\":\"flex-end\",\"textAlign\":\"right\",\"display\":\"flex\"}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"danxuankuang\",\"icon\":\"icondanxuan\",\"formName\":\"单选\",\"isForm\":true,\"dataType\":\"Radio\",\"wigetProps\":{\"options\":[],\"defaultValue\":\"\"},\"propslist\":[\"dataType\",\"required\",\"optionType\",\"styleFlex\",\"disabled\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"],\"dataClass\":\"婚姻状况\",\"queryType\":\"api\"}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T18:39:04","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：联系方式
{"FIELD_ID":"H81000200","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"PHONE","FIELD_NAME":"联系方式","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataType\":\"Input\",\"rules\":[{\"required\":true}],\"wigetProps\":{\"button\":false,\"groupKey\":null},\"formName\":\"联系方式\",\"labelHide\":false,\"labelStyle\":null}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"danhangwenben\",\"icon\":\"icondanhangwenben\",\"formName\":\"单行文本\",\"dataType\":\"Input\",\"isForm\":true,\"rules\":[{\"required\":true}],\"wigetProps\":{\"placeholder\":\"请输入\",\"allowClear\":true,\"maxLength\":200,\"disabled\":false},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T18:39:07","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：办公电话
{"FIELD_ID":"H81000202","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"OFFICE_PHONE","FIELD_NAME":"办公电话","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":null,\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":null,\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"办公电话\"}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"dataType\":\"Input\",\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"办公电话\",\"dataClass\":null,\"labelHide\":false,\"labelStyle\":null,\"colSpan\":8,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-14T00:04:01","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：视力
{"FIELD_ID":"H81000196","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"EYESIGHT_RIGHT","FIELD_NAME":"视力","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":\"4\",\"dataClass\":null,\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":null,\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"视力\"}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"dataType\":\"Input\",\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"视力\",\"dataClass\":null,\"labelHide\":false,\"labelStyle\":null,\"colSpan\":8,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-14T00:04:01","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：职称专业
{"FIELD_ID":"H81000212","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"TECH_POST_PROFESSION","FIELD_NAME":"职称专业","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":null,\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":null,\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"职称专业\"}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"dataType\":\"Input\",\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"职称专业\",\"dataClass\":null,\"labelHide\":false,\"labelStyle\":null,\"colSpan\":8,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-14T00:04:01","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：离院日期
{"FIELD_ID":"H81000221","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"OUT_HOSPITAL_TIME","FIELD_NAME":"离院日期","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataType\":\"DatePicker\",\"wigetProps\":{\"button\":false,\"groupKey\":null},\"formName\":\"离院日期\",\"labelHide\":false,\"labelStyle\":null}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"riqi\",\"icon\":\"iconriqi\",\"formName\":\"日期\",\"dataType\":\"DatePicker\",\"isForm\":true,\"wigetProps\":{},\"propslist\":[\"dataType\",\"required\",\"disabled\",\"picker\",\"allowClear\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T20:54:43","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：离科日期
{"FIELD_ID":"H81000224","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"OUT_LAB_TIME","FIELD_NAME":"离科日期","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataType\":\"DatePicker\",\"wigetProps\":{\"button\":false,\"groupKey\":null},\"formName\":\"离科日期\",\"labelHide\":false,\"labelStyle\":null}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"riqi\",\"icon\":\"iconriqi\",\"formName\":\"日期\",\"dataType\":\"DatePicker\",\"isForm\":true,\"wigetProps\":{},\"propslist\":[\"dataType\",\"required\",\"disabled\",\"picker\",\"allowClear\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T20:54:43","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：科龄
{"FIELD_ID":"H81000225","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"LENGTH_LAB","FIELD_NAME":"科龄","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataClass\":null,\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":null,\"rules\":[{\"required\":true}],\"wigetProps\":{\"button\":false,\"groupKey\":null},\"formName\":\"科龄\"}","ADDN_JSON":null,"FIELD_STATE":"1","FIRST_RPERSON":"zwt_周伟涛","FIRST_RTIME":"2025-06-12T17:33:14","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：最高学位
{"FIELD_ID":"H81000186","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"HIGHEST_DIPLOMA","FIELD_NAME":"最高学位","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":\"最高学位\",\"queryType\":\"api\",\"dataType\":\"Select\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}]},\"formName\":\"最高学位\"}","ADDN_JSON":"{\"colSpan\":8,\"dataClass\":\"最高学位\",\"queryType\":\"api\",\"dataType\":\"Select\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}]},\"formName\":\"最高学位\",\"readOnlyEnum\":\"1\",\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-19T16:34:31","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-27T14:29:06","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：毕业日期
{"FIELD_ID":"H81000188","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"GRADUATE_DATE","FIELD_NAME":"毕业日期","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":null,\"dataType\":\"DatePicker\",\"labelHide\":false,\"labelStyle\":null,\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"毕业日期\"}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"dataType\":\"DatePicker\",\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"毕业日期\",\"dataClass\":null,\"labelHide\":false,\"labelStyle\":null,\"rules\":[{\"required\":null}],\"colSpan\":8,\"propslist\":[\"dataType\",\"required\",\"disabled\",\"picker\",\"allowClear\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-14T00:04:01","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：与紧急联系人的关系
{"FIELD_ID":"H81000204","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"ECONTACT_RELACTION","FIELD_NAME":"与紧急联系人的关系","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataType\":\"Input\",\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"与紧急联系人的关系\",\"dataClass\":null,\"labelHide\":false,\"labelStyle\":null,\"colSpan\":8}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"danhangwenben\",\"icon\":\"icondanhangwenben\",\"formName\":\"单行文本\",\"dataType\":\"Input\",\"isForm\":true,\"rules\":[{\"required\":true}],\"wigetProps\":{\"placeholder\":\"请输入\",\"allowClear\":true,\"maxLength\":200,\"disabled\":false},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T18:39:26","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：证件类型
{"FIELD_ID":"H81000174","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"CARD_TYPE","FIELD_NAME":"证件类型","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":\"证件类型\",\"queryType\":\"api\",\"dataType\":\"Select\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":true}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}]},\"formName\":\"证件类型\"}","ADDN_JSON":"{\"colSpan\":8,\"dataClass\":\"证件类型\",\"queryType\":\"api\",\"dataType\":\"Select\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":true}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}]},\"formName\":\"证件类型\",\"readOnlyEnum\":\"1\",\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-27T14:27:32","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-27T14:28:26","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：职称级别
{"FIELD_ID":"H81000213","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"TECH_POST","FIELD_NAME":"职称级别","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":\"职称级别\",\"queryType\":\"api\",\"dataType\":\"Select\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}],\"defaultValue\":\"\"},\"formName\":\"职称级别\"}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"dataClass\":\"职称级别\",\"queryType\":\"api\",\"dataType\":\"Select\",\"rules\":[{\"required\":null}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}],\"defaultValue\":\"\"},\"formName\":\"职称级别\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"colSpan\":8,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-14T00:04:01","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：专业特长
{"FIELD_ID":"H81000199","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"PROFESSION_EXPERTISE","FIELD_NAME":"专业特长","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":null,\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":null,\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"专业特长\"}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"dataType\":\"Input\",\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"专业特长\",\"dataClass\":null,\"labelHide\":false,\"labelStyle\":null,\"colSpan\":8,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-14T00:04:01","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：备案实验室
{"FIELD_ID":"H81000237","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"BASYS55209","FIELD_NAME":"备案实验室","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataClass\":\"DataClass_By_Smbl_Lab\",\"queryType\":\"api\",\"dataType\":\"Select\",\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}]},\"formName\":\"备案实验室\"}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"xialakuang\",\"icon\":\"iconxialakuang\",\"formName\":\"下拉框\",\"isForm\":true,\"dataType\":\"Select\",\"dataClass\":\"DataClass_By_Smbl_Lab\",\"queryType\":\"api\",\"wigetType\":\"fixed\",\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}]},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T18:39:32","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：来科日期
{"FIELD_ID":"H81000223","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"IN_LAB_TIME","FIELD_NAME":"来科日期","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataType\":\"DatePicker\",\"wigetProps\":{\"button\":false,\"groupKey\":null},\"formName\":\"来科日期\",\"rules\":[{\"required\":true}],\"labelHide\":false,\"labelStyle\":null}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"riqi\",\"icon\":\"iconriqi\",\"formName\":\"日期\",\"dataType\":\"DatePicker\",\"isForm\":true,\"wigetProps\":{},\"propslist\":[\"dataType\",\"required\",\"disabled\",\"picker\",\"allowClear\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T21:10:48","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：是否外单位科研合作人员
{"FIELD_ID":"H81000238","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"SFWDWKYHZRY39890","FIELD_NAME":"是否外单位科研合作人员","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataClass\":\"是否\",\"queryType\":\"api\",\"dataType\":\"Radio\",\"wigetProps\":{\"options\":[],\"defaultValue\":\"\"},\"formName\":\"是否外单位科研合作人员\"}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"danxuankuang\",\"icon\":\"icondanxuan\",\"formName\":\"单选\",\"isForm\":true,\"dataType\":\"Radio\",\"wigetProps\":{\"options\":[],\"defaultValue\":\"\"},\"propslist\":[\"dataType\",\"required\",\"optionType\",\"styleFlex\",\"disabled\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"],\"dataClass\":\"是否\",\"queryType\":\"api\"}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T18:39:34","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：专业组
{"FIELD_ID":"H81000177","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"PGROUP_ID","FIELD_NAME":"专业组","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":\"DataClass_By_PGROUP_ID\",\"queryType\":\"api\",\"dataType\":\"Select\",\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}]},\"formName\":\"专业组\"}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"xialakuang\",\"icon\":\"iconxialakuang\",\"formName\":\"下拉框\",\"isForm\":true,\"dataType\":\"Select\",\"dataClass\":\"DataClass_By_PGROUP_ID\",\"queryType\":\"api\",\"wigetType\":\"fixed\",\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}]},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-19T15:20:34","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：参加工作日期
{"FIELD_ID":"H81000217","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"WORK_TIME","FIELD_NAME":"参加工作日期","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataType\":\"DatePicker\",\"wigetProps\":{\"button\":false,\"groupKey\":null},\"formName\":\"参加工作日期\",\"rules\":[{\"required\":true}],\"labelHide\":false,\"labelStyle\":null}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"riqi\",\"icon\":\"iconriqi\",\"formName\":\"日期\",\"dataType\":\"DatePicker\",\"isForm\":true,\"wigetProps\":{},\"propslist\":[\"dataType\",\"required\",\"disabled\",\"picker\",\"allowClear\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T21:10:54","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：英语等级
{"FIELD_ID":"H81000190","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"ENGLISH_RANK","FIELD_NAME":"英语等级","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":\"英语级别\",\"queryType\":\"api\",\"dataType\":\"Select\",\"labelHide\":false,\"labelStyle\":null,\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"英语等级\"}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"dataClass\":\"英语级别\",\"queryType\":\"api\",\"dataType\":\"Select\",\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"英语等级\",\"labelHide\":false,\"labelStyle\":null,\"colSpan\":8,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-14T00:04:01","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：登录ID
{"FIELD_ID":"H81000176","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"LOGID","FIELD_NAME":"登录ID","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":\"8\",\"dataClass\":\"人员培训分类\",\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}],\"defaultValue\":\"H818100000052\"},\"formName\":\"登录ID\",\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"dataType\":\"Input\",\"rules\":[{\"required\":null}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}],\"defaultValue\":\"H818100000052\"},\"formName\":\"登录ID\",\"dataClass\":\"人员培训分类\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"colSpan\":8,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-14T00:04:01","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":"登录"}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：备注
{"FIELD_ID":"H81000239","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"BZ63459","FIELD_NAME":"备注","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataType\":\"Input\",\"rules\":[{\"required\":false}],\"wigetProps\":{\"placeholder\":\"请输入\",\"allowClear\":true,\"maxLength\":200,\"disabled\":false},\"formName\":\"备注\",\"colSpan\":8,\"dataClass\":null,\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"danhangwenben\",\"icon\":\"icondanhangwenben\",\"formName\":\"单行文本\",\"dataType\":\"Input\",\"isForm\":true,\"rules\":[{\"required\":false}],\"wigetProps\":{\"placeholder\":\"请输入\",\"allowClear\":true,\"maxLength\":200,\"disabled\":false},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T19:54:32","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：培训合格证书
{"FIELD_ID":"*********","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"PXHGZS25179","FIELD_NAME":"培训合格证书","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataType\":\"Upload\",\"wigetProps\":{\"listType\":\"picture-card\",\"multiple\":true,\"accept\":\".png,.jpg,.jpeg,.pdf,.xls,.xlsx,.doc,.docx\",\"showUploadList\":true},\"formName\":\"培训合格证书\"}","ADDN_JSON":"{\"group\":\"基本\",\"formId\":\"fujian\",\"icon\":\"iconfujian\",\"formName\":\"附件\",\"isForm\":true,\"dataType\":\"Upload\",\"wigetProps\":{\"listType\":\"picture-card\",\"multiple\":true,\"accept\":\".png,.jpg,.jpeg,.pdf,.xls,.xlsx,.doc,.docx\",\"showUploadList\":true},\"propslist\":[\"dataType\",\"required\",\"multiple\",\"listType\",\"accept\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T18:39:41","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：有无子女
{"FIELD_ID":"*********","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"CHILDREN_CONDITION","FIELD_NAME":"有无子女","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataClass\":\"有无子女\",\"queryType\":\"api\",\"dataType\":\"Radio\",\"rules\":[{\"required\":false}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}],\"defaultValue\":\"\"},\"formName\":\"有无子女\"}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"danxuankuang\",\"icon\":\"icondanxuan\",\"formName\":\"单选\",\"isForm\":true,\"dataType\":\"Radio\",\"wigetProps\":{\"options\":[],\"defaultValue\":\"2\"},\"propslist\":[\"dataType\",\"required\",\"optionType\",\"styleFlex\",\"disabled\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"],\"dataClass\":\"有无子女\",\"queryType\":\"api\",\"rules\":[{\"required\":false}]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T19:54:38","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：出生年月
{"FIELD_ID":"H81000175","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"BIRTHDAY","FIELD_NAME":"出生年月","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":null,\"dataType\":\"DatePicker\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"},\"picker\":\"date\"},\"formName\":\"出生年月\",\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"dataType\":\"DatePicker\",\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"},\"picker\":\"date\"},\"formName\":\"出生年月\",\"dataClass\":null,\"labelHide\":false,\"labelStyle\":null,\"rules\":[{\"required\":null}],\"colSpan\":8,\"propslist\":[\"dataType\",\"required\",\"disabled\",\"picker\",\"allowClear\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-14T00:04:01","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：身高
{"FIELD_ID":"H81000195","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"HEIGHT","FIELD_NAME":"身高","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":null,\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":null,\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"身高\"}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"colSpan\":8,\"dataClass\":null,\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":null,\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"身高\",\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-14T00:04:01","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：紧急联系人电话
{"FIELD_ID":"H81000205","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"ECONTACT_PHONE","FIELD_NAME":"紧急联系人电话","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":null,\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":null,\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"紧急联系人电话\"}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"dataType\":\"Input\",\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"紧急联系人电话\",\"dataClass\":null,\"labelHide\":false,\"labelStyle\":null,\"colSpan\":8,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-14T00:04:01","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：户籍所在地
{"FIELD_ID":"H81000183","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"DOMICILE_PLACE","FIELD_NAME":"户籍所在地","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataType\":\"Cascader\",\"rules\":[{\"required\":false}],\"wigetProps\":{\"button\":false,\"groupKey\":null},\"formName\":\"户籍所在地\",\"labelHide\":false,\"labelStyle\":null,\"colSpan\":8,\"dataClass\":null}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"jilianxuanze\",\"icon\":\"iconxialakuang\",\"formName\":\"级联选择\",\"isForm\":true,\"dataType\":\"Cascader\",\"wigetProps\":{},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\"],\"rules\":[{\"required\":false}]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T19:55:12","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：数值
{"FIELD_ID":"H81000258","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"SZ52717","FIELD_NAME":"数值","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataType\":\"InputNumber\",\"rules\":[{\"required\":true}],\"wigetProps\":{\"controls\":true},\"formName\":\"数值\"}","ADDN_JSON":"{\"group\":\"基本\",\"formId\":\"shuzhishurukuang\",\"icon\":\"iconshuzhishurukuang\",\"formName\":\"数值\",\"isForm\":true,\"dataType\":\"InputNumber\",\"wigetProps\":{\"controls\":true},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"min\",\"max\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"controls\"],\"rules\":[{\"required\":true}]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T19:55:18","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：基本信息项
{"FIELD_ID":"H81000250","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"JBXXX75570","FIELD_NAME":"基本信息项","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataType\":\"Input\",\"rules\":[{\"required\":true}],\"wigetProps\":{\"placeholder\":\"请输入\",\"allowClear\":true,\"maxLength\":200,\"disabled\":false},\"formName\":\"基本信息项\"}","ADDN_JSON":"{\"group\":\"基本\",\"formId\":\"danhangwenben\",\"icon\":\"icondanhangwenben\",\"formName\":\"单行文本\",\"dataType\":\"Input\",\"isForm\":true,\"rules\":[{\"required\":true}],\"wigetProps\":{\"placeholder\":\"请输入\",\"allowClear\":true,\"maxLength\":200,\"disabled\":false},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T19:55:23","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：岗位类别
{"FIELD_ID":"H81000241","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"GWLB79071","FIELD_NAME":"岗位类别","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":\"8\",\"dataClass\":\"岗位类别\",\"queryType\":\"api\",\"dataType\":\"Select\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}],\"defaultValue\":\"\"},\"formName\":\"岗位类别\"}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"dataClass\":\"岗位类别\",\"queryType\":\"api\",\"dataType\":\"Select\",\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}],\"defaultValue\":\"\"},\"formName\":\"岗位类别\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"colSpan\":8,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-14T00:04:01","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：工作管理类型
{"FIELD_ID":"H81000252","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"GZGLLX49535","FIELD_NAME":"工作管理类型","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataType\":\"Input\",\"rules\":[{\"required\":true}],\"wigetProps\":{\"placeholder\":\"请输入\",\"allowClear\":true,\"maxLength\":200,\"disabled\":false},\"formName\":\"工作管理类型\"}","ADDN_JSON":"{\"group\":\"基本\",\"formId\":\"danhangwenben\",\"icon\":\"icondanhangwenben\",\"formName\":\"单行文本\",\"dataType\":\"Input\",\"isForm\":true,\"rules\":[{\"required\":true}],\"wigetProps\":{\"placeholder\":\"请输入\",\"allowClear\":true,\"maxLength\":200,\"disabled\":false},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T19:55:27","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：参加培训情况
{"FIELD_ID":"H81000242","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"CJPXQK82201","FIELD_NAME":"参加培训情况","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataType\":\"Input\",\"rules\":[{\"required\":true}],\"wigetProps\":{\"placeholder\":\"请输入\",\"allowClear\":true,\"maxLength\":200,\"disabled\":false},\"formName\":\"参加培训情况\"}","ADDN_JSON":"{\"group\":\"基本\",\"formId\":\"danhangwenben\",\"icon\":\"icondanhangwenben\",\"formName\":\"单行文本\",\"dataType\":\"Input\",\"isForm\":true,\"rules\":[{\"required\":true}],\"wigetProps\":{\"placeholder\":\"请输入\",\"allowClear\":true,\"maxLength\":200,\"disabled\":false},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T18:39:12","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：培训合格证号
{"FIELD_ID":"H81000243","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"PXHGZH48698","FIELD_NAME":"培训合格证号","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataType\":\"Input\",\"rules\":[{\"required\":true}],\"wigetProps\":{\"placeholder\":\"请输入\",\"allowClear\":true,\"maxLength\":200,\"disabled\":false},\"formName\":\"培训合格证号\",\"colSpan\":8,\"dataClass\":null,\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"}}","ADDN_JSON":"{\"group\":\"基本\",\"formId\":\"danhangwenben\",\"icon\":\"icondanhangwenben\",\"formName\":\"单行文本\",\"dataType\":\"Input\",\"isForm\":true,\"rules\":[{\"required\":true}],\"wigetProps\":{\"placeholder\":\"请输入\",\"allowClear\":true,\"maxLength\":200,\"disabled\":false},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T19:23:00","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：新字段
{"FIELD_ID":"H81000245","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"XZD45449","FIELD_NAME":"新字段","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataClass\":\"参会人身份\",\"queryType\":\"api\",\"dataType\":\"Select\",\"wigetProps\":{\"options\":[],\"defaultValue\":\"XBD00000001\"},\"formName\":\"新字段\"}","ADDN_JSON":"{\"group\":\"基本\",\"formId\":\"xialakuang\",\"icon\":\"iconxialakuang\",\"formName\":\"下拉框\",\"isForm\":true,\"dataType\":\"Select\",\"wigetProps\":{\"options\":[],\"defaultValue\":\"XBD00000001\"},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"],\"queryType\":\"api\",\"dataClass\":\"参会人身份\",\"isDataTypeToPerson\":true}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T19:30:33","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：现从事岗位
{"FIELD_ID":"H81000240","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"XCSGW70984","FIELD_NAME":"现从事岗位","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataClass\":\"现从事岗位\",\"queryType\":\"api\",\"dataType\":\"Select\",\"rules\":[{\"required\":false}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}],\"defaultValue\":\"\"},\"formName\":\"现从事岗位\",\"colSpan\":8,\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"xialakuang\",\"icon\":\"iconxialakuang\",\"formName\":\"下拉框\",\"isForm\":true,\"dataType\":\"Select\",\"queryType\":\"api\",\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}],\"defaultValue\":\"\"},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"],\"dataClass\":\"现从事岗位\",\"rules\":[{\"required\":false}]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T19:24:19","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：毕业专业
{"FIELD_ID":"H81000189","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"PROFESSION","FIELD_NAME":"毕业专业","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":null,\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":null,\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"毕业专业\"}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"dataType\":\"Input\",\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"毕业专业\",\"dataClass\":null,\"labelHide\":false,\"labelStyle\":null,\"colSpan\":8,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-14T00:04:01","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：视力（左/右）
{"FIELD_ID":"H81000197","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"EYESIGHT_LEFT","FIELD_NAME":"视力（左/右）","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":\"8\",\"dataClass\":null,\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":null,\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"视力（左/右）\"}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"dataType\":\"Input\",\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"视力（左/右）\",\"dataClass\":null,\"labelHide\":false,\"labelStyle\":null,\"colSpan\":8,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-14T00:04:01","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：颜色选择器
{"FIELD_ID":"H81000264","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"YSXZQ25786","FIELD_NAME":"颜色选择器","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataType\":\"ColorPicker\",\"rules\":[{\"required\":false}],\"wigetProps\":{\"showText\":true,\"allowClear\":true},\"formName\":\"颜色选择器\"}","ADDN_JSON":"{\"group\":\"基本\",\"formId\":\"yansexuanze\",\"icon\":\"iconyanse\",\"formName\":\"颜色选择器\",\"dataType\":\"ColorPicker\",\"isForm\":true,\"wigetProps\":{\"showText\":true,\"allowClear\":true},\"propslist\":[\"dataType\",\"required\",\"disabled\",\"showText\"],\"rules\":[{\"required\":false}]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T19:56:21","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：毕业院校
{"FIELD_ID":"H81000187","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"GRADUATE_SCHOOL","FIELD_NAME":"毕业院校","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataType\":\"Input\",\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"毕业院校\",\"dataClass\":null,\"labelHide\":false,\"labelStyle\":null,\"colSpan\":8}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"danhangwenben\",\"icon\":\"icondanhangwenben\",\"formName\":\"单行文本\",\"dataType\":\"Input\",\"isForm\":true,\"rules\":[{\"required\":false}],\"wigetProps\":{\"placeholder\":\"请输入\",\"allowClear\":true,\"maxLength\":200,\"disabled\":false},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T19:56:06","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：政治面貌
{"FIELD_ID":"H81000179","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"POLITICIAN","FIELD_NAME":"政治面貌","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":\"政治面貌\",\"queryType\":\"api\",\"dataType\":\"Select\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}],\"defaultValue\":\"\"},\"formName\":\"政治面貌\"}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"dataClass\":\"政治面貌\",\"queryType\":\"api\",\"dataType\":\"Select\",\"rules\":[{\"required\":null}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}],\"defaultValue\":\"\"},\"formName\":\"政治面貌\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"colSpan\":8,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-14T00:04:01","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：职务
{"FIELD_ID":"H81000208","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"DUTIES","FIELD_NAME":"职务","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":\"职务\",\"queryType\":\"api\",\"dataType\":\"Select\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}],\"defaultValue\":\"\"},\"formName\":\"职务\"}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"dataClass\":\"职务\",\"queryType\":\"api\",\"dataType\":\"Select\",\"rules\":[{\"required\":null}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}],\"defaultValue\":\"\"},\"formName\":\"职务\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"colSpan\":8,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-14T00:04:01","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：岗位类别
{"FIELD_ID":"H81000267","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"GWLB57036","FIELD_NAME":"岗位类别","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataClass\":\"岗位类别\",\"queryType\":\"api\",\"dataType\":\"Select\",\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}],\"defaultValue\":\"\"},\"formName\":\"岗位类别\"}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"xialakuang\",\"icon\":\"iconxialakuang\",\"formName\":\"下拉框\",\"isForm\":true,\"dataType\":\"Select\",\"queryType\":\"api\",\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}],\"defaultValue\":\"\"},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"],\"dataClass\":\"岗位类别\"}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T19:57:02","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：英语级别/成绩（分）
{"FIELD_ID":"H81000191","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"ENGLISH_RANK_SCORE","FIELD_NAME":"英语级别/成绩（分）","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataType\":\"InputNumber\",\"wigetProps\":{\"controls\":true},\"formName\":\"英语级别/成绩（分）\"}","ADDN_JSON":"{\"group\":\"基本\",\"formId\":\"shuzhishurukuang\",\"icon\":\"iconshuzhishurukuang\",\"formName\":\"数值\",\"isForm\":true,\"dataType\":\"InputNumber\",\"wigetProps\":{\"controls\":true},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"min\",\"max\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"controls\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-27T14:31:53","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-27T14:32:11","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：颜色视觉障碍
{"FIELD_ID":"H81000194","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"COLOR_DEFICIENCY","FIELD_NAME":"颜色视觉障碍","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataClass\":\"英语级别\",\"dataType\":\"Select\",\"queryType\":\"enum\",\"rules\":[{\"required\":false}],\"wigetProps\":{\"options\":[{\"label\":\"正常\",\"value\":\"0\"},{\"label\":\"色弱\",\"value\":\"1\"},{\"label\":\"色盲\",\"value\":\"2\"}]},\"formName\":\"颜色视觉障碍\"}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"colSpan\":\"8\",\"dataClass\":\"英语级别\",\"queryType\":\"api\",\"dataType\":\"Select\",\"rules\":[{\"required\":false}],\"wigetProps\":{\"options\":[{\"label\":\"正常\",\"value\":\"0\"},{\"label\":\"色弱\",\"value\":\"1\"},{\"label\":\"色盲\",\"value\":\"2\"}]},\"formName\":\"英语级别/成绩(分)\",\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"]}","FIELD_STATE":"1","FIRST_RPERSON":"ldd_李丹丹","FIRST_RTIME":"2025-06-18T19:13:00","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：英语级别/成绩(分)
{"FIELD_ID":"H81000270","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"YYJB50394","FIELD_NAME":"英语级别/成绩(分)","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":\"8\",\"dataClass\":\"英语级别\",\"queryType\":\"api\",\"dataType\":\"Select\",\"rules\":[{\"required\":false}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}],\"defaultValue\":\"\"},\"formName\":\"英语级别/成绩(分)\"}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"dataClass\":\"英语级别\",\"queryType\":\"api\",\"dataType\":\"Select\",\"rules\":[{\"required\":false}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}],\"defaultValue\":\"\"},\"formName\":\"英语级别/成绩(分)\",\"colSpan\":8,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-14T00:04:01","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：民族
{"FIELD_ID":"H81000173","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"NATION","FIELD_NAME":"民族","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":\"民族\",\"queryType\":\"api\",\"dataType\":\"Select\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":true}],\"wigetProps\":{\"options\":[]},\"formName\":\"民族\"}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"dataClass\":\"民族\",\"queryType\":\"api\",\"dataType\":\"Select\",\"rules\":[{\"required\":true}],\"wigetProps\":{\"options\":[]},\"formName\":\"下拉框\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"colSpan\":8,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-13T22:51:04","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：邮箱
{"FIELD_ID":"H81000201","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"E_MAIL","FIELD_NAME":"邮箱","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":null,\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":{\"color\":\"rgb(0,0,0)\"},\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"邮箱\"}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"dataType\":\"Input\",\"rules\":[{\"required\":null}],\"wigetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"formName\":\"邮箱\",\"dataClass\":null,\"labelHide\":false,\"labelStyle\":null,\"colSpan\":8,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-14T00:04:01","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：姓名
{"FIELD_ID":"H81000171","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"USER_NAME","FIELD_NAME":"姓名","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":null,\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}]},\"formName\":\"姓名\",\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}}}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"colSpan\":8,\"dataType\":\"Input\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}]},\"formName\":\"姓名AAA\",\"dataClass\":null,\"widgetProps\":{\"disabled\":false,\"style\":{\"color\":null,\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"left\",\"justifyContent\":\"left\"}},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"ldd_李丹丹","FIRST_RTIME":"2025-06-18T19:13:00","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：健康状况
{"FIELD_ID":"H81000198","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"HEALTH","FIELD_NAME":"健康状况","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"colSpan\":8,\"dataClass\":\"健康状况\",\"queryType\":\"api\",\"dataType\":\"Select\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}],\"defaultValue\":\"\"},\"formName\":\"健康状况\"}","ADDN_JSON":"{\"dataClass\":\"健康状况\",\"queryType\":\"api\",\"dataType\":\"Select\",\"wigetProps\":{\"options\":[{\"value\":\"选项1\",\"label\":\"选项1\"},{\"value\":\"选项2\",\"label\":\"选项2\"},{\"value\":\"选项3\",\"label\":\"选项3\"}],\"defaultValue\":\"\"},\"formName\":\"健康状况\",\"labelHide\":false,\"labelStyle\":{\"color\":\"#000000\",\"fontSize\":13,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"background\":null,\"textAlign\":\"right\",\"justifyContent\":\"right\"},\"rules\":[{\"required\":null}],\"colSpan\":8,\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"prefix\",\"suffix\",\"variant\",\"placeholder\",\"disabled\",\"showSearch\",\"mode\",\"queryType\",\"dataClass\",\"defaultValue\",\"isDataTypeToPerson\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-14T00:04:01","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：来院日期
{"FIELD_ID":"H81000220","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"IN_HOSPITAL_TIME","FIELD_NAME":"来院日期","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataType\":\"DatePicker\",\"wigetProps\":{\"button\":false,\"groupKey\":null},\"formName\":\"来院日期\",\"labelHide\":false,\"labelStyle\":null}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"riqi\",\"icon\":\"iconriqi\",\"formName\":\"日期\",\"dataType\":\"DatePicker\",\"isForm\":true,\"wigetProps\":{},\"propslist\":[\"dataType\",\"required\",\"disabled\",\"picker\",\"allowClear\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T20:54:43","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.H81.Models.Entities.SYS6_FUNC_FIELD_DICT, XH.H81.Models##INSERT##共公字段数据：院龄
{"FIELD_ID":"H81000222","HOSPITAL_ID":"33A001","MODULE_ID":"H81","FIELD_CLASS":"基本信息","FIELD_CODE":"LENGTH_HOSPITAL","FIELD_NAME":"院龄","READONLY_JSON":"[\"dataType\",\"queryType\",\"dataClass\"]","STYLE_JSON":"{\"dataType\":\"Input\",\"rules\":[{\"required\":true}],\"wigetProps\":{\"button\":false,\"groupKey\":null},\"formName\":\"院龄\",\"labelHide\":false,\"labelStyle\":null}","ADDN_JSON":"{\"readOnlyEnum\":\"1\",\"group\":\"基本\",\"formId\":\"danhangwenben\",\"icon\":\"icondanhangwenben\",\"formName\":\"单行文本\",\"dataType\":\"Input\",\"isForm\":true,\"rules\":[{\"required\":true}],\"wigetProps\":{\"placeholder\":\"请输入\",\"allowClear\":true,\"maxLength\":200,\"disabled\":false},\"propslist\":[\"dataType\",\"required\",\"allowClear\",\"placeholder\",\"maxLength\",\"prefix\",\"suffix\",\"variant\",\"disabled\",\"showCount\"]}","FIELD_STATE":"1","FIRST_RPERSON":"fr_李影","FIRST_RTIME":"2025-06-12T18:39:19","LAST_MPERSON":"fr_李影","LAST_MTIME":"2025-06-19T16:34:31","REMARK":null}
XH.LAB.UTILS.Models.SYS6_MENU, XH.LAB.UTILS##UPDATE:MENU_SORT,MENU_NAME,MENU_ICON,MENU_URL##更新菜单排序：实验室人员管理(.net调用)
{"MENU_ID":"H81-0000000","FIRST_RPERSON":"新工具箱","REFRESH_TYPE":null,"FONT_COLOR":null,"BACKGROUND_COLOR":null,"MENU_NAME":"实验室人员管理(.net调用)","LAST_MTIME":null,"MENU_MODULE":null,"RIGHT_MENU":null,"IF_ASSIST_FUNC":null,"IF_RIGHT_MENU":null,"MENU_TYPE":null,"TOOLBAR_SORT":null,"IF_TOOLBAR":null,"PARENT_CODE":"root","SYS_MENU":"H81-00","MENU_ICON":"True","SHORTCUT_KEY":null,"MENU_SORT":"00000","MENU_ENAME":null,"MENU_STATE":"1","MENU_CODE":"H81-0000000","MENU_URL":null,"RIGHT_MENU_SORT":null,"REMARK":null,"MENU_SNAME":null,"IF_EXEC_AFTER":null,"FIRST_RTIME":"2025-05-08T09:59:07","LAST_MPERSON":null,"EXEC_AFTER_FUNC":null,"MENU_CLASS":"nav_menu","TOOLBAR_SNAME":null,"MENU_LEVEL":"0","MENU_BNAME":"实验室人员管理(.net调用)","ASSIST_FUNC":null,"TOP_INFO_CLASS":null}
XH.LAB.UTILS.Models.SYS6_MENU, XH.LAB.UTILS##UPDATE:MENU_SORT,MENU_NAME,MENU_ICON,MENU_URL##更新菜单排序：记录审核
{"MENU_ID":"H8109","FIRST_RPERSON":"33A001_杏和检验医疗","REFRESH_TYPE":null,"FONT_COLOR":null,"BACKGROUND_COLOR":null,"MENU_NAME":"记录审核","LAST_MTIME":"2025-05-30T14:44:35","MENU_MODULE":null,"RIGHT_MENU":null,"IF_ASSIST_FUNC":null,"IF_RIGHT_MENU":null,"MENU_TYPE":"1","TOOLBAR_SORT":null,"IF_TOOLBAR":null,"PARENT_CODE":"H81","SYS_MENU":"H81","MENU_ICON":"iconxianshi","SHORTCUT_KEY":null,"MENU_SORT":"004","MENU_ENAME":null,"MENU_STATE":"1","MENU_CODE":"H8109","MENU_URL":"/record-review","RIGHT_MENU_SORT":null,"REMARK":null,"MENU_SNAME":null,"IF_EXEC_AFTER":null,"FIRST_RTIME":"2025-05-30T14:44:39","LAST_MPERSON":null,"EXEC_AFTER_FUNC":null,"MENU_CLASS":"nav_menu","TOOLBAR_SNAME":null,"MENU_LEVEL":"1","MENU_BNAME":"记录审核","ASSIST_FUNC":null,"TOP_INFO_CLASS":null}
XH.LAB.UTILS.Models.SYS6_MENU, XH.LAB.UTILS##UPDATE:MENU_SORT,MENU_NAME,MENU_ICON,MENU_URL##更新菜单排序：系统设置
{"MENU_ID":"H8110","FIRST_RPERSON":"33A001_杏和检验医疗","REFRESH_TYPE":null,"FONT_COLOR":null,"BACKGROUND_COLOR":null,"MENU_NAME":"系统设置","LAST_MTIME":"2025-05-30T14:44:35","MENU_MODULE":null,"RIGHT_MENU":null,"IF_ASSIST_FUNC":null,"IF_RIGHT_MENU":null,"MENU_TYPE":"1","TOOLBAR_SORT":null,"IF_TOOLBAR":null,"PARENT_CODE":"H81","SYS_MENU":"H81","MENU_ICON":"iconshezhi","SHORTCUT_KEY":null,"MENU_SORT":"010","MENU_ENAME":null,"MENU_STATE":"1","MENU_CODE":"H8110","MENU_URL":null,"RIGHT_MENU_SORT":null,"REMARK":null,"MENU_SNAME":null,"IF_EXEC_AFTER":null,"FIRST_RTIME":"2025-05-30T14:44:39","LAST_MPERSON":null,"EXEC_AFTER_FUNC":null,"MENU_CLASS":"nav_menu","TOOLBAR_SNAME":null,"MENU_LEVEL":"1","MENU_BNAME":"设置","ASSIST_FUNC":null,"TOP_INFO_CLASS":null}
XH.LAB.UTILS.Models.SYS6_MENU, XH.LAB.UTILS##UPDATE:MENU_SORT,MENU_NAME,MENU_ICON,MENU_URL##更新菜单排序：人员规评登记
{"MENU_ID":"H8111","FIRST_RPERSON":"33A001_杏和检验医疗","REFRESH_TYPE":null,"FONT_COLOR":null,"BACKGROUND_COLOR":null,"MENU_NAME":"人员规评登记","LAST_MTIME":"2025-05-30T14:44:35","MENU_MODULE":null,"RIGHT_MENU":null,"IF_ASSIST_FUNC":null,"IF_RIGHT_MENU":null,"MENU_TYPE":"1","TOOLBAR_SORT":null,"IF_TOOLBAR":null,"PARENT_CODE":"H81","SYS_MENU":"H81","MENU_ICON":"iconrenyuanguipingdengji","SHORTCUT_KEY":null,"MENU_SORT":"006","MENU_ENAME":null,"MENU_STATE":"1","MENU_CODE":"H8111","MENU_URL":null,"RIGHT_MENU_SORT":null,"REMARK":null,"MENU_SNAME":null,"IF_EXEC_AFTER":null,"FIRST_RTIME":"2025-05-30T14:44:39","LAST_MPERSON":null,"EXEC_AFTER_FUNC":null,"MENU_CLASS":"nav_menu","TOOLBAR_SNAME":null,"MENU_LEVEL":"1","MENU_BNAME":"人员规评登记","ASSIST_FUNC":null,"TOP_INFO_CLASS":null}
XH.LAB.UTILS.Models.SYS6_MENU, XH.LAB.UTILS##UPDATE:MENU_SORT,MENU_NAME,MENU_ICON,MENU_URL##更新菜单排序：规评管理
{"MENU_ID":"H8112","FIRST_RPERSON":"33A001_杏和检验医疗","REFRESH_TYPE":null,"FONT_COLOR":null,"BACKGROUND_COLOR":null,"MENU_NAME":"规评管理","LAST_MTIME":"2025-05-30T14:44:35","MENU_MODULE":null,"RIGHT_MENU":null,"IF_ASSIST_FUNC":null,"IF_RIGHT_MENU":null,"MENU_TYPE":"1","TOOLBAR_SORT":null,"IF_TOOLBAR":null,"PARENT_CODE":"H81","SYS_MENU":"H81","MENU_ICON":"iconguipingguanli","SHORTCUT_KEY":null,"MENU_SORT":"005","MENU_ENAME":null,"MENU_STATE":"1","MENU_CODE":"H8112","MENU_URL":null,"RIGHT_MENU_SORT":null,"REMARK":null,"MENU_SNAME":null,"IF_EXEC_AFTER":null,"FIRST_RTIME":"2025-05-30T14:44:39","LAST_MPERSON":null,"EXEC_AFTER_FUNC":null,"MENU_CLASS":"nav_menu","TOOLBAR_SNAME":null,"MENU_LEVEL":"1","MENU_BNAME":"规评管理","ASSIST_FUNC":null,"TOP_INFO_CLASS":null}
XH.LAB.UTILS.Models.SYS6_MENU, XH.LAB.UTILS##UPDATE:MENU_SORT,MENU_NAME,MENU_ICON,MENU_URL##更新菜单排序：查询分析
{"MENU_ID":"H8113","FIRST_RPERSON":"33A001_杏和检验医疗","REFRESH_TYPE":null,"FONT_COLOR":null,"BACKGROUND_COLOR":null,"MENU_NAME":"查询分析","LAST_MTIME":"2025-05-30T14:44:35","MENU_MODULE":null,"RIGHT_MENU":null,"IF_ASSIST_FUNC":null,"IF_RIGHT_MENU":null,"MENU_TYPE":"1","TOOLBAR_SORT":null,"IF_TOOLBAR":null,"PARENT_CODE":"H81","SYS_MENU":"H81","MENU_ICON":"iconchaxunfenxi1","SHORTCUT_KEY":null,"MENU_SORT":"009","MENU_ENAME":null,"MENU_STATE":"1","MENU_CODE":"H8113","MENU_URL":"/query-analysis","RIGHT_MENU_SORT":null,"REMARK":null,"MENU_SNAME":null,"IF_EXEC_AFTER":null,"FIRST_RTIME":"2025-05-30T14:44:39","LAST_MPERSON":null,"EXEC_AFTER_FUNC":null,"MENU_CLASS":"nav_menu","TOOLBAR_SNAME":null,"MENU_LEVEL":"1","MENU_BNAME":"查询分析","ASSIST_FUNC":null,"TOP_INFO_CLASS":null}
XH.LAB.UTILS.Models.SYS6_MENU, XH.LAB.UTILS##UPDATE:MENU_SORT,MENU_NAME,MENU_ICON,MENU_URL##更新菜单排序：门禁管理
{"MENU_ID":"H8114","FIRST_RPERSON":"33A001_杏和检验医疗","REFRESH_TYPE":null,"FONT_COLOR":null,"BACKGROUND_COLOR":null,"MENU_NAME":"门禁管理","LAST_MTIME":"2025-05-30T14:44:35","MENU_MODULE":null,"RIGHT_MENU":null,"IF_ASSIST_FUNC":null,"IF_RIGHT_MENU":null,"MENU_TYPE":"1","TOOLBAR_SORT":null,"IF_TOOLBAR":null,"PARENT_CODE":"H81","SYS_MENU":"H81","MENU_ICON":"iconmenjin   ","SHORTCUT_KEY":null,"MENU_SORT":"008","MENU_ENAME":null,"MENU_STATE":"1","MENU_CODE":"H8114","MENU_URL":null,"RIGHT_MENU_SORT":null,"REMARK":null,"MENU_SNAME":null,"IF_EXEC_AFTER":null,"FIRST_RTIME":"2025-05-30T14:44:39","LAST_MPERSON":null,"EXEC_AFTER_FUNC":null,"MENU_CLASS":"nav_menu","TOOLBAR_SNAME":null,"MENU_LEVEL":"1","MENU_BNAME":"门禁管理","ASSIST_FUNC":null,"TOP_INFO_CLASS":null}
XH.LAB.UTILS.Models.SYS6_MENU, XH.LAB.UTILS##UPDATE:MENU_SORT,MENU_NAME,MENU_ICON,MENU_URL##更新菜单排序：人员标签管理
{"MENU_ID":"H8115","FIRST_RPERSON":"33A001_杏和检验医疗","REFRESH_TYPE":null,"FONT_COLOR":null,"BACKGROUND_COLOR":null,"MENU_NAME":"人员标签管理","LAST_MTIME":"2025-05-30T14:44:35","MENU_MODULE":null,"RIGHT_MENU":null,"IF_ASSIST_FUNC":null,"IF_RIGHT_MENU":null,"MENU_TYPE":"1","TOOLBAR_SORT":null,"IF_TOOLBAR":null,"PARENT_CODE":"H81","SYS_MENU":"H81","MENU_ICON":"icona-0_jilushenhe","SHORTCUT_KEY":null,"MENU_SORT":"007","MENU_ENAME":null,"MENU_STATE":"1","MENU_CODE":"H8115","MENU_URL":"/person-tag","RIGHT_MENU_SORT":null,"REMARK":null,"MENU_SNAME":null,"IF_EXEC_AFTER":null,"FIRST_RTIME":"2025-05-30T14:44:39","LAST_MPERSON":null,"EXEC_AFTER_FUNC":null,"MENU_CLASS":"nav_menu","TOOLBAR_SNAME":null,"MENU_LEVEL":"1","MENU_BNAME":"人员标签管理","ASSIST_FUNC":null,"TOP_INFO_CLASS":null}
XH.LAB.UTILS.Models.SYS6_MENU, XH.LAB.UTILS##UPDATE:MENU_SORT,MENU_NAME,MENU_ICON,MENU_URL##更新菜单排序：人事管理
{"MENU_ID":"H81","FIRST_RPERSON":"33A001_杏和检验医疗","REFRESH_TYPE":null,"FONT_COLOR":null,"BACKGROUND_COLOR":null,"MENU_NAME":"人事管理","LAST_MTIME":"2025-05-30T14:44:35","MENU_MODULE":null,"RIGHT_MENU":null,"IF_ASSIST_FUNC":null,"IF_RIGHT_MENU":null,"MENU_TYPE":"1","TOOLBAR_SORT":null,"IF_TOOLBAR":null,"PARENT_CODE":"root","SYS_MENU":"H81","MENU_ICON":"False","SHORTCUT_KEY":null,"MENU_SORT":"H81","MENU_ENAME":null,"MENU_STATE":"1","MENU_CODE":"H81","MENU_URL":"/OA/PMS/CenterOrganizationStructure","RIGHT_MENU_SORT":null,"REMARK":null,"MENU_SNAME":null,"IF_EXEC_AFTER":null,"FIRST_RTIME":"2025-05-30T14:44:39","LAST_MPERSON":null,"EXEC_AFTER_FUNC":null,"MENU_CLASS":"nav_menu","TOOLBAR_SNAME":null,"MENU_LEVEL":"0","MENU_BNAME":"人事管理","ASSIST_FUNC":null,"TOP_INFO_CLASS":null}
XH.LAB.UTILS.Models.SYS6_MENU, XH.LAB.UTILS##UPDATE:MENU_SORT,MENU_NAME,MENU_ICON,MENU_URL##更新菜单排序：中心组织架构
{"MENU_ID":"H8101","FIRST_RPERSON":"33A001_杏和检验医疗","REFRESH_TYPE":null,"FONT_COLOR":null,"BACKGROUND_COLOR":null,"MENU_NAME":"中心组织架构","LAST_MTIME":"2025-05-30T14:44:35","MENU_MODULE":null,"RIGHT_MENU":null,"IF_ASSIST_FUNC":null,"IF_RIGHT_MENU":null,"MENU_TYPE":"1","TOOLBAR_SORT":null,"IF_TOOLBAR":null,"PARENT_CODE":"H81","SYS_MENU":"H81","MENU_ICON":"iconpaizhao","SHORTCUT_KEY":null,"MENU_SORT":"001","MENU_ENAME":null,"MENU_STATE":"1","MENU_CODE":"H8101","MENU_URL":"/center-org-structure","RIGHT_MENU_SORT":null,"REMARK":null,"MENU_SNAME":null,"IF_EXEC_AFTER":null,"FIRST_RTIME":"2025-05-30T14:44:39","LAST_MPERSON":null,"EXEC_AFTER_FUNC":null,"MENU_CLASS":"nav_menu","TOOLBAR_SNAME":null,"MENU_LEVEL":"1","MENU_BNAME":"中心组织架构","ASSIST_FUNC":null,"TOP_INFO_CLASS":null}
XH.LAB.UTILS.Models.SYS6_MENU, XH.LAB.UTILS##UPDATE:MENU_SORT,MENU_NAME,MENU_ICON,MENU_URL##更新菜单排序：人员结构分析
{"MENU_ID":"H8102","FIRST_RPERSON":"33A001_杏和检验医疗","REFRESH_TYPE":null,"FONT_COLOR":null,"BACKGROUND_COLOR":null,"MENU_NAME":"人员结构分析","LAST_MTIME":"2025-05-30T14:44:35","MENU_MODULE":null,"RIGHT_MENU":null,"IF_ASSIST_FUNC":null,"IF_RIGHT_MENU":null,"MENU_TYPE":"1","TOOLBAR_SORT":null,"IF_TOOLBAR":null,"PARENT_CODE":"H81","SYS_MENU":"H81","MENU_ICON":"icongongzuoshijian","SHORTCUT_KEY":null,"MENU_SORT":"002","MENU_ENAME":null,"MENU_STATE":"1","MENU_CODE":"H8102","MENU_URL":"/staff-structure-analysis","RIGHT_MENU_SORT":null,"REMARK":null,"MENU_SNAME":null,"IF_EXEC_AFTER":null,"FIRST_RTIME":"2025-05-30T14:44:39","LAST_MPERSON":null,"EXEC_AFTER_FUNC":null,"MENU_CLASS":"nav_menu","TOOLBAR_SNAME":null,"MENU_LEVEL":"1","MENU_BNAME":"人员结构分析","ASSIST_FUNC":null,"TOP_INFO_CLASS":null}
XH.LAB.UTILS.Models.SYS6_MENU, XH.LAB.UTILS##UPDATE:MENU_SORT,MENU_NAME,MENU_ICON,MENU_URL##更新菜单排序：人事档案
{"MENU_ID":"H8107","FIRST_RPERSON":"33A001_杏和检验医疗","REFRESH_TYPE":null,"FONT_COLOR":null,"BACKGROUND_COLOR":null,"MENU_NAME":"人事档案","LAST_MTIME":"2025-05-30T14:44:35","MENU_MODULE":null,"RIGHT_MENU":null,"IF_ASSIST_FUNC":null,"IF_RIGHT_MENU":null,"MENU_TYPE":"1","TOOLBAR_SORT":null,"IF_TOOLBAR":"0","PARENT_CODE":"H81","SYS_MENU":"H81","MENU_ICON":"iconshiyanshishebeiguanli","SHORTCUT_KEY":null,"MENU_SORT":"003","MENU_ENAME":null,"MENU_STATE":"1","MENU_CODE":"H8107","MENU_URL":"/personnel-archives","RIGHT_MENU_SORT":null,"REMARK":null,"MENU_SNAME":null,"IF_EXEC_AFTER":null,"FIRST_RTIME":"2025-05-30T14:44:39","LAST_MPERSON":null,"EXEC_AFTER_FUNC":null,"MENU_CLASS":"nav_menu","TOOLBAR_SNAME":null,"MENU_LEVEL":"1","MENU_BNAME":"人事档案","ASSIST_FUNC":null,"TOP_INFO_CLASS":null}
XH.LAB.UTILS.Models.SYS6_MENU, XH.LAB.UTILS##UPDATE:MENU_SORT,MENU_NAME,MENU_ICON,MENU_URL##更新菜单排序：分类设置
{"MENU_ID":"H811001","FIRST_RPERSON":"33A001_杏和检验医疗","REFRESH_TYPE":null,"FONT_COLOR":null,"BACKGROUND_COLOR":null,"MENU_NAME":"分类设置","LAST_MTIME":"2025-05-30T14:44:35","MENU_MODULE":null,"RIGHT_MENU":null,"IF_ASSIST_FUNC":null,"IF_RIGHT_MENU":null,"MENU_TYPE":"1","TOOLBAR_SORT":null,"IF_TOOLBAR":null,"PARENT_CODE":"H8110","SYS_MENU":"H81","MENU_ICON":null,"SHORTCUT_KEY":null,"MENU_SORT":"H811001","MENU_ENAME":null,"MENU_STATE":"1","MENU_CODE":"H811001","MENU_URL":"/classification-settings","RIGHT_MENU_SORT":null,"REMARK":null,"MENU_SNAME":null,"IF_EXEC_AFTER":null,"FIRST_RTIME":"2025-05-30T14:44:39","LAST_MPERSON":null,"EXEC_AFTER_FUNC":null,"MENU_CLASS":"nav_menu","TOOLBAR_SNAME":null,"MENU_LEVEL":"1","MENU_BNAME":"分类设置","ASSIST_FUNC":null,"TOP_INFO_CLASS":null}
XH.LAB.UTILS.Models.SYS6_MENU, XH.LAB.UTILS##UPDATE:MENU_SORT,MENU_NAME,MENU_ICON,MENU_URL##更新菜单排序：数据项设置
{"MENU_ID":"H811002","FIRST_RPERSON":"33A001_杏和检验医疗","REFRESH_TYPE":null,"FONT_COLOR":null,"BACKGROUND_COLOR":null,"MENU_NAME":"数据项设置","LAST_MTIME":"2025-05-30T14:44:35","MENU_MODULE":null,"RIGHT_MENU":null,"IF_ASSIST_FUNC":null,"IF_RIGHT_MENU":null,"MENU_TYPE":"1","TOOLBAR_SORT":null,"IF_TOOLBAR":null,"PARENT_CODE":"H8110","SYS_MENU":"H81","MENU_ICON":null,"SHORTCUT_KEY":null,"MENU_SORT":"H811002","MENU_ENAME":null,"MENU_STATE":"1","MENU_CODE":"H811002","MENU_URL":"/dataItem-settings","RIGHT_MENU_SORT":null,"REMARK":null,"MENU_SNAME":null,"IF_EXEC_AFTER":null,"FIRST_RTIME":"2025-05-30T14:44:39","LAST_MPERSON":null,"EXEC_AFTER_FUNC":null,"MENU_CLASS":"nav_menu","TOOLBAR_SNAME":null,"MENU_LEVEL":"1","MENU_BNAME":"数据项设置","ASSIST_FUNC":null,"TOP_INFO_CLASS":null}
XH.LAB.UTILS.Models.SYS6_MENU, XH.LAB.UTILS##UPDATE:MENU_SORT,MENU_NAME,MENU_ICON,MENU_URL##更新菜单排序：评估规评登记
{"MENU_ID":"H811102","FIRST_RPERSON":"33A001_杏和检验医疗","REFRESH_TYPE":null,"FONT_COLOR":null,"BACKGROUND_COLOR":null,"MENU_NAME":"评估规评登记","LAST_MTIME":"2025-05-30T14:44:35","MENU_MODULE":null,"RIGHT_MENU":null,"IF_ASSIST_FUNC":null,"IF_RIGHT_MENU":null,"MENU_TYPE":"1","TOOLBAR_SORT":null,"IF_TOOLBAR":null,"PARENT_CODE":"H8111","SYS_MENU":"H81","MENU_ICON":null,"SHORTCUT_KEY":null,"MENU_SORT":"H811102","MENU_ENAME":null,"MENU_STATE":"1","MENU_CODE":"H811102","MENU_URL":"/assess-eplan","RIGHT_MENU_SORT":null,"REMARK":null,"MENU_SNAME":null,"IF_EXEC_AFTER":null,"FIRST_RTIME":"2025-05-30T14:44:39","LAST_MPERSON":null,"EXEC_AFTER_FUNC":null,"MENU_CLASS":"nav_menu","TOOLBAR_SNAME":null,"MENU_LEVEL":"1","MENU_BNAME":"评估规评登记","ASSIST_FUNC":null,"TOP_INFO_CLASS":null}
XH.LAB.UTILS.Models.SYS6_MENU, XH.LAB.UTILS##UPDATE:MENU_SORT,MENU_NAME,MENU_ICON,MENU_URL##更新菜单排序：考试规评登记
{"MENU_ID":"H811103","FIRST_RPERSON":"33A001_杏和检验医疗","REFRESH_TYPE":null,"FONT_COLOR":null,"BACKGROUND_COLOR":null,"MENU_NAME":"考试规评登记","LAST_MTIME":"2025-05-30T14:44:35","MENU_MODULE":null,"RIGHT_MENU":null,"IF_ASSIST_FUNC":null,"IF_RIGHT_MENU":null,"MENU_TYPE":"1","TOOLBAR_SORT":null,"IF_TOOLBAR":null,"PARENT_CODE":"H8111","SYS_MENU":"H81","MENU_ICON":null,"SHORTCUT_KEY":null,"MENU_SORT":"H811103","MENU_ENAME":null,"MENU_STATE":"1","MENU_CODE":"H811103","MENU_URL":"/exam-eplan","RIGHT_MENU_SORT":null,"REMARK":null,"MENU_SNAME":null,"IF_EXEC_AFTER":null,"FIRST_RTIME":"2025-05-30T14:44:39","LAST_MPERSON":null,"EXEC_AFTER_FUNC":null,"MENU_CLASS":"nav_menu","TOOLBAR_SNAME":null,"MENU_LEVEL":"1","MENU_BNAME":"考试规评登记","ASSIST_FUNC":null,"TOP_INFO_CLASS":null}
XH.LAB.UTILS.Models.SYS6_MENU, XH.LAB.UTILS##UPDATE:MENU_SORT,MENU_NAME,MENU_ICON,MENU_URL##更新菜单排序：证书规评登记
{"MENU_ID":"H811104","FIRST_RPERSON":"33A001_杏和检验医疗","REFRESH_TYPE":null,"FONT_COLOR":null,"BACKGROUND_COLOR":null,"MENU_NAME":"证书规评登记","LAST_MTIME":"2025-05-30T14:44:35","MENU_MODULE":null,"RIGHT_MENU":null,"IF_ASSIST_FUNC":null,"IF_RIGHT_MENU":null,"MENU_TYPE":"1","TOOLBAR_SORT":null,"IF_TOOLBAR":null,"PARENT_CODE":"H8111","SYS_MENU":"H81","MENU_ICON":null,"SHORTCUT_KEY":null,"MENU_SORT":"H811104","MENU_ENAME":null,"MENU_STATE":"1","MENU_CODE":"H811104","MENU_URL":"/certificate-eplan","RIGHT_MENU_SORT":null,"REMARK":null,"MENU_SNAME":null,"IF_EXEC_AFTER":null,"FIRST_RTIME":"2025-05-30T14:44:39","LAST_MPERSON":null,"EXEC_AFTER_FUNC":null,"MENU_CLASS":"nav_menu","TOOLBAR_SNAME":null,"MENU_LEVEL":"1","MENU_BNAME":"证书规评登记","ASSIST_FUNC":null,"TOP_INFO_CLASS":null}
XH.LAB.UTILS.Models.SYS6_MENU, XH.LAB.UTILS##UPDATE:MENU_SORT,MENU_NAME,MENU_ICON,MENU_URL##更新菜单排序：限权管理
{"MENU_ID":"H811201","FIRST_RPERSON":"33A001_杏和检验医疗","REFRESH_TYPE":null,"FONT_COLOR":null,"BACKGROUND_COLOR":null,"MENU_NAME":"限权管理","LAST_MTIME":"2025-05-30T14:44:35","MENU_MODULE":null,"RIGHT_MENU":null,"IF_ASSIST_FUNC":null,"IF_RIGHT_MENU":null,"MENU_TYPE":"1","TOOLBAR_SORT":null,"IF_TOOLBAR":null,"PARENT_CODE":"H8112","SYS_MENU":"H81","MENU_ICON":null,"SHORTCUT_KEY":null,"MENU_SORT":"H811201","MENU_ENAME":null,"MENU_STATE":"1","MENU_CODE":"H811201","MENU_URL":"/restricted-rights","RIGHT_MENU_SORT":null,"REMARK":null,"MENU_SNAME":null,"IF_EXEC_AFTER":null,"FIRST_RTIME":"2025-05-30T14:44:39","LAST_MPERSON":null,"EXEC_AFTER_FUNC":null,"MENU_CLASS":"nav_menu","TOOLBAR_SNAME":null,"MENU_LEVEL":"1","MENU_BNAME":"限权管理","ASSIST_FUNC":null,"TOP_INFO_CLASS":null}
XH.LAB.UTILS.Models.SYS6_MENU, XH.LAB.UTILS##UPDATE:MENU_SORT,MENU_NAME,MENU_ICON,MENU_URL##更新菜单排序：规评设置
{"MENU_ID":"H811202","FIRST_RPERSON":"33A001_杏和检验医疗","REFRESH_TYPE":null,"FONT_COLOR":null,"BACKGROUND_COLOR":null,"MENU_NAME":"规评设置","LAST_MTIME":"2025-05-30T14:44:35","MENU_MODULE":null,"RIGHT_MENU":null,"IF_ASSIST_FUNC":null,"IF_RIGHT_MENU":null,"MENU_TYPE":"1","TOOLBAR_SORT":null,"IF_TOOLBAR":null,"PARENT_CODE":"H8112","SYS_MENU":"H81","MENU_ICON":null,"SHORTCUT_KEY":null,"MENU_SORT":"H811202","MENU_ENAME":null,"MENU_STATE":"1","MENU_CODE":"H811202","MENU_URL":"/eplan-settings","RIGHT_MENU_SORT":null,"REMARK":null,"MENU_SNAME":null,"IF_EXEC_AFTER":null,"FIRST_RTIME":"2025-05-30T14:44:39","LAST_MPERSON":null,"EXEC_AFTER_FUNC":null,"MENU_CLASS":"nav_menu","TOOLBAR_SNAME":null,"MENU_LEVEL":"1","MENU_BNAME":"规评设置","ASSIST_FUNC":null,"TOP_INFO_CLASS":null}
XH.LAB.UTILS.Models.SYS6_MENU, XH.LAB.UTILS##UPDATE:MENU_SORT,MENU_NAME,MENU_ICON,MENU_URL##更新菜单排序：规评浏览
{"MENU_ID":"H811203","FIRST_RPERSON":"33A001_杏和检验医疗","REFRESH_TYPE":null,"FONT_COLOR":null,"BACKGROUND_COLOR":null,"MENU_NAME":"规评浏览","LAST_MTIME":"2025-05-30T14:44:35","MENU_MODULE":null,"RIGHT_MENU":null,"IF_ASSIST_FUNC":null,"IF_RIGHT_MENU":null,"MENU_TYPE":"1","TOOLBAR_SORT":null,"IF_TOOLBAR":null,"PARENT_CODE":"H8112","SYS_MENU":"H81","MENU_ICON":null,"SHORTCUT_KEY":null,"MENU_SORT":"H811203","MENU_ENAME":null,"MENU_STATE":"1","MENU_CODE":"H811203","MENU_URL":"/eplan-browse","RIGHT_MENU_SORT":null,"REMARK":null,"MENU_SNAME":null,"IF_EXEC_AFTER":null,"FIRST_RTIME":"2025-05-30T14:44:39","LAST_MPERSON":null,"EXEC_AFTER_FUNC":null,"MENU_CLASS":"nav_menu","TOOLBAR_SNAME":null,"MENU_LEVEL":"1","MENU_BNAME":"规评浏览","ASSIST_FUNC":null,"TOP_INFO_CLASS":null}
XH.LAB.UTILS.Models.SYS6_MENU, XH.LAB.UTILS##UPDATE:MENU_SORT,MENU_NAME,MENU_ICON,MENU_URL##更新菜单排序：门禁组合
{"MENU_ID":"H811401","FIRST_RPERSON":"33A001_杏和检验医疗","REFRESH_TYPE":null,"FONT_COLOR":null,"BACKGROUND_COLOR":null,"MENU_NAME":"门禁组合","LAST_MTIME":"2025-05-30T14:44:35","MENU_MODULE":null,"RIGHT_MENU":null,"IF_ASSIST_FUNC":null,"IF_RIGHT_MENU":null,"MENU_TYPE":"1","TOOLBAR_SORT":null,"IF_TOOLBAR":null,"PARENT_CODE":"H8114","SYS_MENU":"H81","MENU_ICON":null,"SHORTCUT_KEY":null,"MENU_SORT":"H811401","MENU_ENAME":null,"MENU_STATE":"1","MENU_CODE":"H811401","MENU_URL":"/access-control-combination","RIGHT_MENU_SORT":null,"REMARK":null,"MENU_SNAME":null,"IF_EXEC_AFTER":null,"FIRST_RTIME":"2025-05-30T14:44:39","LAST_MPERSON":null,"EXEC_AFTER_FUNC":null,"MENU_CLASS":"nav_menu","TOOLBAR_SNAME":null,"MENU_LEVEL":"1","MENU_BNAME":"门禁组合","ASSIST_FUNC":null,"TOP_INFO_CLASS":null}
XH.LAB.UTILS.Models.SYS6_MENU, XH.LAB.UTILS##UPDATE:MENU_SORT,MENU_NAME,MENU_ICON,MENU_URL##更新菜单排序：门禁授权
{"MENU_ID":"H811402","FIRST_RPERSON":"33A001_杏和检验医疗","REFRESH_TYPE":null,"FONT_COLOR":null,"BACKGROUND_COLOR":null,"MENU_NAME":"门禁授权","LAST_MTIME":"2025-05-30T14:44:35","MENU_MODULE":null,"RIGHT_MENU":null,"IF_ASSIST_FUNC":null,"IF_RIGHT_MENU":null,"MENU_TYPE":"1","TOOLBAR_SORT":null,"IF_TOOLBAR":null,"PARENT_CODE":"H8114","SYS_MENU":"H81","MENU_ICON":null,"SHORTCUT_KEY":null,"MENU_SORT":"H811402","MENU_ENAME":null,"MENU_STATE":"1","MENU_CODE":"H811402","MENU_URL":"/access-control-authorization","RIGHT_MENU_SORT":null,"REMARK":null,"MENU_SNAME":null,"IF_EXEC_AFTER":null,"FIRST_RTIME":"2025-05-30T14:44:39","LAST_MPERSON":null,"EXEC_AFTER_FUNC":null,"MENU_CLASS":"nav_menu","TOOLBAR_SNAME":null,"MENU_LEVEL":"1","MENU_BNAME":"门禁授权","ASSIST_FUNC":null,"TOP_INFO_CLASS":null}
XH.LAB.UTILS.Models.SYS6_MENU, XH.LAB.UTILS##UPDATE:MENU_SORT,MENU_NAME,MENU_ICON,MENU_URL##更新菜单排序：访客核准
{"MENU_ID":"H811403","FIRST_RPERSON":"33A001_杏和检验医疗","REFRESH_TYPE":null,"FONT_COLOR":null,"BACKGROUND_COLOR":null,"MENU_NAME":"访客核准","LAST_MTIME":"2025-05-30T14:44:35","MENU_MODULE":null,"RIGHT_MENU":null,"IF_ASSIST_FUNC":null,"IF_RIGHT_MENU":null,"MENU_TYPE":"1","TOOLBAR_SORT":null,"IF_TOOLBAR":null,"PARENT_CODE":"H8114","SYS_MENU":"H81","MENU_ICON":null,"SHORTCUT_KEY":null,"MENU_SORT":"H811403","MENU_ENAME":null,"MENU_STATE":"1","MENU_CODE":"H811403","MENU_URL":"/visitor-approval","RIGHT_MENU_SORT":null,"REMARK":null,"MENU_SNAME":null,"IF_EXEC_AFTER":null,"FIRST_RTIME":"2025-05-30T14:44:39","LAST_MPERSON":null,"EXEC_AFTER_FUNC":null,"MENU_CLASS":"nav_menu","TOOLBAR_SNAME":null,"MENU_LEVEL":"1","MENU_BNAME":"访客核准","ASSIST_FUNC":null,"TOP_INFO_CLASS":null}
XH.LAB.UTILS.Models.SYS6_MENU, XH.LAB.UTILS##UPDATE:MENU_SORT,MENU_NAME,MENU_ICON,MENU_URL##更新菜单排序：访客日志
{"MENU_ID":"H811404","FIRST_RPERSON":"33A001_杏和检验医疗","REFRESH_TYPE":null,"FONT_COLOR":null,"BACKGROUND_COLOR":null,"MENU_NAME":"访客日志","LAST_MTIME":"2025-05-30T14:44:35","MENU_MODULE":null,"RIGHT_MENU":null,"IF_ASSIST_FUNC":null,"IF_RIGHT_MENU":null,"MENU_TYPE":"1","TOOLBAR_SORT":null,"IF_TOOLBAR":null,"PARENT_CODE":"H8114","SYS_MENU":"H81","MENU_ICON":null,"SHORTCUT_KEY":null,"MENU_SORT":"H811404","MENU_ENAME":null,"MENU_STATE":"1","MENU_CODE":"H811404","MENU_URL":"/visitor-logs","RIGHT_MENU_SORT":null,"REMARK":null,"MENU_SNAME":null,"IF_EXEC_AFTER":null,"FIRST_RTIME":"2025-05-30T14:44:39","LAST_MPERSON":null,"EXEC_AFTER_FUNC":null,"MENU_CLASS":"nav_menu","TOOLBAR_SNAME":null,"MENU_LEVEL":"1","MENU_BNAME":"访客日志","ASSIST_FUNC":null,"TOP_INFO_CLASS":null}
XH.LAB.UTILS.Models.SYS6_MENU, XH.LAB.UTILS##UPDATE:MENU_SORT,MENU_NAME,MENU_ICON,MENU_URL##更新菜单排序：门禁日志
{"MENU_ID":"H811405","FIRST_RPERSON":"33A001_杏和检验医疗","REFRESH_TYPE":null,"FONT_COLOR":null,"BACKGROUND_COLOR":null,"MENU_NAME":"门禁日志","LAST_MTIME":"2025-05-30T14:44:35","MENU_MODULE":null,"RIGHT_MENU":null,"IF_ASSIST_FUNC":null,"IF_RIGHT_MENU":null,"MENU_TYPE":"1","TOOLBAR_SORT":null,"IF_TOOLBAR":null,"PARENT_CODE":"H8114","SYS_MENU":"H81","MENU_ICON":null,"SHORTCUT_KEY":null,"MENU_SORT":"H811405","MENU_ENAME":null,"MENU_STATE":"1","MENU_CODE":"H811405","MENU_URL":"/access-control-logs","RIGHT_MENU_SORT":null,"REMARK":null,"MENU_SNAME":null,"IF_EXEC_AFTER":null,"FIRST_RTIME":"2025-05-30T14:44:39","LAST_MPERSON":null,"EXEC_AFTER_FUNC":null,"MENU_CLASS":"nav_menu","TOOLBAR_SNAME":null,"MENU_LEVEL":"1","MENU_BNAME":"门禁日志","ASSIST_FUNC":null,"TOP_INFO_CLASS":null}
XH.LAB.UTILS.Models.SYS6_MENU, XH.LAB.UTILS##UPDATE:MENU_SORT,MENU_NAME,MENU_ICON,MENU_URL##更新菜单排序：人事档案清单导出
{"MENU_ID":"H8107002","FIRST_RPERSON":null,"REFRESH_TYPE":null,"FONT_COLOR":"#185DDC","BACKGROUND_COLOR":"transparent","MENU_NAME":"人事档案清单导出","LAST_MTIME":"2025-06-12T14:51:14","MENU_MODULE":null,"RIGHT_MENU":null,"IF_ASSIST_FUNC":null,"IF_RIGHT_MENU":"1","MENU_TYPE":"1","TOOLBAR_SORT":null,"IF_TOOLBAR":"1","PARENT_CODE":"H8107","SYS_MENU":"H81","MENU_ICON":null,"SHORTCUT_KEY":null,"MENU_SORT":"H8107002","MENU_ENAME":null,"MENU_STATE":"1","MENU_CODE":"H8107002","MENU_URL":null,"RIGHT_MENU_SORT":null,"REMARK":null,"MENU_SNAME":null,"IF_EXEC_AFTER":null,"FIRST_RTIME":null,"LAST_MPERSON":"fr_李影","EXEC_AFTER_FUNC":null,"MENU_CLASS":"button","TOOLBAR_SNAME":null,"MENU_LEVEL":"2","MENU_BNAME":"人事档案清单导出","ASSIST_FUNC":null,"TOP_INFO_CLASS":null}
XH.LAB.UTILS.Models.SYS6_MENU, XH.LAB.UTILS##UPDATE:MENU_SORT,MENU_NAME,MENU_ICON,MENU_URL##更新菜单排序：人事档案清单
{"MENU_ID":"H811406","FIRST_RPERSON":"33A001_杏和检验医疗","REFRESH_TYPE":null,"FONT_COLOR":"#185DDC","BACKGROUND_COLOR":"transparent","MENU_NAME":"人事档案清单","LAST_MTIME":"2025-06-12T14:51:36","MENU_MODULE":null,"RIGHT_MENU":null,"IF_ASSIST_FUNC":null,"IF_RIGHT_MENU":"1","MENU_TYPE":"1","TOOLBAR_SORT":null,"IF_TOOLBAR":"1","PARENT_CODE":"H8107","SYS_MENU":"H81","MENU_ICON":null,"SHORTCUT_KEY":null,"MENU_SORT":"H811406","MENU_ENAME":null,"MENU_STATE":"1","MENU_CODE":"H811406","MENU_URL":null,"RIGHT_MENU_SORT":null,"REMARK":null,"MENU_SNAME":null,"IF_EXEC_AFTER":null,"FIRST_RTIME":"2025-05-30T14:44:39","LAST_MPERSON":"fr_李影","EXEC_AFTER_FUNC":null,"MENU_CLASS":"button","TOOLBAR_SNAME":null,"MENU_LEVEL":"2","MENU_BNAME":"人事档案清单","ASSIST_FUNC":null,"TOP_INFO_CLASS":null}
