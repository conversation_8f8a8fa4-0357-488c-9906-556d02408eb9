﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Dtos.Eguard
{
    /// <summary>
    /// 门禁组合删除\禁用前校验
    /// </summary>
    public class EguardComUpdateVerifyDto
    {
        public string ROWKEY { get; set; }
        /// <summary>
        /// 授权类型（1、岗位；2、人员）
        /// </summary>
        public string? EGUARD_AUTH_TYPE { get; set; }
        /// <summary>
        /// 授权对象
        /// </summary>
        public string? EGUARD_DATA_NAME { get; set; }
        /// <summary>
        /// 通过时间
        /// </summary>
        public string? EGUARD_PASS_JSON { get; set; }
        /// <summary>
        /// 授权期限类型（1、长期；2、固定日期）
        /// </summary>
        public string? AUTH_LIMIT_TYPE { get; set; }
        /// <summary>
        /// 授权开始时间
        /// </summary>
        public DateTime? AUTH_START_DATE { get; set; }
        /// <summary>
        /// 授权结束时间
        /// </summary>
        public DateTime? AUTH_END_DATE { get; set; }
        /// <summary>
        /// 授权人员
        /// </summary>
        public string? AUTH_USER_NAME { get; set; }
        /// <summary>
        /// 授权时间
        /// </summary>
        public DateTime? AUTH_TIME { get; set; }
        /// <summary>
        /// 适用范围
        /// </summary>
        public string? EPLAN_APPLY_TYPE { get; set; }
        public string? EGUARD_AUTH_ID { get; set; }
    }
}
