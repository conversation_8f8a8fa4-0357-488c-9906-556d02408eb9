﻿//using H.Utility.SqlSugarInfra;
//using SqlSugar;
//using System;
//using System.Collections.Generic;
//using System.ComponentModel.DataAnnotations;
//using System.Linq;
//using System.Text;
//using System.Threading.Tasks;

//namespace XH.H81.Models.Entities.EvaluatePlan
//{
//    [DBOwner("XH_OA")]
//    public class OA_EVALUATE_PLAN_USER
//    {
//        [Key]
//        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
//        /// <summary>
//        /// 规评方案设置ID (主键)
//        /// </summary>
//        public string EPLAN_SID { get; set; }
//        [Key]
//        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
//        /// <summary>
//        /// 用户ID (主键)
//        /// </summary>
//        public string USER_ID { get; set; }

//        /// <summary>
//        /// 医疗机构ID
//        /// </summary>
//        public string HOSPITAL_ID { get; set; }

//        /// <summary>
//        /// 规评起始预警日期
//        /// </summary>
//        public DateTime? WARN_START_DATE { get; set; }

//        /// <summary>
//        /// 规评失效日期
//        /// </summary>
//        public DateTime? EXPIRED_DATE { get; set; }

//        /// <summary>
//        /// 状态 (0-禁用, 1-通过, 2-未通过)
//        /// </summary>
//        public string EPLAN_USER_STATE { get; set; }

//        /// <summary>
//        /// 首次登记人
//        /// </summary>
//        public string FIRST_RPERSON { get; set; }

//        /// <summary>
//        /// 首次登记时间
//        /// </summary>
//        public DateTime? FIRST_RTIME { get; set; }

//        /// <summary>
//        /// 最后修改人员
//        /// </summary>
//        public string LAST_MPERSON { get; set; }

//        /// <summary>
//        /// 最后修改时间
//        /// </summary>
//        public DateTime? LAST_MTIME { get; set; }

//        /// <summary>
//        /// 备注
//        /// </summary>
//        public string REMARK { get; set; }
//    }
//}
