﻿using H.Utility;
using XH.H81.Models.Dtos.Eguard;
using XH.H81.Models.Entities.Eguard;
using XH.H81.Models.Entities.Pms;

namespace XH.H81.IServices
{
    public interface IEguardControlService
    {
        ResultDto Test();

        /// <summary>
        /// 获取门禁组合列表
        /// </summary>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        Task<ResultDto> GetEguardComList(string hospitalId);
        /// <summary>
        /// 门禁组合删除/禁用前校验
        /// </summary>
        /// <param name="eguardComId"></param>
        /// <returns></returns>
        Task<ResultDto> EguardComUpdateVerify(string eguardComId);
        /// <summary>
        /// 删除已有组合
        /// </summary>
        /// <param name="eguardComId"></param>
        /// <param name="operPerson"></param>
        /// <returns></returns>
        Task<ResultDto> DeleteEguardAuthCom(string eguardComId, string operPerson);
        /// <summary>
        /// 获取门禁列表树
        /// </summary>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        Task<ResultDto> GetEguardTreeList(string hospitalId);
        Task<ResultDto> GetEguardTableList(string hospitalId);
        Task<ResultDto> ChangeEguardTableState(EditEguardTableDto input, H.Utility.ClaimsDto user);
        Task<ResultDto> UpdateEguardComTable(EditEguardTableDto input, H.Utility.ClaimsDto user);

        #region 门禁组合
        /// <summary>
        /// 门禁组合信息保存（新增、修改）
        /// </summary>
        /// <param name="input"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        Task<ResultDto> SaveEguardInfo(EguardComDto input, H.Utility.ClaimsDto user);
        /// <summary>
        /// 门禁启用禁用
        /// </summary>
        /// <param name="input"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        Task<ResultDto> ChangeEguardState(ChangeEguardComStateDto input, H.Utility.ClaimsDto user);
        /// <summary>
        /// 删除门禁组合信息
        /// </summary>
        /// <param name="input"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        Task<ResultDto> DeleteEguardInfo(DeleteEguardComDto input, H.Utility.ClaimsDto user); 
        #endregion

        #region 门禁时间计划
        Task<ResultDto> SaveEguardPassDict(EguardPassDictDto input, H.Utility.ClaimsDto user);
        Task<ResultDto> ChangeEguardPassDictState(ChangeEguardPassDictDto input, H.Utility.ClaimsDto user);
        Task<ResultDto> DeleteEguardPassDict(DeleteEguardPassDictDto input, H.Utility.ClaimsDto user);
        Task<ResultDto> GetEguardPassDictList(H.Utility.ClaimsDto user);
        #endregion

        #region 门禁授权
        /// <summary>
        /// 按岗位获取门禁列表
        /// </summary>
        /// <param name="authType">LAB-检验科室 ；PGROUP-检验专业组；MGROUP-管理专业组；POST-岗位；PROLE-岗位角色;User-用户id</param>
        /// <param name="dataId">树选中的id</param>
        /// <returns></returns>
        Task<ResultDto> GetUserEguardList(string authType, string dataId);
        #endregion

        #region 门禁授权相关
        /// <summary>
        /// 获取门禁授权列表
        /// </summary>
        /// <param name="input"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        Task<ResultDto> GetEguardList(EguardInput input, H.Utility.ClaimsDto user);
        /// <summary>
        /// 新增保存门禁授权
        /// </summary>
        /// <param name="input"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        Task<ResultDto> SaveEguard(EguardSaveDto input, H.Utility.ClaimsDto user);
        Task<ResultDto> NewEguardSave(NewEguardSaveDto request, ClaimsDto user);
        /// <summary>
        /// 复制岗位授权
        /// </summary>
        /// <param name="request"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        Task<ResultDto> CopyEguardAuth(CopyEguardAuthRequest request, string userName);
        /// <summary>
        /// 判断是否可以操作门禁组合或者门禁
        /// </summary>
        /// <param name="input"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        Task<ResultDto> JudgEguardCom(JudgEguardCom input, H.Utility.ClaimsDto user);
        /// <summary>
        /// 删除门禁授权
        /// </summary>
        /// <param name="input"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        Task<ResultDto> DeleteEguard(EguardSaveDto input, H.Utility.ClaimsDto user);
        /// <summary>
        /// 获取人员信息
        /// </summary>
        /// <param name="postId"></param>
        /// <param name="nameOrLogid"></param>
        /// <param name="hospitalId"></param>
        /// <param name="upostState"></param>
        /// <param name="post"></param>
        /// <returns></returns>
        Task<ResultDto> GetNewPostUserInfo(string postId, string? nameOrLogid, string? hospitalId);
        #endregion

        #region 访客核准
        /// <summary>
        /// 访客申请列表
        /// </summary>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        Task<ResultDto> GetVistorRequest(DateTime startTime, DateTime endTime, string hospitalId);
        /// <summary>
        /// 保存申请记录
        /// </summary>
        /// <param name="request"></param>
        /// <param name="hospitalId"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        Task<ResultDto> SaveVistorRequest(SaveVistorRequestRequest request, string hospitalId, string userName);
        /// <summary>
        /// 操作申请记录
        /// </summary>
        /// <param name="request"></param>
        /// <param name="userName"></param>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        Task<ResultDto> VisitRequestApproval(VisitApprovalRequest request, string userName, string hospitalId);
        /// <summary>
        /// 删除申请记录
        /// </summary>
        /// <param name="visitReqId"></param>
        /// <param name="userName"></param>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        Task<ResultDto> DeleteVisitRequest(string visitReqId, string userName, string hospitalId);
        /// <summary>
        /// 获取科外人员列表
        /// </summary>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        Task<ResultDto> GetVisitorList(string hospitalId);
        /// <summary>
        /// 上传科外人员图片
        /// </summary>
        /// <param name="request"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        Task<ResultDto> UploadVisitorImg(UploadVisitorImg request, string userName);
        #endregion

        #region 访问日志
        /// <summary>
        /// 获取全部访问日志
        /// </summary>
        /// <param name="startTime">检索开始时间</param>
        /// <param name="endTime">检索结束时间</param>
        /// <returns></returns>
        Task<ResultDto> GetAllVisitorLog(DateTime startTime, DateTime endTime, string hospitalId);

        /// <summary>
        /// 获取访客日志（按访客）
        /// </summary>
        /// <param name="startTime">检索开始时间</param>
        /// <param name="endTime">检索结束时间</param>
        /// <returns></returns>
        Task<ResultDto> GetVisitorListByVisitor(DateTime startTime, DateTime endTime, string hospitalId);

        /// <summary>
        /// 获取访客日志（按科室人员）
        /// </summary>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        Task<ResultDto> GetLabPersonListByLabPerson(DateTime startTime, DateTime endTime, string hospitalId);
        /// <summary>
        /// 获取访客日志（按门禁地点）
        /// </summary>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        Task<ResultDto> GetVisitRoomListByRoom(DateTime startTime, DateTime endTime, string hospitalId);
        #endregion
        #region 门禁日志

        /// <summary>
        /// 获取门禁日志（全部）
        /// </summary>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        Task<ResultDto> GetAllOutInLogList(DateTime startTime, DateTime endTime, string hospitalId);
        /// <summary>
        /// 获取门禁日志（按人员）
        /// </summary>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        Task<ResultDto> GetPersonListByPerson(DateTime startTime, DateTime endTime, string hospitalId);
        /// <summary>
        /// 获取门禁（按门禁）
        /// </summary>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        Task<ResultDto> GetEguardListByEguard(DateTime startTime, DateTime endTime, string hospitalId);
        #endregion
        /// <summary>
        /// 获取门禁下拉基础数据
        /// </summary>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        Task<Dictionary<string, List<BaseDataConfigDicDto>>> GetEguardDropDownDataList(string hospitalId,string labId);

        /// <summary>
        /// 按人统计门禁计划
        /// </summary>
        /// <param name="authIds">具体哪次授权设置</param>
        /// <param name="user">具体哪个人</param>
        /// <returns></returns>
        Task<List<PMS_EGUARD_AUTH_USER>> StatisticalUserAccessControlTasks(string[] authIds , PMS_PERSON_INFO? user = null);
        

        /// <summary>
        /// 取消电信授权
        /// </summary>
        /// <returns></returns>
        Task<List<bool>> CancelAccessControTasks();

        /// <summary>
        /// 按时间下发指定的门禁授权任务
        /// </summary>
        /// <param name="time"></param>
        /// <returns></returns>
        Task<List<PMS_EGUARD_AUTH_USER>> PerformAccessControTasks(DateTime time);
        /// <summary>
        /// 发布授权设置
        /// </summary>
        /// <param name="eguardAuthIds"></param>
        /// <returns></returns>
        Task<ResultDto> EguardAuthPublish(string hospitalId);
        Task<ResultDto> NewDeleteEguardAuth(List<string> eguardAuthIds, string operPerson);
        Task<ResultDto> PublishButtonIfNew(string hospitalId);
        Task<ResultDto> GetUpdateEguardAuth(UpdateEguardAuth request);
        Task<ResultDto> NewDeleteEguard(UpdateEguardAuth request, string operPeron);
    }
}
