﻿using AutoMapper;
using H.Utility;
using H.Utility.Helper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Serilog;
using XH.H81.API.Extensions;
using XH.H81.Base.Helper;
using XH.H81.IServices;
using XH.H81.IServices.Pms;
using XH.H81.Models.Dtos;
using XH.H81.Models.Dtos.Exam;
using XH.H81.Models.Dtos.Pms;
using XH.H81.Models.Entities.Pms;
using XH.LAB.UTILS.Helpers;
using XH.LAB.UTILS.Interface;
using XH.LAB.UTILS.Models.Dto;

namespace XH.H81.API.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [ApiExplorerSettings(GroupName = "人事档案相关")]
    [Authorize]
    public class PmsController : ControllerBase
    {
        private IPmsService _IPmsService;
        private readonly IBaseDataServices _IBaseDataServices;
        private readonly IUploadFileService _IUploadFileService;
        private readonly IConfiguration _configuration;
        private readonly Microsoft.AspNetCore.Hosting.IHostingEnvironment _hostingEnvironment;
        private readonly string file_preview = "/H81pdf/api";
        private readonly ISystemService _systemService;
        private readonly string file_preview_address = "";
        private readonly IMapper _mapper;
        public PmsController(IPmsService iPmsService, IBaseDataServices ibasedataServices, Microsoft.AspNetCore.Hosting.IHostingEnvironment hostingEnvironment, IConfiguration configuration, IUploadFileService iUploadFileService, ISystemService systemService, IMapper mapper)
        {
            _IPmsService = iPmsService;
            _IBaseDataServices = ibasedataServices;
            _IUploadFileService = iUploadFileService;
            _hostingEnvironment = hostingEnvironment;
            _configuration = configuration;
            _systemService = systemService;
            file_preview_address = _configuration["S54"];
            _mapper = mapper;
        }
        /**  已经改成form-data接口  待后续删除
      
        /// <summary>
        /// 上传组织架构
        /// </summary>
        /// <param name="obj">{"LAB_ID":"","FILEBASE64":"文件Base64","FILE_NAME":"xxx.jpg"}</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadOrganizationChart(dynamic obj)
        {
            UploadDto uploadDto = new UploadDto();
            string msg = string.Empty;
            bool success = true;
            try
            {
                var claim = this.User.ToClaimsDto();
                dynamic json = JsonConvert.DeserializeObject(Convert.ToString(obj));
                string lab_id = json["LAB_ID"].ToString();
                string file_name = json["FILE_NAME"].ToString();
                string strBaes64 = json["FILEBASE64"].ToString();
                string hospital_id = claim.HOSPITAL_ID;
                string edit_person = claim.HIS_NAME;
                int resultCount = 0;
                string file_suffix = file_name.Substring(file_name.LastIndexOf("."));
                var objec = new
                {
                    fileName = Guid.NewGuid().ToString("N") + file_suffix,//文件名称
                    src = strBaes64,//baes64字符串
                    folderName = Path.Combine("PMS"),//文件夹路径
                    ifCover = true,
                };
                string jsonStr = JsonConvert.SerializeObject(objec);
                ResultDto resultdto = _evaluatePlanServices.UploadPathFile(jsonStr);
                if (resultdto.success == true)
                {
                    resultCount = _IPmsService.SaveOrganizationChart(hospital_id, lab_id, edit_person, resultdto.data.ToString());
                    if (resultCount > 0)
                    {
                        uploadDto.COUNT = resultCount;
                        uploadDto.UPLOADPATH = file_preview + resultdto.data.ToString();
                    }
                    success = true;
                }
            }
            catch (Exception e)
            {
                success = false;
                msg = e.Message;
            }
            return Ok(new ResultDto()
            {
                success = success,
                msg = msg,
                data = uploadDto
            });
        }
           */
        [HttpPost]
        public IActionResult UploadOrganizationChartFile([FromForm] UploadFileDto file)
        {

            file.FILE_NAME = file.FILE.FileName;//文件名称
            file.SAVE_TO_S28 = true;
            file.IFCOVER = true;
            var claim = this.User.ToClaimsDto();
            ResultDto resultDto = _IUploadFileService.UploadFileToServer(file);
            string hospital_id = claim.HOSPITAL_ID;
            string edit_person = claim.HIS_NAME;
            if (resultDto.success && resultDto.data != null)
            {
                _IPmsService.SaveOrganizationChart(hospital_id, claim.LAB_ID, claim.USER_NO, edit_person, resultDto.data.ToString());
                return Ok(OfficeHelper.CombinePath(file_preview, resultDto.data.ToString()).ToResultDto());
            }
            else
            {
                return Ok(resultDto);
            }


        }
        /// <summary>
        /// 获取组织架构地址
        /// </summary>
        /// <param name="obj">{"LAB_ID":""}</param>
        /// <returns></returns>

        [HttpPost]
        public IActionResult GetOrganizationChartImg(dynamic obj)
        {
            string msg = string.Empty;
            string organization_chart = string.Empty;
            try
            {
                var claim = this.User.ToClaimsDto();
                dynamic json = JsonConvert.DeserializeObject(Convert.ToString(obj));
                string hospital_id = claim.HOSPITAL_ID;//院区ID
                string module_id = claim.MODULE_ID;
                string lab_id = json["LAB_ID"].ToString();
                organization_chart = _IPmsService.GetOrganizationChartImg(hospital_id, lab_id, module_id, claim.USER_NO);
                if (organization_chart != "")
                {
                    organization_chart = OfficeHelper.PathCombine(file_preview, organization_chart);
                }
            }
            catch (Exception e)
            {
                msg = e.Message;
            }
            var result = new { ORGANIZATION_CHAR = organization_chart, MSG = msg };
            return Ok(new ResultDto()
            {
                data = result
            });
        }
        /**已经改成form-data方式 待后续删除
        
        /// <summary>
        /// 上传人员照片
        /// </summary>
        /// <param name="obj">{"PERSON_ID":"","FILEBASE64":"文件Base64","FILE_NAME":"xxx.jpg"}</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadPersonPhoto(dynamic obj)
        {
            UploadDto uploadDto = new UploadDto();
            string msg = string.Empty;
            bool success = true;
            int resultCount = 0;
            try
            {
                dynamic json = JsonConvert.DeserializeObject(Convert.ToString(obj));
                string person_id = json["PERSON_ID"].ToString();
                string strBaes64 = json["FILEBASE64"].ToString();
                string file_name = json["FILE_NAME"].ToString();
                var claim = this.User.ToClaimsDto();
                string user_name = claim.HIS_NAME;
                string file_suffix = file_name.Substring(file_name.LastIndexOf("."));
                var objec = new
                {
                    fileName = Guid.NewGuid().ToString("N") + file_suffix,//文件名称
                    src = strBaes64,//baes64字符串
                    folderName = OfficeHelper.PathCombine("PMS", person_id),//文件夹路径
                    ifCover = true,
                };
                string jsonStr = JsonConvert.SerializeObject(objec);
                ResultDto resultdto = _evaluatePlanServices.UploadPathFile(jsonStr);

                if (resultdto.success == true)
                {
                    resultCount = _IPmsService.SavePersonPhoto(resultdto.data.ToString(), person_id);
                    if (resultCount > 0)
                    {
                        uploadDto.COUNT = resultCount;
                        uploadDto.UPLOADPATH = file_preview + resultdto.data.ToString();
                    }
                }
            }
            catch (Exception e)
            {
                success = false;
                msg = e.Message;
            }
            return Ok(new ResultDto()
            {
                success = success,
                msg = msg,
                data = uploadDto
            });
        }
         */
        /// <summary>
        /// 上传人员照片
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadPersonPhotoFile([FromForm] UploadUserPhotoFileDto file)
        {
            file.FILE_NAME = file.FILE.FileName;//文件名称
            int suffixIndex = file.FILE_NAME.LastIndexOf('.');
            string suffix = suffixIndex <= 0 ? "" : file.FILE_NAME.Substring(suffixIndex);
            file.FILE_NAME = $"{Guid.NewGuid()}{suffix.ToLower()}"; //避免覆盖同名头像
            file.SAVE_TO_S28 = true;
            file.IFCOVER = true;
            if (file.USER_ID.IsNullOrEmpty())
            {
                throw new Exception("请传当前操作的人员USER_ID");
            }
            var claim = this.User.ToClaimsDto();
            ResultDto resultDto = _IUploadFileService.UploadFileToServer(file);
            string hospital_id = claim.HOSPITAL_ID;
            string edit_person = claim.HIS_NAME;
            if (resultDto.success && resultDto.data != null)
            {
                string fileUrl = OfficeHelper.CombinePath(file_preview, resultDto.data.ToString());
                _IPmsService.SavePersonPhoto(resultDto.data.ToString(), file.USER_ID);
                return Ok(fileUrl.ToResultDto());
            }
            else
            {
                return Ok(resultDto);
            }
        }

        /// <summary>
        /// 上传人员门禁照片
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadPersonFacePhotoFile([FromForm] UploadUserPhotoFileDto file)
        {
            file.FILE_NAME = file.FILE.FileName;//文件名称
            int suffixIndex = file.FILE_NAME.LastIndexOf('.');
            string suffix = suffixIndex <= 0 ? "" : file.FILE_NAME.Substring(suffixIndex);
            string guid = Guid.NewGuid().ToString();
            file.FILE_NAME = $"{guid}{suffix.ToLower()}"; //避免覆盖同名头像
            file.SAVE_TO_S28 = true;
            file.IFCOVER = true;
            if (file.USER_ID.IsNullOrEmpty())
            {
                throw new Exception("请传当前操作的人员USER_ID");
            }
            var claim = this.User.ToClaimsDto();
            ResultDto resultDto = _IUploadFileService.UploadFileToServer(file);
            byte[] uploadByte = null;
            using (var stream = file.FILE.OpenReadStream())
            {
                uploadByte = new byte[stream.Length];
                stream.Read(uploadByte, 0, uploadByte.Length);
                stream.Seek(0, SeekOrigin.Begin);
            }
            //压缩图片
            string compressByte = PicBase64Helper.CompressBase64Image(Convert.ToBase64String(uploadByte),200);
            file.UPLOAD_BYTES= Convert.FromBase64String(compressByte);
            file.UPLOAD_FILE_NAME=file.FILE_NAME = $"{guid}_clip{suffix.ToLower()}"; //避免覆盖同名头像
            ResultDto resultCompressDto = _IUploadFileService.UploadFileToServer(file);

            string hospital_id = claim.HOSPITAL_ID;
            string edit_person = claim.HIS_NAME;

            Dictionary<string, string> Dic = new Dictionary<string, string>();
            if (resultDto.success && resultDto.data != null)
            {
                string fileUrl = OfficeHelper.CombinePath(file_preview, resultDto.data.ToString());
                string fileClipUrl = OfficeHelper.CombinePath(file_preview, resultCompressDto.data?.ToString());
                Dic.Add("FacePhotoPath", fileUrl);
                Dic.Add("FaceClipPhotoPath", fileClipUrl);
                _IPmsService.SavePersonFacePhoto(resultDto.data.ToString(), resultCompressDto.data?.ToString(), file.USER_ID);
                return Ok(Dic.ToResultDto());
            }
            else
            {
                return Ok(resultDto);
            }
        }

        /// <summary>
        /// 保存人员信息
        /// </summary>
        /// <param name="obj">{"List":"","EDIT_PERSON":""}</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult SavePersonInfo(dynamic obj)
        {
            dynamic json = JsonConvert.DeserializeObject(Convert.ToString(obj));
            string edit_person = json["EDIT_PERSON"].ToString();
            string user_info = json["USER_INFO"].ToString();
            PMS_PERSON_INFO pmsPersonInfo = JsonHelper.FromJson<PMS_PERSON_INFO>(user_info);
            var resultRow = _IPmsService.SavePersonInfo(pmsPersonInfo, edit_person, user_info);
            return Ok(resultRow);
        }

        /// <summary>
        /// 删除人员信息
        /// </summary>
        /// <param name="obj">{"List":"","EDIT_PERSON":""}</param>
        /// <returns></returns>
        [HttpDelete]
        public IActionResult DeletePerson(string personId)
        {
            var resultRow = _IPmsService.DeletePerson(personId);
            return Ok(resultRow);
        }
        /// <summary>
        /// 根据分类对应表获取记录单信息
        /// </summary>
        /// <param name="obj">{"ARCHIVE_TABLE":"","PERSON_ID":""}</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult GetRecordInfoByClass(RecordInfoByClassParm json)
        {
            string archive_table = json.ARCHIVE_TABLE ?? throw new BizException("参数ARCHIVE_TABLE不可为空！");
            string person_id = json.PERSON_ID;
            string smblFlag = json.SMBL_FLAG;
            string dateType = json.DATE_TYPE;
            string startDate = json.START_DATE;
            string endDate = json.END_DATE;
            string searchName = json.searchName;
            string tagId = json.tagId;
            var Result = _IPmsService.GetRecordInfoByClass(archive_table, person_id, smblFlag, dateType, startDate, endDate, json.STATUS, searchName, tagId);
            return Ok(Result.ToResultDto());
        }

        /// <summary>
        /// 获取指分类对应表获取记录单信息
        /// </summary>
        /// <param name="obj">{"ARCHIVE_TABLE":"","PERSON_ID":""}</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult GetRecordInfoByMultipleClass(RecordInfoByClassParm json)
        {
            string archive_table = json.ARCHIVE_TABLE ?? "";
            string person_id = json.PERSON_ID;
            string smblFlag = json.SMBL_FLAG;
            string dateType = json.DATE_TYPE;
            string startDate = json.START_DATE;
            string endDate = json.END_DATE;
            string searchName = json.searchName;
            string idName = json.ID_NAME;
            string tagId = json.tagId;
            List<string> tableList = archive_table.Split(',').Select(a => a.Trim()).ToList();
            var Result = _IPmsService.GetRecordInfoByMultipleClass(tableList, person_id, smblFlag, dateType, startDate, endDate, json.STATUS, searchName, idName, tagId);
            return Ok(Result.ToResultDto());
        }

        /// <summary>
        /// 根据分类和状态获取记录单信息 【前端表示目前没有使用该接口】
        /// </summary>
        /// <param name="obj">{"ARCHIVE_TABLE":"","PERSON_ID":"","CHECK_STATE":""}</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult GetRecordInfoByClassByState(RecordInfoByClassParm json)
        {
            string archive_table = json.ARCHIVE_TABLE ?? throw new BizException("参数ARCHIVE_TABLE不可为空！");
            string person_id = json.PERSON_ID;
            string check_state = json.CHECK_STATE;
            string smblFlag = json.SMBL_FLAG;
            var Result = _IPmsService.GetRecordInfoByClassByState(archive_table, person_id, check_state, smblFlag);
            return Ok(Result.ToResultDto());
            ;
        }
        
        /// <summary>
        /// 获取个人证书列表
        /// </summary>
        /// <param name="personId"></param>
        /// <param name="certificateDid"></param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetPersonCertificateList(string personId, string certificateDid)
        {
            var Result = _IPmsService.GetPersonCertificateList(personId, certificateDid);
            return Ok(Result.ToResultDto());
        }

        /// <summary>
        /// 按证书ID获取个人培训列表
        /// </summary>
        /// <param name="personId"></param>
        /// <param name="certificateDid"></param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetPersonTrainList(string personId, string? certificateId)
        {
            var Result = _IPmsService.GetPersonTrainList(personId, certificateId);
            return Ok(Result.ToResultDto());
        }
        /// <summary>
        /// 保存记录单信息
        /// </summary>
        /// <param name="objData">{"LIST": {"RESUME_SORT": "2","RESUME_UNIT": "世纪联华2","WORK_DEPT": "工作部门","WORK_POST": "养鱼岗位-new","WORK_START_TIME": "2023-05-06",
        /// "WORK_END_TIME": "2023-05-28","WORK_YEAR": 2,"WORK_PROFESSION": "专业","SELECT_CHECK_PERSON": "A247","REMARK": "2","PERSON_ID": "26","RECORD_ID": "10"},
        /// "ARCHIVE_TABLE": "PMS_RESUME_LIST","ACTION": "D","PERSON_ID": "26"}</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult SaveRecordInfo(dynamic objData)
        {
            string resultRow = string.Empty;
            SaveRecordDto SaveRecord = new SaveRecordDto();
            string msg = string.Empty;
            bool success = true;
            try
            {
                dynamic json = JsonConvert.DeserializeObject(Convert.ToString(objData));
                var claim = this.User.ToClaimsDto();
                string action = json["ACTION"].ToString();//操作类型
                string list = json["LIST"].ToString();//信息对象
                string archive_table = json["ARCHIVE_TABLE"].ToString();//要操作分类的表名
                string user_name = claim.HIS_NAME;//操作人名称
                string person_id = json["PERSON_ID"].ToString();//档案人员ID
                string hospital_id = claim.HOSPITAL_ID;

                resultRow = _IPmsService.SaveRecordInfo(archive_table, list, user_name, action, hospital_id, person_id);
                SaveRecord = JsonHelper.FromJson<SaveRecordDto>(resultRow);
            }

            catch (Exception ex)
            {
                success = false;
                msg = ex.Message;
            }
            return Ok(new ResultDto()
            {
                success = success,
                msg = msg,
                data = SaveRecord
            });
        }
        /// <summary>
        /// 批量删除接口
        /// </summary>
        /// <param name="batchDeleteDto"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult BatchDeleteRecord(BatchDeleteDto batchDeleteDto)
        {
            string resultRow = string.Empty;
            SaveRecordDto SaveRecord = new SaveRecordDto();
            string msg = string.Empty;
            bool success = true;
            var claim = this.User.ToClaimsDto();
            try
            {

                resultRow = _IPmsService.BatchDeleteRecord(batchDeleteDto.ArchiveTable, batchDeleteDto.ListRecordId, claim.USER_NAME, batchDeleteDto.PersonId, claim.HOSPITAL_ID);
                SaveRecord = JsonHelper.FromJson<SaveRecordDto>(resultRow);
            }

            catch (Exception ex)
            {
                success = false;
                msg = ex.Message;
            }
            return Ok(new ResultDto()
            {
                success = success,
                msg = msg,
                data = SaveRecord
            });
        }
        /// <summary>
        /// 批量上传记录单文件
        /// </summary>
        /// <param name="obj">{"PERSON_ID":"","FILEBASE64":"","FILE_NAME":"xxx","FILE_SUFFIX":".","ARCHIVE_TABLE":"","FILE_CLASS":""}</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult BatchUploadRecordFile([FromForm] PmsUploadFileDto file)
        {
            var resultdto = _IPmsService.BatchUploadRecordFile(file);
            return Ok(resultdto.ToResultDto());
        }

        /// <summary>
        /// 替换已上传的图片
        /// </summary>
        /// <param name="FILE_ID"></param>
        /// <param name="FILE_CNAME"></param>
        /// <param name="file"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult ReplaceUploadImage(string FILE_ID, string FILE_CNAME, [FromForm] UploadFileDto file)
        {
            var result = _IPmsService.ReplaceUploadImage(FILE_ID, FILE_CNAME, file);
            return Ok(result);
        }

        /// <summary>
        /// 旋转已上传的图片
        /// </summary>
        /// <param name="FILE_ID"></param>
        /// <param name="FILE_CNAME"></param>
        /// <param name="file"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult RotateUploadImage(string FILE_ID, float ANGLE)
        {
            var result = _IPmsService.RotateUploadImage(FILE_ID, ANGLE);
            return Ok(result.ToResultDto());
        }
        ///// <summary>
        ///// 根据记录上传附件
        ///// </summary>
        ///// <returns>{"PERSON_ID":"","FILEBASE64":"","FILE_NAME":"xxx","FILE_SUFFIX":".","ARCHIVE_TABLE":"","FILE_CLASS":"","RECORD_ID":""}</returns>
        //[HttpPost]
        //public IActionResult UploadRecordFileById(List<PmsUploadFileDto> files)
        //{
        //    ResultDto resultdto = new ResultDto();
        //    string resultNums = string.Empty;
        //    string msg = string.Empty;
        //    bool success = true;
        //    string archive_table = string.Empty;
        //    string record_id = string.Empty;
        //    string user_name = string.Empty;
        //    try
        //    {
        //        foreach (var file in files)
        //        {
        //            var claim = this.User.ToClaimsDto();
        //            user_name = claim.HIS_NAME;//操作人名称
        //            string hospital_id = claim.HOSPITAL_ID;
        //            string filePath = OfficeHelper.PathCombine("PMS", file.PERSON_ID);
        //            string contentRootPath = _hostingEnvironment.ContentRootPath;
        //            string fileGuidName = Guid.NewGuid().ToString("N");
        //            archive_table = file.ARCHIVE_TABLE;
        //            record_id = file.RECORD_ID;
        //            file.FILE_SUFFIX = file.FILE_SUFFIX.ToLower(); //后缀转成小写开

        //            PMS_PERSON_FILE pms_person_file = new PMS_PERSON_FILE();
        //            pms_person_file.FILE_CNAME = fileGuidName;
        //            pms_person_file.HOSPITAL_ID = hospital_id;
        //            pms_person_file.FILE_CLASS = archive_table;//要操作分类的表名
        //            pms_person_file.FILE_NAME = file.FILE_NAME; //要操作分类的名称
        //            pms_person_file.FILE_SUFFIX = file.FILE_SUFFIX;
        //            pms_person_file.FILE_PATH = "\\" + filePath + "\\";
        //            pms_person_file.FILE_STATE = "1";
        //            if (file.FILE_SUFFIX == ".pdf")
        //            {
        //                pms_person_file.FILE_TYPE = "PDF";
        //            }
        //            else if (file.FILE_SUFFIX == ".bmp" || file.FILE_SUFFIX == ".jpg" || file.FILE_SUFFIX == ".jpeg" || file.FILE_SUFFIX == ".png" || file.FILE_SUFFIX == ".gif" || file.FILE_SUFFIX == ".jfif")
        //            {
        //                pms_person_file.FILE_TYPE = "IMG";
        //            }
        //            else if (file.FILE_SUFFIX == ".doc" || file.FILE_SUFFIX == ".docx")
        //            {
        //                pms_person_file.FILE_TYPE = "DOC";
        //            }
        //            string file_id = _IPmsService.AlterRecordAffix(pms_person_file, user_name, hospital_id, file.PERSON_ID, file.FILE_CLASS, archive_table, record_id);
        //            if (file_id != "")
        //            {
        //                //优先使用FORMDATA方式上传
        //                if (file.FILE != null)
        //                {
        //                    file.UPLOAD_FOLDER_NAME = filePath;
        //                    file.FILE_NAME = fileGuidName;//GUID
        //                    resultdto = _IUploadFileService.UploadFormDataFile(file);
        //                }
        //                else if (file.FILEBASE64.IsNotNullOrEmpty())
        //                {
        //                    var objfile = new
        //                    {
        //                        fileName = fileGuidName + file.FILE_SUFFIX,//文件名称
        //                        src = file.FILEBASE64,//baes64字符串
        //                        folderName = filePath,//文件夹路径
        //                        ifCover = true,
        //                    };
        //                    string jsonStr = JsonConvert.SerializeObject(objfile);
        //                    resultdto = _evaluatePlanServices.UploadPathFile(jsonStr);
        //                }
        //                if (!resultdto.success)//失败则中断
        //                {
        //                    break;
        //                }
        //                resultNums += "," + file_id;
        //            }
        //        }
        //        if (resultNums != "")
        //        {
        //            resultNums = resultNums.Substring(1, resultNums.Length - 1);
        //            _IPmsService.AddAffix(resultNums, archive_table, record_id, user_name);
        //        }
        //    }
        //    catch (Exception)
        //    {
        //        throw new Exception("调用UploadRecordFileById方法上传文件失败！");
        //    }
        //    return Ok(resultdto);
        //}


        /// <summary>
        /// 根据记录上传附件
        /// </summary>
        /// <returns>{"PERSON_ID":"","FILEBASE64":"","FILE_NAME":"xxx","FILE_SUFFIX":".","ARCHIVE_TABLE":"","FILE_CLASS":"","RECORD_ID":""}</returns>
        [HttpPost]
        public IActionResult UploadRecordFileById([FromForm] PmsUploadFileDto file)
        {
            ResultDto resultdto = new ResultDto();
            string resultNums = string.Empty;
            try
            {
                var claim = this.User.ToClaimsDto();
                string user_name = claim.HIS_NAME;//操作人名称
                string hospital_id = claim.HOSPITAL_ID;
                string contentRootPath = _hostingEnvironment.ContentRootPath;

                string fileGuidName = Guid.NewGuid().ToString("N");
                file.FILE_NAME = file.FILE_NAME.Replace(".", "_");//避免后缀误识别而报错
                file.FILE_SUFFIX = file.FILE_SUFFIX.ToLower(); //后缀转成小写开
                file.UPLOAD_FOLDER_NAME = OfficeHelper.PathCombine("PMS", file.PERSON_ID);
                file.UPLOAD_FILE_NAME = fileGuidName + file.FILE_SUFFIX;

                PMS_PERSON_FILE pms_person_file = new PMS_PERSON_FILE();
                pms_person_file.FILE_CNAME = fileGuidName;
                pms_person_file.HOSPITAL_ID = hospital_id;
                pms_person_file.FILE_CLASS = file.ARCHIVE_TABLE;//要操作分类的表名
                pms_person_file.FILE_NAME = file.FILE_NAME; //要操作分类的名称
                pms_person_file.FILE_SUFFIX = file.FILE_SUFFIX;
                pms_person_file.FILE_PATH = "\\" + file.UPLOAD_FOLDER_NAME + "\\";
                pms_person_file.FILE_STATE = "1";
                if (file.FILE_SUFFIX == ".pdf")
                {
                    pms_person_file.FILE_TYPE = "PDF";
                }
                else if (file.FILE_SUFFIX == ".bmp" || file.FILE_SUFFIX == ".jpg" || file.FILE_SUFFIX == ".jpeg" || file.FILE_SUFFIX == ".png" || file.FILE_SUFFIX == ".gif" || file.FILE_SUFFIX == ".jfif")
                {
                    pms_person_file.FILE_TYPE = "IMG";
                }
                else if (file.FILE_SUFFIX == ".doc" || file.FILE_SUFFIX == ".docx")
                {
                    pms_person_file.FILE_TYPE = "DOC";
                }
                string file_id = _IPmsService.AlterRecordAffix(pms_person_file, user_name, hospital_id, file.PERSON_ID, file.FILE_CLASS, file.ARCHIVE_TABLE, file.RECORD_ID);
                if (file_id != "")
                {
                    resultNums += "," + file_id;
                    ////优先使用FORMDATA方式上传
                    //if (file.FILE != null)
                    //{
                    //    file.FILE_NAME = fileGuidName;//GUID
                    //    resultdto = _IUploadFileService.UploadFormDataFile(file);
                    //}
                    //else if (file.FILEBASE64.IsNotNullOrEmpty())
                    //{
                    //    var objfile = new
                    //    {
                    //        fileName = fileGuidName + file.FILE_SUFFIX,//文件名称
                    //        src = file.FILEBASE64,//baes64字符串
                    //        folderName = file.UPLOAD_FOLDER_NAME,//文件夹路径
                    //        ifCover = true,
                    //    };
                    //    string jsonStr = JsonConvert.SerializeObject(objfile);
                    //    resultdto = _evaluatePlanServices.UploadPathFile(jsonStr);
                    //}
                    resultdto = _IUploadFileService.UploadFileOperate(file);
                }
                if (resultNums != "")
                {
                    resultNums = resultNums.Substring(1, resultNums.Length - 1);
                    _IPmsService.AddAffix(resultNums, file.ARCHIVE_TABLE, file.RECORD_ID, user_name);
                }
            }
            catch (Exception ex)
            {
                Log.Error($"调用UploadRecordFileById方法上传文件失败:{ex.ToString()}");
                throw new Exception("调用UploadRecordFileById方法上传文件失败！");
            }
            return Ok(resultdto);
        }



        /// <summary>
        /// 导入论著记录
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult ImportThesisRecord()
        {
            string msg = string.Empty;
            bool success = true;
            //  StringBuilder Title = new StringBuilder();
            PMS_THESIS_LIST pmsThesisList = new PMS_THESIS_LIST();
            try
            {
                var claim = this.User.ToClaimsDto();
                var files = Request.Form.Files;
                // string hospital_id = Request.Form["HOSPITAL_ID"].ToString();
                string hospital_id = claim.HOSPITAL_ID;
                string person_id = Request.Form["PERSON_ID"].ToString();
                string user_name = claim.HIS_NAME;
                string thesis_sort = Request.Form["THESIS_SORT"].ToString();
                if (files.Count > 0)
                {
                    foreach (var formFile in files)
                    {
                        PMS_THESIS_LIST pms_thesis_list = new PMS_THESIS_LIST();
                        pms_thesis_list.HOSPITAL_ID = hospital_id;
                        pms_thesis_list.PERSON_ID = person_id;
                        pms_thesis_list.THESIS_SORT = thesis_sort;
                        using (StreamReader reader = new StreamReader(formFile.OpenReadStream()))
                        {
                            string line = null;
                            while ((line = reader.ReadLine()) != null)
                            {
                                //  line是读取到这一行的数据
                                string[] L = line.Split('.');
                                for (int i = 0; i < L.Length; i++)
                                {
                                    if (i == 0)
                                    {
                                        //[1]林士明,陈亦鹏,潘浩等.
                                        //L[i].Split(',')
                                        // Title.Append("{\"THESIS_PERSON_SORT\":\"" + 3 + "\",");
                                        pms_thesis_list.THESIS_PERSON_SORT = "3";
                                    }
                                    if (i == 1)
                                    {
                                        //3型酸敏感离子通道在髓核致炎大鼠背根神经节中的表达研究[J]
                                        // Title.Append("\"THESIS_NAME\":\"" + L[i].ToString() + "\",");
                                        // Title.Append("\"THESIS_ITEM_NAME\":\"" + L[i].ToString() + "\",");
                                        string thesisName = L[i].ToString().Substring(0, L[i].ToString().Length - 3);
                                        pms_thesis_list.THESIS_NAME = thesisName;
                                        pms_thesis_list.THESIS_ITEM_NAME = thesisName;
                                        // M--专著 C--论文集 N--报纸文章J--期刊文章 D--学位论文 R--报告对于不属于上述的文献类型，采用字母“Z”标识。
                                    }
                                    if (i == 2)
                                    {
                                        //   浙江医学,2017,39(24):2205 - 2209.
                                        // Title.Append("\"THESIS_PUBLISH\":\"" + L[i].Split(',')[0] + "\",");
                                        // Title.Append("\"ISSUED_TIME\":\"" + L[i].Split(',')[1] + "\",");
                                        pms_thesis_list.THESIS_PUBLISH = L[i].Split(',')[0].ToString();
                                        string year = L[i].Split(',')[1].ToString();
                                        string week_vakue = L[i].Split(',')[2].ToString();
                                        int week = Convert.ToInt32(week_vakue.Substring(0, week_vakue.IndexOf("(")));
                                        pms_thesis_list.ISSUED_TIME = DateTimeByWeekAndDay(year, week, 0).ToString("yyyy-MM-dd");
                                    }
                                    if (i == 3)
                                    {
                                        //DOI:10.
                                    }
                                    if (i == 4)
                                    {
                                        //12056/j.
                                    }
                                    if (i == 5)
                                    {
                                        //issn.
                                        if (L[i].ToString() == "issn")
                                        {

                                        }
                                    }
                                    if (i == 6)
                                    {
                                        //1006-2785.
                                    }
                                    if (i == 7)
                                    {
                                        //2017.
                                    }
                                    if (i == 8)
                                    {
                                        //39.
                                    }
                                    if (i == 9)
                                    {
                                        //24.
                                    }
                                    if (i == 10)
                                    {
                                        //2017-918.
                                    }
                                }
                                //var TitleStr = "[" + Title.ToString().TrimEnd(',') + "}]";
                                // result = "{\"TABLEDATA\":" + TitleStr + "}";
                                pmsThesisList = _IPmsService.ImportThesisRecord(pms_thesis_list, user_name);
                            }
                        }
                    }
                }
            }
            catch (Exception e)
            {
                success = false;
                msg = e.Message;
            }
            return Ok(new ResultDto()
            {
                success = success,
                msg = msg,
                data = pmsThesisList
            });
        }

        /// <summary>
        /// 获取未处理的记录单文件
        /// </summary>
        /// <param name="obj">{"ARCHIVE_TABLE":"","PERSON_ID":""}</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult GetNotHandleRecordFile(dynamic obj)
        {
            var claim = this.User.ToClaimsDto();
            dynamic json = JsonConvert.DeserializeObject(Convert.ToString(obj));
            // string hospital_id = json["HOSPITAL_ID"].ToString();
            string hospital_id = claim.HOSPITAL_ID;
            string archive_table = json["ARCHIVE_TABLE"].ToString();//要操作分类的名称
            string person_id = json["PERSON_ID"].ToString();
            List<PMS_PERSON_FILE> pms_person_file = _IPmsService.GetNotHandleRecordFile(hospital_id, archive_table, person_id);
            return Ok(pms_person_file.ToResultDto());
        }
        /// <summary>
        /// 根据记录单ID获取对应文件
        /// </summary>
        /// <param name="obj">{"ARCHIVE_TABLE":"表","PERSON_ID":"人员ID","RECORD_ID":"表单ID"}</param>
        /// <returns></returns>
        [HttpPost]

        [CustomResponseType(typeof(List<PMS_PERSON_FILE>))]
        public IActionResult GetRecordFileByRecordId(dynamic obj)
        {
            var claim = this.User.ToClaimsDto();
            dynamic json = JsonConvert.DeserializeObject(Convert.ToString(obj));
            string hospital_id = claim.HOSPITAL_ID;
            string archive_table = json["ARCHIVE_TABLE"].ToString();
            string record_id = json["RECORD_ID"].ToString();
            string person_id = json["PERSON_ID"].ToString();
            var Result = _IPmsService.GetRecordFileByRecordId(hospital_id, archive_table, record_id, person_id);
            return Ok(Result.ToResultDto());
        }
        /// <summary>
        /// 记录单修改对应附件和修改记录单文件状态
        /// </summary>
        /// <param name="obj">{"ACTION":"","ARCHIVE_TABLE":"","RECORD_ID":"","FILE_ID":"","FILE_STATE":"","USER_NAME":""}</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult AlterRecordFileState(dynamic obj)
        {
            dynamic json = JsonConvert.DeserializeObject(Convert.ToString(obj));
            var claim = this.User.ToClaimsDto();
            string action = json["ACTION"].ToString();
            string archive_table = json["ARCHIVE_TABLE"].ToString();
            string record_id = json["RECORD_ID"].ToString();
            string file_id = json["FILE_ID"].ToString();
            string file_state = json["FILE_STATE"].ToString();
            string user_name = claim.HIS_NAME;
            var result = _IPmsService.AlterRecordFileState(action, archive_table, record_id, file_id, file_state, user_name);
            return Ok(result);
        }

        /// <summary>
        /// 删除记录单文件
        /// </summary>
        /// <param name="obj">{"FILE_ID":"","ARCHIVE_TABLE":"","RECORD_ID":""}</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult DeleteRecordFileState(dynamic obj)
        {
            dynamic json = JsonConvert.DeserializeObject(Convert.ToString(obj));
            var claim = this.User.ToClaimsDto();
            string file_id = json["FILE_ID"].ToString();
            string archive_table = json["ARCHIVE_TABLE"].ToString();
            string record_id = json["RECORD_ID"].ToString();
            var result = _IPmsService.DeleteRecordFileState(file_id, archive_table, record_id, claim.HIS_NAME);
            return Ok(result);
        }

        /// <summary>
        /// 删除未选择文件
        /// </summary>
        /// <param name="obj">{"FILE_ID":""}</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult DeleteNotHandleFile(dynamic obj)
        {
            dynamic json = JsonConvert.DeserializeObject(Convert.ToString(obj));
            string file_id = json["FILE_ID"].ToString();
            var result = _IPmsService.DeleteNotHandleFile(file_id);
            return Ok(result);
        }


        /// <summary>
        /// 旋转记录单
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult SpinRecordFile(dynamic obj)
        {
            dynamic json = JsonConvert.DeserializeObject(Convert.ToString(obj));
            string person_id = json["PERSON_ID"].ToString();
            string file_id = json["FILE_ID"].ToString();
            var result = _IPmsService.SpinRecordFile(file_id, person_id);
            return Ok(result);
        }

        /// <summary>
        /// 获取人员信息修改记录
        /// </summary>
        /// <param name="obj">{"PERSON_ID":"","HOSPITAL_ID":"","FIELD_NAME":"","START_TIME":"","END_TIME":""}</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult GetPersonInfoAlterLog(dynamic obj)
        {
            var claim = this.User.ToClaimsDto();
            dynamic json = JsonConvert.DeserializeObject(Convert.ToString(obj));
            string person_id = json["PERSON_ID"].ToString();
            string hospital_id = claim.HOSPITAL_ID;
            string start_time = json["START_TIME"].ToString();
            string end_time = json["END_TIME"].ToString();
            List<PMS_CHANGE_LOG> pms_change_log = _IPmsService.GetPersonInfoAlterLog(person_id, hospital_id, start_time, end_time);
            return Ok(pms_change_log.ToResultDto());
        }

        /// <summary>
        /// 获取分类和未审核表单信息
        /// </summary>
        /// <param name="obj">{"MODULE_ID":"","PERSON_ID":"","STATUS":"","ARCHIVE_TABLE":"","LAB_ID":"","USER_NO":""}</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult GetSysClassAndCheckInfo(SysClassAndCheckInfoParm json)
        {
            var claim = this.User.ToClaimsDto();
            string hospital_id = json.HOSPITAL_ID ?? claim.HOSPITAL_ID;
            string module_id = claim.MODULE_ID;
            string person_id = json.PERSON_ID;
            string status = json.STATUS;
            string archive_table = json.ARCHIVE_TABLE;
            string lab_id = json.LAB_ID;
            string smblFlag = json.SMBL_FLAG;
            string user_no = claim.USER_NO;
            string dateType = json.DATE_TYPE;
            string startDate = json.START_DATE;
            string endDate = json.END_DATE;
            string idName = json.ID_NAME;
            string searchName=json.SEARCH_NAME;
            var Result = _IPmsService.GetSysClassAndCheckInfo(module_id, hospital_id, person_id, status, archive_table, lab_id, user_no, smblFlag, dateType,startDate,endDate,idName,searchName);
            return Ok(Result.ToResultDto());
        }

        /// <summary>
        /// 审核驳回记录单信息
        /// </summary>
        /// <param name="obj">{"RECORD_ID":"","ARCHIVE_TABLE":"","USER_NAME":"","PERSON_ID":"","HOSPITAL_ID":"","ACTION":"C审核T撤销","CHANGE_CAUSE":""}</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult CheckRejectRecordClassInfo(dynamic obj)
        {
            ResultDto resultDto = new ResultDto();
            try
            {
                int resultRow = 0;
                var claim = this.User.ToClaimsDto();
                dynamic json1 = JsonConvert.DeserializeObject(Convert.ToString(obj));
                string recordobj = json1["recordObj"].ToString();
                dynamic json = JsonConvert.DeserializeObject(Convert.ToString(recordobj));
                string pwd = json1["PWD"].ToString();
                var objCheck = new
                {
                    logId = claim.LOGID,
                    password = pwd
                };
                string jsonStr = JsonConvert.SerializeObject(objCheck);
                bool success = _systemService.UserVerify(jsonStr).success;
                if (success)
                {
                    for (int i = 0; i < json.Count; i++)
                    {
                        string record_id = json[i]["RECORD_ID"].ToString();
                        string archive_table = json[i]["ARCHIVE_TABLE"].ToString();
                        string user_name = claim.HIS_NAME;
                        string person_id = null;//json[i]["PERSON_ID"].ToString();  //考试及评估报错
                        string hospital_id = claim.HOSPITAL_ID;
                        string userNo = claim.USER_NO;
                        string action = json[i]["ACTION"].ToString();
                        string change_cause = json[i]["CHANGE_CAUSE"].ToString();
                        resultRow += _IPmsService.CheckRejectRecordClassInfo(record_id, archive_table, user_name, person_id, hospital_id, action, change_cause, userNo);
                    }
                    resultDto = resultRow.ToResultDto();
                }
                else
                {
                    resultDto.success = false;
                    resultDto.msg = "密码错误";
                }
            }
            catch (Exception ex)
            {
                resultDto.msg = ex.Message;
                resultDto.success = false;
            }
            return Ok(resultDto);
        }

        /// <summary>
        /// 获取模块对应分类信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult GetSysClassInfo(string? systemType)
        {
            string module_id = "H81";
            systemType = systemType ?? "1";
            var Result = _IPmsService.GetSysClassInfo(module_id, systemType);
            return Ok(Result.ToResultDto());
        }
        /// <summary>
        /// 获取人员及档案分类信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetSysClassInfoByPersonId(string? personId, string tagId, string? systemType)
        {
            var userId = this.User.ToClaimsDto().USER_NO;
            if (personId.IsNullOrEmpty())
            {
                personId = _IPmsService.GetPersonIdByUserId(userId);
            }
            systemType = systemType ?? "1";
            var Result = _IPmsService.GetSysClassInfoByPersonId(personId, userId, tagId, systemType);
            return Ok(Result.ToResultDto());
        }
        /// <summary>
        /// 修改表单对应附件名称
        /// </summary>
        /// <param name="obj">{"FILE_ID":"","FILE_NAME":"","LAST_MPERSON":"","PERSON_ID":"","ARCHIVE_TABLE":""}</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult ModifyRecordFileName(dynamic obj)
        {
            dynamic json = JsonConvert.DeserializeObject(Convert.ToString(obj));
            var claim = this.User.ToClaimsDto();
            string file_id = json["FILE_ID"].ToString();
            string person_id = json["PERSON_ID"].ToString();//操作人ID
            string file_name = json["FILE_NAME"].ToString();
            string last_mperson = claim.HIS_NAME;
            string archive_table = json["ARCHIVE_TABLE"].ToString();
            var resultRow = _IPmsService.ModifyRecordFileName(file_id, file_name, person_id, archive_table, last_mperson);
            return Ok(resultRow);
        }

        public static DateTime DateTimeByWeekAndDay(string Year, int week, int day)
        {
            DateTime someTime = Convert.ToDateTime(Year + "-01-01");

            int i = someTime.DayOfWeek - DayOfWeek.Monday;
            if (i == -1)
                i = 6;// i值 > = 0 ，因为枚举原因，Sunday排在最前，此时Sunday-Monday=-1，必须+7=6。
            TimeSpan ts = new TimeSpan(i, 0, 0, 0);

            //获取第N周的星期一
            someTime = someTime.Subtract(ts).AddDays((week - 1) * 7);
            //获得星期几
            someTime = someTime.AddDays(day - 1);
            return someTime;
        }

        /// <summary>
        /// 批量提交撤销
        /// </summary>
        /// <param name="recordDto"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult BatchSubmitRecordInfo(SubmitRecordDto recordDto)
        {
            var claim = this.User.ToClaimsDto();
            string user_name = claim.HIS_NAME;
            string hospital_id = claim.HOSPITAL_ID;
            string userId = claim.LOGID;
            var resultRow = _IPmsService.BatchSubmitRecordInfo(recordDto.RECORD_ID, recordDto.ARCHIVE_TABLE, user_name, hospital_id, recordDto.OPERATE_TYPE, recordDto.NEXT_OPERATOR, userId, recordDto.OPERATE_CASUE, recordDto.PWD);
            return Ok(resultRow);
        }

        /// <summary>
        /// 统计人员所有分类信息 【前端表示目前没有使用该接口】
        /// </summary>
        /// <param name="obj">{"PERSON_ID":"121"}</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult GetClassMiddleByPersonInfo(dynamic obj)
        {
            dynamic json = JsonConvert.DeserializeObject(Convert.ToString(obj));
            var claim = this.User.ToClaimsDto();
            string module_id = claim.MODULE_ID;
            string person_id = json["PERSON_ID"].ToString();
            string strResult = _IPmsService.GetClassMiddleByPersonInfo(module_id, person_id);
            var Result = JsonHelper.FromJson<ClassMiddleDto>(strResult);
            return Ok(Result.ToResultDto());
        }

        /// <summary>
        /// 删除人员记录单信息
        /// </summary>
        /// <param name="obj">{"ARCHIVE_TABLE":"PMS_RESUME_LIST","RECORD_ID":"313"}</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult DeleteRecordInfo(dynamic obj)
        {

            dynamic json = JsonConvert.DeserializeObject(Convert.ToString(obj));
            string archive_table = json["ARCHIVE_TABLE"].ToString();//要操作分类的表名
            string record_id = json["RECORD_ID"].ToString();
            var resultRow = _IPmsService.DeleteRecordInfo(archive_table, record_id);
            return Ok(resultRow);
        }

        /// <summary>
        /// 获取人事档案记录操作日志流水
        /// </summary>
        /// <param name="record_id"></param>
        /// <param name="is_show_all"></param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetRecordOperLogList(string record_id)
        {
            var resultRow = _IPmsService.GetRecordOperLogList(record_id);
            return Ok(resultRow);
        }
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [AllowAnonymous]
        public IActionResult AutoDispatchUploadFile([FromForm] UploadZipDto zipFile)
        {
            if (!zipFile.FILE_SUFFIX?.ToLower()?.EndsWith("zip") ?? false && !zipFile.FILE_NAME.ToLower().EndsWith(".zip"))
            {
                throw new Exception("档案附件自动上传只支持zip格式！");
            }
            Dictionary<string, object> dataDict = new Dictionary<string, object>();
            var claim = this.User.ToClaimsDto();
            dataDict["HOSPITAL_ID"] = claim.HOSPITAL_ID;
            ResultDto resultDto = _IPmsService.AutoDispatchUploadFile(dataDict, zipFile);
            return Ok(resultDto);
        }

        [HttpGet]
        public IActionResult GetUploadFileTemplate(string personId)
        {
            var result = _IPmsService.GetUploadFileTemplate(personId);
            return File(result, "application/zip", "人事档案附件上传.zip");
        }


        /// <summary>
        /// 获取基础数据
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult GetOaBaseData(string classId)
        {
            ResultDto result = new ResultDto();
            try
            {
                result = _IBaseDataServices.GetOaBaseData(classId).ToResultDto();
            }
            catch (Exception e)
            {
                result.msg = e.Message;
                result.success = false;
            }
            return Ok(result);
        }
        /// <summary>
        /// 获取基础数据分类
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetBaseDataClassList()
        {
            ResultDto result = new ResultDto();
            try
            {
                result = _IBaseDataServices.GetBaseDataClassList().ToResultDto();
            }
            catch (Exception e)
            {
                result.msg = e.Message;
                result.success = false;
            }
            return Ok(result);
        }
        /// <summary>
        /// 修改基础数据类型
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult SaveBaseDataClass(BaseDataClassDto dto)
        {
            ResultDto result = new ResultDto();
            var claim = User.ToClaimsDto();
            string hisName = claim.HIS_NAME;
            try
            {
                result = _IBaseDataServices.SaveBaseDataClass(dto, hisName).ToResultDto();
            }
            catch (Exception e)
            {
                result.msg = e.Message;
                result.success = false;
            }
            return Ok(result);
        }
        /// <summary>
        /// 删除基础数据类型
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult DeleteBaseDataClass(string dataClassId)
        {
            ResultDto result = new ResultDto();
            var claim = User.ToClaimsDto();
            string hisName = claim.HIS_NAME;
            try
            {
                result = _IBaseDataServices.DeleteBaseDataClass(dataClassId, hisName).ToResultDto();
            }
            catch (Exception e)
            {
                result.msg = e.Message;
                result.success = false;
            }
            return Ok(result);
        }

        /// <summary>
        /// 新增、修改实验室管理基础数据
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult AddOrModifyOaBaseData(OaBaseDataDto data)
        {
            ResultDto result = new ResultDto();
            try
            {
                result = _IBaseDataServices.AddOrModifyOaBaseData(data).ToResultDto();
            }
            catch (Exception e)
            {
                result.msg = e.Message;
                result.success = false;
            }
            return Ok(result);
        }

        /// <summary>
        /// 删除实验室管理基础数据
        /// </summary>
        /// <param name="DataId"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult DelOaBaseData(OaBaseDataDto Data)
        {
            ResultDto result = new ResultDto();
            try
            {
                result = _IBaseDataServices.DelOaBaseData(Data).ToResultDto();
            }
            catch (Exception e)
            {
                result.msg = e.Message;
                result.success = false;
            }
            return Ok(result);
        }

        /// <summary>
        /// 基础数据重新排序
        /// </summary>
        /// <param name="sortedDataIds"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult SortOaBaseData(List<OaBaseDataDto> sortedData)
        {
            ResultDto result = new ResultDto();
            try
            {
                result = _IBaseDataServices.SortOaBaseData(sortedData).ToResultDto();
            }
            catch (Exception e)
            {
                result.msg = e.Message;
                result.success = false;
            }
            return Ok(result);
        }


        /// <summary>
        /// 禁用实验室管理基础数据
        /// </summary>
        /// <param name="DataId"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [HttpPost]
        public IActionResult ForbidOaBaseData(string state, string DataId)
        {
            ResultDto result = new ResultDto();
            try
            {
                result = _IBaseDataServices.ForbidOaBaseData(state, DataId).ToResultDto();
            }
            catch (Exception e)
            {
                result.msg = e.Message;
                result.success = false;
            }
            return Ok(result);
        }


        /// <summary>
        /// 保存证书规评明细
        /// </summary>
        /// <param name="listSkillStdSchemeDto"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult SaveSkillCertStdSchemeDetail(List<PMS_SKILL_CERTIFICATE_LIST> listSkillStdSchemeDto)
        {
            var claim = this.User.ToClaimsDto();
            var Result = _IPmsService.SaveSkillCertStdSchemeDetail(listSkillStdSchemeDto, claim.HIS_NAME, claim.HOSPITAL_ID);
            return Ok(Result);
        }


        /// <summary>
        /// 保存证书规评分组信息
        /// </summary>
        /// <param name="skillCert"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult SaveSkillCertStdScheme(PMS_SKILL_CERTIFICATE_LIST skillCert)
        {
            var claim = this.User.ToClaimsDto();
            var Result = _IPmsService.SaveSkillCertStdScheme(skillCert, claim.HIS_NAME, claim.HOSPITAL_ID);
            return Ok(Result);
        }

        /// <summary>
        /// 删除证书分组明细信息
        /// </summary>
        /// <param name="stdGroupId"></param>
        /// <param name="listUserId"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult DeleteSkillStdDetail(DeleteDStdDetailDto deleteDStdDetailDto)
        {
            var claim = this.User.ToClaimsDto();
            var Result = _IPmsService.DeleteSkillStdDetail(deleteDStdDetailDto.StdGroupId, deleteDStdDetailDto.ListUserId, claim.HIS_NAME);
            return Ok(Result);
        }
        /// <summary>
        /// 获取证书规评明细信息
        /// </summary>
        /// <param name="stdGroupId">规评分组id</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetSkillStdDetail(string stdGroupId)
        {
            var claim = this.User.ToClaimsDto();
            var Result = _IPmsService.GetSkillStdDetail(stdGroupId);
            return Ok(Result);
        }


        /// <summary>
        /// 获取证书规评分组信息
        /// </summary>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <param name="certType">证书类型</param>
        /// <param name="certName">证书名称</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetSkillStd(string? startDate, string? endDate, string? certType, string? certName)
        {
            var claim = this.User.ToClaimsDto();
            var Result = _IPmsService.GetSkillStd(startDate, endDate, certType, certName);
            return Ok(Result);
        }
        /// <summary>
        /// 删除资质证书规评
        /// </summary>
        /// <param name="certId"></param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult DeleteSkillStd(string certId)
        {
            var claim = this.User.ToClaimsDto();
            var Result = _IPmsService.DeleteSkillStd(certId, claim.HIS_NAME);
            return Ok(Result);
        }

        /// <summary>
        /// 查询分析、或者按专业组查询
        /// </summary>
        /// <param name="json"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult GetQueryAnanysis(AnalysisInfoByClassParm json)
        {
            var Result = _IPmsService.GetQueryAnanysis(json.ARCHIVE_TABLE, json.DATE_TYPE, json.START_DATE,json.END_DATE,json.AREA_ID,json.PGROUP_ID, json.LIST_PERSONID,json.ID_NAME, json.SEARCH_NAME,json.SMBL_FLAG,json.STATUS,json.tagId);
            return Ok(Result.ToResultDto());
        }


        /// <summary>
        /// 统计分析
        /// </summary>
        /// <param name="archiveTable">分类名</param>
        /// <param name="labId">科室id</param>
        /// <param name="areaId">院区id</param>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult GetStataisicsAnanysis(StataisicsAnanysisDto stataisicsAnanysisDto)
        {
            var claim = this.User.ToClaimsDto();
            if (stataisicsAnanysisDto.hospitalId.IsNullOrEmpty())
                stataisicsAnanysisDto.hospitalId = claim.HOSPITAL_ID;
            var Result = _IPmsService.GetStataisicsAnanysis(stataisicsAnanysisDto.archiveTable, stataisicsAnanysisDto.labId, stataisicsAnanysisDto.areaId, stataisicsAnanysisDto.pgroupId, stataisicsAnanysisDto.startDate, stataisicsAnanysisDto.endDate, stataisicsAnanysisDto.smblFlag, stataisicsAnanysisDto.hospitalId, stataisicsAnanysisDto.dateType, stataisicsAnanysisDto.idName, stataisicsAnanysisDto.searchName, stataisicsAnanysisDto.listRecord);
            return Ok(Result.ToResultDto());
        }

        /// <summary>
        /// 获取访客数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetVisitRequestInfo()
        {
            var claim = this.User.ToClaimsDto();
            var Result = _IPmsService.GetVisitRequestInfo();
            return Ok(Result.ResultDto());
        }

        /// <summary>
        /// 获取系统设置
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetSetupValue()
        {
            var claims = this.User.ToClaimsDto();
            var Result = await _IBaseDataServices.GetSetupValue(claims.HOSPITAL_ID, "");
            return Ok(Result.ToResultDto());
        }
    }


    public class User
    {
        public String uid { get; set; }
        public String name { get; set; }
        public String oldName { get; set; }
    }
}
