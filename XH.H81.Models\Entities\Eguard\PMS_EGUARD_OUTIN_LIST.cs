﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Entities.Eguard
{
    /// <summary>
    /// 实验室门禁出入记录表
    /// </summary>
    [DBOwner("XH_OA")]
    public class PMS_EGUARD_OUTIN_LIST
    {
        /// <summary>
        /// 门禁出入ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string EGUARD_OUTIN_ID { get; set; }
        /// <summary>
        /// 门禁ID
        /// </summary>
        public string EGUARD_DICT_ID { get; set; }
        /// <summary>
        /// 医疗机构ID
        /// </summary>
        public string HOSPITAL_ID { get; set; }
        /// <summary>
        /// 人员类型 0 访客  1 科内人员
        /// </summary>
        public string? PERSON_TYPE { get; set; }
        /// <summary>
        /// 出入人员ID
        /// </summary>
        public string PERSON_ID { get; set; }
        /// <summary>
        /// 抓拍时间
        /// </summary>
        public DateTime? SNAP_TIME { get; set; }
        /// <summary>
        /// 抓拍图片
        /// </summary>
        public string? SNAP_PIC { get; set; }
        /// <summary>
        /// 进出方向（0 进 1 出）
        /// </summary>
        public string? ACCESS_DIRECTION { get; set; }
        /// <summary>
        /// 申请记录ID
        /// </summary>
        public string? VISIT_REQ_ID { get; set; }
        /// <summary>
        /// 状态（0 禁用 1 在用 2 删除）
        /// </summary>
        public string? EGUARD_OUTIN_STATE { get; set; }
        public string? FIRST_RPERSON { get; set; }
        public DateTime? FIRST_RTIME { get; set; }
        public string? LAST_MPERSON { get; set; }
        public DateTime? LAST_MTIME { get; set; }
        public string? REMARK { get; set; }
    }
}
