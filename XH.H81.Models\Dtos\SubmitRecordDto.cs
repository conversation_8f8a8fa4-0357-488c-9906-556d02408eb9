﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Dtos
{
    public class SubmitRecordDto
    {
        /// <summary>
        /// 记录id
        /// </summary>
        public string RECORD_ID { get; set; }
        /// <summary>
        /// 分类代码
        /// </summary>
        public string ARCHIVE_TABLE { get; set; }

        /// <summary>
        /// 操作类型 R撤销 B提交
        /// </summary>
        public string OPERATE_TYPE { get; set; }

        /// <summary>
        /// 撤销原因
        /// </summary>
        public string? OPERATE_CASUE { get; set; }

        /// <summary>
        /// 审核人
        /// </summary>
        public string? NEXT_OPERATOR { get; set; }
        /// <summary>
        /// 密码
        /// </summary>
        public string? PWD { get; set; }

      
    }
}
