﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Dtos.EvaluatePlan
{
    public class PostInfoDto
    {
        /// <summary>
        /// 岗位角色ID
        /// </summary>
        public string POSTROLE_ID { get; set; }
        public string POST_ID { get; set; }
        public string UPOST_ID { get; set; }

        public string LAB_ID { get; set; }

        public string? FIRST_RPERSON { get; set; }

        public string? POST_NAME { get; set; }

        public DateTime? LAST_MTIME { get; set; }

        /// <summary>
        /// 岗位类型 1-主班 2-辅班
        /// </summary>
        public string? POST_TYPE { get; set; }
        /// <summary>
        /// 岗位类型 1-主班 2-辅班
        /// </summary>
        public string? POST_TYPE_NAME { get; set; }

        public string? POST_SORT { get; set; }

        public string? POST_STATE { get; set; }

        public string? REMARK { get; set; }

        public DateTime? FIRST_RTIME { get; set; }

        public string? LAST_MPERSON { get; set; }

        /// <summary>
        /// 岗位分类 1-管理岗 2-技术岗 3-业务岗
        /// </summary>
        public string? POST_CLASS { get; set; }
        /// <summary>
        /// 岗位分类 1-管理岗 2-技术岗 3-业务岗
        /// </summary>
        public string? POST_CLASS_NAME { get; set; }
        /// <summary>
        /// 岗位描述
        /// </summary>
        public string? POST_DESC { get; set; }

        public string? PGROUP_ID { get; set; }

        public string HOSPITAL_ID { get; set; }
        /// <summary>
        /// 岗位职责说明
        /// </summary>
        public string? POST_DUTY { get; set; }

        public string? POST_ULEVEL { get; set; }
        /// <summary>
        /// 岗位角色字典ID
        /// </summary>
        public string? PROLE_DICT_ID { get; set; }
        /// <summary>
        /// 岗位角色名称
        /// </summary>
        public string? POSTROLE_NAME { get; set; }
        /// <summary>
        /// 岗位角色排序
        /// </summary>
        public string? POSTROLE_SORT { get; set; }

        /// <summary>
        /// 起始日期
        /// </summary>
        public DateTime? UPOST_USE_SDATE { get; set; }
        /// <summary>
        /// 有效日期
        /// </summary>
        public DateTime? UPOST_USE_EDATE { get; set; }
        /// <summary>
        /// 结束日期
        /// </summary>
        public DateTime? UPOST_END_DATE { get; set; }

        /// <summary>
        /// 状态:0停岗、1在用、2结束
        /// </summary>
        public string? USER_POST_STATE { get; set; }
        /// <summary>
        /// 状态:0停岗、1在用、2结束
        /// </summary>
        public string? USER_POST_STATE_NAME { get; set; }

        /// <summary>
        /// 专业组人员列表
        /// </summary>
        public object PgroupPersonList { get; set; }
    }

    public class PersonPostInfoDto : PostInfoDto
    {
        public string USER_ID { get; set; }
        public string PERSON_ID { get; set; }
        public string USER_NAME { get; set; }
        public string HIS_ID { get; set; }
        public string SEX_NAME { get; set; }
        public string PGROUP_ID { get; set; }
        public string PGROUP_NAME { get; set; }

        public DateTime? FIRST_RTIME { get; set; }
        public string FIRST_RPERSON { get; set; }
        public DateTime? LAST_MTIME { get; set; }
        public string LAST_MPERSON { get; set; }

    }

}
