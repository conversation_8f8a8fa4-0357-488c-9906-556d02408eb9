﻿using H.Utility;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using XH.H81.API.Extensions;
using XH.H81.IServices;
using XH.H81.Models.Dtos.Exam;
using XH.H81.Models.Dtos.Pms;
using XH.H81.Models.Entities.Pms;

namespace XH.H81.API.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize]
    public class PmsAssessController : ControllerBase
    {
        private IPmsAssessService _pmsAssessService;
        private readonly IBaseDataServices _IBaseDataService;
        public PmsAssessController(IPmsAssessService  pmsAssessService, IBaseDataServices ibasedataService)
        {
            _pmsAssessService = pmsAssessService;
            _IBaseDataService = ibasedataService;
        }

        /// <summary>
        /// 获取用户评估信息
        /// </summary>
        /// <param name="userNo">用户id</param>
        /// <param name="labId">科室id</param>
        /// <param name="mgroupId">管理专业组id</param>
        /// <param name="pgroupId">专业组id</param>
        /// <param name="assessPlanState">状态</param>
        /// <param name="assessPlanType">评估类型</param>
        /// <param name="assessPlanName">评估名称</param>
        /// <param name="smblFlag">生安标志</param>
        /// <param name="dateType">日期类型</param>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <param name="cerId">证书id</param>
        /// <returns></returns>
        [HttpGet]
        [CustomResponseType(typeof(List<PmsAssessPlanPersonDto>))]
        public IActionResult GetUserAssessInfo(string? userNo, string? labId, string? mgroupId, string? pgroupId, string? assessPlanState, string? assessPlanType, string? assessPlanName, string? smblFlag,string? dateType,string? startDate,string? endDate,string? cerId)
        {
            var claim = this.User.ToClaimsDto();
            var Result = _pmsAssessService.GetUserAssessInfo(userNo, labId, mgroupId, pgroupId, assessPlanState, assessPlanType, assessPlanName, smblFlag, dateType, startDate, endDate,cerId, claim.HOSPITAL_ID);
            return Ok(Result);
        }

        /// <summary>
        /// 保存评估信息
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult SaveAssesInfo(PmsAssessPlanPersonDto dto)
        {
            var claim = this.User.ToClaimsDto();
            var Result = _pmsAssessService.SaveAssessInfo(dto, claim.HIS_NAME, claim.HOSPITAL_ID);
            return Ok(Result);
        }
        /// <summary>
        /// 更新评估状态
        /// </summary>
        /// <param name="planpPersonIds">id合集</param>
        /// <param name="assessPlanType">操作状态 1提交 2驳回 3审核 5撤销 6删除</param>
        /// <param name="checkPerson">指定审核人</param>
        /// <param name="operCause">驳回原因</param>
        /// <param name="operComputer">操作电脑</param>
        /// <param name="pwd">密码</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UpdateAssessPlanInfo(OperateAssessPlanDto operateAssessPlanDto)
        {
            var claim = this.User.ToClaimsDto();
            var Result = _pmsAssessService.UpdateAssessPlanInfo(operateAssessPlanDto.PlanpPersonIds, operateAssessPlanDto.AssessPlanType, claim.HIS_NAME, claim.HOSPITAL_ID, operateAssessPlanDto.CheckPerson, operateAssessPlanDto.OperCause, operateAssessPlanDto.OperComputer, operateAssessPlanDto.Pwd, claim.LOGID);
            return Ok(Result);
        }
        /// <summary>
        /// 保存规评明细
        /// </summary>
        /// <param name="pmsAssessPlanPersonDtos"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult SavePmsStdSchemeDetail(List<PmsAssessPlanPersonDto> pmsAssessPlanPersonDtos)
        {
            var claim = this.User.ToClaimsDto();
            var Result = _pmsAssessService.SavePmsAssessStdSchemeDetail(pmsAssessPlanPersonDtos, claim.HIS_NAME, claim.HOSPITAL_ID);
            return Ok(Result);
        }


        /// <summary>
        /// 保存规评分组信息
        /// </summary>
        /// <param name="pmsAssessPlanPersonDto"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult SavePmsStdScheme(PmsAssessPlanPersonDto pmsAssessPlanPersonDto)
        {
            var claim = this.User.ToClaimsDto();
            var Result = _pmsAssessService.SavePmsAssessStdScheme(pmsAssessPlanPersonDto, claim.HIS_NAME, claim.HOSPITAL_ID);
            return Ok(Result);
        }

        /// <summary>
        /// 删除分组明细信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult DeletePmsStdDetail(DeleteDStdDetailDto deleteDStdDetailDto)
        {
            var claim = this.User.ToClaimsDto();
            var Result = _pmsAssessService.DeletePmsAssessStdDetail(deleteDStdDetailDto.StdGroupId, deleteDStdDetailDto.ListUserId, claim.HIS_NAME);
            return Ok(Result);
        }
        /// <summary>
        /// 获取规评明细信息
        /// </summary>
        /// <param name="stdGroupId"></param>
        /// <returns></returns>
        [HttpGet]

        [CustomResponseType(typeof(List<PmsAssessPlanPersonDto>))]
        public IActionResult GetPmsStdDetail(string stdGroupId, string? labPgroupId, string? comName, string? userName)
        {
            var claim = this.User.ToClaimsDto();
            var Result = _pmsAssessService.GetPmsAssessStdDetail(stdGroupId, labPgroupId, comName, userName);
            return Ok(Result);
        }


        /// <summary>
        /// 获取规评分组信息
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="labGroupId"></param>
        /// <param name="eplanName"></param>
        /// <param name="assessName"></param>
        /// <returns></returns>
        [HttpGet]
        [CustomResponseType(typeof(List<PmsAssessPlanPersonDto>))]
        public IActionResult GetPmsStd(string? startDate, string? endDate, string? labGroupId, string? eplanName, string? assessName,string?smblFlag)
        {
            var claim = this.User.ToClaimsDto();
            var Result = _pmsAssessService.GetPmsAssessStd(startDate, endDate, labGroupId, eplanName, assessName, smblFlag);
            return Ok(Result);
        }
    }
}
