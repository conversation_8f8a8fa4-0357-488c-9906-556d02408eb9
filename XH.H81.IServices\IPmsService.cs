﻿using H.Utility;
using Microsoft.AspNetCore.Mvc;
using XH.H81.Models.Dtos;
using XH.H81.Models.Dtos.External;
using XH.H81.Models.Dtos.Pms;
using XH.H81.Models.Entities.Pms;
using XH.LAB.UTILS.Models;
using XH.LAB.UTILS.Models.Dto;

namespace XH.H81.IServices.Pms
{
    public interface IPmsService
    {
        int SaveOrganizationChart(string hospital_id, string lab_id, string user_id, string edit_person, string strFilePath);
        int SavePersonPhoto(string uploadPath, string person_id);

        int SavePersonFacePhoto(string uploadPath, string uploadCompressPath, string person_id);
        ResultDto SavePersonInfo(PMS_PERSON_INFO pms_person_info, string edit_person, string userInfoJson);
        ResultDto DeletePerson(string personId);
        void FillPersonFileInfo(PMS_PERSON_FILE PersonFile);
        List<object> GetRecordInfoByClass(string archive_table, string person_id, string smblFlag, string dateType, string startDate, string endDate, string status, string searchName, string tagId);
        Dictionary<string, List<object>> GetRecordInfoByMultipleClass(List<string> archive_tables, string person_id, string smblFlag, string dateType, string startDate, string endDate, string status, string searchName, string idName, string tagId);
        List<object> GetPersonCertificateList(string personId, string certificateDid);
        List<object> GetPersonTrainList(string personId, string certificateId);
        List<object> GetRecordInfoByClassByState(string archive_table, string record_id, string check_state, string smblFlag);

        string GetOrganizationChartImg(string hospital_id, string lab_id, string module_id, string user_id);
        string GetPersonImg(string user_id);
        string SaveAnnexPath(PMS_PERSON_FILE pms_person_file, string user_name, string action, string hospital_id, string person_id, string file_class, string archive_table);
        string BatchUploadRecordFile([FromForm] PmsUploadFileDto file);
        string AlterRecordAffix(PMS_PERSON_FILE pms_person_file, string user_name, string hospital_id, string person_id, string file_class, string archive_table, string record_id);

        int AddAffix(string file_ids, string archive_table, string record_id, string user_name);
        ResultDto ReplaceUploadImage(string FILE_ID, string FILE_CNAME, UploadFileDto file);

        string RotateUploadImage(string FILE_ID, float ANGLE);
        List<PMS_CHANGE_LOG> GetPersonInfoAlterLog(string person_id, string hospital_id, string start_time, string end_time);
        string SaveRecordInfo(string archive_table, string list, string user_name, string action, string hospital_id, string person_id);

        /// <summary>
        /// 批量删除记录
        /// </summary>
        /// <param name="archive_table"></param>
        /// <param name="listRecordId"></param>
        /// <param name="user_name"></param>
        /// <param name="person_id"></param>
        /// <returns></returns>
        string BatchDeleteRecord(string archive_table, List<string> listRecordId, string user_name, string person_id, string hospital_id);

        /// <summary>
        /// 保存资质证书
        /// </summary>
        /// <param name="prefix"></param>
        /// <param name="archive_table"></param>
        /// <param name="certificate_list"></param>
        /// <param name="user_name"></param>
        /// <param name="action"></param>
        /// <param name="hospital_id"></param>
        /// <param name="person_id"></param>
        /// <param name="train_id"></param>
        /// <returns></returns>
        string SaveAttachCertificate(string archive_table, string certificate_list, string user_name, string action, string hospital_id, string person_id, string select_person, string prefix, string train_id);
        PMS_THESIS_LIST ImportThesisRecord(PMS_THESIS_LIST pms_thesis_list, string user_name);
        List<SysClassInfoDto> GetSysClassAndCheckInfo(string module_id, string hospital_id, string person_id, string status, string archive_table, string lab_id, string user_no, string smblFlag, string dateType, string startDate, string endDate, string idName, string searchName);
        int CheckRejectRecordClassInfo(string record_id, string archive_table, string user_name, string person_id, string hospital_id, string action, string change_cause, string userNo);
        List<PmsClassInfoDto> GetSysClassInfo(string module_id, string systemType = "1");
        List<PmsClassInfoDto> GetSysClassInfoByPersonId(string personId, string userId, string tagId, string systemType = "1");
        string GetPersonIdByUserId(string userId);
        ResultDto ModifyRecordFileName(string file_id, string file_name, string person_id, string archive_table, string last_mperson);
        List<PMS_PERSON_FILE> GetNotHandleRecordFile(string hospital_id, string archive_table, string person_id);

        List<PMS_PERSON_FILE> GetRecordFileByRecordId(string hospital_id, string archive_table, string record_id, string person_id);

        ResultDto AlterRecordFileState(string action, string archive_table, string record_id, string file_id, string file_state, string user_name);

        ResultDto DeleteRecordFileState(string file_id, string archive_table, string record_id, string user_name);
        ResultDto DeleteNotHandleFile(string file_id);
        ResultDto SpinRecordFile(string file_id, string person_id);
        /// <summary>
        /// 
        /// </summary>
        /// <param name="record_id"></param>
        /// <param name="archive_table"></param>
        /// <param name="user_name"></param>
        /// <param name="hospital_id"></param>
        /// <param name="operate_type"></param>
        /// <param name="nextOperator">审核操作人</param>
        /// <param name="userId"></param>
        /// <param name="operateCasue">撤销原因</param>
        /// <param name="pwd"></param>
        /// <returns></returns>
        ResultDto BatchSubmitRecordInfo(string record_id, string archive_table, string user_name, string hospital_id, string operate_type, string nextOperator, string userId, string operateCasue, string pwd);

        string GetClassMiddleByPersonInfo(string module_id, string person_id);

        ResultDto DeleteRecordInfo(string archive_table, string record_id);
        ResultDto GetRecordOperLogList(string record_id);

        ResultDto AutoDispatchUploadFile(Dictionary<string, object> dataDict, UploadZipDto zipFile);
        byte[] GetUploadFileTemplate(string personId);



        #region 资质证书规评录入
        /// <summary>
        /// 保存资质证书规评明细登记
        /// </summary>
        /// <param name="listSkillStdSchemeDto"></param>
        /// <param name="userName"></param>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        ResultDto SaveSkillCertStdSchemeDetail(List<PMS_SKILL_CERTIFICATE_LIST> listSkillStdSchemeDto, string userName, string hospitalId);


        /// <summary>
        /// 保存证书规评登记
        /// </summary>
        /// <param name="skillCert"></param>
        /// <param name="userName"></param>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        ResultDto SaveSkillCertStdScheme(PMS_SKILL_CERTIFICATE_LIST skillCert, string userName, string hospitalId);


        /// <summary>
        /// 获取证书规评信息
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="labGroupId"></param>
        /// <param name="eplanName"></param>
        /// <param name="examName"></param>
        /// <returns></returns>

        ResultDto GetSkillStd(string startDate, string endDate, string certType, string certName);

        /// <summary>
        /// 删除资质证书规评
        /// </summary>
        /// <param name="certId"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        ResultDto DeleteSkillStd(string certId, string userName);
        /// <summary>
        /// 获取证书规评分组明细
        /// </summary>
        /// <param name="stdGroupId"></param>
        /// <returns></returns>
        ResultDto GetSkillStdDetail(string stdGroupId);


        /// <summary>
        /// 删除证书规评明细
        /// </summary>
        /// <param name="stdGroupId"></param>
        /// <param name="listUserId"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        ResultDto DeleteSkillStdDetail(string stdGroupId, List<string> listUserId, string userName);
        #endregion

        /// <summary>
        /// 查询分析
        /// </summary>
        /// <param name="archive_table"></param>
        /// <param name="dateType"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="areaId"></param>
        /// <param name="pgroupId"></param>
        /// <param name="searchName"></param>
        /// <returns></returns>
        List<object> GetQueryAnanysis(string archive_table, string dateType, string startDate, string endDate, string areaId, string pgroupId, List<string> listPersonId, string IdName, string searchName, string smblFlag, string status, string tagId);

        /// <summary>
        /// 记录分析
        /// </summary>
        /// <param name="archive_table"></param>
        /// <param name="labId"></param>
        /// <param name="areaId"></param>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        StaticDataDto GetStataisicsAnanysis(string archive_table, string labId, string areaId, string pgroupId, string startDate, string endDate, string smblFlag, string hospitalId, string dateType, string idName, string searchName, List<string> listRecord);


        /// <summary>
        /// 获取当天访客数据
        /// </summary>
        /// <returns></returns>
        List<VisitRequestDto> GetVisitRequestInfo();
    }
}
