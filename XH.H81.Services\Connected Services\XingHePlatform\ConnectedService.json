{"ExtendedData": {"inputs": ["http://118.31.15.75:16700/XingHePlatform.asmx"], "collectionTypes": ["System.Array", "System.Collections.Generic.Dictionary`2"], "namespaceMappings": ["*, XingHePlatform"], "references": ["<PERSON>Mapper, {AutoMapper, 12.0.0}", "AutoMapper.Extensions.Microsoft.DependencyInjection, {AutoMapper.Extensions.Microsoft.DependencyInjection, 12.0.0}", "BouncyCastle.Crypto, {Portable.BouncyCastle, 1.8.10}", "Castle.Core, {Castle.Core, 5.1.0}", "ClickHouse.Client, {ClickHouse.Client, 6.1.1}", "CSRedisCore, {CSRedisCore, 3.6.9}", "D:\\后台起步框架\\XH.H81.API\\XH.H81.Services\\bin\\Debug\\net6.0\\XH.H81.Base.dll", "D:\\后台起步框架\\XH.H81.API\\XH.H81.Services\\bin\\Debug\\net6.0\\XH.H81.IServices.dll", "D:\\后台起步框架\\XH.H81.API\\XH.H81.Services\\bin\\Debug\\net6.0\\XH.H81.Models.dll", "D:\\后台起步框架\\XH.H81.API\\XH.H81.Services\\bin\\Debug\\net6.0\\Xinghe.Utility.dll", "D:\\后台起步框架\\XH.H81.API\\XH.H81.Services\\bin\\Debug\\net6.0\\Z.EntityFramework.Extensions.EFCore.dll", "D:\\后台起步框架\\XH.H81.API\\XH.H81.Services\\bin\\Debug\\net6.0\\Z.EntityFramework.Plus.EFCore.dll", "<PERSON><PERSON>, {<PERSON><PERSON>, 2.0.123}", "EasyCaching.Core, {EasyCaching.Core, 1.7.0}", "EasyCaching.CSRedis, {EasyCaching.CSRedis, 1.7.0}", "EasyCaching.InMemory, {EasyCaching.InMemory, 1.7.0}", "EasyCaching.Redis, {EasyCaching.Redis, 1.7.0}", "EasyCaching.Serialization.Json, {EasyCaching.Serialization.Json, 1.7.0}", "EasyCaching.Serialization.MessagePack, {EasyCaching.Serialization.MessagePack, 1.7.0}", "EasyCaching.Serialization.Protobuf, {EasyCaching.Serialization.Protobuf, 1.7.0}", "Elasticsearch.Net, {Elasticsearch.Net, 7.8.1}", "FireflySoft.RateLimit.AspNetCore, {FireflySoft.RateLimit.AspNetCore, 3.0.0}", "FireflySoft.RateLimit.Core, {FireflySoft.RateLimit.Core, 3.0.0}", "FSharp.Core, {FSharp.Core, 6.0.5}", "<PERSON><PERSON><PERSON><PERSON><PERSON>, {<PERSON><PERSON><PERSON><PERSON><PERSON>, 2.0.3}", "ICSharpCode.SharpZipLib, {SharpZipLib, 1.3.3}", "KYSharp.SM, {KYSharp.SM.Core, 1.0.1}", "MessagePack, {MessagePack, 2.3.85}", "MessagePack.Annotations, {MessagePack.Annotations, 2.3.85}", "Microsoft.AspNetCore.Authentication.JwtBearer, {Microsoft.AspNetCore.Authentication.JwtBearer, 6.0.10}", "Microsoft.Bcl.AsyncInterfaces, {Microsoft.Bcl.AsyncInterfaces, 6.0.0}", "Microsoft.Data.SqlClient, {Microsoft.Data.SqlClient, 2.1.4}", "Microsoft.EntityFrameworkCore, {Microsoft.EntityFrameworkCore, 6.0.11}", "Microsoft.EntityFrameworkCore.Abstractions, {Microsoft.EntityFrameworkCore.Abstractions, 6.0.11}", "Microsoft.EntityFrameworkCore.Relational, {Microsoft.EntityFrameworkCore.Relational, 6.0.11}", "Microsoft.EntityFrameworkCore.SqlServer, {Microsoft.EntityFrameworkCore.SqlServer, 6.0.11}", "Microsoft.Extensions.Caching.Abstractions, {Microsoft.Extensions.Caching.Abstractions, 6.0.0}", "Microsoft.Extensions.Caching.Memory, {Microsoft.Extensions.Caching.Memory, 6.0.1}", "Microsoft.Extensions.Configuration.Abstractions, {Microsoft.Extensions.Configuration.Abstractions, 6.0.0}", "Microsoft.Extensions.Configuration.Binder, {Microsoft.Extensions.Configuration.Binder, 6.0.0}", "Microsoft.Extensions.DependencyInjection, {Microsoft.Extensions.DependencyInjection, 6.0.1}", "Microsoft.Extensions.DependencyInjection.Abstractions, {Microsoft.Extensions.DependencyInjection.Abstractions, 6.0.0}", "Microsoft.Extensions.DependencyModel, {Microsoft.Extensions.DependencyModel, 3.1.6}", "Microsoft.Extensions.FileProviders.Abstractions, {Microsoft.Extensions.FileProviders.Abstractions, 3.1.8}", "Microsoft.Extensions.Hosting.Abstractions, {Microsoft.Extensions.Hosting.Abstractions, 3.1.8}", "Microsoft.Extensions.Http, {Microsoft.Extensions.Http, 6.0.0}", "Microsoft.Extensions.Logging, {Microsoft.Extensions.Logging, 6.0.0}", "Microsoft.Extensions.Logging.Abstractions, {Microsoft.Extensions.Logging.Abstractions, 6.0.0}", "Microsoft.Extensions.ObjectPool, {Microsoft.Extensions.ObjectPool, 5.0.10}", "Microsoft.Extensions.Options, {Microsoft.Extensions.Options, 6.0.0}", "Microsoft.Extensions.Options.ConfigurationExtensions, {Microsoft.Extensions.Options.ConfigurationExtensions, 6.0.0}", "Microsoft.Extensions.Primitives, {Microsoft.Extensions.Primitives, 6.0.0}", "Microsoft.IdentityModel.JsonWebTokens, {Microsoft.IdentityModel.JsonWebTokens, 6.10.0}", "Microsoft.IdentityModel.Logging, {Microsoft.IdentityModel.Logging, 6.10.0}", "Microsoft.IdentityModel.Protocols, {Microsoft.IdentityModel.Protocols, 6.10.0}", "Microsoft.IdentityModel.Protocols.OpenIdConnect, {Microsoft.IdentityModel.Protocols.OpenIdConnect, 6.10.0}", "Microsoft.IdentityModel.Tokens, {Microsoft.IdentityModel.Tokens, 6.10.0}", "Microsoft.OpenApi, {Microsoft.OpenApi, 1.3.1}", "Microsoft.Win32.SystemEvents, {Microsoft.Win32.SystemEvents, 6.0.0}", "MySqlConnector, {MySqlConnector, 2.1.2}", "Newtonsoft.J<PERSON>, {Newtonsoft<PERSON>J<PERSON>, 13.0.1}", "Nito.AsyncEx.Context, {Nito.AsyncEx.Context, 5.1.0}", "Nito.AsyncEx.Coordination, {Nito.AsyncEx.Coordination, 5.1.0}", "Nito.AsyncEx.Interop.WaitHandles, {Nito.AsyncEx.Interop.WaitHandles, 5.1.0}", "Nito.AsyncEx.Oop, {Nito.AsyncEx.Oop, 5.1.0}", "Nito.AsyncEx.Tasks, {Nito.AsyncEx.Tasks, 5.1.0}", "<PERSON><PERSON>.Cancell<PERSON>, {<PERSON><PERSON>.Cancellation, 1.1.0}", "Nito.Collections.Deque, {Nito.Collections.Deque, 1.1.0}", "Nito.Disposables, {Nito.Disposables, 2.2.0}", "NodaTime, {NodaTime, 3.1.4}", "NPOI, {NPOI, 2.5.6}", "NPOI.OOXML, {NPOI, 2.5.6}", "NPOI.OpenXml4Net, {NPOI, 2.5.6}", "NPOI.OpenXmlFormats, {NPOI, 2.5.6}", "Oracle.EntityFramework<PERSON><PERSON>, {Oracle.EntityFrameworkCore, 6.21.61}", "Oracle.ManagedDataAccess, {Oracle.ManagedDataAccess.Core, 3.21.80}", "Pipelines.Sockets.Unofficial, {Pipelines.Sockets.Unofficial, 2.2.2}", "Pomelo.EntityFrameworkCore.MySql, {Pomelo.EntityFrameworkCore.MySql, 6.0.2}", "protobuf-net, {protobuf-net, 3.1.0}", "protobuf-net.Core, {protobuf-net.Core, 3.1.22}", "RestSharp, {RestSharp, 108.0.2}", "<PERSON><PERSON><PERSON>, {<PERSON><PERSON><PERSON>, 3.3.0}", "<PERSON><PERSON><PERSON>, {Serilog, 2.11.0}", "Serilog.AspNetCore, {Serilog.AspNetCore, 6.0.1}", "Serilog.Extensions.Hosting, {Serilog.Extensions.Hosting, 5.0.1}", "Serilog.Extensions.Logging, {Serilog.Extensions.Logging, 3.1.0}", "Serilog.Formatting.Compact, {Serilog.Formatting.Compact, 1.1.0}", "Serilog.Formatting.Elasticsearch, {Serilog.Formatting.Elasticsearch, 8.4.1}", "Serilog.Settings.Configuration, {Serilog.Settings.Configuration, 3.3.0}", "Serilog.Sinks.Async, {Serilog.Sinks.Async, 1.5.0}", "Serilog.<PERSON><PERSON>.<PERSON>, {Serilog.Sinks.Console, 4.1.0}", "Serilog.Sinks.Debug, {Serilog.Sinks.Debug, 2.0.0}", "Serilog.Sinks.Elasticsearch, {Serilog.Sinks.Elasticsearch, 8.4.1}", "Serilog.Sinks.FastConsole, {Serilog.Sinks.FastConsole, 2.2.0}", "Serilog.Sinks.File, {Serilog.Sinks.File, 5.0.0}", "Serilog.Sinks.PeriodicBatching, {Serilog.Sinks.PeriodicBatching, 2.1.1}", "Serilog.Sinks.SpectreConsole, {Serilog.Sinks.SpectreConsole, 0.3.3}", "Snowflake.Core, {Snowflake.Core, 2.0.0}", "<PERSON><PERSON><PERSON><PERSON>, {<PERSON><PERSON><PERSON><PERSON>, 0.45.0}", "StackExchange.Redis, {StackExchange.Redis, 2.5.43}", "Swashbuckle.AspNetCore.Filters, {Swashbuckle.AspNetCore.Filters, 7.0.5}", "Swashbuckle.AspNetCore.Filters.Abstractions, {Swashbuckle.AspNetCore.Filters.Abstractions, 7.0.5}", "Swashbuckle.AspNetCore.Swagger, {Swashbuckle.AspNetCore.Swagger, 6.2.3}", "Swashbuckle.AspNetCore.SwaggerGen, {Swashbuckle.AspNetCore.SwaggerGen, 6.2.3}", "Swashbuckle.AspNetCore.SwaggerUI, {Swashbuckle.AspNetCore.SwaggerUI, 6.2.3}", "System.Collections.Concurrent, {System.Collections.Concurrent, 4.3.0}", "System.Collections.Immutable, {System.Collections.Immutable, 6.0.0}", "System.Configuration.ConfigurationManager, {System.Configuration.ConfigurationManager, 6.0.0}", "System.Diagnostics.DiagnosticSource, {System.Diagnostics.DiagnosticSource, 6.0.0}", "System.Diagnostics.EventLog, {System.Diagnostics.EventLog, 6.0.0}", "System.Diagnostics.PerformanceCounter, {System.Diagnostics.PerformanceCounter, 6.0.0}", "System.DirectoryServices, {System.DirectoryServices, 5.0.0}", "System.DirectoryServices.Protocols, {System.DirectoryServices.Protocols, 5.0.1}", "System.Drawing.Common, {System.Drawing.Common, 6.0.0}", "System.IdentityModel.Tokens.Jwt, {System.IdentityModel.Tokens.Jwt, 6.10.0}", "System.IO, {System.IO, 4.3.0}", "System.IO.FileSystem.AccessControl, {System.IO.FileSystem.AccessControl, 5.0.0}", "System.IO.Pipelines, {System.IO.Pipelines, 5.0.1}", "System.Linq.<PERSON>, {System.Linq.Async, 5.1.0}", "System.Net.Http, {System.Net.Http, 4.3.4}", "System.Net.Primitives, {System.Net.Primitives, 4.3.0}", "System.Reflection.DispatchProxy, {System.Reflection.DispatchProxy, 4.7.1}", "System.Runtime, {System.Runtime, 4.3.1}", "System.Runtime.CompilerServices.Unsafe, {System.Runtime.CompilerServices.Unsafe, 6.0.0}", "System.Runtime.Handles, {System.Runtime.Handles, 4.3.0}", "System.Security.AccessControl, {System.Security.AccessControl, 6.0.0}", "System.Security.Cryptography.Algorithms, {System.Security.Cryptography.Algorithms, 4.3.0}", "System.Security.Cryptography.Cng, {System.Security.Cryptography.Cng, 5.0.0}", "System.Security.Cryptography.Encoding, {System.Security.Cryptography.Encoding, 4.3.0}", "System.Security.Cryptography.Primitives, {System.Security.Cryptography.Primitives, 4.3.0}", "System.Security.Cryptography.ProtectedData, {System.Security.Cryptography.ProtectedData, 6.0.0}", "System.Security.Cryptography.X509Certificates, {System.Security.Cryptography.X509Certificates, 4.3.0}", "System.Security.Cryptography.Xml, {System.Security.Cryptography.Xml, 5.0.0}", "System.Security.Permissions, {System.Security.Permissions, 6.0.0}", "System.Security.Principal.Windows, {System.Security.Principal.Windows, 5.0.0}", "System.ServiceModel, {System.ServiceModel.Primitives, 4.10.0}", "System.ServiceModel.Http, {System.ServiceModel.Http, 4.10.0}", "System.ServiceModel.Primitives, {System.ServiceModel.Primitives, 4.10.0}", "System.Text.Encoding, {System.Text.Encoding, 4.3.0}", "System.Text.Encodings.Web, {System.Text.Encodings.Web, 6.0.0}", "System.<PERSON>.<PERSON>, {System.Text.<PERSON>, 6.0.6}", "System.Threading.Channels, {System.Threading.Channels, 6.0.0}", "System.Threading.Tasks, {System.Threading.Tasks, 4.3.0}", "System.Threading.Timer, {System.Threading.Timer, 4.0.1}", "System.Windows.Extensions, {System.Windows.Extensions, 6.0.0}", "Unchase.Swashbuckle.AspNetCore.Extensions, {Unchase.Swashbuckle.AspNetCore.Extensions, 2.6.12}"], "targetFramework": "net6.0", "typeReuseMode": "All"}}