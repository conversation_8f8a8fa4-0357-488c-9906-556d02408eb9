﻿using System;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using SqlSugar;
using H.Utility.SqlSugarInfra;

/*
 * <AUTHOR> XingHe
 * @date : 2023-7-24
 * @desc : 评估计划表
 */
namespace XH.H98.Models.Entities.Pms
{
    /// <summary>
    /// 评估计划表
    /// </summary>
    [SugarTable("PMS_ASSESS_PLAN")]
    [DBOwner("XH_OA")]
    public class PMS_ASSESS_PLAN
    {
        /// <summary>
        /// 评估计划ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        //[Column("PLAN_ID")]
        [Required(ErrorMessage = "评估计划ID不允许为空")]
        [StringLength(50, ErrorMessage = "评估计划ID长度不能超出50字符")]
        //[Unicode(false)]
        public string PLAN_ID { get; set; }

        /// <summary>
        /// 检验专业组ID
        /// </summary>
        //[Column("PGROUP_ID")]
        [Required(ErrorMessage = "检验专业组ID不允许为空")]
        [StringLength(20, ErrorMessage = "检验专业组ID长度不能超出20字符")]
        //[Unicode(false)]
        public string PGROUP_ID { get; set; }

        /// <summary>
        /// 方案版本ID
        /// </summary>
        //[Column("SCHEME_VER_ID")]
        [Required(ErrorMessage = "方案版本ID不允许为空")]
        [StringLength(50, ErrorMessage = "方案版本ID长度不能超出50字符")]
        //[Unicode(false)]
        public string SCHEME_VER_ID { get; set; }

        /// <summary>
        /// 计划名称
        /// </summary>
        //[Column("PLAN_NAME")]
        [StringLength(100, ErrorMessage = "计划名称长度不能超出100字符")]
        //[Unicode(false)]
        public string? PLAN_NAME { get; set; }

        /// <summary>
        /// 评估周期开始时间
        /// </summary>
        //[Column("ASSESS_START_TIME")]

        //[Unicode(false)]
        public DateTime? ASSESS_START_TIME { get; set; }

        /// <summary>
        /// 评估周期结束时间
        /// </summary>
        //[Column("ASSESS_END_TIME")]

        //[Unicode(false)]
        public DateTime? ASSESS_END_TIME { get; set; }

        /// <summary>
        /// 自评开始时间
        /// </summary>
        //[Column("SEVALUATION_START_TIME")]

        //[Unicode(false)]
        public DateTime? SEVALUATION_START_TIME { get; set; }

        /// <summary>
        /// 自评结束时间
        /// </summary>
        //[Column("SEVALUATION_END_TIME")]

        //[Unicode(false)]
        public DateTime? SEVALUATION_END_TIME { get; set; }

        /// <summary>
        /// 考评开始时间
        /// </summary>
        //[Column("EVALUATION_START_TIME")]

        //[Unicode(false)]
        public DateTime? EVALUATION_START_TIME { get; set; }

        /// <summary>
        /// 考评结束时间
        /// </summary>
        //[Column("EVALUATION_END_TIME")]

        //[Unicode(false)]
        public DateTime? EVALUATION_END_TIME { get; set; }

        /// <summary>
        /// 考试开始时间
        /// </summary>
        //[Column("EXAM_START_TIME")]

        //[Unicode(false)]
        public DateTime? EXAM_START_TIME { get; set; }

        /// <summary>
        /// 考试结束时间
        /// </summary>
        //[Column("EXAM_END_TIME")]

        //[Unicode(false)]
        public DateTime? EXAM_END_TIME { get; set; }

        /// <summary>
        /// 提醒时间
        /// </summary>
        //[Column("WARN_TIMELIMIT")]

        //[Unicode(false)]
        public int? WARN_TIMELIMIT { get; set; }

        /// <summary>
        /// 提醒时间单位
        /// </summary>
        //[Column("WARN_TIMELIMIT_UNIT")]
        [StringLength(4, ErrorMessage = "提醒时间单位长度不能超出4字符")]
        //[Unicode(false)]
        public string? WARN_TIMELIMIT_UNIT { get; set; }

        /// <summary>
        /// 总分值(分)
        /// </summary>
        //[Column("TOTAL_SCORE")]

        //[Unicode(false)]
        public decimal? TOTAL_SCORE { get; set; }

        /// <summary>
        /// 考试总分(分)
        /// </summary>
        //[Column("EXAM_TOTAL_SCORE")]

        //[Unicode(false)]
        public decimal? EXAM_TOTAL_SCORE { get; set; }

        /// <summary>
        /// 考试合格(分)
        /// </summary>
        //[Column("EXAM_PASS_SCORE")]

        //[Unicode(false)]
        public decimal? EXAM_PASS_SCORE { get; set; }

        /// <summary>
        /// 考评总分(分)
        /// </summary>
        //[Column("EVALUATION_TOTAL_SCORE")]

        //[Unicode(false)]
        public decimal? EVALUATION_TOTAL_SCORE { get; set; }

        /// <summary>
        /// 考评合格(分)
        /// </summary>
        //[Column("EVALUATION_PASS_SCORE")]

        //[Unicode(false)]
        public decimal? EVALUATION_PASS_SCORE { get; set; }

        /// <summary>
        /// 自评权重
        /// </summary>
        //[Column("SEVALUATION_WEIGHT")]

        //[Unicode(false)]
        public decimal? SEVALUATION_WEIGHT { get; set; }

        /// <summary>
        /// 考评权重
        /// </summary>
        //[Column("EVALUATION_WEIGHT")]

        //[Unicode(false)]
        public decimal? EVALUATION_WEIGHT { get; set; }

        /// <summary>
        /// 评价规则
        /// </summary>
        //[Column("EVALUATION_RULE")]
        [StringLength(500, ErrorMessage = "评价规则长度不能超出500字符")]
        //[Unicode(false)]
        public string? EVALUATION_RULE { get; set; }

        /// <summary>
        /// 状态；0未提交 1待审核 2已审核未通过 3待发布 4已发布 5废止 6结束
        /// </summary>
        //[Column("PLAN_STATE")]
        [StringLength(10, ErrorMessage = "状态长度不能超出10字符")]
        //[Unicode(false)]
        public string? PLAN_STATE { get; set; }

        /// <summary>
        /// 首次登记人
        /// </summary>
        //[Column("FIRST_RPERSON")]
        [StringLength(50, ErrorMessage = "首次登记人长度不能超出50字符")]
        //[Unicode(false)]
        public string? FIRST_RPERSON { get; set; }

        /// <summary>
        /// 首次登记时间
        /// </summary>
        //[Column("FIRST_RTIME")]

        //[Unicode(false)]
        public DateTime? FIRST_RTIME { get; set; }

        /// <summary>
        /// 最后修改人员
        /// </summary>
        //[Column("LAST_MPERSON")]
        [StringLength(50, ErrorMessage = "最后修改人员长度不能超出50字符")]
        //[Unicode(false)]
        public string? LAST_MPERSON { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        //[Column("LAST_MTIME")]

        //[Unicode(false)]
        public DateTime? LAST_MTIME { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        //[Column("REMARK")]
        [StringLength(200, ErrorMessage = "备注长度不能超出200字符")]
        //[Unicode(false)]
        public string? REMARK { get; set; }

    }
}