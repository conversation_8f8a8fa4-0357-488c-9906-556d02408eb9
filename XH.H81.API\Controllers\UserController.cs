﻿using H.BASE;
using H.Utility;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NPOI.POIFS.Crypt.Dsig;
using NPOI.SS.Formula.Functions;
using System.Security.Claims;
using XH.H81.IServices;
using XH.H81.IServices.Pms;
using XH.H81.Models.Dtos;
using XH.H81.Models.Dtos.EvaluatePlan;
using XH.H81.Models.Dtos.Pms;
using XH.H81.Models.Dtos.Tag;
using XH.H81.Models.Entities.EvaluatePlan;
using XH.H81.Services;
using XH.LAB.UTILS.Helpers;
using XH.LAB.UTILS.Models;
using XH.LAB.UTILS.Models.Dto;

namespace XH.H81.API.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [ApiExplorerSettings(GroupName = "人员账号相关")]
    [Authorize]
    public class UserController : ControllerBase
    {
        private IUserService _IUserService;
        private readonly IBaseDataServices _IBaseDataService;
        private readonly IEvaluatePlanService _IEvaluatePlanService;
        private readonly IHttpContextAccessor _httpContext;
        public UserController(IUserService iUserService, IBaseDataServices ibasedataService, IEvaluatePlanService iEvaluatePlanService, IHttpContextAccessor ihttpContext)
        {
            _IUserService = iUserService;
            _IBaseDataService = ibasedataService;
            _IEvaluatePlanService = iEvaluatePlanService;
            _httpContext = ihttpContext;
        }


        /// <summary>
        /// 获取人员结构信息
        /// </summary>
        /// <param name="hospitalId"></param>
        /// <param name="labId"></param>
        /// <param name="areaId"></param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetPersonStructureData(string hospitalId, string? labId, string? areaId, string? smblLabId)
        {
            var claim = this.User.ToClaimsDto();
            var Result = _IUserService.GetPersonStructureInfo(hospitalId, labId, areaId, smblLabId, claim.USER_NO);
            return Ok(Result);
        }

        /// <summary>
        /// 获取人员结构信息
        /// </summary>
        /// <param name="hospitalId"></param>
        /// <param name="labId"></param>
        /// <param name="areaId"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult GetPersonStructureInfoByArea(string hospitalId, string labId, string? areaId)
        {
            var claim = this.User.ToClaimsDto();
            var Result = _IUserService.GetPersonStructureInfo_old(hospitalId, labId, areaId, claim.USER_NO);
            return Ok(Result);
        }

        /// <summary>
        /// 获取生安人员一览信息
        /// </summary>
        /// <param name="hospital_id"></param>
        /// <param name="lab_id"></param>
        /// <param name="area_id"></param>
        /// <param name="smblLabId"></param>
        /// <param name="user_id"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult GetPersonStructureInfo_SMBL(string hospital_id, string? lab_id, string? area_id, string? smblLabId, string user_id)
        {
            var claim = this.User.ToClaimsDto();
            var Result = _IUserService.GetPersonStructureInfo_SMBL(hospital_id, lab_id, area_id, smblLabId, user_id);
            return Ok(Result);
        }


        /// <summary>
        /// 根据人员ID获取人员信息
        /// </summary>
        /// <param name="obj">{"USER_ID":"","LOAD_MODE":"主程序调用1，杏通调用2"}</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult GetPersonInfoById(PersonInfoParm obj)
        {
            var claim = this.User.ToClaimsDto();
            string person_id = obj.USER_ID;
            PersonInfoDto Result = _IUserService.GetPersonInfoById(person_id, claim.USER_NO, claim.HOSPITAL_ID, obj.LOAD_MODE);
            return Ok(Result.ToResultDto());
        }

        /// <summary>
        /// 获取人员下拉信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult GetPersonDropDownInfo()
        {
            var claim = this.User.ToClaimsDto();
            var Result = _IUserService.GetPersonDropDownInfo(claim.HOSPITAL_ID);
            return Ok(Result.ToResultDto());
        }
        /// <summary>
        /// 生成人员文件信息
        /// </summary>
        /// <param name="obj">{"PERSON_ID":""}</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult GeneratePersonFileInfo(dynamic obj)
        {
            dynamic json = JsonConvert.DeserializeObject(Convert.ToString(obj));
            var claim = this.User.ToClaimsDto();
            string person_id = json["PERSON_ID"].ToString();
            var result = _IUserService.GeneratePersonFileInfo(person_id, claim.HOSPITAL_ID);
            return Ok(result);
        }
        /// <summary>
        /// 生成当前人员文件信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GenerateCurrentPersonFileInfo(string PERSON_ID)
        {
            var claim = this.User.ToClaimsDto();
            var result = _IUserService.GeneratePersonFileInfo(PERSON_ID, claim.HOSPITAL_ID);
            return Ok(result);
        }

        /// <summary>
        /// 根据医疗机构ID获取所有人员信息
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult GetAllPersonInfoByHospitalId()
        {
            var claim = this.User.ToClaimsDto();
            var result = _IUserService.GetAllPersonInfoByHospitalId(claim.HOSPITAL_ID);
            return Ok(result.ToResultDto());
        }

        /// <summary>
        /// 获取人员信息树  已弃用
        /// </summary>
        /// <param name="obj">{"LAB_ID":"","PGROUP_ID":"","PERSON_NAME":"","USER_NO":""}</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult GetPersonInfoTree(dynamic obj)
        {
            var claim = this.User.ToClaimsDto();
            dynamic json = JsonConvert.DeserializeObject(Convert.ToString(obj));
            string hospital_id = claim.HOSPITAL_ID;
            string lab_id = json["LAB_ID"].ToString();
            string pgroup_id = json["PGROUP_ID"].ToString();
            string person_name = json["PERSON_NAME"].ToString();
            var data = _IUserService.GetPersonInfoTree(hospital_id, lab_id, pgroup_id, person_name, claim.USER_NO);
            return Ok(data.ToResultDto());
        }

        /// <summary>
        /// 获取人员信息树
        /// </summary>
        /// <param name="obj">{"AREA_ID":"","LAB_ID":"","PGROUP_ID":"","PERSON_NAME":"","USER_NO":"","PAGE_TYPE":"1"}(PAGE_TYPE:1-人事档案页面;  2-记录审核页面)</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult GetGroupPersonTree(PersonTreeParm parm)      
        {
            var claim = this.User.ToClaimsDto();
            string hospital_id = parm.HOSPITAL_ID ?? claim.HOSPITAL_ID;
            var orgParm = new OrgUserParams { area_id = parm.AREA_ID, hospital_id = hospital_id, lab_id = parm.LAB_ID, smbl_lab_id = parm.SMBL_LAB_ID, pgroup_id = parm.PGROUP_ID, user_id = claim.USER_NO };
            bool isIncludeAll = parm.PAGE_TYPE == "1";
            //临时代码：POCT及生安入口，后面连PersonTreeParm类定义 treeType （表示入口类型）也应删除
            if (parm?.treeType == "2" || parm?.treeType == "4")
            {
                var tempTree = _IUserService.GetISOPersonTree_Lab(orgParm, parm.PERSON_NAME, GetPermission(parm.PAGE_TYPE));
                string tagClass = parm.treeType == "2" ? "XBD00001202" : "XBD00001204"; //POCT及生安
                tempTree.AllNodesRemove(a => a.NODE_TYPE == GroupTreeNodeTypeEnum.USER.ToIntStr() && !a.TAGS.Select(t => (t as PmsPersonTagDictDto).TAG_CLASS).Contains(tagClass) && a.NODE_TYPE != GroupTreeNodeTypeEnum.LAB.ToIntStr());
                tempTree.RefreshTree(a => a.NODE_TYPE == GroupTreeNodeTypeEnum.USER.ToIntStr());
                return Ok(tempTree.ToResultDto());                ;
            }
                switch (parm.TREE_TYPE)
            {
                case "SMBL_1":
                    var data1 = _IUserService.GetSMBLPersonTree_Lab(orgParm, parm.PERSON_NAME, "H81");
                    return Ok(data1.ToResultDto());
                case "SMBL_2":
                    var data2 = _IUserService.GetSMBLPersonTree_Post(orgParm, parm.PERSON_NAME, GetPermission(parm.PAGE_TYPE), parm.POST_ID);
                    return Ok(data2.ToResultDto());
                case "ISO_LAB":  
                    var data3 = _IUserService.GetISOPersonTree_Lab(orgParm, parm.PERSON_NAME, GetPermission(parm.PAGE_TYPE));
                    return Ok(data3.ToResultDto());
                case "ISO_AREA":
                    var data4 = _IUserService.GetISOPersonTree_Area(orgParm, parm.PERSON_NAME, GetPermission(parm.PAGE_TYPE));
                    return Ok(data4.ToResultDto());
                case "ISO_OTHER":
                    var data5 = _IUserService.GetISOPersonTree_Other(orgParm, parm.PERSON_NAME, GetPermission(parm.PAGE_TYPE), parm.PERSON_TYPE);
                    return Ok(data5.ToResultDto());
                case "ISO_LABOTHER":
                    var data6 = _IUserService.GetISOPersonTree_LabOther(orgParm, parm.PERSON_NAME, "H81");
                    return Ok(data6.ToResultDto());
                default:
                    if (_httpContext.GetSmblFlag() == "1")
                    {
                        var data0 = _IUserService.GetSMBLPersonTree_Lab(orgParm, parm.PERSON_NAME, "H81");
                        return Ok(data0.ToResultDto());
                    }
                    else
                    {
                        var data10 = _IUserService.GetISOLabPersonTree(orgParm, null, "H81", null, false);
                        return Ok(data10.ToResultDto());
                    }
            }
        }

        /// <summary>
        /// 获取人员信息树
        /// </summary>
        /// <param name="obj">{"AREA_ID":"","LAB_ID":"","PGROUP_ID":"","PERSON_NAME":"","USER_NO":"","PAGE_TYPE":"1"}(PAGE_TYPE:1-人事档案页面;  2-记录审核页面)</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult GetHistoryPersonTree(HistoryPersonTreeParm parm)
        {
            var claim = this.User.ToClaimsDto();
            switch (parm.TREE_TYPE)
            {
                case "SMBL_1":
                    var data1 = _IUserService.GetHistoryPersonTree_SMBL_1(parm);
                    return Ok(data1.ToResultDto());
                //case "SMBL_2":
                //    var data2 = _IUserService.GetSMBLPersonTree_Post(orgParm, parm.PERSON_NAME, GetPermission(parm.PAGE_TYPE), parm.POST_ID);
                //    return Ok(data2.ToResultDto());
                //case "ISO_LAB":
                //    var data3 = _IUserService.GetISOPersonTree_Lab(orgParm, parm.PERSON_NAME, GetPermission(parm.PAGE_TYPE));
                //    return Ok(data3.ToResultDto());
                //case "ISO_AREA":
                //    var data4 = _IUserService.GetISOPersonTree_Area(orgParm, parm.PERSON_NAME, GetPermission(parm.PAGE_TYPE));
                //    return Ok(data4.ToResultDto());
                //case "ISO_OTHER":
                //    var data5 = _IUserService.GetISOPersonTree_Other(orgParm, parm.PERSON_NAME, GetPermission(parm.PAGE_TYPE), parm.PERSON_TYPE);
                //    return Ok(data5.ToResultDto());
                //case "ISO_LABOTHER":
                //    var data6 = _IUserService.GetISOPersonTree_LabOther(orgParm, parm.PERSON_NAME, "H81");
                //    return Ok(data6.ToResultDto());
                default:
                    var data0 = _IUserService.GetHistoryPersonTree_SMBL_1(parm);
                    return Ok(data0.ToResultDto());
            }
        }

        /// <summary>
        /// 获取院区-检验专业组下拉列表
        /// </summary>
        ///  <param name="obj">{"AREA_ID":"","LAB_ID":"","USER_NO":"","PAGE_TYPE":"1"}(PAGE_TYPE:1-人事档案页面;  2-记录审核页面)</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult GetAreaGroupDropDownList(dynamic obj)
        {
            var claim = this.User.ToClaimsDto();
            JObject json = JsonConvert.DeserializeObject(Convert.ToString(obj));
            string area_id = json.ContainsKey("AREA_ID") ? json["AREA_ID"].ToString() : "";
            string lab_id = json.ContainsKey("LAB_ID") ? json["LAB_ID"].ToString() : "";
            string page_type = json.ContainsKey("PAGE_TYPE") ? json["PAGE_TYPE"].ToString() : "";
            string hospital_id = json.ContainsKey("HOSPITAL_ID") ? json["HOSPITAL_ID"].ToString() : "";
            if (hospital_id.IsNullOrEmpty())
                hospital_id = claim.HOSPITAL_ID;
            var orgParm = new OrgUserParams { hospital_id = hospital_id, lab_id = lab_id, area_id = area_id, user_id = claim.USER_NO };
            var data = _IUserService.GetAreaGroupDropDownList(orgParm, GetPermission(page_type));
            return Ok(data.ToResultDto());
        }
        /// <summary>
        /// 获取其他类型下拉
        /// </summary>
        /// <param name="labId"></param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetOtherTypeDropDwon(string labId)
        {
            var claim = this.User.ToClaimsDto();
            var data = _IUserService.GetOtherTypeDropDwon(labId, claim.HOSPITAL_ID);
            return Ok(data.ToResultDto());
        }

        /// <summary>
        /// 根据专业组ID获取人员列表
        /// </summary>
        /// <param name="pgroupId">专业组ID</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetPersonList(string pgroupId)
        {
            var claim = this.User.ToClaimsDto();
            var data = _IUserService.GetPersonList(pgroupId, claim.HOSPITAL_ID);
            return Ok(data.ToResultDto());
        }

        /// <summary>
        /// 加载人事档案excel列表
        /// </summary>
        /// <param name="loadOfficeDto">dto</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult LoadPersonExcelData(LoadOfficeDto loadOfficeDto)
        {
            var claim = this.User.ToClaimsDto();
            var data = _IUserService.LoadPersonExcelData(loadOfficeDto, claim.HOSPITAL_ID);
            return Ok(data);
        }
        /// <summary>
        /// 预览人人员模板
        /// </summary>
        /// <param name="styleId">模板id</param>
        /// <param name="personId">人员id</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult PreviewPersonTemplate(string styleId, string personId)
        {
            var claim = this.User.ToClaimsDto();
            var data = _IUserService.PreviewPersonTemplate(styleId, personId, claim.HOSPITAL_ID);
            if (data != null)
            {
                return File(data, "application/pdf", $"{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}.pdf");
            }
            return Ok();
        }

        /// <summary>
        /// 导出人人员模板
        /// </summary>
        /// <param name="styleId">模板id</param>
        /// <param name="personId">人员id</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult ExportPersonTemplate(string styleId, string personId)
        {
            var claim = User.ToClaimsDto();
            var res = _IUserService.ExportPersonTemplate(styleId, personId, claim.HOSPITAL_ID);
            if (res != null)
            {
                return File(res, "application/msword", $"{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}.docx");
            }
            throw new Exception("导出评价表模板失败.");
        }
        /// <summary>
        /// 获取人员分类附件信息
        /// </summary>
        /// <param name="archiveTables">分类id合集多个,隔开</param>
        /// <param name="personId">人员id</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetRecordFileByPersonId(string archiveTables, string personId)
        {
            var claim = this.User.ToClaimsDto();
            var data = _IUserService.GetRecordFileByPersonId(archiveTables, personId);
            return Ok(data.ToResultDto());
        }

        private string GetPermission(string page_type)
        {
            //人事系统权限菜单ID
            const string MODULE_MENU_ID = "H81";
            //人事档案页面权限（编辑权限）
            const string PERSON_INFO_EDIT_MENU_ID = "H8107";
            //记录审核页面权限（人事档案审核权限）
            const string PERSON_INFO_AUDIT_MENU_ID = "H8109";
            string permissMenuId = "";
            switch (page_type)
            {
                case "1":
                    permissMenuId = PERSON_INFO_EDIT_MENU_ID;
                    break;
                case "2":
                    permissMenuId = PERSON_INFO_AUDIT_MENU_ID;
                    break;
                default:
                    permissMenuId = MODULE_MENU_ID;
                    break;
            }
            return permissMenuId;
        }

        /// <summary>
        /// 获取院区列表
        /// </summary>
        /// <param name="labId"></param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetAreaList(string? hospitalId, string? labId)
        {
            var data = _IUserService.GetAreaList(hospitalId, labId);
            return Ok(data.ToResultDto());
        }



        /// <summary>
        /// 获取备案实验室下拉列表
        /// </summary>
        /// <param name="obj">{"AREA_ID":"","LAB_ID":"","PGROUP_ID":"","PERSON_NAME":"","USER_NO":"","PAGE_TYPE":"1"}(PAGE_TYPE:1-人事档案页面;  2-记录审核页面)</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult GetSmblLabDropDown(PersonTreeParm parm)
        {
            var claim = this.User.ToClaimsDto();
            string hospital_id = parm.HOSPITAL_ID ?? claim.HOSPITAL_ID;
            var orgParm = new OrgUserParams { area_id = parm.AREA_ID, hospital_id = hospital_id, lab_id = parm.LAB_ID, smbl_lab_id = parm.SMBL_LAB_ID, pgroup_id = parm.PGROUP_ID, user_id = claim.USER_NO };
            var data = _IUserService.GetSmblLabTree(orgParm,  GetPermission(parm.PAGE_TYPE));
            return Ok(data.ToResultDto());
        }

        /// <summary>
        /// 获取生安岗位下拉列表
        /// </summary>
        /// <param name="obj">{"AREA_ID":"","LAB_ID":"","PGROUP_ID":"","PERSON_NAME":"","USER_NO":"","PAGE_TYPE":"1"}(PAGE_TYPE:1-人事档案页面;  2-记录审核页面)</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult GetSmblPostDropDown(PersonTreeParm parm)
        {
            var claim = this.User.ToClaimsDto();
            string hospital_id = parm.HOSPITAL_ID ?? claim.HOSPITAL_ID;
            var orgParm = new OrgUserParams { area_id = parm.AREA_ID, hospital_id = hospital_id, lab_id = parm.LAB_ID, smbl_lab_id = parm.SMBL_LAB_ID, pgroup_id = parm.PGROUP_ID, user_id = claim.USER_NO };
            var data = _IUserService.GetSmblPostTree(orgParm, GetPermission(parm.PAGE_TYPE));
            return Ok(data.ToResultDto());
        }
        /// <summary>
        /// 获取生安部门节点树
        /// </summary>
        /// <param name="hospitalId"></param>
        /// <param name="labId"></param>
        /// <param name="smblLabId">备案实验室ID</param>
        /// <param name="smblDeptId">生安部门节点ID</param>
        /// <param name="deptKeyWord">生安部门节点关键字</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetSmblDeptTree(string hospitalId, string? labId, string? smblLabId, string? smblDeptId, string? deptKeyWord)
        {
            var claim = this.User.ToClaimsDto();
            var orgParm = new OrgUserParams { hospital_id = hospitalId, lab_id = labId, smbl_lab_id = smblLabId, user_id = claim.USER_NO };
            var data = _IUserService.GetSmblDeptTree(orgParm, "H81", smblDeptId, deptKeyWord);
            return Ok(data.ToResultDto());
        }
        /// <summary>
        /// 获取生安岗位人员树
        /// </summary>
        /// <param name="hospitalId"></param>
        /// <param name="smblDeptId">生安部门节点ID</param>
        /// <param name="smblPostId">生安岗位ID</param>
        /// <param name="postKeyword">生安岗位关键字</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetSmblDeptPostPersonTree(string hospitalId, string? smblDeptId, string? smblPostId, string? postKeyword, string? personKeyword)
        {
            var data = _IUserService.GetSmblDeptPostPersonTree(hospitalId, smblDeptId, smblPostId, postKeyword, personKeyword);
            return Ok(data.ToResultDto());
        }

        /// <summary>
        /// 获取门禁人员树
        /// </summary>
        /// <param name="org"></param>
        /// <param name="personName"></param>
        /// <param name="postId"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult GetEguardPersonTree(OrgUserParams org,string? personName,string? postId)
        {
            postId = postId ?? AppSettingsProvider.CurrModuleId;
            var data = _IUserService.GetISOPersonTree_LabOther(org,personName,postId);
            return Ok(data.ToResultDto());
        }
    }
}
