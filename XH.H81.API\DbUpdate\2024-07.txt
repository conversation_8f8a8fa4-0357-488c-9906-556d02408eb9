﻿--20240722 增加档案分类配置字段长度
ALTER TABLE XH_OA.PMS_ADDN_CLASS_INFO  MODIFY ( CLASS_ADDN_CONFIG VARCHAR2(4000));

--20240722 增加ID字段长度
ALTER TABLE XH_OA.OA_BASE_DATA  MODIFY ( DATA_ID VARCHAR2(50));
ALTER TABLE XH_OA.OA_BASE_DATA  MODIFY ( HOSPITAL_ID VARCHAR2(50));
ALTER TABLE XH_OA.OA_BASE_DATA  MODIFY ( MODULE_ID VARCHAR2(50));
<<<<<<< HEAD
ALTER TABLE XH_OA.OA_BASE_DATA  MODIFY ( FATHER_ID VARCHAR2(50));
=======
ALTER TABLE XH_OA.OA_BASE_DATA  MODIFY ( FATHER_ID VARCHAR2(50));

--2024/7/25  PMS_PERSON_INFO新增毕业院校字段
ALTER TABLE XH_OA.PMS_PERSON_INFO ADD GRADUATE_SCHOOL VARCHAR2(100);
COMMENT ON COLUMN XH_OA.PMS_PERSON_INFO.GRADUATE_SCHOOL IS '毕业院校';

--2024/7/25  PMS_PERSON_INFO新增毕业日期字段
ALTER TABLE XH_OA.PMS_PERSON_INFO ADD GRADUATE_DATE DATE;
COMMENT ON COLUMN XH_OA.PMS_PERSON_INFO.GRADUATE_DATE IS '毕业日期';

--2024/7/25  PMS_PERSON_INFO新增颜色视觉障碍字段
ALTER TABLE XH_OA.PMS_PERSON_INFO ADD COLOR_DEFICIENCY VARCHAR2(10);
COMMENT ON COLUMN XH_OA.PMS_PERSON_INFO.COLOR_DEFICIENCY IS '颜色视觉障碍（0-正常  1-色弱 2-色盲）';

--2024/7/25  PMS_ADDN_CLASS_INFO新增IS_HIDE是否隐藏字段
ALTER TABLE XH_OA. PMS_ADDN_CLASS_INFO ADD IS_HIDE NUMBER(1,0) DEFAULT 0 ;
COMMENT ON COLUMN XH_OA.PMS_ADDN_CLASS_INFO.IS_HIDE IS '是否隐藏：0-否 1-是； 默认否';

--2024/7/25  PMS_ADDN_CLASS_INFO新增IS_PROP_EDITABLE是否可编辑属性字段
ALTER TABLE XH_OA. PMS_ADDN_CLASS_INFO ADD IS_PROP_EDITABLE NUMBER(1,0) DEFAULT 1  ;
COMMENT ON COLUMN XH_OA.PMS_ADDN_CLASS_INFO.IS_PROP_EDITABLE IS '是否可编辑属性：0-否 1-是； 默认是。表示分类管理维护界面能否对它进行字段维护';

--2024/7/25  PMS_ADDN_CLASS_INFO新增IS_AUDITABLE是否有审核流程字段
ALTER TABLE XH_OA. PMS_ADDN_CLASS_INFO ADD IS_AUDITABLE NUMBER(1,0) DEFAULT 1  ;
COMMENT ON COLUMN XH_OA.PMS_ADDN_CLASS_INFO.IS_AUDITABLE IS '是否有审核流程：0-否 1-是； 默认否。表示审核页面是否可见该分类';
>>>>>>> 4cf24ed6d25b4dc1fd1049e4d3069b40553cabe3
