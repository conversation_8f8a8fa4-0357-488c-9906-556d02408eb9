//using System;
//using Microsoft.EntityFrameworkCore;
//using System.ComponentModel.DataAnnotations;
//using System.ComponentModel.DataAnnotations.Schema;
//using SqlSugar;
//using H.Utility.SqlSugarInfra;

//namespace XH.H81.Models.ExternalEntity
//{
//    [Table("LIS6_INCEPT_UNIT_INFO")]
//    [DBOwner("XH_SYS")]
//    public class LIS6_INCEPT_UNIT_INFO
//	{
//		/// <summary>
//		/// 
//		/// </summary>
//		[Key]
//        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
//        //[Column("UNIT_ID")]
//		[Required(ErrorMessage = "不允许为空")]

//		[StringLength(20, ErrorMessage = "UNIT_ID长度不能超出20字符")]
//		//[Unicode(false)]
//        public string UNIT_ID { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("UNIT_STATE")]
//		[StringLength(20, ErrorMessage = "UNIT_STATE长度不能超出20字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? UNIT_STATE { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("FIRST_RPERSON")]
//		[StringLength(50, ErrorMessage = "FIRST_RPERSON长度不能超出50字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? FIRST_RPERSON { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("LAB_ID")]
//		[StringLength(10, ErrorMessage = "LAB_ID长度不能超出10字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? LAB_ID { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("AREA_ID")]
//		[StringLength(20, ErrorMessage = "AREA_ID长度不能超出20字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? AREA_ID { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("UNIT_PACK_STATE")]
//		[StringLength(100, ErrorMessage = "UNIT_PACK_STATE长度不能超出100字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? UNIT_PACK_STATE { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("LAST_MTIME")]
//		//[Unicode(false)]
//		public DateTime? LAST_MTIME { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("UNIT_PERSON")]
//		[StringLength(50, ErrorMessage = "UNIT_PERSON长度不能超出50字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? UNIT_PERSON { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("UNIT_POSITION")]
//		[StringLength(100, ErrorMessage = "UNIT_POSITION长度不能超出100字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? UNIT_POSITION { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("UNIT_SORT")]
//		[StringLength(20, ErrorMessage = "UNIT_SORT长度不能超出20字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? UNIT_SORT { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("UNIT_SHORTCUT")]
//		[StringLength(20, ErrorMessage = "UNIT_SHORTCUT长度不能超出20字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? UNIT_SHORTCUT { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("NUMBING_LIMIT")]
//		[StringLength(50, ErrorMessage = "NUMBING_LIMIT长度不能超出50字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? NUMBING_LIMIT { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("UNIT_INCEPT_PACK")]
//		[StringLength(100, ErrorMessage = "UNIT_INCEPT_PACK长度不能超出100字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? UNIT_INCEPT_PACK { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("CHARGE_DEPT")]
//		[StringLength(20, ErrorMessage = "CHARGE_DEPT长度不能超出20字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? CHARGE_DEPT { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("REMARK")]
//		[StringLength(100, ErrorMessage = "REMARK长度不能超出100字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? REMARK { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("UNIT_TRANSPORT")]
//		[StringLength(20, ErrorMessage = "UNIT_TRANSPORT长度不能超出20字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? UNIT_TRANSPORT { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("UNIT_SENDIN_PACK")]
//		[StringLength(100, ErrorMessage = "UNIT_SENDIN_PACK长度不能超出100字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? UNIT_SENDIN_PACK { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("IF_CHARGE")]
//		[StringLength(20, ErrorMessage = "IF_CHARGE长度不能超出20字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? IF_CHARGE { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("UNIT_NAME")]
//		[StringLength(100, ErrorMessage = "UNIT_NAME长度不能超出100字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? UNIT_NAME { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("UNIT_CODE")]
//		[StringLength(50, ErrorMessage = "UNIT_CODE长度不能超出50字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? UNIT_CODE { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("FIRST_RTIME")]
//		//[Unicode(false)]
//		public DateTime? FIRST_RTIME { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("LAST_MPERSON")]
//		[StringLength(50, ErrorMessage = "LAST_MPERSON长度不能超出50字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? LAST_MPERSON { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("SENDINPACK_STATE")]
//		[StringLength(20, ErrorMessage = "SENDINPACK_STATE长度不能超出20字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? SENDINPACK_STATE { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("IF_AUTONUMBER")]
//		[StringLength(20, ErrorMessage = "IF_AUTONUMBER长度不能超出20字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? IF_AUTONUMBER { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("LAB_LIMIT")]
//		[StringLength(50, ErrorMessage = "LAB_LIMIT长度不能超出50字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? LAB_LIMIT { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("KEY_CODE")]
//		[StringLength(50, ErrorMessage = "KEY_CODE长度不能超出50字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? KEY_CODE { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("INSPECT_LEVEL")]
//		[StringLength(20, ErrorMessage = "INSPECT_LEVEL长度不能超出20字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? INSPECT_LEVEL { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("INCEPT_LEVEL")]
//		[StringLength(20, ErrorMessage = "INCEPT_LEVEL长度不能超出20字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? INCEPT_LEVEL { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("HOSPITAL_ID")]
//		[StringLength(20, ErrorMessage = "HOSPITAL_ID长度不能超出20字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? HOSPITAL_ID { get; set; }

//		/// <summary>
//		/// 
//		/// </summary>
//		//[Column("DISPLAY_TYPE")]
//		[StringLength(10, ErrorMessage = "DISPLAY_TYPE长度不能超出10字符")]
//		//[Unicode(false)]
//        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
//        public string? DISPLAY_TYPE { get; set; }


//	}
//}
