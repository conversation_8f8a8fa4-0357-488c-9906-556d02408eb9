﻿using H.BASE;
using H.Utility;
using H.Utility.Dtos;
using H.Utility.Helper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Infrastructure;
using NetTaste;
using Newtonsoft.Json;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using System.Linq.Dynamic.Core.Tokenizer;
using System.Reflection;
using System.Text;
using XH.H81.IServices;
using XH.H81.Models.Dtos;
using XH.H81.Services;

namespace XH.H85.API.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    public class SystemController : ControllerBase
    {
        private readonly IActionDescriptorCollectionProvider _actionProvider;
        private readonly IConfiguration _configuration;
        private readonly ISystemService _systemService;

        public SystemController(IActionDescriptorCollectionProvider actionProvider, IConfiguration configuration, ISystemService systemService)
        {
            _actionProvider = actionProvider;
            _configuration = configuration;
            _systemService = systemService;
        }
        /// <summary>
        /// 健康检查接口
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IActionResult HealthCheck()
        {
            string version = Assembly.GetExecutingAssembly().GetName().Version.ToString();
            SysInfoDto sys = new SysInfoDto();
            sys.currTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
            sys.version = version;
            return Ok(sys);
        }
        /// <summary>
        /// 导出API列表,需标记ExportInfo描述器
        /// 参考 BaseDataDemoController/ApiExportTest
        /// ApiID根据模块+路径计算哈希值生成,只要路径保持不变,则哈希值保持不变
        /// MenuId需要先约定,多个菜单用逗号隔开
        /// </summary>
        /// <param name="format">EXCEL|JSON</param>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]

        public IActionResult ExportMenuApiList(string format = "excel")
        {
            //所有路由 反射不出路径.只能先搜集所有路径
            var routes = _actionProvider.ActionDescriptors.Items.Select(x => new
            {
                Action = x.RouteValues["Action"],
                Controller = x.RouteValues["Controller"],
                Name = x.AttributeRouteInfo.Name,
                Template = x.AttributeRouteInfo.Template
            }).ToList();
            string moduleId = AppSettingsProvider.CurrModuleId;
            List<ExportApiListDto> list = new List<ExportApiListDto>();

            var types = Assembly.GetExecutingAssembly().GetTypes().Where(type => typeof(ControllerBase).IsAssignableFrom(type));
            List<Type> typeList = new List<Type>();
            foreach (Type type in types)
            {
                string s = type.FullName.ToLower();
                typeList.Add(type);
            }
            typeList.Sort(delegate (Type type1, Type type2) { return type1.FullName.CompareTo(type2.FullName); });
            foreach (Type type in typeList)
            {
                MemberInfo[] members = type.FindMembers(System.Reflection.MemberTypes.Method,
                    BindingFlags.Public |
                    BindingFlags.Static |
                    BindingFlags.NonPublic |        //【位屏蔽】 
                    BindingFlags.Instance |
                   BindingFlags.DeclaredOnly,
                    Type.FilterName, "*");
                string controllerName = type.Name.Replace("Controller", "");


                foreach (var m in members)
                {
                    var attr = m.GetCustomAttributes(true)
                   .FirstOrDefault(x => x.GetType() == typeof(ExportInfo)) as ExportInfo;

                    if (attr == null)
                    {
                        continue;
                    }

                    if (m.DeclaringType.Attributes.HasFlag(System.Reflection.TypeAttributes.Public) != true)
                        continue;

                    string actionName = m.Name;

                    var menuIds = attr.MenuId.Split(',');

                    foreach (string id in menuIds)
                    {
                        if (id.IsNotNullOrEmpty())
                        {
                            ExportApiListDto api = new ExportApiListDto();
                            var r = routes.FirstOrDefault(p => p.Action == actionName && p.Controller == controllerName);
                            api.ModuleId = moduleId;
                            api.ApiUrl = "/" + r.Template;
                            api.ApiName = attr.ApiName;
                            api.MenuId = id;
                            api.ApiId = HashHelper.ComputeHash(moduleId + api.ApiUrl).ToString();
                            list.Add(api);
                        }

                    }
                }

            }

            if (format.ToUpper() == "JSON")
            {
                return Ok(list.ToResultDto());

            }
            else
            {
                using (XSSFWorkbook workbook = new XSSFWorkbook())
                {
                    ISheet sheet = workbook.CreateSheet("Sheet1");//创建一个名称为Sheet0的表;
                    IRow row = sheet.CreateRow(0);//（第一行写标题)
                    row.CreateCell(0).SetCellValue("模块ID");//第一列标题，以此类推
                    row.CreateCell(1).SetCellValue("API ID");
                    row.CreateCell(2).SetCellValue("API NAME");
                    row.CreateCell(3).SetCellValue("路径");
                    row.CreateCell(4).SetCellValue("菜单ID");
                    int count = list.Count;//

                    //每一行依次写入
                    for (int i = 0; i < list.Count; i++)
                    {
                        row = sheet.CreateRow(i + 1);
                        row.CreateCell(0).SetCellValue(list[i].ModuleId);
                        row.CreateCell(1).SetCellValue(list[i].ApiId);
                        row.CreateCell(2).SetCellValue(list[i].ApiName);
                        row.CreateCell(3).SetCellValue(list[i].ApiUrl);
                        row.CreateCell(4).SetCellValue(list[i].MenuId);
                    }
                    //文件写入的位置
                    using (MemoryStream ms = new MemoryStream())
                    {
                        workbook.Write(ms, true);//向打开的这个xls文件中写入数据
                        return File(ms.ToArray(), "application/mx-excel", $"API菜单信息导出_{moduleId}_{DateTime.Now.ToString("yyyyMMdd")}.xlsx");
                    }
                }
            }

        }
        /// <summary>
        /// 获取统一登录地址
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IActionResult GetUnitLoginUrl()
        {
            var addressUnitlogin = _configuration["H57-01"];//统一登录地址
            return Ok(addressUnitlogin.ToResultDto());
        }

        /// <summary>
        /// 刷新token
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IActionResult ReNewToken(string expiredToken, string refreshToken)
        {
            var res = _systemService.ReNewToken(expiredToken, refreshToken);
            return Ok(res);
        }

        /// <summary>
        /// 读取更新日志
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet]
        public IActionResult UpdateLog()
        {
            string pathFile = Path.Combine(AppContext.BaseDirectory, "update_log.md");
            if (!System.IO.File.Exists(pathFile))
            {
                throw new FileNotFoundException("更新日志文件不存在");
            }
            string str = System.IO.File.ReadAllText(pathFile, Encoding.UTF8);
            return Ok(str.ToResultDto());
        }

        /// <summary>
        /// 获取关于系统模块地址
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet]
        public IActionResult GetSoftModuleUrlInfo()
        {
            var addressUnitlogin = _configuration["H57-03"];//统一登录地址
            string url = addressUnitlogin.ToString().Replace("{MODULE_ID}", "H81");
            return Ok(url.ToResultDto());
        }

        /// <summary>
        /// 获取所有设置
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetModuleAllConfig(string hospitalId, string moduleId, string pageId)
        {
            var res = _systemService.GetAllConfig(hospitalId, moduleId, pageId).ToString();
            return Ok(res.ToResultDto());
        }

        /// <summary>
        /// 获取页面综合设置系统模块地址
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Authorize]
        public IActionResult GetPageSettingModuleUrl()
        {
            var addressUnitlogin = _configuration["H04-01"];//公共级-页面综合设置
            var tokenGuid = IDGenHelper.CreateGuid().ToString();
            var claim = this.User.ToClaimsDto();
            var currentModuleId = _configuration["ModuleId"];
            var tokenRes = _systemService.GetIssueTokenInfo(claim.USER_NO, tokenGuid, "H04-01", currentModuleId, "");
            if (tokenRes != null && tokenRes.success && tokenRes.data != null)
            {
                AccountDto account = StringExtensions.FromJsonString<AccountDto>(tokenRes.data.ToString());
                var res = new
                {
                    Address = addressUnitlogin,
                    TokenResult = tokenRes.data,
                    swithUrl = $"{addressUnitlogin}&refresh_token={account.RefreshToken}&access_token={account.AccessToken}"
                };
                return Ok(res.ToResultDto());
            }
            return Ok(string.Empty.ToResultDto());
        }

        /// <summary>
        /// 获取页面布局地址
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Authorize]
        public IActionResult GetPageLayoutUrl()
        {
            var addressUnitlogin = _configuration["H04-02"];//公共级-页面综合设置
            var tokenGuid = IDGenHelper.CreateGuid().ToString();
            var claim = this.User.ToClaimsDto();
            var currentModuleId = _configuration["ModuleId"];
            var tokenRes = _systemService.GetIssueTokenInfo(claim.USER_NO, tokenGuid, "H04-02", currentModuleId, "");
            if (tokenRes != null && tokenRes.success && tokenRes.data != null)
            {
                AccountDto account = StringExtensions.FromJsonString<AccountDto>(tokenRes.data.ToString());
                var res = new
                {
                    Address = addressUnitlogin,
                    TokenResult = tokenRes.data,
                    swithUrl = $"{addressUnitlogin}&refresh_token={account.RefreshToken}&access_token={account.AccessToken}"
                };
                return Ok(res.ToResultDto());
            }
            return Ok(string.Empty.ToResultDto());
        }

        /// <summary>
        /// 获取H115模块地址
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Authorize]
        public IActionResult GetH115ModuleUrl()
        {
            var addressUnitlogin = _configuration["H115"];
            var tokenGuid = IDGenHelper.CreateGuid().ToString();
            var claim = this.User.ToClaimsDto();
            var currentModuleId = _configuration["ModuleId"];
            var tokenRes = _systemService.GetIssueTokenInfo(claim.USER_NO, tokenGuid, "H81", currentModuleId, "");
            if (tokenRes != null && tokenRes.success && tokenRes.data != null)
            {
                AccountDto account = StringExtensions.FromJsonString<AccountDto>(tokenRes.data.ToString());
                var res = new
                {
                    Address = addressUnitlogin,
                    TokenResult = tokenRes.data,
                    swithUrl = $"{addressUnitlogin}&refresh_token={account.RefreshToken}&access_token={account.AccessToken}"
                };
                return Ok(res.ToResultDto());
            }
            return Ok(string.Empty.ToResultDto());
        }
        [HttpGet]
        [AllowAnonymous]
        public IActionResult GetTokenSubstitution(string accessToken, string refreshToken)
        {
            return Ok(_systemService.TokenSubstitution(accessToken, refreshToken));
        }


        /// <summary>
        /// 获取考试管理系统模块地址
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet]
        public IActionResult GetExamModuleUrl(string user_no)
        {
            var addressUnitlogin = _configuration["H84"];//考试管理系统

            var tokenId = IDGenHelper.CreateGuid().ToString();

            var tokenRes = _systemService.GetIssueTokenInfo(user_no, tokenId, "H84", "");

            if (tokenRes != null && tokenRes.success)
            {
                var res = new { Address = addressUnitlogin, TokenResult = tokenRes.data };
                return Ok(res.ToResultDto());
            }
            return Ok(string.Empty.ToResultDto());
        }

        /// <summary>
        /// 获取人员评估系统模块地址
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet]
        public IActionResult GetAssessModuleUrl(string user_no)
        {
            var addressUnitlogin = _configuration["H98"];//人员评估系统

            var tokenId = IDGenHelper.CreateGuid().ToString();

            var tokenRes = _systemService.GetIssueTokenInfo(user_no, tokenId, "H98", "");

            if (tokenRes != null && tokenRes.success)
            {
                var res = new { Address = addressUnitlogin, TokenResult = tokenRes.data };
                return Ok(res.ToResultDto());
            }
            return Ok(string.Empty.ToResultDto());
        }

        /// <summary>
        /// 获取生物安全系统模块地址
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet]
        public IActionResult GetSmblModuleUrl(string user_no)
        {
            var addressUnitlogin = _configuration["H95"];//生物安全系统

            var tokenId = IDGenHelper.CreateGuid().ToString();

            var tokenRes = _systemService.GetIssueTokenInfo(user_no, tokenId, "H95", "");

            if (tokenRes != null && tokenRes.success)
            {
                var res = new { Address = addressUnitlogin, TokenResult = tokenRes.data };
                return Ok(res.ToResultDto());
            }
            return Ok(string.Empty.ToResultDto());
        }

        /// <summary>
        /// 获取系统数据岗位信息模块地址
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet]
        public IActionResult GetSystemDataPostInfoModuleUrl(string user_no)
        {
            var addressUnitlogin = _configuration["H07-11"];//系统数据岗位信息模块

            var tokenId = IDGenHelper.CreateGuid().ToString();

            var tokenRes = _systemService.GetIssueTokenInfo(user_no, tokenId, "H07-11", "");

            if (tokenRes != null && tokenRes.success)
            {
                string removeStr = "?url=postauth-record&user_no={USER_NO}&hospital_id={HOSPITAL_ID}";
                string resultAddress = (addressUnitlogin ?? string.Empty).Replace(removeStr, "");
                var res = new { Address = resultAddress, TokenResult = tokenRes.data };
                return Ok(res.ToResultDto());
            }
            return Ok(string.Empty.ToResultDto());
        }

        /// <summary>
        /// 获取系统数据人员提取模块地址
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet]
        public IActionResult GetSystemDataPersonExtractionModuleUrl(string user_no)
        {
            var addressUnitlogin = _configuration["H07-15"];

            var tokenId = IDGenHelper.CreateGuid().ToString();

            var tokenRes = _systemService.GetIssueTokenInfo(user_no, tokenId, "H07-15", "");

            if (tokenRes != null && tokenRes.success)
            {
                var res = new { Address = addressUnitlogin, TokenResult = tokenRes.data };
                return Ok(res.ToResultDto());
            }
            return Ok(string.Empty.ToResultDto());
        }


        /// <summary>
        /// 获取会议系统模块地址
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet]
        public IActionResult GetMeetingModuleUrl(string user_no)
        {
            var addressUnitlogin = _configuration["H97"];//会议

            var tokenId = IDGenHelper.CreateGuid().ToString();

            var tokenRes = _systemService.GetIssueTokenInfo(user_no, tokenId, "H97", "");

            if (tokenRes != null && tokenRes.success)
            {
                var res = new { Address = addressUnitlogin, TokenResult = tokenRes.data };
                return Ok(res.ToResultDto());
            }
            return Ok(string.Empty.ToResultDto());
        }


        /// <summary>
        /// 用户验证
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult UserVerify(string smPassword)
        {
            var claim = this.User.ToClaimsDto();
            var obj = new
            {
                logId = claim.LOGID,
                password = smPassword
            };
            string jsonStr = JsonConvert.SerializeObject(obj);
            //用户验证
            return Ok(_systemService.UserVerify(jsonStr));
        }
    }
}
