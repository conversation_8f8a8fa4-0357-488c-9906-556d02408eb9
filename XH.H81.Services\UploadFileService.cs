﻿//using H.BASE.SqlSugarInfra.Uow;
//using H.Utility;
//using Microsoft.AspNetCore.Hosting;
//using Microsoft.AspNetCore.Http;
//using Microsoft.Extensions.Configuration;
//using Microsoft.Extensions.Logging;
//using Newtonsoft.Json;
//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Text;
//using System.Threading.Tasks;
//using XH.H81.Base.Helper;
//using XH.H81.IServices;
//using XH.H81.Models.Configs;
//using XH.H81.Models.Dtos;
//using XH.H81.Models.SugarDbContext;

//namespace XH.H81.Services
//{

//    public class UploadFileService : IUploadFileService
//    {
//        private readonly ISqlSugarUow<SugarDbContext_Master> _suow;
//        private readonly ILogger<UploadFileService> _logger;
//        private readonly IBaseDataServices _basedataService;
//        private readonly IHostingEnvironment _hostingEnvironment;
//        private readonly IHttpContextAccessor _httpContext;
//        public UploadFileService(ISqlSugarUow<SugarDbContext_Master> suow, ILogger<UploadFileService> logger, IBaseDataServices basedataService, IHostingEnvironment hostingEnvironment, IHttpContextAccessor httpContext)
//        {
//            _suow = suow;
//            _logger = logger;
//            _httpContext = httpContext;
//            _basedataService = basedataService;
//            _hostingEnvironment = hostingEnvironment;
//        }



//        /// <summary>
//        /// 上传文件并生成预览文件（file.FILE或file.UPLOAD_BYTES不为空时，优先使用FormData二进制方式上传。否则如果file.FILE_BASE64有值，则以base64方式上传）
//        /// </summary>
//        /// <param name="file"></param>
//        /// <returns></returns>
//        public ResultDto UploadFileOperate(UploadFileDto file)
//        {
//            ResultDto result = new ResultDto();
//            //优先使用FormData方式上传
//            if (file.FILE != null || (file.UPLOAD_BYTES != null && file.UPLOAD_BYTES.Any()))
//            {
//                result = UploadFormDataFileOperate(file);
//            }
//            //使用Base64方式上传，该方式只有上传S28一种途径，不能保存服务器本地
//            else if (file.FILEBASE64 != null)
//            {
//                result = UploadBase64FileOperate(file.UPLOAD_FILE_NAME, IUploadFileService.S28_UPLOAD_PATH, file.FILEBASE64, file.FILE_SUFFIX);
//            }
//            else
//            {
//                result.success = false;
//                result.msg = "FILE与FILEBASE64字段都为空，无法上传！";
//            }
//            return result;
//        }


//        /// <summary>
//        /// 采用base64方式上传文件并生成预览文件（执行上传，WORD、EXCEL文件会额外生成PDF并上传供预览使用）
//        /// </summary>
//        /// <param name="fileName"></param>
//        /// <param name="moduleId"></param>
//        /// <param name="base64String"></param>
//        /// <param name="file_suffix"></param>
//        /// <returns></returns>
//        private ResultDto UploadBase64FileOperate(string fileName, string newPath, string base64String, string file_suffix)
//        {
//            ResultDto result = new ResultDto();
//            OfficeHelper oh = new OfficeHelper();
//            //string guidefile = OfficeHelper.PathCombine("PMS", "assessfile");
//            string base64file = base64String.Split(',')[1];
//            string jsonStr = string.Empty;
//            string serviceUrl = string.Empty;
//            //获取程序所在目录
//            string contentRootPath = _hostingEnvironment.ContentRootPath;
//            string folderPath = @"ExampleFile";
//            string sourcePath = OfficeHelper.PathCombine(contentRootPath, folderPath);
//            if (!Directory.Exists(sourcePath))
//            {
//                Directory.CreateDirectory(sourcePath);
//            }
//            string sourceName = fileName;
//            string strBase = string.Empty;
//            if (file_suffix == "" || file_suffix == ".pdf" || file_suffix == ".PDF" || file_suffix.ToLower() == ".jpg" || file_suffix.ToLower() == ".png" || file_suffix.ToLower() == ".bmp" || file_suffix.ToLower() == ".jpeg")/*|| file_suffix.ToLower() == ".xlsx" || file_suffix.ToLower() == ".xls"*/
//            {
//                if (file_suffix == ".pdf" || file_suffix == ".PDF")
//                {
//                    strBase = OfficeHelper.PdfAddWatermark(base64file, sourcePath, sourceName);
//                }
//                else
//                {
//                    strBase = "data:application/pdf;base64," + base64file;
//                }
//                var obj = new
//                {
//                    fileName = fileName.ToLower(),//文件名称
//                    src = strBase,//baes64字符串
//                    folderName = newPath,//文件夹路径
//                    ifCover = true,
//                };
//                jsonStr = JsonConvert.SerializeObject(obj);
//                result = _basedataService.UploadPathFile(jsonStr);
//            }
//            else if (file_suffix != "" && file_suffix != ".pdf")
//            {
//                //if (file_suffix.Contains("xls"))//excel文件不能直接加水印
//                //    strBase = base64file;
//                //else
//                //    strBase = OfficeHelper.WordAddWatermark(base64file, sourcePath, sourceName);
//                strBase = base64file;
//                if (strBase != null)
//                {
//                    var obj = new
//                    {
//                        fileName = fileName,//文件名称
//                        src = strBase,//baes64字符串
//                        folderName = newPath,//文件夹路径
//                        ifCover = true,
//                    };
//                    jsonStr = JsonConvert.SerializeObject(obj);
//                    result = _basedataService.UploadPathFile(jsonStr);
//                    if (result.success == true)
//                    {
//                        string fileName1 = fileName.Replace(file_suffix, ".pdf");
//                        if (file_suffix == ".xls" || file_suffix == ".xlsx")
//                            strBase = oh.ExcelToPdfBySpire(strBase, OfficeHelper.PathCombine(sourcePath, fileName), OfficeHelper.PathCombine(sourcePath, fileName1));
//                        else
//                            strBase = oh.WordToPdfBySpire(strBase, OfficeHelper.PathCombine(sourcePath, fileName), OfficeHelper.PathCombine(sourcePath, fileName1));
//                        strBase = OfficeHelper.PdfAddWatermark(strBase, sourcePath, sourceName);
//                        var obj1 = new
//                        {
//                            fileName = fileName1,//文件名称
//                            src = strBase,//baes64字符串
//                            folderName = newPath,//文件夹路径
//                            ifCover = true,
//                        };
//                        jsonStr = JsonConvert.SerializeObject(obj1);
//                        result = _basedataService.UploadPathFile(jsonStr);
//                    }
//                }
//                else
//                {
//                    result.success = false;
//                }
//            }
//            return result;
//        }


//        /// <summary>
//        /// 使用FormData方式上传文件并生成预览文件（执行上传，WORD、EXCEL文件会额外生成PDF并上传供预览使用）
//        /// </summary>
//        /// <param name="fileName"></param>
//        /// <param name="moduleId"></param>
//        /// <param name="base64String"></param>
//        /// <param name="file_suffix"></param>
//        /// <returns></returns>
//        private ResultDto UploadFormDataFileOperate(UploadFileDto file)
//        {
//            ResultDto result = new ResultDto();
//            OfficeHelper oh = new OfficeHelper();
//            //获取程序所在目录
//            string contentRootPath = _hostingEnvironment.ContentRootPath;
//            string folderPath = @"ExampleFile";
//            string sourcePath = OfficeHelper.PathCombine(contentRootPath, folderPath);
//            if (!Directory.Exists(sourcePath))
//            {
//                Directory.CreateDirectory(sourcePath);
//            }
//            byte[] bytes = null;
//            if (file.FILE != null)
//            {
//                using (var stream = file.FILE.OpenReadStream())
//                {
//                    bytes = new byte[stream.Length];
//                    stream.Read(bytes, 0, bytes.Length);
//                    stream.Seek(0, SeekOrigin.Begin);
//                }
//            }
//            else if (file.UPLOAD_BYTES != null &&file.UPLOAD_BYTES.Any())
//            {
//                bytes= file.UPLOAD_BYTES;
//            }
//            string file_suffix = file.FILE_SUFFIX.ToLower();
//            byte[] uploadByte = null;
//            if (file_suffix == "" || file_suffix == ".pdf" || file_suffix == ".PDF" || file_suffix.ToLower() == ".jpg" || file_suffix.ToLower() == ".png" || file_suffix.ToLower() == ".bmp" || file_suffix.ToLower() == ".jpeg")
//            {
//                if (file_suffix == ".pdf" || file_suffix == ".PDF")
//                {
//                    uploadByte = OfficeHelper.PdfAddWatermark(bytes, sourcePath, file.FILE_NAME);
//                }
//                else
//                {
//                    uploadByte = bytes;
//                }
//                file.UPLOAD_BYTES = uploadByte;
//                result = UploadFileToServer(file);
//            }
//            else if (file_suffix != "" && file_suffix != ".pdf")
//            {
//                //if (file_suffix == "doc" || file_suffix == "docx")
//                //    uploadByte = OfficeHelper.WordAddWatermark(bytes, sourcePath, file.FILE_NAME);
//                //else
//                //    uploadByte = bytes;//excel文件不能直接加水印
//                uploadByte = bytes;
//                if (uploadByte != null && uploadByte.Any())
//                {
//                    file.UPLOAD_BYTES = uploadByte;
//                    result = UploadFileToServer(file);
//                    if (result.success == true)
//                    {
//                        byte[] previewFileBytes;
//                        string fileNameAndSuffix = file.FILE_NAME.LastIndexOf(file_suffix)>-1 ? file.FILE_NAME : file.FILE_NAME + file_suffix;
//                        string tempPdfName = file.FILE_NAME.Replace(".", "_")+".pdf";
//                        if (file_suffix == ".xls" || file_suffix == ".xlsx")
//                            previewFileBytes = oh.ExcelToPdfBySpire(uploadByte, OfficeHelper.PathCombine(sourcePath, fileNameAndSuffix), OfficeHelper.PathCombine(sourcePath, tempPdfName));
//                        else
//                            previewFileBytes = oh.WordToPdfBySpire(uploadByte, OfficeHelper.PathCombine(sourcePath, fileNameAndSuffix), OfficeHelper.PathCombine(sourcePath, tempPdfName));

//                        previewFileBytes = OfficeHelper.PdfAddWatermark(previewFileBytes, sourcePath, file.FILE_NAME);
//                        var previewFile = new UploadFileDto
//                        {
//                            UPLOAD_FILE_NAME = file.UPLOAD_FILE_NAME.Replace(file_suffix, ".pdf"),
//                            UPLOAD_FOLDER_NAME = file.UPLOAD_FOLDER_NAME,
//                            IFCOVER = file.IFCOVER,
//                            SAVE_TO_S28 = file.SAVE_TO_S28,
//                            UPLOAD_BYTES = previewFileBytes,
//                            FILE_SUFFIX = ".pdf",
//                        };
//                        result = UploadFileToServer(previewFile);
//                    }
//                }
//                else
//                {
//                    result.success = false;
//                }
//            }
//            return result;
//        }

//        /// <summary>
//        /// 使用FormData方式上传文件处理（此方法决定是否调用S28上传）
//        /// </summary>
//        /// <param name="dto"></param>
//        /// <returns></returns>
//        public ResultDto UploadFileToServer(UploadFileDto file)
//        {
//            ResultDto dto = new ResultDto();
//            try
//            {
//                if (file.FILE_NAME.IsNullOrEmpty() && file.FILE != null)
//                    file.FILE_NAME = file.FILE.FileName;
//                if (file.UPLOAD_FILE_NAME.IsNullOrEmpty())
//                    file.UPLOAD_FILE_NAME = file.FILE_NAME;
//                if (file.FILE_SUFFIX.IsNullOrEmpty() && file.FILE.FileName.IsNotNullOrEmpty())
//                    file.FILE_SUFFIX = file.FILE.FileName.Substring(file.FILE.FileName.LastIndexOf('.'));
//                if (file.FILE_TYPE.IsNullOrEmpty() && file.FILE_SUFFIX.IsNotNullOrEmpty())
//                    file.FILE_TYPE = file.FILE_SUFFIX.Replace(".", "");
//                //是否调用S28上传文件
//                if (file.SAVE_TO_S28)
//                {
//                    if (file.UPLOAD_FOLDER_NAME.IsNullOrEmpty())
//                        file.UPLOAD_FOLDER_NAME = IUploadFileService.S28_UPLOAD_PATH;

//                    if (file.UPLOAD_BYTES == null)
//                    {
//                        using (var stream = file.FILE.OpenReadStream())
//                        {
//                            file.UPLOAD_BYTES = new byte[stream.Length];
//                            stream.Read(file.UPLOAD_BYTES, 0, file.UPLOAD_BYTES.Length);
//                            stream.Seek(0, SeekOrigin.Begin);
//                        }
//                    }
//                    dto = _basedataService.UploadPathFormDataFile(file.UPLOAD_FOLDER_NAME, file.UPLOAD_FILE_NAME, file.UPLOAD_BYTES);
//                }
//                else//保存文件到本地服务器
//                {
//                    if (file.UPLOAD_FOLDER_NAME.IsNullOrEmpty())
//                        file.UPLOAD_FOLDER_NAME = IUploadFileService.LOCAL_UPLOAD_PATH;
//                    //获取程序所在目录
//                    string contentRootPath = _hostingEnvironment.ContentRootPath;
//                    string sourcePath = contentRootPath + file.UPLOAD_FOLDER_NAME;
//                    if (sourcePath != null)
//                        Directory.CreateDirectory(sourcePath);
//                    var newFile = OfficeHelper.PathCombine(sourcePath ?? "", $"{file.FILE_NAME}");
//                    if (File.Exists(newFile))
//                        File.Delete(newFile);
//                    using (FileStream fs = new FileStream(newFile, FileMode.CreateNew))
//                    {
//                        file.FILE.CopyToAsync(fs);
//                        fs.Flush();
//                    }
//                    file.FILE_URL = newFile;
//                    dto.data = OfficeHelper.PathCombine(file.UPLOAD_FOLDER_NAME ?? "", $"{file.FILE_NAME}");
//                }
//            }
//            catch (Exception ex)
//            {
//                dto.success = false;
//                dto.msg = ex.ToString();
//                _logger.LogError($"{file.FILE_NAME}上传失败:{ex.ToString()}");
//            }
//            GC.Collect();
//            return dto;
//        }

//        /// <summary>
//        /// 删除文件，如果有生成预览文件也自动删除
//        /// </summary>
//        /// <param name="pathAndFileName"></param>
//        public ResultDto DeleteFileOperate(string pathAndFileName)
//        {
//            pathAndFileName = pathAndFileName.TrimStart(new char[] { '/', '\\' }).Replace("\\", "/");//前置的路径分隔符会导致S28识别不出来
//            //删除原文件
//            var obj = new List<object>
//            {
//                new {path = pathAndFileName}//文件名称
//            };

//            //删除预览文件
//            string suffix = pathAndFileName.Substring(pathAndFileName.LastIndexOf(".")).ToLower();
//            if (OfficeHelper.CastPDFToPreviewSuffixList.Contains(suffix))
//            {
//                obj.Add(new { path = pathAndFileName.Substring(0, pathAndFileName.LastIndexOf("."))+".pdf" });//预览文件名称 
//            }
//            string jsonStr = JsonConvert.SerializeObject(obj);
//            ResultDto resultDto = _basedataService.DeleteUploadFile(jsonStr);
//            return resultDto;
//        }


//        /// <summary>
//        /// 人事档案附件压缩包自动识别并上传（先注册dispatcher，再调用本方法，注册方式参考下面RegisterDispatcherSample方法）
//        /// </summary>
//        /// <param name="dispatcher">执行逻辑编排类（FileFunctionDispatcher）</param>
//        /// <param name="zipFile">人事档案附件压缩包对象</param>
//        /// <param name="dict">执行处理过程中需要外部传入的参数字典</param>
//        public ResultDto AutoDispatchUploadFile(FileFunctionDispatcher dispatcher, UploadZipDto zipFile, Dictionary<string, object> dict)
//        {
//            ResultDto resultDto = new ResultDto { success = false };
//            Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
//            //获取程序所在目录
//            string path = _hostingEnvironment.ContentRootPath;
//            string zipDir = OfficeHelper.PathCombine(path, "Temp_"+Guid.NewGuid().ToString());
//            string zipPathFile = OfficeHelper.PathCombine(zipDir, zipFile.FILE_NAME);
//            string upzipDir = OfficeHelper.PathCombine(path, "Temp_"+Guid.NewGuid().ToString());
//            List<string> files = new List<string>();
//            byte[] bytes;
//            try
//            {
//                Directory.CreateDirectory(zipDir);
//                Directory.CreateDirectory(upzipDir);
//                using (var stream = zipFile.FILE.OpenReadStream())
//                {
//                    bytes = new byte[stream.Length];
//                    stream.Read(bytes, 0, bytes.Length);
//                    stream.Seek(0, SeekOrigin.Begin);
//                };
//                File.WriteAllBytes(zipPathFile, bytes);
//                System.IO.Compression.ZipFile.ExtractToDirectory(zipPathFile, upzipDir, Encoding.GetEncoding("gbk"));
//                files = LoadFilesInFold(upzipDir);
//                resultDto.success = true;

//                //循环遍历文件列表
//                foreach (string file in files)
//                {
//                    FileDispatcherContext oneFileResult = null;
//                    try
//                    {
//                        //执行调度逻辑
//                        oneFileResult = dispatcher.Start(file, upzipDir, dict);

//                        if (!oneFileResult.Success)
//                        {
//                            resultDto.success &= oneFileResult.Success;
//                            resultDto.msg += $"处理{file.Substring(upzipDir.Length)}文件时失败，处理链路信息：{oneFileResult.Msg} \r\n  ";
//                        }
//                    }
//                    catch (Exception ex)
//                    {
//                        resultDto.success &= oneFileResult.Success;
//                        resultDto.msg += $"处理{file.Substring(upzipDir.Length)}文件时出现异常，处理链路信息：{oneFileResult.Msg} \r\n异常信息： {ex.Message}\r\n  ";
//                    }
//                }
//                return resultDto;
//            }
//            catch (Exception ex)
//            {
//                Serilog.Log.Error($"PerformUpload方法执行失败：{ex.Message}");
//                throw new Exception("解析压缩文档失败！");
//            }
//            finally
//            {
//                if (Directory.Exists(zipDir))
//                    Directory.Delete(zipDir, true);
//                if (Directory.Exists(upzipDir))
//                    Directory.Delete(upzipDir, true);
//            }

//            static List<string> LoadFilesInFold(string fold)
//            {
//                var pathFiles = new List<string>();
//                string dir = Directory.GetDirectoryRoot(fold);
//                foreach (var d in Directory.GetDirectories(fold))
//                {
//                    pathFiles.AddRange(LoadFilesInFold(d));
//                }
//                foreach (var f in Directory.GetFiles(fold))
//                {
//                    pathFiles.Add(f.Replace('\\', '/'));
//                }
//                return pathFiles;
//            }
//        }


//        ///// <summary>
//        ///// 文件预处理（自动填充文件类型等）
//        ///// </summary>
//        //public UploadFileDto FilePreproccess(UploadFileDto file)
//        //{
//        //    file.FILE_NAME = file.FILE_NAME.Replace(".", "_");//避免后缀误识别而报错
//        //    file.FILE_SUFFIX = file.FILE_SUFFIX.ToLower(); //后缀转成小写开
//        //    if (file.UPLOAD_FOLDER_NAME.IsNullOrEmpty())
//        //    {
//        //        file.UPLOAD_FOLDER_NAME = OfficeHelper.PathCombine("PMS", file.PERSON_ID);
//        //    }
//        //    if (file.UPLOAD_FILE_NAME.IsNullOrEmpty())
//        //    {
//        //        file.UPLOAD_FILE_NAME = Guid.NewGuid().ToString("N") + file.FILE_SUFFIX;
//        //    }           
//        //}
//    }
//}


