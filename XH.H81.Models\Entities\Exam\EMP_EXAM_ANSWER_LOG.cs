﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Entities.Exam
{
    /// <summary>
    /// 考试答题日志表
    /// </summary>
    [DBOwner("XH_OA")]
    public class EMP_EXAM_ANSWER_LOG
    {
        /// <summary>
        /// 考试答题ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Key]
        public string? EXAM_ANSWER_ID { get; set; }
        /// <summary>
        /// 人员考试ID
        /// </summary>
        public string? EXAM_USERID { get; set; }
        /// <summary>
        /// 医疗机构ID
        /// </summary>
        public string? HOSPITAL_ID { get; set; }
        /// <summary>
        /// 操作类型
        /// </summary>
        public string? OPER_TYPE { get; set; }
        /// <summary>
        /// 原因分类
        /// </summary>
        public string? CAUSE_TYPE { get; set; }
        /// <summary>
        /// 操作原因
        /// </summary>
        public string? OPER_CAUSE { get; set; }
        /// <summary>
        /// 操作时间
        /// </summary>
        public DateTime? OPER_TIME { get; set; }
        /// <summary>
        /// 操作人
        /// </summary>
        public string? OPER_PERSON { get; set; }
        /// <summary>
        /// 操作电脑
        /// </summary>
        public string? OPER_COMPUTER { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public string? OPER_STATE { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string? REMARK { get; set; }
    }
}
