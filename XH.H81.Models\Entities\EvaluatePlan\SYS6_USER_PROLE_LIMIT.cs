﻿//using H.Utility.SqlSugarInfra;
//using SqlSugar;
//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Text;
//using System.Threading.Tasks;

//namespace XH.H81.Models.Entities.EvaluatePlan
//{

//    /// <summary>
//    /// 用户规评岗位限权表
//    /// </summary>
//    [DBOwner("XH_SYS")]
//    public class SYS6_USER_PROLE_LIMIT
//    {
//        /// <summary>
//        /// 用户岗位限权ID（主键）
//        /// </summary>
//        [SugarColumn(IsPrimaryKey = true)]
//        public string USER_LIMIT_ID { get; set; }

//        /// <summary>
//        /// 医疗机构ID
//        /// </summary>
//        public string HOSPITAL_ID { get; set; }

//        /// <summary>
//        /// 用户ID
//        /// </summary>
//        public string USER_NO { get; set; }

//        /// <summary>
//        /// 岗位角色ID
//        /// </summary>
//        public string? POSTROLE_ID { get; set; }

//        /// <summary>
//        /// 限权类型
//        /// </summary>
//        public string? LIMIT_TYPE { get; set; }

//        /// <summary>
//        /// 限权菜单ID串
//        /// </summary>
//        public string? LIMIT_MENU_SID { get; set; }

//        /// <summary>
//        /// 事件处理批号
//        /// </summary>
//        public string? EVENT_HANDLE_UID { get; set; }

//        /// <summary>
//        /// 首次登记人
//        /// </summary>
//        public string? FIRST_RPERSON { get; set; }

//        /// <summary>
//        /// 首次登记时间
//        /// </summary>
//        public DateTime? FIRST_RTIME { get; set; }

//        /// <summary>
//        /// 最后修改人员
//        /// </summary>
//        public string? LAST_MPERSON { get; set; }

//        /// <summary>
//        /// 最后修改时间
//        /// </summary>
//        public DateTime? LAST_MTIME { get; set; }

//        /// <summary>
//        /// 备注
//        /// </summary>
//        public string? REMARK { get; set; }
//    }
//}
