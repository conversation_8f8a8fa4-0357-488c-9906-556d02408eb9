﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Dtos.Template
{
    /// <summary>
    /// 实验室表单模板字段定义表 
    /// </summary>
    [DBOwner("XH_OA")]
    public class OA_FIELD_DICT
    {
        /// <summary>
        /// 上传文件ID
        /// </summary>
        [Key]
        [SugarColumn(IsPrimaryKey = true)]
        /// <summary>
        /// 字段ID
        /// </summary>
        public string FIELD_ID { get; set; }

        /// <summary>
        /// 数据分类 0:数据项 1:记录项
        /// </summary>
        public string FIELD_TYPE { get; set; }
        /// <summary>
        /// 模块ID
        /// </summary>
        public string MODULE_ID { get; set; }

        /// <summary>
        /// 类型代码 数据库为必填
        /// </summary>
        public string CLASSE_CODE { get; set; }

        /// <summary>
        /// 类型名称
        /// </summary>
        public string? CLASSE_NAME { get; set; }

        /// <summary>
        /// 字段代码
        /// </summary>
        public string FIELD_CODE { get; set; }

        /// <summary>
        /// 字段名称
        /// </summary>
        public string FIELD_NAME { get; set; }

        /// <summary>
        /// 字段说明
        /// </summary>
        public string? FIELD_DESC { get; set; }

        /// <summary>
        /// 排序号;001
        /// </summary>
        public string FIELD_SORT { get; set; }

        /// <summary>
        /// 默认值
        /// </summary>
        public string? FIELD_DEFAULT { get; set; }

        /// <summary>
        /// 格式;YYYY
        /// </summary>
        public string? FIELD_FORMAT { get; set; }

        /// <summary>
        /// 数据的作用域，支持多个 如 证书类型是附件，且同时还是下拉.在word显示时需要有超链接，则如下 FILE, DICT, ALINK
        /// </summary>
        public string? FIELD_DOMAIN { get; set; }

        /// <summary>
        /// 数据源（目前用于字典，但兼容了扩展） [{"type":"DICT","origin:"",jsonData:""}origin  1,2,3  (固定枚举、进OA_BASE_DATA字典、走模块提供的接口)jsonData值示例1 : {"1":"男","2":女"}   2:"OA_BASE_DATA"     3:"/api/xxxxxx"  (根据模块名获取地址然后去取)"} ]
        /// </summary>
        public string? FIELD_DOMAIN_ORIGIN { get; set; }

        /// <summary>
        /// 状态;0禁用1启用2删除
        /// </summary>
        public string FIELD_STATE { get; set; }

        /// <summary>
        /// 首次登记人
        /// </summary>
        public string FIRST_RPERSON { get; set; }

        /// <summary>
        /// 首次登记时间
        /// </summary>
        public DateTime FIRST_RTIME { get; set; }

        /// <summary>
        /// 最后修改人员
        /// </summary>
        public string LAST_MPERSON { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime LAST_MTIME { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string REMARK { get; set; }

    }
}
