﻿using System;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using SqlSugar;
using H.Utility.SqlSugarInfra;

/*
 * <AUTHOR> XingHe
 * @date : 2023-8-25
 * @desc : 人员评估记录表
 */
namespace XH.H81.Models.Entities.Pms
{
    /// <summary>
    /// 人员评估记录表
    /// </summary>
    [SugarTable("PMS_ASSESS_PLAN_PERSON")]
    [DBOwner("XH_OA")]
    public class PMS_ASSESS_PLAN_PERSON
    {
        /// <summary>
        /// 计划人员评估ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        //[Column("PLAN_PERSON_ID")]
        [Required(ErrorMessage = "计划人员评估ID不允许为空")]
        [StringLength(50, ErrorMessage = "计划人员评估ID长度不能超出50字符")]
        //[Unicode(false)]
        public string PLAN_PERSON_ID { get; set; }

        /// <summary>
        /// 评估计划ID
        /// </summary>
        //[Column("PLAN_ID")]
        [Required(ErrorMessage = "评估计划ID不允许为空")]
        [StringLength(50, ErrorMessage = "评估计划ID长度不能超出50字符")]
        //[Unicode(false)]
        public string PLAN_ID { get; set; }

        /// <summary>
        /// 检验专业组ID
        /// </summary>
        //[Column("PGROUP_ID")]
        [Required(ErrorMessage = "检验专业组ID不允许为空")]
        [StringLength(20, ErrorMessage = "检验专业组ID长度不能超出20字符")]
        //[Unicode(false)]
        public string PGROUP_ID { get; set; }

        /// <summary>
        /// 参加考评人员ID
        /// </summary>
        //[Column("USER_ID")]
        [Required(ErrorMessage = "参加考评人员ID不允许为空")]
        [StringLength(50, ErrorMessage = "参加考评人员ID长度不能超出50字符")]
        //[Unicode(false)]
        public string USER_ID { get; set; }

        /// <summary>
        /// 考评次数
        /// </summary>
        //[Column("EVALUATE_NUM")]
        [Required(ErrorMessage = "考评次数不允许为空")]
        //[Unicode(false)]
        public int EVALUATE_NUM { get; set; }


        /// <summary>
        /// 总评价
        /// </summary>
        //[Column("TOTAL_EVALUATE")]
        [StringLength(50, ErrorMessage = "总评价长度不能超出50字符")]
        //[Unicode(false)] 
        public string? TOTAL_EVALUATE { get; set; }

        /// <summary>
        /// 总成绩名次
        /// </summary>
        //[Column("TOTAL_RANKING")]

        //[Unicode(false)]
        public int? TOTAL_RANKING { get; set; }

        /// <summary>
        /// 总成绩（分）
        /// </summary>
        //[Column("TOTAL_SCORE")]
        [StringLength(255, ErrorMessage = "总成绩（分）长度不能超出255字符")]
        //[Unicode(false)]
        public string? TOTAL_SCORE { get; set; }

        /// <summary>
        /// 综合评价
        /// </summary>
        //[Column("COMP_EVALUATE")]
        [StringLength(50, ErrorMessage = "综合评价长度不能超出50字符")]
        //[Unicode(false)]
        public string? COMP_EVALUATE { get; set; }

        /// <summary>
        /// 综合考评名次
        /// </summary>
        //[Column("EVALUATE_RANKING")]

        //[Unicode(false)]
        public int? EVALUATE_RANKING { get; set; }

        /// <summary>
        /// 综合考评成绩(分)
        /// </summary>
        //[Column("EVALUATE_SCORE")]

        //[Unicode(false)]
        public decimal? EVALUATE_SCORE { get; set; }

        /// <summary>
        /// 自评成绩(分)
        /// </summary>
        //[Column("SEVALUATION_SCORE")]

        //[Unicode(false)]
        public decimal? SEVALUATION_SCORE { get; set; }

        /// <summary>
        /// 自评时间
        /// </summary>
        //[Column("SEVALUATION_TIME")]

        //[Unicode(false)]
        public DateTime? SEVALUATION_TIME { get; set; }

        /// <summary>
        /// 考试名次
        /// </summary>
        //[Column("EXAM_RANKING")]

        //[Unicode(false)]
        public int? EXAM_RANKING { get; set; }

        /// <summary>
        /// 考试成绩(分)
        /// </summary>
        //[Column("EXAM_SCORE")]

        //[Unicode(false)]
        public decimal? EXAM_SCORE { get; set; }

        /// <summary>
        /// 评卷人员
        /// </summary>
        //[Column("MARK_EXAM_PERSON")]
        [StringLength(50, ErrorMessage = "评卷人员长度不能超出50字符")]
        //[Unicode(false)]
        public string? MARK_EXAM_PERSON { get; set; }

        /// <summary>
        /// 评卷时间
        /// </summary>
        //[Column("MARK_EXAM_TIME")]

        //[Unicode(false)]
        public DateTime? MARK_EXAM_TIME { get; set; }

        /// <summary>
        /// 审核人员
        /// </summary>
        //[Column("CHECK_PERSON")]
        [StringLength(50, ErrorMessage = "审核人员长度不能超出50字符")]
        //[Unicode(false)]
        public string? CHECK_PERSON { get; set; }

        /// <summary>
        /// 审核时间
        /// </summary>
        //[Column("CHECK_TIME")]

        //[Unicode(false)]
        public DateTime? CHECK_TIME { get; set; }

        /// <summary>
        /// 补考评开始时间
        /// </summary>
        //[Column("EVALUATION_START_TIME")]

        //[Unicode(false)]
        public DateTime? EVALUATION_START_TIME { get; set; }

        /// <summary>
        /// 补考评结束时间
        /// </summary>
        //[Column("EVALUATION_END_TIME")]

        //[Unicode(false)]
        public DateTime? EVALUATION_END_TIME { get; set; }

        /// <summary>
        /// 补考试开始时间
        /// </summary>
        //[Column("EXAM_START_TIME")]

        //[Unicode(false)]
        public DateTime? EXAM_START_TIME { get; set; }

        /// <summary>
        /// 补考试结束时间
        /// </summary>
        //[Column("EXAM_END_TIME")]

        //[Unicode(false)]
        public DateTime? EXAM_END_TIME { get; set; }

        /// <summary>
        /// 评估状态；0未参加 1已参加 2已自评 3已考评（专家全部考评完成）4废止
        /// </summary>
        //[Column("EVALUATE_STATE")]
        [StringLength(10, ErrorMessage = "评估状态长度不能超出10字符")]
        //[Unicode(false)]
        public string? EVALUATE_STATE { get; set; }

        /// <summary>
        /// 评卷状态;0未参加 1未评卷 2待审核 3通过 4驳回
        /// </summary>
        //[Column("MARK_EXAM_STATE")]
        [StringLength(10, ErrorMessage = "评卷状态长度不能超出10字符")]
        //[Unicode(false)]
        public string? MARK_EXAM_STATE { get; set; }

        /// <summary>
        /// 首次登记人
        /// </summary>
        //[Column("FIRST_RPERSON")]
        [StringLength(50, ErrorMessage = "首次登记人长度不能超出50字符")]
        //[Unicode(false)]
        public string? FIRST_RPERSON { get; set; }

        /// <summary>
        /// 首次登记时间
        /// </summary>
        //[Column("FIRST_RTIME")]

        //[Unicode(false)]
        public DateTime? FIRST_RTIME { get; set; }

        /// <summary>
        /// 最后修改人员
        /// </summary>
        //[Column("LAST_MPERSON")]
        [StringLength(50, ErrorMessage = "最后修改人员长度不能超出50字符")]
        //[Unicode(false)]
        public string? LAST_MPERSON { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        //[Column("LAST_MTIME")]

        //[Unicode(false)]
        public DateTime? LAST_MTIME { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        //[Column("REMARK")]
        [StringLength(200, ErrorMessage = "备注长度不能超出200字符")]
        //[Unicode(false)]
        public string? REMARK { get; set; }

        /// <summary>
        /// 考试分评价结果
        /// </summary>
        public string?  EXAM_EVALUATE { get; set; }

        /// <summary>
        /// 评价重算依据
        /// </summary>
        public string? RECOMPUTE_DEPEND { get; set; }

        /// <summary>
        /// 数据来源类型
        /// </summary>
        public string? SOURCE_TYPE { get; set; }


        /// <summary>
        /// 规评组合ID
        /// </summary>
        public string? STD_GROUP_ID { get; set; }


        /// <summary>
        /// 规评方案ID
        /// </summary>
        public string? STD_SCHEME_ID { get; set; }


        /// <summary>
        /// 评估名称
        /// </summary>
        public string? EVALUATE_NAME { get; set; }

        /// <summary>
        /// 关联培训ID
        /// </summary>
        public string? TRAIN_ID { get; set; }

        /// <summary>
        /// 关联资质证书ID
        /// </summary>
        public string? CER_ID { get; set; }

        /// <summary>
        /// 附件
        /// </summary>
        public string? EVALUATE_AFFIX { get; set; }

        /// <summary>
        /// 生安标志
        /// </summary>
        public string? SMBL_FLAG { get; set; }

    }
}