﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Dtos.Template
{
    public class StyleTemplateFillDataDto
    {
        /// <summary>
        /// 模板ID
        /// </summary>
        public string STYLE_ID { get; set; }

        /// <summary>
        /// 数据
        /// </summary>
        public List<StyleTemplateClassDataDto> DATAS { get; set; }
    }

    /// <summary>
    /// 模板字段数据DTO
    /// </summary>
    public class StyleTemplateClassDataDto
    {
        /// <summary>
        /// 类型ID
        /// </summary>
        public string CLASSE_CODE { get; set; }
        /// <summary>
        /// 单字段数据
        /// </summary>
        public Dictionary<string, string> FIELDS { get; set; }=new Dictionary<string, string>();


        /// <summary>
        /// 列表数据ARRAYS不为空时必须传,作为判断表格填充第一个字段的条件,值为FIELD_CODE
        /// </summary>
        //  public string? START_FIELD { get; set; }

        /// <summary>
        /// 列表数据
        /// </summary>
        public List<Dictionary<string, string>> ARRAYS { get; set; }=new List<Dictionary<string, string>>();
    }

    /** 示例
{
    "STYLE_ID": "BeJson",
	"DATAS": [
        {
        "CLASSE_CODE": "安装信息",
			"FIELDS": {
            "OPEN_DATE": "2019-12-14",
				"LAB_PERSON": "胡庆丰",
				"HOS_PERSON": "包云露"

            }
    },
		{
        "CLASSE_CODE": "数据项",
			"FIELDS": {
            "OPEN_DATE": "2020-11-7",
				"LAB_PERSON": "胡庆丰",
				"HOS_PERSON": "包云露"

            }
    },
		{
        "CLASSE_CODE": "履历信息",
			"ARRAYS": [
                {
            "OPEN_DATE": "2023-10-14",
					"LAB_PERSON": "胡庆丰",
					"HOS_PERSON": "包云露"

                },
				{
            "OPEN_DATE": "2024-7-14",
					"LAB_PERSON": "胡庆丰",
					"HOS_PERSON": "包云露"

                }
			]
		}
	]
} * */
}
