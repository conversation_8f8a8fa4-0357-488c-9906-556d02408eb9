﻿using XH.H81.Models.Entities.Pms;

namespace XH.H81.Models.Dtos.Pms
{
    public class SysClassInfoDto
    {
        public string MODULE_CLASSID { get; set; }

        public string CLASS_NAME { get; set; }

        public string ARCHIVE_TABLE { get; set; }

        public string CLASS_TABLE_NAME { get; set; }

        /// <summary>
        ///  分类归属 0-普通 1-健康档案
        /// </summary>
        public string CLASS_KIND { get; set; }

        public string IF_UPLOAD_FILE { get; set; }

        public string PRIMARY_KEY { get; set; }

        public List<object> PMS_RESUME_LIST { get; set; }

        public List<object> PMS_REWARD_LIST { get; set; }
        public List<object> PMS_TEACH_LIST { get; set; }
        public List<object> PMS_STUDY_LIST { get; set; }
        public List<object> PMS_RESEARCH_LIST { get; set; }
        public List<object> PMS_THESIS_LIST { get; set; }
        public List<object> PMS_EDUCATION_LIST { get; set; }
        public List<object> PMS_SKILL_CERTIFICATE_LIST { get; set; }
        public List<object> PMS_TRAIN_LIST { get; set; }
        public List<object> PMS_SOCIAL_OFFICE_LIST { get; set; }
        public List<object> PMS_INTELLECTUAL_LIST { get; set; }
        public List<object> PMS_EXCHANGE_LIST { get; set; }

        public List<object> PMS_PROFESSIONAL_LIST { get; set; }

        public List<object> PMS_EXPATRIATE_LIST { get; set; }

        public List<object> PMS_EXAM_LIST { get; set; }

        public List<object> PMS_ASSESS_LIST { get; set; }
        public IList<Dictionary<string, object>> PMS_ADDN_RECORD { get; set; }

    }
}
