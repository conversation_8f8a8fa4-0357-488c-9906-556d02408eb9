﻿using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;

namespace XH.H81.Models
{
    public partial class DBContext_Slave : DbContext
    {
        public DBContext_Slave()
        {
        }

        public DBContext_Slave(DbContextOptions<DBContext_Slave> options)
            : base(options)
        {

        }

    
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {


            foreach (var property in modelBuilder.Model.GetEntityTypes().SelectMany(e => e.GetProperties().Where(p => p.ClrType == typeof(string))))
            {
                property.SetIsUnicode(false);
            }
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            base.OnConfiguring(optionsBuilder);

            optionsBuilder.EnableSensitiveDataLogging();
            optionsBuilder.EnableDetailedErrors();
        }

    }
}
