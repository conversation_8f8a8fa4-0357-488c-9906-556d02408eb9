﻿using H.Utility;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XH.H81.Models.Dtos.Exam;
using XH.H81.Models.Entities.Exam;

namespace XH.H81.IServices
{
    public interface IExamService
    {
        /// <summary>
        /// 获取考试信息
        /// </summary>
        /// <param name="userNo"></param>
        /// <param name="smblFlag"></param>
        /// <returns></returns>
        ResultDto GetUserExamInfo(string userNo, string? labId, string mgroupId, string pgroupId, string? examState, string? examType, string examName, string? smblFlag, string dateType, string startDate, string endDate, string cerId,string hospitalId);

        /// <summary>
        /// 保存考试记录
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="userName"></param>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        ResultDto SaveExamInfo(EmpExamUserAnswerDto dto,string userName,string hospitalId);

        /// <summary>
        /// 更新考试记录
        /// </summary>
        /// <returns></returns>
        ResultDto UpdateExamUserAnswerInfo(string? exam_userids, string? exam_user_state, string? user_name, string? hospital_id, string? check_person, string? oper_cause, string? oper_computer, string? pwd_bs, string? log_id);

        /// <summary>
        /// 保存考试规评登记
        /// </summary>
        /// <param name="stdSchemeDto"></param>
        /// <param name="userName"></param>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        ResultDto SaveExamStdScheme(ExamStdSchemeDto stdSchemeDto, string userName, string hospitalId);

        /// <summary>
        /// 保存考试规评等级明细
        /// </summary>
        /// <param name="listsStdSchemeDto"></param>
        /// <param name="userName"></param>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        ResultDto SaveExamStdSchemeDetail(List<ExamStdSchemeDto> listsStdSchemeDto, string userName, string hospitalId);
        /// <summary>
        /// 获取规评信息
        /// </summary>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <param name="labGroupId">科室/专业组id</param>
        /// <param name="eplanName"></param>
        /// <param name="examName"></param>
        /// <returns></returns>

        ResultDto GetExamStd(string startDate, string endDate, string labGroupId, string eplanName, string examName, string smblFlag);


        /// <summary>
        /// 获取规评分组明细
        /// </summary>
        /// <param name="stdGroupId"></param>
        /// <param name="labPgroupId"></param>
        /// <param name="comName"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        ResultDto GetExamStdDetail(string stdGroupId, string labPgroupId, string comName, string userName);

        /// <summary>
        /// 删除考试规评明细
        /// </summary>
        /// <param name="stdGroupId"></param>
        /// <param name="listUserId"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        ResultDto DeleteExamStdDetail(string stdGroupId, List<string> listUserId, string userName);

    }
}
