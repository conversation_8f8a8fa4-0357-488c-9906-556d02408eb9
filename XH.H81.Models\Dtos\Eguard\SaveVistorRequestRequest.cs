﻿using Microsoft.AspNetCore.Http;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Dtos.Eguard
{
    public class SaveVistorRequestRequest
    {
        /// <summary>
        /// 访问申请ID
        /// </summary>
        public string? VISIT_REQ_ID { get; set; }
        /// <summary>
        /// 人员姓名
        /// </summary>
        public string? PERSON_NAME { get; set; }
        /// <summary>
        /// 人员ID
        /// </summary>
        public string? PERSON_ID { get; set; }

        /// <summary>
        /// 申请类型
        /// </summary>
        public string VISIT_REQ_TYPE { get; set; }
        /// <summary>
        /// 科室人员
        /// </summary>
        public string? VISIT_PERSON_ID { get; set; }
        /// <summary>
        /// 访问地点
        /// </summary>
        public string? VISIT_ROOM_ID { get; set; }
        /// <summary>
        /// 同行人员
        /// </summary>
        public string? FELLOW_PERSON { get; set; }
        /// <summary>
        /// 同行人数
        /// </summary>
        public int? FELLOW_PERSON_NUM { get; set; }
        /// <summary>
        /// 访问理由
        /// </summary>
        public string? VISIT_REQ_REASON { get; set; }
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? VISIT_START_TIME { get; set; }
        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? VISIT_END_TIME { get; set; }
        /// <summary>
        /// 图片地址
        /// </summary>
        public string? PHOTO_PATH { get; set; }
        /// <summary>
        /// 手机号
        /// </summary>
        public string? PHONE_NO { get; set; }
        /// <summary>
        /// 所属单位
        /// </summary>
        public string? WORK_UNIT { get; set; }
        /// <summary>
        /// 图片
        /// </summary>
        public IFormFile? FILE { get; set; }
        /// <summary>
        /// 图片后缀
        /// </summary>
        public string? FILE_SUFFIX { get; set; }
        public string? FILE_NAME { get;set; }
        public string? VISIT_TIME_FRAME { get; set; }
    }
}
