﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Entities.Eguard
{
    /// <summary>
    /// 门禁组合字典表
    /// </summary>
    [DBOwner("XH_OA")]
    public class PMS_EGUARD_COM_DICT
    {
        /// <summary>
        /// 组合ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string EGUARD_COM_ID { get; set; }
        /// <summary>
        /// 医疗机构ID
        /// </summary>
        public string HOSPITAL_ID { get; set; }
        /// <summary>
        /// 组合名称
        /// </summary>
        public string? EGUARD_COM_NAME { get; set; }
        /// <summary>
        /// 组合描述
        /// </summary>
        public string? EGUARD_COM_DESC { get; set; }
        /// <summary>
        /// 组合状态
        /// </summary>
        public string? EGUARD_COM_STATE { get; set; }
        public string? FIRST_RPERSON { get; set; }
        public DateTime? FIRST_RTIME { get; set; }
        public string? LAST_MPERSON { get; set; }
        public DateTime? LAST_MTIME { get; set; }
        public string? REMARK { get; set; }
    }
}
