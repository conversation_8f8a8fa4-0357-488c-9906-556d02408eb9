﻿using H.Utility;
using XH.H81.Models.Dtos;
using XH.H81.Models.Dtos.Pms;
using XH.H81.Models.Entities.Pms;
using XH.H81.Models.ExternalEntity;
using XH.LAB.UTILS.Models;
using XH.LAB.UTILS.Models.Dto;

namespace XH.H81.IServices
{
    public interface IModuleLabGroupService
    {
        List<Sys6MenuDto> GetMenuInfo(string strModuleId, string strHospitalid, string lab_id, string user_no);
        List<InspectionLabDto> GetLisInspectionLabInfo(string strUserNo, string strHospitalId);
        LogonHospitalLabList GetLogonHospitalLabList(string strUserNo, string strHospitalId);

        //List<Lis_Inspection_pgroupDto> GetPgroupInfoByLabIdUser(string hospital_id, string lab_id, string pgroup_id, string person_name, string user_no, string page_type);

        IEnumerable<InspectionPgroupDto> GetPersonInfoTree(string hospital_id, string lab_id, string pgroup_id, string person_name, string user_no);

        List<DropDowDto> GetGroupDropDownInfo(string hospital_id, string pgroup_id, string lab_id);

        List<DropDowDto> GetUserDropDownInfo(string hospital_id, string lab_id);
        //  List<LIS5_INSPECTION_PGROUP> GetLisInspectionPgroupInfo(string strUserNo, string strHospitalId);


        //////////////////////////////////////////////////////////////////////////////////////////////////////////////////
        Dictionary<string, List<DropDown>> GetBaseDataInfo(string hospital_id);

        public Dictionary<string, List<LevelDropDown>> GetLevelTitle();
        string GetPersonId(string hospital_id, string user_no);

        /// <summary>
        /// 获取院区名称
        /// </summary>
        /// <param name="areaId"></param>
        /// <returns></returns>
        string GetAreaName(string areaId);

        bool GitRecordWriteRowNum(string person_id, string hospital_id);

        /// <summary>
        /// 通过PERSON_ID判断是否存在人事档案录入记录（GitRecordWriteRowNum方法的性能优化版）
        /// </summary>
        /// <param name="person_id"></param>
        /// <param name="hospital_id"></param>
        /// <returns></returns>
        Dictionary<string, bool> GetPersonHasRecord(List<string> pgroup_ids, string hospital_id);

        /// <summary>
        /// 获取用户的岗位权限许可的导航列表（或按钮列表，权限类列表）
        /// </summary>
        /// <param name="userNo"></param>
        /// <param name="menuClass"></param>
        /// <param name="parentCode"></param>
        /// <param name="hospitalId"></param>
        /// <param name="labId"></param>
        /// <param name="pGroupId"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        List<SYS6_MENU> GetUserMenuList(string userNo, MenuClassEnum menuClass = MenuClassEnum.ALL, string parentCode = null, string hospitalId = null, string labId = null, string pGroupId = null);

        /// <summary>
        /// 判断用户在具体条件下（医院、科室、专业）是否存在某一权限（页面/按钮/权限类）
        /// </summary>
        /// <param name="userNo"></param>
        /// <param name="menuId"></param>
        /// <param name="hospitalId"></param>
        /// <param name="labId"></param>
        /// <param name="pGroupId"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        bool CheckUserMenuPermission(string userNo, string menuId, string hospitalId = null, string labId = null, string pGroupId = null, bool isThrowException = false);

        /// <summary>
        /// 获取用户某一权限（页面/按钮/权限类）下的专业组列表
        /// </summary>
        /// <param name="userNo"></param>
        /// <param name="menuId"></param>
        /// <param name="hospitalId"></param>
        /// <param name="labId"></param>
        /// <returns></returns>
        List<SYS6_INSPECTION_PGROUP> GetUserPermissionPgroup(string userNo, string menuId, string hospitalId = null, string labId = null);

        /// <summary>
        /// 获取专业组列表
        /// </summary>
        /// <param name="hospitalId"></param>
        /// <param name="labId"></param>
        /// <returns></returns>
        List<SYS6_INSPECTION_PGROUP> GetPgroupList(string hospitalId, string labId = null);

        /// <summary>
        /// 获取备案实验室列表
        /// </summary>
        /// <param name="hospitalId"></param>
        /// <param name="labId"></param>
        /// <returns></returns>
        List<SMBL_LAB> GetSmblLabList(string hospitalId, string labId = null); 

        /// <summary>
        /// 获取用户某一权限（页面/按钮/权限类）下的科室列表，返回结果包含所有状态的科室
        /// </summary>
        /// <param name="userNo"></param>
        /// <param name="menuId">需要判断权限的菜单ID（可以是页面/按钮/类），来源于：select * from XH_SYS.SYS6_MENU where sys_menu ='H98'</param>
        /// <param name="hospitalId"></param>
        /// <param name="labId"></param>
        /// <returns></returns>
        List<SYS6_INSPECTION_LAB> GetUserPermissionLab(string userNo, string menuId, string hospitalId = null, string labId = null);
        List<PMS_ADDN_CLASS_INFO> GetPmsAddnClassInfo(bool isIncludeDisable = false);
        List<PMS_PERSON_INFO> GetRangePersonList(OrgUserParams orgParm, string person_name, string permissMenuId, out List<SYS6_USER> userList, out OrgTree orgTreeNodes);
        PersonRange GetPgroupRangePersonList(OrgUserParams orgParm, string person_name, string permissMenuId, bool ifCheckPremission = true);
        List<AreaGroupTreeNode> GetTestOrganizationTree(string? hospital_id);
        SMBL_ORG GetSmblOrg(string hospitalId);
        OrgTree GetLabMgroupPgroupTree(OrgUserParams orgParm, string permissMenuId, bool ifReturnSourceObject);

        /// <summary>
        /// 获取组合人员信息
        /// </summary>
        /// <returns></returns>
        List<UserComDto> GetUserComInfo(string comName, string userName);
    }
}
