﻿using System.Diagnostics;
using System.IdentityModel.Tokens.Jwt;
using System.ServiceModel;
using System.Text;
using System.Xml;
using H.BASE;
using H.Utility;
using H.Utility.Helper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using RestSharp;
using Serilog;
using XH.H81.IServices;
using XH.H81.Models.Entities;
using XingHePlatform;

namespace XH.H81.Services
{
    public class SystemService : ISystemService
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<SystemService> _logger;
        private readonly IHttpContextAccessor _httpContext;
        private readonly XingHePlatformSoapClient _clientXhPlatform;
        private readonly RestClient _clientH04;
        private readonly RestClient _clientS01;
        private readonly RestClient _clientS10;
        private readonly RestClient _clientH5702;

        public SystemService(IConfiguration configuration, ILogger<SystemService> logger, IHttpContextAccessor httpContext)
        {
            var addressS10 = configuration["S10"];//5.0接口地址
            var addressH04 = configuration["H04-13"];//检验工具箱
            var addressS01 = configuration["UrlModuleS01"];//S01
            var addressH5702 = configuration["H57-02"];
            _configuration = configuration;
            _logger = logger;
            _httpContext = httpContext;

            if (addressS10.IsNotNullOrEmpty())
            {
                var binding = new BasicHttpBinding();
                binding.MaxReceivedMessageSize = 241000000;
                _clientXhPlatform = new XingHePlatformSoapClient(binding, new EndpointAddress(addressS10));
            }
            if (addressS01.IsNotNullOrEmpty())
            {
                _clientS01 = new RestClient(new RestClientOptions()
                {
                    RemoteCertificateValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true,
                    BaseUrl = new Uri(addressS01),
                    ThrowOnAnyError = true
                });
            }
            if (addressH04.IsNotNullOrEmpty())
            {
                _clientH04 = new RestClient(new RestClientOptions()
                {
                    RemoteCertificateValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true,
                    BaseUrl = new Uri(addressH04),
                    ThrowOnAnyError = true
                });
            }
            if (addressH5702.IsNotNullOrEmpty())
            {
                _clientH5702 = new RestClient(new RestClientOptions()
                {
                    RemoteCertificateValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true,
                    BaseUrl = new Uri(addressH5702),
                    ThrowOnAnyError = true
                });
            }

        }


        #region 接口相关

        /// <summary>
        /// 调用接口返回原始数据
        /// </summary>
        /// <param name="headXml"></param>
        /// <param name="bodyXml"></param>
        /// <param name="dataformat"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public string CallXhPlatformInterfaceSource(string headXml, string bodyXml, out string dataformat)
        {
            string format = "JSON";
            string methodCode = "";
            Stopwatch sw = new Stopwatch();
            sw.Start();
            //头部新增签名节点
            XmlDocument xmlHead = new XmlDocument();
            xmlHead.LoadXml(headXml);
            XmlNode headRoot = xmlHead.SelectSingleNode("Root");
            //接口以后可能会需要鉴权,此处加入签名字段
            XmlElement node = xmlHead.CreateElement("Sign");
            node.InnerText = SmxUtilsHelper.SM3Utils(AppSettingsProvider.XingHePlatFormKey + bodyXml);
            headRoot.AppendChild(node); //
            //没有定义数据格式节点的,自动加入<DataFormat>JSON</DataFormat>以返回json格式数据(接口默认返回xml)
            var formater = headRoot.SelectNodes("DataFormat");
            if (formater.Count == 0)
            {
                XmlElement nodeFormat = xmlHead.CreateElement("DataFormat");
                nodeFormat.InnerText = "JSON";
                headRoot.AppendChild(nodeFormat);
            }
            else
            {
                format = formater[0].InnerText.ToUpper();
            }

            var MethodCode = headRoot.SelectSingleNode("MethodCode");
            if (MethodCode == null)
            {
                throw new Exception("接口HeadXml中MethodCode未定义");
            }
            else
            {
                methodCode = MethodCode.InnerText;
            }

            if (format != "JSON" && format != "XML")
            {
                throw new Exception($"5.0接口:不支持的DataFormat类型:{format}");
            }

            dataformat = format;

            headXml = XMLHelper.ConvertXmlToString(xmlHead);
            var resFromInterface =
                _clientXhPlatform.CallInterfaceAsync(headXml, bodyXml).Result.Body.CallInterfaceResult;
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .ForContext("SourceContext", "XinghePlatform")
                .ForContext("HeadXml", headXml)
                .ForContext("BodyXml", bodyXml)
                .Information($"调用接口方法:{methodCode},耗时:{sw.ElapsedMilliseconds}ms");

            return resFromInterface;
        }

        /// <summary>
        /// 直接取Items内容并转为T
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="headXml"></param>
        /// <param name="bodyXml"></param>
        /// <returns>T</returns>
        public T CallXhPlatformInterface<T>(string headXml, string bodyXml) where T : class
        {
            var res = this.CallXhPlatformInterfaceSource(headXml, bodyXml, out string dataformat);
            string code = "";
            string message = "";
            string data = "";
            JObject obj;
            if (dataformat == "XML")
            {
                var doc = new XmlDocument();
                doc.LoadXml(res);
                obj = JObject.Parse(JsonConvert.SerializeXmlNode(doc));
                code = obj["Response"]["ResultCode"].ToString();
                message = obj["Response"]["ResultMsg"].ToString();
                if (code == "1")
                {
                    data = obj["Response"]["Items"]?["Item"].ToString();
                }
            }

            if (dataformat == "JSON")
            {
                obj = JObject.Parse(res);
                code = obj["ResultCode"].ToString();
                message = obj["ResultMsg"].ToString();
                if (code == "1")
                {
                    data = obj["Items"].ToString();
                }
            }

            if (code == "-1")
            {
                throw new Exception("接口调用成功,但是返回了错误信息:" + message);
            }

            if (code == "0")
            {
                //无数据
                return null;
            }

            //Xml转json时,如果只有一个子节点,此节点的数据讲返回一个对象,而不是array 为了统一 此处强行转array
            if (data.Substring(0, 1) == "{")
                data = "[" + data + "]";

            return JsonHelper.FromJson<T>(data.ToString());
        }

        /// <summary>
        /// 调用6.0方法方法
        /// </summary>
        /// <param name="headXml"></param>
        /// <param name="bodyXml"></param>
        /// <returns></returns>
        public ResultDto CallS10InterfaceSource(string headXml, string bodyXml)
        {
            ResultDto result = new ResultDto();
            Stopwatch sw = new Stopwatch();
            sw.Start();
            JObject patientinfo = new JObject();
            //进行Base64编码
            patientinfo["headXml"] = Convert.ToBase64String(Encoding.UTF8.GetBytes(headXml));
            patientinfo["bodyxml"] = Convert.ToBase64String(Encoding.UTF8.GetBytes(bodyXml));
            string sendObj = JsonConvert.SerializeObject(patientinfo);
            string url = "/S10/API";
            RestRequest request = new RestRequest(url);
            request.AddJsonBody(sendObj);
            var response = _clientS10.ExecutePost<string>(request);
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用S10(6.0接口),耗时:{sw.ElapsedMilliseconds}ms");
            if (response.ErrorException != null)
            {
                Log.Error($"调用S10(6.0接口)发生错误:{response.ErrorException}");
                throw new Exception($"调用S10(6.0接口)发生错误:{response.ErrorException}", response.ErrorException);
            }
            else
            {
                result.data = response.Content;
            }

            return result;
        }

        #endregion

        #region 设置相关 周围涛维护

        [UseCacheWhenFailed]
        public object GetAllConfig(string hospitalId, string moduleId, string pageId, string? qunitId)
        {
            hospitalId.CheckNotNullOrEmpty("hospitalId");
            moduleId.CheckNotNullOrEmpty("moduleId");
            pageId.CheckNotNullOrEmpty("pageId");

            var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();
            Stopwatch sw = new Stopwatch();
            sw.Start();

            string url =
                $"/externalapi/External/GetSetItemsByPageId?hospitalId={hospitalId}&moduleId={moduleId}&pageId={pageId}&qunitId={qunitId}";
            var sss = _configuration["H04-13"];
            RestRequest request = new RestRequest(url);
            if (!token.Contains("Bearer"))
            {
                request.AddHeader("Authorization", "Bearer " + token);
            }
            else
            {
                request.AddHeader("Authorization", token);
            }
            try //避免调用外部模块报错泄露绝对路径
            {
                var response = _clientH04.ExecuteGet<ResultDto>(request);
                sw.Stop();
                Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                    .Information($"调用H04模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
                if (response.ErrorException != null)
                {
                    Log.Error($"调用H04模块[{url}]发生错误:{response.ErrorException}");
                    throw new Exception($"调用H04模块获取模块设置发生错误!");
                }
                else
                {
                    if (response.Data.success)
                    {
                        object hidedSortConfig = HideAllSortField(response.Data.data);
                        return hidedSortConfig;
                    }
                    else
                    {
                        Log.Error($"调用H04模块[{url}]请求完成,但是返回了错误:" + response.Data.msg);
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex.ToString());
                throw new Exception("调用H04模块获取模块设置发生错误!");
            }
        }
        /// <summary>
        /// 去掉排序字段
        /// </summary>
        /// <param name="config"></param>
        /// <returns></returns>
        private object HideAllSortField(object config)
        {
            StringBuilder sbDelete = new StringBuilder();

            string rightString = config?.ToString();
            string keyWord = "\"formName\":\"排序号\"";
            if (rightString.IsNotNullOrEmpty())
            {
                StringBuilder sb = new StringBuilder();
                int keyIndex = rightString.IndexOf(keyWord);//是关键字
                while (keyIndex > 0)
                {
                    keyIndex = keyIndex + 16;//16是关键字的长度
                    string leftString = rightString.Substring(0, keyIndex);
                    rightString = rightString.Substring(keyIndex + 1);
                    int startIndex = leftString.LastIndexOf("{");//片段开始
                    if (startIndex > 0)
                    {
                        int endIndex = rightString.IndexOf("}");//片段结束
                        if (endIndex > 0)
                        {
                            if (rightString.Substring(endIndex + 1, 1) == ",")//去除逗号
                            {
                                endIndex = endIndex + 1;
                            }
                            sbDelete.Append(leftString.Substring(startIndex));
                            sbDelete.Append(rightString.Substring(0, endIndex + 1));
                            leftString = leftString.Substring(0, startIndex);
                            rightString = rightString.Substring(endIndex + 1);
                        }
                    }
                    sb.Append(leftString);
                    keyIndex = rightString.IndexOf(keyWord);//关键字
                }
                string test = sbDelete.ToString();

                return $"{sb}{rightString}";
            }
            else
            {
                return config;
            }        
        }


        /// <summary>
        /// 清除工具箱缓存
        /// </summary>
        /// <param name="hospitalId"></param>
        /// <param name="moduleId"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public ResultDto ClearCache(string hospitalId, string moduleId)
        {
            var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();
            Stopwatch sw = new Stopwatch();
            sw.Start();

            string url =
                $"/externalapi/External/ClearCache?hospitalId={hospitalId}&moduleId={moduleId}";
            var sss = _configuration["H04-13"];
            RestRequest request = new RestRequest(url);
            if (!token.Contains("Bearer"))
            {
                request.AddHeader("Authorization", "Bearer " + token);
            }
            else
            {
                request.AddHeader("Authorization", token);
            }

            var response = _clientH04.ExecuteGet<ResultDto>(request);
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用H04模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
            if (response.ErrorException != null)
            {
                Log.Error($"调用H04模块[{url}]发生错误:{response.ErrorException}");
                throw new Exception($"调用H04模块[{url}]发生错误:{response.ErrorException}", response.ErrorException);
            }
            else
            {
                if (response.Data.success)
                {
                    return response.Data;
                }
                else
                {
                    Log.Error($"调用H04模块[{url}]请求完成,但是返回了错误:" + response.Data.msg);
                    return null;
                }
            }
        }


        /// <summary>
        /// 获取复杂表单信息
        /// </summary>
        /// <param name="hospitalId"></param>
        /// <param name="moduleId"></param>
        /// <param name="setUpId"></param>
        /// <param name="merge"></param>
        /// <param name="qunitId"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public ResultDto GetComplexForms(string hospitalId, string moduleId, string setUpId, string merge, string qunitId)
        {
            var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();
            Stopwatch sw = new Stopwatch();
            sw.Start();

            string url =
                $"/externalapi/External/GetComplexForms?hospitalId={hospitalId}&moduleId={moduleId}&setupId={setUpId}&merge={merge}&qunitID={qunitId}";
            var sss = _configuration["H04-13"];
            RestRequest request = new RestRequest(url);
            if (!token.Contains("Bearer"))
            {
                request.AddHeader("Authorization", "Bearer " + token);
            }
            else
            {
                request.AddHeader("Authorization", token);
            }

            var response = _clientH04.ExecuteGet<ResultDto>(request);
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用H04模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
            if (response.ErrorException != null)
            {
                Log.Error($"调用H04模块[{url}]发生错误:{response.ErrorException}");
                throw new Exception($"调用H04模块[{url}]发生错误:{response.ErrorException}", response.ErrorException);
            }
            else
            {
                if (response.Data.success)
                {
                        return response.Data;
                }
                else
                {
                    Log.Error($"调用H04模块[{url}]请求完成,但是返回了错误:" + response.Data.msg);
                    ResultDto result = new ResultDto();
                    result.success = false;
                    result.msg = "查询模板为空";
                    return result;
                }
            }
        }

        /// <summary>
        /// 保存工具库字段
        /// </summary>
        /// <param name="json"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public ResultDto SaveFieldDict(string json)
        {
            try
            {
                var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();
                Stopwatch sw = new Stopwatch();
                sw.Start();

                string url =
                    $"/externalapi/External/SaveFieldDict";
                var sss = _configuration["H04-13"];
                RestRequest request = new RestRequest(url);
                if (!token.Contains("Bearer"))
                {
                    request.AddHeader("Authorization", "Bearer " + token);
                }
                else
                {
                    request.AddHeader("Authorization", token);
                }
                request.AddJsonBody(json);
                var response = _clientH04.ExecutePost<ResultDto>(request);
                sw.Stop();
                Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                    .Information($"调用H04模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
                if (response.ErrorException != null)
                {
                    Log.Error($"调用H04模块[{url}]发生错误:{response.ErrorException}");
                    throw new Exception($"调用H04模块[{url}]发生错误:{response.ErrorException}", response.ErrorException);
                }
                else
                {
                    if (response.Data.success)
                    {
                        return response.Data;
                    }
                    else
                    {
                        Log.Error($"调用H04模块[{url}]请求完成,但是返回了错误:" + response.Data.msg);
                        return response.Data;
                    }
                }
            }
            catch (Exception ex)
            {
                return null;
            }
            return null;
        }


        /// <summary>
        /// 创建表单模板
        /// </summary>
        /// <param name="json"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public ResultDto CreateComplexForms(string json)
        {
            var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();
            Stopwatch sw = new Stopwatch();
            sw.Start();

            string url =
                $"/externalapi/External/CreateComplexForms";
            var sss = _configuration["H04-13"];
            RestRequest request = new RestRequest(url);
            if (!token.Contains("Bearer"))
            {
                request.AddHeader("Authorization", "Bearer " + token);
            }
            else
            {
                request.AddHeader("Authorization", token);
            }
            request.AddJsonBody(json);
            var response = _clientH04.ExecutePost<ResultDto>(request);
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用H04模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
            if (response.ErrorException != null)
            {
                Log.Error($"调用H04模块[{url}]发生错误:{response.ErrorException}");
                throw new Exception($"调用H04模块[{url}]发生错误:{response.ErrorException}", response.ErrorException);
            }
            else
            {
                if (response.Data.success)
                {
                    return response.Data;
                }
                else
                {
                    Log.Error($"调用H04模块[{url}]请求完成,但是返回了错误:" + response.Data.msg);
                    return null;
                }
            }
        }

        public ResultDto GetGetFieldDicts(string hospitalId,string moduleId, string filedClass)
        {
            var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();
            Stopwatch sw = new Stopwatch();
            sw.Start();

            string url = $"/externalapi/External/GetFieldDicts?hospitalId={hospitalId}&moduleId={moduleId}&fieldClass={filedClass}";
            var sss = _configuration["H04-13"];
            RestRequest request = new RestRequest(url);
            if (!token.Contains("Bearer"))
            {
                request.AddHeader("Authorization", "Bearer " + token);
            }
            else
            {
                request.AddHeader("Authorization", token);
            }

            var response = _clientH04.ExecuteGet<ResultDto>(request);
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用H04模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
            if (response.ErrorException != null)
            {
                Log.Error($"调用H04模块[{url}]发生错误:{response.ErrorException}");
                throw new Exception($"调用H04模块[{url}]发生错误:{response.ErrorException}", response.ErrorException);
            }
            else
            {
                if (response.Data.success)
                {
                    return response.Data;
                }
                else
                {
                    Log.Error($"调用H04模块[{url}]请求完成,但是返回了错误:" + response.Data.msg);
                    return null;
                }
            }
        }

        [UseCacheWhenFailed]
        public object GetSingleConfig(EnumSetupType stepType, string hospitalId, string moduleId, string setupName,
            string? qunitId)
        {
            Stopwatch sw = new Stopwatch();
            sw.Start();
            string url = "";
            switch (stepType)
            {
                case EnumSetupType.表格设置:
                    url = "/externalapi/External/GetXhGridConfigJsonObj";
                    break;
                case EnumSetupType.条件设置:
                    url = "/externalapi/External/GetXhConditonConfigJsonObj";
                    break;
                case EnumSetupType.tab页设置:
                    url = "/externalapi/External/GetReportUnitTabJsonObj";
                    break;
                case EnumSetupType.表单设置:
                    url = "/externalapi/External/GetFormConfig";
                    break;
                case EnumSetupType.显示设置:
                    url = "/externalapi/External/GetDisplayInfoConfig";
                    break;
            }

            url += $"?hospitalId={hospitalId}&moduleId={moduleId}&setupName={setupName}&qunitId={qunitId}";
            RestRequest request = new RestRequest(url);
            var response = _clientH04.ExecuteGet<ResultDto>(request);
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用H04模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
            if (response.ErrorException != null)
            {
                Log.Error($"调用H04模块[{url}]发生错误:{response.ErrorException}");
                throw new Exception($"调用H04模块[{url}]发生错误:{response.ErrorException}", response.ErrorException);
            }
            else
            {
                if (response.Data.success)
                {
                    return response.Data.data;
                }
                else
                {
                    Log.Error($"调用H04模块[{url}]请求完成,但是返回了错误:" + response.Data.msg);
                    return null;
                }
            }
        }

        #endregion

        #region token相关 

        public ResultDto GetIssueTokenInfo(string userNo, string tokenGuid, string moduleId, string? extraInfo)
        {
            Stopwatch sw = new Stopwatch();

            sw.Start();
            var accessToken = _httpContext.HttpContext.Request.Headers.Authorization;
            if (accessToken.ToString() is null)
            {
                throw new ArgumentException("请先登录");
            }

            string url =
                $"/api/Account/GetIssueTokenInfo?userNo={userNo}&tokenGuid={tokenGuid}&moduleId={moduleId}&extraInfo={extraInfo}";

            RestRequest request = new RestRequest(url);
            request.AddHeader("Authorization", $"{accessToken}");
            var response = _clientS01.ExecutePost<ResultDto>(request);
            Log.Information($"获取Token:{response.ResponseUri}");
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用S01模块[颁发Token],耗时:{sw.ElapsedMilliseconds}ms");
            if (response.ErrorException != null)
            {
                Log.Error($"调用S01模块[颁发Token]发生错误:{response.ErrorException}");
                throw new Exception($"调用S01模块[颁发Token]发生错误:{response.ErrorException}", response.ErrorException);
            }
            else
            {
                if (response.Data.success)
                {
                    return response.Data;
                }
                else
                {
                    Log.Error($"调用S01模块[颁发Token]请求完成,但是返回了错误:" + response.Data.msg);
                    return null;
                }
            }
        }   /// <summary>
            /// 
            /// </summary>
            /// <param name="userNo"></param>
            /// <param name="tokenGuid"></param>
            /// <param name="moduleId"></param>
            /// <param name="callModuleId"></param>
            /// <param name="extraInfo"></param>
            /// <returns></returns>
            /// <exception cref="Exception"></exception>
        public ResultDto GetIssueTokenInfo(string userNo, string tokenGuid, string moduleId, string callModuleId, string? extraInfo)
        {
            Stopwatch sw = new Stopwatch();
            sw.Start();
            var accessToken = _httpContext.HttpContext.Request.Headers.Authorization;
            string url = $"/api/Account/GetIssueTokenInfo?userNo={userNo}&tokenGuid={tokenGuid}&callModuleId={callModuleId}&moduleId={moduleId}&extraInfo={extraInfo}";

            RestRequest request = new RestRequest(url);
            request.AddHeader("Authorization", $"{accessToken}");
            var response = _clientS01.ExecutePost<ResultDto>(request);
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用S01模块[颁发Token],耗时:{sw.ElapsedMilliseconds}ms");
            if (response.ErrorException != null)
            {
                Log.Error($"调用S01模块[颁发Token]发生错误:{response.ErrorException}");
                throw new Exception($"调用S01模块[颁发Token]发生错误:{response.ErrorException}", response.ErrorException);
            }
            else
            {
                if (response.Data.success)
                {
                    return response.Data;
                }
                else
                {
                    Log.Error($"调用S01模块[颁发Token]请求完成,但是返回了错误:" + response.Data.msg);
                    return null;
                }
            }
        }


        public ResultDto ReNewToken(string expiredToken, string refreshToken)
        {
            //调用S01 服务续签
            //如果需要改造成各个应用自行续签.在此处修改代码
            Stopwatch sw = new Stopwatch();
            sw.Start();
            string url = $"/api/Account/GetExtensionTokenInfo?accountToken={expiredToken}";

            RestRequest request = new RestRequest(url);
            request.AddHeader("Authorization", "Bearer " + refreshToken);
            var response = _clientS01.ExecutePost<ResultDto>(request);
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用S01模块续签Token,耗时:{sw.ElapsedMilliseconds}ms");
            if (response.ErrorException != null)
            {
                Log.Error($"调用S01模块[续签Token]发生错误:{response.ErrorException}");
                throw new Exception($"调用S01模块[续签Token]发生错误:{response.ErrorException}", response.ErrorException);
            }
            else
            {
                if (response.Data.success)
                {
                    return response.Data;
                }
                else
                {
                    Log.Error($"调用S01模块[续签Token]请求完成,但是返回了错误:" + response.Data.msg);

                    throw new Exception($"调用S01模块[续签Token]请求完成,但是返回了错误:" + response.Data.msg);
                }
            }
        }

        /// <summary>
        /// 传入用户信息自定义生成TOKEN
        /// </summary>
        /// <param name="userNo"></param>
        /// <param name="tokenGuid"></param>
        /// <param name="moduleId"></param>
        /// <param name="obj">自定义用户信息</param>
        /// <returns></returns>
        public ResultDto CustomCreateToken(string userNo, dynamic obj, string tokenGuid, string moduleId)
        {
            Stopwatch sw = new Stopwatch();
            sw.Start();
            string sendObj = JsonConvert.SerializeObject(obj);
            string url = $"/api/Account/CustomCreateToken?userNo={userNo}&tokenGuid={tokenGuid}&moduleId={moduleId}";
            RestRequest request = new RestRequest(url);
            request.AddJsonBody(sendObj);
            var response = _clientS01.ExecutePost<ResultDto>(request);
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用S01模块颁发Token,耗时:{sw.ElapsedMilliseconds}ms");
            if (response.ErrorException != null)
            {
                Log.Error($"调用S01模块[颁发Token]发生错误:{response.ErrorException}");
                throw new Exception($"调用S01模块[颁发Token]发生错误:{response.ErrorException}", response.ErrorException);
            }
            else
            {
                if (response.Data.success)
                {
                    return response.Data;
                }
                else
                {
                    Log.Error($"调用S01模块[颁发Token]请求完成,但是返回了错误:" + response.Data.msg);
                    throw new Exception($"调用S01模块[颁发Token]请求完成,但是返回了错误:" + response.Data.msg);
                }
            }
        }



        /// <summary>
        /// 换token
        /// </summary>
        /// <param name="expriedToken">token</param>
        /// <param name="refreshToken">刷新token</param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public ResultDto TokenSubstitution(string expriedToken, string refreshToken)
        {
            Stopwatch sw = new Stopwatch();
            sw.Start();
            string url = $"/api/Account/TokenSubstitution?accountToken={expriedToken}&refreshToken={refreshToken}";

            RestRequest request = new RestRequest(url);
            request.AddHeader("Authorization", "Bearer " + refreshToken);
            var response = _clientS01.ExecutePost<ResultDto>(request);
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用S01模块TokenSubstitution,耗时:{sw.ElapsedMilliseconds}ms");
            if (response.ErrorException != null)
            {
                Log.Error($"调用S01模块[TokenSubstitution]发生错误:{response.ErrorException}");
                throw new Exception($"调用S01模块[TokenSubstitution]发生错误:{response.ErrorException}", response.ErrorException);
            }
            else
            {
                if (response.Data.success)
                {
                    return response.Data;
                }
                else
                {
                    Log.Error($"调用S01模块[TokenSubstitution]请求完成,但是返回了错误:" + response.ToString());

                    throw new Exception($"调用S01模块[TokenSubstitution]请求完成,但是返回了错误:" + response.Data.msg);
                }
            }
        }
        #endregion



        #region 密码校验


        /// <summary>
        /// 用户验证
        /// </summary>
        /// <param name="logId"></param>
        /// <param name="password"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public ResultDto UserVerify(string jsonStr)
        {
            var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();
            Stopwatch sw = new Stopwatch();
            sw.Start();
            string url = $"/api/Login/UserVerify";
            RestRequest request = new RestRequest(url);
            request.AddHeader("Authorization", token);
            request.AddJsonBody(jsonStr);
            RestResponse<ResultDto> response;
            try
            {
                response = _clientH5702.ExecutePost<ResultDto>(request);
            }
            catch (Exception e)
            {
                Log.Error($"调用H5702模块[{url}]发生错误:{e.ToString()}");
                throw new BizException($"调用H5702模块发生错误!");
            }
            sw.Stop();
            Log.ForContext("elapsed", sw.ElapsedMilliseconds)
                .Information($"调用H5702模块[{url}],耗时:{sw.ElapsedMilliseconds}ms");
            if (response == null || response.ErrorException != null)
            {
                Log.Error($"调用H5702模块[{url}]发生错误:{response?.ErrorException}");
                throw new BizException($"调用H5702模块发生错误!");
            }
            else
            {
                if (response.Data.success)
                {
                    return response.Data;
                }
                else
                {
                    Log.Error($"调用H5702模块[{url}]请求完成,但是返回了错误:" + response.Data.msg);
                    return response.Data;
                }
            }
        }

        #endregion


        #region 获取机构信息


        /// <summary>
        /// 用户验证
        /// </summary>
        /// <param name="logId"></param>
        /// <param name="password"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public ResultDto GetHospitalInfo(string hospitalId)
        {
            var token = _httpContext.HttpContext.Request.Headers.Authorization.ToString();
            string url = $"/api/Infra/GetHospitalInfo";//?hospitalId={hospitalId}
            RestRequest request = new RestRequest(url);
            request.AddParameter("hospitalId", hospitalId);
            request.AddHeader("Authorization", token);
            RestResponse<ResultDto> response;
            try
            {
                response = _clientH5702.ExecuteGet<ResultDto>(request);
            }
            catch (Exception e)
            {
                Log.Error($"调用H5702模块[{url}]获取机构信息发生错误:{e.ToString()}");
                throw new BizException($"调用H5702模块获取机构信息发生错误!");
            }
            if (response == null || response.ErrorException != null)
            {
                Log.Error($"调用H5702模块获取机构信息[{url}]发生错误:{response?.ErrorException}");
                throw new BizException($"调用H5702模块获取机构信息发生错误!");
            }
            else
            {
                if (response.Data.success)
                {
                    return response.Data;
                }
                else
                {
                    Log.Error($"调用H5702模块获取机构信息[{url}]请求完成,但是返回了错误:" + response.Data.msg);
                    return response.Data;
                }
            }
        }


        #endregion
    }
}