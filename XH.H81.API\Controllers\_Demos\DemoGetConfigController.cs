﻿using H.Utility;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using XH.H81.IServices;

namespace XH.H81.API.Controllers._demos
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize]
    [ApiExplorerSettings(GroupName = "DEMO")]

    public class DemoGetConfigController : ControllerBase
    {
        private readonly ISystemService _systemService;

        public DemoGetConfigController(ISystemService systemService)
        {
            _systemService = systemService;
        }

        /// <summary>
        /// 获取所有设置
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetModuleAllConfig()
        {
           //var res= _systemService.GetAllConfig("H0000", "H07", "B3201").ToString();

            var res = _systemService.GetAllConfig("33A001", "H81", "H81").ToString();

            return Ok(res.ToResultDto());
        }

        /// <summary>
        /// 获取单个设置
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetSingleConfig()
        {
            var res = _systemService.GetSingleConfig(EnumSetupType.表格设置,"H0000", "B32", "医疗机构注册列表" ).ToString();
            return Ok(res.ToResultDto());
        }
    }
}
