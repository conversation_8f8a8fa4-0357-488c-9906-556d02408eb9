﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Entities.others
{
    /// <summary>
    /// 用户组合表
    /// </summary>
    [DBOwner("XH_SYS")]
    public class SYS6_USER_COM
    {
        /// <summary>
        /// 用户组合ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]

        public string? USER_COM_ID { get; set; }
        /// <summary>
        /// 医疗机构ID
        /// </summary>
        public string? HOSPITAL_ID { get; set; }
        /// <summary>
        /// 科室ID
        /// </summary>
        public string? LAB_ID { get; set; }
        /// <summary>
        /// 排序号
        /// </summary>
        public string? USER_COM_USORT { get; set; }
        /// <summary>
        /// 用户组合名称
        /// </summary>
        public string? USER_COM_NAME { get; set; }
        /// <summary>
        /// 用户ID
        /// </summary>
        public string? USER_SNO { get; set; }
        /// <summary>
        /// 状态[0禁用1在用2删除]
        /// </summary>
        public string? USER_COM_STATE { get; set; }
    }
}
