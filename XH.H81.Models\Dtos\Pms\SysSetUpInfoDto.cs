﻿using Org.BouncyCastle.Bcpg;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Dtos.Pms
{
    /// <summary>
    /// 返回工具箱分类信息
    /// </summary>
    public class SysSetUpInfoDto
    {
        /// <summary>
        /// 主键
        /// </summary>

        public string? SETUP_ID { get; set; }
        /// <summary>
        /// 分类名称
        /// </summary>
        public string? SETUP_CNAME { get; set; }

        /// <summary>
        /// 0禁用1在用
        /// </summary>
        public string? SETUP_STATE { get; set; }
        /// <summary>
        /// 是否上传文件 0不上传1 上传
        /// </summary>
        public string? IF_UPLOAD_FILE { get; set; }

        /// <summary>
        /// 上传文件是否必填 0非必填 1必填
        /// </summary>
        public string? IF_MUST_UPLOAD_FILE { get; set; }

        /// <summary>
        /// 是否为新增字段 1为新增
        /// </summary>
        public string? IF_NEW { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public string? SETUP_SORT { get; set; }

        /// <summary>
        /// 分类代码
        /// </summary>
        public string? SETUP_CODE { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string? REMARK { get; set; }

        /// <summary>
        /// 是否隐藏 0-否 1-是； 默认否
        /// </summary>
        public int? IS_HIDE { get; set; }

        /// <summary>
        /// 是否可编辑属性 0-否 1-是； 默认是
        /// </summary>
        public int? IS_PROP_EDITABLE { get; set; }

        /// <summary>
        /// 是否有审核流程 0-否 1-是； 默认否 表示审核页面是否可见该分类
        /// </summary>
        public int? IS_AUDITABLE { get; set; }

        /// <summary>
        ///  分类归属
        /// </summary>
        public string? CLASS_KIND { get; set; }

        /// <summary>
        ///  分类归属名称
        /// </summary>
        public string? CLASS_KIND_NAME { get; set; }

        /// <summary>
        ///  生安标志
        /// </summary>
        public string? SMBL_FLAG { get; set; }

        /// <summary>
        ///  记录生安标记
        /// </summary>
        public string? SMBL_REC_FLAG { get; set; }

        /// <summary>
        /// 分类归属子节点
        /// </summary>
        public List<SysSetUpInfoDto>? CHILD { get; set; }=new List<SysSetUpInfoDto>();

        public List<DATA_TYPE>? DATA_TYPE_LIST{ get; set; }
    }

    public class DATA_TYPE
    {
        public string DATA_TYPE_UID { get; set; }
        public string DATA_TYPE_NAME { get; set; }
        public string DATA_TYPE_TABLE {  get; set; }
        public string DATA_TYPE_CLASS {  get; set; }
        public bool IS_EDITABLE { get; set; } = true;
    }

}
