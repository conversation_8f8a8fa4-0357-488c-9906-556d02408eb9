﻿using System;
using System.Collections.Generic;
using H.Utility.SqlSugarInfra;
using SqlSugar;

/*
 * <AUTHOR>
 * @date : 2025-4-2
 * @desc : 人员标签记录表
 */
namespace XH.H81.Models.Entities.Tag
{
    /// <summary>
    /// 人员标签记录表
    /// </summary>
    [DBOwner("XH_OA")]
    public class PMS_PERSON_TAG
    {
        /// <summary>
        /// 人员id
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string PERSON_ID { get; set; }

        /// <summary>
        /// 标签id
        /// </summary>
        [SugarColumn( IsPrimaryKey = true)]
        public string PERSON_TAG_ID { get; set; }

        /// <summary>
        /// 人员标签标志;0禁用 1在用（默认） 2删除
        /// </summary>
        public string TAG_PSTATE { get; set; }

        /// <summary>
        /// 首次登记人
        /// </summary>
        public string FIRST_RPERSON { get; set; }

        /// <summary>
        /// 首次登记时间
        /// </summary>
        public DateTime FIRST_RTIME { get; set; }

        /// <summary>
        /// 最后修改人员
        /// </summary>
        public string LAST_MPERSON { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime LAST_MTIME { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string REMARK { get; set; }

    }
}