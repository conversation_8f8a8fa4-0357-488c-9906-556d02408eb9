using System;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using SqlSugar;
using H.Utility.SqlSugarInfra;

namespace XH.H81.Models.ExternalEntity
{
    [Table("SYS6_FEILD_DICT")]
    [DBOwner("XH_SYS")]
    public class SYS6_FEILD_DICT 
	{
		/// <summary>
		/// 字段ID
		/// </summary>
		[Key]
        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        //[Column("FIELD_ID")]
		[Required(ErrorMessage = "字段ID不允许为空")]

		[StringLength(50, ErrorMessage = "字段ID长度不能超出50字符")]
		//[Unicode(false)]
		public string FIELD_ID { get; set; }

		/// <summary>
		/// 首次登记人
		/// </summary>
		//[Column("FIRST_RPERSON")]
		[StringLength(50, ErrorMessage = "首次登记人长度不能超出50字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FIRST_RPERSON { get; set; }

		/// <summary>
		/// 最后修改时间
		/// </summary>
		//[Column("LAST_MTIME")]
		//[Unicode(false)]
		public DateTime? LAST_MTIME { get; set; }

		/// <summary>
		/// 模块ID
		/// </summary>
		//[Column("FIELD_MODULE")]
		[Required(ErrorMessage = "模块ID不允许为空")]

		[StringLength(50, ErrorMessage = "模块ID长度不能超出50字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string FIELD_MODULE { get; set; }

		/// <summary>
		/// 格式
		/// </summary>
		//[Column("FEILD_FORMAT")]
		[StringLength(50, ErrorMessage = "格式长度不能超出50字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FEILD_FORMAT { get; set; }

		/// <summary>
		/// 最后修改人员
		/// </summary>
		//[Column("LAST_MRPERSON")]
		[StringLength(50, ErrorMessage = "最后修改人员长度不能超出50字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? LAST_MRPERSON { get; set; }

		/// <summary>
		/// 字段说明
		/// </summary>
		//[Column("FEILD_DESC")]
		[StringLength(500, ErrorMessage = "字段说明长度不能超出500字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FEILD_DESC { get; set; }

		/// <summary>
		/// 默认值
		/// </summary>
		//[Column("FIELD_VALUE")]
		[StringLength(50, ErrorMessage = "默认值长度不能超出50字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FIELD_VALUE { get; set; }

		/// <summary>
		/// 系统表名称
		/// </summary>
		//[Column("TABLE_NAME")]
		[StringLength(50, ErrorMessage = "系统表名称长度不能超出50字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? TABLE_NAME { get; set; }

		/// <summary>
		/// 状态
		/// </summary>
		//[Column("FIELD_STATE")]
		[StringLength(20, ErrorMessage = "状态长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FIELD_STATE { get; set; }

		/// <summary>
		/// 字段ID
		/// </summary>
		//[Column("FIELD_CODE")]
		[StringLength(50, ErrorMessage = "字段ID长度不能超出50字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FIELD_CODE { get; set; }

		/// <summary>
		/// 字段名称
		/// </summary>
		//[Column("FIELD_NAME")]
		[StringLength(50, ErrorMessage = "字段名称长度不能超出50字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FIELD_NAME { get; set; }

		/// <summary>
		/// 备注
		/// </summary>
		//[Column("REMARK")]
		[StringLength(200, ErrorMessage = "备注长度不能超出200字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? REMARK { get; set; }

		/// <summary>
		/// 首次登记时间
		/// </summary>
		//[Column("FIRST_RTIME")]
		//[Unicode(false)]
		public DateTime? FIRST_RTIME { get; set; }

		/// <summary>
		/// 系统表ID
		/// </summary>
		//[Column("TABLE_ID")]
		[Required(ErrorMessage = "系统表ID不允许为空")]

		[StringLength(50, ErrorMessage = "系统表ID长度不能超出50字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string TABLE_ID { get; set; }

		/// <summary>
		/// 排序号
		/// </summary>
		//[Column("FIELD_SORT")]
		[StringLength(20, ErrorMessage = "排序号长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FIELD_SORT { get; set; }


	}
}
