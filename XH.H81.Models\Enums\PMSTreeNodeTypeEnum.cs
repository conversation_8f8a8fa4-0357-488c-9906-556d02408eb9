﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models
{
    /// <summary>
    /// 专业组树节点类型
    /// </summary>
    public enum PMSTreeNodeTypeEnum
    {
        /// <summary>
        /// 检验专业组
        /// </summary>
        [Description("检验专业组")]
        PGROUP = 1,

        /// <summary>
        ///管理专业组
        /// </summary>
        [Description("管理专业组")]
        MGROUP = 2,

        /// <summary>
        /// 院区
        /// </summary>
        [Description("院区")]
        AREA = 3,

        /// <summary>
        /// 人员
        /// </summary>
        [Description("人员")]
        PERSON = 0,

        /// <summary>
        /// 生安岗位
        /// </summary>
        [Description("生安岗位")]
        SMBL_POST = 8180,

        /// <summary>
        /// 生安组织节点
        /// </summary>
        [Description("生安组织节点")]
        SMBL_NODE = 8181,
    }
}
