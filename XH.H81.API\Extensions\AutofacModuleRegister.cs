﻿using System.Reflection;
using Autofac;
using Autofac.Extras.DynamicProxy;
using H.BASE.IServices;
using H.Utility.AOP;
using XH.H81.IServices;
using XH.H81.IServices.Message;
using XH.H81.IServices.Pms;
using XH.H81.Services;
using XH.H81.Services.Pms;
using XH.LAB.UTILS.Implement;
using XH.LAB.UTILS.Interface;
using IMessageService = XH.LAB.UTILS.Interface.IMessageService;
using MessageService = XH.LAB.UTILS.Implement.MessageService;

namespace H.BASE
{
    public class AutofacModuleRegister : Autofac.Module
    {
        protected override void Load(ContainerBuilder builder)
        {
            //缓存AOP
            builder.RegisterType<CacheAOP>();
            builder.RegisterType<UseCacheWhenFailedAOP>();
            //拦截器列表
            var interceptType = new List<Type>();
            //缓存拦截器
            interceptType.Add(typeof(CacheAOP));
            //访问失败返回缓存数据拦截器
            interceptType.Add(typeof(UseCacheWhenFailedAOP));
            /////////////////
            //系统服务
            builder.RegisterType<SystemService>().As<ISystemService>().InstancePerLifetimeScope()
                .EnableInterfaceInterceptors()//启用拦截器
                .InterceptedBy(interceptType.ToArray());
            ////系统服务
            //builder.RegisterType<RedisStreamMsgServices>().As<IRedisStreamMsgServices>().InstancePerLifetimeScope();
            builder.RegisterType<RedisStreamMsgServices>().As<IRedisStreamMsg_OLDServices>().InstancePerLifetimeScope();
            //基础数据服务
            builder.RegisterType<BaseDataServices>().As<IBaseDataServices>().InstancePerLifetimeScope()
                .EnableInterfaceInterceptors()//启用拦截器
                .InterceptedBy(interceptType.ToArray());
            builder.RegisterType<HttpContextAccessor>().As<IHttpContextAccessor>().SingleInstance();
            /////////////////
            //消息服务
            builder.RegisterType<MessageService>().As<IMessageService>().InstancePerLifetimeScope()
                .EnableInterfaceInterceptors()//启用拦截器
                .InterceptedBy(interceptType.ToArray());
            builder.RegisterType<HttpContextAccessor>().As<IHttpContextAccessor>().SingleInstance();
            //业务服义注册 IDemoServices 可能有多个实现,根据实际需要注册指定的实现类
            builder.RegisterType<DemoServices>().As<IDemoServices>().InstancePerLifetimeScope()
                .EnableInterfaceInterceptors()//启用拦截器
                .InterceptedBy(interceptType.ToArray());

            builder.RegisterType<ModuleLabGroupService>().As<IModuleLabGroupService>().InstancePerLifetimeScope()
                        .EnableInterfaceInterceptors()
                        .InterceptedBy(interceptType.ToArray());

            //人事
            builder.RegisterType<PmsService>().As<IPmsService>().InstancePerLifetimeScope()
                .EnableInterfaceInterceptors()
                .InterceptedBy(interceptType.ToArray());

            //人员信息
            builder.RegisterType<UserService>().As<IUserService>().InstancePerLifetimeScope()
                .EnableInterfaceInterceptors()
                .InterceptedBy(interceptType.ToArray());


            //分类设置
            builder.RegisterType<PageSettingService>().As<IPageSettingService>().InstancePerLifetimeScope()
                .EnableInterfaceInterceptors()
                .InterceptedBy(interceptType.ToArray());

            //文件上传
            builder.RegisterType<UploadFileService>().As<IUploadFileService>().InstancePerLifetimeScope()
                .EnableInterfaceInterceptors()//启用拦截器
                .InterceptedBy(interceptType.ToArray());

            //权限服务
            builder.RegisterType<AuthorityService>().As<IAuthorityService>().InstancePerLifetimeScope()
                .EnableInterfaceInterceptors()//启用拦截器
                .InterceptedBy(interceptType.ToArray());

            builder.RegisterType<H115OnlyOfficeService>().As<IH115OnlyOfficeService>().InstancePerLifetimeScope()
                 .EnableInterfaceInterceptors()//启用拦截器
                 .InterceptedBy(interceptType.ToArray());

            builder.RegisterType<OrganizationTreeService>().As<IOrganizationTreeService>().InstancePerLifetimeScope()
                 .EnableInterfaceInterceptors()//启用拦截器
                 .InterceptedBy(interceptType.ToArray());

            builder.RegisterType<AuthorityService2>().As<IAuthorityService2>().InstancePerLifetimeScope()
                .EnableInterfaceInterceptors()//启用拦截器
                .InterceptedBy(interceptType.ToArray());

            builder.RegisterType<OrganizationTreeService2>().As<IOrganizationTreeService2>().InstancePerLifetimeScope()
                 .EnableInterfaceInterceptors()//启用拦截器
                 .InterceptedBy(interceptType.ToArray());

            builder.RegisterType<EvaluatePlanService>().As<IEvaluatePlanService>().InstancePerLifetimeScope()
                 .EnableInterfaceInterceptors()//启用拦截器
                 .InterceptedBy(interceptType.ToArray());


            builder.RegisterType<ExamService>().As<IExamService>().InstancePerLifetimeScope()
                 .EnableInterfaceInterceptors()//启用拦截器
                 .InterceptedBy(interceptType.ToArray());


            builder.RegisterType<PmsAssessService>().As<IPmsAssessService>().InstancePerLifetimeScope()
                 .EnableInterfaceInterceptors()//启用拦截器
                 .InterceptedBy(interceptType.ToArray());

            builder.RegisterType<PmsTagService>().As<IPmsTagService>().InstancePerLifetimeScope()
           .EnableInterfaceInterceptors()//启用拦截器
           .InterceptedBy(interceptType.ToArray());

            builder.RegisterType<DmisService>().As<IDmisService>().InstancePerLifetimeScope()
                 .EnableInterfaceInterceptors()//启用拦截器
                 .InterceptedBy(interceptType.ToArray());


            builder.RegisterType<EguardControlService>().As<IEguardControlService>().InstancePerLifetimeScope()
                .EnableInterfaceInterceptors()
                .InterceptedBy(interceptType.ToArray());

            builder.RegisterType<ExternalService>().As<IExternalService>().InstancePerLifetimeScope()
                .EnableInterfaceInterceptors()
                .InterceptedBy(interceptType.ToArray());
        }
    }
}
