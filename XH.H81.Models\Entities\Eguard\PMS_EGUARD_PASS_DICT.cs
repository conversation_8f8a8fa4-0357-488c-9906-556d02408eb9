﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace XH.H81.Models.Entities.Eguard
{
    /// <summary>
    /// 门禁时间字典表
    /// </summary>
    [DBOwner("XH_OA")]
    public class PMS_EGUARD_PASS_DICT
    {
        /// <summary>
        /// 门禁时间ID
        /// </summary>
        [Key]
        [SugarColumn(IsPrimaryKey = true)]
        public string EGUARD_PASS_ID { get; set; }
        /// <summary>
        /// 机构ID
        /// </summary>
        public string HOSPITAL_ID { get; set; }
        /// <summary>
        /// 计划名称
        /// </summary>
        public string EGUARD_PASS_NAME { get; set; }
        /// <summary>
        /// 通过时间JSON
        /// </summary>
        public string? PASS_TIME_JSON { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public string? EGUARD_PASS_STATE { get; set; }
        public string? FIRST_RPERSON { get; set; }
        public DateTime? FIRST_RTIME { get; set; }
        public string? LAST_MPERSON { get; set; }
        public DateTime? LAST_MTIME { get; set; }
        public string? REMARK { get; set; }
    }
}
