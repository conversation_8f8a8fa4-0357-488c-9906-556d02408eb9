﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Dtos.Dmis
{
    public class DocumentSystemMenu
    {
        [JsonProperty("FIRSTMENU_ID")]
        public string? FirstmenuId { get; set; }

        [JsonProperty("FIRSTMENU_NAME")]
        public string? FirstmenuName { get; set; }

        [JsonProperty("DmisMneu")]
        public List<DmisMneu> DmisMneu { get; set; } = new List<DmisMneu>();

        public TreeData ToTreeData()
        {
            var result = new TreeData()
            {
                title = FirstmenuName,
                key = FirstmenuId,
                value = FirstmenuId,
            };

            foreach (var dmisMneuItem in DmisMneu)
            {
                var treeData = new TreeData()
                {
                    title = dmisMneuItem.SecondmenuName,
                    key = dmisMneuItem.SecondmenuId,
                    value = dmisMneuItem.SecondmenuId,
                };
                if (dmisMneuItem.SDmisMneu is not null)
                {
                    foreach (var sDmisMneuItem in dmisMneuItem.SDmisMneu)
                    {
                        treeData.children.Add(new TreeData()
                        {
                            title = sDmisMneuItem.SonmenuName,
                            key = sDmisMneuItem.SonmenuId,
                            value = sDmisMneuItem.SonmenuId,
                        });
                    }
                }
                result.children.Add(treeData);
            }
            return result;
        }
    }



    public class DmisMneu
    {

        [JsonProperty("SECONDMENU_ID")]
        public string? SecondmenuId { get; set; }

        [JsonProperty("SECONDMENU_NAME")]
        public string? SecondmenuName { get; set; }

        [JsonProperty("REFRESH_TYPE")]
        public string? RefreshType { get; set; }

        [JsonProperty("REFRESH_TYPE_NAME")]
        public string? RefreshTypeName { get; set; }

        [JsonProperty("TOP_INFO_CLASS")]
        public string? TopInfoClass { get; set; }

        [JsonProperty("TOP_INFO_CLASS_NAME")]
        public string? TopInfoClassName { get; set; }

        [JsonProperty("SDmisMneu")]
        public List<SDmisMneu> SDmisMneu { get; set; } = new();
    }


    public partial class SDmisMneu
    {
        [JsonProperty("SONMENU_ID")]
        public string? SonmenuId { get; set; }

        [JsonProperty("SONMENU_NAME")]
        public string? SonmenuName { get; set; }

        [JsonProperty("REFRESH_TYPE")]
        public string? RefreshType { get; set; }
    }

    public class TreeData
    {

        public string title { get; set; }
        public string value { get; set; }
        public string key { get; set; }
        public List<TreeData> children { get; set; } = new();
    }
  
}
