﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.ExternalEntity
{
    public class PageSettingTable
    {
        /// <summary>
        /// 
        /// </summary>
        public string theme { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool hideZebraStripe { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool pagination { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string paginationPageSize { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool autoFitWith { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool showRowNumberCol { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool hideStatusBar { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool editable { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool singleClickEdit { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string title { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string rowSelection { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool showSelectionCol { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool suppressRowClickSelection { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string rowHeight { get; set; }
    }

}
