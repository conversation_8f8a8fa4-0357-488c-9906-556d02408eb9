﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Dtos.Tag
{
    public class PmsPersonTagDictDto
    {
        /// <summary>
        /// 主键;PK
        /// </summary>
        public string? PERSON_TAG_ID { get; set; }


        public string? PERSON_ID { get; set; }

        /// <summary>
        /// 机构id
        /// </summary>
        public string? HOSPITAL_ID { get; set; }

        /// <summary>
        /// 标签分类;固定基础数据
        /// </summary>
        public string TAG_CLASS { get; set; }

        /// <summary>
        /// 标签分类名称
        /// </summary>
        public string? TAG_CLASS_NAME { get; set; }

        /// <summary>
        /// 档案记录
        /// </summary>
        public string? TAG_RECORD_NAME { get; set; }
        /// <summary>
        /// 来源类型;LAB-科室 PRGOUP-检验专业组
        /// </summary>
        public string? SOURCE_TYPE { get; set; }

        /// <summary>
        /// 来源ID
        /// </summary>
        public string? SOURCE_ID { get; set; }

        /// <summary>
        /// 标签名称
        /// </summary>
        public string TAG_NAME { get; set; }

        /// <summary>
        /// 标签简称
        /// </summary>
        public string TAG_SNAME { get; set; }

        /// <summary>
        /// 标签颜色
        /// </summary>
        public string? TAG_COLOR { get; set; }

        /// <summary>
        /// 标签图标
        /// </summary>
        public string? TAG_ICON { get; set; }

        /// <summary>
        /// 标签标志;0禁用1在用2删除  默认1
        /// </summary>
        public string? TAG_STATE { get; set; }

        /// <summary>
        /// 上级标签ID
        /// </summary>
        public string? PARENT_ID { get; set; }

        /// <summary>
        /// 排序号
        /// </summary>
        public string? TAG_SORT { get; set; }

        /// <summary>
        /// 首次登记人
        /// </summary>
        public string? FIRST_RPERSON { get; set; }

        /// <summary>
        /// 首次登记时间
        /// </summary>
        public DateTime? FIRST_RTIME { get; set; }

        /// <summary>
        /// 最后修改人员
        /// </summary>
        public string? LAST_MPERSON { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime? LAST_MTIME { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? REMARK { get; set; }

        /// <summary>
        /// 是否缺省上级标签内容
        /// </summary>
        public bool? ENABLE_PARENT { get; set; }

        /// <summary>
        /// 人员标签分类：1-实验室管理(ISO15189)；2-POCT；3-生物安全；4-高等级
        /// </summary>
        public string? STANDART_ID { get; set; }
       
    }
}
