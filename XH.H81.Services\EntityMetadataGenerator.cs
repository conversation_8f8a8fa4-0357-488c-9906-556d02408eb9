﻿using H.Utility;
using Newtonsoft.Json;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace XH.H81.Services
{
    public class EntityMetadataGenerator
    {
        public static List<PropertyInfoModel> GenerateMetadataJson<T>()
        {
            var properties = typeof(T).GetProperties()
                .Select(p => new PropertyInfoModel
                {
                    PropertyName = p.Name,
                    PropertyType = GetPropertyTypeName(p.PropertyType),
                    Description = GetDescription(p),
                    IsPrimaryKey = IsPrimaryKey(p),
                    IsIgnore = IsIgnore(p),
                    DbType = GetDbType(p)
                })
                .ToList();
            properties = properties.FindAll(w => !w.IsIgnore && w.Description.IsNotNullOrEmpty());
            return properties;  
            //return System.Text.Json.JsonSerializer.Serialize(properties, new JsonSerializerOptions
            //{
            //    WriteIndented = true,
            //    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            //});
        }

        private static string GetPropertyTypeName(Type type)
        {
            if (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(Nullable<>))
            {
                return $"{type.GetGenericArguments()[0].Name}?";
            }
            return type.Name;
        }

        private static string GetDescription(PropertyInfo property)
        {
            var descriptionAttr = property.GetCustomAttribute<System.ComponentModel.DescriptionAttribute>();
            if (descriptionAttr != null)
            {
                return descriptionAttr.Description;
            }

            // 如果没有Description特性，尝试获取注释
            var summaryAttr = property.GetCustomAttributesData()
                .FirstOrDefault(a => a.AttributeType.Name == "SugarColumn");

            if (summaryAttr != null && summaryAttr.NamedArguments != null)
            {
                var descArg = summaryAttr.NamedArguments.FirstOrDefault(a => a.MemberName == "ColumnDescription");
                if (descArg.TypedValue.Value != null)
                {
                    return descArg.TypedValue.Value.ToString();
                }
            }

            return string.Empty;
        }

        private static bool IsPrimaryKey(PropertyInfo property)
        {
            var sugarColumn = property.GetCustomAttribute<SugarColumn>();
            return sugarColumn?.IsPrimaryKey == true;
        }

        private static bool IsIgnore(PropertyInfo property)
        {
            var sugarColumn = property.GetCustomAttribute<SugarColumn>();
            return sugarColumn?.IsIgnore == true;
        }

        private static string GetDbType(PropertyInfo property)
        {
            var sugarColumn = property.GetCustomAttribute<SugarColumn>();
            if (sugarColumn?.SqlParameterDbType != null)
            {
                string dateType = "1";
                switch (sugarColumn.SqlParameterDbType.ToString())
                {
                    case "AnsiString":
                        dateType = "1";
                        break;
                    case "Date":
                        dateType = "3";
                        break;
                    case "Int32":
                        dateType = "2";
                        break;
                }
                return dateType;
            }
            return "未指定";
        }
    }


    public class PropertyInfoModel
    {
        public string PropertyName { get; set; }
        public string PropertyType { get; set; }
        public string Description { get; set; }
        public bool IsPrimaryKey { get; set; }
        public bool IsIgnore { get; set; }
        public string DbType { get; set; }
    }
}
