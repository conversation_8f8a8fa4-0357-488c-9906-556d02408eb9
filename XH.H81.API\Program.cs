#region (c) 2022 杏和软件. All rights reserved.

// @Author: ch<PERSON><PERSON><PERSON><PERSON>
// @Create: 2022-11-03 11:18
// @LastModified: 2022-11-03 15:50
// @Des:程序入口配置文件 [2023-03-28]为方便后续更新  所有初始化信息集成至Xinghe.Utility库中,
////***********起步框架使用文档:https://kdocs.cn/l/cbQKvmV5YAnX***********
#endregion

using AspNetCoreRateLimit;
using H.BASE;
using H.BASE.CommScheduleJobs;
using H.BASE.Infrastructure;
using H.BASE.SqlSugarInfra;
using H.Utility.Middleware;
using Microsoft.Extensions.FileProviders;
using Serilog;
using Swashbuckle.AspNetCore.SwaggerUI;
using System.Reflection;
using XH.H81.API.Middleware;
using XH.H81.API.ScheduleJobs;
using XH.H81.IServices;
using XH.H81.Models;
using XH.H81.Models.SugarDbContext;
using XH.H81.Services;
using XH.LAB.UTILS.Middleware;
using SerilogHelper = H.BASE.Infrastructure.SerilogHelper;

//系统配置文件定义
var _configuration = new ConfigurationBuilder()
    .SetBasePath(AppContext.BaseDirectory)
    .AddJsonFile("configs/appsettings.json", false, true)
    .Build();
//启用全程序集扫描
AppSettingsProvider.ContextScanAll = true;
//初始化
var appBuiler = new AppInit(_configuration)
    .Init(null, args)
    .RegisterELK()
    .RegisterAuthentication()
    .RegisterCacheProvider()
    .RegisterIpRateLimit()
    //.RegisterDBContext<DBContext_Master, DBContext_Slave, DBContext_Master2, DBContext_Slave2>()
    .RegisterSqlSugarDBContext<SugarDbContext_Master, SugarDbContext_Slave, SugarDbContext_Master2, SugarDbContext_Slave2>(EnumSqlSugarModel.ScopeModel)
    .ConfigWebHost()
    .ConfigControllers()
    .ConfigureContainer(new AutofacModuleRegister())
    .ConfigureAutoMapper(typeof(MappingProfile))
    .builder;

//swagger个性东西比较多,放出来自己控制
appBuiler.Services.AddSwaggerSetup();

#region 自定义定时任务

/////////////////////////Redis消息队列定时任务
var computerName = System.Net.Dns.GetHostName();
var consumerName = AppSettingsProvider.CurrModuleId + "_" + computerName;
//本系统需要监听的缓存在本系统中的存放方式 [DISTRIBUTED|LOCAL] 按照业务系统自行选择
var cacheType = "DISTRIBUTED";

//消费者名称,每个服务实例指定一个ID,用于记录消息被哪个服务实例消费,无其他用途 
AppSettingsProvider.consumerName = consumerName;//模块ID+机器名称
//如果本应用缓存使用分布式缓存,则所有的服务实例可以设置为同一个,通常为模块ID
//如果本应用缓存存在实例本地,如memeory chche,本地文件,静态变量等 中,则需要为每个服务实例指定不同的消费组.以便各自消费各自注册的消息的队列
//分布式=使用模块id作为消费组ID  单点缓存=直接使用消费者ID作为消费者ID
AppSettingsProvider.consumerGroupName = cacheType == "DISTRIBUTED" ? AppSettingsProvider.CurrModuleId : consumerName;
//本系统需要监听消息队列key数组,如果没有,请务必留空以免占用不必要的资源,key值由公司统一约定,此处必须保证填写正确
//!!!todo:注意,此处注册的两个队列仅作演示,生产过程中请务必按照实际需求注册和清理 消息队列名称由公司统一约定,你参阅文档()(不会用)
//AppSettingsProvider.listenStreamKeys = new string[] { "XH:BASE:STREAM:LIS_CHARGE_ITEM", "XH:BASE:STREAM:LIS_TEST_ITEM" };
//appBuiler.Services.AddHostedService<RedisStreamListener>();
///////////////////////////
//定时清理本地日志为典型的定时任务场景,放出来方便大家参考
appBuiler.Services.AddHostedService<AutoClearLogFile>();
//appBuiler.Services.AddHostedService<AutoSyncPersonData>();
//appBuiler.Services.AddHostedService<AutoSyncBaseData>();
appBuiler.Services.AddHostedService<HandleEplanSchedule>();
appBuiler.Services.AddHostedService<TableStructDetectAndInitDataService>();
//更多定时任务在此处添加
//..
#endregion

var app = appBuiler.Build();

#region 管道和中间件配置
var OpenSwagger = _configuration["OpenSwagger"] ?? "0";
if (app.Environment.IsDevelopment() || OpenSwagger == "1")
{
    app.UseSwagger();
    app.UseSwaggerUI(options =>
    {
        //options.SwaggerEndpoint("/swagger/XH.H81.API/swagger.json", "XH.H81.API");
        options.SwaggerEndpoint("/swagger/DEMO/swagger.json", "DEMO");
        //options.SwaggerEndpoint("/swagger/ModuleLabGroup/swagger.json", "ModuleLabGroup");
        //options.SwaggerEndpoint("/swagger/Pms/swagger.json", "Pms");
        //options.SwaggerEndpoint("/swagger/User/swagger.json", "User");
        //options.SwaggerEndpoint("/swagger/EvaluatePlan/swagger.json", "EvaluatePlan");
        options.SwaggerEndpoint("/swagger/EguardControl/swagger.json", "EguardControl");
        options.SwaggerEndpoint("/swagger/人员账号相关/swagger.json", "人员账号相关");
        options.SwaggerEndpoint("/swagger/组织架构相关/swagger.json", "组织架构相关");
        options.SwaggerEndpoint("/swagger/人事档案相关/swagger.json", "人事档案相关");
        options.SwaggerEndpoint("/swagger/人员规评相关/swagger.json", "人员规评相关");
        options.SwaggerEndpoint("/swagger/对外接口/swagger.json", "对外接口");
        options.RoutePrefix = string.Empty;
        options.DocExpansion(DocExpansion.None); //->修改界面打开时自动折叠
        options.IndexStream = (Func<Stream>)(() => Assembly.GetExecutingAssembly()
            .GetManifestResourceStream("XH.H81.API.swagger.html") ?? throw new InvalidOperationException());
    });
}
app.UseDirectoryBrowser();

app.UseDirectoryBrowser(new DirectoryBrowserOptions
{
    FileProvider = new PhysicalFileProvider(Path.Combine(Directory.GetCurrentDirectory(), "ExampleFile")),
    RequestPath = "/ExampleFile"
});
app.UseStaticFiles();


app.UseStaticFiles(new StaticFileOptions
{
    FileProvider = new PhysicalFileProvider(Path.Combine(Directory.GetCurrentDirectory(), "ExampleFile")),
    OnPrepareResponse = (c) =>
    {
        c.Context.Response.Headers.Add("Access-Control-Allow-Origin", "*");
    },
    RequestPath = "/ExampleFile"
});

//返回头标记xh_trace_id 以便在elk中查询整个访问链路
app.UseMiddleware<TraceResposeHeadMiddleware>();

app.UseSerilogRequestLogging(options =>
{
    options.EnrichDiagnosticContext = SerilogHelper.EnrichFromRequest;
});

var disableRateLimit = _configuration["DisableRateLimit"] ?? "1";
if (disableRateLimit != "1")
{
    app.UseIpRateLimiting();
}

//全局异常中间件
app.UseMiddleware<GlobalExceptionMiddleware>();
//关键字过滤中间件
//app.UseMiddleware<SQLInjectMiddleware>();
//http自动引导到https
//app.UseHttpsRedirection();
//资源代理
app.UseMiddleware<CustomFileResourceMiddleware>("/H81pdf/api");
//资源代理
app.UseMiddleware<CustomFileResourceMiddleware>("/S54");

//资源代理
var routers = new List<string> { "/H81pdf/api", "/S54" };//支持同一服务下的多个路径
app.UseMiddleware<CustomMatchFileResourceMiddleware>("S54", routers);

//以下两项顺序必须正确
app.UseAuthentication(); //认证
app.UseAuthorization(); //授权中间件
app.MapControllers();
var baseDataService = app.Services.GetRequiredService<IBaseDataServices>();
if (baseDataService != null)
{
    baseDataService.ProductInit();
}
Log.Information("==>初始化完成..");

#endregion

app.Run();