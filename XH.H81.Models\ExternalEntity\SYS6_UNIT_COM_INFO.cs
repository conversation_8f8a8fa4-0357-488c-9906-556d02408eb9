using System;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using SqlSugar;
using H.Utility.SqlSugarInfra;

namespace XH.H81.Models.ExternalEntity
{
    [Table("SYS6_UNIT_COM_INFO")]
    [DBOwner("XH_SYS")]
    public class SYS6_UNIT_COM_INFO
	{
		/// <summary>
		/// 
		/// </summary>
		[Key]
        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        //[Column("UNITCOM_ID")]
		[Required(ErrorMessage = "不允许为空")]

		[StringLength(50, ErrorMessage = "UNITCOM_ID长度不能超出50字符")]
		//[Unicode(false)]
        public string UNITCOM_ID { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("LAB_ID")]
		[StringLength(20, ErrorMessage = "LAB_ID长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? LAB_ID { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("FIRST_RPERSON")]
		[StringLength(50, ErrorMessage = "FIRST_RPERSON长度不能超出50字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FIRST_RPERSON { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("UNITCOM_DESC")]
		[StringLength(500, ErrorMessage = "UNITCOM_DESC长度不能超出500字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? UNITCOM_DESC { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("LAST_MTIME")]
		//[Unicode(false)]
		public DateTime? LAST_MTIME { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("UNITCOM_STATE")]
		[StringLength(20, ErrorMessage = "UNITCOM_STATE长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? UNITCOM_STATE { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("UNITCOM_NAME")]
		[StringLength(50, ErrorMessage = "UNITCOM_NAME长度不能超出50字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? UNITCOM_NAME { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("UNITCOM_SORT")]
		[StringLength(20, ErrorMessage = "UNITCOM_SORT长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? UNITCOM_SORT { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("REMARK")]
		[StringLength(100, ErrorMessage = "REMARK长度不能超出100字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? REMARK { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("FIRST_RTIME")]
		//[Unicode(false)]
		public DateTime? FIRST_RTIME { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("LAST_MPERSON")]
		[StringLength(50, ErrorMessage = "LAST_MPERSON长度不能超出50字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? LAST_MPERSON { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("PGROUP_ID")]
		[StringLength(20, ErrorMessage = "PGROUP_ID长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? PGROUP_ID { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("HOSPITAL_ID")]
		[StringLength(20, ErrorMessage = "HOSPITAL_ID长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? HOSPITAL_ID { get; set; }


	}
}
