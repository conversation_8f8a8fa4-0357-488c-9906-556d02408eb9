﻿using H.Utility;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using XH.H81.API.Extensions;
using XH.H81.IServices;
using XH.H81.Models.Dtos.Exam;

namespace XH.H81.API.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize]
    public class ExamController : ControllerBase
    {
        private IExamService _examService;
        private readonly IBaseDataServices _IBaseDataService;
        public ExamController(IExamService examService, IBaseDataServices ibasedataService)
        {
            _examService = examService;
            _IBaseDataService = ibasedataService;
        }
        /// <summary>
        /// 获取考试分类
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetExamClassInfo()
        {
            var claim = this.User.ToClaimsDto();
            var Result = _IBaseDataService.GetExamClassInfo(claim.HOSPITAL_ID);
            return Ok(Result.ToResultDto());
        }

        /// <summary>
        /// 获取用户考试信息
        /// </summary>
        /// <param name="userNo">用户id</param>
        /// <param name="labId">科室id</param>
        /// <param name="mgroupId">管理专业组id</param>
        /// <param name="pgroupId">专业组id</param>
        /// <param name="examState">状态</param>
        /// <param name="examType">考试类型</param>
        /// <param name="examName">考试名称</param>
        /// <param name="smblFlag">生安标志</param>
        /// <param name="dateType">日期类型</param>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <param name="cerId">证书id</param>
        /// <returns></returns>
        [HttpGet]
        [CustomResponseType(typeof(List<EmpExamUserAnswerDto>))]
        public IActionResult GetUserExamInfo(string? userNo,string? labId,string? mgroupId,string? pgroupId,string? examState,string ?examType,string? examName,string? smblFlag,string ?dateType,string? startDate,string?endDate,string?cerId )
        {
            var claim = this.User.ToClaimsDto();
            var Result = _examService.GetUserExamInfo(userNo, labId, mgroupId, pgroupId,examState, examType,examName,smblFlag, dateType,startDate,endDate,cerId, claim.HOSPITAL_ID);
            return Ok(Result);
        }

        /// <summary>
        /// 保存考试信息
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult SaveExamInfo(EmpExamUserAnswerDto dto)
        {
            var claim = this.User.ToClaimsDto();
            var Result = _examService.SaveExamInfo(dto, claim.HIS_NAME, claim.HOSPITAL_ID);
            return Ok(Result);
        }

        /// <summary>
        /// 考试状态流转
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        //[AllowAnonymous]
        public IActionResult UpdateExamUserAnswerInfo(OperateExamAnswerDto operateExamAnswerDto)
        {
            var claims = this.User.ToClaimsDto();
            return Ok(_examService.UpdateExamUserAnswerInfo(operateExamAnswerDto.ExamUserIds, operateExamAnswerDto.ExamUserState, claims.HIS_NAME, claims.HOSPITAL_ID, operateExamAnswerDto.CheckPerson, operateExamAnswerDto.OperCause, operateExamAnswerDto.OperComputer, operateExamAnswerDto.Pwd, claims.LOGID));
            //return Ok(_iexammanagementservice.UpdateExamUserAnswerInfo(exam_userids, exam_user_state, "admin", "33A001", check_person, oper_cause,oper_computer,  pwd_bs,"xhsystem"));
        }


        /// <summary>
        /// 保存考试规评
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        //[AllowAnonymous]
        public IActionResult SaveExamStdScheme(ExamStdSchemeDto stdSchemeDto)
        {
            var claims = this.User.ToClaimsDto();
            return Ok(_examService.SaveExamStdScheme(stdSchemeDto, claims.HIS_NAME, claims.HOSPITAL_ID));
        }

        /// <summary>
        /// 保存考试规评明细
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        //[AllowAnonymous]
        public IActionResult SaveExamStdSchemeDetail(List<ExamStdSchemeDto> listStdSchemeDto)
        {
            var claims = this.User.ToClaimsDto();
            return Ok(_examService.SaveExamStdSchemeDetail(listStdSchemeDto, claims.HIS_NAME, claims.HOSPITAL_ID));
        }
        /// <summary>
        /// 获取考试规评列表信息
        /// </summary>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <param name="labGroupId">科室专业组id</param>
        /// <param name="eplanName">规评名称</param>
        /// <param name="examName">考试名称</param>
        /// <returns></returns>
        [HttpGet]
        [CustomResponseType(typeof(List<ExamStdSchemeDto>))]
        public IActionResult GetExamStd(string? startDate, string? endDate, string? labGroupId, string? eplanName, string? examName, string? smblFlag)
        {
            var claims = this.User.ToClaimsDto();
            return Ok(_examService.GetExamStd(startDate, endDate, labGroupId, eplanName, examName, smblFlag));
        }

        /// <summary>
        /// 获取规评明细
        /// </summary>
        /// <param name="stdGroupId">规评组合id</param>
        /// <param name="labPgroupId">科室/专业组id</param>
        /// <param name="comName">人员组合名称</param>
        /// <param name="userName">姓名</param>
        /// <returns></returns>
        [HttpGet]
        [CustomResponseType(typeof(List<ExamStdSchemeDto>))]
        public IActionResult GetExamStdDetail(string stdGroupId, string? labPgroupId, string? comName, string? userName)
        {
            var claims = this.User.ToClaimsDto();
            return Ok(_examService.GetExamStdDetail(stdGroupId, labPgroupId, comName, userName));
        }

        /// <summary>
        /// 删除考试规评明细
        /// </summary>
        /// <param name="DeleteDStdDetailDto"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult DeleteExamStdDetail(DeleteDStdDetailDto deleteDStdDetailDto)
        {
            var claims = this.User.ToClaimsDto();
            return Ok(_examService.DeleteExamStdDetail(deleteDStdDetailDto.StdGroupId, deleteDStdDetailDto.ListUserId, claims.HIS_NAME));
        }
    }
}
