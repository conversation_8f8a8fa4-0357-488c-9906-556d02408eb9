﻿using System;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using SqlSugar;
using H.Utility.SqlSugarInfra;
using AutoMapper;

/*
 * <AUTHOR> Xing<PERSON>e
 * @date : 2023-8-25
 * @desc : 人员评估记录表
 */
namespace XH.H81.Models.Entities.Pms
{

    [AutoMap(typeof(PMS_ASSESS_PLAN_PERSON), ReverseMap = true)]
    /// <summary>
    /// 人员评估记录表DTO
    /// </summary>
    public class PmsAssessPlanPersonDto
    {
        /// <summary>
        /// 计划人员评估ID
        /// </summary>
        public string? PLAN_PERSON_ID { get; set; }

 
        /// <summary>
        /// 检验专业组ID
        /// </summary>
        //[Column("PGROUP_ID")]
        public string? PGROUP_ID { get; set; }

        /// <summary>
        /// 参加考评人员ID
        /// </summary>
        //[Column("USER_ID")]
        public string? USER_ID { get; set; }


        /// <summary>
        /// 总评价
        /// </summary>
        //[Column("TOTAL_EVALUATE")]
        public string? TOTAL_EVALUATE { get; set; }


        /// <summary>
        /// 总成绩（分）
        /// </summary>
        //[Column("TOTAL_SCORE")]
        //[Unicode(false)]
        public string? TOTAL_SCORE { get; set; }

        /// <summary>
        /// 综合评价
        /// </summary>
        //[Column("COMP_EVALUATE")]
        //[Unicode(false)]
        public string? COMP_EVALUATE { get; set; }


        /// <summary>
        /// 综合考评成绩(分)
        /// </summary>
        //[Column("EVALUATE_SCORE")]

        //[Unicode(false)]
        public decimal? EVALUATE_SCORE { get; set; }

        /// <summary>
        /// 自评成绩(分)
        /// </summary>
        //[Column("SEVALUATION_SCORE")]

        //[Unicode(false)]
        public decimal? SEVALUATION_SCORE { get; set; }


        /// <summary>
        /// 考试成绩(分)
        /// </summary>
        //[Column("EXAM_SCORE")]

        //[Unicode(false)]
        public decimal? EXAM_SCORE { get; set; }

        /// <summary>
        /// 审核人员
        /// </summary>
        //[Column("CHECK_PERSON")]
        [StringLength(50, ErrorMessage = "审核人员长度不能超出50字符")]
        //[Unicode(false)]
        public string? CHECK_PERSON { get; set; }

        /// <summary>
        /// 审核时间
        /// </summary>
        //[Column("CHECK_TIME")]

        //[Unicode(false)]
        public DateTime? CHECK_TIME { get; set; }

   

        /// <summary>
        /// 评估状态；0未参加 1已参加 2已自评 3已考评（专家全部考评完成）4废止
        /// </summary>
        //[Column("EVALUATE_STATE")]
        [StringLength(10, ErrorMessage = "评估状态长度不能超出10字符")]
        //[Unicode(false)]
        public string? EVALUATE_STATE { get; set; }

        /// <summary>
        /// 评估时间
        /// </summary>
        public DateTime? EVALUATION_START_TIME { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        //[Column("REMARK")]
        [StringLength(200, ErrorMessage = "备注长度不能超出200字符")]
        //[Unicode(false)]
        public string? REMARK { get; set; }

        /// <summary>
        /// 考试分评价结果
        /// </summary>
        public string?  EXAM_EVALUATE { get; set; }


        /// <summary>
        /// 补考试开始时间
        /// </summary>
        //[Column("EXAM_START_TIME")]

        //[Unicode(false)]
        public DateTime? EXAM_START_TIME { get; set; }

        /// <summary>
        /// 数据来源类型
        /// </summary>
        public string? SOURCE_TYPE { get; set; }


        /// <summary>
        /// 规评组合ID
        /// </summary>
        public string? STD_GROUP_ID { get; set; }


        /// <summary>
        /// 规评方案ID
        /// </summary>
        public string? STD_SCHEME_ID { get; set; }



        /// <summary>
        /// 规评方案名称
        /// </summary>
        public string? EPLAN_NAME { get; set; }

        /// <summary>
        /// 评估分类名称
        /// </summary>
        public string? ASSESS_CLASS_NAME { get; set; }
        /// <summary>
        /// 规评方案适用类型
        /// </summary>
        public string? EPLAN_APPLY_TYPE { get; set; }


        /// <summary>
        /// 规评效期单位类型 (1-年, 2-月, 3-日)
        /// </summary>
        public string? SHELF_LIFE_UTYPE { get; set; }

        /// <summary>
        /// 规评效期
        /// </summary>
        public decimal? EPLAN_SHELF_LIFE { get; set; }



        /// <summary>
        /// 规评预警时长
        /// </summary>
        public decimal? WARN_DURATION { get; set; }

        /// <summary>
        /// 预警时长单位类型 (1-年, 2-月, 3-日)
        /// </summary>
        public string? WARN_UTYPE { get; set; }


        /// <summary>
        /// 评估名称
        /// </summary>
        public string? EVALUATE_NAME { get; set; }

        /// <summary>
        /// 关联培训ID
        /// </summary>
        public string? TRAIN_ID { get; set; }

        /// <summary>
        /// 关联资质证书ID
        /// </summary>
        public string? CER_ID { get; set; }


        /// <summary>
        /// 资质证书类型
        /// </summary>

        public string? CERTIFICATE_TYPE { get; set; }

        /// <summary>
        /// 资质证书类型
        /// </summary>

        public string? CERTIFICATE_DATE { get; set; }

        /// <summary>
        /// 证书单位
        /// </summary>

        public string? CERTIFICATE_UNIT { get; set; }

        /// <summary>
        /// 证书效期
        /// </summary>

        public string? CERTIFICATE_VALIDITY { get; set; }

        /// <summary>
        /// 附件
        /// </summary>
        public string? EVALUATE_AFFIX { get; set; }

        /// <summary>
        /// 生安标志
        /// </summary>
        public string? SMBL_FLAG { get; set; }

        /// <summary>
        /// 规评方案专业组id
        /// </summary>

        public string? DICT_PGROUP_ID { get; set; }

        /// <summary>
        /// 科室名称
        /// </summary>

        public string? LAB_ID { get; set; }

        /// <summary>
        /// 科室名称
        /// </summary>

        public string? LAB_NAME { get; set; }

        /// <summary>
        /// 专业组ID
        /// </summary>

        public string? PGROUP_NAME { get; set; }

        /// <summary>
        /// 附件名称
        /// </summary>
        public string? AFFIX_NAME { get; set; }


        /// <summary>
        /// 用户名称
        /// </summary>
        public string? USER_NAME { get; set; }


        /// <summary>
        /// 工号
        /// </summary>
        public string? HIS_ID { get; set; }

        /// <summary>
        /// 用户名称
        /// </summary>
        public string? PERSON_NAME { get; set; }
        /// <summary>
        /// 用户组合名称
        /// </summary>
        public string? USER_COM_NAME { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public string? STATE_NAME { get; set; }

        /// <summary>
        /// 驳回原因
        /// </summary>
        public string? REJECT_REASON { get; set; }
        /// <summary>
        /// 驳回时间
        /// </summary>
        public string? REJECT_DATE { get; set; }
        /// <summary>
        /// 驳回人员
        /// </summary>
        public string? REJECT_PERSON { get; set; }


        /// <summary>
        /// 评估分类id
        /// </summary>
        public string? EVAL_STAGE_CLASS { get; set; }


        public string? PERSON_ID { get; set; }
        /// <summary>
        /// 关联证书对象
        /// </summary>
        public PMS_SKILL_CERTIFICATE_LIST? CERTIFICATE { get; set; }
        /// <summary>
        ///附件文件
        /// </summary>
        public List<PMS_PERSON_FILE>? PMS_PERSON_FILE { get; set; }


    }
}