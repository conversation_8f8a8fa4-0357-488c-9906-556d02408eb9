﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Dtos.Exam
{
    public class OperateExamAnswerDto
    {
        /// <summary>
        /// 人员id合集多个,隔开
        /// </summary>
        public string ExamUserIds { get; set; }

        /// <summary>
        /// 8撤销 6驳回 5提交 7审核 10 删除
        /// </summary>
        public string ExamUserState { get; set; }

        /// <summary>
        /// 指定审核人
        /// </summary>
        public string? CheckPerson { get; set; }

        /// <summary>
        /// 审核/驳回原因
        /// </summary>
        public string? OperCause { get; set; }

        /// <summary>
        /// 操作电脑
        /// </summary>
        public string? OperComputer { get; set; }

        /// <summary>
        /// 密码
        /// </summary>
        public string? Pwd { get; set; }

    }
}
