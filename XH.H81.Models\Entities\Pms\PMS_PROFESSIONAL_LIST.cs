﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text;
namespace XH.H81.Models.Entities.Pms
{
    /// <summary>
    ///职称记录表
    /// </summary>
    [DBOwner("XH_OA")]
    public class PMS_PROFESSIONAL_LIST
    {
        /// <summary>
        ///记录ID
        /// </summary>
        [Key]
        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string PROFESSIONAL_ID { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? RECORD_ID { get; set; }
        /// <summary>
        ///医疗机构ID
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? HOSPITAL_ID { get; set; }
        /// <summary>
        ///人员ID
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? PERSON_ID { get; set; }
        /// <summary>
        ///人员名称
        /// </summary>
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? PERSON_NAME { get; set; }
        /// <summary>
        ///排序号
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? PROFESSIONAL_SORT { get; set; }
        /// <summary>
        ///职称专业
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? PROFESSIONAL_UNIT { get; set; }
        /// <summary>
        ///职称级别
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? PROFESSIONAL_LEVEL { get; set; }
        /// <summary>
        ///职称级别名称
        /// </summary>
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? PROFESSIONAL_LEVEL_NAME { get; set; }
        /// <summary>
        ///职称类型
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? TECHNOLOGY_TYPE { get; set; }
        /// <summary>
        ///职称类型名称
        /// </summary>
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? TECHNOLOGY_TYPE_NAME { get; set; }
        /// <summary>
        ///职称评定单位
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? EVALUATE_UNIT { get; set; }
        /// <summary>
        ///职称评定日期
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? EVALUATE_DATE { get; set; }
        /// <summary>
        ///附件
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? PROFESSIONAL_AFFIX { get; set; }
        /// <summary>
        ///附件名称
        /// </summary>
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? AFFIX_NAME { get; set; }
        /// <summary>
        ///指定审核人员
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? SELECT_CHECK_PERSON { get; set; }
        /// <summary>
        ///指定审核人员名称
        /// </summary>
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? SELECT_CHECK_PERSON_NAME { get; set; }
        /// <summary>
        ///审核时间
        /// </summary>
        public DateTime? CHECK_TIME { get; set; }
        /// <summary>
        ///审核人员
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CHECK_PERSON { get; set; }
        /// <summary>
        ///审核人员名称
        /// </summary>
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CHECK_PERSON_NAME { get; set; }
        /// <summary>
        ///首次登记人
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FIRST_RPERSON { get; set; }
        /// <summary>
        ///首次登记时间
        /// </summary>
        public DateTime? FIRST_RTIME { get; set; }
        /// <summary>
        ///最后修改人员
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? LAST_MPERSON { get; set; }
        /// <summary>
        ///最后修改时间
        /// </summary>
        public DateTime? LAST_MTIME { get; set; }
        /// <summary>
        ///备注
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? REMARK { get; set; }

        /// <summary>
        /// 字段和所属值
        /// </summary>
        public string RECORD_DATA { get; set; }
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CHECK_STATE { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CHECK_STATE_NAME { get; set; }

        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? NEW_STATE { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? NEW_STATE_NAME { get; set; }
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? PROFESSIONAL_NAME { get; set; }
        public string? PROFESSIONAL_STATE { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? PROFESSIONAL_STATE_NAME { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public List<PMS_PERSON_FILE> PMS_PERSON_FILE { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? SORT_NUM { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? SUBMIT_STATE { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? REJECT_REASON { get; set; }
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? REJECT_DATE { get; set; }

        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? REJECT_PERSON { get; set; }
        /// <summary>
        /// 职务/职称
        /// </summary>

        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? JOBORTECHNOLOGY { get; set; }

        /// <summary>
        /// 专业组名称
        /// </summary>
        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? PGROUP_NAME { get; set; }

        /// <summary>
        /// 工号
        /// </summary>

        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? HIS_ID { get; set; }
        /// <summary>
        /// 生安标志
        /// </summary>
        public string SMBL_FLAG { get; set; } = "0";
    }
}
