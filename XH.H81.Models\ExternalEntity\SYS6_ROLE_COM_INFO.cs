using System;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using SqlSugar;
using H.Utility.SqlSugarInfra;

namespace XH.H81.Models.ExternalEntity
{
    /// <summary>
    /// 权限组合字典表
    /// </summary>
    [Table("SYS6_ROLE_COM_INFO")]
    [DBOwner("XH_SYS")]
    public class SYS6_ROLE_COM_INFO
    {
        /// <summary>
        /// 
        /// </summary>
        [Key]
        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        //[Column("ROLECOM_ID")]
        [Required(ErrorMessage = "不允许为空")]

        [StringLength(50, ErrorMessage = "ROLECOM_ID长度不能超出50字符")]
        //[Unicode(false)]
        public string ROLECOM_ID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        //[Column("LAB_ID")]
        [Required(ErrorMessage = "不允许为空")]

        [StringLength(20, ErrorMessage = "LAB_ID长度不能超出20字符")]
        //[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string LAB_ID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        //[Column("FIRST_RPERSON")]
        [StringLength(50, ErrorMessage = "FIRST_RPERSON长度不能超出50字符")]
        //[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FIRST_RPERSON { get; set; }

        /// <summary>
        /// 
        /// </summary>
        //[Column("LAST_MTIME")]
        //[Unicode(false)]
        public DateTime? LAST_MTIME { get; set; }

        /// <summary>
        /// 
        /// </summary>
        //[Column("ROLECOM_STATE")]
        [StringLength(20, ErrorMessage = "ROLECOM_STATE长度不能超出20字符")]
        //[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? ROLECOM_STATE { get; set; }

        /// <summary>
        /// 
        /// </summary>
        //[Column("ROLECOM_SORT")]
        [StringLength(20, ErrorMessage = "ROLECOM_SORT长度不能超出20字符")]
        //[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? ROLECOM_SORT { get; set; }

        /// <summary>
        /// 
        /// </summary>
        //[Column("ROLECOM_NAME")]
        [StringLength(50, ErrorMessage = "ROLECOM_NAME长度不能超出50字符")]
        //[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? ROLECOM_NAME { get; set; }

        /// <summary>
        /// 
        /// </summary>
        //[Column("REMARK")]
        [StringLength(100, ErrorMessage = "REMARK长度不能超出100字符")]
        //[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? REMARK { get; set; }

        /// <summary>
        /// 
        /// </summary>
        //[Column("FIRST_RTIME")]
        //[Unicode(false)]
        public DateTime? FIRST_RTIME { get; set; }

        /// <summary>
        /// 
        /// </summary>
        //[Column("LAST_MPERSON")]
        [StringLength(50, ErrorMessage = "LAST_MPERSON长度不能超出50字符")]
        //[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? LAST_MPERSON { get; set; }

        /// <summary>
        /// 
        /// </summary>
        //[Column("ROLECOM_DESC")]
        [StringLength(500, ErrorMessage = "ROLECOM_DESC长度不能超出500字符")]
        //[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? ROLECOM_DESC { get; set; }

        /// <summary>
        /// 
        /// </summary>
        //[Column("HOSPITAL_ID")]
        [Required(ErrorMessage = "不允许为空")]

        [StringLength(20, ErrorMessage = "HOSPITAL_ID长度不能超出20字符")]
        //[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string HOSPITAL_ID { get; set; }


    }
}
