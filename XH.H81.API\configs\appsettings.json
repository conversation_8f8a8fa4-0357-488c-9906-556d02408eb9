{
  //公共基础配置服务地址
  "UrlModuleS01": "https://localhost:18801",
  //"UrlModuleS01": "https://d01.lis-china.com:18801",
  //"UrlModuleS01": "https://************:18801",//省人
  //"UrlModuleS01": "https://*************:18801",
  // "UrlModuleS01": "https://**********:18801",
  //"UrlModuleS01": "https://**************:18801", //公司平台
  //"UrlModuleS01": "https://************:18801", // 118人大金仓
  //注意确保模块ID为你当前项目的模块ID
  "ModuleId": "H81",
  //端口信息
  "Kestrel": {
    "Endpoints": {
      "Https": {
        "Url": "https://*:18481",
        "Certificate": {
          "Path": "xhdevcert.pfx",
          "Password": "7540000E"
        }
      }
    }
  },
  //是否开启Swagger 开发环境自动开启,生产环境根据此项配置开启,(按等保要求,生产环境不允许暴露接口信息),除调试需求外,此项务必保持关
  "OpenSwagger": "0",
  "AllowedHosts": "*",
  //默认启动线程数很少,高并发场景时每0.5秒增加一个线程,导致压力测试会有很高的错误率
  //为了增加高并发场景性能,建设设大 最小启动线程数(酌情)
  "MinThreads": 1000,
  //本地日志文件保留时间(天) 0=无限期不清理 默认清理15天
  "LogKeepDays": "15",
  //是否禁用拦截器缓存,仅在调试时使用
  "DisableCacheAOP": "0",
  //全局禁用角色权限验证(仅保留基本token验证),即关闭垂直越权管理
  "GlobalDisableRoleAuthorize": "1",
  //全局禁用token是否存在验证(不判断token是否在token池里是否存在)
  "GlobalDisableTokenValidate": "0",
  "DisableRateLimit": "0",
  //IP限流配置
  "IpRateLimiting": {
    "EnableEndpointRateLimiting": false,
    "StackBlockedRequests": false,
    //请注意此场景,被调方位于WAF防火墙下,调用方位于WAF外,X-Real-IP 可能指向WAF地址,需要改成X-Forward-IP
    "RealIpHeader": "X-Real-IP",
    "ClientIdHeader": "X-ClientId",
    "HttpStatusCode": 429,
    //白名单 
    //"IpWhitelist": [ "127.0.0.1", "::1/10", "***********/24" ],
    "EndpointWhitelist": [],
    "ClientWhitelist": [],
    "GeneralRules": [
      {
        "Endpoint": "*",
        "Period": "1s",
        //注意,公共服务,ip来源可能来自某单个服务器的项目,请谨慎设置限流值,或设置白名单或关闭限流
        "Limit": 20
      }
    ]
  },
  //所需软件模块,用','号分开
  "ExtraRegistrationModules": "J02,S03,S28,H57-02,S54,H57-03,H04,H04-01,H04-13,H115,H98,H84,H05,H95,H97,H91,H07-11,H07-15",
  "getMaxNumberRedisPrefix": "XHLIS:MAX",
  "publicKey": "E291A2AFF63C4D07B663FC7014ACD62B@xhlis", //公钥
  ////初始化机构ID
  //"HospitalId": "33A001",
  //是否对所有Sql语句进行标识符长度大于30的检查
  "CheckSqlFieldTooLong": "0",
  //生安入口的人员树是否清除人数为0的节点（默认清除，0表示不清除）
  "isSMBLTreeClearZeroNode": "1",
  //是否调用电信相关下发接口
  "IsCallTelecommunications": "1",
  //是否启用规评限权功能
  "IsEplanLimitRoleEnable": "2",
  //是否开启规评限权计算定时任务，0-关闭，1-开启，默认0关闭
  "IsOpenEplanLimitRoleTask": "1",
  //初始化数据时写入的机构ID, 放开本参数后重启服务，即开始[初始化数据导入]
  //"InitDataHospitalId": "33A001",
  //人岗权旧数据根据岗位自动关联管理单元，参数为模块范围，放开本参数后重启服务后自动执行
  //"AutoMatchModuleOldPostUnit": "H81,H82,H84,H98,H86,H91,H92,H97,H94", 
}
