﻿using H.Utility;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using XH.H81.IServices;

namespace XH.H81.API.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize]
    public class DmisController : ControllerBase
    {
        private IDmisService  _dmisService;
        private readonly IBaseDataServices _IBaseDataService;
        public DmisController(IDmisService dmisService)
        {
            _dmisService = dmisService;
        }
        /// <summary>
        /// 获取人员文档学习记录
        /// </summary>
        /// <param name="lab_id">科室id</param>
        /// <param name="firstmenukey">固定H9101</param>
        /// <param name="class_id">文档分类id</param>
        /// <param name="doc_type">文档类型</param>
        /// <param name="user_id">人员id</param>
        /// <param name="start_time">开始时间</param>
        /// <param name="end_time">结束时间</param>
        /// <param name="doc_name">文档名称</param>
        /// <param name="learning_state"></param>
        /// <param name="smbl_flag">生安标志</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetJobLogDocInfo(string lab_id, string firstmenukey, string? class_id, string? doc_type, string user_id, string start_time, string end_time,
            string? doc_name, string? learning_state, string? smbl_flag)
        {
            var claim = this.User.ToClaimsDto();
            string default_lab = claim.LAB_ID;
            var result = _dmisService.GetJobLogDocInfo(claim.HOSPITAL_ID, lab_id, firstmenukey, class_id, doc_type, user_id, start_time, end_time, doc_name, learning_state,
                default_lab,smbl_flag);
            return Ok(result.ToResultDto());
        }

        /// <summary>
        /// 获取文档分类下拉树
        /// </summary>
        /// <param name="lab_id"></param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetClassTypeDropDownInfo(string lab_id)
        {
            var claim = this.User.ToClaimsDto();
            string default_lab = claim.LAB_ID;
            var result = _dmisService.GetClassTypeDropDownInfo(lab_id);
            return Ok(result);
        }

        /// <summary>
        /// 获取文档学习记录(按文档)
        /// </summary>
        /// <param name="lab_id">科室id</param>
        /// <param name="firstmenukey">固定H9101</param>
        /// <param name="class_id">文档分类id</param>
        /// <param name="doc_type">文档类型</param>
        /// <param name="doc_name">文档名称</param>
        /// <param name="doc_state">状态0废止1发布</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetDocJobLogInfo(string lab_id, string firstmenukey, string? class_id, string? doc_type, string? doc_name, string? doc_state)
        {
            var claim = this.User.ToClaimsDto();
            string default_lab = claim.LAB_ID;
            var result = _dmisService.GetDocJobLogInfo(lab_id, firstmenukey, class_id, doc_type, doc_name, doc_state,"1");
            return Ok(result);
        }

        /// <summary>
        ///  获取已分学习任务人员信息(按人员)
        /// </summary>
        /// <param name="lab_id">科室id</param>
        /// <param name="pgroup_id">专业组id</param>
        /// <param name="user_name">人员名称</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetJobLogUserInfo(string lab_id, string? pgroup_id, string? user_name)
        {
            var claim = this.User.ToClaimsDto();
            var result = _dmisService.GetJobLogUserInfo(lab_id, pgroup_id, user_name, "1");
            return Ok(result);
        }


        /// <summary>
        /// /获取文档对应人员学习记录
        /// </summary>
        /// <param name="lab_id"></param>
        /// <param name="firstmenukey"></param>
        /// <param name="doc_id"></param>
        /// <param name="start_time"></param>
        /// <param name="end_time"></param>
        /// <param name="pgroup_id"></param>
        /// <param name="user_name"></param>
        /// <param name="learning_state">0未完成1已完成</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetUserJobLogInfo(string lab_id, string firstmenukey, string? doc_id, string start_time, string end_time, string? pgroup_id, string? user_name, string? learning_state)
        {
            var claim = this.User.ToClaimsDto();
            var result = _dmisService.GetUserJobLogInfo(lab_id, firstmenukey, doc_id, start_time,end_time,pgroup_id,user_name,learning_state, "1");
            return Ok(result);
        }
    }
}
