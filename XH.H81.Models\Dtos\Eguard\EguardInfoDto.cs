﻿
namespace XH.H81.Models.Dtos.Eguard
{
    /// <summary>
    /// 门禁信息
    /// </summary>
    public class EguardInfoDto
    {
        /// <summary>
        /// 门禁ID
        /// </summary>
        public string EGUARD_ID { get; set; }
        /// <summary>
        /// 门禁名称
        /// </summary>
        public string EGUARD_NAME { get; set; }
    }




    /// <summary>
    /// 获取门禁列表入参
    /// </summary>
    public class EguardInput
    {
        /// <summary>
        /// 门禁组合ID
        /// </summary>
        public string? EGUARD_COM_ID { get; set; }
        /// <summary>
        /// 设备位置ID
        /// </summary>
        public string? POSITION_ID { get; set; }
        /// <summary>
        /// 门禁名称 模糊查询
        /// </summary>
        public string? EQUIPMENT_NAME { get; set; }
        /// <summary>
        /// 岗位角色
        /// </summary>
        public List<string>? RoleIds { get; set; }
        /// <summary>
        /// 人员ID
        /// </summary>
        public List<string>? PersonIds { get; set; }

    }


    /// <summary>
    /// 授权列表返参
    /// </summary>
    public class EguardOutput
    {
        /// <summary>
        /// 唯一ID
        /// </summary>
        public string? OnlyId { get; set; }
        /// <summary>
        /// 门禁授权ID
        /// </summary>
        public string? EGUARD_AUTH_ID { get; set; }
        /// <summary>
        /// 门禁组合ID
        /// </summary>
        public string? EGUARD_COM_ID { get; set; }
        /// <summary>
        /// 门禁组合名称
        /// </summary>
        public string? EGUARD_COM_NAME { get; set; }
        /// <summary>
        /// 门禁地点ID
        /// </summary>
        public string? POSITION_ID { get; set; }
        /// <summary>
        /// 门禁地点名称
        /// </summary>
        public string? POSITION_NAME { get; set; }
        /// <summary>
        /// 门禁设备ID
        /// </summary>
        public string? EQUIPMENT_ID { get; set; }
        /// <summary>
        /// 门禁设备名称
        /// </summary>
        public string? EQUIPMENT_NAME { get; set; }
        /// <summary>
        ///  适用范围
        /// </summary>
        public string? EPLAN_APPLY_TYPE { get; set; }
        /// <summary>
        ///  授权期限类型 1.长期  2.固定时间 （）
        /// </summary>
        public string? AUTH_LIMIT_TYPE { get; set; }
        /// <summary>
        /// 类型是长期的取值 时间字典表
        /// </summary>
        public string? EGUARD_PASS_ID { get; set; }
        /// <summary>
        /// 授权时间是固定时间的 解析这个json串
        /// </summary>
        public string? EGUARD_PASS_JSON { get; set; }
        /// <summary>
        /// 门禁组合描述
        /// </summary>
        public string? EGUARD_COM_DESC { get; set; }
        /// <summary>
        /// 门禁类型
        /// </summary>
        public string? EGUARD_DATA_TYPE { get; set; }
        /// <summary>
        /// 门禁ID
        /// </summary>
        public string? EGUARD_DATA_ID { get; set; }
        /// <summary>
        /// 岗位人员类型
        /// </summary>
        public string? EGUARD_AUTH_TYPE { get; set; }
        /// <summary>
        /// 岗位/人员ID
        /// </summary>
        public string? RolePersonId { get; set; }
        /// <summary>
        /// 授权开始时间
        /// </summary>
        public DateTime? AUTH_START_DATE { get; set; }
        /// <summary>
        /// 授权结束时间
        /// </summary>
        public DateTime? AUTH_END_DATE { get; set; }
        /// <summary>
        /// 行合并数
        /// </summary>
        public int? ROWNO_MERGE_COUNT { get; set; }
        /// <summary>
        /// 组合行合并数
        /// </summary>
        public int? ROWCOM_MERGE_COUNT { get; set; }
        /// <summary>
        /// 发布状态（0 未发布；1 发布）
        /// </summary>
        public string? PUBLISH_STATE { get; set; }
    }

    /// <summary>
    /// 门禁授权入参（保存修改）
    /// </summary>
    public class EguardSaveDto
    {
        /// <summary>
        /// 门禁授权字典ID  有值是修改 无值是新增保存
        /// </summary>
        public string? EGUARD_AUTH_ID { get; set; }
        /// <summary>
        /// 门禁类型 1门禁组合 2门禁
        /// </summary>
        public string? EGUARD_DATA_TYPE { get; set; }
        /// <summary>
        /// 门禁组合ID
        /// </summary>
        public List<string>? EGUARD_COM_IDs { get; set; }
        /// <summary>
        /// 门禁设备ID
        /// </summary>
        public List<string>? EQUIPMENT_IDs { get; set; }
        /// <summary>
        /// 授权类型 1长期 2固定日期
        /// </summary>
        public string? AUTH_LIMIT_TYPE { get; set; }
        /// <summary>
        /// 门禁授权开始时间
        /// </summary>
        public DateTime? AUTH_START_DATE { get; set; }
        /// <summary>
        /// 门禁授权结束时间
        /// </summary>
        public DateTime? AUTH_END_DATE { get; set; }
        /// <summary>
        /// 门禁计划时间对应字典ID  等于-1 表示无计划
        /// </summary>
        public string? EGUARD_PASS_ID { get; set; }
        /// <summary>
        /// 无门禁时间计划  按天或者按周 保存json（json格式在门禁时间计划维护那边已经给出了）
        /// </summary>
        public string? EGUARD_PASS_JSON { get; set; }
        /// <summary>
        /// 授权类型 1岗位  2人员
        /// </summary>
        public string? EGUARD_AUTH_TYPE { get; set; }
        /// <summary>
        /// 门禁岗位ID 按照岗位设置
        /// </summary>
        public List<string>? RoleIds { get; set; }
        /// <summary>
        /// 门禁人员ID 按照人员设置
        /// </summary>
        public List<string>? PersonIds { get; set; }
        /// <summary>
        /// 适用范围
        /// </summary>
        public string? EPLAN_APPLY_TYPE { get; set; }
        public string? EGUARD_COM_DESC { get; set; }
    }
    /// <summary>
    /// 新版门禁授权设置
    /// </summary>
    public class NewEguardSaveDto
    {
        /// <summary>
        /// 修改还是新增 0、新增；1、修改
        /// </summary>
        public string? IF_UPDATE { get; set; }
        /// <summary>
        /// 门禁类型 1门禁组合 2门禁
        /// </summary>
        public string EGUARD_DATA_TYPE { get; set; }
        /// <summary>
        /// 门禁组合ID
        /// </summary>
        public List<string>? EGUARD_COM_IDs { get; set; }
        /// <summary>
        /// 门禁设备ID
        /// </summary>
        public List<string>? EQUIPMENT_IDs { get; set; }
        /// <summary>
        /// 授权类型 1岗位  2人员
        /// </summary>
        public string? EGUARD_AUTH_TYPE { get; set; }
        /// <summary>
        /// 门禁岗位ID 按照岗位设置
        /// </summary>
        public List<string>? RoleIds { get; set; }
        /// <summary>
        /// 门禁人员ID 按照人员设置
        /// </summary>
        public List<string>? PersonIds { get; set; }
        /// <summary>
        /// 适用范围
        /// </summary>
        public string? EPLAN_APPLY_TYPE { get; set; }
        public List<NewEguardSaveItem> EguardSaveItem { get; set; } = new List<NewEguardSaveItem>();
    }
    public class NewEguardSaveItem
    {
        /// <summary>
        /// 门禁授权字典ID  有值是修改 无值是新增保存
        /// </summary>
        public string? EGUARD_AUTH_ID { get; set; }
        /// <summary>
        /// 授权类型 1长期 2固定日期
        /// </summary>
        public string? AUTH_LIMIT_TYPE { get; set; }
        /// <summary>
        /// 门禁授权开始时间
        /// </summary>
        public DateTime? AUTH_START_DATE { get; set; }
        /// <summary>
        /// 门禁授权结束时间
        /// </summary>
        public DateTime? AUTH_END_DATE { get; set; }
        /// <summary>
        /// 门禁计划时间对应字典ID  等于-1 表示无计划
        /// </summary>
        public string? EGUARD_PASS_ID { get; set; }
        /// <summary>
        /// 无门禁时间计划  按天或者按周 保存json（json格式在门禁时间计划维护那边已经给出了）
        /// </summary>
        public string? EGUARD_PASS_JSON { get; set; }
        public string? ID { get; set; }
    }
    public class UpdateEguardAuth
    {
        /// <summary>
        /// 门禁组合ID/门禁id
        /// </summary>
        public string EGUARD_COM_ID { get; set; }
        /// <summary>
        /// 1 门禁组合 2 门禁
        /// </summary>
        public string EGUARD_COM_TYPE { get; set; }
        /// <summary>
        /// 授权数据ID(左侧树选择的id)
        /// </summary>
        public List<string>? EGUARD_DATA_ID { get;set; }
        /// <summary>
        /// 授权类型 1岗位  2人员
        /// </summary>
        public string EGUARD_AUTH_TYPE { get; set; }
        /// <summary>
        /// 适用范围
        /// </summary>
        public string EPLAN_APPLY_TYPE { get; set; }
    }
}
