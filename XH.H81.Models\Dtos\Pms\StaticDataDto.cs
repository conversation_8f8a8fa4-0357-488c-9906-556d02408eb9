﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Dtos.Pms
{
    public class GroupListItem
    {
        /// <summary>
        /// 朝晖生化组
        /// </summary>
        public string name { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string value { get; set; }
    }

    public class StaticDataItem
    {
        /// <summary>
        /// 
        /// </summary>
        public string name { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string value { get; set; }
    }

    public class StaticTypeItem
    {
        /// <summary>
        /// 论著类型
        /// </summary>
        public string staticName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public List<StaticDataItem> staticData { get; set; }
    }

    public class StaticDataDto
    {
        /// <summary>
        /// 
        /// </summary>
        public string total { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public List<GroupListItem> groupList { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public List<StaticTypeItem> staticType { get; set; }
    }
}
