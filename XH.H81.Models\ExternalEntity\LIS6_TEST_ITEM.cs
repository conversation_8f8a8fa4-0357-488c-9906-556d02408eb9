using System;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using SqlSugar;
using H.Utility.SqlSugarInfra;

namespace XH.H81.Models.ExternalEntity
{
    [Table("LIS6_TEST_ITEM")]
    [DBOwner("XH_SYS")]
    public class LIS6_TEST_ITEM
	{
		/// <summary>
		/// 
		/// </summary>
		[Key]
        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        //[Column("TEST_ITEM_ID")]
		[Required(ErrorMessage = "不允许为空")]

		[StringLength(10, ErrorMessage = "TEST_ITEM_ID长度不能超出10字符")]
		//[Unicode(false)]
		public string TEST_ITEM_ID { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("LAB_ID")]
		[Required(ErrorMessage = "不允许为空")]

		[StringLength(20, ErrorMessage = "LAB_ID长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string LAB_ID { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("IF_NUMBER")]
		[StringLength(10, ErrorMessage = "IF_NUMBER长度不能超出10字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? IF_NUMBER { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("TEST_ITEM_PRECISION")]
		[StringLength(2, ErrorMessage = "TEST_ITEM_PRECISION长度不能超出2字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? TEST_ITEM_PRECISION { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("FIRST_RPERSON")]
		[StringLength(50, ErrorMessage = "FIRST_RPERSON长度不能超出50字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FIRST_RPERSON { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("ITEM_SD_PRECISION")]
		[StringLength(2, ErrorMessage = "ITEM_SD_PRECISION长度不能超出2字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? ITEM_SD_PRECISION { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("TEST_ITEM_CODE")]
		[StringLength(10, ErrorMessage = "TEST_ITEM_CODE长度不能超出10字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? TEST_ITEM_CODE { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("SNOWFLAKES_ID")]
		[StringLength(50, ErrorMessage = "SNOWFLAKES_ID长度不能超出50字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? SNOWFLAKES_ID { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("LAST_MTIME")]
		//[Unicode(false)]
		public DateTime? LAST_MTIME { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("REPOSITORY_FILE")]
		[StringLength(255, ErrorMessage = "REPOSITORY_FILE长度不能超出255字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? REPOSITORY_FILE { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("TEST_METHOD")]
		[StringLength(100, ErrorMessage = "TEST_METHOD长度不能超出100字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? TEST_METHOD { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("ENGLISH_SHORT_NAME")]
		[StringLength(50, ErrorMessage = "ENGLISH_SHORT_NAME长度不能超出50字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? ENGLISH_SHORT_NAME { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("IF_CNAS")]
		[StringLength(20, ErrorMessage = "IF_CNAS长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? IF_CNAS { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("EXPRESSIONS_ITEM")]
		[StringLength(100, ErrorMessage = "EXPRESSIONS_ITEM长度不能超出100字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? EXPRESSIONS_ITEM { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("STANDARD_ID")]
		[StringLength(30, ErrorMessage = "STANDARD_ID长度不能超出30字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? STANDARD_ID { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("INSTANCE_ID")]
		[Required(ErrorMessage = "不允许为空")]

		[StringLength(20, ErrorMessage = "INSTANCE_ID长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string INSTANCE_ID { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("TEST_ITEM_TYPE")]
		[StringLength(20, ErrorMessage = "TEST_ITEM_TYPE长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? TEST_ITEM_TYPE { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("TEST_ITEM_SORT")]
		[StringLength(20, ErrorMessage = "TEST_ITEM_SORT长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? TEST_ITEM_SORT { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("TEST_ITEM_JTYPE")]
		[StringLength(20, ErrorMessage = "TEST_ITEM_JTYPE长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? TEST_ITEM_JTYPE { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("TEST_ITEM_UNIT")]
		[StringLength(20, ErrorMessage = "TEST_ITEM_UNIT长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? TEST_ITEM_UNIT { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("COMMENLY_USED")]
		[StringLength(20, ErrorMessage = "COMMENLY_USED长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? COMMENLY_USED { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("CHINESE_NAME")]
		[Required(ErrorMessage = "不允许为空")]

		[StringLength(100, ErrorMessage = "CHINESE_NAME长度不能超出100字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string CHINESE_NAME { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("CHINESE_SHORT_NAME")]
		[Required(ErrorMessage = "不允许为空")]

		[StringLength(50, ErrorMessage = "CHINESE_SHORT_NAME长度不能超出50字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string CHINESE_SHORT_NAME { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("HR_SIGN")]
		[StringLength(20, ErrorMessage = "HR_SIGN长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? HR_SIGN { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("OBTAIN_SAME_ITEM")]
		[StringLength(20, ErrorMessage = "OBTAIN_SAME_ITEM长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? OBTAIN_SAME_ITEM { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("CNAS_HR")]
		[StringLength(20, ErrorMessage = "CNAS_HR长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CNAS_HR { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("STATE_FLAG")]
		[StringLength(2, ErrorMessage = "STATE_FLAG长度不能超出2字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? STATE_FLAG { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("REMARK")]
		[StringLength(200, ErrorMessage = "REMARK长度不能超出200字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? REMARK { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("REVIEW_DAY")]
		//[Unicode(false)]
		public decimal? REVIEW_DAY { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("EXPRESSIONS", TypeName = "CLOB")]
		[StringLength(4000, ErrorMessage = "EXPRESSIONS长度不能超出4000字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? EXPRESSIONS { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("FIRST_RTIME")]
		//[Unicode(false)]
		public DateTime? FIRST_RTIME { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("LAST_MPERSON")]
		[StringLength(50, ErrorMessage = "LAST_MPERSON长度不能超出50字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? LAST_MPERSON { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("ENGLISH_NAME")]
		[StringLength(100, ErrorMessage = "ENGLISH_NAME长度不能超出100字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? ENGLISH_NAME { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("IF_VERIFY")]
		[StringLength(10, ErrorMessage = "IF_VERIFY长度不能超出10字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? IF_VERIFY { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("SPELL_CODE")]
		[StringLength(20, ErrorMessage = "SPELL_CODE长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? SPELL_CODE { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("REGULATION_FILE")]
		[StringLength(100, ErrorMessage = "REGULATION_FILE长度不能超出100字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? REGULATION_FILE { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("TEST_ITEM_CLASS")]
		[StringLength(50, ErrorMessage = "TEST_ITEM_CLASS长度不能超出50字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? TEST_ITEM_CLASS { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("EXPRESSIONS_JS", TypeName = "CLOB")]
		[StringLength(4000, ErrorMessage = "EXPRESSIONS_JS长度不能超出4000字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? EXPRESSIONS_JS { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("ITEM_DES")]
		[StringLength(500, ErrorMessage = "ITEM_DES长度不能超出500字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? ITEM_DES { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("HOSPITAL_ID")]
		[StringLength(20, ErrorMessage = "HOSPITAL_ID长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? HOSPITAL_ID { get; set; }


	}
}
