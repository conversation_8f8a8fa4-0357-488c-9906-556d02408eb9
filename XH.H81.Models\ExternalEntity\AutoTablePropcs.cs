﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.ExternalEntity
{
    /// <summary>
    /// 
    /// </summary>
    public class ClassPropSetting
    {
        public List<AutoClassProp> Setting { get; set; }
    }
    public class AutoClassProp
    {
        public string ClassName { get; set; }

        public string ClassCode { get; set; }

        public List<Properties> Prop { get; set; }
    }

    public class Properties
    { 
        public string Key { get; set; }

        public string Name { get; set; }

        public int IsShow { get; set; }

        public int? Width { get; set; }

    }
}
