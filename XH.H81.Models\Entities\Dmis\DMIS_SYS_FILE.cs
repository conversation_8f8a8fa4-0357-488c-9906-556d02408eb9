using System;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using SqlSugar;
using H.Utility.SqlSugarInfra;

namespace XH.H81.Models.Entity.Dmis
{
    [DBOwner("XH_OA")]
    public class DMIS_SYS_FILE
    {
        /// <summary>
        /// 文件ID
        /// </summary>
        [Key]
        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        [Column("FILE_ID")]
        [Required(ErrorMessage = "文件ID不允许为空")]

        [StringLength(50, ErrorMessage = "文件ID长度不能超出50字符")]
        [Unicode(false)]
        public string FILE_ID { get; set; }

        /// <summary>
        /// 文件类型
        /// </summary>
        [Column("FILE_TYPE")]
        [StringLength(20, ErrorMessage = "文件类型长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FILE_TYPE { get; set; }

        /// <summary>
        /// 首次登记人
        /// </summary>
        [Column("FIRST_RPERSON")]
        [StringLength(50, ErrorMessage = "首次登记人长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FIRST_RPERSON { get; set; }

        /// <summary>
        /// 修订状态
        /// </summary>
        [Column("REVISION_STATE")]
        [StringLength(20, ErrorMessage = "修订状态长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? REVISION_STATE { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        [Column("LAST_MTIME")]
        [Unicode(false)]
        public DateTime? LAST_MTIME { get; set; }

        /// <summary>
        ///修订次数
        /// </summary>
        [Column("REVISION_NUM")]
        [Unicode(false)]
        public decimal? REVISION_NUM { get; set; }

        /// <summary>
        /// 文件别名
        /// </summary>
        [Column("FILE_CNAME")]
        [StringLength(100, ErrorMessage = "文件别名长度不能超出100字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FILE_CNAME { get; set; }

        /// <summary>
        /// 文件编号
        /// </summary>
        [Column("FILE_NUM")]
        [StringLength(50, ErrorMessage = "文件编号长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FILE_NUM { get; set; }

        /// <summary>
        /// 修订时间
        /// </summary>
        [Column("REVISION_TIME")]
        [Unicode(false)]
        public DateTime? REVISION_TIME { get; set; }

        /// <summary>
        /// 文档ID
        /// </summary>
        [Column("DOC_ID")]
        [StringLength(20, ErrorMessage = "文档ID长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? DOC_ID { get; set; }

        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FORM_VER_ID { get; set; }

        /// <summary>
        /// 文件状态 0未处理1已处理 2废止
        /// </summary>
        [Column("FILE_STATE")]
        [StringLength(20, ErrorMessage = "文件状态 0未处理1已处理 2废止长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FILE_STATE { get; set; }

        /// <summary>
        /// 分类ID
        /// </summary>
        [Column("CLASS_ID")]
        [StringLength(20, ErrorMessage = "分类ID长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CLASS_ID { get; set; }


        /// <summary>
        /// 备注
        /// </summary>
        [Column("REMARK")]
        [StringLength(200, ErrorMessage = "备注长度不能超出200字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? REMARK { get; set; }

        /// <summary>
        /// 文件后缀
        /// </summary>
        [Column("FILE_SUFFIX")]
        [StringLength(20, ErrorMessage = "文件后缀长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FILE_SUFFIX { get; set; }

        /// <summary>
        /// 修订人员
        /// </summary>
        [Column("REVISION_PERSON")]
        [StringLength(50, ErrorMessage = "修订人员长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? REVISION_PERSON { get; set; }

        /// <summary>
        /// 首次登记时间
        /// </summary>
        [Column("FIRST_RTIME")]
        [Unicode(false)]
        public DateTime? FIRST_RTIME { get; set; }

        /// <summary>
        /// 最后修改人员
        /// </summary>
        [Column("LAST_MPERSON")]
        [StringLength(50, ErrorMessage = "最后修改人员长度不能超出50字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? LAST_MPERSON { get; set; }

        /// <summary>
        /// 文件路径
        /// </summary>
        [Column("FILE_PATH")]
        [StringLength(200, ErrorMessage = "文件路径长度不能超出200字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FILE_PATH { get; set; }

        /// <summary>
        /// 文件名称
        /// </summary>
        [Column("FILE_NAME")]
        [StringLength(200, ErrorMessage = "文件名称长度不能超出200字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FILE_NAME { get; set; }

        /// <summary>
        /// 文件排序号
        /// </summary>
        [Column("FILE_SORT")]
        [StringLength(20, ErrorMessage = "文件排序号长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FILE_SORT { get; set; }

        /// <summary>
        /// 医疗机构ID
        /// </summary>
        [Column("HOSPITAL_ID")]
        [StringLength(20, ErrorMessage = "医疗机构ID长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? HOSPITAL_ID { get; set; }

        /// <summary>
        ///文件来源1普通  2纸质,默认1
        /// </summary>
        [Column("FILE_ORIGIN")]
        [StringLength(10, ErrorMessage = "文件来源长度不能超出10字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FILE_ORIGIN { get; set; }

        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FILE_STATE_NAME { get; set; }


        [SugarColumn(IsIgnore = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FILE_ORIGIN_NAME { get; set; }

        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FILE_ORIENTATION { get; set; }


        /// <summary>
        /// 附件大小
        /// </summary>
        [Column("FILE_SIZE")]
        [StringLength(20, ErrorMessage = "附件大小长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FILE_SIZE { get; set; }

        /// <summary>
        /// 总页数
        /// </summary>
        [Column("FILE_PAGES")]
        [StringLength(20, ErrorMessage = "总页数长度不能超出20字符")]
        [Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FILE_PAGES { get; set; }
    }
}
