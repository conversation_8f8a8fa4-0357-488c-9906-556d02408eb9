﻿using System.Diagnostics;
using AutoMapper;
using H.BASE;
using H.BASE.SqlSugarInfra.Uow;
using H.Utility;
using H.Utility.Helper;
using Microsoft.Extensions.Caching.Memory;
using Serilog;
using XH.H81.IServices;
using XH.H81.Models;
using XH.H81.Models.Entities;
using XH.H81.Models.SugarDbContext;
using XH.LAB.UTILS.Helpers;
using XH.LAB.UTILS.Models.Entites;

namespace XH.H81.API.ScheduleJobs
{
    /// <summary>
    /// 定时同步用户信息到档案人员表
    /// </summary>
    public class AutoSyncBaseData : IHostedService, IDisposable
    {
        private IConfiguration _configuration;
        private ISqlSugarUow<SugarDbContext_Master> _soa;
        private IMapper _mapper;
        private IBaseDataServices _IBaseDataServices;
        private IMemoryCache _cache;
        private Timer _timer;
        static object _lockObject = new object();
        const string lockName = "XH:LOCK:PMS_SyncBaseDataFromSYS";
        public AutoSyncBaseData(IConfiguration configuration, IBaseDataServices iBaseDataServices, ISqlSugarUow<SugarDbContext_Master> soa, IMapper mapper, IMemoryCache cache)
        {
            _configuration = configuration;
            _IBaseDataServices = iBaseDataServices;
            _soa = soa;
            _cache = cache ?? throw new ArgumentNullException(nameof(cache));
            _mapper = mapper;
            ExecutingChangeSqlHelper.ExecutingChangeSql(_soa, configuration, true);
            CoverSqlLog();
        }
        public void Dispose()
        {
            _timer?.Dispose();
        }
        //屏蔽过多的同步日志并加锁
        private void CoverSqlLog()
        {
            //_soa.Db.Aop.OnDiffLogEvent = a => { };
            //_soa.Db.Aop.OnLogExecuting = (a, b) => { };
            _soa.Db.Aop.OnLogExecuted = (a, b) =>
            {
            };
        }

        public Task StartAsync(CancellationToken cancellationToken)
        {
            if (_timer != null )
            {
                _timer.Dispose();
                _timer = null;
            }
            if (_timer == null && AppSettingsProvider.SynergySign == "1") //仅连接的数据库为主协同库时才执行
            {
                Log.Information("==>[定时同步SYS基础数据到OA_BASE_DATA]已开启,30分钟同步一次.");
                _timer = new Timer(DoWork, null, TimeSpan.Zero,
                     TimeSpan.FromMinutes(30));
            }
            return Task.CompletedTask;
        }

        public Task StopAsync(CancellationToken cancellationToken)
        {
            _timer?.Change(Timeout.Infinite, 0);
            return Task.CompletedTask;
        }

        private async void DoWork(object param)
        {
            lock (EntityHelper.LockObject)
            {
                try
                {
                    //_soa?.Db?.Open();
                    if (GetTheLock())
                    {
                        DoSyncBaseData();
                    }
                    else
                    {
                        GetTheLockFalse();
                    }

                    //刷新基础数据缓存
                    ResetSys6BaseDataCache();
                    ResetOaBaseDataCache();
                }
                catch (Exception e)
                {
                    var redis = XhRedisHelper.UseS03();
                    var isDeleted = redis.KeyDelete(lockName);
                    if (isDeleted)
                    {
                        Console.WriteLine($"已有停止正在执行的定时任务，服务名：AutoSyncBaseData");
                    }
                    else
                    {
                        var serviceName = redis.StringGet(lockName);
                        var ttl = redis.KeyExpireTime(lockName);
                        Console.WriteLine($"已有服务正在执行，服务名：{serviceName},过期时间为{ttl?.ToString("yyyy-MM-dd HH:mm:ss")}。");
                    }
                    Console.WriteLine($"AutoSyncBaseData同步考试数据失败：{e.Message}");
                }
            }
            //finally
            //{
            //    _soa?.Db?.Close();
            //}
        }

        private void DoSyncBaseData()
        {
            lock (_lockObject)
            {
                try
                {
                    //查找SYS6_BASE_DATA新增数据项
                    List<SYS6_BASE_DATA> newAddData = null;
                    try
                    {
                        newAddData = _soa.Db.Queryable<SYS6_BASE_DATA>()
                         .InnerJoin<SYS6_BASE_DATA_CLASS>((s, c) => s.CLASS_ID == c.CLASS_ID && s.DATA_STATE == "1" && c.DATA_TABLE.ToUpper() == "OA_BASE_DATA" && c.CLASS_STATE == "1")
                         .LeftJoin<OA_BASE_DATA>((s, c, o) => o.CLASS_ID == s.CLASS_ID && o.DATA_ID == s.DATA_ID)
                         .Where((s, c, o) => o.DATA_ID == null)
                         .Select((s, c, o) => new { s })
                         .ToList()
                         .Select(a => a.s) //避免产生select *
                         .ToList();
                    }
                    catch { }
                    Stopwatch stopwatch = Stopwatch.StartNew();

                    if (newAddData != null && newAddData.Any())
                    {
                        //更新人员数据
                        List<OA_BASE_DATA> insertData = newAddData.Distinct().Where(a => a.DATA_CNAME != null)
                            .Select(d => new OA_BASE_DATA
                            {
                                CLASS_ID = d.CLASS_ID,
                                DATA_ID = d.DATA_ID,
                                DATA_NAME = d.DATA_CNAME,
                                STATE_FLAG = d.DATA_STATE,
                                DATA_SNAME = d.DATA_SNAME,
                                DATA_ENAME = d.DATA_ENAME,
                                HOSPITAL_ID = d.HOSPITAL_ID,
                                MODULE_ID = "H81",//暂时从SYS没有找到对应字段，先观察观察
                                DATA_SORT = EntityHelper.GetSort(),
                                SPELL_CODE = d.SPELL_CODE,
                                FIRST_RPERSON = d.FIRST_RPERSON,
                                FIRST_RTIME = d.FIRST_RTIME,
                                LAST_MPERSON = d.LAST_MPERSON,
                                LAST_MTIME = d.LAST_MTIME,
                                REMARK = d.REMARK,
                            })
                            .ToList();

                        string dataStr = string.Join('、', insertData.Select(a => $"{a.CLASS_ID}-{a.DATA_NAME}"));
                        int updateCount = _soa.Db.Updateable(insertData).ExecuteCommand();
                        stopwatch.Stop();
                        if (updateCount > 0)
                        {
                            Log.Information($"==>[定时同步SYS基础数据到OA_BASE_DATA]执行结束.共发现[{insertData.Count}]条基础字段变更数据,更新成功条数:[{updateCount}],数据为:[{dataStr}],耗时:[{stopwatch.ElapsedMilliseconds}]ms");
                        }
                    }

                }

                catch (Exception ex)
                {
                    Log.Error($"==>[定时同步SYS基础数据到OA_BASE_DATA]执行结束:{ex}");
                }
                //finally
                //{
                //    _soa?.Db?.Close();
                //}
            }
        }


        private bool GetTheLock()
        {
            var time = TimeSpan.FromSeconds(60);

            //var redis = _cachingProvider.GetRedisProvider("S03");
            var redis = XhRedisHelper.UseS03();

            var result = redis.LockTake(lockName, $"{_configuration.GetSection("Kestrel:Endpoints:Https:Url").Value}", time);

            return result;
        }

        private void GetTheLockFalse()
        {
            //var redis = _cachingProvider.GetRedisProvider("S03");
            var redis = XhRedisHelper.UseS03();
            var serviceName = redis.StringGet(lockName);
            var ttl = redis.KeyExpireTime(lockName);
            Console.WriteLine($"已有服务正在执行，服务名：{serviceName},过期时间为{ttl?.ToString("yyyy-MM-dd HH:mm:ss")}。");
        }


        /// <summary>
        /// 获取基础数据
        /// </summary>
        /// <returns></returns>
        public List<SYS6_BASE_DATA> GetSys6BaseData()
        {
            if (!_cache.TryGetValue("SYS6_BASE_DATA", out List<SYS6_BASE_DATA> listData) || listData == null)
            {
                listData = _soa.Db.Queryable<SYS6_BASE_DATA>().Where(t => t.DATA_STATE == "1").ToList();
                _cache.Set("SYS6_BASE_DATA", listData);
            }
            return listData;
        }
        public List<OA_BASE_DATA> GetOaBaseData()
        {
            if (!_cache.TryGetValue("OA_BASE_DATA", out List<OA_BASE_DATA> listData) || listData == null)
            {
                listData = _soa.Db.Queryable<OA_BASE_DATA>().Where(t => t.STATE_FLAG == "1").ToList();
                _cache.Set("OA_BASE_DATA", listData);
            }
            return listData;
        }

        public void ResetSys6BaseDataCache()
        {
            _cache.Remove("SYS6_BASE_DATA");
            GetSys6BaseData();
        }
        public void ResetOaBaseDataCache()
        {
            _cache.Remove("OA_BASE_DATA");
            GetOaBaseData();
        }
    }
}
