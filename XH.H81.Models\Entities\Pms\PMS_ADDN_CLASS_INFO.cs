﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.Entities.Pms
{
    [DBOwner("XH_OA")]
    public class PMS_ADDN_CLASS_INFO
    {
        /// <summary>
        /// 主键id
        /// </summary>
        [Key]
        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string CLASS_ID { get; set; }

        /// <summary>
        /// 医疗机构ID
        /// </summary>
        public string HOSPITAL_ID { get; set; }

        /// <summary>
        /// 分类类型0:固定 1:扩展 默认0
        /// </summary>
        public string CLASS_TYPE { get; set; } = "0";


        /// <summary>
        /// 分类名
        /// </summary>
        public string CLASS_NAME { get; set; }

        /// <summary>
        /// 分类排序
        /// </summary>
        public string CLASS_SORT { get; set; }

        /// <summary>
        /// 分类状态0禁用1在用2删除,默认1
        /// </summary>
        public string CLASS_STATE { get; set; } = "1";

        /// <summary>
        /// 表单配置记录id
        /// </summary>
        public string FORM_SETUP_ID { get; set; }

        /// <summary>
        /// 表格配置记录id
        /// </summary>
        public string TABLE_SETUP_ID { get; set; }

        /// <summary>
        /// 分类数据存储表名
        /// </summary>
        public string CLASS_TABLE_NAME { get; set; }

        /// <summary>
        /// 分类扩展配置内容
        /// </summary>
        public string CLASS_ADDN_CONFIG { get; set; }

        /// <summary>
        /// 首次记录人
        /// </summary>
        public string FIRST_RPERSON { get; set; }

        /// <summary>
        /// 首次记录时间
        /// </summary>
        public DateTime? FIRST_RTIME { get; set; }

        /// <summary>
        /// 最后操作人
        /// </summary>
        public string LAST_MPERSON { get; set; }

        /// <summary>
        /// 最后操作时间
        /// </summary>
        public DateTime? LAST_TIME { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string REMARK { get; set; }

        /// <summary>
        /// 是否隐藏 0-否 1-是； 默认否
        /// </summary>
        public int IS_HIDE { get; set; }

        /// <summary>
        /// 是否可编辑属性 0-否 1-是； 默认是
        /// </summary>
        public int IS_PROP_EDITABLE { get; set; }

        /// <summary>
        /// 是否有审核流程 0-否 1-是； 默认否 表示审核页面是否可见该分类
        /// </summary>
        public int IS_AUDITABLE { get; set; }

        /// <summary>
        ///  分类归属 0-普通 1-健康档案
        /// </summary>
        public string CLASS_KIND { get; set; }

        /// <summary>
        ///  生安标志
        /// </summary>
        public string SMBL_FLAG { get; set; } = "0";

        /// <summary>
        ///  记录生安标记
        /// </summary>
        public string SMBL_REC_FLAG { get; set; }

    }
}
