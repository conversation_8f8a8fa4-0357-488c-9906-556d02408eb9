using System;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using SqlSugar;
using H.Utility.SqlSugarInfra;

namespace XH.H81.Models.ExternalEntity
{
	[Table("LIS6_INSTRUMENT_INFO")]
    [DBOwner("XH_SYS")]
    public class LIS6_INSTRUMENT_INFO
	{
		/// <summary>
		/// 
		/// </summary>
		[Key]
        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        //[Column("INSTRUMENT_ID")]
		[Required(ErrorMessage = "不允许为空")]

		[StringLength(20, ErrorMessage = "INSTRUMENT_ID长度不能超出20字符")]
		//[Unicode(false)]
        public string INSTRUMENT_ID { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("LAB_ID")]
		[Required(ErrorMessage = "不允许为空")]

		[StringLength(20, ErrorMessage = "LAB_ID长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string LAB_ID { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("INSTRUMENT_REGISTER")]
		[StringLength(50, ErrorMessage = "INSTRUMENT_REGISTER长度不能超出50字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? INSTRUMENT_REGISTER { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("FIRST_RPERSON")]
		[StringLength(50, ErrorMessage = "FIRST_RPERSON长度不能超出50字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FIRST_RPERSON { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("INSTRUMENT_FACTORY")]
		[StringLength(50, ErrorMessage = "INSTRUMENT_FACTORY长度不能超出50字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? INSTRUMENT_FACTORY { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("LAST_MTIME")]
		//[Unicode(false)]
		public DateTime? LAST_MTIME { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("COMM_MODE")]
		[StringLength(20, ErrorMessage = "COMM_MODE长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? COMM_MODE { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("IUNIT_ID")]
		[StringLength(20, ErrorMessage = "IUNIT_ID长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? IUNIT_ID { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("INSTRUMENT_SNUM")]
		[Required(ErrorMessage = "不允许为空")]

		[StringLength(50, ErrorMessage = "INSTRUMENT_SNUM长度不能超出50字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string INSTRUMENT_SNUM { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("NUMBERING_MODE")]
		[StringLength(20, ErrorMessage = "NUMBERING_MODE长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? NUMBERING_MODE { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("INSTRUMENT_NAME")]
		[StringLength(50, ErrorMessage = "INSTRUMENT_NAME长度不能超出50字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? INSTRUMENT_NAME { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("INSTANCE_ID")]
		[Required(ErrorMessage = "不允许为空")]

		[StringLength(20, ErrorMessage = "INSTANCE_ID长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string INSTANCE_ID { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("INSTRUMENT_TYPE")]
		[StringLength(50, ErrorMessage = "INSTRUMENT_TYPE长度不能超出50字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? INSTRUMENT_TYPE { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("SINSTRUMENT_ID")]
		[Required(ErrorMessage = "不允许为空")]

		[StringLength(50, ErrorMessage = "SINSTRUMENT_ID长度不能超出50字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string SINSTRUMENT_ID { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("ORDER_SEND_STAGE")]
		[StringLength(10, ErrorMessage = "ORDER_SEND_STAGE长度不能超出10字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? ORDER_SEND_STAGE { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("INSTRUMENT_STATE")]
		[StringLength(50, ErrorMessage = "INSTRUMENT_STATE长度不能超出50字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? INSTRUMENT_STATE { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("REMARK")]
		[StringLength(100, ErrorMessage = "REMARK长度不能超出100字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? REMARK { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("GROUP_ID")]
		[Required(ErrorMessage = "不允许为空")]

		[StringLength(20, ErrorMessage = "GROUP_ID长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string GROUP_ID { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("CLIENT_ID")]
		[StringLength(20, ErrorMessage = "CLIENT_ID长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? CLIENT_ID { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("EQUIPMENT_ID")]
		[Required(ErrorMessage = "不允许为空")]

		[StringLength(20, ErrorMessage = "EQUIPMENT_ID长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string EQUIPMENT_ID { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("FIRST_RTIME")]
		//[Unicode(false)]
		public DateTime? FIRST_RTIME { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("LAST_MPERSON")]
		[StringLength(50, ErrorMessage = "LAST_MPERSON长度不能超出50字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? LAST_MPERSON { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("INSTRUMENT_POSITION")]
		[StringLength(50, ErrorMessage = "INSTRUMENT_POSITION长度不能超出50字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? INSTRUMENT_POSITION { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("GRAPH_TYPE")]
		[StringLength(20, ErrorMessage = "GRAPH_TYPE长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? GRAPH_TYPE { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("INSTRUMENT_CNAME")]
		[StringLength(50, ErrorMessage = "INSTRUMENT_CNAME长度不能超出50字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? INSTRUMENT_CNAME { get; set; }

		/// <summary>
		/// 
		/// </summary>
		//[Column("HOSPITAL_ID")]
		[Required(ErrorMessage = "不允许为空")]

		[StringLength(20, ErrorMessage = "HOSPITAL_ID长度不能超出20字符")]
		//[Unicode(false)]
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string HOSPITAL_ID { get; set; }
	}
}
