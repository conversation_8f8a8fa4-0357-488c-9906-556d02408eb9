﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XH.H81.Models.Dtos.IoTDevices.Access;

namespace XH.H81.Models.Dtos.IoTDevices.Platform
{
    public class XhXmlAccessWeekPlanDto : XhIotRoot
    {  /// <summary>
       /// ID 编号，最好从1自增
       /// </summary>
        [JsonProperty("id" ,NullValueHandling = NullValueHandling.Ignore)]
        public int? Id { get; set; }
        /// <summary>
        /// 模板名称
        /// </summary>
        [JsonProperty("name")]
        public string Name { get; set; }
        /// <summary>
        /// 是否开启：true生效
        /// </summary>
        [JsonProperty("enable")]
        public bool Enable { get; set; }
        /// <summary>
        /// 周计划配置信息
        /// </summary>
        [JsonProperty("WeekPlanCfg")]
        public List<AccessWeekPlanDetailDto> WeekPlanDetail { get; set; }
    }
}
