﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Base.Helper
{
    public class ResultDto<T>
    {
        public bool success { get; set; } = true;

        public string? msg { get; set; }

        public string errCode { get; set; }

        public T data { get; set; }

        public object data1 { get; set; }

        public object data2 { get; set; }
    }


    public static class ResultExt
    {

        public static ResultDto<T> ResultDto<T>(this T obj, bool success = true, string? msg = null)
        {
            return new ResultDto<T>
            {
                data = obj,
                success = success,
                msg = msg
            };
        }


    }

}
