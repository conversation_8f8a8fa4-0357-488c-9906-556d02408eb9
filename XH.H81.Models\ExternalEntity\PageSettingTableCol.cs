﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XH.H81.Models.ExternalEntity
{
    /// <summary>
    /// 工具箱表格列属性
    /// </summary>
    public class PageSettingTableCol
    {
        /// <summary>
        /// 课题经费
        /// </summary>
        public string headerName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string field { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool? hide { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int? width { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string minWidth { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string maxWidth { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string requiredMarkValue { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string fieldColor { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool sortable { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool resizable { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool suppressMovable { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool allowWrapText { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool editable { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string filter { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string tooltipField { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool outPut { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string pinned { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string mock { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string dateFormart { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string sort { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string dataType { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string dataClass { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string oneLevelName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool autoHeight { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool wrapText { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public CellStyle cellStyle { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string fieldSort { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool suppressMenu { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public GetterAutoMapper valueGetterAutoMapper { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool? wrapTex { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool? enableRowGroup { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string dateFormat { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool? unitFlag { get; set; }
    }

    public class CellStyle
    {
        /// <summary>
        /// 
        /// </summary>
        public string backgroundColor { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string color { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string fontSize { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string textAlign { get; set; }
    }

    public class GetterAutoMapper
    {
        /// <summary>
        /// 
        /// </summary>
        public string dataName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string mapField { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string textField { get; set; }

    }

}