// ---------------------------------------------
//  @Author: chen<PERSON><PERSON><PERSON>
//  @Date:  2023-03-28 23:04
//  @Last Modified by: chen<PERSON><PERSON><PERSON>
//  @Last Modified time: 2023-03-28 23:04
//  @Description:处理消息服务
// ---------------------------------------------

using H.BASE.IServices;
using H.Utility.Dtos;
using StackExchange.Redis;
using StackExchange.Redis.Extensions.Core.Abstractions;

namespace XH.H81.Services;

public class RedisStreamMsgServices : IRedisStreamMsg_OLDServices//IRedisStreamMsgServices//
{
    private IRedisDatabase _db;

    public RedisStreamMsgServices(IRedisClientFactory clientFactory)
    {
        _db = clientFactory.GetRedisClient("S02").GetDefaultDatabase();
    }

    /// <summary>
    /// 处理收到的Stream消息
    /// </summary>
    /// <param name="streamKey"></param>
    /// <param name="msg"></param>
    /// <exception cref="NotImplementedException"></exception>
    public List<StreamMsgConfirmDto> HandleStreamMsg(List<StreamMsgDto> msgList)
    {
        //todo:注意,此处代码需要各自业务系统根据实际需求实现,以下代码仅为示例代码
        Console.WriteLine("收到{0}条消息,开始处理", msgList.Count);
        var result = new List<StreamMsgConfirmDto>();
        foreach (var streamMsgDto in msgList)
        {
            //newMsg=新消息 peddingMsg=已读没回的消息
            //业务系统自己觉得该怎么处理这两种类型的消息
            var msgType = streamMsgDto.msgType;
            //处理前加分布式锁方式重复处理 10秒后自动释放
            var x = _db.Database.LockTake("XH:BASE:MSGLOCK:" + streamMsgDto.msgId, "1", TimeSpan.FromSeconds(10));
            if (!x)
            {
                continue;
            }
            switch (streamMsgDto.streamKey)
            {
                case "XH:BASE:STREAM:LIS_CHARGE_ITEM":
                {
                    //处理逻辑
                    Thread.Sleep(500); //模拟处理
                    //处理完成
                    Console.WriteLine("已处理1条诊疗项目变更消息,来自:" + msgType);
                    //加入处理结果list
                    result.Add(new StreamMsgConfirmDto()
                    {
                        success = true,
                        msgId = streamMsgDto.msgId,
                        streamKey = streamMsgDto.streamKey
                    });
                }
                    break;
                case "XH:BASE:STREAM:LIS_TEST_ITEM":
                {
                    //处理逻辑
                    Thread.Sleep(500); //模拟处理
                    Console.WriteLine("已处理1条分析项目变更消息,来自:" + msgType);
                    //处理完成
                    result.Add(new StreamMsgConfirmDto()
                    {
                        success = true,
                        msgId = streamMsgDto.msgId,
                        streamKey = streamMsgDto.streamKey
                    });
                }
                    break;
                //其他
            }
        }

        //返回处理结果 服务会根据success状态自动发送ACK确认消息
        return result;
    }
}