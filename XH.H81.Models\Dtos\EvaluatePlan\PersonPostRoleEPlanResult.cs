﻿using AutoMapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XH.H81.Models.Exam;
using XH.LAB.UTILS.Models.Entites;

namespace XH.H81.Models.Dtos.EvaluatePlan
{
    [AutoMap(typeof(OaEvaluatePlanSetup), ReverseMap = true)]
    public class PersonPostRoleEPlanResult : OaEvaluatePlanSetup
    {
        /// <summary>
        /// 结果类型：0-未参与, 1-通过, 2-未通过 3-临期 4-过期
        /// </summary>
        public string RESULT_TYPE { get; set; }
        /// <summary>
        /// 结果描述
        /// </summary>
        public string RESULT_DESC { get; set; }
        /// <summary>
        /// 临期警告时间值
        /// </summary>
        public decimal? RESULT_WARN_DURATION { get; set; }
    }
}
