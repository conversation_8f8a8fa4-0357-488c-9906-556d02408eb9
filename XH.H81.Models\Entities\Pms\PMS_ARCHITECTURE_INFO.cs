﻿using H.Utility.SqlSugarInfra;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace XH.H81.Models.Entities.Pms

{
    [DBOwner("XH_OA")]
    public class PMS_ARCHITECTURE_INFO
    {
        [Key]
        [SugarColumn(IsPrimaryKey = true, SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string ARCHITECTURE_ID { get; set; }
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? MODULE_ID { get; set; }
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? HOSPITAL_ID { get; set; }
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? LAB_ID { get; set; }
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? ARCHITECTURE_PATH { get; set; }
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? ARCHITECTURE_STATE { get; set; }
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FIRST_RPERSON { get; set; }
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? FIRST_RTIME { get; set; }
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? LAST_MPERSON { get; set; }
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? LAST_MTIME { get; set; }
        [SugarColumn(SqlParameterDbType = System.Data.DbType.AnsiString)]
        public string? REMARK { get; set; }

    }
}
